<?php
/**
 * POST加密请求测试客户端
 */

class PostEncryptTestClient
{
    private $baseUrl;
    private $encryptKey;
    
    public function __construct($baseUrl = 'http://localhost:8787', $encryptKey = null)
    {
        $this->baseUrl = rtrim($baseUrl, '/');
        $this->encryptKey = $encryptKey ?: 'your_encrypt_key_here'; // 替换为实际的加密密钥
    }
    
    /**
     * AES加密
     */
    private function aesEncrypt($data)
    {
        $iv = substr($this->encryptKey, 0, 16);
        return openssl_encrypt($data, 'AES-128-CBC', $this->encryptKey, 0, $iv);
    }
    
    /**
     * AES解密
     */
    private function aesDecrypt($data)
    {
        $iv = substr($this->encryptKey, 0, 16);
        return openssl_decrypt($data, 'AES-128-CBC', $this->encryptKey, 0, $iv);
    }
    
    /**
     * 发送HTTP请求
     */
    private function sendRequest($url, $method = 'GET', $data = null, $headers = [])
    {
        $ch = curl_init();
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $this->baseUrl . $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HEADER => true,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_TIMEOUT => 120, // 增加超时时间
            CURLOPT_VERBOSE => true, // 启用详细输出
        ]);
        
        if ($data !== null) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
        $error = curl_error($ch);
        
        curl_close($ch);
        
        if ($error) {
            return [
                'error' => $error,
                'http_code' => $httpCode
            ];
        }
        
        $headers = substr($response, 0, $headerSize);
        $body = substr($response, $headerSize);
        
        return [
            'http_code' => $httpCode,
            'headers' => $headers,
            'body' => $body
        ];
    }
    
    /**
     * 测试POST加密请求getotTal2
     */
    public function testPostEncryptedGetotal2()
    {
        echo "=== 测试POST加密请求getotTal2 ===\n";
        
        // 准备要加密的数据（可以是空数组，因为接口不依赖POST参数）
        $postData = [
            'test' => true,
            'timestamp' => time()
        ];
        
        $jsonData = json_encode($postData);
        $encryptedData = $this->aesEncrypt($jsonData);
        
        echo "原始POST数据: $jsonData\n";
        echo "加密POST数据: $encryptedData\n";
        
        $response = $this->sendRequest('/api/getotal2-encrypted', 'POST', $encryptedData, [
            'Content-Type: text/plain',
            'Authorization: 1' // 添加认证头，假设用户ID为1
        ]);
        
        echo "HTTP状态码: {$response['http_code']}\n";
        
        if (isset($response['error'])) {
            echo "CURL错误: {$response['error']}\n";
            return;
        }
        
        echo "响应头部:\n{$response['headers']}\n";
        
        // 检查是否是加密响应
        if (strpos($response['headers'], 'X-Encrypted-Response: true') !== false) {
            echo "检测到加密响应，正在解密...\n";
            echo "加密响应数据长度: " . strlen($response['body']) . " bytes\n";
            
            try {
                $decryptedData = $this->aesDecrypt($response['body']);
                echo "解密成功！\n";
                echo "解密后数据长度: " . strlen($decryptedData) . " bytes\n";
                
                $decodedData = json_decode($decryptedData, true);
                if ($decodedData) {
                    echo "JSON解析成功！\n";
                    if (isset($decodedData['_debug'])) {
                        echo "调试信息:\n";
                        print_r($decodedData['_debug']);
                    }
                    if (isset($decodedData['error'])) {
                        echo "接口返回错误: {$decodedData['message']}\n";
                    } else {
                        echo "数据获取成功，包含以下字段: " . implode(', ', array_keys($decodedData)) . "\n";
                    }
                } else {
                    echo "JSON解析失败: " . json_last_error_msg() . "\n";
                    echo "解密后的原始数据: " . substr($decryptedData, 0, 500) . "...\n";
                }
            } catch (Exception $e) {
                echo "解密失败: " . $e->getMessage() . "\n";
                echo "原始响应数据: " . substr($response['body'], 0, 200) . "...\n";
            }
        } else {
            echo "普通响应（未加密）:\n";
            echo "响应数据长度: " . strlen($response['body']) . " bytes\n";
            echo "响应内容: " . substr($response['body'], 0, 500) . "...\n";
        }
        
        echo "\n";
    }
    
    /**
     * 测试原始getotTal2接口（对比）
     */
    public function testOriginalGetotal2()
    {
        echo "=== 测试原始getotTal2接口（POST加密） ===\n";
        
        $postData = ['test' => true];
        $jsonData = json_encode($postData);
        $encryptedData = $this->aesEncrypt($jsonData);
        
        $response = $this->sendRequest('/api/getotTal2', 'POST', $encryptedData, [
            'Content-Type: text/plain',
            'Authorization: 1'
        ]);
        
        echo "HTTP状态码: {$response['http_code']}\n";
        
        if (isset($response['error'])) {
            echo "CURL错误: {$response['error']}\n";
            return;
        }
        
        echo "响应头部:\n{$response['headers']}\n";
        echo "响应数据长度: " . strlen($response['body']) . " bytes\n";
        
        // 尝试解密响应
        if (strpos($response['headers'], 'X-Encrypted-Response: true') !== false) {
            try {
                $decryptedData = $this->aesDecrypt($response['body']);
                echo "解密成功，数据长度: " . strlen($decryptedData) . " bytes\n";
                
                $decodedData = json_decode($decryptedData, true);
                if ($decodedData) {
                    echo "数据字段: " . implode(', ', array_keys($decodedData)) . "\n";
                } else {
                    echo "JSON解析失败\n";
                }
            } catch (Exception $e) {
                echo "解密失败: " . $e->getMessage() . "\n";
            }
        } else {
            echo "响应内容: " . substr($response['body'], 0, 200) . "...\n";
        }
        
        echo "\n";
    }
    
    /**
     * 运行所有测试
     */
    public function runAllTests()
    {
        echo "开始测试POST加密请求...\n\n";
        
        $this->testPostEncryptedGetotal2();
        $this->testOriginalGetotal2();
        
        echo "所有测试完成！\n";
    }
}

// 使用示例
if (php_sapi_name() === 'cli') {
    // 命令行运行
    $client = new PostEncryptTestClient('http://localhost:8787', 'your_encrypt_key_here');
    $client->runAllTests();
} else {
    // 浏览器运行
    echo "<pre>";
    $client = new PostEncryptTestClient('http://localhost:8787', 'your_encrypt_key_here');
    $client->runAllTests();
    echo "</pre>";
}
