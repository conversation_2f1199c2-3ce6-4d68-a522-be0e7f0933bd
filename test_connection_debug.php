<?php
/**
 * 连接问题调试测试客户端
 */

class ConnectionDebugClient
{
    private $baseUrl;
    private $encryptKey;
    
    public function __construct($baseUrl = 'http://localhost:8787', $encryptKey = null)
    {
        $this->baseUrl = rtrim($baseUrl, '/');
        $this->encryptKey = $encryptKey ?: 'your_encrypt_key_here';
    }
    
    /**
     * AES加密
     */
    private function aesEncrypt($data)
    {
        $iv = substr($this->encryptKey, 0, 16);
        return openssl_encrypt($data, 'AES-128-CBC', $this->encryptKey, 0, $iv);
    }
    
    /**
     * 发送HTTP请求
     */
    private function sendRequest($url, $method = 'GET', $data = null, $headers = [], $timeout = 30)
    {
        $ch = curl_init();
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $this->baseUrl . $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HEADER => true,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_TIMEOUT => $timeout,
            CURLOPT_CONNECTTIMEOUT => 10,
            CURLOPT_VERBOSE => false,
        ]);
        
        if ($data !== null) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        }
        
        $startTime = microtime(true);
        $response = curl_exec($ch);
        $requestTime = microtime(true) - $startTime;
        
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
        $error = curl_error($ch);
        $info = curl_getinfo($ch);
        
        curl_close($ch);
        
        if ($error) {
            return [
                'success' => false,
                'error' => $error,
                'http_code' => $httpCode,
                'request_time' => $requestTime
            ];
        }
        
        $headers = substr($response, 0, $headerSize);
        $body = substr($response, $headerSize);
        
        return [
            'success' => true,
            'http_code' => $httpCode,
            'headers' => $headers,
            'body' => $body,
            'request_time' => $requestTime,
            'body_size' => strlen($body),
            'info' => $info
        ];
    }
    
    /**
     * 测试1：简单POST请求
     */
    public function testSimplePost()
    {
        echo "=== 测试1：简单POST请求 ===\n";
        
        $postData = ['test' => 'simple', 'timestamp' => time()];
        $jsonData = json_encode($postData);
        $encryptedData = $this->aesEncrypt($jsonData);
        
        $result = $this->sendRequest('/api/test/simple-post', 'POST', $encryptedData, [
            'Content-Type: text/plain',
            'Authorization: 1'
        ]);
        
        echo "请求结果: " . ($result['success'] ? '成功' : '失败') . "\n";
        echo "HTTP状态码: {$result['http_code']}\n";
        echo "请求耗时: " . round($result['request_time'], 3) . "s\n";
        
        if ($result['success']) {
            echo "响应大小: {$result['body_size']} bytes\n";
            echo "响应内容: " . substr($result['body'], 0, 200) . "...\n";
        } else {
            echo "错误信息: {$result['error']}\n";
        }
        
        echo "\n";
        return $result['success'];
    }
    
    /**
     * 测试2：小数据量请求
     */
    public function testSmallData()
    {
        echo "=== 测试2：小数据量请求 ===\n";
        
        $postData = ['test' => 'small_data'];
        $jsonData = json_encode($postData);
        $encryptedData = $this->aesEncrypt($jsonData);
        
        $result = $this->sendRequest('/api/test/small-data', 'POST', $encryptedData, [
            'Content-Type: text/plain',
            'Authorization: 1'
        ], 60); // 增加超时时间
        
        echo "请求结果: " . ($result['success'] ? '成功' : '失败') . "\n";
        echo "HTTP状态码: {$result['http_code']}\n";
        echo "请求耗时: " . round($result['request_time'], 3) . "s\n";
        
        if ($result['success']) {
            echo "响应大小: {$result['body_size']} bytes\n";
            $decoded = json_decode($result['body'], true);
            if ($decoded && isset($decoded['test_info'])) {
                echo "服务器内存使用: " . round($decoded['test_info']['memory'] / 1024 / 1024, 2) . "MB\n";
            }
        } else {
            echo "错误信息: {$result['error']}\n";
        }
        
        echo "\n";
        return $result['success'];
    }
    
    /**
     * 测试3：逐步增加数据量
     */
    public function testIncrementalData()
    {
        echo "=== 测试3：逐步增加数据量 ===\n";
        
        for ($step = 1; $step <= 4; $step++) {
            echo "步骤 {$step}: ";
            
            $postData = ['step' => $step];
            $jsonData = json_encode($postData);
            $encryptedData = $this->aesEncrypt($jsonData);
            
            $result = $this->sendRequest('/api/test/incremental-data', 'POST', $encryptedData, [
                'Content-Type: text/plain',
                'Authorization: 1'
            ], 120);
            
            if ($result['success']) {
                echo "成功 - 耗时: " . round($result['request_time'], 3) . "s";
                echo " - 大小: {$result['body_size']} bytes";
                
                $decoded = json_decode($result['body'], true);
                if ($decoded && isset($decoded['size'])) {
                    echo " - 数据大小: {$decoded['size']} bytes";
                }
                echo "\n";
            } else {
                echo "失败 - {$result['error']}\n";
                break;
            }
        }
        
        echo "\n";
    }
    
    /**
     * 测试4：原始getotTal2接口（不加密响应）
     */
    public function testOriginalGetotal2()
    {
        echo "=== 测试4：原始getotTal2接口（不加密响应） ===\n";
        
        $postData = ['test' => 'original'];
        $jsonData = json_encode($postData);
        $encryptedData = $this->aesEncrypt($jsonData);
        
        $result = $this->sendRequest('/api/App_getperson/getotTal2', 'POST', $encryptedData, [
            'Content-Type: text/plain',
            'Authorization: 1'
        ], 180); // 3分钟超时
        
        echo "请求结果: " . ($result['success'] ? '成功' : '失败') . "\n";
        echo "HTTP状态码: {$result['http_code']}\n";
        echo "请求耗时: " . round($result['request_time'], 3) . "s\n";
        
        if ($result['success']) {
            echo "响应大小: {$result['body_size']} bytes\n";
            echo "响应大小(MB): " . round($result['body_size'] / 1024 / 1024, 2) . "MB\n";
            
            // 检查是否是有效的JSON
            $decoded = json_decode($result['body'], true);
            if ($decoded) {
                echo "JSON解析: 成功\n";
                echo "数据字段: " . implode(', ', array_keys($decoded)) . "\n";
            } else {
                echo "JSON解析: 失败\n";
                echo "响应内容前200字符: " . substr($result['body'], 0, 200) . "\n";
            }
        } else {
            echo "错误信息: {$result['error']}\n";
        }
        
        echo "\n";
    }
    
    /**
     * 运行所有测试
     */
    public function runAllTests()
    {
        echo "开始连接问题调试测试...\n\n";
        
        // 逐步测试，找出问题所在
        if (!$this->testSimplePost()) {
            echo "简单POST请求失败，基础连接有问题\n";
            return;
        }
        
        if (!$this->testSmallData()) {
            echo "小数据量请求失败，可能是认证或业务逻辑问题\n";
            return;
        }
        
        $this->testIncrementalData();
        $this->testOriginalGetotal2();
        
        echo "所有测试完成！\n";
    }
}

// 使用示例
if (php_sapi_name() === 'cli') {
    $client = new ConnectionDebugClient('http://localhost:8787', 'your_encrypt_key_here');
    $client->runAllTests();
} else {
    echo "<pre>";
    $client = new ConnectionDebugClient('http://localhost:8787', 'your_encrypt_key_here');
    $client->runAllTests();
    echo "</pre>";
}
