<?php
/**
 * This file is part of webman.
 *
 * Licensed under The MIT License
 * For full copyright and license information, please see the MIT-LICENSE.txt
 * Redistributions of files must retain the above copyright notice.
 *
 * <AUTHOR>
 * @copyright walkor<<EMAIL>>
 * @link      http://www.workerman.net/
 * @license   http://www.opensource.org/licenses/mit-license.php MIT License
 */

return [
    'listen'           => 'http://0.0.0.0:8686',
    'transport'        => 'tcp',
    'context'          => [],
    'name'             => 'webman',
    'count'            => 1, // Windows环境下减少进程数，提高稳定性
    'user'             => '',
    'group'            => '',
    'reusePort'        => false,
    'event_loop'       => '',
    'stop_timeout'     => 120000, // 2分钟停止超时
    'pid_file'         => runtime_path() . '/webman.pid',
    'status_file'      => runtime_path() . '/webman.status',
    'stdout_file'      => runtime_path() . '/logs/stdout.log',
    'log_file'         => runtime_path() . '/logs/workerman.log',
    'max_package_size' => 100 * 1024 * 1024, // 100MB，适合大数据响应
    'max_request'      => 1000, // 限制每个进程处理的请求数
];