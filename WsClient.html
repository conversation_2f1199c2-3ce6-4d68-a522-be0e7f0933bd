<!DOCTYPE html>

<html>

<head>

    <meta charset="UTF-8">

    <title>会议室客户端</title>

</head>

<body>

    <div>

        <input type="text" id="roomId" placeholder="输入会议室ID">

        <input type="text" id="name" placeholder="输入参会人名称">

        <button onclick="joinRoom()">加入会议室</button>

        <button onclick="setHost()">成为主持人</button>

        <button onclick="finishSpeech()">结束讲话</button>

    </div>

    <div id="messages"></div>

 

<script>

let ws = null;

let currentRoomId = null;

let currentName = null; 



function joinRoom() {

    const roomId = document.getElementById('roomId').value; 

    const name = document.getElementById('name').value; 

    currentRoomId = roomId;

    currentName = name;

    

    // ws = new WebSocket(`ws://************:6066`);

    ws = new WebSocket(`wss://************/ws`);

    

    ws.onopen  = () => {

        ws.send(JSON.stringify({ 

            type: 'join',

            roomId: roomId,

            name: name, 

        }));

        // addMessage(`${name}已加入会议室：${roomId}`);

    };

    

    ws.onmessage  = (e) => {

        const data = JSON.parse(e.data); 

        if (data.type  === 'hostFinished') {

            addMessage(`系统通知：${data.message}`); 

        }else if(data.type === 'setHost'){

            addMessage(`主持人变更通知：${data.message}`); 

        }else if(data.type === 'join'){

            addMessage(`加入会议室通知：${data.message}`); 

        }

    };

}

 

function setHost() {

    ws.send(JSON.stringify({ 

        type: 'setHost',

        roomId: currentRoomId,

        hostName: currentName,

    }));

    // addMessage('您已成为主持人');

}

 

function finishSpeech() {

    ws.send(JSON.stringify({ 

        type: 'hostFinished',

        hostName: currentName,

    }));

    // addMessage('已发送结束通知');

}

 

function addMessage(msg) {

    const div = document.createElement('div'); 

    div.textContent  = msg;

    document.getElementById('messages').appendChild(div); 

}

</script>

</body>

</html>