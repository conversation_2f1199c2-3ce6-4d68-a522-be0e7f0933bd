<?php

namespace app\api\controller;

use app\AdminController;

use support\Response;

use Webman\Http\Request;

use app\common\model\WxSsp as WxSspModel;

use think\facade\Db;

use think\helper\Str;

use system\File;

use FilesystemIterator;

use Functions;



/**

 * API用户登录

 */

class App_message

{

    //详情

    public function getmessagedetails(){

        $id = request()->post()['id'];

        // return json_encode($id);

        $list = DB::name('admin_notice')

                // ->where('b.type > 0')

                ->where('id',$id)

                ->select();

        for ($i=0; $i < count($list); $i++) { 

            // $res['url'] = '/pages/manage-files-details/manage-files-details?id='.$list[$i]['id'];

            $res['title'] = $list[$i]['title'];

            $res['date'] = date('Y-m-d',$list[$i]['create_time']);

            $res['content'] = $list[$i]['content'];

            // $res['attachment'][$i]['url'] = 'https://jsjsq.2ha.cc/'.$list[$i]['files'];

            $res['attachment'][$i]['name'] = $list[$i]['title'];

            $res['status'] = $list[$i]['status'];

        }

        return json_encode($res);

        

    }



    //未读

    public function getmessagelist(){

        $post = request()->post();

        // $request = json_decode($post['userinfo']);

        // return json_encode($post);

        $list = DB::name('admin_notice')

                ->where('admin_id',$post['user_id'])

                ->where('status',0)

                ->order("id desc")

                ->select();

        for ($i=0; $i < count($list); $i++) { 

            // return json_encode($list[$i]['create_time']);

            $res[$i]['url'] = '/pages/message-details/message-details?id='.$list[$i]['id'];

            $res[$i]['title'] = $list[$i]['title'];

            $res[$i]['date'] = date('Y-m-d',$list[$i]['create_time']);

        }

        return json_encode($res);

        

    }



    public function edit(){

        $post = request()->post();

        $data = [

            'status' => '1',

        ];

        $list = DB::name('admin_notice')

                ->where('id',$post['id'])

                ->save($data);

    }



    public function getmessageall(){

        $post = request()->post();

        // $request = json_decode($post['userinfo']);

        // return json_encode($request);

        $list = DB::name('admin_notice')->where('admin_id',$post['user_id'])->order("id desc")->select();

        for ($i=0; $i < count($list); $i++) { 

            $res[$i]['url'] = '/pages/message-details/message-details?id='.$list[$i]['id'];

            $res[$i]['title'] = $list[$i]['title'];

            $res[$i]['date'] = date('Y-m-d',$list[$i]['create_time']);

        }

        return json_encode($res);

        

    }



    



    

    

   

    

} 

