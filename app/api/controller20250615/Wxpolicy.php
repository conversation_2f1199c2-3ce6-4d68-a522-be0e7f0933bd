<?php

namespace app\api\controller;

use app\AdminController;

use support\Response;

use Webman\Http\Request;

use app\common\model\WxSsp as WxSspModel;

use think\facade\Db;

use think\helper\Str;

use system\File;

use FilesystemIterator;



/**

 * API用户登录

 */

class Wxpolicy

{
     /**
     * 配置文件
     */
    protected mixed $config = [];  
    
    public function __construct()
    {
        // 读取配置文件
        if ($config = saenv('site', true)) {
            $this->config = array_merge($this->config, $config);
        }
    }
    //列表

    public function select_policylist(){

        $post = request()->post();

        if (isset($post['id'])) {

            $res =  Db::name("wx_policy")->where("status",1)->where("id",$post['id'])->select();

        }else{

            $res =  Db::name("wx_policy")->where("status",1)->select()->toArray();

            

        }

        if (isset($post['index'])) {

            $res_index =  Db::name("wx_policy")->where("status",1)->order("id",'desc')->select();

            $res[0] = $res_index[0];

        }

        $j = 0;

        $z = 0;

        // return json_encode($res);

        $result = array();

        for ($i=0; $i < count($res); $i++) { 

            if ($res[$i]['policytype_id'] == '政务服务') {



                $result['zw'][$j]['url'] = '/pages/affairs-details/affairs-details?id='.$res[$i]['id'];

                $result['zw'][$j]['title'] = $res[$i]['policy_name'];

                $result['zw'][$j]['date'] = date("Y.m.d",strtotime($res[$i]['create_time']));

                $result['zw'][$j]['description'] = $res[$i]['introduction'];

                $result['zw'][$j]['policy_text'] = $res[$i]['policy_text'];

                $result['zw'][$j]['policy_id'] = $res[$i]['policytype_id'];

                $result['zw'][$j]['id'] = $res[$i]['id'];

                $result['zw'][$j]['image'] = $this->config['site_http'].json_decode($res[$i]['image'])[0]->src;

                $j++;

            }

            if ($res[$i]['policytype_id'] == '企业服务') {

                $result['qy'][$z]['url'] = '/pages/affairs-details/affairs-details?id='.$res[$i]['id'];

                $result['qy'][$z]['title'] = $res[$i]['policy_name'];

                $result['qy'][$z]['date'] = date("Y.m.d",strtotime($res[$i]['create_time']));

                $result['qy'][$z]['description'] = $res[$i]['introduction'];

                $result['qy'][$z]['policy_text'] = $res[$i]['policy_text'];

                $result['qy'][$z]['policy_id'] = $res[$i]['policytype_id'];

                $result['qy'][$z]['id'] = $res[$i]['id'];

                $result['qy'][$z]['image'] = $this->config['site_http'].json_decode($res[$i]['image'])[0]->src;

                $z++;

            }

            

        }

        

        return json_encode($result);

        

    }

    



   

    

}

