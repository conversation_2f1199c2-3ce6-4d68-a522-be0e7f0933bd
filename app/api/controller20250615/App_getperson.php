<?php
namespace app\api\controller;
use app\admin\model\Person;
use app\AdminController;
use support\Response;
use Webman\Http\Request;
use app\common\model\WxSsp as WxSspModel;
use app\admin\controller\sc\Index as ScIndex;
use think\facade\Db;
use think\helper\Str;
use system\File;
use FilesystemIterator;
use Functions;

/**
 * API用户登录
 */
class App_getperson
{
    //列表
    public function getotTal2(){
        $userinfo = get_admin_info();
        $id = 0;
        $column = 'community_id';
        if ($userinfo['grid_id'] !=null) {
            $id = $userinfo['grid_id'];
            $column = 'grid_id';
        }elseif ($userinfo['grid_group_id'] !=null) {
            $id = $userinfo['grid_group_id'];
            $column = 'grid_group_id';
        }elseif ($userinfo['community_id'] !=null) {
            $id = $userinfo['community_id'];
            $column = 'community_id';
        }
        $Scindex = new ScIndex();
        $total['rkfl'] = $Scindex->getRkflTotal($id,$column);
        $total['fwsj'] = $Scindex->getFwTotal($id,$column);
        $total['jgdw'] = $Scindex->getPlaceTotal($id,$column);
        $total['rkfb'] = $Scindex->getRkfbTotal($id,$column);
        $total['pffb'] = $Scindex->getPffbTotal($id,$column);
        $total['tsrq'] = $Scindex->getTsrqTotal3($id,$column);
        $total[$column] = $id;
        return json_encode($total);
    }

    public function getCommunity(){
        $post = request()->post();
        $request = json_decode($post['userinfo']);
        // return json_encode($request);
        $res = [];
        if ($request->grid_id !=null) {
            $res['grid'] = DB::table('grid')
                        ->field('grid_name')
                        ->where('grid_id',$request->grid_id)
                        ->find()['grid_name'];
        }
        if ($request->grid_group_id !=null) {
            $res['grid_group'] = DB::table('grid')
                            ->field('grid_group_name')
                            ->where('grid_group_id',$request->grid_group_id)
                            // ->where('community_id',$request->community_id)
                            ->find()['grid_group_name'];
        }
        if ($request->community_id !=null) {
            $res['community'] = DB::table('community')
                            ->field('community_name')
                            ->where('id',$request->community_id)
                            ->find()['community_name'];
        }
        return json_encode($res);
        
    }

} 
