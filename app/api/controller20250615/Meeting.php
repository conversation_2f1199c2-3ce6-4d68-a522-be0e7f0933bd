<?php

namespace app\api\controller;

use app\ApiController;
use app\common\model\Meeting as MeetingModel;
use app\admin\service\MeetingConfereeService;

/**
 * UPLOAD文件上传类
 */
define('DS', DIRECTORY_SEPARATOR);

/**
 * 视频会议API接口
 */
class Meeting extends ApiController
{
    /**
     * 配置文件
     */
    protected mixed $config = [];

    /**
     * 类构造函数
     * class constructor.
     * @throws InvalidArgumentException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function __construct()
    {
        if ($config = saenv('upload', true)) {
            $this->config = array_merge($this->config, $config);
        }
    }

    // 会议详情
    public function detail()
    {
        $roomId = input("roomId", 0);
        $model = new MeetingModel;
        $data = $model->where("room_id", $roomId)->find()->toArray();
        return json($data);
    }
    // 开始会议
    // public function start()
    // {
    //     $roomId = input("roomId", 0);
    //     $meeting = MeetingModel::where("room_id", $roomId)->find();
    //     if (!empty($meeting)) {
    //         $meeting = $meeting->toArray();
    //         $meeting['status'] = 1;
    //         MeetingModel::update($meeting);
    //         return $this->success('操作成功', '', []);
    //     } else {
    //         return $this->error('操作失败：会议ID无效', '', []);
    //     }
    // }
    // 结束会议
    public function stop()
    {
        $roomId = input("roomId", 0);
        $meeting = MeetingModel::where("room_id", $roomId)->find();
        if (!empty($meeting)) {
            $meeting = $meeting->toArray();
            $current_time = date('H:i:s');
            $meeting['end_time']  = $current_time;
            $meeting['status']    = 2;
            MeetingModel::update($meeting);
            return $this->success('操作成功', '', []);
        } else {
            return $this->error('操作失败：会议ID无效', '', []);
        }
    }

    // 会议期间附加参会人员
    public function appendConferees()
    {
        $roomId = input("roomId", 0);
        $admin_ids = input("admin_ids", []);
        $meeting = MeetingModel::where("room_id", $roomId)->find();
        if (!empty($meeting)) {
            $meeting = $meeting->toArray();

            $adminIds = explode(",", $admin_ids);
            $admin_info['id'] = $meeting['create_by'];
            // 添加参会人员
            MeetingConfereeService::appendConferees($admin_info, $meeting["id"], $adminIds);
            return $this->success('添加成功', '', []);
        } else {
            return $this->error('添加失败：会议ID无效', '', []);
        }
    }
    // 录制会议音频
    public function uploadRecording()
    { 
        $post = request()->post();
        
        $roomId = $post['roomId'];
        $userName = $post['userName'];
       
        // 读取音频文件
        $file = request()->file('file');
        if($file){
            // 使用用户名称md5值作为会议窗口唯一标识
            $clientId = md5($userName);
           
            $fileExt = $file->getUploadExtension();
            // 验证文件类型
            if (!in_array($fileExt, ['webm'])) {
                throw new \Exception('不支持的音频格式');
            }

            $savePath = public_path() . DS . $this->config['upload_path'] . DS . 'recordings' . DS . date($this->config['upload_style']) . DS . $roomId . DS;
            // 创建目录（若不存在）
            if (!is_dir($savePath)) {
                mkdir($savePath, 0777, true);
            }
            
            // 目标文件存储路径
            $fileName = $clientId . '.webm';
            $fullPath = $savePath . $fileName;

            // 追加写入文件内容
            $fileHandle = fopen($fullPath, 'a');
            flock($fileHandle, LOCK_EX);
            fwrite($fileHandle, file_get_contents($file->getRealPath()));
            flock($fileHandle, LOCK_UN);
            fclose($fileHandle);

            return json_encode(['status' => 'success', 'file' => $fileName, 'filePath' => $fullPath]);
        }
    }
    // 停止录制会议音频
    public function stopRecording()
    {
        $post = request()->post();
        $roomId = $post['roomId'];
        // $userName = $post['userName'];
        // $clientId = md5($userName);
 
        //更加会议ID查询所有的录制文件并将文件转换为mp3文件
        $savePath = public_path() . DS . $this->config['upload_path'] . DS . 'recordings' . DS . date($this->config['upload_style']) . DS . $roomId . DS;
        $files = glob($savePath . '*.webm');
        if ($files) {
            foreach ($files as $file) {
                $fileName = basename($file);
                $fileName = str_replace('.webm', '.wav', $fileName);
                $fullPath = $savePath . $fileName;
                // 调用ffmpeg命令将webm文件转换为mp3文件
                $cmd = "ffmpeg -i " . $file . " -vn -ar 44100 -ac 2 -ab 192k -f wav " . $fullPath;
                return $cmd;
                exec($cmd);
            }
            return json_encode(['status' => 'success', 'files' => count($files)]);
        }
        return json_encode(['status' => 'error', 'message' => '录制文件不存在']);
    }
}
