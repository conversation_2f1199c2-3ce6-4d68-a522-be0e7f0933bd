<?php

namespace app\api\middleware\system;

class Test
{
    private function aesEncrypt($data)
    {
        $key = $this->APP_ENCRYPT_KEY;
        $key = get_env('APP_ENCRYPT_KEY');
        print_r($key."\n");
        $iv = substr($key, 0, 16);
        return openssl_encrypt($data, 'AES-128-CBC', $key, 0, $iv);
    }

    private function aesDecrypt($data)
    {
        $key = $this->APP_ENCRYPT_KEY;
        $key = get_env('APP_ENCRYPT_KEY');
        $iv = substr($key, 0, 16);
        return openssl_decrypt($data, 'AES-128-CBC', $key, 0, $iv);
    }

    public function main() {
        $data = json_encode(['admin_id' => 265, 'date' => '2025-05-01','title'=>'测试']);
        $encrypted = $this->aesEncrypt($data);
        $decrypted = $this->aesDecrypt($encrypted);
        print_r($data."\n");
        print_r($encrypted."\n");
        print_r($decrypted."\n");
        print_r($this->aesDecrypt('OVD1QYn6gYWSdg5wHfvu+Q=='));
    }
}

$test = new Test();
$test->main();
