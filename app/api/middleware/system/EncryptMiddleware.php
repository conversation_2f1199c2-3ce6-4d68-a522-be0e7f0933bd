<?php

namespace app\api\middleware\system;

use Webman\Http\Request;
use Webman\Http\Response;
use Webman\MiddlewareInterface;

class EncryptMiddleware implements MiddlewareInterface
{
    public function process(Request $request, callable $handler): Response
    {
        // 解密请求数据
        if($request->method() != 'GET') {
            $raw = $request->rawBody();
            $newRequest = clone $request;
            if($raw) {
                $decrypted = $this->aesDecrypt($raw);
                $request->postData = json_decode($decrypted, true);
                // 将解密数据放入post参数中（关键修改）
//                $newRequest->_data = json_decode($decrypted, true);
//                $newRequest->request = $newRequest->_data; // 兼容性处理
            }
//            return response(json_encode($newRequest->_data));
//            $request = $newRequest;
        }

        $response = $handler($request);
        // 加密响应数据
        $data = json_decode($response->rawBody(), true);
        return response(json_encode($data));
//        $encrypted = $this->aesEncrypt(json_encode($data));
//        return $response->withBody($encrypted);
        return $response;
    }

    private function aesEncrypt($data)
    {
        $key = get_env('APP_ENCRYPT_KEY');
        $iv = substr($key, 0, 16);
        return openssl_encrypt($data, 'AES-128-CBC', $key, 0, $iv);
    }

    private function aesDecrypt($data)
    {
        $key = get_env('APP_ENCRYPT_KEY');
        $iv = substr($key, 0, 16);
        return openssl_decrypt($data, 'AES-128-CBC', $key, 0, $iv);
    }
}

