<?php

namespace app\api\middleware\system;

use Webman\Http\Request;
use Webman\Http\Response;
use Webman\MiddlewareInterface;

class EncryptMiddleware implements MiddlewareInterface
{
    public function process(Request $request, callable $handler): Response
    {
        // 解密请求数据
        if($request->method() != 'GET') {
            $raw = $request->rawBody();
            if($raw) {
                $decrypted = $this->aesDecrypt($raw);
                $decryptedData = json_decode($decrypted, true);

                if ($decryptedData !== null && is_array($decryptedData)) {
                    // 尝试使用反射设置POST数据
                    if (!$this->setRequestPostData($request, $decryptedData)) {
                        // 如果反射失败，使用包装类
                        $request = new DecryptedRequest($request, $decryptedData);
                    }
                }
            }
        }

        $response = $handler($request);
        return $response;
    }

    /**
     * 设置请求的POST数据
     * @return bool 成功返回true，失败返回false
     */
    private function setRequestPostData(Request $request, array $data): bool
    {
        try {
            // 尝试多种方法来设置POST数据

            // 方法1：直接通过Workerman的Request类设置
            $workermanRequestClass = 'Workerman\\Protocols\\Http\\Request';
            if ($request instanceof $workermanRequestClass) {
                $reflection = new \ReflectionClass($workermanRequestClass);
                $dataProperty = $reflection->getProperty('_data');
                $dataProperty->setAccessible(true);

                $currentData = $dataProperty->getValue($request);
                if (!is_array($currentData)) {
                    $currentData = [];
                }

                $currentData['post'] = $data;
                $dataProperty->setValue($request, $currentData);
                return true;
            }

            // 方法2：通过当前类的反射
            $reflection = new \ReflectionClass($request);
            while ($reflection) {
                if ($reflection->hasProperty('_data')) {
                    $dataProperty = $reflection->getProperty('_data');
                    $dataProperty->setAccessible(true);

                    $currentData = $dataProperty->getValue($request);
                    if (!is_array($currentData)) {
                        $currentData = [];
                    }

                    $currentData['post'] = $data;
                    $dataProperty->setValue($request, $currentData);
                    return true;
                }
                $reflection = $reflection->getParentClass();
            }

            return false;

        } catch (\Exception $e) {
            // 如果反射失败，记录错误
            error_log("EncryptMiddleware: Failed to set POST data - " . $e->getMessage());
            return false;
        }
    }

    private function aesEncrypt($data)
    {
        $key = get_env('APP_ENCRYPT_KEY');
        $iv = substr($key, 0, 16);
        return openssl_encrypt($data, 'AES-128-CBC', $key, 0, $iv);
    }

    private function aesDecrypt($data)
    {
        $key = get_env('APP_ENCRYPT_KEY');
        $iv = substr($key, 0, 16);
        return openssl_decrypt($data, 'AES-128-CBC', $key, 0, $iv);
    }
}

