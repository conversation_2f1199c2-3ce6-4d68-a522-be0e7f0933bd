<?php

namespace app\api\middleware\system;

use Webman\Http\Request;
use Webman\Http\Response;
use Webman\MiddlewareInterface;

class EncryptMiddleware implements MiddlewareInterface
{
    public function process(Request $request, callable $handler): Response
    {
        // 解密请求数据
        $appEncryptEnabled = get_env('APP_ENCRYPT_ENABLED');
        $clientType = $request->header('X-Client-Type') ?? '';
        if ($appEncryptEnabled
            && strtolower($clientType) === 'android'
            && $request->method() != 'GET') {

            $raw = $request->rawBody();
            if ($raw) {
                $decrypted = $this->aesDecrypt($raw);
                if ($decrypted === false) {
                    return \response('解密失败，请检查密钥是否正确以及请求参数是否加密', 400);
                }
                $decryptedData = json_decode($decrypted, true);
                if ($decryptedData !== null && is_array($decryptedData)) {
                    // 尝试使用反射设置POST数据
                    if (!$this->setRequestPostData($request, $decryptedData)) {
                        // 如果反射失败，使用包装类
                        $request = new DecryptedRequest($request, $decryptedData);
                    }
                }
            }

            $response = $handler($request);

            // 加密响应数据
            return $this->encryptResponse($response, $appEncryptEnabled);
        } else {
            $response = $handler($request);
            return $response;
        }
    }

    /**
     * 设置请求的POST数据
     * @return bool 成功返回true，失败返回false
     */
    private function setRequestPostData(Request $request, array $data): bool
    {
        try {
            // 方法1：直接通过Workerman的Request类设置
            $workermanRequestClass = 'Workerman\\Protocols\\Http\\Request';
            if ($request instanceof $workermanRequestClass) {
                $reflection = new \ReflectionClass($workermanRequestClass);
                $dataProperty = $reflection->getProperty('_data');
                $dataProperty->setAccessible(true);

                $currentData = $dataProperty->getValue($request);
                if (!is_array($currentData)) {
                    $currentData = [];
                }

                $currentData['post'] = $data;
                $dataProperty->setValue($request, $currentData);
                return true;
            }

            // 方法2：通过当前类的反射
//            $reflection = new \ReflectionClass($request);
//            while ($reflection) {
//                if ($reflection->hasProperty('_data')) {
//                    $dataProperty = $reflection->getProperty('_data');
//                    $dataProperty->setAccessible(true);
//
//                    $currentData = $dataProperty->getValue($request);
//                    if (!is_array($currentData)) {
//                        $currentData = [];
//                    }
//
//                    $currentData['post'] = $data;
//                    $dataProperty->setValue($request, $currentData);
//                    return true;
//                }
//                $reflection = $reflection->getParentClass();
//            }

            return false;

        } catch (\Exception $e) {
            // 如果反射失败，记录错误
            error_log("EncryptMiddleware: Failed to set POST data - " . $e->getMessage());
            return false;
        }
    }

    /**
     * 加密响应数据
     */
    private function encryptResponse(Response $response, $appEncryptEnabled): Response
    {
        try {
            // 获取响应内容
            $responseBody = $response->rawBody();
            // 检查是否需要加密（只加密JSON响应）
            if ($appEncryptEnabled && $this->shouldEncryptResponse($response, $responseBody)) {
                // 加密响应数据
                $encryptedData = $this->aesEncrypt($responseBody);
//                $encryptedData = $this->aesDecrypt($encryptedData);
                // 创建新的响应对象，保持原有的状态码和头部信息
                $newResponse = new Response(
                    $response->getStatusCode(),
                    $this->getEncryptedResponseHeaders($response),
                    $encryptedData
                );

                return $newResponse;
            }

            return $response;

        } catch (\Exception $e) {
            // 如果加密失败，记录错误但返回原始响应
            error_log("EncryptMiddleware: Failed to encrypt response - " . $e->getMessage());
            return $response;
        }
    }

    /**
     * 判断是否需要加密响应
     */
    private function shouldEncryptResponse(Response $response, string $responseBody): bool
    {
        // 只加密成功的响应（状态码200-299）
        $statusCode = $response->getStatusCode();
        if ($statusCode < 200 || $statusCode >= 300) {
            return false;
        }

        // 检查响应内容是否为空
        if (empty($responseBody)) {
            return false;
        }

        // 检查响应数据大小，超过5MB不加密，避免内存问题
        $responseSize = strlen($responseBody);
        if ($responseSize > 5 * 1024 * 1024) { // 5MB
            error_log("EncryptMiddleware: Response too large for encryption: {$responseSize} bytes");
            return false;
        }

        // 检查是否为有效的JSON
        $decoded = json_decode($responseBody, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return false;
        }

        return true;
    }

    /**
     * 获取加密响应的头部信息
     */
    private function getEncryptedResponseHeaders(Response $response): array
    {
        $headers = $response->getHeaders();

        // 修改Content-Type为纯文本，因为加密后不再是JSON
        $headers['Content-Type'] = 'text/plain; charset=utf-8';

        // 添加自定义头部标识这是加密响应
        $headers['X-Encrypted-Response'] = 'true';
        return $headers;
    }

    private function aesEncrypt($data)
    {
        $key = get_env('APP_ENCRYPT_KEY');
        $iv = get_env('APP_ENCRYPT_IV');
        $algorithm = get_env('APP_ENCRYPT_ALGORITHM') ?? 'AES-128-CBC';
        return openssl_encrypt($data, $algorithm, $key, 0, $iv);
    }

    private function aesDecrypt($data)
    {
        $key = get_env('APP_ENCRYPT_KEY');
        $iv = get_env('APP_ENCRYPT_IV');
        $algorithm = get_env('APP_ENCRYPT_ALGORITHM') ?? 'AES-128-CBC';
        return openssl_decrypt($data, $algorithm, $key, 0, $iv);
    }
}

