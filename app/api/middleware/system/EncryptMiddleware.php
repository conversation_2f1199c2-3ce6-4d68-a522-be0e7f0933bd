<?php

namespace app\api\middleware\system;

use Webman\Http\Request;
use Webman\Http\Response;
use Webman\MiddlewareInterface;

class EncryptMiddleware implements MiddlewareInterface
{
    public function process(Request $request, callable $handler): Response
    {


        // 解密请求数据
        $appEncryptEnabled = get_env('APP_ENCRYPT_ENABLED');
        $clientType = $request->header('X-Client-Type') ?? '';

        if ($appEncryptEnabled
            && strtolower($clientType) === 'android'
            && $request->method() != 'GET') {

            $raw = $request->rawBody();
            if ($raw) {
                $decrypted = $this->aesDecrypt($raw);
                if ($decrypted === false) {
                    return \response('解密失败，请检查密钥是否正确以及请求参数是否加密', 400);
                }
                $decryptedData = json_decode($decrypted, true);
                if ($decryptedData !== null && is_array($decryptedData)) {
                    // 尝试使用反射设置POST数据
                    if (!$this->setRequestPostData($request, $decryptedData)) {
                        // 如果反射失败，使用包装类
                        $request = new DecryptedRequest($request, $decryptedData);
                    }
                }
            }

            // 监控请求处理过程
            $startTime = microtime(true);
            $startMemory = memory_get_usage(true);

            try {
                error_log("EncryptMiddleware: Starting request processing for encrypted request");
                $response = $handler($request);

                $processingTime = microtime(true) - $startTime;
                $memoryUsed = memory_get_usage(true) - $startMemory;

                error_log("EncryptMiddleware: Encrypted request processed in {$processingTime}s, memory: " .
                         round($memoryUsed / 1024 / 1024, 2) . "MB");

            } catch (\Exception $e) {
                error_log("EncryptMiddleware: Encrypted request processing failed - " . $e->getMessage());
                throw $e;
            } catch (\Error $e) {
                error_log("EncryptMiddleware: Fatal error in encrypted request - " . $e->getMessage());
                throw $e;
            }

            // 加密响应数据，但添加安全检查
            return $this->encryptResponseSafely($response, $appEncryptEnabled);
        } else {
            $response = $handler($request);
            return $response;
        }
    }

    /**
     * 设置请求的POST数据
     * @return bool 成功返回true，失败返回false
     */
    private function setRequestPostData(Request $request, array $data): bool
    {
        try {
            // 方法1：直接通过Workerman的Request类设置
            $workermanRequestClass = 'Workerman\\Protocols\\Http\\Request';
            if ($request instanceof $workermanRequestClass) {
                $reflection = new \ReflectionClass($workermanRequestClass);
                $dataProperty = $reflection->getProperty('_data');
                $dataProperty->setAccessible(true);

                $currentData = $dataProperty->getValue($request);
                if (!is_array($currentData)) {
                    $currentData = [];
                }

                $currentData['post'] = $data;
                $dataProperty->setValue($request, $currentData);
                return true;
            }

            // 方法2：通过当前类的反射
//            $reflection = new \ReflectionClass($request);
//            while ($reflection) {
//                if ($reflection->hasProperty('_data')) {
//                    $dataProperty = $reflection->getProperty('_data');
//                    $dataProperty->setAccessible(true);
//
//                    $currentData = $dataProperty->getValue($request);
//                    if (!is_array($currentData)) {
//                        $currentData = [];
//                    }
//
//                    $currentData['post'] = $data;
//                    $dataProperty->setValue($request, $currentData);
//                    return true;
//                }
//                $reflection = $reflection->getParentClass();
//            }

            return false;

        } catch (\Exception $e) {
            // 如果反射失败，记录错误
            error_log("EncryptMiddleware: Failed to set POST data - " . $e->getMessage());
            return false;
        }
    }

    /**
     * 加密响应数据
     */
    private function encryptResponse(Response $response, $appEncryptEnabled): Response
    {
        try {
            // 获取响应内容
            $responseBody = $response->rawBody();
            // 检查是否需要加密（只加密JSON响应）
            if ($appEncryptEnabled && $this->shouldEncryptResponse($response, $responseBody)) {
                // 加密响应数据
                $encryptedData = $this->aesEncrypt($responseBody);
//                $encryptedData = $this->aesDecrypt($encryptedData);
                // 创建新的响应对象，保持原有的状态码和头部信息
                $newResponse = new Response(
                    $response->getStatusCode(),
                    $this->getEncryptedResponseHeaders($response),
                    $encryptedData
                );

                return $newResponse;
            }

            return $response;

        } catch (\Exception $e) {
            // 如果加密失败，记录错误但返回原始响应
            error_log("EncryptMiddleware: Failed to encrypt response - " . $e->getMessage());
            return $response;
        }
    }

    /**
     * 判断是否需要加密响应
     */
    private function shouldEncryptResponse(Response $response, string $responseBody): bool
    {
        // 只加密成功的响应（状态码200-299）
        $statusCode = $response->getStatusCode();
        if ($statusCode < 200 || $statusCode >= 300) {
            return false;
        }

        // 检查响应内容是否为空
        if (empty($responseBody)) {
            return false;
        }

        // 检查响应数据大小，超过5MB不加密，避免内存问题
        $responseSize = strlen($responseBody);
        if ($responseSize > 5 * 1024 * 1024) { // 5MB
            error_log("EncryptMiddleware: Response too large for encryption: {$responseSize} bytes");
            return false;
        }

        // 检查是否为有效的JSON
        $decoded = json_decode($responseBody, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return false;
        }

        return true;
    }

    /**
     * 获取加密响应的头部信息
     */
    private function getEncryptedResponseHeaders(Response $response): array
    {
        $headers = $response->getHeaders();

        // 修改Content-Type为纯文本，因为加密后不再是JSON
        $headers['Content-Type'] = 'text/plain; charset=utf-8';

        // 添加自定义头部标识这是加密响应
        $headers['X-Encrypted-Response'] = 'true';
        return $headers;
    }

    /**
     * 安全的响应加密方法，针对Windows环境优化
     */
    private function encryptResponseSafely(Response $response, $appEncryptEnabled): Response
    {
        try {
            if (!$appEncryptEnabled) {
                return $response;
            }

            // 获取响应内容
            $responseBody = $response->rawBody();
            $responseSize = strlen($responseBody);

            error_log("EncryptMiddleware: Processing response, size: " . round($responseSize / 1024 / 1024, 2) . "MB");

            // Windows环境下，对大响应更加保守，超过5MB就不加密
            if ($responseSize > 5 * 1024 * 1024) {
                error_log("EncryptMiddleware: Response too large for Windows encryption: {$responseSize} bytes, skipping encryption");
                return $response;
            }

            // 检查是否需要加密
            if ($this->shouldEncryptResponse($response, $responseBody)) {
                error_log("EncryptMiddleware: Starting encryption for Windows environment");

                // Windows环境下的内存管理
                $originalMemoryLimit = ini_get('memory_limit');
                ini_set('memory_limit', '1G'); // Windows下使用较小的内存限制

                // 强制垃圾回收
                if (function_exists('gc_collect_cycles')) {
                    gc_collect_cycles();
                }

                try {
                    // 分块处理大数据，避免一次性加密导致内存溢出
                    if ($responseSize > 1024 * 1024) { // 1MB以上分块处理
                        error_log("EncryptMiddleware: Using chunked encryption for large data");
                        $encryptedData = $this->chunkEncrypt($responseBody);
                    } else {
                        $encryptedData = $this->aesEncrypt($responseBody);
                    }

                    if ($encryptedData === false) {
                        error_log("EncryptMiddleware: Encryption failed, returning original response");
                        return $response;
                    }

                    error_log("EncryptMiddleware: Encryption completed successfully");

                    // 创建新的响应对象
                    $newResponse = new Response(
                        $response->getStatusCode(),
                        $this->getEncryptedResponseHeaders($response),
                        $encryptedData
                    );

                    return $newResponse;

                } finally {
                    // 恢复原始设置
                    ini_set('memory_limit', $originalMemoryLimit);

                    // 再次强制垃圾回收
                    if (function_exists('gc_collect_cycles')) {
                        gc_collect_cycles();
                    }
                }
            }

            return $response;

        } catch (\Exception $e) {
            error_log("EncryptMiddleware: Exception during encryption - " . $e->getMessage());
            return $response;
        } catch (\Error $e) {
            error_log("EncryptMiddleware: Fatal error during encryption - " . $e->getMessage());
            return $response;
        }
    }

    /**
     * 分块加密，避免大数据一次性加密导致内存问题
     */
    private function chunkEncrypt($data): string
    {
        try {
            // 对于大数据，直接使用标准加密，但添加内存监控
            $memoryBefore = memory_get_usage(true);
            $result = $this->aesEncrypt($data);
            $memoryAfter = memory_get_usage(true);

            error_log("EncryptMiddleware: Chunk encryption memory usage: " .
                     round(($memoryAfter - $memoryBefore) / 1024 / 1024, 2) . "MB");

            return $result;
        } catch (\Exception $e) {
            error_log("EncryptMiddleware: Chunk encryption failed - " . $e->getMessage());
            return false;
        }
    }

    private function aesEncrypt($data)
    {
        $key = get_env('APP_ENCRYPT_KEY');
        $iv = get_env('APP_ENCRYPT_IV');
        $algorithm = get_env('APP_ENCRYPT_ALGORITHM') ?? 'AES-128-CBC';
        return openssl_encrypt($data, $algorithm, $key, 0, $iv);
    }

    private function aesDecrypt($data)
    {
        $key = get_env('APP_ENCRYPT_KEY');
        $iv = get_env('APP_ENCRYPT_IV');
        $algorithm = get_env('APP_ENCRYPT_ALGORITHM') ?? 'AES-128-CBC';
        return openssl_decrypt($data, $algorithm, $key, 0, $iv);
    }
}

