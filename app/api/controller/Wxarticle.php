<?php
namespace app\api\controller;
use app\ApiController;
use support\Response;
use Webman\Http\Request;
use app\common\model\WxSsp as WxSspModel;
use think\facade\Db;
use think\helper\Str;
use system\File;
use FilesystemIterator;

/**
 * API用户登录
 */
class Wxarticle extends ApiController
{
    //列表
    public function select_my_article(){
    	if (request()->isPost()) {
	        $post = request()->post();
	        if (isset($post['user_id'])) {
	        	$where['user_id'] = $post['user_id'];
	        }else{
	        	$where['status'] = '已审核';
	        }
	        
	    }else{
	    	$where = [];
	    }
	        $article_list = DB::name("dictionary")->where("pid",'607')->select();
	        $list = array();
	        
	        for ($i=0; $i < count($article_list); $i++) { 
	            $res = Db::name("article")
	           			 ->where($where)
	                    ->where("article_id",$article_list[$i]['name'])
	                    ->order('create_time','desc')
	                    ->select();
	            
	            if (count($res) < 1) {
	                $list[$i] = [];
	            } else {
	                // 将结果转换为普通数组
	                $resArray = $res->toArray();
	                
	                for ($j=0; $j < count($resArray); $j++) { 
	                    $resArray[$j]['url'] = '/pages/article-details/article-details?id='.$resArray[$j]['id'];
	                    $resArray[$j]['author'] = $resArray[$j]['people'];
	                    $resArray[$j]['date'] = date("Y-m-d",$resArray[$j]['create_time']);
	                    $resArray[$j]['description'] = $resArray[$j]['content'];
	                }

	                $list[$i] = $resArray;
	            }
	        }
	        for ($i=0; $i < count($article_list); $i++) { 
	            $tabs[$i] = $article_list[$i]['name'];
	        }
	        
	        // $tabs = array_column($article_list, 'name');
	        $result['article_list'] = $tabs;
	        $result['list'] = $list;

	        return json_encode($result);
    }

    public function del_article(){
    	$post = request()->post();
    	$id = $post['id'];
    	$this->status=Db::name('article')->where('id', $id)->delete(); 
        return $this->status ? $this->success() : $this->error();
    }

    public function article_list(){
        $article_list = DB::name("dictionary")->where("pid",'607')->select();
        for ($i=0; $i < count($article_list); $i++) { 
            $tabs[$i]['title'] = $article_list[$i]['name'];
        }
        return json_encode($tabs);
    }

    public function submit(){
        $post = request()->post();
        // return json_encode($post['article_id']);
        $res['create_time'] = time();
        $image[0]['src'] = $post['images'][0];
        $res['image'] = json_encode($image);
        $res['status'] = '审核中';
        $res['people'] = $post['people'];
        $res['user_id'] = $post['user_id'];
        $res['content'] = $post['content'];
        $res['title'] = $post['title'];
        // unset($post['images']);
        for ($i = 0; $i < count($post['detailValue']); $i++) {
             $res['article_id'] = $post['detailValue'][$i];
            //  return json_encode($res);
             $result = Db::name("article")->save($res);
        }
        // $this->status = Db::name("article")->save($post);
        return $result ? $this->success() : $this->error();
    }

    public function select_my_article_detial(){
        $post = request()->post();
        $id = $post['id'];
        $res = Db::name("article")
               ->where("id",$id)
               ->find();
        $edit['page_view'] = $res['page_view'] + 1;
        $edit_page_view = Db::name("article")
               ->where("id",$id)
               ->save($edit);
        $res['author'] = $res['people'];
        $res['date'] = date("Y-m-d",$res['create_time']);
        $res['image'] = json_decode($res['image'])[0];
        return json_encode($res);
    }
}
