<?php

namespace app\api\controller;

use app\ApiController;
use app\admin\controller\sc\Index as ScIndex;
use support\Response;
use Webman\Http\Request;
use support\Log;

/**
 * 诊断测试控制器
 */
class DiagnosticTest extends ApiController
{
    /**
     * 测试不加密的getotTal2接口
     */
    public function testGetotTal2Plain(Request $request): Response
    {
        $userinfo = get_admin_info();
        $id = 0;
        $column = 'community_id';
        if ($userinfo['grid_id'] != null) {
            $id = $userinfo['grid_id'];
            $column = 'grid_id';
        } elseif ($userinfo['grid_group_id'] != null) {
            $id = $userinfo['grid_group_id'];
            $column = 'grid_group_id';
        } elseif ($userinfo['community_id'] != null) {
            $id = $userinfo['community_id'];
            $column = 'community_id';
        }

        $Scindex = new ScIndex();
        $total = [];
        $startTime = microtime(true);
        $memoryStart = memory_get_usage(true);
        
        try {
            Log::info('开始诊断测试');
            
            // 测试每个方法的执行时间和内存使用
            $stepStart = microtime(true);
            $total['rkfl'] = $Scindex->getRkflTotal($id, $column);
            $stepTime = microtime(true) - $stepStart;
            Log::info("getRkflTotal 耗时: {$stepTime}s, 内存: " . memory_get_usage(true));
            
            $stepStart = microtime(true);
            $total['fwsj'] = $Scindex->getFwTotal($id, $column);
            $stepTime = microtime(true) - $stepStart;
            Log::info("getFwTotal 耗时: {$stepTime}s, 内存: " . memory_get_usage(true));
            
            $stepStart = microtime(true);
            $total['jgdw'] = $Scindex->getPlaceTotal($id, $column);
            $stepTime = microtime(true) - $stepStart;
            Log::info("getPlaceTotal 耗时: {$stepTime}s, 内存: " . memory_get_usage(true));
            
            $stepStart = microtime(true);
            $total['rkfb'] = $Scindex->getRkfbTotal($id, $column);
            $stepTime = microtime(true) - $stepStart;
            Log::info("getRkfbTotal 耗时: {$stepTime}s, 内存: " . memory_get_usage(true));
            
            $stepStart = microtime(true);
            $total['pffb'] = $Scindex->getPffbTotal($id, $column);
            $stepTime = microtime(true) - $stepStart;
            Log::info("getPffbTotal 耗时: {$stepTime}s, 内存: " . memory_get_usage(true));
            
            $stepStart = microtime(true);
            $total['tsrq'] = $Scindex->getTsrqTotal3($id, $column);
            $stepTime = microtime(true) - $stepStart;
            Log::info("getTsrqTotal3 耗时: {$stepTime}s, 内存: " . memory_get_usage(true));
            
            $total[$column] = $id;
            
            $totalTime = microtime(true) - $startTime;
            $memoryEnd = memory_get_usage(true);
            $memoryUsed = $memoryEnd - $memoryStart;
            
            Log::info("总耗时: {$totalTime}s, 内存使用: {$memoryUsed} bytes");
            
        } catch (\Exception $e) {
            Log::error('诊断测试错误: ' . $e->getMessage());
            return $this->error('测试失败: ' . $e->getMessage());
        }

        // 计算响应大小
        $jsonData = json_encode($total);
        $responseSize = strlen($jsonData);
        
        $diagnosticInfo = [
            'total_time' => $totalTime ?? 0,
            'memory_used' => $memoryUsed ?? 0,
            'response_size' => $responseSize,
            'response_size_mb' => round($responseSize / 1024 / 1024, 2),
            'data_count' => [
                'rkfl_count' => count($total['rkfl'] ?? []),
                'fwsj_count' => count($total['fwsj'] ?? []),
                'jgdw_count' => count($total['jgdw'] ?? []),
                'rkfb_count' => isset($total['rkfb']) ? (is_array($total['rkfb']) ? count($total['rkfb']) : 1) : 0,
                'pffb_count' => isset($total['pffb']) ? (is_array($total['pffb']) ? count($total['pffb']) : 1) : 0,
                'tsrq_count' => count($total['tsrq'] ?? []),
            ]
        ];
        
        // 返回纯文本响应，避免加密
        return response(json_encode([
            'diagnostic' => $diagnosticInfo,
            'data' => $total
        ]), 200, [
            'Content-Type' => 'text/plain; charset=utf-8'
        ]);
    }
    
    /**
     * 测试单独的getTsrqTotal3方法
     */
    public function testTsrqTotal3Only(Request $request): Response
    {
        $userinfo = get_admin_info();
        $id = 0;
        $column = 'community_id';
        if ($userinfo['grid_id'] != null) {
            $id = $userinfo['grid_id'];
            $column = 'grid_id';
        } elseif ($userinfo['grid_group_id'] != null) {
            $id = $userinfo['grid_group_id'];
            $column = 'grid_group_id';
        } elseif ($userinfo['community_id'] != null) {
            $id = $userinfo['community_id'];
            $column = 'community_id';
        }

        $Scindex = new ScIndex();
        $startTime = microtime(true);
        $memoryStart = memory_get_usage(true);
        
        try {
            $result = $Scindex->getTsrqTotal3($id, $column);
            
            $totalTime = microtime(true) - $startTime;
            $memoryUsed = memory_get_usage(true) - $memoryStart;
            
            $jsonData = json_encode($result);
            $responseSize = strlen($jsonData);
            
            return response(json_encode([
                'success' => true,
                'time' => $totalTime,
                'memory' => $memoryUsed,
                'size' => $responseSize,
                'count' => count($result),
                'data' => $result
            ]), 200, [
                'Content-Type' => 'text/plain; charset=utf-8'
            ]);
            
        } catch (\Exception $e) {
            return response(json_encode([
                'success' => false,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]), 200, [
                'Content-Type' => 'text/plain; charset=utf-8'
            ]);
        }
    }
    
    /**
     * 测试getRbac数据量
     */
    public function testGetRbac(Request $request): Response
    {
        $rbac = getRbac();
        
        return response(json_encode([
            'count' => count($rbac),
            'data' => $rbac
        ]), 200, [
            'Content-Type' => 'text/plain; charset=utf-8'
        ]);
    }
    
    /**
     * 测试系统配置信息
     */
    public function testSystemInfo(Request $request): Response
    {
        $info = [
            'php_version' => PHP_VERSION,
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
            'post_max_size' => ini_get('post_max_size'),
            'upload_max_filesize' => ini_get('upload_max_filesize'),
            'current_memory_usage' => memory_get_usage(true),
            'peak_memory_usage' => memory_get_peak_usage(true),
            'webman_max_package_size' => config('server.max_package_size'),
            'encrypt_key_set' => !empty(get_env('APP_ENCRYPT_KEY')),
        ];
        
        return response(json_encode($info), 200, [
            'Content-Type' => 'text/plain; charset=utf-8'
        ]);
    }
}
