<?php
declare (strict_types=1);

namespace app\api\controller;

use app\ApiController;
use app\common\exception\OperateException;
use app\common\service\notice\EmailService;
use app\common\service\notice\SmsService;
use Psr\SimpleCache\InvalidArgumentException;
use support\Response;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use app\common\library\Upload;

/**
 * 异步调用
 */
class Ajax extends ApiController
{
    /**
     * 首页
     */
    public function index(): Response
    {
        return response('Hello!');
    }

    /**
     * 文件上传
     * @return Response|void
     * @throws DataNotFoundException
     * @throws DbException
     * @throws InvalidArgumentException
     * @throws ModelNotFoundException
     */
    public function upload()
    {
        if (request()->isPost()) {
            $file = Upload::instance()->upload();
            if (!$file) {
                return $this->error(Upload::instance()->getError());
            }
            return json($file);
        }
    }
    
    public function init(){
        $ids = input("ids",[]);
        $options = getDictionaryGroupForApp($ids);
        return json(['options'=>$options]);
    }

    public function getCommunityList(){
        return json(getCommunity());
    }

    public function getGridGroupList(){
        $community_id = input('community_id',0);
        return json(getGridGroup($community_id));
    }

    public function getGridList(){
        $grid_id = input('grid_id',0);
        return json(getGrid($grid_id));
    }

    public function getGridStreetList(){
        $community_id = input('community_id',0);
        $grid_id = input('grid_id',0);
        return json(getStreet($community_id,$grid_id));
    }

    public function getBuildingList(){
        $grid_id = input('grid_id',0);
        return json(getBuilding($grid_id));
    }

    public function getHouseList(){
        $building_id = input('building_id',0);
        return json(getHouse($building_id));
    }

    public function getPerson(){
        $id_code = input('id_code','');
        return json(getPerson($id_code,0));
    }

    public function getCateTree(){
        $dbname = input('dbname','');
        return json(getCateTree($dbname));
    }

    public function getCommonSearch(){
        $Communities = getCommunity();
        $community_id = $Communities[0]['id'];
        $GridGroups = getGridGroup($community_id);
        $grid_group_id = $GridGroups[0]['id'];
        $Grids = getGrid($grid_group_id);
        $options = [];
        foreach ($Communities as $item){
            $options['community'][] = ['value'=>$item['id'],'text'=>$item['community_name']];
        }
        foreach ($GridGroups as $item){
            $options['grid_group'][] = ['value'=>$item['id'],'text'=>$item['grid_name']];
        }
        foreach ($Grids as $item){
            $options['grid'][] = ['value'=>$item['id'],'text'=>$item['grid_name']];
        }
        return $options;
    }

    public function getPersonParam(){
        $param = $this->getCommonSearch();
        $ids = [67,404,408,59,57,54,56,58,64,65,61,63,66,55,68,69,70,71,72,73,74,75];
        $dictionaryGroup = getDictionaryGroupForApp($ids);
        $param['endowment_insurance'] = $dictionaryGroup[67];
        $param['rpr'] = $dictionaryGroup[404];
        $param['rpr_nature'] = $dictionaryGroup[408];
        $param['person_type'] = $dictionaryGroup[59];
        $param['enlistment'] = $dictionaryGroup[57];
        $param['nation'] = $dictionaryGroup[54];
        $param['education'] = $dictionaryGroup[56];
        $param['face'] = $dictionaryGroup[58];
        $param['sex'] = [['value'=>2,'text'=>'男'],['value'=>1,'text'=>'女']];
        $param['marry'] = $dictionaryGroup[64];
        $param['only_child'] = $dictionaryGroup[65];
        $param['other_child'] = $dictionaryGroup[66];
        $param['health'] = $dictionaryGroup[61];
        $param['labor_capacity'] = $dictionaryGroup[63];
        $param['religion'] = $dictionaryGroup[55];
        $param['tags']=['consistency'=>'人户一致','retire'=>'退休','disability'=>'残疾','dead'=>'死亡'];
        foreach ($dictionaryGroup[68] as $item){
            $param['tags'][$item['value']] = $item['text'];
        }
        foreach ($dictionaryGroup[69] as $item){
            $param['tags'][$item['value']] = $item['text'];
        }
        foreach ($dictionaryGroup[70] as $item){
            $param['tags'][$item['value']] = $item['text'];
        }
        foreach ($dictionaryGroup[71] as $item){
            $param['tags'][$item['value']] = $item['text'];
        }
        foreach ($dictionaryGroup[72] as $item){
            $param['tags'][$item['value']] = $item['text'];
        }
        foreach ($dictionaryGroup[73] as $item){
            $param['tags'][$item['value']] = $item['text'];
        }
        foreach ($dictionaryGroup[74] as $item){
            $param['tags'][$item['value']] = $item['text'];
        }
        foreach ($dictionaryGroup[75] as $item){
            $param['tags'][$item['value']] = $item['text'];
        }
        foreach ($param['endowment_insurance'] as &$item){
            $item['value'] = $item['value'] + 1;
        }
        foreach ($param['other_child'] as &$item){
            $item['value'] = $item['value'] + 1;
        }
        return json($param);
    }

    public function getBuildingParam(){
        $param = $this->getCommonSearch();
        $ids = [13,349,355,362,367,30,42,401];
        $dictionaryGroup = getDictionaryGroupForApp($ids);
        $param['build_types'] = $dictionaryGroup[13];
        $param['types'] = $dictionaryGroup[30];
        $param['purposes'] = $dictionaryGroup[42];
        $param['code_types'] = $dictionaryGroup[401];
        $param['property_right'] = $dictionaryGroup[349];
        $param['usage_category'] = $dictionaryGroup[355];
        $param['house_type'] = $dictionaryGroup[362];
        $param['residence_nature'] = $dictionaryGroup[367];
        $param['rent'] = [
            ['value'=>0, 'text'=>'否'],
            ['value'=>1, 'text'=>'是']
        ];
        $param['road'] = getStreetTop(1);
        $param['street'] = getStreetTop(2);
        return json($param);
    }

    public function getHouseParam(){
        $param = $this->getCommonSearch();
        $ids = [13,349,355,362,367];
        $dictionaryGroup = getDictionaryGroupForApp($ids);
        $param['build_types'] = $dictionaryGroup[13];
        $param['property_right'] = $dictionaryGroup[349];
        $param['usage_category'] = $dictionaryGroup[355];
        $param['house_type'] = $dictionaryGroup[362];
        $param['residence_nature'] = $dictionaryGroup[367];
        $param['rent'] = [['value'=>2,'text'=>'是'],['value'=>1,'text'=>'否']];
        return json($param);
    }

    public function getHousingParam()
    {
        $param = $this->getCommonSearch();
        $ids = [13,373,349,355,362,367,378];
        $dictionaryGroup = getDictionaryGroupForApp($ids);
        $param['build_type'] = $dictionaryGroup[13];
        $param['property_right'] = $dictionaryGroup[349];
        $param['usage_category'] = $dictionaryGroup[355];
        $param['house_type'] = $dictionaryGroup[362];
        $param['residence_nature'] = $dictionaryGroup[367];
        $param['rent_used'] = $dictionaryGroup[367];
        $param['rent_relation'] = $dictionaryGroup[378];
        return json($param);
    }

    public function getVehicleParam(){
        $param = $this->getCommonSearch();
        $ids = [30,42,401];
        $dictionaryGroup = getDictionaryGroupForApp($ids);
        $param['types'] = $dictionaryGroup[30];
        $param['purposes'] = $dictionaryGroup[42];
        $param['code_types'] = $dictionaryGroup[401];
        return json($param);
    }

    public function getPersonOtherParam(){
        $params = $this->getCommonSearch();
        $ids = input("ids",[]);
        $options = getDictionaryGroupForApp($ids);
        foreach ($options as $key=>$item){
            $params[$key] = $item;
        }
        $category = getDictionaryGroup($ids);
        return json(['params'=>$params,'category'=>$category]);
    }

    /**
     * 发送短信验证码
     * @return Response
     * @throws InvalidArgumentException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function smsSend(): Response
    {
        $mobile = input('mobile', '');
        $event = input('event', 'register');
        if (!SmsService::filterMobile($mobile)) {
            return $this->error('手机号码不正确');
        }
        SmsService::send($mobile, $event);
        return $this->success("验证码发送成功！");
    }

    /**
     * 发送邮件验证码
     * @return Response
     * @throws InvalidArgumentException
     * @throws OperateException
     */
    public function emailSend(): Response
    {
        $email = input('email');
        $event = input('event', 'register');
        if (!EmailService::filterEmail($email)) {
            return $this->error('邮件格式不正确');
        }
        EmailService::captcha($email, $event);
        return $this->success("验证码发送成功！");
    }
}
