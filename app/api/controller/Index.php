<?php
declare (strict_types=1);
// +----------------------------------------------------------------------
// | swiftAdmin 极速开发框架 [基于WebMan开发]
// +----------------------------------------------------------------------
// | Copyright (c) 2020-2030 http://www.swiftadmin.net
// +----------------------------------------------------------------------
// | swiftAdmin.net High Speed Development Framework
// +----------------------------------------------------------------------
// | Author: meystack <<EMAIL>>  Apache 2.0 License
// +----------------------------------------------------------------------
namespace app\api\controller;

use app\admin\enums\AdminEnum;
use app\ApiController;
use app\common\exception\OperateException;
use app\common\library\ResultCode;
use app\common\model\system\Admin;
use Webman\Event\Event;
use support\Cache;

/**
 * API接口前端示例文件
 */
class Index extends ApiController
{
    // 首页展示
    public function index(): \support\Response
    {
        return json(['msg' => 'success', 'data' => 'Hello']);
    }

    public function login(): \support\Response
    {
        $name = request()->post('username');
        $pwd = request()->post('password');
        $result = Admin::checkLogin($name, $pwd);
        if (empty($result)) {
            return $this->error('用户名或密码错误');
        }
        if ($result['status'] !== 1) {
            return $this->error(ResultCode::STATUSEXCEPTION['msg']);
        }
        try {
            $data['login_ip'] = request()->getRealIp();
            $data['login_time'] = time();
            $data['count'] = $result['count'] + 1;
            Admin::update($data, ['id' => $result['id']]);
            $adminInfo = Admin::alias("a")
            ->join("sa_community c","a.community_id=c.id","LEFT")
            ->join("sa_grid g","a.grid_id=g.id","LEFT")
            ->join("sa_grid gg","a.grid_group_id=gg.id","LEFT")
            ->where("a.id",$result['id'])->field("a.*,g.grid_name,gg.grid_name grid_group_name,c.community_name")
            ->find()->toArray();
            $adminInfo['token'] = request()->buildToken();
            request()->session()->set(AdminEnum::ADMIN_SESSION, $adminInfo);
            Admin::where("id",$adminInfo['id'])->update(['token'=>$adminInfo['token']]);
            return $this->success('登陆成功','',$adminInfo);
        } catch (\Throwable $th) {
            throw new OperateException($th->getMessage());
        }
    }

    public function checkLogin(): \support\Response
    {
        $uid = request()->header("Authorization");
        $adminInfo = Admin::where("id",$uid)->find();
        if($adminInfo){
            return $this->success("","",$adminInfo);
        }else{
            return $this->error("该账号已在其他设备上登录");
        }
    }

    public function checkOnline(): \support\Response
    {
        try {
            $token = request()->header("Authorization");
            // 根据$token查询管理员账号，如果查不到就报错。
            $adminInfo = Admin::where('token', $token)->find();
            if($adminInfo){
                return $this->success("", "", $adminInfo);
            } else {
                return $this->error("", "", '该账号已在其他设备上登录');
            }
        } catch (\Throwable $th) {
            return $this->error("", "", $th->getMessage());
        }
    }
}
