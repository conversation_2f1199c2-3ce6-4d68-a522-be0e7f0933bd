<?php

namespace app\api\controller;

use app\AdminController;

use support\Response;

use Webman\Http\Request;

use app\common\model\Addmap as AddmapModel;

use think\facade\Db;

use think\helper\Str;

use system\File;

use FilesystemIterator;



/**

 * API用户登录

 */

class Addmap

{

    //添加

    public function add(){

        if (request()->isPost()) {

            $post = request()->post();

            $data['admin_id'] = $post['admin_id'];
            $data['title'] = $post['title'];

            for ($i=0; $i < count($post['image']); $i++) { 
                $images[$i]['src'] = $post['image'][$i]['url'];
            }

            $data['image'] = json_encode($images);
            $data['admin_id'] = $post['admin_id'];

            $rand = $this -> rands();
            $data['longitude'] = $post['longitude'].$rand;
            $data['latitude'] = $post['latitude'].$rand;
            $data['create_time'] = date('Y-m-d H:i:s',time());
            $status = Db::name("addmap")->save($data);

            return $status;
        }
    }



    //查询

    public function find(){

        if (request()->isPost()) {

             $post = request()->post();
             $id = $post[0];

             $data = DB::table("sa_addmap")
                    ->join("sa_admin","sa_addmap.admin_id=sa_admin.id","LEFT")
                    ->field("sa_addmap.*,sa_admin.nickname")
                    ->where("sa_addmap.id",$id)
                    ->find();

            for ($i=0; $i < count(json_decode($data['image'])); $i++) { 
                $data['images'][] = json_decode($data['image'])[$i]->src;
            }

            return json_encode($data);
        }
    }



    public function select(){
        if (request()->isPost()) {

            $post = request()->post();
            return json_encode($post);
            $admin_id = $post['admin_id'];
            $where = array();

            if (isset($post['date'])) {
                $where[]=["create_time",'like','%'.$post['date'].'%'];
            }

            if (isset($post['title'])) {
                $where[]=["title",'like','%'.$post['title'].'%'];
            }

            $data = DB::name("addmap")
                    ->field("image,create_time,title,id")
                    ->where("admin_id",$admin_id)
                    ->where($where)
                    ->select();
     
            if(!empty($data)){
                for ($i=0; $i < count($data); $i++) { 
                    $res[$i]['image'] = json_decode($data[$i]['image'])[0]->src;
                    $res[$i]['id'] = $data[$i]['id'];
                    $res[$i]['date'] = $data[$i]['create_time'];
                    $res[$i]['title'] = $data[$i]['title'];
                }
            }
            
            if (isset($res)) {
                return json_encode($res);
            }
        }
    }

    //打卡轨迹

    public function select_gj(){

        if (request()->isPost()) {

            $post = request()->post();
            $admin_id = $post['admin_id'];

            if (isset($post['date'])) {
                $where[]=["create_time",'like','%'.$post['date'].'%'];
            }else{
                $date = date('Y-m-d');
                $where[]=["create_time",'like','%'.$date.'%'];
            }

            $data = DB::name("addmap")
                    ->where("admin_id",$admin_id)
                    ->where($where)
                    ->field('longitude,latitude')->select();
            return json_encode($data);
        }
    }



    public function rands(){

        $min = 10000;
        $max = 99999;
        $randomNumber = rand($min, $max);

        return $randomNumber;
    }



    

    



   

    

}

