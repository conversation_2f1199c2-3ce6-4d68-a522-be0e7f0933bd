<?php

namespace app\api\controller;

use app\ApiController;
use support\Response;
use Webman\Http\Request;

/**
 * 测试加密中间件
 */
class TestEncrypt extends ApiController
{
    /**
     * 测试POST参数接收
     */
    public function testPost(Request $request): Response
    {
        // 获取所有POST参数
        $postData = $request->post();
        
        // 获取特定参数
        $adminId = $request->post('admin_id');
        $date = $request->post('date');
        $title = $request->post('title');
        
        // 使用input方法获取参数
        $inputAdminId = $request->input('admin_id');
        $inputDate = $request->input('date');
        $inputTitle = $request->input('title');
        
        // 返回调试信息
        $result = [
            'post_all' => $postData,
            'post_specific' => [
                'admin_id' => $adminId,
                'date' => $date,
                'title' => $title
            ],
            'input_method' => [
                'admin_id' => $inputAdminId,
                'date' => $inputDate,
                'title' => $inputTitle
            ],
            'raw_body' => $request->rawBody(),
            'method' => $request->method(),
            'all_data' => $request->all()
        ];
        
        return $this->success('测试成功', '', $result);
    }
    
    /**
     * 生成测试用的加密数据
     */
    public function generateTestData(): Response
    {
        $testData = [
            'admin_id' => 265,
            'date' => '2025-05-01',
            'title' => '测试'
        ];
        
        $jsonData = json_encode($testData);
        $encrypted = $this->aesEncrypt($jsonData);
        
        return $this->success('生成成功', '', [
            'original' => $testData,
            'json' => $jsonData,
            'encrypted' => $encrypted
        ]);
    }
    
    /**
     * AES加密
     */
    private function aesEncrypt($data)
    {
        $key = get_env('APP_ENCRYPT_KEY');
        $iv = substr($key, 0, 16);
        return openssl_encrypt($data, 'AES-128-CBC', $key, 0, $iv);
    }
    
    /**
     * AES解密
     */
    private function aesDecrypt($data)
    {
        $key = get_env('APP_ENCRYPT_KEY');
        $iv = substr($key, 0, 16);
        return openssl_decrypt($data, 'AES-128-CBC', $key, 0, $iv);
    }
}
