<?php

namespace app\api\controller;

use app\ApiController;
use app\common\model\SlideImage as SlideImageModel;

/**
 * UPLOAD文件上传类
 */
define('DS', DIRECTORY_SEPARATOR);

/**
 * 轮播图API接口
 */
class SlideImage extends ApiController
{
    /**
     * 配置文件
     */
    protected mixed $config = [];

    /**
     * 类构造函数
     * class constructor.
     * @throws InvalidArgumentException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function __construct()
    {
        if ($config = saenv('upload', true)) {
            $this->config = array_merge($this->config, $config);
        }
    }

    // 列表查询
    public function list(){
        $list = DB::name("slide_image")->where("type",1)->select();
        return json_encode($list);
    }
}
