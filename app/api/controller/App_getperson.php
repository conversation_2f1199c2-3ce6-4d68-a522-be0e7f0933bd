<?php
namespace app\api\controller;
use app\admin\model\Person;
use app\AdminController;
use support\Response;
use Webman\Http\Request;
use app\common\model\WxSsp as WxSspModel;
use app\admin\controller\sc\Index as ScIndex;
use think\facade\Db;
use think\helper\Str;
use system\File;
use FilesystemIterator;
use Functions;
use support\Log; // 引入 Log 类

/**
 * API用户登录
 */
class App_getperson
{
    //列表
    public function getotTal2(){
        $userinfo = get_admin_info();
        $id = 0;
        $column = 'community_id';
        if ($userinfo['grid_id'] !=null) {
            $id = $userinfo['grid_id'];
            $column = 'grid_id';
        }elseif ($userinfo['grid_group_id'] !=null) {
            $id = $userinfo['grid_group_id'];
            $column = 'grid_group_id';
        }elseif ($userinfo['community_id'] !=null) {
            $id = $userinfo['community_id'];
            $column = 'community_id';
        }

        $Scindex = new ScIndex();
        $total = [];
        try {
            Log::info('一般信息');
            $total['rkfl'] = $Scindex->getRkflTotal($id,$column);
            Log::info('一般信息1'.json_encode($total['rkfl']));
            $total['fwsj'] = $Scindex->getFwTotal($id,$column);
            Log::info('一般信息2'.json_encode($total['fwsj']));
            $total['jgdw'] = $Scindex->getPlaceTotal($id,$column);
            Log::info('一般信息3'.json_encode($total['jgdw']));
//            $total['rkfb'] = $Scindex->getRkfbTotal($id,$column);
//            Log::info('一般信息4'.json_encode($total['rkfb']));
//            $total['pffb'] = $Scindex->getPffbTotal($id,$column);
//            Log::info('一般信息5'.json_encode($total['pffb']));
            $total['tsrq'] = $Scindex->getTsrqTotal3($id,$column);
            Log::info('一般信息6'.json_encode($total['tsrq']));
            $total[$column] = $id;
            Log::info(json_encode($total));
        }catch (\Exception $e) {
            Log::error('人口信息错误: ' . $e->getMessage());
        }

        return json_encode($total);
    }

    public function getotTal1(){
        $userinfo = get_admin_info();
        $id = 0;
        $column = 'community_id';
        if ($userinfo['grid_id'] !=null) {
            $id = $userinfo['grid_id'];
            $column = 'grid_id';
        }elseif ($userinfo['grid_group_id'] !=null) {
            $id = $userinfo['grid_group_id'];
            $column = 'grid_group_id';
        }elseif ($userinfo['community_id'] !=null) {
            $id = $userinfo['community_id'];
            $column = 'community_id';
        }

        $Scindex = new ScIndex();
        $total['tsrq'] = $Scindex->getTsrqTotal3($id,$column);
//        $total[$column] = $id;  //返回当前小区id
        return json_encode($total);
    }

    public function getCommunity(){
        $post = request()->post();
        $request = json_decode($post['userinfo']);
        // return json_encode($request);
        $res = [];
        if ($request->grid_id !=null) {
            $res['grid'] = DB::table('grid')
                        ->field('grid_name')
                        ->where('grid_id',$request->grid_id)
                        ->find()['grid_name'];
        }
        if ($request->grid_group_id !=null) {
            $res['grid_group'] = DB::table('grid')
                            ->field('grid_group_name')
                            ->where('grid_group_id',$request->grid_group_id)
                            // ->where('community_id',$request->community_id)
                            ->find()['grid_group_name'];
        }
        if ($request->community_id !=null) {
            $res['community'] = DB::table('community')
                            ->field('community_name')
                            ->where('id',$request->community_id)
                            ->find()['community_name'];
        }
        return json_encode($res);
        
    }

} 
