<?php
namespace app\api\controller;
use app\admin\controller\sc\Index as ScIndex;
use Functions;
use support\Log;
use think\facade\Db;

// 引入 Log 类

/**
 * API用户登录
 */
class App_getperson
{
    //列表
    public function getotTal2(){
        $userinfo = get_admin_info();
        $id = 0;
        $column = 'community_id';
        if ($userinfo['grid_id'] !=null) {
            $id = $userinfo['grid_id'];
            $column = 'grid_id';
        }elseif ($userinfo['grid_group_id'] !=null) {
            $id = $userinfo['grid_group_id'];
            $column = 'grid_group_id';
        }elseif ($userinfo['community_id'] !=null) {
            $id = $userinfo['community_id'];
            $column = 'community_id';
        }
        // 添加性能监控
        $startTime = microtime(true);
        $startMemory = memory_get_usage(true);
        $Scindex = new ScIndex();
        $total = [];
        try {
            Log::info('开始获取统计数据');

            // 设置更大的内存和时间限制，确保能处理大数据
            $originalMemoryLimit = ini_get('memory_limit');
            $originalTimeLimit = ini_get('max_execution_time');

            // Windows环境下使用较保守的设置
            ini_set('memory_limit', '1G');
            set_time_limit(180);

            // 强制垃圾回收
            if (function_exists('gc_collect_cycles')) {
                gc_collect_cycles();
            }

            Log::info('Windows环境 - 内存限制: ' . ini_get('memory_limit') . ', 时间限制: ' . ini_get('max_execution_time') . 's');
            Log::info('开始内存使用: ' . round(memory_get_usage(true) / 1024 / 1024, 2) . 'MB');

            $total['rkfl'] = $Scindex->getRkflTotal($id,$column);
            gc_collect_cycles(); // 强制垃圾回收
            Log::info('rkfl完成，内存使用: ' . round(memory_get_usage(true) / 1024 / 1024, 2) . 'MB');

            $total['fwsj'] = $Scindex->getFwTotal($id,$column);
            gc_collect_cycles();
            Log::info('fwsj完成，内存使用: ' . round(memory_get_usage(true) / 1024 / 1024, 2) . 'MB');

            $total['jgdw'] = $Scindex->getPlaceTotal($id,$column);
            gc_collect_cycles();
            Log::info('jgdw完成，内存使用: ' . round(memory_get_usage(true) / 1024 / 1024, 2) . 'MB');

            $total['rkfb'] = $Scindex->getRkfbTotal($id,$column);
            gc_collect_cycles();
            Log::info('rkfb完成，内存使用: ' . round(memory_get_usage(true) / 1024 / 1024, 2) . 'MB');

            $total['pffb'] = $Scindex->getPffbTotal($id,$column);
            gc_collect_cycles();
            Log::info('pffb完成，内存使用: ' . round(memory_get_usage(true) / 1024 / 1024, 2) . 'MB');

            // 获取完整的特殊人群统计
            $total['tsrq'] = $Scindex->getTsrqTotal3($id, $column);
            gc_collect_cycles();
            Log::info('tsrq完成，内存使用: ' . round(memory_get_usage(true) / 1024 / 1024, 2) . 'MB');

            $total[$column] = $id;
            Log::info('所有数据获取完成');

            // 恢复原始设置
            ini_set('memory_limit', $originalMemoryLimit);
            set_time_limit($originalTimeLimit);

        }catch (\Exception $e) {
            Log::error('人口信息错误: ' . $e->getMessage());
            Log::error('错误堆栈: ' . $e->getTraceAsString());

            // 恢复原始设置
            if (isset($originalMemoryLimit)) {
                ini_set('memory_limit', $originalMemoryLimit);
            }
            if (isset($originalTimeLimit)) {
                set_time_limit($originalTimeLimit);
            }

            // 返回错误信息而不是空数组
            return json_encode([
                'error' => true,
                'message' => $e->getMessage(),
                'partial_data' => $total
            ]);
        }

        // 检查响应大小
        $jsonResponse = json_encode($total);
        $responseSize = strlen($jsonResponse);
        Log::info('响应数据大小: ' . round($responseSize / 1024 / 1024, 2) . 'MB');
        Log::info('峰值内存使用: ' . round(memory_get_peak_usage(true) / 1024 / 1024, 2) . 'MB');
        $endTime = microtime(true);
        $endMemory = memory_get_usage(true);

        Log::info('Performance', [
            'execution_time' => $endTime - $startTime,
            'memory_used' => $endMemory - $startMemory,
            'peak_memory' => memory_get_peak_usage(true)
        ]);
        return $jsonResponse;
    }

    public function getotTal1(){
        $userinfo = get_admin_info();
        $id = 0;
        $column = 'community_id';
        if ($userinfo['grid_id'] !=null) {
            $id = $userinfo['grid_id'];
            $column = 'grid_id';
        }elseif ($userinfo['grid_group_id'] !=null) {
            $id = $userinfo['grid_group_id'];
            $column = 'grid_group_id';
        }elseif ($userinfo['community_id'] !=null) {
            $id = $userinfo['community_id'];
            $column = 'community_id';
        }

        $Scindex = new ScIndex();
        $total['tsrq'] = $Scindex->getTsrqTotal3($id,$column);
//        $total[$column] = $id;  //返回当前小区id
        return json_encode($total);
    }

    public function getCommunity(){
        $post = request()->post();
        $request = json_decode($post['userinfo']);

        $res = [];
        if ($request->grid_id !=null) {
            $res['grid'] = DB::table('grid')
                        ->field('grid_name')
                        ->where('grid_id',$request->grid_id)
                        ->find()['grid_name'];
        }
        if ($request->grid_group_id !=null) {
            $res['grid_group'] = DB::table('grid')
                            ->field('grid_group_name')
                            ->where('grid_group_id',$request->grid_group_id)
                            // ->where('community_id',$request->community_id)
                            ->find()['grid_group_name'];
        }
        if ($request->community_id !=null) {
            $res['community'] = DB::table('community')
                            ->field('community_name')
                            ->where('id',$request->community_id)
                            ->find()['community_name'];
        }
        return json_encode($res);
    }

    /**
     * 不加密版本的getotTal2接口，用于调试
     */
    public function getotTal2Plain(){
        $userinfo = get_admin_info();
        $id = 0;
        $column = 'community_id';
        if ($userinfo['grid_id'] !=null) {
            $id = $userinfo['grid_id'];
            $column = 'grid_id';
        }elseif ($userinfo['grid_group_id'] !=null) {
            $id = $userinfo['grid_group_id'];
            $column = 'grid_group_id';
        }elseif ($userinfo['community_id'] !=null) {
            $id = $userinfo['community_id'];
            $column = 'community_id';
        }

        $Scindex = new ScIndex();
        $total = [];
        $startTime = microtime(true);

        try {
            Log::info('开始获取统计数据(Plain版本)');

            $total['rkfl'] = $Scindex->getRkflTotal($id,$column);
            $total['fwsj'] = $Scindex->getFwTotal($id,$column);
            $total['jgdw'] = $Scindex->getPlaceTotal($id,$column);
            $total['tsrq'] = $this->getOptimizedTsrqTotal($Scindex, $id, $column);
            $total[$column] = $id;

            $totalTime = microtime(true) - $startTime;
            $total['_debug'] = [
                'execution_time' => $totalTime,
                'memory_usage' => memory_get_usage(true),
                'response_size' => strlen(json_encode($total))
            ];

            Log::info('Plain版本数据获取完成，耗时: ' . $totalTime . 's');

        }catch (\Exception $e) {
            Log::error('Plain版本错误: ' . $e->getMessage());
            return response(json_encode([
                'error' => true,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]), 200, [
                'Content-Type' => 'text/plain; charset=utf-8'
            ]);
        }

        // 返回纯文本响应，避免加密
        return response(json_encode($total), 200, [
            'Content-Type' => 'text/plain; charset=utf-8'
        ]);
    }

    /**
     * POST加密版本的getotTal2接口，用于调试加密问题
     */
    public function getotTal2Encrypted(){
        // 记录请求信息
        Log::info('POST加密请求开始', [
            'method' => request()->method(),
            'raw_body' => request()->rawBody(),
            'post_data' => request()->post(),
            'headers' => request()->header()
        ]);

        $userinfo = get_admin_info();
        $id = 0;
        $column = 'community_id';
        if ($userinfo['grid_id'] !=null) {
            $id = $userinfo['grid_id'];
            $column = 'grid_id';
        }elseif ($userinfo['grid_group_id'] !=null) {
            $id = $userinfo['grid_group_id'];
            $column = 'grid_group_id';
        }elseif ($userinfo['community_id'] !=null) {
            $id = $userinfo['community_id'];
            $column = 'community_id';
        }

        $Scindex = new ScIndex();
        $total = [];
        $startTime = microtime(true);

        try {
            Log::info('开始获取统计数据(加密版本)');

            // 设置内存和时间限制
            ini_set('memory_limit', '512M');
            set_time_limit(120);

            $total['rkfl'] = $Scindex->getRkflTotal($id,$column);
            Log::info('rkfl完成');

            $total['fwsj'] = $Scindex->getFwTotal($id,$column);
            Log::info('fwsj完成');

            $total['jgdw'] = $Scindex->getPlaceTotal($id,$column);
            Log::info('jgdw完成');

            // 优化特殊人群统计，限制数据量
            $total['tsrq'] = $this->getOptimizedTsrqTotal($Scindex, $id, $column);
            Log::info('tsrq完成');

            $total[$column] = $id;

            $totalTime = microtime(true) - $startTime;
            $total['_debug'] = [
                'execution_time' => $totalTime,
                'memory_usage' => memory_get_usage(true),
                'response_size' => strlen(json_encode($total)),
                'post_params' => request()->post()
            ];

            Log::info('加密版本数据获取完成，耗时: ' . $totalTime . 's');

        }catch (\Exception $e) {
            Log::error('加密版本错误: ' . $e->getMessage());
            Log::error('错误堆栈: ' . $e->getTraceAsString());

            // 返回错误信息
            return json_encode([
                'error' => true,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'partial_data' => $total
            ]);
        }

        Log::info('准备返回响应，数据大小: ' . strlen(json_encode($total)) . ' bytes');

        // 返回JSON响应，会被中间件加密
        return json_encode($total);
    }

    /**
     * 优化的特殊人群统计方法，限制数据量避免进程崩溃
     */
    private function getOptimizedTsrqTotal($Scindex, $id, $column) {
        try {
            // 限制只获取前5个类型的数据，避免数据量过大
            $rbac = array_slice(getRbac(), 0, 5);
            $alias = "t";
            $where[$alias.".".$column] = $id;

            if($column=='community_id'&&$id==0){
                $where=[];
            }

            $where['dead'] = 0;
            $total = [];

            foreach ($rbac as $item){
                try {
                    $currentWhere = $where;
                    $itemwhere = "";
                    if(array_key_exists('where',$item)){
                        $itemwhere = $item['where'];
                    }

                    $query = \think\facade\Db::name("person_".$item['table'])->alias($alias)
                        ->join("sa_person p",$alias.".person_id=p.id ");

                    if($item['join_house']==1){
                        $query->join("sa_building b","p.building_id = b.id",'left');
                        $query->join("sa_house h","p.house_id = h.id",'left');
                    }

                    $currentWhere['p.delete_time'] = null;
                    $query->where($currentWhere)->where($itemwhere);

                    $count = $query->count();
                    $data = [
                        'name' => $item['name'],
                        'value' => $count
                    ];
                    $total[] = $data;
                } catch (\Exception $e) {
                    // 单个类型查询失败时，记录错误但继续处理其他类型
                    Log::error("特殊人群统计错误 - {$item['name']}: " . $e->getMessage());
                    $total[] = [
                        'name' => $item['name'],
                        'value' => 0
                    ];
                }
            }

            return $total;

        } catch (\Exception $e) {
            Log::error('优化特殊人群统计错误: ' . $e->getMessage());
            return [];
        }
    }

}
