<?php
namespace app\api\controller;
use app\admin\model\Person;
use app\AdminController;
use support\Response;
use Webman\Http\Request;
use app\common\model\WxSsp as WxSspModel;
use app\admin\controller\sc\Index as ScIndex;
use think\facade\Db;
use think\helper\Str;
use system\File;
use FilesystemIterator;
use Functions;
use support\Log; // 引入 Log 类

/**
 * API用户登录
 */
class App_getperson
{
    //列表
    public function getotTal2(){
        $userinfo = get_admin_info();
        $id = 0;
        $column = 'community_id';
        if ($userinfo['grid_id'] !=null) {
            $id = $userinfo['grid_id'];
            $column = 'grid_id';
        }elseif ($userinfo['grid_group_id'] !=null) {
            $id = $userinfo['grid_group_id'];
            $column = 'grid_group_id';
        }elseif ($userinfo['community_id'] !=null) {
            $id = $userinfo['community_id'];
            $column = 'community_id';
        }

        $Scindex = new ScIndex();
        $total = [];
        try {
            Log::info('开始获取统计数据');

            // 设置内存和时间限制
            ini_set('memory_limit', '1024M');
            set_time_limit(180);

            $total['rkfl'] = $Scindex->getRkflTotal($id,$column);
            Log::info('rkfl完成');

            $total['fwsj'] = $Scindex->getFwTotal($id,$column);
            Log::info('fwsj完成');

            $total['jgdw'] = $Scindex->getPlaceTotal($id,$column);
            Log::info('jgdw完成');

            // 注释掉可能导致数据量过大的部分
            // $total['rkfb'] = $Scindex->getRkfbTotal($id,$column);
            // Log::info('rkfb完成');

            // $total['pffb'] = $Scindex->getPffbTotal($id,$column);
            // Log::info('pffb完成');

            // 优化特殊人群统计，限制数据量
            $total['tsrq'] = $Scindex->getTsrqTotal3($id, $column);
            Log::info('tsrq完成');

            $total[$column] = $id;
            Log::info('所有数据获取完成');

        }catch (\Exception $e) {
            Log::error('人口信息错误: ' . $e->getMessage());
            Log::error('错误堆栈: ' . $e->getTraceAsString());

            // 返回错误信息而不是空数组
            return json_encode([
                'error' => true,
                'message' => $e->getMessage(),
                'partial_data' => $total
            ]);
        }

        return json_encode($total);
    }

    public function getotTal1(){
        $userinfo = get_admin_info();
        $id = 0;
        $column = 'community_id';
        if ($userinfo['grid_id'] !=null) {
            $id = $userinfo['grid_id'];
            $column = 'grid_id';
        }elseif ($userinfo['grid_group_id'] !=null) {
            $id = $userinfo['grid_group_id'];
            $column = 'grid_group_id';
        }elseif ($userinfo['community_id'] !=null) {
            $id = $userinfo['community_id'];
            $column = 'community_id';
        }

        $Scindex = new ScIndex();
        $total['tsrq'] = $Scindex->getTsrqTotal3($id,$column);
//        $total[$column] = $id;  //返回当前小区id
        return json_encode($total);
    }

    public function getCommunity(){
        $post = request()->post();
        $request = json_decode($post['userinfo']);

        $res = [];
        if ($request->grid_id !=null) {
            $res['grid'] = DB::table('grid')
                        ->field('grid_name')
                        ->where('grid_id',$request->grid_id)
                        ->find()['grid_name'];
        }
        if ($request->grid_group_id !=null) {
            $res['grid_group'] = DB::table('grid')
                            ->field('grid_group_name')
                            ->where('grid_group_id',$request->grid_group_id)
                            // ->where('community_id',$request->community_id)
                            ->find()['grid_group_name'];
        }
        if ($request->community_id !=null) {
            $res['community'] = DB::table('community')
                            ->field('community_name')
                            ->where('id',$request->community_id)
                            ->find()['community_name'];
        }
        return json_encode($res);
    }

    /**
     * 不加密版本的getotTal2接口，用于调试
     */
    public function getotTal2Plain(){
        $userinfo = get_admin_info();
        $id = 0;
        $column = 'community_id';
        if ($userinfo['grid_id'] !=null) {
            $id = $userinfo['grid_id'];
            $column = 'grid_id';
        }elseif ($userinfo['grid_group_id'] !=null) {
            $id = $userinfo['grid_group_id'];
            $column = 'grid_group_id';
        }elseif ($userinfo['community_id'] !=null) {
            $id = $userinfo['community_id'];
            $column = 'community_id';
        }

        $Scindex = new ScIndex();
        $total = [];
        $startTime = microtime(true);

        try {
            Log::info('开始获取统计数据(Plain版本)');

            $total['rkfl'] = $Scindex->getRkflTotal($id,$column);
            $total['fwsj'] = $Scindex->getFwTotal($id,$column);
            $total['jgdw'] = $Scindex->getPlaceTotal($id,$column);
            $total['tsrq'] = $this->getOptimizedTsrqTotal($Scindex, $id, $column);
            $total[$column] = $id;

            $totalTime = microtime(true) - $startTime;
            $total['_debug'] = [
                'execution_time' => $totalTime,
                'memory_usage' => memory_get_usage(true),
                'response_size' => strlen(json_encode($total))
            ];

            Log::info('Plain版本数据获取完成，耗时: ' . $totalTime . 's');

        }catch (\Exception $e) {
            Log::error('Plain版本错误: ' . $e->getMessage());
            return response(json_encode([
                'error' => true,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]), 200, [
                'Content-Type' => 'text/plain; charset=utf-8'
            ]);
        }

        // 返回纯文本响应，避免加密
        return response(json_encode($total), 200, [
            'Content-Type' => 'text/plain; charset=utf-8'
        ]);
    }

    /**
     * POST加密版本的getotTal2接口，用于调试加密问题
     */
    public function getotTal2Encrypted(){
        // 记录请求信息
        Log::info('POST加密请求开始', [
            'method' => request()->method(),
            'raw_body' => request()->rawBody(),
            'post_data' => request()->post(),
            'headers' => request()->header()
        ]);

        $userinfo = get_admin_info();
        $id = 0;
        $column = 'community_id';
        if ($userinfo['grid_id'] !=null) {
            $id = $userinfo['grid_id'];
            $column = 'grid_id';
        }elseif ($userinfo['grid_group_id'] !=null) {
            $id = $userinfo['grid_group_id'];
            $column = 'grid_group_id';
        }elseif ($userinfo['community_id'] !=null) {
            $id = $userinfo['community_id'];
            $column = 'community_id';
        }

        $Scindex = new ScIndex();
        $total = [];
        $startTime = microtime(true);

        try {
            Log::info('开始获取统计数据(加密版本)');

            // 设置内存和时间限制
            ini_set('memory_limit', '512M');
            set_time_limit(120);

            $total['rkfl'] = $Scindex->getRkflTotal($id,$column);
            Log::info('rkfl完成');

            $total['fwsj'] = $Scindex->getFwTotal($id,$column);
            Log::info('fwsj完成');

            $total['jgdw'] = $Scindex->getPlaceTotal($id,$column);
            Log::info('jgdw完成');

            // 优化特殊人群统计，限制数据量
            $total['tsrq'] = $this->getOptimizedTsrqTotal($Scindex, $id, $column);
            Log::info('tsrq完成');

            $total[$column] = $id;

            $totalTime = microtime(true) - $startTime;
            $total['_debug'] = [
                'execution_time' => $totalTime,
                'memory_usage' => memory_get_usage(true),
                'response_size' => strlen(json_encode($total)),
                'post_params' => request()->post()
            ];

            Log::info('加密版本数据获取完成，耗时: ' . $totalTime . 's');

        }catch (\Exception $e) {
            Log::error('加密版本错误: ' . $e->getMessage());
            Log::error('错误堆栈: ' . $e->getTraceAsString());

            // 返回错误信息
            return json_encode([
                'error' => true,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'partial_data' => $total
            ]);
        }

        Log::info('准备返回响应，数据大小: ' . strlen(json_encode($total)) . ' bytes');

        // 返回JSON响应，会被中间件加密
        return json_encode($total);
    }

}
