<?php
declare (strict_types=1);

namespace app\api\controller;

use app\ApiController;
use app\common\model\PersonEmployment as PersonEmploymentModel;

/**
 * 
 * App充分就业管理
 * <!---->
 * Class AppPersonEmployment
 * @package app\api\controller
 */
class AppPersonEmployment extends ApiController
{

    public function __construct()
    {
        parent::__construct();
        $this->model = new PersonEmploymentModel;
    }

    // public function index()
    // {
    //     $alias = "e";
    //     $where = $this->getWhere($alias);
    //     return json($this->getList($alias,$where));
    // }
    //在业人口列表
    public function employment(){
        $alias = "e";
        $where = $this->getWhere($alias);
        $where["$alias.type"]=1;
        return json($this->getList($alias,$where));
    }
    //失业人口列表
    public function unemployment(){
        $alias = "e";
        $where = $this->getWhere($alias);
        $where["$alias.type"]=2;
        return json($this->getList($alias,$where));
    }
    //未就业人口列表
    public function notEmployment(){
        $alias = "e";
        $where = $this->getWhere($alias);
        $where["$alias.type"]=3;
        return json($this->getList($alias,$where));
    }
    //无就业信息人口列表
    public function noEmployment(){
        $alias = "e";
        $where = $this->getWhere($alias);
        $where[]= ["$alias.type", "not in", "1,2,3"];
        return json($this->getList($alias,$where));
    }

    public function getWhere($alias,$allow=true){
        $this->keepField='sex';
        $where = $this->buildSelectParams($alias . ".", $allow);
        $road_id = (int)input('road_id');
        $street_id = (int)input('street_id');
        $road_start = (int)input('road_start');
        $road_end = (int)input('road_end');
        $street_start = (int)input('street_start');
        $street_end = (int)input('street_end');
        $age_start = (int)input('age_start');
        $age_end = (int)input('age_end');
        $unit = (int)input('unit',0);
        $other_child = input("other_child");
        $endowment_insurance = input("endowment_insurance");
        $keyword = input('keyword','');
        if($other_child){
            $other_child--;
            $where[$alias.".other_child"]=$other_child;
        }
        if($endowment_insurance){
            $endowment_insurance--;
            $where[$alias.".endowment_insurance"]=$endowment_insurance;
        }
        if($age_start||$age_end){
            $exp = 'between';
            $arr = [$age_start,$age_end];
            if ($age_start === 0) {
                $exp = '<=';
                $arr = $age_end;
            } elseif ($age_end === 0) {
                $exp = '>=';
                $arr = $age_start;
            }
            $where[] = [$alias.".age", $exp, $arr];
        }
        $birth_start = input('birth_start','');
        $birth_end = input('birth_end','');
        if($birth_start||$birth_end){
            $exp = 'between';
            $arr = [$birth_start,$birth_end];
            if ($birth_start === '') {
                $exp = '<=';
                $arr = $birth_end;
            } elseif ($birth_end === '') {
                $exp = '>=';
                $arr = $birth_start;
            }
            $where[] = [$alias.".birth", $exp, $arr];
        }

        if($road_id){
            $where['b.road_id']=[$road_id];
        }
        if($street_id){
            $where['b.street_id']=[$street_id];
        }
        if($road_start){
            $where[]=['b.road_start','>=',$road_start];
        }
        if($road_end){
            $where[]=['b.road_end','<=',$road_end];
        }
        if($street_start){
            $where[]=['b.street_start','>=',$street_start];
        }
        if($street_end){
            $where[]=['b.street_end','<=',$street_end];
        }
        if($unit){
            $where['h.unit']=$unit;
        }
        if($keyword){
            $where[] = [$alias.".name|".$alias.".id_code|".$alias.".phone|".$alias.".address", 'like', '%' . $keyword . '%'];
        }
        return $where;
    }

    protected function getList($alias,$where){
        $page = (int)input('page', 1);
        $limit = (int)input('limit', 18);
        $keyword = input('keyword','');
        if($keyword){
            $where[] = ["p.name|p.id_code|p.phone|p.address", 'like', '%' . $keyword . '%'];
        }
        $count = $this->model->alias($alias)
            ->join("sa_person p","$alias.person_id=p.id")
            ->join("sa_building b", "p.building_id=b.id")
            ->join("sa_house h","p.house_id=h.id")
            ->where($where)->count();

        $fieldList = $this->model->getFields();
        $order = !array_key_exists('sort', $fieldList) ? $alias.'.id' : $alias.'.sort';
        $order =  "p.community_id,p.grid_group_id,p.grid_id,b.sort,h.unit,h.floor,h.sort,p.relation,".$order;
        $subQuery = $this->model->alias($alias)->field($alias.'.id')
            ->join("sa_person p","$alias.person_id=p.id")
            ->join("sa_community c",  "p.community_id=c.id", "LEFT")
            ->join("sa_grid g",  "p.grid_id=g.id", "LEFT")
            ->join("sa_grid gg", "p.grid_group_id=gg.id", "LEFT")
            ->join("sa_building b", "p.building_id=b.id")
            ->join("sa_house h","p.house_id=h.id")
            ->where($where)->order($order, 'desc')->limit($limit)->page($page)->buildSql();
        $subQuery = '( SELECT object.id FROM ' . $subQuery . ' AS object )';
        $list = $this->model->alias($alias)
            ->join("sa_person p","$alias.person_id=p.id")
            ->join("sa_community c",  "p.community_id=c.id", "LEFT")
            ->join("sa_grid g",  "p.grid_id=g.id", "LEFT")
            ->join("sa_grid gg", "p.grid_group_id=gg.id", "LEFT")
            ->join("sa_building b", "p.building_id=b.id")
            ->join("sa_house h","p.house_id=h.id")
            ->field("$alias.id,$alias.type,$alias.person_id,p.community_id,p.name,p.id_code,p.sex,p.age,p.education,p.face,p.nation,p.health,p.labor_capacity,p.person_type,
                g.grid_name,gg.grid_name grid_group_name,c.community_name,b.building_name")
            ->where($alias . '.id in' . $subQuery)->order($order)->select()->toArray();
        
        foreach ($list as &$item){
            $item['community']=$item['community_name']."-".$item['grid_group_name'].$item['grid_name'];
        }    
        return ['list' => $list,'count'=>$count,'sql'=>$this->model->getLastSql()];
    }

}
