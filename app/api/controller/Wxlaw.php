<?php

namespace app\api\controller;

use app\AdminController;

use support\Response;

use Webman\Http\Request;

use app\common\model\WxSsp as WxSspModel;

use think\facade\Db;

use think\helper\Str;

use system\File;

use FilesystemIterator;



/**

 * API用户登录

 */

class Wxlaw 

{

     /**
     * 配置文件
     */
    protected mixed $config = [];  
    
    public function __construct()
    {
        // 读取配置文件
        if ($config = saenv('site', true)) {
            $this->config = array_merge($this->config, $config);
        }
    }
    //律师列表
    public function select_er(){

        $post = request()->post();
       
        $where = [];
        $where[] = ['status', '=', 1];
        if(isset($post['search'])){
            $search = $post['search'];
            $where[] = ['Lawfirm_name|address|mobile', 'like', '%' . $search . '%'];
        }

        if (isset($post['three'])) {
            $res =  Db::name("wx_lawfirm")->where($where)->limit(3)->select()->toArray();
        }else{
            $res =  Db::name("wx_lawfirm")->where($where)->select()->toArray();
        }

        $result = [];
        for ($i=0; $i < count($res); $i++) { 

            $result[$i]['image'] = $this->config['site_http'].json_decode($res[$i]['image'])[0]->src;
            $result[$i]['title'] = $res[$i]['Lawfirm_name'];
            $result[$i]['company'] = $res[$i]['address'];
            $result[$i]['url'] = '/pages/lawyer-details/lawyer-details?id='.$res[$i]['id'];
            $result[$i]['mobile'] = $res[$i]['mobile'];;
            $result[$i]['wechat'] = $res[$i]['wechat'];
            $result[$i]['introduction'] = $res[$i]['introduction'];
            $result[$i]['id'] = $res[$i]['id'];
        }

        return json_encode($result);
    }

    //律师详情

    public function select_xq(){

        $id = request()->post()['id'];

        $res =  Db::name("wx_lawfirm")->where("id",$id)->find();

        return json_encode($res);
    }



    //法律法规列表

    public function select_law(){

        $post = request()->post();
       
        $where = [];
        $where[] = ['status', '=', 1];
        if(isset($post['search'])){
            $search = $post['search'];
            $where[] = ['lawtype_id|law_name|law_text', 'like', '%' . $search . '%'];
        }

        if (isset($post['id'])) {
            $res =  Db::name("wx_law")->where("status",1)->where("id",$post['id'])->select();
        }else{
            if (isset($post['name'])) {
            	if ($post['name'] !='全部') {
            		$res =  Db::name("wx_law")->where($where)->where("lawtype_id",$post['name'])->select()->toArray();
            	}else{
            		$res =  Db::name("wx_law")->where($where)->select()->toArray();
            	}
            }else{
                $res =  Db::name("wx_law")->where($where)->select()->toArray();
            }
        }
        // return Db::getLastSql();

        for ($i=0; $i < count($res); $i++) { 

            $result['list'][$i]['url'] = '/pages/law-details/law-details?id='.$res[$i]['id'];
            $result['list'][$i]['title'] = $res[$i]['law_name'];
            $result['list'][$i]['date'] = '日期：'.date("Y.m.d",strtotime($res[$i]['create_time']));
            $result['list'][$i]['description'] = $res[$i]['introduction'];
            $result['list'][$i]['id'] = $res[$i]['id'];
            $result['list'][$i]['law_text'] = $res[$i]['law_text'];
            $result['list'][$i]['lawtype_id'] = $res[$i]['lawtype_id'];
        }

        $result['type_list'] = DB::name("dictionary")
        					->where("pid",534)
        					->field('name')
        					->select();

        for ($i=0; $i < count($result['type_list']); $i++) { 

            // $value = '/pages/law/law?name=' . $result['type_list'][$i]['name'];
            $result['type_list']->offsetSet($i, ['name'=>$result['type_list'][$i]['name']]);
        }

        return json_encode($result);
    }
}

