<?php
declare (strict_types=1);
// +----------------------------------------------------------------------
// | swiftAdmin 极速开发框架 [基于WebMan开发]
// +----------------------------------------------------------------------
// | Copyright (c) 2020-2030 http://www.swiftadmin.net
// +----------------------------------------------------------------------
// | swiftAdmin.net High Speed Development Framework
// +----------------------------------------------------------------------
// | Author: meystack <<EMAIL>>  Apache 2.0 License
// +----------------------------------------------------------------------
namespace app\api\controller;

use app\ApiController;
use app\common\model\Building;
use app\common\model\House;
use app\common\model\PlaceCommunalFacility;
use app\common\model\PlaceEnterprise;
use app\common\model\PlaceGovernment;
use app\common\model\PlaceMerchant;
use app\common\model\PersonVehicle;
use think\facade\Db;

/**
 * API接口前端示例文件
 */
class AppBuilding extends ApiController
{
    /**
     * @var array|mixed|null
     */
    private mixed $admin_info;
      /**
     * 配置文件
     */
    protected mixed $config = [];  

    /**
     * 初始化方法
     */
    public function __construct()
    {
        parent::__construct();
        $this->admin_info = get_admin_info();
        // 读取配置文件
        if ($config = saenv('site', true)) {
            $this->config = array_merge($this->config, $config);
        }
    }
    // 首页展示
    public function building(): \support\Response
    {
        $page = (int)input('page', 1);
        $limit = (int)input('limit', 50);
        $alias = "b";
        $this->model = new Building();
        $where = $this->buildSelectParams("$alias.");
        $keyword = input('keyword','');
        if($keyword){
            $where[] = [$alias.".building_name|".$alias.".address", 'like', '%' . $keyword . '%'];
        }
        $fieldList = $this->model->getFields();
        $order = !array_key_exists('sort', $fieldList) ? 'id' : 'sort';
        $order = "$alias.community_id,$alias.grid_group_id,$alias.grid_id,$alias.".$order;
        $count = $this->model->alias($alias)->where($where)->count();
        $build_result = $this->model->alias("b")
            ->field("b.id,b.building_name,b.unit,b.floor,b.address,b.image,COUNT(DISTINCT h.id) AS house, COUNT(DISTINCT p.id) AS person")
            ->join("sa_house h","b.id=h.building_id","LEFT")
            ->join("sa_person p","h.id=p.house_id","LEFT")
            ->limit($limit)->page($page)
            ->where($where)->order($order)->group("b.id")->select()->toArray();
        foreach ($build_result as &$item){
            $item['url'] = "/pages/list/list?id=".$item['id'];
            if ($item['image']&&strpos($item['image'], 'http') !== 0) {
                $item['image'] = json_decode($item['image'],true);
                if($item['image']){
                    if(strpos($item['image'][0]['src'], 'http') !== 0){
                        $item['image'][0]['src']=$this->config['site_http'].getThumb($item['image'][0]['src']);
                    }
                    $item['image'] =  $item['image'][0]['src'];
                }
            }
        }
        return json(['building' => $build_result,'count'=>$count]);
    }

    public function house(): \support\Response
    {
        $id = input("id");
        $alias = "h";
        $building = Db::name("building")->alias($alias)
            ->join("sa_community c","$alias.community_id=c.id","LEFT")
            ->join("sa_grid g","$alias.grid_id=g.id","LEFT")
            ->join("sa_grid gg","$alias.grid_group_id=gg.id","LEFT")
            ->join("sa_street s","$alias.street_id=s.id","LEFT")
            ->join("sa_street r","$alias.road_id=r.id","LEFT")
            ->join("sa_dictionary d","d.pid=13 and $alias.type=d.value")
            ->field("$alias.*,g.grid_name,gg.grid_name grid_group_name,c.community_name,s.name street_name,r.name road_name, d.name type_name")
            ->where("$alias.id",$id)->find();
        $total = getTotal($id,"building_id");
        $building = array_merge($building,$total);
        if (!empty($building['image'])) {
            if(strpos($building['image'], 'http') !== 0){
                $building['image'] = json_decode($building['image'],true);
                if(count($building['image'])){
                    $building['banner'] = [];
                    foreach ($building['image'] as $item){
                        if(strpos($item['src'], 'http') !== 0){
                            $item['src'] = $this->config['site_http'].$item['src'];
                        }
                        $building['banner'][]= $item['src'];
                    }
                }
            }else{
                $building['banner'] = [$building['image']];
            }
        }
        $unit_list = Db::name("building")->alias("b")
            ->field("h.unit,COUNT(DISTINCT h.id) AS house, COUNT(DISTINCT p.id) AS person")
            ->join("sa_house h","b.id=h.building_id","LEFT")
            ->join("sa_person p","h.id=p.house_id","LEFT")
            ->where(["b.id"=>$building['id']])->order("h.unit")->group("h.unit")->select()->toArray();
        $list = [];
        foreach($unit_list as $unit){
            $list[$unit['unit']] = $unit;
        }
        $unit = 1;
        for($unit;$unit<=$building['unit'];$unit++){

            $list[$unit]['data'] = Db::name("house")->alias("h")
                ->join("sa_person p","p.house_id = h.id and p.relation=1","LEFT")
                ->field("h.*,p.name")
                ->where(["h.building_id"=>$id,"h.unit"=>$unit])->group("h.id")->order("h.floor desc,h.sort")->select()->toArray();
            foreach ($list[$unit]['data'] as &$item){
                $item['name']=Db::name("person")->where("house_id",$item['id'])->order("relation")->value("name");
                $item['tags'] = addTags($item,'house',true);
            }
            $list[$unit]['url'] = "/pages/place/housing?building_id=$id&unit=$unit&building_name=".$building['building_name'];
            foreach ($list[$unit]['data'] as &$item){
                $item['url'] = "/pages/population-manage/population-syrk?community_id={$item['community_id']}&grid_group_id={$item['grid_group_id']}&grid_id={$item['grid_id']}&building_id=$id&house_id=".$item['id'];
            }
        }
        $data = getTotal2($id,'building_id');
        $data['categories'] = ['党员','志愿者','高龄老人','退伍军人','空巢老人'];
        $where['p.building_id']=$id;
        $data['nums'][] = Db::name("person_communist")->alias("a")->join("sa_person p","a.person_id=p.id and p.delete_time is null and dead = 0")->where($where)->count();
        $data['nums'][] = Db::name("person_volunteer")->alias("a")->join("sa_person p","a.person_id=p.id and p.delete_time is null and dead = 0")->where($where)->count();
        $data['nums'][] = Db::name("person_oldest")->alias("a")->join("sa_person p","a.person_id=p.id and p.delete_time is null and dead = 0")->where($where)->count();
        $data['nums'][] = Db::name("person_soldier")->alias("a")->join("sa_person p","a.person_id=p.id and p.delete_time is null and dead = 0")->where($where)->count();
        $data['nums'][] = Db::name("person_elderly")->alias("a")->join("sa_person p","a.person_id=p.id and p.delete_time is null and dead = 0")->where($where)->where("a.type",1)->count();
        return json(['building' => $building,'list'=>$list,'data'=>$data]);
    }

    public function houseList(): \support\Response
    {
        $building_id = input("building_id",0);
        $unit = input("unit",0);
        $alias = "h";
        $where['building_id'] = $building_id;
        $where['unit'] = $unit;
        $count = Db::name("house")->alias($alias)->where($where)->count();
        $list = Db::name("house")->where($where)->order("floor desc,sort")->select()->toArray();
        foreach ($list as &$item){
            $item['url'] = "/pages/place/housing-add?id=".$item['id'];
        }
        $ids = [53,349,355,362,367,373,378];
        $dictionaryGroup = getDictionaryGroup($ids);
        return json(['list'=>$list,'count'=>$count,'params'=>$dictionaryGroup]);
    }

    public function merchant(): \support\Response
    {
        $page = (int)input('page', 1);
        $limit = (int)input('limit', 50);
        $alias = "m";
        $this->model = new PlaceMerchant();
        $where = $this->buildSelectParams("$alias.");
        $keyword = input('keyword','');
        if($keyword){
            $where[] = [$alias.".merchant_name|".$alias.".address", 'like', '%' . $keyword . '%'];
        }
        $fieldList = $this->model->getFields();
        $order = !array_key_exists('sort', $fieldList) ? 'id' : 'sort';
        $order = "$alias.community_id,$alias.grid_group_id,$alias.grid_id,$alias.".$order;
        $count = $this->model->alias($alias)->where($where)->count();
        $subQuery = $this->model->alias($alias)
            ->field($alias.'.id')->where($where)->order($order)->limit($limit)->page($page)->buildSql();
        $subQuery = '( SELECT object.id FROM ' . $subQuery . ' AS object )';
        $build_result = $this->model->alias($alias)
            ->join("sa_place_merchant_type t1","$alias.type_1_id=t1.id","LEFT")
            ->join("sa_place_merchant_type t2","$alias.type_2_id=t2.id","LEFT")
            ->field("$alias.id,$alias.merchant_name,$alias.address,$alias.image,t1.name t1_name,t2.name t2_name")
            ->where($alias.'.id in' . $subQuery)->order($order)->select()->toArray();
        foreach ($build_result as &$item){
            $item['url'] = "/pages/place/merchant?id=".$item['id'];
            if ($item['image']&&strpos($item['image'], 'http') !== 0) {
                $item['image'] = json_decode($item['image'],true);
                if($item['image']){
                    if(strpos($item['image'][0]['src'], 'http') !== 0){
                        $item['image'][0]['src']=$this->config['site_http'].getThumb($item['image'][0]['src']);
                    }
                    $item['image'] = $item['image'][0]['src'];}
            }
        }
        return json(['building' => $build_result,'count'=>$count]);
    }

    public function enterprise(): \support\Response
    {
        $page = (int)input('page', 1);
        $limit = (int)input('limit', 50);
        $alias = "m";
        $this->model = new PlaceEnterprise();
        $where = $this->buildSelectParams("$alias.");
        $keyword = input('keyword','');
        if($keyword){
            $where[] = [$alias.".enterprise_name|".$alias.".address", 'like', '%' . $keyword . '%'];
        }
        $fieldList = $this->model->getFields();
        $order = !array_key_exists('sort', $fieldList) ? 'id' : 'sort';
        $order = "$alias.community_id,$alias.grid_group_id,$alias.grid_id,$alias.".$order;
        $count = $this->model->alias($alias)->where($where)->count();
        $subQuery = $this->model->alias($alias)
            ->field($alias.'.id')->where($where)->order($order)->limit($limit)->page($page)->buildSql();
        $subQuery = '( SELECT object.id FROM ' . $subQuery . ' AS object )';
        $build_result = $this->model->alias($alias)
            ->join("sa_place_enterprise_type t1","$alias.type_1_id=t1.id","LEFT")
            ->join("sa_place_enterprise_type t2","$alias.type_2_id=t2.id","LEFT")
            ->field("$alias.id,$alias.enterprise_name,$alias.address,$alias.image,t1.name t1_name,t2.name t2_name")
            ->where($alias.'.id in' . $subQuery)->order($order)->select()->toArray();
        foreach ($build_result as &$item){
            $item['url'] = "/pages/place/enterprise?id=".$item['id'];
            if ($item['image']&&strpos($item['image'], 'http') !== 0) {
                $item['image'] = json_decode($item['image'],true);
                if($item['image']){
                    if(strpos($item['image'][0]['src'], 'http') !== 0){
                        $item['image'][0]['src']=$this->config['site_http'].getThumb($item['image'][0]['src']);
                    }
                    $item['image'] = $item['image'][0]['src'];}
            }
        }
        return json(['building' => $build_result,'count'=>$count]);
    }

    public function government(): \support\Response
    {
        $page = (int)input('page', 1);
        $limit = (int)input('limit', 50);
        $alias = "m";
        $this->model = new PlaceGovernment();
        $where = $this->buildSelectParams("$alias.");
        $keyword = input('keyword','');
        if($keyword){
            $where[] = [$alias.".unit_name|".$alias.".address", 'like', '%' . $keyword . '%'];
        }
        $fieldList = $this->model->getFields();
        $order = !array_key_exists('sort', $fieldList) ? 'id' : 'sort';
        $order = "$alias.community_id,$alias.grid_group_id,$alias.grid_id,$alias.".$order;
        $count = $this->model->alias($alias)->where($where)->count();
        $subQuery = $this->model->alias($alias)
            ->field($alias.'.id')->where($where)->order($order)->limit($limit)->page($page)->buildSql();
        $subQuery = '( SELECT object.id FROM ' . $subQuery . ' AS object )';
        $build_result = $this->model->alias($alias)
            ->field("$alias.id,$alias.unit_name,$alias.address,$alias.image,$alias.type")
            ->where($alias.'.id in' . $subQuery)->order($order)->select()->toArray();
        foreach ($build_result as &$item){
            $item['url'] = "/pages/place/government?id=".$item['id'];
            if ($item['image']&&strpos($item['image'], 'http') !== 0) {
                $item['image'] = json_decode($item['image'],true);
                if($item['image']){
                    if(strpos($item['image'][0]['src'], 'http') !== 0){
                        $item['image'][0]['src']=$this->config['site_http'].getThumb($item['image'][0]['src']);
                    }
                    $item['image'] = $item['image'][0]['src'];}
            }
            $item['type']=$item['type']==1?'机关单位':'事业单位';
        }
        return json(['building' => $build_result,'count'=>$count]);
    }

    public function communalFacility(): \support\Response
    {
        $page = (int)input('page', 1);
        $limit = (int)input('limit', 50);
        $alias = "m";
        $this->model = new PlaceCommunalFacility();
        $where = $this->buildSelectParams("$alias.");
        $keyword = input('keyword','');
        if($keyword){
            $where[] = [$alias.".name|".$alias.".address", 'like', '%' . $keyword . '%'];
        }
        $fieldList = $this->model->getFields();
        $order = !array_key_exists('sort', $fieldList) ? 'id' : 'sort';
        $order = "$alias.community_id,$alias.grid_group_id,$alias.grid_id,$alias.".$order;
        $count = $this->model->alias($alias)->where($where)->count();
        $subQuery = $this->model->alias($alias)
            ->field($alias.'.id')->where($where)->order($order)->limit($limit)->page($page)->buildSql();
        $subQuery = '( SELECT object.id FROM ' . $subQuery . ' AS object )';
        $build_result = $this->model->alias($alias)
            ->join("sa_place_communal_facility_type t1","$alias.type_1_id=t1.id","LEFT")
            ->join("sa_place_communal_facility_type t2","$alias.type_2_id=t2.id","LEFT")
            ->field("$alias.id,$alias.name,$alias.code,$alias.address,$alias.image,t1.name t1_name,t2.name t2_name")
            ->where($alias.'.id in' . $subQuery)->order($order)->select()->toArray();
        foreach ($build_result as &$item){
            $item['url'] = "/pages/place/communalFacility?id=".$item['id'];
            if ($item['image']&&strpos($item['image'], 'http') !== 0) {
                $item['image'] = json_decode($item['image'],true);
                if($item['image']){
                    if(strpos($item['image'][0]['src'], 'http') !== 0){
                        $item['image'][0]['src']=$this->config['site_http'].getThumb($item['image'][0]['src']);
                    }
                    $item['image'] = $item['image'][0]['src'];}
            }
        }
        return json(['building' => $build_result,'count'=>$count]);
    }

    public function vehicle(): \support\Response
    {
        $page = (int)input('page', 1);
        $limit = (int)input('limit', 50);
        $alias = "m";
        $this->model = new PersonVehicle();
        $where = $this->buildSelectParams("$alias.");
        $keyword = input('keyword','');
        if($keyword){
            $where[] = [$alias.".vehicle_code|".$alias.".id_code|".$alias.".owner", 'like', '%' . $keyword . '%'];
        }
        $fieldList = $this->model->getFields();
        $order = !array_key_exists('sort', $fieldList) ? $alias.'.id' : $alias.'.sort';
        $order = "$alias.community_id,$alias.grid_group_id,$alias.grid_id,".$order;
        $count = $this->model->alias($alias)->where($where)->count();
        $subQuery = $this->model->alias($alias)->field($alias.'.id')->where($where)->order($order)->limit($limit)->page($page)->buildSql();
        $subQuery = '( SELECT object.id FROM ' . $subQuery . ' AS object )';
        $build_result = $this->model->alias($alias)
            ->join("sa_dictionary d","d.pid=30 and ".$alias.".type = d.value","LEFT")
            ->join("sa_dictionary d1","d1.pid=42 and ".$alias.".purpose = d1.value","LEFT")
            ->join("sa_dictionary d2","d2.pid=401 and ".$alias.".code_type = d2.value","LEFT")
            ->field("$alias.id,$alias.brand,$alias.vehicle_code,$alias.owner,$alias.owner,$alias.phone,$alias.path image,
            d.name type,d1.name purpose,d2.name code_type")
            ->where($alias.'.id in' . $subQuery)->order($order)->select()->toArray();
        foreach ($build_result as &$item){
            $item['url'] = "/pages/place/vehicle?id=".$item['id'];
            if ($item['image']&&strpos($item['image'], 'http') !== 0) {
                $item['image'] = json_decode($item['image'],true);
                if($item['image']){
                    if(strpos($item['image'][0]['src'], 'http') !== 0){
                        $item['image'][0]['src']=$this->config['site_http'].getThumb($item['image'][0]['src']);
                    }
                    $item['image'] = $item['image'][0]['src'];}
            }
        }
        return json(['building' => $build_result,'count'=>$count,'sql'=>$this->model->getLastSql()]);
    }

    public function merchantDetail(): \support\Response
    {
        $id = input("id",0);
        $this->model = new PlaceMerchant();
        $alias = "m";
        $data = $this->model->alias($alias)
            ->join("sa_community c",$alias.".community_id=c.id","LEFT")
            ->join("sa_grid g",$alias.".grid_id=g.id","LEFT")
            ->join("sa_grid gg",$alias.".grid_group_id=gg.id","LEFT")
            ->join("sa_building b", $alias.".building_id=b.id","LEFT")
            ->join("sa_street s",$alias.".street_id=s.id","LEFT")
            ->join("sa_street r",$alias.".road_id=r.id","LEFT")
            ->join("sa_place_merchant_type t1","$alias.type_1_id=t1.id","LEFT")
            ->join("sa_place_merchant_type t2","$alias.type_2_id=t2.id","LEFT")
            ->field($alias.".*,g.grid_name,gg.grid_name grid_group_name,c.community_name,b.building_name,s.name street_name,r.name road_name,t1.name t1_name,t2.name t2_name")
            ->where("$alias.id",$id)->find()->toArray();
        $sql = $this->model->getLastSql();
        if (!empty($data['image'])) {
            if(strpos($data['image'], 'http') !== 0){
                $data['image'] = json_decode($data['image'],true);
                if(count($data['image'])){
                    $data['banner'] = [];
                    foreach ($data['image'] as $item){
                        if(strpos($item['src'], 'http') !== 0){
                            $item['src'] = $this->config['site_http'].$item['src'];
                        }
                        $data['banner'][]= $item['src'];
                    }
                }
            }else{
                $data['banner'] = [$data['image']];
            }
        }
        return json(['data' => $data]);
    }

    public function enterpriseDetail(): \support\Response
    {
        $id = input("id",0);
        $this->model = new PlaceEnterprise();
        $alias = "m";
        $data = $this->model->alias($alias)
            ->join("sa_community c",$alias.".community_id=c.id","LEFT")
            ->join("sa_grid g",$alias.".grid_id=g.id","LEFT")
            ->join("sa_grid gg",$alias.".grid_group_id=gg.id","LEFT")
            ->join("sa_building b", $alias.".building_id=b.id","LEFT")
            ->join("sa_street s",$alias.".street_id=s.id","LEFT")
            ->join("sa_street r",$alias.".road_id=r.id","LEFT")
            ->join("sa_place_enterprise_type t1","$alias.type_1_id=t1.id","LEFT")
            ->join("sa_place_enterprise_type t2","$alias.type_2_id=t2.id","LEFT")
            ->field($alias.".*,g.grid_name,gg.grid_name grid_group_name,c.community_name,b.building_name,s.name street_name,r.name road_name,t1.name t1_name,t2.name t2_name")
            ->where("$alias.id",$id)->find()->toArray();
        $sql = $this->model->getLastSql();
        if (!empty($data['image'])) {
            if(strpos($data['image'], 'http') !== 0){
                $data['image'] = json_decode($data['image'],true);
                if(count($data['image'])){
                    $data['banner'] = [];
                    foreach ($data['image'] as $item){
                        if(strpos($item['src'], 'http') !== 0){
                            $item['src'] = $this->config['site_http'].$item['src'];
                        }
                        $data['banner'][]= $item['src'];
                    }
                }
            }else{
                $data['banner'] = [$data['image']];
            }
        }
        return json(['data' => $data]);
    }

    public function governmentDetail(): \support\Response
    {
        $id = input("id",0);
        $this->model = new PlaceGovernment();
        $alias = "m";
        $data = $this->model->alias($alias)
            ->join("sa_community c",$alias.".community_id=c.id","LEFT")
            ->join("sa_grid g",$alias.".grid_id=g.id","LEFT")
            ->join("sa_grid gg",$alias.".grid_group_id=gg.id","LEFT")
            ->join("sa_building b", $alias.".building_id=b.id","LEFT")
            ->join("sa_street s",$alias.".street_id=s.id","LEFT")
            ->join("sa_street r",$alias.".road_id=r.id","LEFT")
            ->field($alias.".*,g.grid_name,gg.grid_name grid_group_name,c.community_name,b.building_name,s.name street_name,r.name road_name")
            ->where("$alias.id",$id)->find()->toArray();
        $sql = $this->model->getLastSql();
        if (!empty($data['image'])) {
            if(strpos($data['image'], 'http') !== 0){
                $data['image'] = json_decode($data['image'],true);
                if(count($data['image'])){
                    $data['banner'] = [];
                    foreach ($data['image'] as $item){
                        if(strpos($item['src'], 'http') !== 0){
                            $item['src'] = $this->config['site_http'].$item['src'];
                        }
                        $data['banner'][]= $item['src'];
                    }
                }
            }else{
                $data['banner'] = [$data['image']];
            }
        }
        return json(['data' => $data]);
    }

    public function communalFacilityDetail(): \support\Response
    {
        $id = input("id",0);
        $this->model = new PlaceCommunalFacility();
        $alias = "m";
        $data = $this->model->alias($alias)
            ->join("sa_community c",$alias.".community_id=c.id","LEFT")
            ->join("sa_grid g",$alias.".grid_id=g.id","LEFT")
            ->join("sa_grid gg",$alias.".grid_group_id=gg.id","LEFT")
            ->join("sa_place_communal_facility_type t1","$alias.type_1_id=t1.id","LEFT")
            ->join("sa_place_communal_facility_type t2","$alias.type_2_id=t2.id","LEFT")
            ->field($alias.".*,g.grid_name,gg.grid_name grid_group_name,c.community_name,t1.name t1_name,t2.name t2_name")
            ->where("$alias.id",$id)->find()->toArray();
        $sql = $this->model->getLastSql();
        if (!empty($data['image'])) {
            if(strpos($data['image'], 'http') !== 0){
                $data['image'] = json_decode($data['image'],true);
                if(count($data['image'])){
                    $data['banner'] = [];
                    foreach ($data['image'] as $item){
                        if(strpos($item['src'], 'http') !== 0){
                            $item['src'] = $this->config['site_http'].$item['src'];
                        }
                        $data['banner'][]= $item['src'];
                    }
                }
            }else{
                $data['banner'] = [$data['image']];
            }
        }
        return json(['data' => $data]);
    }

    public function vehicleDetail(): \support\Response
    {
        $id = input("id",0);
        $this->model = new PersonVehicle();
        $alias = "v";
        $data = $this->model->alias($alias)
            ->join("sa_community c",$alias.".community_id=c.id","LEFT")
            ->join("sa_grid g",$alias.".grid_id=g.id","LEFT")
            ->join("sa_grid gg",$alias.".grid_group_id=gg.id","LEFT")
            ->field($alias.".*,g.grid_name,gg.grid_name grid_group_name,c.community_name")
            ->where("$alias.id",$id)->find()->toArray();
        $data['banner'] = [];
        if (!empty($data['path'])) {
            if(strpos($data['path'], 'http') !== 0){
                $data['path'] = json_decode($data['path'],true);
                if(count($data['path'])){
                    foreach ($data['path'] as $item){
                        if(strpos($item['src'], 'http') !== 0){
                            $item['src'] = $this->config['site_http'].$item['src'];
                        }
                        $data['banner'][]= $item['src'];
                    }
                }
            }else{
                $data['banner'][] = $data['path'];
            }
        }
        $data['path'] = $data['banner'];
        $data['banner'] = [];
        if (!empty($data['code_path'])) {
            if(strpos($data['code_path'], 'http') !== 0){
                $data['code_path'] = json_decode($data['code_path'],true);
                if(count($data['code_path'])){
                    foreach ($data['code_path'] as $item){
                        if(strpos($item['src'], 'http') !== 0){
                            $item['src'] = $this->config['site_http'].$item['src'];
                        }
                        $data['banner'][]= $item['src'];
                    }
                }
            }else{
                $data['banner'][] = $data['code_path'];
            }
        }
        $data['code_path'] = $data['banner'];
        $data['banner'] = array_merge($data['path'],$data['code_path']);
        $ids = [30,42,401];
        $dictionaryGroup = getDictionaryGroup($ids);
        $options = getDictionaryGroupForApp($ids);
        return json(['data' => $data,'params'=>$dictionaryGroup,'options'=>$options]);
    }

    public function houseDetail(): \support\Response
    {
        $id = input("id",0);
        $building_id = input("building_id");
        $unit = input("unit");
        $this->model = new House();
        $alias = "m";
        if($id){
            $data = $this->model->alias($alias)
                ->field($alias.".*")
                ->where("$alias.id",$id)->find()->toArray();
        }else{
            $data = Db::name("building")->where("id",$building_id)
                ->field("id building_id,community_id,grid_group_id,grid_id, $unit unit,0 rent")
                ->find();
        }
        $data = dataDateFormat($data);
        $ids = [53,349,355,362,367,373,378];
        $dictionaryGroup = getDictionaryGroupForApp($ids);
        return json(['data' => $data,'params'=>$dictionaryGroup]);
    }

    public function merchantAdd(): \support\Response
    {
        $this->model = new PlaceMerchant();
        return $this->add();
    }

    public function enterpriseAdd(): \support\Response
    {
        $this->model = new PlaceEnterprise();
        return $this->add();
    }

    public function governmentAdd(): \support\Response
    {
        $this->model = new PlaceGovernment();
        return $this->add();
    }

    public function communalFacilityAdd(): \support\Response
    {
        $this->model = new PlaceCommunalFacility();
        return $this->add();
    }

    public function vehicleAdd(): \support\Response
    {
        $post = request()->post();
        $this->model = new PersonVehicle();

        $validate = $this->isValidate ? get_class($this->model) : $this->isValidate;
        $post = request_validate_rules($post, $validate, $this->scene);
        if (empty($post) || !is_array($post)) {
            return $this->error($post);
        }

        return $this->add();
    }

    public function houseAdd(): \support\Response
    {
        $this->model = new House();
        return $this->add();
    }

    public function add(): \support\Response{
        $post = request()->post();
        if (empty($post) || !is_array($post)) {
            return $this->error("参数错误");
        }
        $post = dataArrayToJson($post);
        if(empty($post['id'])){
            if(!empty($this->model->create_by)){
                $post[$this->model->create_by]=$this->admin_info['name'];
            }
            if(!empty($this->model->createTime)){
                $post[$this->model->createTime]=date('Y-m-d H:i:s');
            }
            $this->status = $this->model->create($post);
        }else{
            if(!empty($this->model->update_by)){
                $post[$this->model->update_by]=$this->admin_info['name'];
            }
            // 不更新创建日期、创建人
            unset($post['create_time']);
            unset($post['create_by']);

            $this->status = $this->model->update($post);
        }
        return $this->status ? $this->success() : $this->error();
    }

}
