<?php

namespace app\api\controller;

use app\AdminController;

use support\Response;

use Webman\Http\Request;

use app\common\model\WxSsp as WxSspModel;

use think\facade\Db;

use think\helper\Str;

use system\File;

use FilesystemIterator;
use app\ApiController;



/**

 * API用户登录

 */

class Wxoffers  extends ApiController

{

    public function select_xq(){

        $post = request()->post();

        // return json_encode($post);

        if (isset($post['id'])) {

            $id = $post['id'];

            $res = Db::name("wx_job_offers")->where("id",$post['id'])->select()->toArray();

            for ($i=0; $i < count($res); $i++) { 

                $result['title'] = $res[$i]['job_title'];

                $result['price'] = $res[$i]['salary_range'];

                $result['add'] = $res[$i]['location'];

                $result['companyName'] = $res[$i]['company_name'];

                $result['job_description'] = $res[$i]['job_description'];

                $result['contact_info'] = $res[$i]['contact_info'];

                $result['requirements'] = $res[$i]['requirements'];

                $type1 =  Db::name("place_merchant_type")->where("id",$res[$i]['type_1_id'])->find()['name'];

                if ($res[$i]['type_2_id'] == '未选择') {

                	$type2 =  '';

                }else{

                	$type2 =  Db::name("place_merchant_type")->where("id",$res[$i]['type_2_id'])->find()['name'];

                }

                

                $result['companyTag'] = array($res[$i]['company_size'],$type1,$type2);

            }



            return json_encode($result);

        }

    }



    public function select(){

        $post = request()->post();

        if (isset($post['id'])) {
            $res = Db::name("wx_job_offers")->where("id",$post['id'])->where("status",1)->select()->toArray();
        }else{
            if (isset($post['user_id'])) {
                $user_id = $post['user_id'];
                $res = Db::name("wx_job_offers")->where("user_id",$user_id)->select()->toArray();
            }else{
            	if (!empty($post['find'])) {
                    $where = [];
                    $where['type_1_id'] = $post['find'];
            		$res = Db::name("wx_job_offers")->where($where)->where("status",1)->select()->toArray();
            	}else{
            		$res = Db::name("wx_job_offers")->where("status",1)->select()->toArray();
            	}
            }
        }
        
        $type =  Db::name("place_merchant_type")->field('name')->where('pid',0)->select();
        
        $data = [];
        for ($i=0; $i < count($res); $i++) { 
            $data[$i]['title'] = $res[$i]['job_title'];
            $data[$i]['price'] = $res[$i]['salary_range'];
            $data[$i]['name'] = $res[$i]['company_name'];
            $data[$i]['id'] = $res[$i]['id'];
            $data[$i]['contact_info'] = $res[$i]['contact_info'];
            $data[$i]['status'] = $res[$i]['status'];
        }

        $result = [];
        $result['list'] = $data;
        $result['type'][0] = '全部';
        for ($j=0; $j < count($type); $j++) { 
        	$result['type'][] = $type[$j]['name'];
        }

        return json_encode($result);
    }

  

    public function select_off(){



        // if (request()->isPost()) {

            // $result = ['请选择'];

            $res = Db::name("place_merchant_type")->where("pid",'0')->field('name,id')->select()->toArray();

            for ($i=0; $i < count($res); $i++) { 

                $result[] = $res[$i]['name'];

                // $result[]['id'] = $res[$i]['id'];

            }

            return json_encode($result);

        // }

    }

    public function select_off_s(){



        $post = request()->post();

        // return json_encode($post);

        $name = $post['name'];

        $result = ['请选择'];

        $pid = Db::name("place_merchant_type")->where("name",$name)->find()['id'];

        $res = Db::name("place_merchant_type")->where("pid",$pid)->field("name,id")->select();

        for ($i=0; $i < count($res); $i++) { 

            $result[] = $res[$i]['name'];

        }

        return json_encode($result);
    }

    

    public function submit(){

        $data = request()->post();

        $data['type_1_id'] = Db::name("place_merchant_type")->where("name",$data['type_1_id'])->find()['id'];
        if ($data['type_2_id'] !="请选择") {
            $data['type_2_id'] = Db::name("place_merchant_type")->where("name",$data['type_2_id'])->find()['id'];
        }else{
            $data['type_2_id'] = '未选择';
        }

        $data['create_time'] = date("Y-m-d H:i:s");

        $result = Db::name("wx_job_offers")->save($data);
        return $result ? $this->success() : $this->error();
    }
}

