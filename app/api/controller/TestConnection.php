<?php

namespace app\api\controller;

use app\ApiController;
use app\admin\controller\sc\Index as ScIndex;
use support\Response;
use Webman\Http\Request;
use support\Log;

/**
 * 连接测试控制器
 */
class TestConnection extends ApiController
{
    /**
     * 测试简单POST请求
     */
    public function testSimplePost(Request $request): Response
    {
        Log::info('TestConnection: Simple POST test started');
        
        try {
            $postData = $request->post();
            Log::info('TestConnection: POST data received', $postData);
            
            $result = [
                'success' => true,
                'message' => 'POST请求成功',
                'post_data' => $postData,
                'timestamp' => time(),
                'memory_usage' => memory_get_usage(true)
            ];
            
            Log::info('TestConnection: Returning result', $result);
            
            return json_encode($result);
            
        } catch (\Exception $e) {
            Log::error('TestConnection: Error - ' . $e->getMessage());
            return json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * 测试小数据量的getotTal2
     */
    public function testSmallData(Request $request): Response
    {
        Log::info('TestConnection: Small data test started');
        
        try {
            $userinfo = get_admin_info();
            $id = 0;
            $column = 'community_id';
            if ($userinfo['grid_id'] !=null) {
                $id = $userinfo['grid_id'];
                $column = 'grid_id';
            }elseif ($userinfo['grid_group_id'] !=null) {
                $id = $userinfo['grid_group_id'];
                $column = 'grid_group_id';
            }elseif ($userinfo['community_id'] !=null) {
                $id = $userinfo['community_id'];
                $column = 'community_id';
            }

            $Scindex = new ScIndex();
            
            // 只获取基础数据，不获取复杂的统计
            $total = [];
            $total['rkfl'] = $Scindex->getRkflTotal($id,$column);
            $total[$column] = $id;
            $total['test_info'] = [
                'timestamp' => time(),
                'memory' => memory_get_usage(true),
                'size' => strlen(json_encode($total))
            ];
            
            Log::info('TestConnection: Small data test completed');
            
            return json_encode($total);
            
        } catch (\Exception $e) {
            Log::error('TestConnection: Small data test error - ' . $e->getMessage());
            return json_encode([
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * 测试逐步增加数据量
     */
    public function testIncrementalData(Request $request): Response
    {
        Log::info('TestConnection: Incremental data test started');
        
        try {
            $step = $request->post('step', 1);
            
            $userinfo = get_admin_info();
            $id = 0;
            $column = 'community_id';
            if ($userinfo['grid_id'] !=null) {
                $id = $userinfo['grid_id'];
                $column = 'grid_id';
            }elseif ($userinfo['grid_group_id'] !=null) {
                $id = $userinfo['grid_group_id'];
                $column = 'grid_group_id';
            }elseif ($userinfo['community_id'] !=null) {
                $id = $userinfo['community_id'];
                $column = 'community_id';
            }

            $Scindex = new ScIndex();
            $total = [];
            
            // 根据步骤逐步添加数据
            if ($step >= 1) {
                $total['rkfl'] = $Scindex->getRkflTotal($id,$column);
                Log::info('TestConnection: Step 1 completed');
            }
            
            if ($step >= 2) {
                $total['fwsj'] = $Scindex->getFwTotal($id,$column);
                Log::info('TestConnection: Step 2 completed');
            }
            
            if ($step >= 3) {
                $total['jgdw'] = $Scindex->getPlaceTotal($id,$column);
                Log::info('TestConnection: Step 3 completed');
            }
            
            if ($step >= 4) {
                // 获取简化的特殊人群数据（只取前3种）
                $rbac = array_slice(getRbac(), 0, 3);
                $tsrqData = [];
                foreach ($rbac as $item) {
                    $tsrqData[] = [
                        'name' => $item['name'],
                        'value' => rand(10, 100) // 使用随机数模拟，避免复杂查询
                    ];
                }
                $total['tsrq'] = $tsrqData;
                Log::info('TestConnection: Step 4 completed');
            }
            
            $total[$column] = $id;
            $total['step'] = $step;
            $total['size'] = strlen(json_encode($total));
            
            Log::info("TestConnection: Incremental test step {$step} completed, size: {$total['size']} bytes");
            
            return json_encode($total);
            
        } catch (\Exception $e) {
            Log::error('TestConnection: Incremental test error - ' . $e->getMessage());
            return json_encode([
                'success' => false,
                'error' => $e->getMessage(),
                'step' => $step ?? 0
            ]);
        }
    }
}
