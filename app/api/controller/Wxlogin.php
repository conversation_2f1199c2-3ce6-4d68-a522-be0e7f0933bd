<?php





namespace app\api\controller;



use app\admin\enums\AdminEnum;
use app\ApiController;
use app\common\exception\OperateException;
use app\common\model\system\User as UserModel;
use think\facade\Db;


/**

 * API用户登录

 */

class Wxlogin extends ApiController

{

    public function curl($url="",$data=""){

        $curl = curl_init();

        curl_setopt($curl, CURLOPT_URL, $url);

        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);

        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST,FALSE);

        curl_setopt($curl, CURLOPT_POST, 0);

        curl_setopt($curl, CURLOPT_POSTFIELDS, $data);

        $headerArray =array("Content-type:application/json;charset='utf-8'","Accept:application/json");

        curl_setopt($curl,CURLOPT_HTTPHEADER,$headerArray);

        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);

        $output = curl_exec($curl);

        curl_close($curl);

        return $output;

    }

    function generateRandomString($length) {

        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';

        $charactersLength = strlen($characters);

        $randomString = '';

        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[rand(0, $charactersLength - 1)];
        }
        return $randomString;
    }


    public function access_token($appId,$appSecret){

        // 构造获取access_token的URL
        $url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={$appId}&secret={$appSecret}";

        // 初始化cURL会话

        $ch = curl_init();

        // 设置cURL选项
        curl_setopt($ch, CURLOPT_URL, $url); // 目标URL
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true); // 返回结果而不是输出
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // 不验证证书 (如果你不需要验证证书)

        // 如果你需要验证证书，可以设置证书路径

        // curl_setopt($ch, CURLOPT_CAINFO, '/path/to/ca.pem');

        // 执行cURL会话
        $response = curl_exec($ch);

        // 检查是否有错误发生
        if(curl_errno($ch)){
            return 'cURL error: ' . curl_error($ch);
        }

        // 关闭cURL会话
        curl_close($ch);

        // 解析响应数据
        $data = json_decode($response, true);
        if (isset($data['access_token'])) {
            return $data['access_token'];
        } else {
            return $data['errcode'].$data['errmsg'];
        }
    }

    public function select(){

        $req = request()->post();

        $user_id = Db::name("user")
                ->where('openid',$req['openid'])
                ->find();

        if (!$user_id) {
            return NULL;
        }else{
            $user_id = $user_id['id'];
        }

        return $user_id;
    }



    public function add(){

        $req = request()->get();

        if (!$req['code'] || !$req['openid']) {
            $return_data = array();
            $return_data['error_code'] = 1;
            $return_data['msg'] = '参数不足';
            return json_encode($return_data);exit;
        }


        $appId = 'wx4f4fb3e1270327e3';
        $appSecret = '639485a674faec322160fec66289b365';

         $code = $req['code'];//小程序传来的code值

         //获取用户手机号
         if (isset($_SESSION['access_token'])){
                $access_token = $_SESSION['access_token'];
        }else{
            $access_token = $this->access_token($appId,$appSecret);

            // 设置 session 过期时间为两小时（7200秒）
            session_set_cookie_params(3000);

            // 存储 session 数据
            $_SESSION['access_token'] = $access_token;
        }

            
        $phone_url = 'https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token='.$access_token. '&code=' . $code;

        $phone_code = json_encode(array("code"=>$code));
        $phone_info = $this->curl($phone_url,$phone_code);
        $phone_json = json_decode($phone_info);
        $phone = $phone_json->phone_info->phoneNumber;

        $data = array();

        $data['mobile'] = $phone;
        $data['openid'] = $req['openid'];

        $data['create_time'] = time();

        $id = Db::name("user")->insertGetId($data);

        $return_data = array();

        $return_data['error_code'] = 0;

        $return_data['msg'] = '插入成功';

        $return_data['id'] = $id;

        return json_encode($return_data);exit;
    }

    // public function add(){

    //     $result = request()->get();

    //     // return json_encode($result);

    //     if (isset($result['encryptedData']) && isset($result['iv'])) {

    //         // 你的AppID和AppSecret

    //         $appId = 'wx4f4fb3e1270327e3';

    //         $appSecret = '639485a674faec322160fec66289b365';

    //         // 请求微信接口，获取openid和session_key

    //         // $url = "https://api.weixin.qq.com/sns/jscode2session?appid={$appId}&secret={$appSecret}&js_code={$code}&grant_type=authorization_code";

    //         // $response = file_get_contents($url);

    //         // $result = json_decode($response, true);

        

    //         // $openId = $result['openid'];

    //         $sessionKey = $_SESSION['session_key'];



    //         // 假设你已经从小程序端获取到了 encryptedData 和 iv

    //         $encryptedData = $result['encryptedData'];

    //         $iv = $result['iv'];



    //         // 使用 sessionKey 对 encryptedData 和 iv 进行解密

    //         $pc = new WXBizDataCrypt($appId, $sessionKey);

    //         $errCode = $pc->decryptData($encryptedData, $iv, $result);

    //         if ($errCode == 0) {

    //             $phoneNumber = $result['phoneNumber']; // 解密后的数据即为手机号

    //             // 处理手机号，例如将其存储到数据库或返回给小程序

    //             return json_encode(['phoneNumber' => $phoneNumber]);

    //         } else {

    //             // 解密失败，返回错误信息

    //             return json_encode(['error' => '解密失败']);

    //         }

    //     }



    // }

    public function login(){

        $code = request()->get()['code'];

        // 你的AppID和AppSecret
        $appId = 'wx4f4fb3e1270327e3';

        $appSecret = '639485a674faec322160fec66289b365';

        // $nonceStr = $this -> generateRandomString(32);

        // 请求微信接口，获取openid和session_key
        $url = "https://api.weixin.qq.com/sns/jscode2session?appid={$appId}&secret={$appSecret}&js_code={$code}&grant_type=authorization_code";
        $response = file_get_contents($url);
        $result = json_decode($response, true);

        if (isset($result['openid']) && isset($result['session_key'])) {
//            // 开启 session
//            if (isset($_SESSION['access_token'])){
//                $access_token = $_SESSION['access_token'];
//            }else{
//                $access_token = $this->access_token($appId,$appSecret);
//
//                // 设置 session 过期时间为两小时（7200秒）
//                session_set_cookie_params(3000);
//
//                // 存储 session 数据
//                $_SESSION['access_token'] = $access_token;
//            }

            // 根据openid查询用户信息，如果不存在则注册用户信息，如果存在则返回用户信息
            $user = User::checkWxLogin($result['openid']);
            if (empty($user)) {
                $user = User::wxRegister($result);
            }

            try {
                // 更新用户登录ip
                $data['login_ip'] = request()->getRealIp();
                $data['login_time'] = time();
                $data['login_count'] = $user['login_count'] + 1;
                UserModel::update($data, ['id' => $user['id']]);

                $adminInfo = UserModel::alias("a")
                    ->where("a.id",$user['id'])->field("a.*")
                    ->find()->toArray();
                request()->session()->set(AdminEnum::ADMIN_SESSION, $adminInfo);

                // 更新用户token
                $adminInfo['token'] = request()->buildToken();
                UserModel::where("id",$adminInfo['id'])->update(['token'=>$adminInfo['token']]);
                return json_encode($adminInfo);
            } catch (\Throwable $th) {
                throw new OperateException($th->getMessage());
            }
        }
   }

   public function info(){
       $post = request()->post();
       $user = UserModel::alias("a")
           ->field("a.*")
           ->where("a.id",$post['user_id'])
           ->findOrEmpty()->toArray();
       return json_encode($user);
   }

   public function setNickname(){
       $post = request()->post();

       UserModel::where("id",$post['user_id'])->update(['nickname'=>$post['nickname'],'avatar'=>$post['avatar']]);

       $user = UserModel::alias("a")
           ->field("a.*")
           ->where("a.id",$post['user_id'])
           ->findOrEmpty()->toArray();
       return json_encode($user);
   }
}

