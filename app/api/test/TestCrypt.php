<?php
function generateRandomString($length) {
    $chars = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $result = '';
    for ($i = 0; $i < $length; $i++) {
        $result .= $chars[random_int(0, strlen($chars) - 1)];
    }
    return $result;
}

function aesEncrypt($data)
{
    $key = 'UjsHfnPHY7UmyNet';
    $iv = '97raIcIhuvaKiVyh';
    $algorithm = 'AES-128-CBC';
    return openssl_encrypt($data, $algorithm, $key, 0, $iv);
}

function aesDecrypt($data)
{
    $key = 'UjsHfnPHY7UmyNet';
    $iv = '97raIcIhuvaKiVyh';
    $algorithm = 'AES-128-CBC';
    return openssl_decrypt($data, $algorithm, $key, 0, $iv);
}

//var_dump(aesEncrypt('{"admin_id":128}'));
var_dump(aesDecrypt('o1iBrNIOXbpnvq9eH+E/hQ=='));

//var_dump(generateRandomString(32));
//var_dump(generateRandomString(16));