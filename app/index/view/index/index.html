<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">
<title>SwiftAdmin 官方演示站</title>
<meta http-equiv="Cache-Control" content="no-transform " />
<include file="public:header" />
</head>
<body>
<div id="header">
	<include file="public:nav"/>
</div>

<div id="content" >
	<div class="layui-container">
	  <div class="layui-row">
	    <div class="layui-col-md6">
	    	<div class="layui-swift-box">
				<h1>SWIFTADMIN 极速开发框架</h1>
				<h1 style="font-size: 26px; font-weight: 300">基于高性能WM框架，性能在TP5 10倍以上</h1>
				<p>SwiftAdmin框架主张简单就是高效的原则，在设计和运维上采用最精简最高效的做法去完成业务系统的需求，并且基于ant Design的设计原则，是一款优秀的前后台极速开发解决方案。相信你第一眼就有立刻想体验SwiftAdmin框架的冲动和热情！</p>
				<div class="layui-swift-desc">
					<a href="#" onclick="javascript:layui.layer.msg('基于高性能WebMan');return false;" target="_blank">
						<img src="https://badgen.net/badge/WebMan/1LTS/red" alt="WebMan">
					</a>
					<a href="#" onclick="javascript:layui.layer.msg('前端采用Layui经典规范');return false;" target="_blank">
						<img src="https://badgen.net/badge/layui/2DEV/" alt="layui">
					</a>
					<a href="https://gitee.com/meystack/swiftadmin/stargazers" target="_blank">
						<img src="https://gitee.com/meystack/swiftadmin/badge/star.svg?theme=gvp" alt="star">
					</a>
					<a class="fork" href="https://gitee.com/meystack/swiftadmin/members" target="_blank">
						<img src="https://gitee.com/meystack/swiftadmin/badge/fork.svg?theme=gvp" alt="fork">
					</a>
					<a href="#" onclick="javascript:layui.layer.msg('请遵循Apache2.0开源协议精神');return false;" target="_blank">
						<img src="https://badgen.net/badge/license/Apache/" alt="swiftadmin">
					</a>
				</div>

				<div class="layui-swift-images">
					<img src="/static/images/demo/1.png" >
					<img src="/static/images/demo/2.png" >
					<img src="/static/images/demo/3.png" >
					<img class="hidden" src="/static/images/demo/4.png" >
				</div>
	    	</div>
	    </div>
	    <div class="layui-col-md6">
	      <img src="/static/images/hero-img.png" width="90%" height="500" alt="">
	    </div>
	  </div>

	</div>
</div>

<include file="public:footer"/>
</body>
<script>
	layui.use(['jquery','layer'],function(){
		var $ = layui.jquery;
		var layer = layui.layer;

		$('.layui-swift-images img').click(function(){
            layer.photos({
                photos: '.layui-swift-images',
                shadeClose: true,
                closeBtn: 2,
                anim: 10
            })
		})
	})
</script>	
</html>