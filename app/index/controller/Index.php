<?php
declare (strict_types=1);
// +----------------------------------------------------------------------
// | swiftAdmin 极速开发框架 [基于WebMan开发]
// +----------------------------------------------------------------------
// | Copyright (c) 2020-2030 http://www.swiftadmin.net
// +----------------------------------------------------------------------
// | swiftAdmin.net High Speed Development Framework
// +----------------------------------------------------------------------
// | Author: meystack <<EMAIL>> Apache 2.0 License
// +----------------------------------------------------------------------
namespace app\index\controller;

use app\HomeController;
use Psr\SimpleCache\InvalidArgumentException;
use support\Response;

class Index extends HomeController
{
    /**
     * 前端首页
     * @throws InvalidArgumentException
     */
    public function index()
    {
        return $this->redirect("/manage");
    }

}

