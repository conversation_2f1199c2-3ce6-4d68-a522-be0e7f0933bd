<?php
declare (strict_types = 1);
namespace app\admin\controller;

use app\AdminController;
use PhpOffice\PhpSpreadsheet\IOFactory;
use support\Response;
use think\helper\Str;
use Webman\Http\Request;
use app\common\model\PersonElderly as PersonElderlyModel;
use app\admin\model\Person;
use think\facade\Db;

/**
 * person_vehicle
 * 车辆管理
 * <!---->
 * Class PersonVehicle
 * @package app\admin\controller
 */
class PersonElderly extends AdminController
{
    /**
     * PersonVehicle模型对象
     * @var PersonElderlyModel
     */
    protected $type = 1;
    public function __construct()
    {
        parent::__construct();
        $this->model = new PersonElderlyModel;
        $this->columnFormat=[
            'A' => [0, 'index'],
            'B' => [1, 'community_name','column'=>'community_id'],
            'C' => [1, 'grid_group_name','column'=>'grid_group_id'],
            'D' => [1, 'grid_name','column'=>'grid_id'],
            'E' => [3, 'jurisdiction',501,'required'=>1],
            'F' => [1,'name'],
            'G' => [4, 'id_code'],
            'H' => [6, 'gender'],
            'I' => [6, 'age'],
            'J' => [4, 'phone'],
            'K' => [3, 'health', 61],
            'L' => [1, 'address'],
            'M' => [3,'house_type',519],
            'N' => [3, 'lease_type',362],
            'O' => [1,'guardian'],
            'P' => [4, 'guardian_id_code'],
            'Q' => [1,'guardian_relation'],
            'R' => [4, 'guardian_phone'],
            'S' => [1,'underwriter'],
            'T' => [1, 'post'],
            'U' => [4,'underwriter_phone'],
            'V' => [1, 'frequency'],
            'W' => [1,'details'],
            'X' => [1, 'wechat_code'],
            'Y' => [1,'grid_user'],
            'Z' => [1,'grid_phone'],
        ];
        $this->type = (int)input('type', 1);
        if($this->type==1){
            $this->tpl_name = "空巢老人模版";
        }else if($this->type==2){
            $this->tpl_name = "孤寡老人模版";
        }else if($this->type==3){
            $this->tpl_name = "独居老人模版";
        }
        $this->table_name = 'sa_person_elderly_imp';
        $this->table_source = "sa_person_elderly";
        $this->pk = "person_id";
        $this->row_index = 4;
    }

    /**
     * 添加资源
     * @return Response|void
     */
    public function add()
    {
        $type = (int)input('type', 1);
        if (request()->isPost()) {

            $post = $this->preRuleOutFields(\request()->post());
            if ($this->dataLimit) {
                $post[$this->dataLimitField] = get_admin_id();
            }

            $validate = $this->isValidate ? get_class($this->model) : $this->isValidate;
            $post = request_validate_rules($post, $validate, $this->scene);
            if (empty($post) || !is_array($post)) {
                return $this->error($post);
            }
            if(empty($post['person_id'])){
                return $this->error('请调取人口数据！');
            }
            $age = Db::name("person")->where("id",$post['person_id'])->value("age");
            if($age<60){
                return $this->error('年龄未满60岁');
            }
            $post = dataArrayToJson($post);
            if(!empty($this->model->create_by)){
                $post[$this->model->create_by]=$this->admin_info['name'];
            }
            if(!empty($this->model->createTime)){
                $post[$this->model->createTime]=date('Y-m-d H:i:s');
            }
            $this->status = $this->model->create($post);
            $Person = new Person();
            $Person->rbacReverse($post[$this->pk],$this->table_source,"type=$type");
            return $this->status ? $this->success() : $this->error();
        }
        $data = $this->getTableFields();
        $Communities = Db::name("community")->select()->toArray();
        $grid_groups = Db::name("grid")->where(["community_id"=>$data['community_id'],'grid_group'=>0])->select()->toArray();
        $grids = Db::name("grid")->where(["grid_group"=>$data['grid_group_id']])->select()->toArray();
        return $this->view('', ['data' => $data,
            'Communities'=>$Communities,
            'grid_groups'=>$grid_groups,
            'grids'=>$grids,
            'type'=>$type
        ]);
    }

    /**
     * 编辑资源
     * @return Response|void
     */
    public function edit()
    {
        $id = input('id');
        $data = $this->model->where('id', $id)->findOrEmpty()->toArray();

        // 限制数据调用
        if (!$this->authService->SuperAdmin() && $this->dataLimit
            && in_array($this->dataLimitField, $this->model->getFields())) {
            if ($data[$this->dataLimitField] != get_admin_id()) {
                return $this->error('没有权限');
            }
        }

        if (request()->isPost()) {
            $post = $this->preRuleOutFields(\request()->post());
            $validate = $this->isValidate ? get_class($this->model) : $this->isValidate;
            $post = request_validate_rules($post, $validate, $this->scene);
            if (empty($post) || !is_array($post)) {
                return $this->error($post);
            }
            if(empty($post['person_id'])){
                return $this->error('请调取人口数据！');
            }
            $age = Db::name("person")->where("id",$post['person_id'])->value("age");
            if($age<60){
                return $this->error('年龄未满60岁');
            }
            $post = dataArrayToJson($post);
            if(!empty($this->model->update_by)){
                $post[$this->model->update_by]=$this->admin_info['name'];
            }
            $this->status = $this->model->update($post);
            $Person = new Person();
            $Person->rbacReverse($post[$this->pk],$this->table_source,"type={$data['type']}");
            return $this->status ? $this->success() : $this->error();
        }

        /**
         * 默认共享模板
         */
        $template = str_replace('/_', '/', Str::snake(request()->getController()));
        $Communities = Db::name("community")->select()->toArray();
        $grid_groups = Db::name("grid")->where(["community_id"=>$data['community_id'],'grid_group'=>0])->select()->toArray();
        $grids = Db::name("grid")->where(["grid_group"=>$data['grid_group_id']])->select()->toArray();
        return $this->view($template . '/add', [
            'data' => $data,
            'Communities'=>$Communities,
            'grid_groups'=>$grid_groups,
            'grids'=>$grids,
            'type'=>$data['type']
        ]);
    }

    /**
     * index 函数
     */
    public function index()
    {
        $type = (int)input('type', 1);
        if (request()->isAjax()||request()->isPost()) {
            $page = (int)input('page', 1);
            $limit = (int)input('limit', 18);
            $alias = "e";
            $this->keepField='sex';
            $where = $this->buildSelectParams($alias.".");
            $age_start = (int)input('age_start');
            $age_end = (int)input('age_end');
            if($age_start||$age_end){
                $exp = 'between';
                $arr = [$age_start,$age_end];
                if ($age_start === 0) {
                    $exp = '<=';
                    $arr = $age_end;
                } elseif ($age_end === 0) {
                    $exp = '>=';
                    $arr = $age_start;
                }
                $where[] = ["p.age", $exp, $arr];
            }
            $keyword = input('keyword','');
            if($keyword){
                $where[] = ["p.name|"."p.id_code", 'like', '%' . $keyword . '%'];
            }
            $count = $this->model->alias($alias)
                ->join("sa_person p",$alias.".person_id=p.id and (p.delete_time is null or (p.delete_flag=1 and p.grid_id <> $alias.grid_id)) and dead = 0")
                ->where($where)->count();
            //$page = $count <= $limit ? 1 : $page;
            $fieldList = $this->model->getFields();
            $order = !array_key_exists('sort', $fieldList) ? $alias.'.id' : $alias.'.sort';
            $order = "$alias.community_id,$alias.grid_group_id,$alias.grid_id,".$order;
            $subQuery = $this->model->alias($alias)
                ->join("sa_person p",$alias.".person_id=p.id")
                ->field($alias.'.id')->where($where)->order($order)->limit($limit)->page($page)->buildSql();
            $subQuery = '( SELECT object.id FROM ' . $subQuery . ' AS object )';
            $list = $this->model->alias($alias)
                ->join("sa_community c",$alias.".community_id=c.id","LEFT")
                ->join("sa_grid g",$alias.".grid_id=g.id","LEFT")
                ->join("sa_grid gg",$alias.".grid_group_id=gg.id","LEFT")
                ->join("sa_person p",$alias.".person_id=p.id and (p.delete_time is null or (p.delete_flag=1 and p.grid_id <> $alias.grid_id)) and dead = 0")
                ->join("sa_building b", "p.building_id=b.id","LEFT")
                ->field($alias.".*,g.grid_name,gg.grid_name grid_group_name,c.community_name,
                p.name,p.id_code,p.age,p.sex,p.address,p.face,p.nation,p.education,p.school,p.major,p.hometown,p.phone,b.building_name")
                ->where($alias.'.id in' . $subQuery)->order($order)->select()->toArray();
            return $this->success($this->model->getLastSql(), '/', $list, $count);
        }else{
            Db::query("CREATE TEMPORARY TABLE temp_{$this->table_source} AS
SELECT person_id, min(id) as min_id,grid_id
FROM {$this->table_source} WHERE type = $type
GROUP BY person_id,grid_id;");
            Db::query("DELETE FROM {$this->table_source}
WHERE type = $type and id NOT IN (SELECT min_id FROM temp_{$this->table_source});");
            Db::query("DROP TEMPORARY TABLE temp_{$this->table_source};");
        }
        return $this->view('',['type'=>$type]);
    }
    
    public function proData(){
        $this->checkRule=[
            ['type'=>'required','title'=>'必填项未填','columns'=>"community_id,grid_group_id,grid_id,jurisdiction"],
        ];
        $type = (int)input("type",1);
        if (request()->isAjax()) {
            $page = (int)input('page', 1);
            $limit = (int)input('limit', 18);
            $alias = "e";
            $this->keepField='sex';
            $where = $this->buildSelectParams($alias.".");
            $key = input("check_type",0);
            $check_where = $this->buildCheckWhere("$alias.");
            $count = $this->model->alias($alias)
                ->join("sa_person p",$alias.".person_id=p.id")
                ->where($where)->where($check_where)->count();
            //$page = $count <= $limit ? 1 : $page;
            $fieldList = $this->model->getFields();
            $order = !array_key_exists('sort', $fieldList) ? $alias.'.id' : $alias.'.sort';
            $order = "$alias.community_id,$alias.grid_group_id,$alias.grid_id,".$order;
            $subQuery = $this->model->alias($alias)
                ->join("sa_person p",$alias.".person_id=p.id")
                ->field($alias.'.id')->where($where)->where($check_where)->order($order)->limit($limit)->page($page)->buildSql();
            $subQuery = '( SELECT object.id FROM ' . $subQuery . ' AS object )';
            $list = $this->model->alias($alias)
                ->join("sa_community c",$alias.".community_id=c.id","LEFT")
                ->join("sa_grid g",$alias.".grid_id=g.id","LEFT")
                ->join("sa_grid gg",$alias.".grid_group_id=gg.id","LEFT")
                ->join("sa_person p",$alias.".person_id=p.id and (p.delete_time is null or (p.delete_flag=1 and p.grid_id <> $alias.grid_id)) and dead = 0")
                ->join("sa_building b", "p.building_id=b.id","LEFT")
                ->field("'{$this->checkRule[$key]['title']}' check_name, ".$alias.".*,g.grid_name,gg.grid_name grid_group_name,c.community_name,
                p.name,p.id_code,p.age,p.sex,p.address,p.face,p.nation,p.education,p.school,p.major,p.hometown,p.phone,b.building_name")
                ->where($alias.'.id in' . $subQuery)->order($order)->select()->toArray();
            return $this->success($this->model->getLastSql(), '/', $list, $count);
        }
        return $this->view('',['checkRule'=>$this->checkRule,'type'=>$type]);
    }

    public function jurisdictionCheck(){
        $id = (int)input("id",0);
        $type = (int)input("type",0);
        $where = [];
        $alias = "a";
        if($id){
            $where[] = [$alias.".id","<>",$id];
        }
        $where['p.id_code']=input("id_code",'');
        $where[$alias.".type"] = $type;
        $where[$alias.'.jurisdiction']=input("jurisdiction",'');
        $data = $this->model->alias($alias)
            ->join("sa_person p",$alias.".person_id=p.id and p.delete_time is null and p.dead =0")
            ->join("sa_community c",$alias.".community_id=c.id","LEFT")
            ->join("sa_grid g",$alias.".grid_id=g.id","LEFT")
            ->join("sa_grid gg",$alias.".grid_group_id=gg.id","LEFT")
            ->field("g.grid_name,gg.grid_name grid_group_name,c.community_name")
            ->where($where)->find();
        if($data){
            return $this->error("该管理类型已存在于".$data['community_name'].",".$data['grid_group_name'].",".$data['grid_name']);
        }else{
            return $this->success($this->model->getLastSql());
        }
    }

    public function dataImport()
    {
        if (request()->isPost()) {
            set_time_limit(0);
            $filePath = input('file_1');
            $msg = "";
            $spreadsheet = IOFactory::load('public'.$filePath);
            //获取活动工作表
            $worksheet = $spreadsheet->getActiveSheet();

            $title = $worksheet->getCell("A1")->getValue();
            $type = 1;
            if($title == "孤寡老人"){
                $type=2;
            }else if($title == "独居老人"){
                $type=3;
            }
            $Dictionary = [];
            foreach ($this->columnFormat as $key => $item) {
                if($item[0]==3){
                    $dataList = getDictionary($item[2]);
                    $Dictionary[$item[2]] = $dataList;
                }
            }
            $admin_id = get_admin_id();
            $columns_arr=[];
            $update_field = [];
            $pk_column = "";
            foreach ($this->columnFormat as $key => $vo){
                if ($vo[0] < 1 || $vo[0] > 4) {
                    continue;
                }
                $columns_arr[$vo[1]]=$vo[1];
                if(array_key_exists('column',$vo)){
                    $vo[1] = $vo['column'];
                }
                $update_field[] = $vo[1];
                if($vo[0]==4&&$vo[1]=='id_code'){
                        $pk_column = $key;
                }
            }
            $create_time=date('Y-m-d H:i:s');
            $create_by = $this->admin_info['name'];
            $columns=implode(",",$columns_arr);
            $columns .= ",sort,admin_id,type";
            $insert_sql = "INSERT INTO ".$this->table_name."(".$columns.") VALUES ";
            $values = [];

            Db::query("delete from ".$this->table_name." where admin_id=".$admin_id);
            $error_arr = [];
            foreach ($worksheet->getRowIterator() as $i=>$row) {
                if ($i < $this->row_index||empty($worksheet->getCell($pk_column.$i)->getValue())) {
                    continue;
                }
                $cellIterator = $row->getCellIterator();
                $cellIterator->setIterateOnlyExistingCells(false); // 遍历所有单元格，包括空单元格
                $values_arr = [];
                foreach ($cellIterator as $key => $cell) {
                    $value = $cell->getValue();
                    if(empty($this->columnFormat[$key])){
                        return $this->error("存在未定义的excel列，请检查导入表格是否和模板表格格式相同");
                    }
                    if(array_key_exists('required',$this->columnFormat[$key])&&empty($value)){
                        $error_arr[$i].="第{$key}列必填项为空;";
                    }
                    $vo = $this->columnFormat[$key];
                    if (($vo[0] < 1&&$vo != -3) || $vo[0] > 4) {
                        continue;
                    } else {
                        switch ($vo[0]) {
                            case 2:
                                $value = $value == '是' ? 1 : 0;
                                break;
                            case 3:
                                $key = array_search($value, $Dictionary[$vo[2]]);
                                if ($key !== false) {
                                    $value = $key;
                                } else {
                                    $value = 0;
                                }
                                break;
                        }
                        if(array_key_exists($vo[1], $values_arr)&&empty($value)){
                            continue;
                        }
                        $values_arr[$vo[1]] = "\"".$value."\"";
                    }
                }
                $values_sql = "(".implode(",",$values_arr).",".($i-$this->row_index+1).",".$admin_id.",".$type.")";
                $values[] = $values_sql;
                $values_arr = [];
                if($i%1000==0){
                    Db::query($insert_sql.implode(",",$values));
                    $values = [];
                }
            }
            if(count($values)){
                Db::query($insert_sql.implode(",",$values));
            }
            Db::query("UPDATE ".$this->table_name." i INNER JOIN sa_community c on i.community_name=c.community_name set i.community_id=c.id");
            Db::query("UPDATE ".$this->table_name." i INNER JOIN sa_grid g on i.community_id=g.community_id and i.grid_group_name=g.grid_name set i.grid_group_id=g.id");
            Db::query("UPDATE ".$this->table_name." i INNER JOIN sa_grid g on i.community_id=g.community_id and i.grid_name=g.grid_name set i.grid_id=g.id");
            Db::query("UPDATE ".$this->table_name." i INNER JOIN ".$this->table_source." p on i.id_code=p.id_code and i.type=p.type and i.community_id=p.community_id and i.grid_group_id=p.grid_group_id and i.grid_id=p.grid_id set i.".$this->pk."=p.id");
            $list = Db::table($this->table_name)->where("admin_id",$admin_id)->where("community_id",0)->field("sort")->select()->toArray();
            foreach ($list as $item){
                $error_arr[$item['sort']+$this->row_index-1].="找不到对应的社区信息;";
            }
            $list = Db::table($this->table_name)->where("admin_id",$admin_id)->where("grid_group_id",0)->field("sort")->select()->toArray();
            foreach ($list as $item){
                $error_arr[$item['sort']+$this->row_index-1].="找不到对应的网格组信息;";
            }
            $list = Db::table($this->table_name)->where("admin_id",$admin_id)->where("grid_id",0)->field("sort")->select()->toArray();
            foreach ($list as $item){
                $error_arr[$item['sort']+$this->row_index-1].="找不到对应的网格信息;";
            }
            $list = Db::table($this->table_name)->alias("i")->join("sa_person p","i.id_code=p.id_code","LEFT")->where("i.admin_id",$admin_id)->where("p.id is null")->field("i.sort")->select()->toArray();
            foreach ($list as $item){
                $error_arr[$item['sort']+$this->row_index-1].="找不到对应的人口信息;";
            }
            $list = Db::table($this->table_name)->alias("i")->join("sa_person p","i.id_code=p.id_code","LEFT")->where("i.admin_id",$admin_id)->where("p.age < 60")->field("i.sort")->select()->toArray();
                foreach ($list as $item){
                    $error_arr[$item['sort']+$this->row_index-1].="年龄未满60岁;";
                }
            if(count($error_arr)>0){
                return $this->error("信息校验未通过",'',['data'=>$error_arr]);
            }
            try{
                $sql = $this->buildUpdSql($admin_id);
                Db::query($sql);
                $field=implode(",",$update_field);
                $field.=",sort";
                $insert_sql = "INSERT INTO ".$this->table_source." ({$field},create_time,create_by,type) select {$field},'$create_time' create_time,'$create_by' create_by,$type type from ".$this->table_name." where admin_id = {$admin_id} and ".$this->pk." = 0";
                Db::query($insert_sql);
                $sql = "update {$this->table_source} i
                inner join sa_person p on i.id_code = p.id_code $where set i.person_id=p.id where p.delete_time is null and p.dead = 0 and IFNULL(i.person_id,0) = 0;";
                Db::query($sql);
                $Person = new Person();
                $Person->rbacReverseImp($this->table_source,"type=$type");
            }catch (\Exception $e){
                return $this->error("导入失败，请检查模板");
                return $this->error($sql);
            }
            
            return $this->success( '导入成功');
        }
        return $this->view('/public/data_import');
    }

    public function dataToExcel()
    {
        $alias = "h";
        $where = $this->buildSelectParams($alias.".");
        $keyword = input('keyword','');
        if($keyword){
            $where[] = ["p.name|"."p.id_code", 'like', '%' . $keyword . '%'];
        }
        $fieldList = $this->model->getFields();
        $order = !array_key_exists('sort', $fieldList) ? $alias.'.id' : $alias.'.sort';
        $order = "$alias.community_id,$alias.grid_group_id,$alias.grid_id,".$order;
        $list = $this->model->alias($alias)
            ->join("sa_person p", $alias.".person_id=p.id and p.delete_time is null and dead = 0")
            ->join("sa_community c",$alias.".community_id=c.id","LEFT")
            ->join("sa_grid g",$alias.".grid_id=g.id","LEFT")
            ->join("sa_grid gg",$alias.".grid_group_id=gg.id","LEFT")
            ->field("$alias.*,
            g.grid_name,gg.grid_name grid_group_name,c.community_name,
            p.name,p.id_code,p.birth,p.sex,p.age,p.nation,p.education,p.school,p.major,p.hometown,p.address,p.phone,p.wechat_code,p.health")
            ->where($where)->order($order)->select()->toArray();
        $filePath = $this->excelExport($list);
        if(request()->isPost()){
            return $this->success('',$filePath);
        }
        return $this->redirect($filePath);
    }

}
