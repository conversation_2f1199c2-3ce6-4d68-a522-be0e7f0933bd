<?php
declare(strict_types=1);

namespace app\admin\controller;

use app\AdminController;
use support\Response;
use Webman\Http\Request;
use app\common\model\WxSsp as WxSspModel;
use think\facade\Db;
use think\helper\Str;

/**
 * wx_ssp
 * 微信随手拍
 * <!---->
 * Class WxSjsb
 * @package app\admin\controller
 */
class WxSjsb extends AdminController
{
    /**
     * WxSsp模型对象
     * @var \app\common\model\WxSsp
     */
    public function __construct()
    {
        parent::__construct();
        $this->model = new WxSspModel;
        // $this->columnFormat=[
        //     'A' => [0, '微信随手拍'],
        //     'B' => [1, 'community_name','column'=>'community_id'],
        //     'C' => [1, 'grid_group_name','column'=>'grid_group_id'],
        //     'D' => [1, 'grid_name','column'=>'grid_id'],
        // ];
        // $this->tpl_name = "微信随手拍模版";
        // $this->table_name = 'sa_wx_ssp';
        // $this->table_source = "sa_wx_ssp";
        // $this->pk = "house_id";
        // $this->row_index = 2;
        // $this->filterWhere = ['road_start','road_end','street_start','street_end'];
    }

    /**
     * 默认生成的方法为index/add/edit/del/status 五个方法
     * 当创建CURD的时候，DIY的函数体和模板为空，请自行编写代码
     */

    /**
     * index 函数
     */
    public function index()
    {
        // return json_encode($this->model);
        if (request()->isAjax()) {
            $page = (int)input('page', 1);
            $limit = (int)input('limit', 18);
            $where = array();
            $where = $this -> buildSelectParamsOnly();
            $keyword_status = input('status','');
            if ($keyword_status) {
                if ($keyword_status == '未处理') {
                    $where['status'] = 0;
                }
                if ($keyword_status == '已处理') {
                    $where['status'] = 1;
                }
                if ($keyword_status == '已挂起') {
                    $where['status'] = -1;
                }
                if ($keyword_status == '已驳回') {
                    $where['status'] = -2;
                }
            }
            $where[] = ['admin_id', '>', '0'];
            $keyword_event = input('event','');
            if ($keyword_event) {
                $where['event'] = $keyword_event;
            }
            $keyword_rate = input('rate','');
            if ($keyword_rate) {
                $where['rate'] = $keyword_rate;
            }
            $keyword_begin = input('begin','');
            $keyword_over = input('over','');
            if ($keyword_begin) {
                $where[] = ['create_time', '>', $keyword_begin];
            }
            if ($keyword_over) {
                $where[] = ['create_time', '<', $keyword_over];
            }
            // $where = $this->buildSelectParams("b.");
            $count = $this->model->alias("b")->where($where)->count();
            //$page = $count <= $limit ? 1 : $page;
            $fieldList = $this->model->getFields();
            $order = !array_key_exists('sort', $fieldList) ? 'id desc' : 'sort';
            $order = "b.community_id,b.grid_group_id,b.grid_id,".$order;
            $subQuery = $this->model->alias("b")->field('b.id')->where($where)->order($order)->buildSql();
            $subQuery = '( SELECT object.id FROM ' . $subQuery . ' AS object )';
            $list = $this->model->alias("b")
                ->join("sa_community c","b.community_id=c.id","LEFT")
                ->join("sa_grid g","b.grid_id=g.id","LEFT")
                ->join("sa_grid gg","b.grid_group_id=gg.id","LEFT")
                ->field("b.*,g.grid_name,gg.grid_name grid_group_name,c.community_name")
                ->where('b.id in' . $subQuery)->order($order)
                ->limit($limit)->page($page)->select()->toArray();
            for ($i=0; $i < count($list); $i++) {
                if ($list[$i]['status'] == 0) {
                    $list[$i]['status_res'] = '未处理';
                }
                if ($list[$i]['status'] == 1) {
                    $list[$i]['status_res'] = '已处理';
                }
                if ($list[$i]['status'] == -1) {
                    $list[$i]['status_res'] = '已挂起';
                }
                if ($list[$i]['status'] == -2) {
                    $list[$i]['status_res'] = '已驳回';
                }
            }
            return $this->success($this->model->getLastSql(), '/', $list, $count);
        }
        return $this->view();
    }

    /**
     * 添加资源
     * @return Response|void
     */
    public function add()
    {
        if (request()->isPost()) {
            $post = $this->preRuleOutFields(\request()->post());
            if ($this->dataLimit) {
                $post[$this->dataLimitField] = get_admin_id();
            }
            $validate = $this->isValidate ? get_class($this->model) : $this->isValidate;
            $post = request_validate_rules($post, $validate, $this->scene);
            if (empty($post) || !is_array($post)) {
                return $this->error($post);
            }
            $post = dataArrayToJson($post);
            // if(!empty($this->model->create_by)){
            $post['create_by']=$this->admin_info['name'];
            // }
            // return $post[$this->model->create_by];
            $this->status = $this->model->create($post);
            return $this->status ? $this->success() : $this->error();
        }
        $data = $this->getTableFields();
        $Communities = Db::name("community")->select()->toArray();
        $grid_groups = Db::name("grid")->where(["community_id"=>$data['community_id'],'grid_group'=>0])->select()->toArray();
        $grids = Db::name("grid")->where(["grid_group"=>$data['grid_group_id']])->select()->toArray();
        return $this->view('', ['data' => $data,
            'Communities'=>$Communities,
            'grid_groups'=>$grid_groups,
            'grids'=>$grids
        ]);
    }

    public function htmltype($name){
        
        // return $type;
        $where['name'] = $name;
        $where['pid'] = 610;

        $id = DB::name("dictionary")->where($where)->find()['id'];
        //事件下发分类
        $type = DB::name("wx_ssptype")->where('pid',$id)->select();
        // for ($i=0; $i < count($type); $i++) { 
        return $type;
        // }
    }

    /**
     * 编辑资源
     * @return Response|void
     */
    public function edit()
    {
        $id = input('id');
        $data = $this->model->where('id', $id)->findOrEmpty()->toArray();
        // 限制数据调用
        if (!$this->authService->SuperAdmin() && $this->dataLimit
            && in_array($this->dataLimitField, $this->model->getFields())) {
            if ($data[$this->dataLimitField] != get_admin_id()) {
                return $this->error('没有权限');
            }
        }
        if (request()->isPost()) {
            $post = $this->preRuleOutFields(\request()->post());
            $validate = $this->isValidate ? get_class($this->model) : $this->isValidate;
            $post = request_validate_rules($post, $validate, $this->scene);
            if (empty($post) || !is_array($post)) {
                return $this->error($post);
            }
            $post = dataArrayToJson($post);
            // if(!empty($this->model->update_by)){
            $post['update_by']=$this->admin_info['name'];
            if ($post['feedback'] == null) {
                $post['feedback'] = '';
            }
            $this->status = $this->model->update($post);
            return $this->status ? $this->success() : $this->error();
        }
        /**
         * 默认共享模板
         */
        $template = str_replace('/_', '/', Str::snake(request()->getController()));
        $Communities = Db::name("community")->select()->toArray();
        $grid_groups = Db::name("grid")->where(["community_id"=>$data['community_id'],'grid_group'=>0])->select()->toArray();
        $grids = Db::name("grid")->where(["grid_group"=>$data['grid_group_id']])->select()->toArray();
        $data['department'] = $this->htmltype($data['event']);
        return $this->view($template . '/add', [
            'data' => $data,
            'Communities'=>$Communities,
            'grid_groups'=>$grid_groups,
            'grids'=>$grids
        ]);
    }
}
