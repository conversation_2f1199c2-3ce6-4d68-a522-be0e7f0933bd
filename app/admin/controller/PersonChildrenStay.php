<?php
declare (strict_types = 1);
namespace app\admin\controller;

use app\AdminController;
use support\Response;
use think\helper\Str;
use Webman\Http\Request;
use app\common\model\PersonChildrenStay as PersonChildrenStayModel;
use app\admin\model\Person;
use think\facade\Db;

/**
 * person_vehicle
 * 车辆管理
 * <!---->
 * Class PersonVehicle
 * @package app\admin\controller
 */
class PersonChildrenStay extends AdminController
{
    /**
     * PersonVehicle模型对象
     * @var \app\common\model\PersonChildrenStay
     */

    public function __construct()
    {
        parent::__construct();
        $this->model = new PersonChildrenStayModel;
        $this->columnFormat=[
            'A' => [0, '建三江'],
            'B' => [0, '建三江局直'],
            'C' => [0, 'index'],
            'D' => [1, 'community_name','column'=>'community_id'],
            'E' => [1, 'grid_group_name','column'=>'grid_group_id'],
            'F' => [1, 'grid_name','column'=>'grid_id'],
            'G' => [1,'name'],
            'H' => [4, 'id_code'],
            'I' => [1,'address'],
            'J' => [3, 'education', 56],
            'K' => [3, 'health', 61],
            'L' => [3, 'spiritual_outlook', 457],
            'M' => [3,'risk_level', 461],
            'N' => [2, 'guardian'],
            'O' => [2,'guardian_live'],
            'P' => [1, 'guardian_name'],
            'Q' => [1, 'guardian_relation'],
            'R' => [4, 'guardian_phone'],
            'S' => [1,'father_name'],
            'T' => [4, 'father_id_code'],
            'U' => [4,'father_phone'],
            'V' => [2, 'father_out_work'],
            'W' => [1,'father_out_work_place'],
            'X' => [1, 'mother_name'],
            'Y' => [4, 'mother_id_code'],
            'Z' => [4, 'mother_phone'],
            'AA' => [2,'mother_out_work'],
            'AB' => [1, 'mother_out_work_place'],
            'AC' => [3, 'jurisdiction',501,'required'=>1],
            'AD' => [1,'grid_user'],
            'AE' => [1,'grid_phone'],
        ];
        $this->tpl_name = "留守儿童模板";
        $this->table_name = 'sa_person_children_stay_imp';
        $this->table_source = "sa_person_children_stay";
        $this->pk = "person_id";
        $this->row_index = 4;
    }

    /**
     * 添加资源
     * @return Response|void
     */
    public function add()
    {
        if (request()->isPost()) {

            $post = $this->preRuleOutFields(\request()->post());
            if ($this->dataLimit) {
                $post[$this->dataLimitField] = get_admin_id();
            }

            $validate = $this->isValidate ? get_class($this->model) : $this->isValidate;
            $post = request_validate_rules($post, $validate, $this->scene);
            if (empty($post) || !is_array($post)) {
                return $this->error($post);
            }
            if(empty($post['person_id'])){
                return $this->error('请调取人口数据！');
            }
            $age = Db::name("person")->where("id",$post['person_id'])->value("age");
            if($age>17){
                return $this->error('年龄已满18岁');
            }
            $post = dataArrayToJson($post);
            if(!empty($this->model->create_by)){
                $post[$this->model->create_by]=$this->admin_info['name'];
            }
            if(!empty($this->model->createTime)){
                $post[$this->model->createTime]=date('Y-m-d H:i:s');
            }
            $this->status = $this->model->create($post);
            $Person = new Person();
            $Person->rbacReverse($post[$this->pk],$this->table_source);
            return $this->status ? $this->success() : $this->error();
        }
        $data = $this->getTableFields();
        $Communities = Db::name("community")->select()->toArray();
        $grid_groups = Db::name("grid")->where(["community_id"=>$data['community_id'],'grid_group'=>0])->select()->toArray();
        $grids = Db::name("grid")->where(["grid_group"=>$data['grid_group_id']])->select()->toArray();
        return $this->view('', ['data' => $data,
            'Communities'=>$Communities,
            'grid_groups'=>$grid_groups,
            'grids'=>$grids
        ]);
    }

    /**
     * 编辑资源
     * @return Response|void
     */
    public function edit()
    {
        $id = input('id');
        $data = $this->model->where('id', $id)->findOrEmpty()->toArray();

        // 限制数据调用
        if (!$this->authService->SuperAdmin() && $this->dataLimit
            && in_array($this->dataLimitField, $this->model->getFields())) {
            if ($data[$this->dataLimitField] != get_admin_id()) {
                return $this->error('没有权限');
            }
        }

        if (request()->isPost()) {
            $post = $this->preRuleOutFields(\request()->post());
            $validate = $this->isValidate ? get_class($this->model) : $this->isValidate;
            $post = request_validate_rules($post, $validate, $this->scene);
            if (empty($post) || !is_array($post)) {
                return $this->error($post);
            }
            if(empty($post['person_id'])){
                return $this->error('请调取人口数据！');
            }
            $age = Db::name("person")->where("id",$post['person_id'])->value("age");
            if($age>17){
                return $this->error('年龄已满18岁');
            }
            $post = dataArrayToJson($post);
            if(!empty($this->model->update_by)){
                $post[$this->model->update_by]=$this->admin_info['name'];
            }
            $this->status = $this->model->update($post);
            $Person = new Person();
            $Person->rbacReverse($post[$this->pk],$this->table_source);
            return $this->status ? $this->success() : $this->error();
        }

        /**
         * 默认共享模板
         */
        $template = str_replace('/_', '/', Str::snake(request()->getController()));
        $Communities = Db::name("community")->select()->toArray();
        $grid_groups = Db::name("grid")->where(["community_id"=>$data['community_id'],'grid_group'=>0])->select()->toArray();
        $grids = Db::name("grid")->where(["grid_group"=>$data['grid_group_id']])->select()->toArray();
        return $this->view($template . '/add', [
            'data' => $data,
            'Communities'=>$Communities,
            'grid_groups'=>$grid_groups,
            'grids'=>$grids
        ]);
    }

    /**
     * index 函数
     */
    public function index()
    {
        if (request()->isAjax()||request()->isPost()) {
            $page = (int)input('page', 1);
            $limit = (int)input('limit', 18);
            $alias = "e";
            $where = $this->buildSelectParams($alias.".");


            $keyword = input('keyword','');
            if($keyword){
                $where[] = ["p.name|"."p.id_code", 'like', '%' . $keyword . '%'];
            }
            $count = $this->model->alias($alias)
                ->join("sa_person p",$alias.".person_id=p.id and (p.delete_time is null or (p.delete_flag=1 and p.grid_id <> $alias.grid_id)) and dead = 0")
                ->where($where)->count();
            //$page = $count <= $limit ? 1 : $page;
            $fieldList = $this->model->getFields();
            $order = !array_key_exists('sort', $fieldList) ? $alias.'.id' : $alias.'.sort';
            $order = "$alias.community_id,$alias.grid_group_id,$alias.grid_id,".$order;
            $subQuery = $this->model->alias($alias)
                ->join("sa_person p",$alias.".person_id=p.id")
                ->field($alias.'.id')->where($where)->order($order)->limit($limit)->page($page)->buildSql();
            $subQuery = '( SELECT object.id FROM ' . $subQuery . ' AS object )';
            $list = $this->model->alias($alias)
                ->join("sa_community c",$alias.".community_id=c.id","LEFT")
                ->join("sa_grid g",$alias.".grid_id=g.id","LEFT")
                ->join("sa_grid gg",$alias.".grid_group_id=gg.id","LEFT")
                ->join("sa_person p",$alias.".person_id=p.id and (p.delete_time is null or (p.delete_flag=1 and p.grid_id <> $alias.grid_id)) and dead = 0")
                ->join("sa_building b", "p.building_id=b.id","LEFT")
                ->field($alias.".*,g.grid_name,gg.grid_name grid_group_name,c.community_name,
                p.name,p.id_code,p.age,p.sex,p.address,p.face,p.nation,p.education,p.school,p.major,p.hometown,p.phone,b.building_name")
                ->where($alias.'.id in' . $subQuery)->order($order)->select()->toArray();
            return $this->success($this->model->getLastSql(), '/', $list, $count);
        }else{
            $this->noRepeat();
        }
        return $this->view();
    }

    public function dataToExcel()
    {
        $alias = "h";
        $where = $this->buildSelectParams($alias.".");
        $keyword = input('keyword','');
        if($keyword){
            $where[] = ["p.name|"."p.id_code", 'like', '%' . $keyword . '%'];
        }
        $fieldList = $this->model->getFields();
        $order = !array_key_exists('sort', $fieldList) ? $alias.'.id' : $alias.'.sort';
        $order = "$alias.community_id,$alias.grid_group_id,$alias.grid_id,".$order;
        $list = $this->model->alias($alias)
            ->join("sa_person p", $alias.".person_id=p.id and p.delete_time is null and dead = 0")
            ->join("sa_community c",$alias.".community_id=c.id","LEFT")
            ->join("sa_grid g",$alias.".grid_id=g.id","LEFT")
            ->join("sa_grid gg",$alias.".grid_group_id=gg.id","LEFT")
            ->field("$alias.*,
            g.grid_name,gg.grid_name grid_group_name,c.community_name,
            p.name,p.id_code,p.birth,p.sex,p.age,p.nation,p.education,p.school,p.major,p.hometown,p.address,p.phone,p.wechat_code,p.health")
            ->where($where)->order($order)->select()->toArray();
        $filePath = $this->excelExport($list);
        if(request()->isPost()){
            return $this->success('',$filePath);
        }
        return $this->redirect($filePath);
    }

}
