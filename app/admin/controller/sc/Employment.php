<?php
declare (strict_types=1);
// +----------------------------------------------------------------------
// | swiftAdmin 极速开发框架 [基于WebMan开发]
// +----------------------------------------------------------------------
// | Copyright (c) 2020-2030 http://www.swiftadmin.net
// +----------------------------------------------------------------------
// | swiftAdmin.net High Speed Development Framework
// +----------------------------------------------------------------------
// | Author: meystack <<EMAIL>> Apache 2.0 License
// +----------------------------------------------------------------------
namespace app\admin\controller\sc;

use app\AdminController;
use think\facade\Db;

class Employment extends AdminController
{
    public function index()
    {
        if(empty($this->admin_info['community_id'])){
            $total = $this->getTotal(0,"community_id");
            $param['type']="index";
            $param['id']=0;
            return $this->view('',['title'=>'建三江充分就业','param'=>$param,'total'=>$total]);
        }else if(empty($this->admin_info['grid_group_id'])){
            return $this->redirect(url('sc/Employment/community'));
        }else if(empty($this->admin_info['grid_id'])){
            return $this->redirect(url('sc/Employment/gridGroup'));
        }else{
            return $this->redirect(url('sc/Employment/grid'));
        }
    }

    public function changeScene(){
        $scene_id = input("scene_id",0);
        $param['menu']=1;
        $param['id'] =  Db::name("community")->where("scene_id",$scene_id)->value("id");
        if($param['id']){
            return $this->redirect(url('sc/Employment/community',$param));
        }else{
            $grid =  Db::name("grid")->where("scene_id",$scene_id)->find();
            $param['id'] = $grid['id'];
            if($grid['grid_group_id']==0){
                return $this->redirect(url('sc/Employment/gridGroup',$param));
            }else{
                return $this->redirect(url('sc/Employment/grid',$param));
            }
        }
    }

    public function community(){
        $community_id = input("id",$this->admin_info['community_id']);
        $title = Db::name("community")->where("id",$community_id)->value("community_name");

        $param['type']="community";
        $param['id']=$community_id;
        $total = $this->getTotal($community_id,"community_id");
        return $this->view('',['title'=>$title."概况",'param'=>$param,'total'=>$total]);
    }

    public function getTotal($id,$column){
        $total['person']=$this->getRkflTotal($id,$column);
        return $total;
    }

    protected function getRkflTotal($id,$column){
        $where["p.".$column] = $id;
        if($column=='community_id'&&$id==0){
            $where=[];
        }
        $where[] = ["p.person_type" , "<", "4"];
        $where["p.household"] = 0;
        $where['p.delete_time'] = null;
        $person['ldnl'] = Db::name("person")->alias("p")
            ->join("sa_building b", "p.building_id=b.id")
            ->join("sa_house h","p.house_id=h.id")
            ->where($where)->where("p.labor_capacity = 1")->count();
        $emp_count = Db::name("person")->alias("p")->join("sa_building b", "p.building_id=b.id")
            ->join("sa_house h","p.house_id=h.id")
            ->join("sa_person_employment e","e.person_id=p.id and e.type>0")
            ->field("count(e.id) num, e.type")
            ->where($where)->group("e.type")->select()->toArray();
        foreach ($emp_count as $item){
            $person['emp_type_'.$item['type']] = $item['num'];
        }
        $unemployment_reemployment = Db::name("person")->alias("p")->join("sa_building b", "p.building_id=b.id")
            ->join("sa_house h","p.house_id=h.id")
            ->join("sa_person_employment e","e.person_id=p.id and e.type > 0  and e.unemployment_reemployment=1")
            ->field("count(e.id) num, e.type")
            ->where($where)->group("e.type")->select()->toArray();
        $total = 0;
        foreach ($unemployment_reemployment as $item){
            $person['syry_'.$item['type']] = $item['num'];
            $total+=$item['num'];
        }
        if($total==0){
            $person['syry_per']=0;
        }else{
            $person['syry_per'] = round($person['syry_1']/$total*100,2);
        }
        $employment_difficult_reemployment =  Db::name("person")->alias("p")->join("sa_building b", "p.building_id=b.id")
            ->join("sa_house h","p.house_id=h.id")
            ->join("sa_person_employment e","e.person_id=p.id and e.type > 0 and e.employment_difficult_reemployment=1")
            ->field("count(e.id) num, e.type")
            ->where($where)->group("e.type")->select()->toArray();
        $total = 0;
        foreach ($employment_difficult_reemployment as $item){
            $person['jykn_'.$item['type']] = $item['num'];
            $total+=$item['num'];
        }
        if($total==0){
            $person['jykn_per']=0;
        }else{
            $person['jykn_per'] = round($person['jykn_1']/$total*100,2);
        }

        $employment_form = Db::name("person")->alias("p")->join("sa_building b", "p.building_id=b.id")
            ->join("sa_house h","p.house_id=h.id")
            ->join("sa_person_employment e","e.person_id=p.id and e.type = 1")
            ->field("count(e.id) num, e.employment_form")
            ->where($where)->group("e.employment_form")->select()->toArray();
        foreach ($employment_form as $item){
            $person['form_type_'.$item['employment_form']] = $item['num'];
        }
        $person['syrk'] = Db::name("person")->alias("p")
            ->join("sa_building b", "p.building_id=b.id")
            ->join("sa_house h","p.house_id=h.id")
            ->where($where)->count();
        return $person;
    }
}