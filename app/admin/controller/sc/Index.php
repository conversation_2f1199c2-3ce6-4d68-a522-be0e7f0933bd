<?php
declare (strict_types = 1);
// +----------------------------------------------------------------------
// | swiftAdmin 极速开发框架 [基于WebMan开发]
// +----------------------------------------------------------------------
// | Copyright (c) 2020-2030 http://www.swiftadmin.net
// +----------------------------------------------------------------------
// | swiftAdmin.net High Speed Development Framework
// +----------------------------------------------------------------------
// | Author: meystack <<EMAIL>> Apache 2.0 License
// +----------------------------------------------------------------------
namespace app\admin\controller\sc;

use app\AdminController;
use app\common\model\sc\ScDynamics;
use app\common\model\sc\ScGovernment;
use app\common\model\sc\ScSite;
use Psr\SimpleCache\InvalidArgumentException;
use think\db\Where;
use think\facade\Db;

class Index extends AdminController
{
    protected string $url = "";

    /**
     * 前端首页
     * @throws InvalidArgumentException
     */
    public function index()
    {
        if (empty($this->admin_info['community_id'])) {
            $list = Db::name("community")->where("id", ">", 0)->select()->toArray();
            foreach ($list as &$item) {
                $item['person'] = Db::name("organization_person")->alias("p")->where("p.community_id", $item['id'])
                    ->join("sa_organization o", "p.organization_id=o.id")
                    ->where("(o.org_name like '%书记%' or o.org_name like '主任%') and p.grid_group_id is null")->order("p.id")->find();
            }
            $data['level'] = 0;
            $menu          = input("menu", 0);
            if ($menu) {
                return $this->view("sc/index/right_menu", ['list' => $list, 'data' => $data]);
            }
            $param['type'] = "index";
            $param['id']   = 0;
            $total         = $this->getScTotal(0, "community_id");
            return $this->view('', ['list' => $list, 'title' => '建三江街道办', 'param' => $param, 'total' => $total, 'data' => $data]);
        } else if (empty($this->admin_info['grid_group_id'])) {
            return $this->redirect(url('sc/community'));
        } else if (empty($this->admin_info['grid_id'])) {
            return $this->redirect(url('sc/community'));
        } else {
            return $this->redirect(url('sc/community'));
        }
    }

    public function changeScene()
    {
        $scene_id      = input("scene_id", 0);
        $param['menu'] = 1;
        $param['id']   = Db::name("community")->where("scene_id", $scene_id)->value("id");
        if ($param['id']) {
            return $this->redirect(url('sc/community', $param));
        } else {
            $grid        = Db::name("grid")->where("scene_id", $scene_id)->find();
            $param['id'] = $grid['id'];
            if ($grid['grid_group_id'] == 0) {
                return $this->redirect(url('sc/gridGroup', $param));
            } else {
                return $this->redirect(url('sc/grid', $param));
            }
        }
    }

    public function community()
    {
        $community_id = input("id", $this->admin_info['community_id']);
        $title        = Db::name("community")->where("id", $community_id)->value("community_name");
        $where        = ["community_id" => $community_id, "grid_group_id" => 0];
        if ($this->admin_info['grid_group_id']) {
            $where['id'] = $this->admin_info['grid_group_id'];
        }
        $list = Db::name("grid")->where($where)->select()->toArray();
        foreach ($list as &$item) {
            $item['person'] = Db::name("organization_person")->where("grid_group_id", $item['id'])->where("grid_id is null")->order("organization_id")->find();
        }
        $data['level']  = 1;
        $data['action'] = "index";
        $data['id']     = 0;
        $menu           = input("menu", 0);
        $param['type']  = "community";
        $param['id']    = $community_id;
        $total          = $this->getScTotal($community_id, "community_id");
        if ($menu) {
            return $this->view("sc/index/right_menu", ['list' => $list, 'data' => $data, 'title' => $title, 'total' => $total, 'admin_info' => $this->admin_info]);
        }
        if (empty($this->admin_info['community_id'])) {
            $this->url = url('sc/index');
        }
        return $this->view('', ['list' => $list, 'url' => $this->url, 'title' => $title . "概况", 'param' => $param, 'total' => $total, 'data' => $data]);
    }

    public function gridGroup()
    {
        $grid_group_id = input("id", $this->admin_info['grid_group_id']);
        if ($this->admin_info['grid_group_id'] && $grid_group_id != $this->admin_info['grid_group_id']) {
            $grid_group_id = -1;
        }
        $grid_group = Db::name("grid")->where("id", $grid_group_id)->find();
        $title      = $grid_group['grid_name'];

        $where = ["grid_group_id" => $grid_group_id];

        $list = Db::name("grid")->where($where)->select()->toArray();
        foreach ($list as &$item) {
            $item['person'] = Db::name("organization_person")->where("grid_id", $item['id'])->find();
        }
        $data['level']  = 2;
        $data['action'] = "community";
        $data['id']     = $grid_group['community_id'];
        $menu           = input("menu", 0);
        $param['type']  = "gridGroup";
        $param['id']    = $grid_group_id;
        $total          = $this->getScTotal($grid_group_id, "grid_group_id");
        if ($menu) {
            return $this->view("sc/index/right_menu", ['list' => $list, 'data' => $data, 'title' => $title, 'total' => $total, 'admin_info' => $this->admin_info]);
        }
        if (empty($this->admin_info['grid_group_id'])) {
            $this->url = url('sc/community', ['id' => $grid_group['community_id']]);
        }
        return $this->view('', ['list' => $list, 'url' => $this->url, 'title' => $title . "概况", 'param' => $param, 'total' => $total, 'data' => $data]);
    }

    public function grid()
    {
        $grid_id = input("id", $this->admin_info['grid_id']);
        if ($this->admin_info['grid_id'] && $grid_id != $this->admin_info['grid_id']) {
            $grid_id = -1;
        }
        $where['id'] = $grid_id;
        if ($this->admin_info['grid_group_id']) {
            $where['grid_group_id'] = $this->admin_info['grid_group_id'];
        }
        $grid = Db::name("grid")->where($where)->find();
        if (empty($grid)) {
            $grid_id = -1;
        }
        $title = $grid['grid_name'];
        $list  = Db::name("building")->alias("b")
            ->field("b.*,COUNT(DISTINCT h.id) AS house, COUNT(DISTINCT p.id) AS person")
            ->join("sa_house h", "b.id=h.building_id", "LEFT")
            ->join("sa_person p", "b.id=p.building_id", "LEFT")
            ->where(["b.grid_id" => $grid_id])->order("sort")->group("b.id")->select()->toArray();
        $data['level']  = 3;
        $data['action'] = "gridGroup";
        $data['id']     = $grid['grid_group_id'];
        $menu           = input("menu", 0);
        $param['type']  = "grid";
        $param['id']    = $grid_id;
        $total          = $this->getScTotal($grid_id, "grid_id");
        if ($menu) {
            return $this->view("sc/index/right_menu", ['list' => $list, 'data' => $data, 'title' => $title, 'total' => $total, 'admin_info' => $this->admin_info]);
        }
        if (empty($this->admin_info['grid_id'])) {
            $this->url = url('sc/gridGroup', ['id' => $grid['grid_group_id']]);
        }
        return $this->view('', ['list' => $list, 'url' => $this->url, 'title' => $title . "概况", 'param' => $param, 'total' => $total, 'data' => $data]);
    }

    public function building()
    {
        $building_id = input("id");
        $where['id'] = $building_id;
        if ($this->admin_info['community_id']) {
            $where['community_id'] = $this->admin_info['community_id'];
        }
        if ($this->admin_info['grid_group_id']) {
            $where['grid_group_id'] = $this->admin_info['grid_group_id'];
        }
        if ($this->admin_info['grid_id']) {
            $where['grid_id'] = $this->admin_info['grid_id'];
        }
        $building = Db::name("building")->where($where)->find();
        if (empty($building)) {
            $building_id = 0;
        }
        $title = $building['building_name'];
        $list  = Db::name("building")->alias("b")
            ->field("h.unit,COUNT(DISTINCT h.id) AS house, COUNT(DISTINCT p.id) AS person")
            ->join("sa_house h", "b.id=h.building_id", "LEFT")
            ->join("sa_person p", "h.id=p.house_id", "LEFT")
            ->where("p.dead", 0)
            ->where("p.delete_time is null")
            ->where(["b.id" => $building_id])->order("h.unit")->group("h.unit")->select()->toArray();
        $total['merchant']   = getMerchantTotal($building_id, 'building_id');
        $total['enterprise'] = getEnterpriseTotal($building_id, 'building_id'); //商户
        $total['government'] = getGovernmentTotal($building_id, 'building_id'); //企业
        $data['level']       = 4;
        $data['action']      = "grid";
        $data['id']          = $building['grid_id'];
        $menu                = input("menu", 0);
        if ($menu) {
            return $this->view("sc/index/right_menu", ['list' => $list, 'building' => $building, 'data' => $data, 'total' => $total, 'title' => $title]);
        }
        $this->url     = url('sc/grid', ['id' => $building['grid_id']]);
        $param['type'] = "building";
        $param['id']   = $building_id;
        $total         = $this->getScTotal($building_id, "building_id");
        return $this->view('', ['list' => $list, 'url' => $this->url, 'title' => $title . "概况", 'building' => $building, 'param' => $param, 'total' => $total, 'data' => $data]);
    }

    public function houseList()
    {
        $id    = input("id", 0);
        $unit  = input("unit", 0);
        $image = input("image", '');
        $image = str_replace('thumb_', '', $image);
        $list  = Db::name("house")->where(["building_id" => $id, "unit" => $unit])->order("floor desc,sort")->select()->toArray();
        foreach ($list as &$item) {
            $item['person'] = Db::name("person")->where("house_id", $item['id'])->order("relation")->value("name");
            $item['tags']   = implode(" ", addTags($item, 'house'));
        }
        return $this->view('', ['list' => $list, 'image' => $image, 'unit' => $unit]);
    }

    public function houseList2()
    {
        $id    = input("id", 0);
        $unit  = input("unit", 0);
        $image = input("image", '');
        $image = str_replace('thumb_', '', $image);
        $list  = Db::name("house")->where(["building_id" => $id, "unit" => $unit])->order("floor desc,sort")->select()->toArray();
        foreach ($list as &$item) {
            $item['person'] = Db::name("person")->where("house_id", $item['id'])->order("relation")->value("name");
            $item['tags']   = implode(" ", addTags($item, 'house'));
        }

        $list2 = [];
        for ($i = 0; $i < count($list); $i++) {
            $v        = $list[$i];
            $rooms    = 0;
            $roomList = [];
            foreach ($list as $k1 => $v1) {
                if ($v['floor'] == $v1['floor']) {
                    $rooms++;
                    $roomList[] = $v1;
                }
            }
            if ($rooms == 3) {
                $list2[] = [$roomList[0]];
                $list2[] = [$roomList[1], $roomList[2]];
                $i += 2;
            } else {
                $list2[] = [$v];
            }
        }

        $html = '';
        for ($i = 0; $i < count($list2); $i++) {
            $v = $list2[$i];
            // return json($v);

            $html .= '<li class="toDetail" id="' . $v['id'] . '">';
            $room = '';
            if (count($v) == 2) {
                $room1 = '<div class="housePop" data-url="' . url('sc/person/house', ['id' => $v[0]['id']]) . '"><div style="overflow:hidden;white-space:nowrap;text-overflow: ellipsis;">门牌号：' . $v[0]['code'] . '</div><div>房屋面积：' . $v[0]['area'] . '平</div><div style="overflow:hidden;white-space:nowrap;text-overflow: ellipsis;">标签：' . (htmlspecialchars_decode($v[0]['tags'])) . '</div></div><div><i style="color:#39ffff;" class="iconfont icon-shouye"></i></div>';
                $room2 = '<div class="housePop" data-url="' . url('sc/person/house', ['id' => $v[1]['id']]) . '"><div style="overflow:hidden;white-space:nowrap;text-overflow: ellipsis;">门牌号：' . $v[1]['code'] . '</div><div>房屋面积：' . $v[1]['area'] . '平</div><div style="overflow:hidden;white-space:nowrap;text-overflow: ellipsis;">标签：' . (htmlspecialchars_decode($v[1]['tags'])) . '</div></div><div><i style="color:#39ffff;" class="iconfont icon-shouye"></i></div>';
                $room .= '<div style="width:100%;margin:0;height:100%;display:flex;flex-direction:column;justify-content:space-between;align-items:center;">';
                $room .= '<div style="width:100%;border:1px solid #39ffff;color:#fff;line-height:20px;padding:0 5px;display:flex;justify-content:space-between;align-items:center;">' . $room1 . '</div>';
                $room .= '<div style="width:100%;border:1px solid #39ffff;color:#fff;line-height:20px;padding:0 5px;display:flex;justify-content:space-between;align-items:center;">' . $room2 . '</div>';
                $room .= '</div>';
            } else {
                $room .= '<dt><i class="iconfont icon-shouye"></i>' . $v[0]['owner_name'] . '</dt>';
                $room .= '<dd>门牌号：' . $v[0]['code'] . '</dd>';
                $room .= '<dd>房屋面积：' . $v[0]['area'] . '平</dd>';
                $room .= '<dd>标签：' . (htmlspecialchars_decode($v[0]['tags'])) . '</dd>';
            }
            if (($i + 1) % 2 == 0) {
                $html .= '<span class="glyphicon glyphicon-menu-right"></span>';
                $html .= '<dl style="width:100%;' . ((count($v) == 2) ? 'padding:0;border:none;height:140px;' : '') . '" ' . (count($v) != 2 ? ' class="housePop" data-url="' . url('sc/person/house', ['id' => $v[0]['id']]) . '"' : '') . '>' . $room . '</dl>';
                $html .= '<p>' . $v[0]['floor'] . '层</p>';
            } else {
                $html .= '<p>' . $v[0]['floor'] . '层</p>';
                $html .= '<dl style="width:100%;height:140px" class="housePop" data-url="' . url('sc/person/house', ['id' => $v[0]['id']]) . '">' . $room . '</dl>';
                $html .= '<span class="glyphicon glyphicon-menu-left"></span>';
            }
            $html .= '</li>';
        }

        return $this->view('sc/index/house_list2', ['list' => $list, 'image' => $image, 'unit' => $unit, 'html' => $html]);
    }

    public function buildingList()
    {
        $param['type'] = input("type");
        $param['id']   = input('id', 0);
        $floor_type    = input("floor_type");
        $build_types   = getDictionary(13);
        if ($floor_type) {
            if ($floor_type == 1) {
                $sql = "SELECT d.`name`,d.`value` from (SELECT count(*) num,type  from sa_building where floor > 1 and type not in (11,16) GROUP BY type) t INNER JOIN sa_dictionary d on d.pid=13 and d.value=t.type";
            } else if ($floor_type == 2) {
                $sql = "SELECT d.`name`,d.`value` from (SELECT count(*) num,type  from sa_building where floor = 1 and type not in (11,16) GROUP BY type) t INNER JOIN sa_dictionary d on d.pid=13 and d.value=t.type";
            } else if ($floor_type == 3) {
                $sql = "SELECT d.`name`,d.`value` from sa_dictionary d where pid=13 and value = 11";
            } else {
                $sql = "SELECT d.`name`,d.`value` from sa_dictionary d where pid=13 and value = 16";
            }
            $build_data  = Db::query($sql);
            $build_types = [];
            foreach ($build_data as $item) {
                $build_types[$item['value']] = $item['name'];
            }
        }
        $type = $param['type'];
        if ($param['type'] == "gridGroup") {
            $type = "grid";
        }
        if ($type == "index") {
            $title = '建三江街道办';
        } else {
            $data  = Db::name($type)->where("id", $param['id'])->find();
            $title = $data[$type . '_name'];
        }
        $this->url = url("sc/" . $param['type'], ['id' => $param['id']]);
        return $this->view('', ['param' => $param, 'url' => $this->url, 'title' => $title . "概况", "floor_type" => $floor_type, 'build_types' => $build_types]);
    }

    public function person()
    {
        $param['type'] = input("type");
        $param['id']   = input('id', 0);
        $type          = $param['type'];
        if ($param['type'] == "gridGroup") {
            $type = "grid";
        }
        if ($type == "index") {
            $title = '建三江街道办';
        } else {
            $data  = Db::name($type)->where("id", $param['id'])->find();
            $title = $data[$type . '_name'];
        }
        $for       = input("for");
        $this->url = url("sc/" . $param['type'], ['id' => $param['id']]);
        return $this->view('', ['param' => $param, 'url' => $this->url, 'title' => $title . "概况", "for" => $for]);
    }

    public function house()
    {
        $param['type'] = input("type");
        $param['id']   = input('id', 0);
        $for           = input("for");
        $type          = $param['type'];
        if ($param['type'] == "gridGroup") {
            $type = "grid";
        }
        if ($type == "index") {
            $title = '建三江街道办';
        } else {
            $data  = Db::name($type)->where("id", $param['id'])->find();
            $title = $data[$type . '_name'];
        }
        $this->url = url("sc/" . $param['type'], ['id' => $param['id']]);
        return $this->view('', ['param' => $param, 'url' => $this->url, 'title' => $title . "概况", "for" => $for]);
    }

    public function place()
    {
        $param['type'] = input("type");
        $param['id']   = input('id', 0);
        $type          = $param['type'];
        if ($param['type'] == "gridGroup") {
            $type = "grid";
        }
        if ($type == "index") {
            $title = '建三江街道办';
        } else {
            $data  = Db::name($type)->where("id", $param['id'])->find();
            $title = $data[$type . '_name'];
        }
        // return "sc/".$param['type'];
        $this->url = url("sc/" . $param['type'], ['id' => $param['id']]);
        return $this->view('', ['param' => $param, 'url' => $this->url, 'title' => $title . "概况"]);
    }

    public function monitor_all()
    {
        if ($this->admin_info['community_id']) {
            return $this->redirect(url('sc/monitor', ['type' => 'community', 'id' => $this->admin_info['community_id']]));
        }
        $param['type'] = input("type");
        $param['id']   = input('id', 0);
        $get_id        = intval(input("get_id", 0));
        $type          = $param['type'];
        $community_id  = $param['id'];
        if ($param['type'] == "gridGroup") {
            $type = "grid";
        }
        if ($type == "index") {
            $title        = '建三江街道办';
            $community_id = 0;
        } else {
            $data  = Db::name($type)->where("id", $param['id'])->find();
            $title = $data[$type . '_name'];
            if ($type != 'community') {
                $community_id = $data['community_id'];
            }
        }
        $this->url = url("sc/" . $param['type'], ['id' => $param['id']]);
        if ($community_id) {
            $list_one = Db::name('monitor')->where('community_id', $community_id)->limit(1)->select()->toArray();
        } else {
            $list_one = Db::name('monitor')->limit(1)->select()->toArray();
        }
        $list             = DB::name("monitor")->group('name')->select();
        $list['list_one'] = $list_one;
        // return json_encode($list);
        return $this->view('', ['param' => $param, 'url' => $this->url, 'title' => "实时可视", 'list' => $list, 'get_id' => $get_id]);
    }

    public function monitor()
    {
        $param['type'] = input("type");
        $param['id']   = input('id', 0);
        $type          = $param['type'];
        $community_id  = $param['id'];
        if ($param['type'] == "gridGroup") {
            $type = "grid";
        }
        if ($type == "index") {
            $title        = '建三江街道办';
            $community_id = 0;
        } else {
            $data  = Db::name($type)->where("id", $param['id'])->find();
            $title = $data[$type . '_name'];
            if ($type != 'community') {
                $community_id = $data['community_id'];
            }
        }
        $this->url = url("sc/" . $param['type'], ['id' => $param['id']]);
        if ($community_id) {
            $list = Db::name('monitor')->where('community_id', $community_id)->limit(4)->select()->toArray();
        } else {
            $list = Db::name('monitor')->limit(4)->select()->toArray();
        }

        return $this->view('', ['param' => $param, 'url' => $this->url, 'title' => "实时可视", 'list' => $list]);
    }

    public function meeting()
    {
        $param['type'] = input("type");
        $param['id']   = input('id', 0);
        $type          = $param['type'];
        if ($param['type'] == "gridGroup") {
            $type = "grid";
        }
        if ($type == "index") {
            $title = '建三江街道办';
        } else {
            $data  = Db::name($type)->where("id", $param['id'])->find();
            $title = $data[$type . '_name'];
        }
        $this->url = url("sc/" . $param['type'], ['id' => $param['id']]);
//        $ing = Db::name("meeting")->where("status",1)->find();
//        if($ing){
//            return $this->redirect(url("meeting/room",['param'=>$param,'url'=>$this->url,'title'=>"视频会议",'room_id'=>$ing['id']]));
//        }
        //查询会议记录：默认查询9条
        $list = Db::name("meeting")->where('deleted', 0)->order("id desc")->limit(9)->select()->toArray();
        // $wait = Db::name("meeting")->where("start_date >= '".date('Y-m-d')."'")->order("id desc")->select()->toArray();

        // 查询待参加的会议信息
        // 我可以加入的会议
        $query1 = Db::name("meeting_conferee")->alias("mc")
            ->join("sa_meeting m", "mc.meeting_id = m.id")
            ->join("sa_admin a", "a.id = mc.admin_id")
            ->join("sa_admin aa", "aa.id = m.admin_id")
            ->join("sa_community c", "c.id = a.community_id", "left")
            ->join("sa_grid g", "g.id = a.grid_group_id", "left")
            ->join("sa_grid gg", "gg.id = a.grid_id", "left")
            ->join("sa_community ac", "ac.id = aa.community_id", "left")
            ->join("sa_grid ag", "ag.id = aa.grid_group_id", "left")
            ->join("sa_grid agg", "agg.id = aa.grid_id", "left")
            ->field(" m.id,m.admin_id,ifnull(agg.grid_name,ifnull(concat(ac.community_name,ag.grid_name),ac.community_name)) as aunit_name,aa.nickname as admin_name,ac.community_name as acommunity_name,ag.grid_name as agrid_group_name,agg.grid_name as agrid_name,m.title,m.start_date,m.start_time,m.end_time,m.details,m.create_time,m.create_by,m.audio_url,m.task_id,m.room_id,mc.admin_id as conferee_id,a.name as conferee_name,ifnull(gg.grid_name,ifnull(concat(c.community_name,g.grid_name),c.community_name)) as unit_name, a.nickname as nickname,'2' as type,c.community_name,g.grid_name as grid_group_name,gg.grid_name")
            ->where("m.deleted", 0)
            ->where("m.status", '<>', '2')
            ->where("mc.admin_id", $this->admin_info['id'])
            ->where("m.admin_id", "<>", $this->admin_info['id'])
            ->where("start_date >= '" . date('Y-m-d') . "'")
            ->buildSql();

        // 我发起的会议
        $query2 = Db::name("meeting")->alias("m")
            ->join("sa_admin a", "a.id = m.admin_id")
            ->join("sa_community c", "c.id = a.community_id", "left")
            ->join("sa_grid g", "g.id = a.grid_group_id", "left")
            ->join("sa_grid gg", "gg.id = a.grid_id", "left")
            ->field("m.id,m.admin_id,ifnull(gg.grid_name,ifnull(concat(c.community_name,g.grid_name),c.community_name)) as aunit_name,a.nickname as admin_name,c.community_name as acommunity_name,g.grid_name as agrid_group_name,gg.grid_name as agrid_name,m.title,m.start_date,m.start_time,m.end_time,m.details,m.create_time,m.create_by,m.audio_url,m.task_id,m.room_id,m.admin_id as conferee_id,a.name as conferee_name,ifnull(gg.grid_name,ifnull(concat(c.community_name,g.grid_name),c.community_name))  as unit_name, a.nickname as nickname,'1' as type,c.community_name,g.grid_name as grid_group_name,gg.grid_name")
            ->where("m.deleted", 0)
            ->where("m.status", '<>', '2')
            ->where("m.admin_id", $this->admin_info['id'])
            ->where("start_date >= '" . date('Y-m-d') . "'")
            ->buildSql();

        $querySql = "select * from (" . $query1 . " union " . $query2 . ") t order by id desc";
        // return json($querySql);

        $wait = Db::query($querySql);
        if (! empty($wait)) {
            foreach ($wait as $key => $item) {
                // 会议发起人
                $aunit_name = $item['aunit_name'];
                if ($aunit_name == '街道本级') {
                    $name                     = str_replace('街道本级', '指挥中心', $aunit_name);
                    $wait[$key]['aunit_name'] = $name;
                } else {
                    $wait[$key]['aunit_name'] = $aunit_name . '-' . $item['admin_name'];
                }

                // 参会人员
                $unit_name = $item['unit_name'];
                if ($unit_name == '街道本级') {
                    $name = str_replace('街道本级', '指挥中心', $unit_name);
                    if ($item['type'] == 1) {
                        $wait[$key]['unit_name'] = $name;
                    } else {
                        $wait[$key]['unit_name'] = $name . '-' . $item['nickname'];
                    }
                } else {
                    $wait[$key]['unit_name'] = $unit_name . '-' . $item['nickname'];
                }
            }
        }
        $wait0 = $wait[0];
        // 删除第一个元素
        array_shift($wait);

        return $this->view('', ['param' => $param, 'url' => $this->url, 'title' => "一键会议", "list" => $list, "wait0" => $wait0, "wait" => $wait]);
    }

    public function map()
    {
        $param['type'] = input("type");
        $param['id']   = input('id', 0);
        $type          = $param['type'];
        if ($param['type'] == "gridGroup") {
            $type = "grid";
        }
        if ($type == "index") {
            $title = '建三江街道办';
        } else {
            $data  = Db::name($type)->where("id", $param['id'])->find();
            $title = $data[$type . '_name'];
        }
        $this->url = url("sc/" . $param['type'], ['id' => $param['id']]);
        $list      = $list      = Db::name("building_map")->select()->toArray();
        return $this->view('', ['param' => $param, 'url' => $this->url, 'title' => "地标性建筑", 'list' => $list]);
    }

    public function site()
    {
        $this->model = new ScSite();
        if (request()->isAjax()) {
            $where = $this->buildSelectParams("", false);
        } else {
            $where = $this->buildSelectParamsOnly("", false);
        }
        $dy_list = [];
        $dy_type = getDictionary(564);
        foreach ($dy_type as $key => $item) {
            $dy_list[$item] = $this->model->where($where)->where("type", $item)->field("id,add_time,title,thumb,type")->order("add_time desc")->limit(5)->select()->toArray();
        }
        $community_id = input("community_id", 0);
//        if(input("community_id",0)){
//            $community_id = input("community_id",0);
//        }else if(isset($this->admin_info['community_id'])) {
//            $community_id = $this->admin_info['community_id'];
//        }else{
//            $community_id = 0;
//        }
        $jianjie = DB::table('sa_sc_brief')->where('community_id', $community_id)->find();
        if (request()->isAjax()) {
            return $this->view('', ['dy_type' => $dy_type, 'dy_list' => $dy_list, 'ajax' => 1, 'jianjie' => $jianjie]);
        }
        $param['type'] = input("type");
        $param['id']   = input('id', 0);
        $type          = $param['type'];
        if ($param['type'] == "gridGroup") {
            $type = "grid";
        }
        if ($type == "index") {
            $title = '建三江街道办';
        } else {
            $data  = Db::name($type)->where("id", $param['id'])->find();
            $title = $data[$type . '_name'];
        }
        $this->url = url("sc/" . $param['type'], ['id' => $param['id']]);
        return $this->view('', ['jianjie' => $jianjie, 'dy_type' => $dy_type, 'dy_list' => $dy_list,
            'param'                           => $param, 'url'       => $this->url,
            'title'                           => $title . "-党建园地"]);
    }

    public function workLink()
    {
        $param['type'] = input("type");
        $param['id']   = input('id', 0);
        $type          = $param['type'];
        if ($param['type'] == "gridGroup") {
            $type = "grid";
        }
        if ($type == "index") {
            $title = '建三江街道办';
        } else {
            $data  = Db::name($type)->where("id", $param['id'])->find();
            $title = $data[$type . '_name'];
        }
        $this->url = url("sc/" . $param['type'], ['id' => $param['id']]);
        return $this->view('', ['param' => $param, 'url' => $this->url, 'title' => "部门联动"]);
    }

    public function details()
    {
        $param['type'] = input("type");
        $param['id']   = input('id', 0);
        $type          = $param['type'];
        if ($param['type'] == "gridGroup") {
            $type = "grid";
        }
        if ($type == "index") {
            $title = '建三江街道办';
            $data  = Db::name("community")->where("id", 0)->find();
        } else if ($type == "building") {
            $grid_id = Db::name($type)->where("id", $param['id'])->value("grid_id");
            $type    = "grid";
            $data    = Db::name($type)->where("id", $grid_id)->find();
            $title   = $data['grid_name'];
        } else {
            $data  = Db::name($type)->where("id", $param['id'])->find();
            $title = $data[$type . '_name'];
        }
        $this->url = url("sc/" . $param['type'], ['id' => $param['id']]);
        return $this->view('', [
            'title' => $title,
            'data'  => $data,
        ]);
    }

    public function portal()
    {
        $param['type'] = input("type");
        $param['id']   = input('id', 0);
        $type          = $param['type'];
        if ($param['type'] == "gridGroup") {
            $type = "grid";
        }
        if ($type == "index") {
            $title = '建三江街道办';
        } else {
            $data  = Db::name($type)->where("id", $param['id'])->find();
            $title = $data[$type . '_name'];
        }
        $this->url = url("sc/" . $param['type'], ['id' => $param['id']]);
        $list      = Db::name("sc_dynamics")->order("community_id,grid_group_id,grid_id,add_time desc")->paginate([
            'list_rows' => 22,
            'query'     => $param,
        ]);
        return $this->view('', ['param' => $param, 'url'            => $this->url,
            'title'                         => $title . "-要闻动态", 'list' => $list, 'page' => $list->render()]);
    }

    public function dynamics()
    {
        $this->model = new ScDynamics();
        if (request()->isAjax()) {
            $where = $this->buildSelectParams("", false);
        } else {
            $where = $this->buildSelectParamsOnly("", false);
        }
        $dy_list = [];
        // 2025-05-13 by 周洪利
        // 增加按年份筛选数据，默认为当年数据。
        $year_id = (int) input('year_id', date('Y'));
        $dy_type = getDictionary(544);
        foreach ($dy_type as $key => $item) {
            $dy_list[$item] = $this->model
                ->where($where)
                ->where("type", $item)
            // 2025-05-13 by 周洪利
            // 增加按年份筛选数据
                ->where(function ($query) use ($year_id) {
                    $query->whereRaw('YEAR(create_time) = ?', [$year_id]);
                    return $query;
                })
                ->field("id,add_time,title,thumb,type")
                ->order("add_time desc")
                ->limit(5)
                ->select()
                ->toArray();
        }
        if (request()->isAjax()) {
            return $this->view('', ['dy_type' => $dy_type, 'dy_list' => $dy_list, 'ajax' => 1]);
        }
        $param['type'] = input("type");
        $param['id']   = input('id', 0);
        $type          = $param['type'];
        if ($param['type'] == "gridGroup") {
            $type = "grid";
        }
        if ($type == "index") {
            $title = '建三江街道办';
        } else {
            $data  = Db::name($type)->where("id", $param['id'])->find();
            $title = $data[$type . '_name'];
        }
        $this->url = url("sc/" . $param['type'], ['id' => $param['id']]);
        return $this->view('', ['dy_type' => $dy_type, 'dy_list' => $dy_list,
            'param'                           => $param, 'url'       => $this->url,
            'title'                           => $title . "-要闻动态"]);
    }

    public function government()
    {
        $this->model = new ScGovernment();
        if (request()->isAjax()) {
            $where = $this->buildSelectParams("", false);
        } else {
            $where = $this->buildSelectParamsOnly("", false);
        }
        $dy_list = [];
        $dy_type = getDictionary(554);
        foreach ($dy_type as $key => $item) {
            $dy_list[$item] = $this->model->where($where)->where("type", $item)->field("id,add_time,title,thumb,type")->order("add_time desc")->limit(5)->select()->toArray();
        }
        if (request()->isAjax()) {
            return $this->view('', ['dy_type' => $dy_type, 'dy_list' => $dy_list, 'ajax' => 1]);
        }
        $param['type'] = input("type");
        $param['id']   = input('id', 0);
        $type          = $param['type'];
        if ($param['type'] == "gridGroup") {
            $type = "grid";
        }
        if ($type == "index") {
            $title = '建三江街道办';
        } else {
            $data  = Db::name($type)->where("id", $param['id'])->find();
            $title = $data[$type . '_name'];
        }
        return $this->view('', ['dy_type' => $dy_type, 'dy_list' => $dy_list,
            'param'                           => $param, 'url'       => $this->url,
            'title'                           => $title . "-政务公开"]);
    }

    public function file()
    {
        if ($this->admin_info['community_id']) {
            return $this->redirect(url('sc/file_view'));
        }
        $param['type'] = input("type");
        $param['id']   = input('id', 0);
        $type          = $param['type'];
        if ($param['type'] == "gridGroup") {
            $type = "grid";
        }
        if ($type == "index") {
            $title = '建三江街道办';
        } else {
            $data  = Db::name($type)->where("id", $param['id'])->find();
            $title = $data[$type . '_name'];
        }
        $list[0]     = Db::name("sc_file")->where("level", 1)->order("add_time desc")->limit(5)->select()->toArray();
        $list[1]     = Db::name("sc_file")->where("level", 2)->order("add_time desc")->limit(5)->select()->toArray();
        $list[2]     = Db::name("sc_file")->where("level", 3)->order("add_time desc")->limit(5)->select()->toArray();
        $list['day'] = Db::query("SELECT * from (SELECT f.id, f.title, DATEDIFF(f.download_limit, CURDATE()) DAY, count(*) num, f.add_time
                        FROM  `sa_sc_file` `f` LEFT JOIN `sa_sc_file_log` `l` ON `f`.`id` = l.file_id AND l.type = 2
                        GROUP BY `f`.`id` ORDER BY `add_time` DESC) t where t.day < 7 and t.num < 12 limit 5");
        $list['month']['total']     = Db::name("sc_file")->where("add_month", date('Y-m'))->count();
        $list['month']['community'] = Db::name("community")->alias("c")
            ->join("sa_sc_file_log l", "c.id=l.community_id and l.type=2", "left")
            ->join("sa_sc_file f", "l.file_id=f.id and f.add_month='" . date('Y-m') . "'", "left")->field("c.community_name,count(f.id) num")
            ->where("c.id > 0")->group("c.id")->select()->toArray();
        $total = Db::name("sc_file")->where("add_month", date('Y-m'))->field("id")->order("add_time")->select()->toArray();
        $index = [0 => '①', 1 => '②', 2 => '③', 3 => '④', 4 => '⑤', 5 => '⑥', 6 => '⑦', 7 => '⑧', 8 => '⑨', 9 => '⑩'];
        foreach ($total as $key => $item) {
            $list['finish']['file'][$item['id']] = $index[$key];
        }
        $community = Db::name("community")->alias("c")
            ->join("sa_sc_file_log l", "c.id=l.community_id and l.type=2", "left")
            ->join("sa_sc_file f", "l.file_id=f.id and f.add_month='" . date('Y-m') . "'", "left")->field("c.community_name,f.id")
            ->where("c.id > 0")->order("c.id")->select()->toArray();
        $list['finish']['community'] = [];
        foreach ($community as $item) {
            $list['finish']['community'][$item['community_name']][] = $item['id'];
        }
        $this->url = url("sc/" . $param['type'], ['id' => $param['id']]);
        return $this->view('', ['param' => $param, 'url' => $this->url, 'title' => $title . "-文件管理", 'list' => $list]);
    }

    public function file_view()
    {
        $type = "community";
        $id   = $this->admin_info['community_id'];
        if ($this->admin_info['grid_id']) {
            $type = "grid";
            $id   = $this->admin_info['grid_id'];
        } else if ($this->admin_info['grid_group_id']) {
            $type = "grid";
            $id   = $this->admin_info['grid_group_id'];
        }
        $data  = Db::name($type)->where("id", $id)->find();
        $title = $data[$type . '_name'];
        $where = [];
        if ($this->admin_info['grid_id']) {
            $where[] = ['grid_id', 'find in set', $this->admin_info['grid_id']];
        } else if ($this->admin_info['grid_group_id']) {
            $where[] = ['grid_id', 'find in set', $this->admin_info['grid_group_id']];
        }
        $list[0]   = Db::name("sc_file")->where("level", 1)->where($where)->order("add_time desc")->limit(5)->select()->toArray();
        $list[1]   = Db::name("sc_file")->where("level", 2)->where($where)->order("add_time desc")->limit(5)->select()->toArray();
        $list[2]   = Db::name("sc_file")->where("level", 3)->where($where)->order("add_time desc")->limit(5)->select()->toArray();
        $this->url = url("sc/" . $type, ['id' => $id]);
        return $this->view('', ['param' => ['type' => $type, 'id' => $id], 'url' => $this->url, 'title' => $title . "-文件管理", 'list' => $list, 'admin_info' => $this->admin_info]);
    }

    // 全景
    public function panorama()
    {
        $param['type'] = input("type");
        $param['id']   = input('id', 0);
        $type          = $param['type'];
        if ($param['type'] == "gridGroup") {
            $type = "grid";
        }
        if ($type == "index") {
            $url = 'index';
        } else if ($type == "building") {
            $where['id'] = $param['id'];
            if ($this->admin_info['community_id']) {
                $where['community_id'] = $this->admin_info['community_id'];
            }
            if ($this->admin_info['grid_group_id']) {
                $where['grid_group_id'] = $this->admin_info['grid_group_id'];
            }
            if ($this->admin_info['grid_id']) {
                $where['grid_id'] = $this->admin_info['grid_id'];
            }
            $image = Db::name($type)->where($where)->value("image");
            if (empty($image)) {
                return "";
            }
            if (strpos($image, 'http') === 0) {
                $url = $image;
            } else {
                $image = json_decode($image, true);
                $url   = $image[0]['src'];
            }
            // return "<img src='$url' id='draggable-image' draggable='false' style='position: absolute; cursor: grab;display: block; margin: 0 auto; max-width: 1200px; max-height: 750px;transform:translate(-50%,-50%);z-index:100;' />";
            return "<img src='$url' style='display: block; margin: 0 auto; max-width: 1200px; max-height: 750px;' />";

        } else {
            $url = Db::name($type)->where("id", $param['id'])->value("url");
        }
        // return null;
        return $this->redirect("/720/html/$url/index.html");
    }

    public function getScTotal($id, $column)
    {
        $total['rkfl'] = $this->getRkflTotal($id, $column);
        $total['fwsj'] = $this->getFwTotal($id, $column);
        $total['jgdw'] = $this->getPlaceTotal($id, $column);
        $total['rkfb'] = $this->getRkfbTotal($id, $column);
        $total['pffb'] = $this->getPffbTotal($id, $column);
        $total['tsrq'] = $this->getTsrqTotal($id, $column);
        if ($column != 'building_id') {
            $total['ggss'] = $this->getGgssTotal($id, $column);
        }
        return $total;
    }

    public function getRkflTotal($id, $column)
    {
        $where["p." . $column] = $id;
        if ($column == 'community_id' && $id == 0) {
            $where = [];
        }
        $where['p.delete_time'] = null;
        // 常住人口
        $person['czrk'] = Db::name("person")->alias("p")
            ->join("sa_building b", "p.building_id=b.id")
            ->join("sa_house h", "p.house_id=h.id")
            ->where($where)->where("p.person_type = 1 and p.household = 0 and p.dead=0")->count();
        // 流动人口
        $person['ldrk'] = Db::name("person")->alias("p")
            ->join("sa_building b", "p.building_id=b.id")
            ->join("sa_house h", "p.house_id=h.id")
            ->where($where)->where("p.person_type = 2 and p.household = 0 and p.dead=0")->count();
        // 暂离人口
        $person['zlrk'] = Db::name("person")->alias("p")
            ->join("sa_building b", "p.building_id=b.id")
            ->join("sa_house h", "p.house_id=h.id")
            ->where($where)->where("p.person_type = 3 and p.household = 0 and p.dead=0")->count();
        // 实有人口
        $person['syrk'] = Db::name("person")->alias("p")
            ->join("sa_building b", "p.building_id=b.id")
            ->join("sa_house h", "p.house_id=h.id")
            ->where($where)->where("p.person_type < 4 and p.household = 0 and p.dead=0")->count();
        return $person;
    }

    public function getFwTotal($id, $column)
    {
        $alias                   = "b.";
        $where[$alias . $column] = $id;
        if ($column == 'community_id' && $id == 0) {
            $where = [];
        }
        $data[] = Db::name("building")->alias("b")->where($where)->count();
        $data[] = Db::name("building")->alias("b")->where($where)->where("floor > 1 and type not in (11,16)")->count();
        $data[] = Db::name("building")->alias("b")->where($where)->where("floor = 1 and type not in (11,16)")->count();
        $data[] = Db::name("building")->alias("b")->where($where)->where("type = 11")->count();
        $data[] = Db::name("building")->alias("b")->where($where)->where("type = 16")->count();
        $data[] = Db::name("house")->alias("b")->where($where)->count();
        return $data;
    }

    public function getPlaceTotal($id, $column)
    {
        $where[$column] = $id;
        if ($column == 'community_id' && $id == 0) {
            $where = [];
        }
        $place['merchant']     = Db::name("place_merchant")->where($where)->count();
        $place['enterprise']   = Db::name("place_enterprise")->where($where)->count();
        $place['government_1'] = Db::name("place_government")->where($where)->where("type = 1")->count();
        $place['government_2'] = Db::name("place_government")->where($where)->where("type = 2")->count();
        if ($column != "building_id") {
            $place['facilities'] = Db::name("place_communal_facility")->where($where)->count();
        }
        $place['hospital'] = Db::name("place_government")->where($where)->where("unit_name like '%医院%'")->count();
        $place['clinic']   = Db::name("place_government")->where($where)->where("unit_name like '%社区门诊%'")->count();
        $place['school']   = Db::name("place_government")->where($where)->where("unit_name like '%学%'")->count();
        $place['book']     = Db::name("place_government")->where($where)->where("unit_name like '%图书馆%'")->count();
        return $place;
    }

    public function getRkfbTotal($id, $column)
    {
        $where[$column] = $id;
        $groups         = [];
        $group_column   = "community_name";
        $group_name     = [];
        $group_value    = [];
        $group_key      = "";
        $group_map      = [];
        if ($column == 'community_id' && $id == 0) {
            $groups    = Db::name("community")->where("id > 0")->select()->toArray();
            $group_key = "community_id";
        } else if ($column == 'community_id') {
            $groups       = Db::name("grid")->where($where)->where("grid_group_id", 0)->select()->toArray();
            $group_column = "grid_name";
            $group_key    = "grid_group_id";
        } else if ($column == 'grid_group_id') {
            $groups       = Db::name("grid")->where($where)->select()->toArray();
            $group_column = "grid_name";
            $group_key    = "grid_id";
        } else if ($column == 'grid_id') {
            $groups       = Db::name("building")->where($where)->select()->toArray();
            $group_column = "building_name";
            $group_key    = "building_id";
        }
        foreach ($groups as $group) {
            $group_name[]  = "\"" . $group[$group_column] . "\"";
            $group_value[] = Db::name("person")->alias("p")
                ->join("sa_building b", "p.building_id=b.id")
                ->join("sa_house h", "p.house_id=h.id")
                ->where("p." . $group_key, $group['id'])->where("p.delete_time is null and p.person_type < 4 and p.household = 0 and p.dead=0")->count();
            $group_map[] = "'{$group_key}={$group['id']}'";
        }
        return [
            "group_name"  => implode(",", $group_name),
            "group_value" => implode(",", $group_value),
            "group_map"   => implode(",", $group_map),
        ];
    }

    public function getPffbTotal($id, $column)
    {
        $where[$column] = $id;
        $groups         = [];
        $group_column   = "community_name";
        $group_name     = [];
        $group_value    = [];
        $group_key      = "";
        if ($column == 'community_id' && $id == 0) {
            $groups    = Db::name("community")->where("id > 0")->select()->toArray();
            $group_key = "community_id";
        } else if ($column == 'community_id') {
            $groups       = Db::name("grid")->where($where)->where("grid_group_id", 0)->select()->toArray();
            $group_column = "grid_name";
            $group_key    = "grid_group_id";
        } else if ($column == 'grid_group_id') {
            $groups       = Db::name("grid")->where($where)->select()->toArray();
            $group_column = "grid_name";
            $group_key    = "grid_id";
        } else if ($column == 'grid_id') {

        }
        foreach ($groups as $group) {
            if ($group_column == 'community_name') {
                $group[$group_column] = msubstr($group[$group_column], 0, 2, "utf-8", false);
            } else if ($group_column == 'group_name') {
                $title_arr            = explode("第", $group[$group_column]);
                $group[$group_column] = "第" . $title_arr[1];
            }
            $group_name[]  = "\"" . $group[$group_column] . "\"";
            $group_value[] = Db::name("building")->where($group_key, $group['id'])
                ->where("floor = 1 and type not in (11,16)")->count();
        }
        return [
            "group_name"  => implode(",", $group_name),
            "group_value" => implode(",", $group_value),
        ];
    }

    public function getGgssTotal($id, $column)
    {

        if ($column == 'community_id' && $id == 0) {
            $where = [];
        } else if ($column == 'building' && $id) {
            // 查找建筑
            $building_rs = Db::name('building')->where('id', $id)->find();
            $where[]     = ['grid_id' => $building_rs['grid_id']];
        } else {
            $where[$column] = $id;
        }

        $place['place_gy']  = Db::name("place_communal_facility")->where($where)->where("type_2_id = 4")->count();
        $place['place_gc']  = Db::name("place_communal_facility")->where($where)->where("type_2_id = 5")->count();
        $place['place_tyc'] = Db::name("place_communal_facility")->where($where)->where("type_2_id = 6")->count();
        $place['place_js']  = Db::name("place_communal_facility")->where($where)->where("type_2_id = 12")->count();
        $place['place_tcc'] = Db::name("place_communal_facility")->where($where)->where("type_2_id = 13")->count();
        $place['place_gce'] = Db::name("place_communal_facility")->where($where)->where("type_2_id = 14")->count();
        $place['place_jyz'] = Db::name("place_communal_facility")->where($where)->where("type_2_id = 16")->count();
        $place['place_jq']  = Db::name("place_communal_facility")->where($where)->where("type_2_id = 17")->count();
        return $place;
    }

    public function getTsrqTotal($id, $column)
    {
        $cate                          = [70 => '老人', 71 => '困境人群', 72 => '困境儿童', 73 => '病人', 74 => '劣迹人群', 75 => '其他人群'];
        $rbac                          = getRbac();
        $alias                         = "t";
        $where[$alias . "." . $column] = $id;
        if ($column == 'community_id' && $id == 0) {
            $where = [];
        }
        $data = [];
        foreach ($rbac as $item) {
            $count = Db::name("person_" . $item['table'])->alias($alias)
                ->join("sa_person p", $alias . ".person_id=p.id and (p.delete_time is null or (p.delete_flag=1 and p.grid_id <> $alias.grid_id)) and dead = 0")
                ->where($where)->count();
            $data[$item['cate_id']]['name'] = $cate[$item['cate_id']];
            if (empty($data[$item['cate_id']]['value'])) {
                $data[$item['cate_id']]['value'] = 0;
            }
            $data[$item['cate_id']]['value'] += $count;
        }
        $total = [];
        foreach ($data as $item) {
            $total[] = $item;
        }
        return $total;
    }

    public function getTsrqTotal2($id, $column)
    {
        $dictionaryGroup       = getDictionaryGroup([70, 71, 72, 73, 74, 75]);
        $cate                  = [70 => '老人', 71 => '困境人群', 72 => '困境儿童', 73 => '病人', 74 => '劣迹人群', 75 => '其他人群'];
        $where["p." . $column] = $id;
        if ($column == 'community_id' && $id == 0) {
            $where = [];
        }
        $where['p.delete_time']            = null;
        $data                              = [];
        $dictionaryGroup[75]['face']       = "党员";
        $dictionaryGroup[75]['disability'] = "残疾人";
        foreach ($dictionaryGroup as $index => $groupList) {
            $sub_where = [];
            foreach ($groupList as $key => $vo) {
                $key         = camelCaseToUnderscore($key);
                $sub_where[] = "$key = 1";
            }
            $sub_where = implode(" or ", $sub_where);
            $value     = Db::name("person")->alias("p")
                ->where($where)->where($sub_where)->count();
            $data[] = ['name' => $cate[$index], 'value' => $value];
        }
        return $data;
    }

    // 统计所有特殊人群人员数量
   public function getTsrqTotal3($id,$column){
        $rbac = getRbac();
        $alias = "t";

        $where[$alias.".".$column] = $id;
        if($column=='community_id'&&$id==0){
            $where=[];
        }

        $where['dead'] = 0;

        $total = [];
        foreach ($rbac as $item){
            $currentWhere = $where;  // $where 已初始化为数组
            $itemwhere = "";
            if(array_key_exists('where',$item)){
                $itemwhere = $item['where'];
            }
            $query = Db::name("person_".$item['table'])->alias($alias)
                ->join("sa_person p",$alias.".person_id=p.id ");

           if($item['join_house']==1){
                $query->join("sa_building b","p.building_id = b.id",'left');
                $query->join("sa_house h","p.house_id = h.id",'left');
            }

            $currentWhere['p.delete_time'] = null;
            $query->where($currentWhere)->where($itemwhere);

            $count = $query->count();
            $data = [
                'name' => $item['name'],
                'value' => $count
            ];
            $total[] = $data;
        }

        return $total;
    }

}
