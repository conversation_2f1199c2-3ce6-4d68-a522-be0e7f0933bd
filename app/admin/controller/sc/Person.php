<?php
declare (strict_types = 1);
// +----------------------------------------------------------------------
// | swiftAdmin 极速开发框架 [基于WebMan开发]
// +----------------------------------------------------------------------
// | Copyright (c) 2020-2030 http://www.swiftadmin.net
// +----------------------------------------------------------------------
// | swiftAdmin.net High Speed Development Framework
// +----------------------------------------------------------------------
// | Author: meystack <<EMAIL>> Apache 2.0 License
// +----------------------------------------------------------------------
namespace app\admin\controller\sc;

use app\AdminController;
use app\common\model\PersonEmployment;
use app\common\model\PersonIncome;
use think\facade\Db;

class Person extends AdminController
{
    protected string $url = "";

    public function building()
    {
        $id    = input("id");
        $alias = "h";
        $data  = Db::name("building")->alias($alias)
            ->join("sa_community c", "$alias.community_id=c.id", "LEFT")
            ->join("sa_grid g", "$alias.grid_id=g.id", "LEFT")
            ->join("sa_grid gg", "$alias.grid_group_id=gg.id", "LEFT")
            ->join("sa_street s", "$alias.street_id=s.id", "LEFT")
            ->join("sa_street r", "$alias.road_id=r.id", "LEFT")
            ->field("$alias.*,g.grid_name,gg.grid_name grid_group_name,c.community_name,s.name street_name,r.name road_name")
            ->where("$alias.id", $id)->find();
        $unit = 1;
        $list = [];
        for ($unit; $unit <= $data['unit']; $unit++) {
            $list[$unit] = Db::name("house")->where(["building_id" => $id, "unit" => $unit])->order("sort")->select()->toArray();
        }
        return $this->view('', ['list' => $list, 'data' => $data]);
    }

    public function house()
    {
        $id    = input("id");
        $alias = "h";
        $house = Db::name("house")->alias($alias)
            ->join("sa_community c", $alias . ".community_id=c.id", "LEFT")
            ->join("sa_grid g", $alias . ".grid_id=g.id", "LEFT")
            ->join("sa_grid gg", $alias . ".grid_group_id=gg.id", "LEFT")
            ->join("sa_building b", $alias . ".building_id=b.id")
            ->field($alias . ".*,g.grid_name,gg.grid_name grid_group_name,c.community_name,b.building_name")
            ->where("$alias.id", $id)->find();
        $list = Db::name("person")->where("house_id", $id)->where('delete_flag', '<>', 1)->select()->toArray();
        return $this->view('', ['list' => $list, 'house' => $house]);
    }

    public function personInfo()
    {
        $id    = input("id");
        $alias = "p";
        $data  = Db::name("person")->alias($alias)
            ->join("sa_community c", $alias . ".community_id=c.id", "LEFT")
            ->join("sa_grid g", $alias . ".grid_id=g.id", "LEFT")
            ->join("sa_grid gg", $alias . ".grid_group_id=gg.id", "LEFT")
            ->join("sa_building b", $alias . ".building_id=b.id", "LEFT")
            ->join("sa_house h", $alias . ".house_id=h.id", "LEFT")
            ->field($alias . ".*,g.grid_name,gg.grid_name grid_group_name,c.community_name,b.building_name,h.unit,h.floor,h.code")
            ->where("$alias.id", $id)->find();
        $PersonEmployment = new PersonEmployment();
        $PersonIncome     = new PersonIncome();
        $emp              = $PersonEmployment->where("person_id", $data['id'])->find();
        $emp              = empty($emp) ? $this->getTableFields($PersonEmployment) : $emp;
        $income           = $PersonIncome->where("person_id", $data['id'])->find();
        $income           = empty($income) ? $this->getTableFields($PersonIncome) : $income;
        $family           = empty($data['house_id']) ? [] : Db::name("person")->where("id <> $id and house_id= {$data['house_id']}")->where('delete_flag', '<>', 1)->select()->toArray();
        $vehicle          = Db::name("person_vehicle")->where("id_code", $data['id_code'])->select()->toArray();

        // 2025-05-12 by 周洪利
        // 实现特殊人群分类显示
        $is_government  = [];
        $is_dilemma     = [];
        $children_group = [];
        $disease_group  = [];
        $other_group    = [];
        $old_group      = [];
        $risk_group     = [];
        $person_rs      = Db::name('person')->where('id', $id)->find();
        if ($person_rs) {
            // 政务人群
            if ($person_rs['representative_party'] == 1) {
                $is_government[] = '党代表';
            }
            if ($person_rs['representative_people'] == 1) {
                $is_government[] = '人大代表';
            }
            if ($person_rs['committee_member'] == 1) {
                $is_government[] = '政协委员';
            }

            // 社会关爱老年人群体
            if ($person_rs['old_oldest'] == 1) {
                $old_group[] = '高龄老人';
            }
            if ($person_rs['old_empty'] == 1) {
                $old_group[] = '空巢老人';
            }
            if ($person_rs['old_lonely'] == 1) {
                $old_group[] = '孤寡老人';
            }
            if ($person_rs['old_single'] == 1) {
                $old_group[] = '独居老人';
            }

            // 困境人群
            if ($person_rs['low_income_family'] == 1 || $person_rs['low_income_person'] == 1) {
                $is_dilemma[] = '低保对象';
            }
            if ($person_rs['special_support'] == 1) {
                $is_dilemma[] = '特扶';
            }
            if ($person_rs['guarantee_house'] == 1) {
                $is_dilemma[] = '保障房';
            }

            // 困境儿童
            if ($person_rs['children_orphan'] == 1) {
                $children_group[] = '孤儿';
            }
            if ($person_rs['children_abandoned'] == 1) {
                $children_group[] = '无人抚养儿童';
            }
            if ($person_rs['children_stay'] == 1) {
                $children_group[] = '留守儿童';
            }
            if ($person_rs['children_disability'] == 1) {
                $children_group[] = '残疾儿童';
            }
            if ($person_rs['children_subsistence_allowance'] == 1) {
                $children_group[] = '低保儿童';
            }

            // 特殊疾病群体
            if ($person_rs['illness_mental'] == 1) {
                $disease_group[] = '精神病人';
            }
            if ($person_rs['illness_aids'] == 1) {
                $disease_group[] = '艾滋病人';
            }
            if ($person_rs['disability'] == 1) {
                $disease_group[] = '残疾人';
            }

            // 特定风险人群
            if ($person_rs['drugged'] == 1) {
                $risk_group[] = '吸毒人员';
            }
            if ($person_rs['petition'] == 1) {
                $risk_group[] = '重点信访';
            }
            if ($person_rs['releaser'] == 1) {
                $risk_group[] = '刑满释放人员';
            }
            if ($person_rs['corrector'] == 1) {
                $risk_group[] = '社区矫正人员';
            }

            // 其他人群
            if ($person_rs['ex_serviceman'] == 1) {
                $other_group[] = '退役军人';
            }
            if ($person_rs['volunteer'] == 1) {
                $other_group[] = '志愿者';
            }
            if ($person_rs['face'] == 1) {
                $other_group[] = '党员';
            }
            if ($person_rs['adolescent'] == 1) {
                $other_group[] = '重点青少年';
            }
        }

        $personGroup = [
            'old_group'      => $old_group,      //社会关爱老年人群
            'government'     => $is_government,  // 是否政务人群
            'dilemma'        => $is_dilemma,     //困境人群
            'other_group'    => $other_group,    //其他人群
            'children_group' => $children_group, //困境儿童
            'disease_group'  => $disease_group,  //特定疾病人群
            'risk_group'     => $risk_group,     //特定风险人群
        ];

        return $this->view('', ['data' => $data, 'emp' => $emp, 'income' => $income, 'family' => $family, 'vehicle' => $vehicle, 'personGroup' => $personGroup]);
    }

    public function place()
    {
        $id    = input("id");
        $type  = input("type");
        $alias = "p";
        if ($type == 'communal_facility') {
            $data = Db::name("place_" . $type)->alias($alias)
                ->join("sa_community c", $alias . ".community_id=c.id", "LEFT")
                ->join("sa_grid g", $alias . ".grid_id=g.id", "LEFT")
                ->join("sa_grid gg", $alias . ".grid_group_id=gg.id", "LEFT")
                ->field($alias . ".*,g.grid_name,gg.grid_name grid_group_name,c.community_name")
                ->where("$alias.id", $id)->find();
        } else if ($type == 'vehicle') {
            $data = Db::name("person_" . $type)->alias($alias)
                ->join("sa_community c", $alias . ".community_id=c.id", "LEFT")
                ->join("sa_grid g", $alias . ".grid_id=g.id", "LEFT")
                ->join("sa_grid gg", $alias . ".grid_group_id=gg.id", "LEFT")
                ->field($alias . ".*,g.grid_name,gg.grid_name grid_group_name,c.community_name")
                ->where("$alias.id", $id)->find();
        } else {
            $data = Db::name("place_" . $type)->alias($alias)
                ->join("sa_community c", $alias . ".community_id=c.id", "LEFT")
                ->join("sa_grid g", $alias . ".grid_id=g.id", "LEFT")
                ->join("sa_grid gg", $alias . ".grid_group_id=gg.id", "LEFT")
                ->join("sa_building b", $alias . ".building_id=b.id", "LEFT")
                ->field($alias . ".*,g.grid_name,gg.grid_name grid_group_name,c.community_name,b.building_name")
                ->where("$alias.id", $id)->find();
        }
        return $this->view('sc/person/' . $type, ['data' => $data, 'type' => $type, 'sql' => $sql]);
    }

    public function map()
    {
        $id   = input("id");
        $data = Db::name("building_map")->where("id", $id)->find();
        return $this->view('', ['data' => $data]);
    }

    public function load()
    {
        $cate          = input('cate', "person");
        $community_id  = input('community_id', 0);
        $grid_group_id = input('grid_group_id', 0);
        $grid_id       = input('grid_id', 0);
        $type          = 'community_id';
        $id            = $community_id;
        $building_id   = input('building_id', '');
        // if($building_id != ''){
        //   $id = $building_id;
        // }

        if ($grid_id) {
            $type = 'grid_id';
            $id   = $grid_id;
        } else if ($grid_group_id) {
            $type = 'grid_group_id';
            $id   = $grid_group_id;
        }
        $total = match ($cate) {
            "person" => $this->getPersonTotal($id, $type),
            "building" => $this->getTypeTotal($id, $type, 13, 'building', 'type'),
            "house" => $this->getHouseTotal($id, $type),
            "place" => $this->getPlaceTotal($id, $type),
            default => [],
        };
        return $this->success("", "/", $total);
    }

    // 获取场所分布统计数据
    public function getCsfbTotal($id, $column)
    {

        $place_merchant = Db::name('place_merchant')->where(function ($query) use ($column, $id) {
            if ($id) {
                $query->where($column, $id);
                return $query;
            }
        })->count('id');
        $place_enterprise = Db::name('place_enterprise')->where(function ($query) use ($column, $id) {
            if ($id) {
                $query->where($column, $id);
                return $query;
            }
        })->count('id');
        $place_government = Db::name('place_government')->where(function ($query) use ($column, $id) {
            if ($id) {
                $query->where($column, $id);
                return $query;
            }
        })->count('id');
        $place_communal_facility = Db::name('place_communal_facility')->where(function ($query) use ($column, $id) {
            if ($id) {
                $query->where($column, $id);
                return $query;
            }
        })->count('id');

        $total = ["group_name" => ['个体工商户', '企业', '机关单位', '公共设施'], "group_value" => [$place_merchant, $place_enterprise, $place_government, $place_communal_facility]];
        return $total;
    }

    public function getPersonTotal($id, $column)
    {
        $index         = new Index();
        $total['rkfl'] = $index->getRkflTotal($id, $column);
        $total['rkfb'] = $this->getRkfbTotal($id, $column);
        $total['hklx'] = $this->getHklxTotal($id, $column); //户口类型

        unset($total['hklx']['3']); //删除户口类型中的其他项

        $total['edu'] = $this->getEduTotal($id, $column);
        return $total;
    }

    public function getTypeTotal($id, $column, $type_id, $table, $type)
    {
        $build_types = getDictionary($type_id);
        $group       = [];
        foreach ($build_types as $key => &$item) {
            $group[$key] = ['name' => $item, 'value' => 0];
        }
        $where[$column] = $id;
        if ($column == 'community_id' && $id == 0) {
            $where = [];
        }
        $list = Db::name($table)->where($where)
            ->field("$type,count(*) value")->group($type)
            ->select()->toArray();
        unset($item);
        foreach ($list as $item) {
            $group[$item[$type]]['value'] = $item['value'];
        }
        $group_value = [];
        $group_name  = [];
        foreach ($group as &$item) {
            // $group_name[] = $item['name'];
            // if($item['name'] == '多层住宅'){
            //     $group_name[] = $item['name'].'(4-9层)';
            // }
            // if($item['name'] == '高层住宅'){
            //     $group_name[] = $item['name'].'(10层以上)';
            // }
            // if($item['name'] == '低层住宅平房'){
            //     $group_name[] = $item['name'].'(1-3层)';
            // }

            // 2025-05-11 by 周洪利
            // 此处需求变更，要求将多层住宅、多层住宅(4-9)层这些合并在一起显示
            // 因为原本接口也未对新加的多层住宅(4-9层)做出数据统计，只加了name没有加value。
            // 所以此处直接去掉原来的逻辑处理，只在原来的名字后面，追加一个后缀（4-9层）....
            if ($item['name'] == '多层住宅') {
                $item['name'] = $item['name'] . '(含4-9层)';
            }
            if ($item['name'] == '高层住宅') {
                $item['name'] = $item['name'] . '(含10层以上)';
            }
            if ($item['name'] == '低层住宅平房') {
                $item['name'] = $item['name'] . '(含1-3层)';
            }
            $group_name[]  = $item['name'];
            $group_value[] = $item['value'];
        }
        return [
            "group_name"  => $group_name,
            "group_value" => $group_value,
        ];
    }

    public function getHouseTotal($id, $column)
    {
        $total['fwfb']             = $this->getFwfbTotal($id, $column, "house");
        $total['property_right']   = $this->getTypeTotal($id, $column, 349, 'house', 'property_right');
        $total['usage_category']   = $this->getTypeTotal($id, $column, 355, 'house', 'usage_category');
        $total['house_type']       = $this->getTypeTotal($id, $column, 362, 'house', 'house_type');
        $total['residence_nature'] = $this->getTypeTotal($id, $column, 367, 'house', 'residence_nature');
        return $total;
    }

    public function getPlaceTotal($id, $column)
    {
        $total['gtfb']   = $this->getPlfbTotal($id, $column, "place_merchant");
        $total['qyfb']   = $this->getPlfbTotal($id, $column, "place_enterprise");
        $total['dwfb']   = $this->getPlfbTotal($id, $column, "place_government");
        $total['ggfb']   = $this->getPlfbTotal($id, $column, "place_communal_facility");
        $total['gtqyfl'] = $this->getPlflTotal($id, $column);
        $index           = new Index();
        $total['ggss']   = $index->getGgssTotal($id, $column);
        $total['jgdw']   = $index->getPlaceTotal($id, $column);
        $total['clyt']   = $this->getTypeTotal($id, $column, 42, 'person_vehicle', 'purpose');
        $total['csfb']   = $this->getCsfbTotal($id, $column);
        return $total;
    }

    public function getPlflTotal($id, $column)
    {
        $where[$column] = $id;
        if ($column == 'community_id' && $id == 0) {
            $where = [];
        }
        $merchant = Db::name("place_merchant_type")->alias("t")
            ->join("sa_place_merchant m", "m.type_1_id=t.id", "left")
            ->field("t.name,count(m.id) value")->group("t.id")
            ->where($where)->where("t.pid=0")->select()->toArray();
        $enterprise = Db::name("place_merchant_type")->alias("t")
            ->join("sa_place_enterprise m", "m.type_1_id=t.id", "left")
            ->field("t.name,count(m.id) value")->group("t.id")
            ->where($where)->where("t.pid=0")->select()->toArray();
        $group_value = [];
        $group_name  = [];
        $total       = 0;
        foreach ($merchant as $key => $item) {
            $group_name[]     = $item['name'];
            $group_value[0][] = $item['value'];
            $group_value[1][] = $enterprise[$key]['value'];
            $total += $item['value'] + $enterprise[$key]['value'];
        }
        return [
            "total"       => $total,
            "group_name"  => $group_name,
            "group_value" => $group_value,
        ];
    }

    public function getPlfbTotal($id, $column, $table)
    {
        $where[$column] = $id;
        $groups         = [];
        $group_column   = "community_name";
        $group_name     = [];
        $group_value    = [];
        $group_key      = "";
        if ($column == 'community_id' && $id == 0) {
            $groups    = Db::name("community")->where("id > 0")->select()->toArray();
            $group_key = "community_id";
        } else if ($column == 'community_id') {
            $groups       = Db::name("grid")->where($where)->where("grid_group_id", 0)->select()->toArray();
            $group_column = "grid_name";
            $group_key    = "grid_group_id";
        } else if ($column == 'grid_group_id') {
            $groups       = Db::name("grid")->where($where)->select()->toArray();
            $group_column = "grid_name";
            $group_key    = "grid_id";
        } else if ($column == 'grid_id') {
            $groups       = Db::name("building")->where($where)->select()->toArray();
            $group_column = "building_name";
            $group_key    = "building_id";
        }
        foreach ($groups as $group) {
            $group_name[] = $group[$group_column];
            if ($table == 'place_communal_facility') {
                $group_value[] = 0;
                continue;
            }
            $group_value[] = Db::name($table)->alias("p")
                ->where("p." . $group_key, $group['id'])->count();
        }
        return [
            "group_name"  => $group_name,
            "group_value" => $group_value,
        ];
    }

    public function getFwfbTotal($id, $column, $table)
    {
        $where[$column] = $id;
        $groups         = [];
        $group_column   = "community_name";
        $group_name     = [];
        $group_value    = [];
        $group_key      = "";
        if ($column == 'community_id' && $id == 0) {
            $groups    = Db::name("community")->where("id > 0")->select()->toArray();
            $group_key = "community_id";
        } else if ($column == 'community_id') {
            $groups       = Db::name("grid")->where($where)->where("grid_group_id", 0)->select()->toArray();
            $group_column = "grid_name";
            $group_key    = "grid_group_id";
        } else if ($column == 'grid_group_id') {
            $groups       = Db::name("grid")->where($where)->select()->toArray();
            $group_column = "grid_name";
            $group_key    = "grid_id";
        } else if ($column == 'grid_id') {
            $groups       = Db::name("building")->where($where)->select()->toArray();
            $group_column = "building_name";
            $group_key    = "building_id";
        }
        foreach ($groups as $group) {
            $group_name[]  = $group[$group_column];
            $group_value[] = Db::name($table)->alias("p")
                ->join("sa_building b", "p.building_id=b.id")
                ->where("p." . $group_key, $group['id'])->count();
        }
        return [
            "group_name"  => $group_name,
            "group_value" => $group_value,
        ];
    }

    public function getRkfbTotal($id, $column)
    {
        $where[$column] = $id;
        $groups         = [];
        $group_column   = "community_name";
        $group_name     = [];
        $group_value    = [];
        $group_key      = "";
        $group_map      = [];
        if ($column == 'community_id' && $id == 0) {
            $groups    = Db::name("community")->where("id > 0")->select()->toArray();
            $group_key = "community_id";
        } else if ($column == 'community_id') {
            $groups       = Db::name("grid")->where($where)->where("grid_group_id", 0)->select()->toArray();
            $group_column = "grid_name";
            $group_key    = "grid_group_id";
        } else if ($column == 'grid_group_id') {
            $groups       = Db::name("grid")->where($where)->select()->toArray();
            $group_column = "grid_name";
            $group_key    = "grid_id";
        } else if ($column == 'grid_id') {
            $groups       = Db::name("building")->where($where)->select()->toArray();
            $group_column = "building_name";
            $group_key    = "building_id";
        }
        foreach ($groups as $group) {
            $group_name[]  = $group[$group_column];
            $group_value[] = Db::name("person")->alias("p")
                ->join("sa_building b", "p.building_id=b.id")
                ->join("sa_house h", "p.house_id=h.id")
                ->where("p." . $group_key, $group['id'])->where("p.delete_time is null and p.person_type < 4 and p.household = 0 and p.dead=0")->count();
        }
        return [
            "group_name"  => $group_name,
            "group_value" => $group_value,
        ];
    }

    public function getHklxTotal($id, $column)
    {
        $where["p." . $column] = $id;
        if ($column == 'community_id' && $id == 0) {
            $where = [];
        }
        $where['p.delete_time'] = null;
        $where['p.dead'] = 0;
        $where['p.household'] = 0;
        $person                 = Db::name("person")->alias("p")
            ->join("sa_building b", "p.building_id=b.id", "INNER")
            ->join("sa_house h", "p.house_id=h.id", "INNER")
            ->join("sa_community c", "p.community_id=c.id")
            ->join("sa_grid g", "p.grid_id=g.id")
            ->join("sa_grid gg", "p.grid_group_id=gg.id")
            ->field("count(*) value,p.rpr,'未知' name")
            ->where('p.person_type < 4')
            ->where($where)->group("p.rpr")->select()->toArray();

        // 2025-05-11 by 周洪利
        // 此处原本调用的getDictionary函数，但其有些问题，不能正常返回键值和键名，问题出在键名是数字上
        // 所以另外写了一个函数，在原有数字键名前加key前缀来避免问题。
        $rpr = getDictionary2(404);

        foreach ($person as &$item) {
            $item['name'] = $rpr['key' . $item['rpr']];
        }
        if ($column == 'community_id' && $id != 0) {
            $person = $this->community_img_data($id);
            return $person;
        }

        return $person;
    }

    //人口信息
    public function community_img_data($id)
    {
        //非农业
        $result = Db::query("SELECT `p`.*,`g`.`grid_name`,gg.grid_name grid_group_name,`c`.`community_name`,`b`.`building_name` FROM `sa_person` `p` LEFT JOIN `sa_community` `c` ON `p`.`community_id`=`c`.`id` LEFT JOIN `sa_grid` `g` ON `p`.`grid_id`=`g`.`id` LEFT JOIN `sa_grid` `gg` ON `p`.`grid_group_id`=`gg`.`id` INNER JOIN `sa_building` `b` ON `p`.`building_id`=`b`.`id` INNER JOIN `sa_house` `h` ON `p`.`house_id`=`h`.`id` WHERE (  ( p.id in( SELECT object.id FROM ( SELECT `p`.`id` FROM `sa_person` `p` INNER JOIN `sa_building` `b` ON `p`.`building_id`=`b`.`id` INNER JOIN `sa_house` `h` ON `p`.`house_id`=`h`.`id` WHERE (  `p`.`community_id` = $id  AND `p`.`rpr` = '1'  AND `p`.`delete_time` IS NULL  AND `p`.`person_type` < '4'  AND `p`.`household` = '0' ) AND `p`.`delete_time` IS NULL ORDER BY `p`.`community_id`,`p`.`grid_group_id`,`p`.`grid_id`,`b`.`sort`,`h`.`unit`,`h`.`floor`,`h`.`sort`,`p`.`relation`,`p`.`sort`) AS object ) ) ) AND `p`.`delete_time` IS NULL ORDER BY `p`.`community_id`,`p`.`grid_group_id`,`p`.`grid_id`,`b`.`sort`,`h`.`unit`,`h`.`floor`,`h`.`sort`,`p`.`relation`,`p`.`sort`");
        $result = count($result);
        //农业
        $result2 = Db::query("SELECT `p`.*,`g`.`grid_name`,gg.grid_name grid_group_name,`c`.`community_name`,`b`.`building_name` FROM `sa_person` `p` LEFT JOIN `sa_community` `c` ON `p`.`community_id`=`c`.`id` LEFT JOIN `sa_grid` `g` ON `p`.`grid_id`=`g`.`id` LEFT JOIN `sa_grid` `gg` ON `p`.`grid_group_id`=`gg`.`id` INNER JOIN `sa_building` `b` ON `p`.`building_id`=`b`.`id` INNER JOIN `sa_house` `h` ON `p`.`house_id`=`h`.`id` WHERE (  ( p.id in( SELECT object.id FROM ( SELECT `p`.`id` FROM `sa_person` `p` INNER JOIN `sa_building` `b` ON `p`.`building_id`=`b`.`id` INNER JOIN `sa_house` `h` ON `p`.`house_id`=`h`.`id` WHERE (  `p`.`community_id` = $id  AND `p`.`rpr` = '2'  AND `p`.`delete_time` IS NULL  AND `p`.`person_type` < '4'  AND `p`.`household` = '0' ) AND `p`.`delete_time` IS NULL ORDER BY `p`.`community_id`,`p`.`grid_group_id`,`p`.`grid_id`,`b`.`sort`,`h`.`unit`,`h`.`floor`,`h`.`sort`,`p`.`relation`,`p`.`sort`) AS object ) ) ) AND `p`.`delete_time` IS NULL ORDER BY `p`.`community_id`,`p`.`grid_group_id`,`p`.`grid_id`,`b`.`sort`,`h`.`unit`,`h`.`floor`,`h`.`sort`,`p`.`relation`,`p`.`sort`");
        $result2 = count($result2);
        //居民
        $result3 = Db::query("SELECT `p`.*,`g`.`grid_name`,gg.grid_name grid_group_name,`c`.`community_name`,`b`.`building_name` FROM `sa_person` `p` LEFT JOIN `sa_community` `c` ON `p`.`community_id`=`c`.`id` LEFT JOIN `sa_grid` `g` ON `p`.`grid_id`=`g`.`id` LEFT JOIN `sa_grid` `gg` ON `p`.`grid_group_id`=`gg`.`id` INNER JOIN `sa_building` `b` ON `p`.`building_id`=`b`.`id` INNER JOIN `sa_house` `h` ON `p`.`house_id`=`h`.`id` WHERE (  ( p.id in( SELECT object.id FROM ( SELECT `p`.`id` FROM `sa_person` `p` INNER JOIN `sa_building` `b` ON `p`.`building_id`=`b`.`id` INNER JOIN `sa_house` `h` ON `p`.`house_id`=`h`.`id` WHERE (  `p`.`community_id` = $id  AND `p`.`rpr` = '3'  AND `p`.`delete_time` IS NULL  AND `p`.`person_type` < '4'  AND `p`.`household` = '0' ) AND `p`.`delete_time` IS NULL ORDER BY `p`.`community_id`,`p`.`grid_group_id`,`p`.`grid_id`,`b`.`sort`,`h`.`unit`,`h`.`floor`,`h`.`sort`,`p`.`relation`,`p`.`sort`) AS object ) ) ) AND `p`.`delete_time` IS NULL ORDER BY `p`.`community_id`,`p`.`grid_group_id`,`p`.`grid_id`,`b`.`sort`,`h`.`unit`,`h`.`floor`,`h`.`sort`,`p`.`relation`,`p`.`sort`");
        $result3 = count($result3);

        $data = [
            ['value' => $result, 'rpr' => '1', 'name' => '非农业'],
            ['value' => $result2, 'rpr' => '2', 'name' => '农业'],
            ['value' => $result3, 'rpr' => '3', 'name' => '居民户口'],
        ];

        return $data;
    }

    public function getEduTotal($id, $column)
    {
        $where["p." . $column] = $id;
        if ($column == 'community_id' && $id == 0) {
            $where = [];
        }
        $where['p.delete_time'] = null;
        $person                 = Db::name("person")->alias("p")
            ->join("sa_building b", "p.building_id=b.id", "INNER")
            ->join("sa_house h", "p.house_id=h.id", "INNER")
            ->field("count(*) value,p.education,'未知' name")
            ->where('p.person_type','<',4)
            ->where('p.household', 0)
            ->where('p.dead', 0)
            ->where($where)->group("p.education")->select()->toArray();
        $edu  = getDictionary(56);
        $data = [];
        $xl   = ['文盲', '学龄前', '小学', '初中', '高中', '职高', '技校', '大专', '大学', '研究生（硕士）', '研究生（博士）'];

        foreach ($xl as $k => $v) {
            foreach ($person as &$item) {
                if ($v == $edu[$item['education']]) {
                    $data['category'][] = $edu[$item['education']];
                    $data['value'][]    = $item['value'];
                }
            }

        }

        return $data;
    }

}
