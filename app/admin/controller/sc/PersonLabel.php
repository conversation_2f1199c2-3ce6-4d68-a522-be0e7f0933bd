<?php
declare (strict_types=1);
// +----------------------------------------------------------------------
// | swiftAdmin 极速开发框架 [基于WebMan开发]
// +----------------------------------------------------------------------
// | Copyright (c) 2020-2030 http://www.swiftadmin.net
// +----------------------------------------------------------------------
// | swiftAdmin.net High Speed Development Framework
// +----------------------------------------------------------------------
// | Author: meystack <<EMAIL>> Apache 2.0 License
// +----------------------------------------------------------------------
namespace app\admin\controller\sc;

use app\AdminController;
use think\facade\Db;

class PersonLabel extends AdminController
{
    protected array $rbac = [];
    public function __construct()
    {
        parent::__construct();
        $this->rbac = [
            'personCommunist'=>['table' => 'personCommunist','name'=>'党员','column'=>[
                '工作时间'=>[1,'work_date'],
                '入党时间'=>[1,'work_date'],
                '党龄'=>[1,'work_date'],
                '党内职务'=>[1,'communist_post'],
                '所属党支部'=>[1,'communist_branch'],
                '现或原单位'=>[1,'old_unit'],
                '职务'=>[1,'post'],
                '级别'=>[1,'level'],
                '技术职称'=>[1,'technical_title'],
                '在岗情况'=>[2,'working']
            ]],
            'personVolunteer'=>['table' => 'personVolunteer','name'=>'志愿者','column'=>[
                '单位'=>[1,'unit'],
                '志愿者协会'=>[1,'association'],
                '备注'=>[1,'details']
            ]],
            'personIncomeLowFamily'=>['table' => 'personIncomeLowFamily','name'=>'低保户','column'=>[
                '户数'=>[1,'household_number'],
                '保障人数'=>[1,'people_number'],
                '月总收入'=>[1,'income_month'],
                '人均月收入'=>[1,'income_avg'],
                '月保障金'=>[1,'income_guarantee_month'],
                '家庭类别'=>[3,'household_type',447],
                '户口'=>[3,'household',453],
                'A/B/C类'=>[3,'type',507],
                '备注'=>[1,'details'],
                '家庭成员1姓名'=>[1,'member_name_1'],
                '家庭成员1身份证号'=>[1,'member_id_code_1'],
                '家庭成员2姓名'=>[1,'member_name_2'],
                '家庭成员2身份证号'=>[1,'member_id_code_2'],
                '家庭成员3姓名'=>[1,'member_name_3'],
                '家庭成员3身份证号'=>[1,'member_id_code_3'],
                '家庭成员4姓名'=>[1,'member_name_4'],
                '家庭成员4身份证号'=>[1,'member_id_code_4']
            ]],
            'personOldest'=>['table' => 'personOldest','name'=>'高龄老人','column'=>[
                '监护人'=>[1,'guardian'],
                '监护人电话'=>[1,'guardian_phone'],
                '备注'=>[1,'details']
            ]],
            'personDisability'=>['table' => 'personDisability','name'=>'残疾人','column'=>[
                '残疾类型'=>[3,'disability_type',411],
                '残疾等级'=>[3,'disability_level',418],
                '残疾证书编号'=>[1,'disability_code'],
                '发证日期'=>[1,'certificate_date'],
                '工作情况'=>[1,'professional'],
                '备注'=>[1,'details'],
            ]],
            'personSoldier'=>['table' => 'personSoldier','name'=>'退伍军人','column'=>[
                '入伍时间'=>[1,'enter_date'],
                '退伍时间'=>[1,'leave_date'],
                '入伍区域'=>[3,'enlistment_area',424],
                '退伍证编号'=>[1,'code'],
                '工作单位'=>[1,'unit'],
                '参战项目'=>[1,'war_zone'],
                '备注'=>[1,'details'],
            ]],
            'personSupport'=>['table' => 'personSupport','name'=>'特扶','column'=>[
                '银行卡号'=>[1,'bank_code'],
                '子女数(子)'=>[1,'boy'],
                '子女数(女)'=>[1,'girl'],
                '子女情况'=>[3,'child_info',428],
                '备注'=>[1,'details'],
            ]],
            'personIllnessMental'=>['table' => 'personIllnessMental','name'=>'精神病人','column'=>[
                '是否有证'=>[2,'has_code'],
                '证书编号'=>[1,'code'],
                '监护人'=>[1,'guardian'],
                '监护人电话'=>[1,'guardian_phone'],
                '服务日期'=>[1,'service_date'],
                '备注'=>[1,'details'],
            ]],
            'personReleaser'=>['table' => 'personReleaser','name'=>'刑满释放人员','column'=>[
                '刑科'=>[1,'section'],
                '刑期'=>[1,'releases'],
                '释放日期'=>[1,'release_date'],
                '安置帮教(开始)'=>[1,'help_start'],
                '安置帮教(截止)'=>[1,'help_end'],
                '备注'=>[1,'details'],
            ]],
            'personCorrector'=>['table' => 'personCorrector','name'=>'社区矫正人员','column'=>[
                '刑科'=>[1,'section'],
                '刑期'=>[1,'correctors'],
                '矫正期限(开始)'=>[1,'term_start'],
                '矫正期限(截止)'=>[1,'term_end'],
                '矫正类型'=>[1,'type'],
                '包保人'=>[1,'guardian'],
                '包保人电话'=>[1,'guardian_phone'],
                '备注'=>[1,'details'],
            ]],
            'personDrugged'=>['table' => 'personDrugged','name'=>'吸毒人员','column'=>[
                '工作单位或就业情况'=>[1,'unit'],
                '包保人'=>[1,'guardian'],
                '包保人电话'=>[1,'guardian_phone'],
                '开始吸毒日期'=>[1,'begin_date'],
                '是否接受治疗'=>[2,'treat'],
                '备注'=>[1,'details'],
            ]],
            'personIllnessAids'=>['table' => 'personIllnessAids','name'=>'艾滋病人','column'=>[
                '确诊日期'=>[1,'diagnose'],
                '工作单位或就业情况'=>[1,'unit'],
                '备注'=>[1,'details'],
            ]],
            'personElderly_1'=>['table' => 'personElderly','name'=>'空巢老人', 'type'=>'1','column'=>[
                '平房/楼房'=>[3,'house_type',519],
                '自有/租赁'=>[3,'lease_type',362],
                '监护人'=>[1,'guardian'],
                '监护人电话'=>[1,'guardian_phone'],
                '与老人关系'=>[3,'guardian_relation',53],
                '监护人身份证号'=>[1,'guardian_id_code'],
                '包保人'=>[1,'underwriter'],
                '包保人电话'=>[1,'underwriter_phone'],
                '包保人职务'=>[1,'post'],
                '探访频次'=>[1,'frequency'],
                '备注'=>[1,'details']
            ]],
            'personElderly_2'=>['table' => 'personElderly','name'=>'孤寡老人', 'type'=>'2','column'=>[
                '平房/楼房'=>[3,'house_type',519],
                '自有/租赁'=>[3,'lease_type',362],
                '监护人'=>[1,'guardian'],
                '监护人电话'=>[1,'guardian_phone'],
                '与老人关系'=>[3,'guardian_relation',53],
                '监护人身份证号'=>[1,'guardian_id_code'],
                '包保人'=>[1,'underwriter'],
                '包保人电话'=>[1,'underwriter_phone'],
                '包保人职务'=>[1,'post'],
                '探访频次'=>[1,'frequency'],
                '备注'=>[1,'details']
            ]],
            'personElderly_3'=>['table' => 'personElderly','name'=>'独居老人', 'type'=>'3','column'=>[
                '平房/楼房'=>[3,'house_type',519],
                '自有/租赁'=>[3,'lease_type',362],
                '监护人'=>[1,'guardian'],
                '监护人电话'=>[1,'guardian_phone'],
                '与老人关系'=>[3,'guardian_relation',53],
                '监护人身份证号'=>[1,'guardian_id_code'],
                '包保人'=>[1,'underwriter'],
                '包保人电话'=>[1,'underwriter_phone'],
                '包保人职务'=>[1,'post'],
                '探访频次'=>[1,'frequency'],
                '备注'=>[1,'details']
            ]],
            'personPetition'=>['table' => 'personPetition','name'=>'重点信访','column'=>[
                '家庭人员基本情况'=>[1,'family_info'],
                '上访诉求'=>[1,'appeal'],
                '风险程度'=>[3,'risk_level',443],
                '备注'=>[1,'details']
            ]],
            'personAffordableHousing'=>['table' => 'personAffordableHousing','name'=>'保障房','column'=>[
                '所属群体'=>[3,'group_0',434],
                '是否低保'=>[2,'income_low'],
                '房屋地址'=>[1,'house_address'],
                '房屋面积'=>[1,'building_area'],
                '出租人'=>[1,'lease_from'],
                '承租人'=>[1,'lease_to'],
                '承租开始'=>[1,'lease_start'],
                '承租结束'=>[1,'lease_end'],
                '月租金'=>[1,'rent_month'],
                '押金'=>[1,'deposit'],
                '合同签订日期'=>[1,'transaction'],
                '家庭成员1'=>[1,'member_name_1'],
                '成员身份证号1'=>[1,'member_id_code_1'],
                '与户主关系1'=>[3,'relation_1',53],
                '民族1'=>[3,'nation_1',54],
                '文化程度1'=>[3,'education_1',56],
                '婚姻状况1'=>[3,'marry_1',64],
                '就业状况1'=>[3,'employment_1',522],
                '所属群体1'=>[3,'group_1',434],
                '联系方式1'=>[1,'phone_1'],
                '家庭成员2'=>[1,'member_name_2'],
                '成员身份证号2'=>[1,'member_id_code_2'],
                '与户主关系2'=>[3,'relation_2',53],
                '民族2'=>[3,'nation_2',54],
                '文化程度2'=>[3,'education_2',56],
                '婚姻状况2'=>[3,'marry_2',64],
                '就业状况2'=>[3,'employment_2',522],
                '所属群体2'=>[3,'group_2',434],
                '联系方式2'=>[1,'phone_2'],
                '家庭成员3'=>[1,'member_name_3'],
                '成员身份证号3'=>[1,'member_id_code_3'],
                '与户主关系3'=>[3,'relation_3',53],
                '民族3'=>[3,'nation_3',54],
                '文化程度3'=>[3,'education_3',56],
                '婚姻状况3'=>[3,'marry_3',64],
                '就业状况3'=>[3,'employment_3',522],
                '所属群体3'=>[3,'group_3',434],
                '联系方式3'=>[1,'phone_3'],
                '家庭成员4'=>[1,'member_name_4'],
                '成员身份证号4'=>[1,'member_id_code_4'],
                '与户主关系4'=>[3,'relation_4',53],
                '民族4'=>[3,'nation_4',54],
                '文化程度4'=>[3,'education_4',56],
                '婚姻状况4'=>[3,'marry_4',64],
                '就业状况4'=>[3,'employment_4',522],
                '所属群体4'=>[3,'group_4',434],
                '联系方式4'=>[1,'phone_4'],
            ]],
            'personIncomeLowPeople'=>['table' => 'personIncomeLowPeople','name'=>'低收入人群','column'=>[
                '家庭人口数'=>[1,'people_number'],
                '月总收入'=>[1,'income_month'],
                '人均月收入'=>[1,'income_avg'],
                '月保障金'=>[1,'income_guarantee_month'],
                '家庭类别'=>[3,'household_type',447],
                '户口'=>[3,'household',453],
                '所在单位'=>[1,'unit'],
                '备注'=>[1,'details'],
                '家庭成员1姓名'=>[1,'member_name_1'],
                '家庭成员1身份证号'=>[1,'member_id_code_1'],
                '家庭成员2姓名'=>[1,'member_name_2'],
                '家庭成员2身份证号'=>[1,'member_id_code_2'],
                '家庭成员3姓名'=>[1,'member_name_3'],
                '家庭成员3身份证号'=>[1,'member_id_code_3'],
                '家庭成员4姓名'=>[1,'member_name_4'],
                '家庭成员4身份证号'=>[1,'member_id_code_4']
            ]],
            'personChildrenOrphan'=>['table' => 'personChildrenOrphan','name'=>'孤儿','column'=>[
                '精神面貌'=>[3,'spiritual_outlook',457],
                '风险等级'=>[3,'risk_level',461],
                '有无监护人'=>[2,'guardian'],
                'guardian'=>[
                    '与监护人同住'=>[2,'guardian_live'],
                    '姓名'=>[1,'guardian_name'],
                    '与儿童关系'=>[1,'guardian_relation'],
                    '联系方式'=>[1,'guardian_phone'],
                ],
                '帮扶情况'=>[1,'help_info'],
                '帮扶金额'=>[1,'help_money'],
                '帮扶人姓名'=>[1,'help_name'],
                '类别'=>[1,'help_type'],
                '联系方式'=>[1,'help_phone'],
                '家庭成员1姓名'=>[1,'member_name_1'],
                '家庭成员1身份证号'=>[1,'member_id_code_1'],
                '家庭成员2姓名'=>[1,'member_name_2'],
                '家庭成员2身份证号'=>[1,'member_id_code_2'],
                '家庭成员3姓名'=>[1,'member_name_3'],
                '家庭成员3身份证号'=>[1,'member_id_code_3'],
                '家庭成员4姓名'=>[1,'member_name_4'],
                '家庭成员4身份证号'=>[1,'member_id_code_4']
            ]],
            'personChildrenStay'=>['table' => 'personChildrenStay','name'=>'留守儿童','column'=>[
                '精神面貌'=>[3,'spiritual_outlook',457],
                '风险等级'=>[3,'risk_level',461],
                '有无监护人'=>[2,'guardian'],
                'guardian'=>[
                    '与监护人同住'=>[2,'guardian_live'],
                    '姓名'=>[1,'guardian_name'],
                    '与儿童关系'=>[1,'guardian_relation'],
                    '联系方式'=>[1,'guardian_phone'],
                ],
                '帮扶情况'=>[1,'help_info'],
                '帮扶金额'=>[1,'help_money'],
                '帮扶人姓名'=>[1,'help_name'],
                '类别'=>[1,'help_type'],
                '联系方式'=>[1,'help_phone'],
                '父亲'=>[1,'father_name'],
                '身份证号(父)'=>[1,'father_id_code'],
                '联系方式(父)'=>[1,'father_phone'],
                '外出务工(父)'=>[2,'father_out_work'],
                'father_out_work'=>[
                    '务工地点(父)'=>[1,'father_out_work_place'],
                ],
                '母亲'=>[1,'mother_name'],
                '身份证号(母)'=>[1,'mother_id_code'],
                '联系方式(母)'=>[1,'mother_phone'],
                '外出务工(母)'=>[2,'mother_out_work'],
                'mother_out_work'=>[
                    '务工地点(母)'=>[1,'mother_out_work_place'],
                ]
            ]],
            'personChildrenDisability'=>['table' => 'personChildrenDisability','name'=>'残疾儿童','column'=>[
                '精神面貌'=>[3,'spiritual_outlook',457],
                '风险等级'=>[3,'risk_level',461],
                '有无监护人'=>[2,'guardian'],
                'guardian'=>[
                    '与监护人同住'=>[2,'guardian_live'],
                    '姓名'=>[1,'guardian_name'],
                    '与儿童关系'=>[1,'guardian_relation'],
                    '联系方式'=>[1,'guardian_phone'],
                ],
                '帮扶情况'=>[1,'help_info'],
                '帮扶金额'=>[1,'help_money'],
                '帮扶人姓名'=>[1,'help_name'],
                '类别'=>[1,'help_type'],
                '联系方式'=>[1,'help_phone'],
                '家庭成员1姓名'=>[1,'member_name_1'],
                '家庭成员1身份证号'=>[1,'member_id_code_1'],
                '家庭成员2姓名'=>[1,'member_name_2'],
                '家庭成员2身份证号'=>[1,'member_id_code_2'],
                '家庭成员3姓名'=>[1,'member_name_3'],
                '家庭成员3身份证号'=>[1,'member_id_code_3'],
                '家庭成员4姓名'=>[1,'member_name_4'],
                '家庭成员4身份证号'=>[1,'member_id_code_4']
            ]],
            'personChildrenSubsistenceAllowance'=>['table' => 'personChildrenSubsistenceAllowance','name'=>'低保儿童','column'=>[
                '精神面貌'=>[3,'spiritual_outlook',457],
                '风险等级'=>[3,'risk_level',461],
                '有无监护人'=>[2,'guardian'],
                'guardian'=>[
                    '与监护人同住'=>[2,'guardian_live'],
                    '姓名'=>[1,'guardian_name'],
                    '与儿童关系'=>[1,'guardian_relation'],
                    '联系方式'=>[1,'guardian_phone'],
                ],
                '帮扶情况'=>[1,'help_info'],
                '帮扶金额'=>[1,'help_money'],
                '帮扶人姓名'=>[1,'help_name'],
                '类别'=>[1,'help_type'],
                '联系方式'=>[1,'help_phone'],
                '家庭成员1姓名'=>[1,'member_name_1'],
                '家庭成员1身份证号'=>[1,'member_id_code_1'],
                '家庭成员2姓名'=>[1,'member_name_2'],
                '家庭成员2身份证号'=>[1,'member_id_code_2'],
                '家庭成员3姓名'=>[1,'member_name_3'],
                '家庭成员3身份证号'=>[1,'member_id_code_3'],
                '家庭成员4姓名'=>[1,'member_name_4'],
                '家庭成员4身份证号'=>[1,'member_id_code_4']
            ]],
            'personChildrenAbandoned'=>['table' => 'personChildrenAbandoned','name'=>'无人抚养儿童','column'=>[
                '精神面貌'=>[3,'spiritual_outlook',457],
                '风险等级'=>[3,'risk_level',461],
                '有无监护人'=>[2,'guardian'],
                'guardian'=>[
                    '与监护人同住'=>[2,'guardian_live'],
                    '姓名'=>[1,'guardian_name'],
                    '与儿童关系'=>[1,'guardian_relation'],
                    '联系方式'=>[1,'guardian_phone'],
                ],
                '帮扶情况'=>[1,'help_info'],
                '帮扶金额'=>[1,'help_money'],
                '帮扶人姓名'=>[1,'help_name'],
                '类别'=>[1,'help_type'],
                '联系方式'=>[1,'help_phone'],
                '家庭成员1姓名'=>[1,'member_name_1'],
                '家庭成员1身份证号'=>[1,'member_id_code_1'],
                '家庭成员2姓名'=>[1,'member_name_2'],
                '家庭成员2身份证号'=>[1,'member_id_code_2'],
                '家庭成员3姓名'=>[1,'member_name_3'],
                '家庭成员3身份证号'=>[1,'member_id_code_3'],
                '家庭成员4姓名'=>[1,'member_name_4'],
                '家庭成员4身份证号'=>[1,'member_id_code_4']
            ]],

        ];
    }

    public function index(){
        $param['type'] = input("type",'community');
        $param['id'] = input('id',0);
        $type = $param['type'];
        if($param['type']=="gridGroup"){
            $type = "grid";
        }
        if($type=="index"){
            $type = "community";
            $title = '特殊人群';
        }else{
            $data = Db::name($type)->where("id",$param['id'])->find();
            $title = $data[$type.'_name'];
        }
        $table = input("table","personCommunist");
        
        $this->url=url("sc/".$param['type'],['id'=>$param['id']]);
        $total = $this->getTsrqTotal($param['id'],$type."_id");
        
        return $this->view('',['param'=>$param,'url'=>$this->url,'title'=>$title."概况","table"=>$table,'rbac'=>$this->rbac,'total'=>$total]);
    }

    public function load(){
        $community_id = input('community_id',0);
        $grid_group_id = input('grid_group_id',0);
        $grid_id = input('grid_id',0);
        $table = camelCaseToSnakeCase(input('table', 'personCommunist'));
        $type = 'community_id';
        $id = $community_id;
        if($grid_id){
            $type = 'grid_id';
            $id = $grid_id;
        }else if($grid_group_id){
            $type = 'grid_group_id';
            $id = $grid_group_id;
        }
        $total = $this->getTsrqTotal($id,$type);
        

        $alias = "a";
        $where = $this->buildQueryParams($table,$alias.".",false);
        
        $total['community_data'] = [];
        $community_data = [];
        $community_res = Db::name('community')->select()->toArray();
        foreach($community_res as $k=>$v){
            if($v['id']!=0){
                $where['a.community_id'] = $v['id'];
            }
            
            $count_res = Db::name($table)
            ->alias($alias)
            // ->join('sa_person p', 'a.person_id=p.id and (p.delete_time is null and p.dead=0 or (p.delete_flag=1 and p.grid_id <> a.grid_id))')
            ->join('sa_person p', 'a.person_id=p.id and (p.delete_time is null and p.dead=0)')
            ->where($where)
            ->fetchSql(false)
            ->count();

            $community_data[] = [
                "community_id" => $v['id'],
                "community_name" => $v['community_name'],
                "person_count" => $count_res,
            ];
        }

        $community_data = $community_data;
        $total['community_data'] = $community_data;

        return $this->success("","/",$total);
    }
    
    protected function buildQueryParams($table,$alias="",$allow=true): array
    {
        $where = [];
        $params = request()->all();
        if (!empty($params) && is_array($params)) {

            $tableFields = DB::name($table)->getFields();
            $old_alias = $alias;
            // $admin_info = get_admin_info();
            // $adminAccess = getAdminAccess($admin_info["id"]);
            // $adminGroup = explode(',', $adminAccess['group_id']);
            // 如果是超级管理员、街道管理员、社区管理员
            // if($allow){
            //     if(in_array(1, $adminGroup)
            //         ||in_array(2, $adminGroup)
            //         ||in_array(3, $adminGroup)){
            //         if(array_key_exists("community_id", $this->tableFields)){
            //             if($admin_info['community_id']>0&&empty($params['community_id'])){
            //                 $where[$alias.'community_id'] = $admin_info['community_id'];
            //                 if($admin_info['grid_group_id']>0&&empty($params['grid_group_id'])){
            //                     $where[$alias.'grid_group_id'] = $admin_info['grid_group_id'];
            //                     if($admin_info['grid_id']>0&&empty($params['grid_id'])){
            //                         $where[$alias.'grid_id'] = $admin_info['grid_id'];
            //                     }
            //                 }
            //             }
            //         }
            //     }else if(in_array(5, $adminGroup)){
            //         //网格组管理员
            //         $where[$alias.'community_id'] = $admin_info['community_id'];
            //         $where[$alias.'grid_group_id'] = $admin_info['grid_group_id'];
            //         if($admin_info['grid_id']>0&&empty($params['grid_id'])){
            //             $where[$alias.'grid_id'] = $admin_info['grid_id'];
            //         }
            //     }else if (in_array(6, $adminGroup)){
            //         //网格管理员
            //         $where[$alias.'community_id'] = $admin_info['community_id'];
            //         $where[$alias.'grid_group_id'] = $admin_info['grid_group_id'];
            //         $where[$alias.'grid_id'] = $admin_info['grid_id'];
            //     }
            // }

            foreach ($params as $field => $value) {

                if(strpos($field, '__') !== false){
                    $arr = explode('__',$field);
                    $alias = $arr[0].".";
                    $field = $arr[1];
                }else{
                    $alias = $old_alias;
                }
                if(empty($value)){
                    continue;
                }
                if($field=='id'){
                    $where[]=[$alias.'id','in',implode(",",$value)];
                    continue;
                }
                // 过滤字段
                if (in_array($field, $this->filterWhere)) {
                    continue;
                }
                // 非表内字段
                if (!array_key_exists($field, $tableFields)) {
                    if($old_alias != $alias){
                        $where[] = [$alias.$field, '=', intval($value )];
                    }
                    continue;
                }

                // 默认状态字段
                if ($field == $this->keepField && $value) {
                    $where[] = [$alias.$field, '=', intval($value - 1)];
                    continue;
                }

                // 获取类型
                $type = $tableFields[$field]['type'];
                $type = explode('(', $type)[0];
                $value = str_replace('/\s+/', '', $value);
                switch ($type) {
                    case 'char':
                    case 'text':
                    case 'varchar':
                    case 'tinytext':
                    case 'longtext':
                        $where[] = [$alias.$field, 'like', '%' . $value . '%'];
                        break;
                    case 'int':
                    case 'bigint':
                    case 'integer':
                    case 'tinyint':
                    case 'smallint':
                    case 'mediumint':
                    case 'float':
                    case 'double':
                    case 'timestamp':
                    case 'year':
                        $value = str_replace(',', '-', $value);
                        if (strpos($value, '-')) {

                            $arr = explode(' - ', $value);
                            if (empty($arr)) {
                                continue 2;
                            }
                            if (in_array($field, $this->converTime)) {
                                if (isset($arr[0])) {
                                    $arr[0] = strtotime($arr[0]);
                                }
                                if (isset($arr[1])) {
                                    $arr[1] = strtotime($arr[1]);
                                }
                            }
                            $exp = 'between';
                            if ($arr[0] === '') {
                                $exp = '<=';
                                $arr = $arr[1];
                            } elseif ($arr[1] === '') {
                                $exp = '>=';
                                $arr = $arr[0];
                            }
                            $where[] = [$alias.$field, $exp, $arr];
                        } else {
                            $where[] = [$alias.$field, '=', $value];
                        }
                        break;
                    case 'set';
                        $where[] = [$alias.$field, 'find in set', $value];
                        break;
                    case 'enum';
                        $where[] = [$alias.$field, '=', $value];
                        break;
                    case 'date';
                    case 'time';
                    case 'datetime';
                        $value = str_replace(',', '-', $value);
                        if (strpos($value, '-')) {
                            $arr = explode(' - ', $value);
                            if (!array_filter($arr)) {
                                continue 2;
                            }
                            $exp = 'between';
                            if ($arr[0] === '') {
                                $exp = '<=';
                                $arr = $arr[1];
                            } elseif ($arr[1] === '') {
                                $exp = '>=';
                                $arr = $arr[0];
                            }
                            $where[] = [$alias.$field, $exp, $arr];
                        } else {
                            $where[] = [$alias.$field, '=', $value];
                        }
                        break;
                    case 'blob';
                        break;
                    default:
                        // 默认值
                        break;
                }
            }
        }

        return $where;
    }

    public function getTsrqTotal($id,$column){
        $rbac = getRbac();
        $alias = "t";
        $where[$alias.".".$column] = $id;
        if($column=='community_id'&&$id==0){
            $where=[];
        }
        // $where['p.delete_time'] = null;
        $data = ['total'=>[]];
        
        
        foreach($rbac as $k=>$v){
            if($v['name'] == '重点信访'){
                $rbac[$k]['cate_id'] = '74';
            }
            if($v['name'] == '残疾人'){
                $rbac[$k]['cate_id'] = '73';
            }
            
        }
        // return $rbac;
        foreach ($rbac as $item){
            $itemwhere = "";
            if(array_key_exists('where',$item)){
                $itemwhere = $item['where'];
            }
            $count = Db::name("person_".$item['table'])->alias($alias)
                // ->join("sa_person p",$alias.".person_id=p.id and (p.delete_time is null or (p.delete_flag=1 and p.grid_id <> $alias.grid_id)) and dead = 0")
                ->join("sa_person p",$alias.".person_id=p.id and (p.delete_time is null) and dead = 0")
                ->where($where)->where($itemwhere)->count();
                
            // if(){
                
            // }
            $data[$item['cate_id']]['name'][]  = $item['name'];
            $data[$item['cate_id']]['value'][] = $count;
            $data['total']['name'][] = $item['name'];
            $data['total']['value'][] = $count;
        }
        return $data;
    }

    public function getTsrqTotal2($id,$column){
        $dictionaryGroup =getDictionaryGroup([70,71,72,73,74,75]);
        $where["p.".$column] = $id;
        if($column=='community_id'&&$id==0){
            $where=[];
        }
        $where['p.delete_time'] = null;
        $data = ['total'=>[]];
        $value = Db::name("person")->alias("p")->where($where)->where("face",1)->count();
        $data['total']['name'][]='党员';
        $data['total']['value'][]=$value;
        $data[75][]=['name'=>"党员",'value'=>$value];
        $value = Db::name("person")->alias("p")->where($where)->where("disability",1)->count();
        $data['total']['name'][]='残疾人';
        $data['total']['value'][]=$value;
        $data[75][]=['name'=>"残疾人",'value'=>$value];
        foreach ($dictionaryGroup as $index => $groupList){
            $total=0;
            foreach ($groupList as $key => $vo){
                $key = camelCaseToUnderscore($key);
                $where[$key] = "1";
                $value = Db::name("person")->alias("p")->where($where)->count();
                $total+=$value;
                $vo = mb_substr($vo, 0, 4, "utf8");
                $data[$index][]=['name'=>$vo,'value'=>$value];
                $data['total']['name'][]=$vo;
                $data['total']['value'][]=$value;
                unset($where[$key]);
            }
        }
        return $data;
    }

    public function detail(){
        $table = input("table","personCommunist");
        $id = input("id");
        $type = input("type");
        $data = Db::name($table)->where("id",$id)->find();
        $person = getPerson('',$data['person_id']);
        if($type){
            $table.="_".$type;
        }
        $column = $this->rbac[$table]['column'];
        $arr = [];
        foreach($column as $key=>$item){
            if(array_key_exists(0,$item)){
                $arr[$key]=$this->dataFormat($data,$item);
            }else if($data[$key]==1){
                foreach ($item as $k=>$vo){
                    $arr[$k]=$this->dataFormat($data,$vo);
                }
            }
        }
        return $this->view('',['person'=>$person,'data'=>$arr,'title'=>$this->rbac[$table]['name']]);
    }

    public function dataFormat($data,$value){
        $formatValue="";
        switch ($value[0]){
            case 1:
                $formatValue = $data[$value[1]];
                if (str_contains($value[1], 'phone')) {
                    $formatValue = hide_str($formatValue,3,4);
                }else if (str_contains($value[1], 'id_code')) {
                    $formatValue = hide_str($formatValue,6,8);
                }else if (str_contains($value[1], 'name')) {
                    $formatValue = hide_str($formatValue,3,4);
                }
                break;
            case 2:
                $formatValue = $data[$value[1]]==1?'是':'否';
                break;
            case 3:
                $dictionary = getDictionary($value[2]);
                $formatValue = $dictionary[$data[$value[1]]];
                break;
        }
        if($formatValue=='0000-00-00'){
            $formatValue='';
        }
        return $formatValue;
    }

}

