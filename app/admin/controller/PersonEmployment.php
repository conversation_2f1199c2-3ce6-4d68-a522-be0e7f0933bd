<?php
declare (strict_types=1);

namespace app\admin\controller;

use app\AdminController;
use app\common\model\PersonEmployment as PersonEmploymentModel;
use app\common\model\Person;

/**
 * person
 * 实有人口管理
 * <!---->
 * Class Person
 * @package app\admin\controller
 */
class PersonEmployment extends AdminController
{

    public function __construct()
    {
        parent::__construct();
        $this->model = new PersonEmploymentModel;
    }

    public function index()
    {
        if (request()->isAjax()||request()->isPost()) {
            $alias = "e";
            $where = $this->buildSelectParams("$alias.");
            return $this->getList($alias,$where);
        }
        return $this->view();
    }

    public function employment(){
        if (request()->isAjax()) {
            $alias = "e";
            $where = $this->buildSelectParams("$alias.");
            $where["$alias.type"]=1;
            return $this->getList($alias,$where);
        }
        return $this->view();
    }

    public function unemployment(){
        if (request()->isAjax()) {
            $alias = "e";
            $where = $this->buildSelectParams("$alias.");
            $where["$alias.type"]=2;
            return $this->getList($alias,$where);
        }
        return $this->view();
    }

    public function notEmployment(){
        if (request()->isAjax()) {
            $alias = "e";
            $where = $this->buildSelectParams("$alias.");
            $where["$alias.type"]=3;
            return $this->getList($alias,$where);
        }
        return $this->view();
    }

    public function noEmployment(){
        if (request()->isAjax()) {
            $alias = "e";
            $where = $this->buildSelectParams("$alias.");
            $where[]= ["$alias.type", "not in", "1,2,3"];
            return $this->getList($alias,$where);
        }
        return $this->view();
    }

    public function ledger(){
        return $this->view();
    }

    public function excel(){
        return $this->view();
    }

    public function laborExcel(){
        $alias = "e";
        $list = $this->model->alias($alias)
            ->join("sa_person p","$alias.person_id=p.id")
            ->field("p.name,p.sex,p.id_code,p.age,p.education,p.nation,p.address,e.type,e.employment_form,p.phone")
            ->where("e.type between 1 and 2")->select()->toArray();
        $this->columnFormat=[
            'A' => [0, 'index'],
            'B' => [1, 'name'],
            'C' => [6, 'gender'],
            'D' => [4, 'id_code'],
            'E' => [6, 'age'],
            'F' => [3, 'education', 56],
            'G' => [3, 'nation', 54],
            'H' => [1, 'address'],
            'I' => [3, 'type',440],//从业状态
            'J' => [3, 'employment_form', 77],//就业形式
            'K' => [4, 'phone']
        ];
        $this->tpl_name = "充分就业-劳动力资源台账";
        $this->row_index = 4;
        $filePath = $this->excelExport($list);
        return $this->redirect($filePath);
    }

    public function newEmpPersonExcel(){
        $alias = "e";
        $year = date("Y");
        $list = $this->model->alias($alias)->field($alias.'.id')
            ->join("sa_person p","$alias.person_id=p.id")
            ->field("p.name,p.sex,p.id_code,p.age,p.education,p.nation,p.address,e.type,e.employment_form,p.phone,
            e.employment_date,e.unemployment_reemployment,e.employment_difficult_reemployment,e.skill_level_certificate,e.employment_unit")
            ->where("e.type = 1 and year(employment_date)=$year ")->select()->toArray();
        $this->columnFormat=[
            'A' => [0, 'index'],
            'B' => [0, '建三江'],
            'C' => [1, 'name'],
            'D' => [6, 'gender'],
            'E' => [4, 'id_code'],
            'F' => [4, 'phone'],
            'G' => [3, 'education', 56],
            'H' => [1, 'employment_date'],
            'I' => [1, 'employment_unit'],
            'J' => [3, 'employment_form', 77],//就业形式
            'K' => [2, 'employment_difficult_reemployment'],//就业困难再就业
            'L' => [2, 'unemployment_reemployment'],//失业人员再就业
            'M' => [1, 'skill_level_certificate'],//技能等级证书名称
        ];
        $this->tpl_name = "充分就业-新就业人员实名制台账";
        $this->row_index = 4;
        $filePath = $this->excelExport($list);
        return $this->redirect($filePath);
    }

    public function employmentDifficultExcel(){
        $alias = "e";
        $year = date("Y");
        $list = $this->model->alias($alias)->field($alias.'.id')
            ->join("sa_person p","$alias.person_id=p.id")
            ->field("p.name,p.sex,p.id_code,p.age,p.education,p.nation,p.address,e.type,e.employment_form,p.phone,
            e.employment_date,e.unemployment_reemployment,e.employment_difficult_reemployment,e.skill_level_certificate,
            e.unemployment_difficult_type,e.unemployment_unit")
            ->where("(e.type = 1 and e.employment_difficult_reemployment = 1) or (e.type = 2 and e.unemployment_difficult=1)")->select()->toArray();
        $this->columnFormat=[
            'A' => [0, 'index'],
            'B' => [1, 'name'],
            'C' => [6, 'gender'],
            'D' => [6, 'age'],
            'E' => [4, 'id_code'],
            'F' => [3, 'education', 56],
            'G' => [3, 'unemployment_difficult_type',489],//就业困难人员类别
            'H' => [1, 'unemployment_unit'],
            'I' => [1, 'address'],
            'J' => [4, 'phone'],
            'K' => [1, 'employment_date','date'=>1],
            'L' => [3, 'employment_form', 77],//就业形式
        ];
        $this->tpl_name = "充分就业-就业困难人员管理台帐";
        $this->row_index = 5;
        $filePath = $this->excelExport($list);
        return $this->redirect($filePath);
    }
    
    //失业人员管理台账导出
    public function unemploymentExcel(){
        $alias = "e";
        $year = date("Y");
        $list = $this->model->alias($alias)->field($alias.'.id')
            ->join("sa_person p","$alias.person_id=p.id")
            ->field("p.name,p.sex,p.id_code,p.age,p.education,p.nation,p.address,e.type,e.employment_form,p.phone,
            e.employment_date,e.unemployment_reemployment,e.employment_difficult_reemployment,e.skill_level_certificate,
            e.unemployment_difficult_type,e.unemployment_unit,e.employment_idea,e.accept_employment_service")
            ->where("e.type = 2")->select()->toArray();
        $this->columnFormat=[
            'A' => [0, 'index'],
            'B' => [1, 'name'],
            'C' => [4, 'id_code'],
            'D' => [6, 'gender'],
            'E' => [6, 'age'],
            'F' => [3, 'education', 56],
            'G' => [1, 'unemployment_unit'],
            'H' => [3, 'employment_idea', 465],//拟求职就业意向
            'I' => [3, 'accept_employment_service', 471],//拟接受公共就业人才服务内容
            'J' => [4, 'phone'],
            'K' => [1, 'lost_employment_date','date'=>1],//失业时间
            // 'L' => [3, 'unemployment_cause', 478],//失业登记原因
            'L' => [3, 'unemployment_difficult_type',489],//就业困难人员类别
            'M' => [2, 'lost_employment_subsidy'],//申领失业保险金
        ];
        $this->tpl_name = "充分就业-失业人员管理台账";
        $this->row_index = 4;
        $filePath = $this->excelExport($list);
        return $this->redirect($filePath);
    }

    public function not_employmentExcel(){
        $alias = "e";
        $list = $this->model->alias($alias)->field($alias.'.id')
            ->join("sa_person p","$alias.person_id=p.id")
            ->field("p.name,p.sex,p.id_code,p.age,p.education,p.nation,p.address,p.school,p.graduation_date,
            p.major,e.type,e.employment_form,p.phone,e.employment_date,e.unemployment_reemployment,
            e.employment_difficult_reemployment,e.skill_level_certificate,
            e.unemployment_difficult_type,e.unemployment_unit,e.unemployment_cause")
            ->where("e.type = 2 and p.education > 8 and e.unemployment_cause = 1")->select()->toArray();
        $this->columnFormat=[
            'A' => [0, 'index'],
            'B' => [1, 'name'],
            'C' => [6, 'gender'],
            'D' => [3, 'nation', 54],
            'E' => [4, 'id_code'],
            'F' => [1, 'school'],
            'G' => [1, 'graduation_date','date'=>1],
            'H' => [3, 'education', 56],
            'I' => [1, 'major'],
            'J' => [3, 'unemployment_cause', 478],//失业登记原因
            'K' => [4, 'phone']
        ];
        $this->tpl_name = "充分就业-未就业高校毕业生信息台账";
        $this->row_index = 4;
        $filePath = $this->excelExport($list);
        return $this->redirect($filePath);
    }

    public function emploaymentFamilyExcel(){
        $alias = "e";
        $list = $this->model->alias($alias)->field($alias.'.id')
            ->join("sa_person p","$alias.person_id=p.id")
            ->field("p.name,p.sex,p.id_code,p.age,p.education,p.nation,p.address,p.school,p.graduation_date,
            p.major,e.type,e.employment_form,p.phone,e.employment_date,e.unemployment_reemployment,
            e.employment_difficult_reemployment,e.skill_level_certificate,
            e.unemployment_difficult_type,e.employment_unit,e.certificate_code,p.relation")
            ->where("e.employment_difficult_type = 2")->select()->toArray();
        $this->columnFormat=[
            'A' => [0, 'index'],
            'B' => [0, '建三江'],
            'C' => [1, 'name'],
            'D' => [6, 'gender'],
            'E' => [6, 'age'],
            'F' => [4, 'id_code'],
            'G' => [3, 'education', 56],
            'H' => [1, 'certificate_code'],
            'I' => [3, 'employment_form', 77],//就业形式
            'J' => [1, 'employment_unit'],//就业单位
            'K' => [1, 'employment_date','date'=>1],//就业时间
            'L' => [3, 'relation', 53],
            'M' => [1, 'address'],
            'N' => [4, 'phone']
        ];
        $this->tpl_name = "充分就业-零就业家庭具体情况台账";
        $this->row_index = 5;
        $filePath = $this->excelExport($list);
        return $this->redirect($filePath);
    }

    protected function getList($alias,$where){
        $page = (int)input('page', 1);
        $limit = (int)input('limit', 18);
        $keyword = input('keyword','');
        if($keyword){
            $where[] = ["p.name|p.id_code|p.phone|p.address", 'like', '%' . $keyword . '%'];
        }
        $count = $this->model->alias($alias)
            ->join("sa_person p","$alias.person_id=p.id")
            ->join("sa_building b", "p.building_id=b.id")
            ->join("sa_house h","p.house_id=h.id")
            ->where($where)->count();

        //$page = $count <= $limit ? 1 : $page;
        $fieldList = $this->model->getFields();
        $order = !array_key_exists('sort', $fieldList) ? $alias.'.id' : $alias.'.sort';
        $order =  "p.community_id,p.grid_group_id,p.grid_id,b.sort,h.unit,h.floor,h.sort,p.relation,".$order;
        $subQuery = $this->model->alias($alias)->field($alias.'.id')
            ->join("sa_person p","$alias.person_id=p.id")
            ->join("sa_community c",  "p.community_id=c.id", "LEFT")
            ->join("sa_grid g",  "p.grid_id=g.id", "LEFT")
            ->join("sa_grid gg", "p.grid_group_id=gg.id", "LEFT")
            ->join("sa_building b", "p.building_id=b.id")
            ->join("sa_house h","p.house_id=h.id")
            ->where($where)->order($order, 'desc')->limit($limit)->page($page)->buildSql();
        $subQuery = '( SELECT object.id FROM ' . $subQuery . ' AS object )';
        $list = $this->model->alias($alias)
            ->join("sa_person p","$alias.person_id=p.id")
            ->join("sa_community c",  "p.community_id=c.id", "LEFT")
            ->join("sa_grid g",  "p.grid_id=g.id", "LEFT")
            ->join("sa_grid gg", "p.grid_group_id=gg.id", "LEFT")
            ->join("sa_building b", "p.building_id=b.id")
            ->join("sa_house h","p.house_id=h.id")
            ->field("$alias.id,$alias.type,$alias.person_id,p.community_id,p.name,p.id_code,p.sex,p.age,p.education,p.face,p.nation,p.health,p.labor_capacity,p.person_type,
                g.grid_name,gg.grid_name grid_group_name,c.community_name,b.building_name")
            ->where($alias . '.id in' . $subQuery)->order($order)->select()->toArray();
        return $this->success('查询成功', '/', $list, $count);
    }

}
