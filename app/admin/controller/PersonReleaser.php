<?php
declare (strict_types = 1);
namespace app\admin\controller;

use app\AdminController;
use support\Response;
use think\helper\Str;
use Webman\Http\Request;
use app\common\model\PersonReleaser as PersonReleaserModel;
use app\admin\model\Person;
use think\facade\Db;

/**
 * person_vehicle
 * 车辆管理
 * <!---->
 * Class PersonVehicle
 * @package app\admin\controller
 */
class PersonReleaser extends AdminController
{
    /**
     * PersonVehicle模型对象
     * @var \app\common\model\PersonReleaser
     */

    public function __construct()
    {
        parent::__construct();
        $this->model = new PersonReleaserModel;
        $this->columnFormat=[
            'A' => [0, 'index'],
            'B' => [1, 'community_name','column'=>'community_id'],
            'C' => [1, 'grid_group_name','column'=>'grid_group_id'],
            'D' => [1, 'grid_name','column'=>'grid_id'],
            'E' => [3, 'jurisdiction',501,'required'=>1],
            'F' => [1, 'address'],
            'G' => [1,'name'],
            'H' => [6, 'gender'],
            'I' => [6, 'age'],
            'J' => [4, 'id_code'],
            'K' => [4, 'phone'],
            'L' => [1, 'section'],
            'M' => [1, 'releases'],
            'N' => [1, 'release_date','date'=>1],
            'O' => [1, 'help_start','date'=>1],
            'P' => [1, 'help_end','date'=>1],
            'Q' => [1, 'details'],
            'R' => [1,'wechat_code'],
            'S' => [1,'grid_user'],
            'T' => [1,'grid_phone'],
        ];
        $this->tpl_name = "刑满释放模版";
        $this->table_name = 'sa_person_releaser_imp';
        $this->table_source = "sa_person_releaser";
        $this->pk = "person_id";
        $this->row_index = 3;
    }

    /**
     * 添加资源
     * @return Response|void
     */
    public function add()
    {
        if (request()->isPost()) {

            $post = $this->preRuleOutFields(\request()->post());
            if ($this->dataLimit) {
                $post[$this->dataLimitField] = get_admin_id();
            }

            $validate = $this->isValidate ? get_class($this->model) : $this->isValidate;
            $post = request_validate_rules($post, $validate, $this->scene);
            if (empty($post) || !is_array($post)) {
                return $this->error($post);
            }
            if(empty($post['person_id'])){
                return $this->error('请调取人口数据！');
            }
            $post = dataArrayToJson($post);
            if(!empty($this->model->create_by)){
                $post[$this->model->create_by]=$this->admin_info['name'];
            }
            if(!empty($this->model->createTime)){
                $post[$this->model->createTime]=date('Y-m-d H:i:s');
            }
            $this->status = $this->model->create($post);
            $Person = new Person();
            $Person->rbacReverse($post[$this->pk],$this->table_source);
            return $this->status ? $this->success() : $this->error();
        }
        $data = $this->getTableFields();
        $Communities = Db::name("community")->select()->toArray();
        $grid_groups = Db::name("grid")->where(["community_id"=>$data['community_id'],'grid_group'=>0])->select()->toArray();
        $grids = Db::name("grid")->where(["grid_group"=>$data['grid_group_id']])->select()->toArray();
        return $this->view('', ['data' => $data,
            'Communities'=>$Communities,
            'grid_groups'=>$grid_groups,
            'grids'=>$grids
        ]);
    }

    /**
     * 编辑资源
     * @return Response|void
     */
    public function edit()
    {
        $id = input('id');
        $data = $this->model->where('id', $id)->findOrEmpty()->toArray();

        // 限制数据调用
        if (!$this->authService->SuperAdmin() && $this->dataLimit
            && in_array($this->dataLimitField, $this->model->getFields())) {
            if ($data[$this->dataLimitField] != get_admin_id()) {
                return $this->error('没有权限');
            }
        }

        if (request()->isPost()) {
            $post = $this->preRuleOutFields(\request()->post());
            $validate = $this->isValidate ? get_class($this->model) : $this->isValidate;
            $post = request_validate_rules($post, $validate, $this->scene);
            if (empty($post) || !is_array($post)) {
                return $this->error($post);
            }
            if(empty($post['person_id'])){
                return $this->error('请调取人口数据！');
            }
            $post = dataArrayToJson($post);
            if(!empty($this->model->update_by)){
                $post[$this->model->update_by]=$this->admin_info['name'];
            }
            $this->status = $this->model->update($post);
            $Person = new Person();
            $Person->rbacReverse($post[$this->pk],$this->table_source);
            return $this->status ? $this->success() : $this->error();
        }

        /**
         * 默认共享模板
         */
        $template = str_replace('/_', '/', Str::snake(request()->getController()));
        $Communities = Db::name("community")->select()->toArray();
        $grid_groups = Db::name("grid")->where(["community_id"=>$data['community_id'],'grid_group'=>0])->select()->toArray();
        $grids = Db::name("grid")->where(["grid_group"=>$data['grid_group_id']])->select()->toArray();
        return $this->view($template . '/add', [
            'data' => $data,
            'Communities'=>$Communities,
            'grid_groups'=>$grid_groups,
            'grids'=>$grids
        ]);
    }

    /**
     * index 函数
     */
    public function index()
    {
        if (request()->isAjax()||request()->isPost()) {
            $page = (int)input('page', 1);
            $limit = (int)input('limit', 18);
            $alias = "e";
            $this->filterWhere = ['page', 'limit', 'help_start', 'help_end'];
            $where = $this->buildSelectParams($alias.".");
            $keyword = input('keyword','');
            $help_start = input("help_start");
            $help_end = input("help_end");
            if($keyword){
                $where[] = ["p.name|"."p.id_code", 'like', '%' . $keyword . '%'];
            }
            if($help_start||$help_end){
                if($help_start&&$help_end){
                    $where[] = [$alias.".help_end", "between", [$help_start,$help_end] ];
                }else{
                    if($help_start){
                        $where[] = [$alias.".help_end", ">=", $help_start ];
                    }else{
                        $where[] = [$alias.".help_end", "<=", $help_end ];
                    }
                }
            }
            $count = $this->model->alias($alias)
                // ->join("sa_person p",$alias.".person_id=p.id and (p.delete_time is null or (p.delete_flag=1 and p.grid_id <> $alias.grid_id)) and dead = 0")
                ->join("sa_person p",$alias.".person_id=p.id and (p.delete_time is null) and dead = 0")
                ->where($where)->count();
            //$page = $count <= $limit ? 1 : $page;
            $fieldList = $this->model->getFields();
            $order = !array_key_exists('sort', $fieldList) ? $alias.'.id' : $alias.'.sort';
            $order = "$alias.community_id,$alias.grid_group_id,$alias.grid_id,".$order;
            $subQuery = $this->model->alias($alias)
                ->join("sa_person p",$alias.".person_id=p.id")
                ->field($alias.'.id')->where($where)->order($order)->limit($limit)->page($page)->buildSql();
            $subQuery = '( SELECT object.id FROM ' . $subQuery . ' AS object )';
            $list = $this->model->alias($alias)
                ->join("sa_community c",$alias.".community_id=c.id","LEFT")
                ->join("sa_grid g",$alias.".grid_id=g.id","LEFT")
                ->join("sa_grid gg",$alias.".grid_group_id=gg.id","LEFT")
                // ->join("sa_person p",$alias.".person_id=p.id and (p.delete_time is null or (p.delete_flag=1 and p.grid_id <> $alias.grid_id)) and dead = 0")
                ->join("sa_person p",$alias.".person_id=p.id and (p.delete_time is null) and dead = 0")
                ->join("sa_building b", "p.building_id=b.id","LEFT")
                ->field($alias.".*,g.grid_name,gg.grid_name grid_group_name,c.community_name,
                p.name,p.id_code,p.age,p.sex,p.address,p.face,p.nation,p.education,p.school,p.major,p.hometown,p.phone,b.building_name")
                ->where($alias.'.id in' . $subQuery)->order($order)->select()->toArray();
            return $this->success($this->model->getLastSql(), '/', $list, $count);
        }else{
            $this->noRepeat();
        }
        return $this->view();
    }

    public function dataToExcel()
    {
        $alias = "h";
        $this->filterWhere = ['page', 'limit', 'help_start', 'help_end'];
        $where = $this->buildSelectParams($alias.".");
        $keyword = input('keyword','');
        $help_start = input("help_start");
        $help_end = input("help_end");
        if($keyword){
            $where[] = ["p.name|"."p.id_code", 'like', '%' . $keyword . '%'];
        }
        if($help_start||$help_end){
            if($help_start&&$help_end){
                $where[] = [$alias.".help_end", "between", [$help_start,$help_end] ];
            }else{
                if($help_start){
                    $where[] = [$alias.".help_end", ">=", $help_start ];
                }else{
                    $where[] = [$alias.".help_end", "<=", $help_end ];
                }
            }
        }
        $fieldList = $this->model->getFields();
        $order = !array_key_exists('sort', $fieldList) ? $alias.'.id' : $alias.'.sort';
        $order = "$alias.community_id,$alias.grid_group_id,$alias.grid_id,".$order;
        $list = $this->model->alias($alias)
            ->join("sa_person p", $alias.".person_id=p.id and p.delete_time is null and dead = 0")
            ->join("sa_community c",$alias.".community_id=c.id","LEFT")
            ->join("sa_grid g",$alias.".grid_id=g.id","LEFT")
            ->join("sa_grid gg",$alias.".grid_group_id=gg.id","LEFT")
            ->field("$alias.*,
            g.grid_name,gg.grid_name grid_group_name,c.community_name,
            p.name,p.id_code,p.birth,p.sex,p.age,p.nation,p.education,p.school,p.major,p.hometown,p.address,p.phone,p.wechat_code,p.health")
            ->where($where)->order($order)->select()->toArray();
        $filePath = $this->excelExport($list);
        if(request()->isPost()){
            return $this->success('',$filePath);
        }
        return $this->redirect($filePath);
    }

}
