<?php
declare (strict_types = 1);
namespace app\admin\controller;

use app\AdminController;
use app\common\model\ScFileLog;
use support\Response;
use think\facade\Db;
use think\helper\Str;
use Webman\Http\Request;
use app\common\model\ScFile as ScFileModel;

/**
 * sc_file
 * 文件发布
 * <!---->
 * Class ScFile
 * @package app\admin\controller
 */
class ScFile extends AdminController
{
    /**
     * ScFile模型对象
     * @var \app\common\model\ScFile
     */

    public function __construct()
    {
        parent::__construct();
        $this->model = new ScFileModel;
    }

    // 文件发布-新增
    public function add()
    {
        if (request()->isPost()) {

            $post = $this->preRuleOutFields(\request()->post());
            if ($this->dataLimit) {
                $post[$this->dataLimitField] = get_admin_id();
            }

            $validate = $this->isValidate ? get_class($this->model) : $this->isValidate;
            $post = request_validate_rules($post, $validate, $this->scene);
            if (empty($post) || !is_array($post)) {
                return $this->error($post);
            }
            $post = dataArrayToJson($post);
            $post['add_month']=substr($post['add_time'], 0, 7);
            
            $post['community_id']=$this->admin_info['community_id'];
            if(!empty($this->model->create_by)){
                $post[$this->model->create_by]=$this->admin_info['name'];
            }
            $this->status = $this->model->create($post);
            return $this->status ? $this->success() : $this->error();
        }

        return $this->view('', ['data' => $this->getTableFields()]);
    }

    /**
     * 文件发布-编辑
     * @return Response|void
     */
    public function edit()
    {
        $id = input('id');
        $data = $this->model->where('id', $id)->findOrEmpty()->toArray();

        // 限制数据调用
        if (!$this->authService->SuperAdmin() && $this->dataLimit
            && in_array($this->dataLimitField, $this->model->getFields())) {
            if ($data[$this->dataLimitField] != get_admin_id()) {
                return $this->error('没有权限');
            }
        }

        if (request()->isPost()) {
            $post = $this->preRuleOutFields(\request()->post());
            $validate = $this->isValidate ? get_class($this->model) : $this->isValidate;
            $post = request_validate_rules($post, $validate, $this->scene);
            if (empty($post) || !is_array($post)) {
                return $this->error($post);
            }
            $post = dataArrayToJson($post);
            $post['add_month']=substr($post['add_time'], 0, 7);
            if(!empty($this->model->update_by)){
                $post[$this->model->update_by]=$this->admin_info['name'];
            }
            $this->status = $this->model->update($post);
            return $this->status ? $this->success() : $this->error();
        }

        /**
         * 默认共享模板
         */
        $template = str_replace('/_', '/', Str::snake(request()->getController()));
        return $this->view($template . '/add', [
            'data' => $data
        ]);
    }

    // 文件派发列表查询
    public function search()
    {
        if (request()->isAjax()) {
            $page = (int)input('page', 1);
            $limit = (int)input('limit', 18);
            $where = $this->buildSelectParams();

            // 删除where部分查询条件
            unset($where['community_id']);
            unset($where['grid_group_id']);
            unset($where['grid_id']);

            // 增加查询条件：文件级别暂不上线
            // $count = $this->model->where($where)->where('level','>=',$this->getQueryLevel())->count();
            $count = $this->model->where($where)->count();
            $fieldList = $this->model->getFields();
            $order = !array_key_exists('sort', $fieldList) ? 'id' : 'sort';
            // $subQuery = $this->model->field('id')->where($where)->where('level','>=',$this->getQueryLevel())->order($order, 'desc')->limit($limit)->page($page)->buildSql();
            $subQuery = $this->model->field('id')->where($where)->order($order, 'desc')->limit($limit)->page($page)->buildSql();
            
            $subQuery = '( SELECT object.id FROM ' . $subQuery . ' AS object )';
            $list = $this->model->alias("f")
                ->join("sa_sc_file_log l","f.id=l.file_id and l.type=2 and l.community_id='".(int)$this->admin_info['community_id']."'","LEFT")
                ->field("f.*,ifNull(l.create_time,'') log_time")
                ->where('f.id in' . $subQuery)->order("f.".$order, 'desc')->select()->toArray();
            return $this->success('查询成功', '/', $list, $count);
        }
        return $this->view('',$this->admin_info);
    }
    
    // 文件查看列表查询
    public function search2()
    {
        if (request()->isAjax()) {
            $this->model = new ScFileLog();
            $page = (int)input('page', 1);
            $limit = (int)input('limit', 18);
            $where = [];
            if($this->admin_info['grid_id']){
                $where[] = ['grid_id','find in set',$this->admin_info['grid_id']];
            }else if($this->admin_info['grid_group_id']){
                $where[] = ['grid_id','find in set',$this->admin_info['grid_group_id']];
            }
            $count = $this->model->where($where)->count();
       
            $fieldList = $this->model->getFields();
            $order = !array_key_exists('sort', $fieldList) ? 'id' : 'sort';
            $subQuery = $this->model->field('id')->where($where)->order($order, 'desc')->limit($limit)->page($page)->buildSql();
            $subQuery = '( SELECT object.id FROM ' . $subQuery . ' AS object )';
            $list = $this->model->alias("l")
                ->join("sa_sc_file f","f.id=l.file_id and l.type=2 and l.community_id='".(int)$this->admin_info['community_id']."'")
                ->field("f.*,ifNull(l.create_time,'') log_time")
                ->where('l.id in' . $subQuery)->order("l.".$order, 'desc')->select()->toArray();
            return $this->success($this->model->getLastSql(), '/', $list, $count);
        }
        return $this->view();
    }

    // 查看派发情况
    public function log(){
        $id = input('id');
        $list = Db::name("sc_file_log")->alias("l")
            ->join("sa_sc_file f","l.file_id=f.id")
            ->join("sa_community c","l.community_id=c.id")
            ->field("l.*,f.title,c.community_name")
            ->where("l.file_id",$id)->select();
        return $this->view('',[
            'list' => $list
        ]);
    }

    // 文件派发、文件查看-详情
    public function logDetail(){
        $id = input('id');
        $data = $this->model->where('id', $id)->findOrEmpty()->toArray();
        $post['file_id']=$id;
        $post['type']=0;
        $this->logInsert($post);
        return $this->view('',[
            'data' => $data
        ]);
    }

    // 文件派发、文件查看-下载
    public function logDownload(){
        $id = input('id');
        $data = $this->model->where('id', $id)->findOrEmpty()->toArray();
        $post['file_id']=$id;
        $post['type']=1;
        $this->logInsert($post);
        return $this->redirect($data['files']);
    }

    // 文件派发-派发
    public function logAdd(){
        $id = input('id');
        $data = $this->model->where('id', $id)->findOrEmpty()->toArray();
        if (request()->isAjax()) {
            $grid_id = input("grid_id",[]);
            $post['file_id']=$id;
            $post['grid_id'] = implode(",",$grid_id);
            $post['type']=2;
            $this->logInsert($post);
            return $this->success();
        }
        $where['community_id'] = (int)$this->admin_info['community_id'];
        $gird_group = Db::name("grid")->where("grid_group_id",0)->where($where)->field("id,grid_name")->select()->toArray();
        $grid = Db::name("grid")->where($where)->where("grid_group_id",">",0)->field("id,grid_name")->select()->toArray();
        return $this->view('',['id'=>$id,'grid_group'=>$gird_group,'grid'=>$grid,'data'=>$data]);
    }
    // 保存文件操作日志。
    protected function logInsert($data){
        //$data['type']：0-查看、1-下载、2-派发
        $data['create_time']=date('Y-m-d H:i:s');
        $data['create_by']=$this->admin_info['name'];
        $data['community_id']=(int)$this->admin_info['community_id'];
        Db::name("sc_file_log")->insert($data);
    }
    //  根据不同角色获取查询最大级别
    private function getQueryLevel(){
        $admin_info = get_admin_info();
        $adminAccess = getAdminAccess($admin_info["id"]);
        $adminGroup = explode(',', $adminAccess['group_id']);
        // 如果是超级管理员、街道管理员、社区管理员
        if(in_array(1, $adminGroup)
                ||in_array(2, $adminGroup)
                ||in_array(3, $adminGroup)){
           return 1;
        }else if(in_array(5, $adminGroup)){
            //网格组管理员
            return 2;
        }else{
            //网格管理员或其他角色
            return 3;
        }
    }
}
