<?php
declare (strict_types = 1);
namespace app\admin\controller;

use app\AdminController;
use app\common\model\ScFileLog;
use app\common\model\ScFileReceivelog;
use support\Response;
use think\facade\Db;
use think\helper\Str;
use Webman\Http\Request;
use app\common\model\ScFile as ScFileModel;

/**
 * sc_party
 * 信息发布
 * <!---->
 * Class ScFile
 * @package app\admin\controller
 */
class ScFile extends AdminController
{
    /**
     * ScFile模型对象
     * @var \app\common\model\ScFile
     */

    public function __construct()
    {
        parent::__construct();
        $this->model = new ScFileModel;
    }

    public function add()
    {
        if (request()->isPost()) {

            $post = $this->preRuleOutFields(\request()->post());
            if ($this->dataLimit) {
                $post[$this->dataLimitField] = get_admin_id();
            }

            $validate = $this->isValidate ? get_class($this->model) : $this->isValidate;
            $post = request_validate_rules($post, $validate, $this->scene);
            if (empty($post) || !is_array($post)) {
                return $this->error($post);
            }
            $post = dataArrayToJson($post);
            $post['add_month']=substr($post['add_time'], 0, 7);
            if(!empty($this->model->create_by)){
                $post[$this->model->create_by]=$this->admin_info['name'];
            }
            $this->status = $this->model->create($post);
            return $this->status ? $this->success() : $this->error();
        }

        return $this->view('', ['data' => $this->getTableFields()]);
    }

    /**
     * 编辑资源
     * @return Response|void
     */
    public function edit()
    {
        $id = input('id');
        $data = $this->model->where('id', $id)->findOrEmpty()->toArray();

        // 限制数据调用
        if (!$this->authService->SuperAdmin() && $this->dataLimit
            && in_array($this->dataLimitField, $this->model->getFields())) {
            if ($data[$this->dataLimitField] != get_admin_id()) {
                return $this->error('没有权限');
            }
        }

        if (request()->isPost()) {
            $post = $this->preRuleOutFields(\request()->post());
            $validate = $this->isValidate ? get_class($this->model) : $this->isValidate;
            $post = request_validate_rules($post, $validate, $this->scene);
            if (empty($post) || !is_array($post)) {
                return $this->error($post);
            }
            $post = dataArrayToJson($post);
            $post['add_month']=substr($post['add_time'], 0, 7);
            if(!empty($this->model->update_by)){
                $post[$this->model->update_by]=$this->admin_info['name'];
            }
            $this->status = $this->model->update($post);
            return $this->status ? $this->success() : $this->error();
        }

        /**
         * 默认共享模板
         */
        $template = str_replace('/_', '/', Str::snake(request()->getController()));
        return $this->view($template . '/add', [
            'data' => $data
        ]);
    }

    // 2025-05-12 by 周洪利
    // 返回用户接收文件的记录
    public function receivelog() {
        if (request()->isAjax()) {
            $this->model = new scFileReceivelog();
            $page = (int)input('page', 1);
            $limit = (int)input('limit', 18);
            $keyword = (string)input('keyword', null);
            $where = [];
            if($this->admin_info['community_id']){
                $where[] = ['l.community_id', '=', $this->admin_info['community_id']];
            }
            if($this->admin_info['grid_id']){
                $where[] = ['l.grid_id', '=', $this->admin_info['grid_id']];
            }
            if($this->admin_info['grid_group_id']){
                $where[] = ['l.grid_group_id', '=', $this->admin_info['grid_group_id']];
            }

            $count = $this->model
            ->alias('l')
            ->join('sa_admin a', 'a.id=l.receiver_uid', 'LEFT')
            ->join('sa_sc_file f', 'f.id=l.file_id', 'LEFT')
            ->where($where)
            ->where(function($query) use($keyword){
                if($keyword) {
                    $query->where('f.title',  'like', '%' . $keyword . '%')
                    ->whereOr('a.nickname',  'like', '%' . $keyword . '%');
                }
            })
            ->count();
            $list = $this->model->alias("l")
                ->join("sa_sc_file f","f.id=l.file_id")
                ->join("sa_admin a", "a.id=l.receiver_uid")
                ->join("sa_community c", "c.id=l.community_id", "LEFT")
                ->join("sa_grid g", "g.id=l.grid_group_id", "LEFT")
                ->join("sa_grid h", "h.id=l.grid_id", "LEFT")
                ->field("f.id, f.level, f.title, f.create_time dispatch_time, l.receiver_nickname, l.log_type, a.name as receiver_name, l.create_time, g.grid_name as grid_group_name, h.grid_name, c.community_name", true)
                ->where($where)
                ->where(function($query) use($keyword){
                    if($keyword) {
                        $query->where('f.title',  'like', '%' . $keyword . '%')
                        ->whereOr('a.nickname',  'like', '%' . $keyword . '%');
                    }
                })
                ->order('l.id desc')
                ->select()
                ->toArray();
            return $this->success($this->model->getLastSql(), '/', $list, $count);
        }
        return $this->view();
    }

    public function search()
    {
        if (request()->isAjax()) {
            $page = (int)input('page', 1);
            $limit = (int)input('limit', 18);
            $where = $this->buildSelectParams();
            
            // 删除where部分查询条件
            unset($where['community_id']);
            unset($where['grid_group_id']);
            unset($where['grid_id']);
            
            $count = $this->model->where($where)->count();
            
            //$page = $count <= $limit ? 1 : $page;
            $fieldList = $this->model->getFields();
            $order = !array_key_exists('sort', $fieldList) ? 'id' : 'sort';
            $subQuery = $this->model->field('id')->where($where)->order($order, 'desc')->limit($limit)->page($page)->buildSql();
            $subQuery = '( SELECT object.id FROM ' . $subQuery . ' AS object )';
            $list = $this->model->alias("f")
                ->join("sa_sc_file_log l","f.id=l.file_id and l.type=2 and l.community_id='".(int)$this->admin_info['community_id']."'","LEFT")
                ->field("f.*,ifNull(l.create_time,'') log_time")
                ->where('f.id in' . $subQuery)->order("f.".$order, 'desc')->select()->toArray();
            return $this->success('查询成功', '/', $list, $count);
        }
        return $this->view('',$this->admin_info);
    }
    
    public function search2()
    {
        if (request()->isAjax()) {
            $this->model = new ScFileLog();
            $page = (int)input('page', 1);
            $limit = (int)input('limit', 18);
            $where = [];
            if($this->admin_info['grid_id']){
                $where[] = ['grid_id', '=', $this->admin_info['grid_id']];
            }else if($this->admin_info['grid_group_id']){
                $where[] = ['grid_id', '=', $this->admin_info['grid_group_id']];
            }
            $count = $this->model->where($where)->count();
            //$page = $count <= $limit ? 1 : $page;
            $fieldList = $this->model->getFields();
            $order = !array_key_exists('sort', $fieldList) ? 'id' : 'sort';
            $subQuery = $this->model->field('id')->where($where)->order($order, 'desc')->limit($limit)->page($page)->buildSql();
            $subQuery = '( SELECT object.id FROM ' . $subQuery . ' AS object )';
            $list = $this->model->alias("l")
                ->join("sa_sc_file f","f.id=l.file_id and l.community_id='".(int)$this->admin_info['community_id']."'")
                ->field("f.*,ifNull(l.create_time,'') log_time")
                ->where('l.id in' . $subQuery)->order("l.".$order, 'desc')->select()->toArray();
            return $this->success($this->model->getLastSql(), '/', $list, $count);
        }
        return $this->view();
    }


    public function log(){
        $id = input('id');
        $list = Db::name("sc_file_log")->alias("l")
            ->join("sa_sc_file f","l.file_id=f.id")
            ->join("sa_community c","l.community_id=c.id")
            ->field("l.*,f.title,c.community_name")
            ->where("l.file_id",$id)->select();
        return $this->view('',[
            'list' => $list
        ]);
    }

    public function logDetail(){
        $id = input('id');
        $data = $this->model->where('id', $id)->findOrEmpty()->toArray();
        // if($data['look_time'] == ''){
        //     $this->model->where('id', $id)->update(['look_time' => date('Y-m-d H:i:s')]);
        // }
        // return json($data);
        $post['file_id']=$id;
        $post['type']=0;
        $this->logInsert($post);
        $this->receivelogInsert(['file_id'=>$id, 'log_type'=>1]);
        return $this->view('',[
            'data' => $data
        ]);
    }

    public function logDownload(){
        $id = input('id');
        $data = $this->model->where('id', $id)->findOrEmpty()->toArray();
        // if($data['look_time'] == ''){
        //     $this->model->where('id', $id)->update(['look_time' => date('Y-m-d H:i:s')]);
        // }
        $post['file_id']=$id;
        $post['type']=1;
        $this->logInsert($post);
        return $this->redirect($data['files']);
    }

    public function logAdd(){
        $id = input('id');
        $data = $this->model->where('id', $id)->findOrEmpty()->toArray();
        if (request()->isAjax()) {
            $grid_id = input("grid_id",[]);
            $post['file_id']=$id;
            $post['grid_id'] = implode(",",$grid_id);
            $post['type']=2;
            $this->logInsert($post);
            return $this->success();
        }
        $where['community_id'] = (int)$this->admin_info['community_id'];
        $gird_group = Db::name("grid")->where("grid_group_id",0)->where($where)->field("id,grid_name")->select()->toArray();
        $grid = Db::name("grid")->where($where)->where("grid_group_id",">",0)->field("id,grid_name")->select()->toArray();
        return $this->view('',['id'=>$id,'grid_group'=>$gird_group,'grid'=>$grid,'data'=>$data]);
    }

    protected function logInsert($data){
        $data['create_time']=date('Y-m-d H:i:s');
        $data['create_by']=$this->admin_info['name'];
        $data['community_id']=(int)$this->admin_info['community_id'];
        Db::name("sc_file_log")->insert($data);
    }

    protected function receivelogInsert($data){
        $data['create_time']=date('Y-m-d H:i:s');
        $data['receiver_uid']=(int)$this->admin_info['id'];
        $data['receiver_nickname']=(string)$this->admin_info['nickname'];
        $data['community_id']=(int)$this->admin_info['community_id'];
        $data['grid_group_id']=(int)$this->admin_info['grid_group_id'];
        $data['grid_id']=(int)$this->admin_info['grid_id'];
        Db::name("sc_file_receivelog")->insert($data);
    }
}
