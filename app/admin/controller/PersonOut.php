<?php
declare (strict_types=1);

namespace app\admin\controller;

use app\AdminController;
use app\common\model\PersonImp;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use app\common\model\Person as PersonModel;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use think\facade\Db;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\IOFactory;
use app\admin\service\ExcelService;

/**
 * person
 * 委外人口管理
 * <!---->
 * Class Person
 * @package app\admin\controller
 */
class PersonOut extends AdminController
{

    protected array $columnFormat = [];
    protected array $where = [];
    protected string $tpl_name;
    protected string $table_name;
    protected string $table_source;
    protected int $row_index;
    protected string $pk;

    /**
     * Person模型对象
     * @var PersonModel
     */

    public function __construct()
    {
        parent::__construct();
        $this->model = new PersonModel;
        $this->columnFormat=[
            'A' => [0, '建三江街道办事处'],
            'B' => [1, 'community_name','required'=>'1'],
            'C' => [1, 'grid_group_name','required'=>'1'],
            'D' => [1, 'grid_name','required'=>'1'],
            'E' => [1, 'building_name'],
            'F' => [1, 'unit'],
            'G' => [1, 'floor'],
            'H' => [1, 'house_code'],
            'I' => [-2, 'relation','cut'=>1,'required'=>'1'],
            'J' => [1, 'name','required'=>'1'],
            'K' => [4, 'id_code','required'=>'1'],
            'L' => [6, 'birthday'],
            'M' => [6, 'age'],
            'N' => [6, 'gender'],
            'O' => [3, 'nation', 54,'required'=>'1'],
            'P' => [1, 're_name'],
            'Q' => [3, 'face', 58,'required'=>'1'],
            'R' => [3, 'education', 56,'required'=>'1'],
            'S' => [1, 'school'],
            'T' => [1, 'major'],
            'U' => [1, 'graduation_date'],
            'V' => [3, 'marry', 64,'required'=>'1'],
            'W' => [1, 'marry_date'],
            'X' => [3, 'relation', 53,'required'=>'1'],
            'Y' => [4, 'rpr_code'],
            'Z' => [2, 'consistency','required'=>'1'],
            'AA' => [2, 'dead'],
            'AB' => [1, 'dead_date'],
            'AC' => [3, 'blood', 62],
            'AD' => [3, 'religion', 55],
            'AE' => [3, 'house_type', 60,'required'=>'1'],
            'AF' => [1, 'house_area'],
            'AG' => [3, 'rpr', 404,'required'=>'1'],
            'AH' => [3, 'rpr_nature', 408,'required'=>'1'],
            'AI' => [2, 'gestational_age'],
            'AJ' => [1, 'hometown'],
            'AK' => [1, 'rpr_address','required'=>'1'],
            'AL' => [1, 'address','required'=>'1'],
            'AM' => [2, 'single_parent'],
            'AN' => [4, 'phone','required'=>'1'],
            'AO' => [4, 'phone_1'],
            'AP' => [4, 'phone_2'],
            'AQ' => [3, 'only_child',65],
            'AR' => [1, 'only_child_code'],
            'AS' => [3, 'other_child', 66],
            'AT' => [3, 'labor_capacity', 63,'required'=>'1'],
            'AU' => [3, 'health', 61],
            'AV' => [2, 'disability'],
            'AW' => [3, 'disability_type', 411],
            'AX' => [3, 'disability_level', 418],
            'AY' => [1, 'disability_code'],
            'AZ' => [2, 'poverty'],
            'BA' => [2, 'extreme_poverty'],
            'BB' => [2, 'retire'],
            'BC' => [1, 'retire_unit'],
            'BD' => [2, 'insurance_worker'],
            'BE' => [2, 'insurance_towns'],
            'BF' => [2, 'insurance_village'],
            'BG' => [3, 'endowment_insurance', 67],
            'BH' => [2, 'low_income_family'],
            'BI' => [2, 'low_income_person'],
            'BJ' => [2, 'optimal'],
            'BK' => [2, 'releaser'],
            'BL' => [2, 'martyr'],
            'BM' => [2, 'corrector'],
            'BN' => [2, 'children_orphan'],
            'BO' => [2, 'children_stay'],
            'BP' => [2, 'children_abandoned'],
            'BQ' => [2, 'children_disability'],
            'BR' => [2, 'children_subsistence_allowance'],
            'BS' => [2, 'ex_serviceman'],
            'BT' => [1, 'soldier_code'],
            'BU' => [3, 'enlistment', 57],
            'BV' => [2, 'old_oldest'],
            'BW' => [6, 'old'],
            'BX' => [2, 'temporary_relief'],
            'BY' => [2, 'beyond_border'],
            'BZ' => [2, 'drugged'],
            'CA' => [5, 'nation', 1],
            'CB' => [2, 'illness_mental'],
            'CC' => [2, 'cult'],
            'CD' => [2, 'monk'],
            'CE' => [2, 'petition'],
            'CF' => [2, 'help_poor'],
            'CG' => [2, 'illness_aids'],
            'CH' => [2, 'special_support'],
            'CI' => [2, 'old_empty'],
            'CJ' => [2, 'old_lonely'],
            'CK' => [2, 'old_single'],
            'CL' => [2, 'overseas'],
            'CM' => [2, 'representative_party'],
            'CN' => [2, 'representative_people'],
            'CO' => [2, 'committee_member'],
            'CP' => [2, 'volunteer'],
            'CQ' => [1, 'association'],
            'CR' => [1, 'home_year_income'],
            'CS' => [1, 'home_year_info'],
            'CT' => [4, 'urgent_phone'],
            'CU' => [1, 'vehicle_code'],
            'CV' => [2, 'house'],
            'CW' => [1, 'house_number'],
            'CX' => [1, 'house_info'],
            'CY' => [2, 'plow_land'],
            'CZ' => [1, 'plow_land_info'],
            'DA' => [2, 'forest_land'],
            'DB' => [1, 'forest_land_info'],
            'DC' => [2, 'agricultural_machinery'],
            'DD' => [1, 'agricultural_machinery_info'],
            'DE' => [2, 'equities'],
            'DF' => [1, 'equities_info'],
            'DG' => [2, 'stock_right'],
            'DH' => [1, 'stock_right_info'],
            'DI' => [2, 'firm'],
            'DJ' => [1, 'firm_info'],
            'DK' => [2, 'other'],
            'DL' => [1, 'other_info'],
            'DM' => [1, 'details'],
            'DN' => [1, 'wechat_code'],
        ];
        $this->tpl_name = "委外人口模版";
        $this->table_name = 'sa_person_imp';
        $this->table_source = "sa_person";
        $this->pk = "person_id";
        $this->row_index = 2;
    }

    public function dataImport()
    {
        if (request()->isPost()) {
            set_time_limit(0);
            $filePath = input('file_1');
            $spreadsheet = IOFactory::load('public'.$filePath);
            //获取活动工作表
            $worksheet = $spreadsheet->getActiveSheet();
            $sheetNames = $spreadsheet->getSheetNames();
            if($sheetNames[0]!='委外人口'){
                return $this->error("请使用正确的模板导入");
            }
            $ids = [];
            foreach ($this->columnFormat as $item) {
                if($item[0]==3){
                    $ids[] = $item[2];
                }
            }
            $Dictionary = getDictionaryGroup($ids);
            $admin_id = get_admin_id();
            $columns_arr=[];
            $update_field = [];
            foreach ($this->columnFormat as $vo){
                if ($vo[0] < 1 || $vo[0] > 4) {
                    continue;
                }
                $columns_arr[$vo[1]]=$vo[1];
                if(array_key_exists('column',$vo)){
                    $vo[1] = $vo['column'];
                }
                $update_field[] = $vo[1];
            }
            $create_time=date('Y-m-d H:i:s');
            $create_by = $this->admin_info['name'];
            $columns=implode(",",$columns_arr);
            $columns .= ",sort,admin_id,person_type";
            $insert_sql = "INSERT INTO ".$this->table_name."(".$columns.") VALUES ";
            $values = [];
            $PersonImp = new PersonImp();
            Db::query("delete from ".$this->table_name." where admin_id=".$admin_id);
            $error_arr = [];
            foreach ($worksheet->getRowIterator() as $i=>$row) {
                if ($i < $this->row_index||empty($worksheet->getCell('K'.$i)->getValue())) {
                    continue;
                }
                $cellIterator = $row->getCellIterator();
                $cellIterator->setIterateOnlyExistingCells(false); // 遍历所有单元格，包括空单元格
                $values_arr = [];
                foreach ($cellIterator as $key => $cell) {
                    $value = $cell->getValue();
                    if(empty($this->columnFormat[$key])){
                        return $this->error("存在未定义的excel列，请检查导入表格是否和模板表格格式相同");
                    }
                    if(array_key_exists('required',$this->columnFormat[$key])&&empty($value)){
                        $error_arr[$i].="第{$key}列必填项为空;";
                    }
                    $vo = $this->columnFormat[$key];
                    if (($vo[0] < 1&&$vo != -3) || $vo[0] > 4) {
                        continue;
                    } else {
                        switch ($vo[0]) {
                            case 2:
                                $value = $value == '是' ? 1 : 0;
                                break;
                            case 3:
                                $key = array_search($value, $Dictionary[$vo[2]]);
                                if ($key !== false) {
                                    $value = $key;
                                } else {
                                    $value = 0;
                                }
                                break;
                        }
                        if(array_key_exists($vo[1], $values_arr)&&empty($value)){
                            continue;
                        }
                        $values_arr[$vo[1]] = "\"".$value."\"";
                    }
                }
                $values_sql = "(".implode(",",$values_arr).",".($i-$this->row_index+1).",".$admin_id.",4)";
                $values[] = $values_sql;
                $values_arr = [];
                if($i%1000==0){
                    Db::query($insert_sql . implode(",", $values));
                    $values = [];
                }
            }
            if(count($values)){
                // return $this->error($insert_sql.$values[0]);
                Db::query($insert_sql.implode(",",$values));
            }
            
            Db::query("DELETE FROM ".$this->table_name." WHERE admin_id=$admin_id and id_code IS NULL");
            Db::query("UPDATE ".$this->table_name." SET age = FLOOR(DATEDIFF(CURDATE(), STR_TO_DATE(SUBSTRING(id_code, 7, 8), '%Y%m%d')) / 365.25),birth = STR_TO_DATE(SUBSTRING(id_code, 7, 8), '%Y%m%d') WHERE admin_id=$admin_id and id_code IS NOT NULL");
            Db::query("UPDATE ".$this->table_name." i INNER JOIN ".$this->table_source." p on i.id_code=p.id_code set i.".$this->pk."=p.id where admin_id=$admin_id and p.delete_time is null");
            Db::query("UPDATE ".$this->table_name." i INNER JOIN sa_community c on i.community_name=c.community_name set i.community_id=c.id where admin_id=$admin_id");
            Db::query("UPDATE ".$this->table_name." i INNER JOIN sa_grid g on i.community_id=g.community_id and i.grid_group_name=g.grid_name set i.grid_group_id=g.id where admin_id=$admin_id");
            Db::query("UPDATE ".$this->table_name." i INNER JOIN sa_grid g on i.community_id=g.community_id and i.grid_name=g.grid_name set i.grid_id=g.id where admin_id=$admin_id");
            Db::query("UPDATE ".$this->table_name." i INNER JOIN sa_building b on i.grid_id = b.grid_id and i.building_name=b.building_name set i.building_id=b.id where admin_id=$admin_id");
            Db::query("UPDATE ".$this->table_name." i INNER JOIN sa_house h on i.building_id=h.building_id and i.unit = h.unit and i.floor=h.floor and i.house_code = h.code set i.house_id=h.id where admin_id=$admin_id");
            Db::query("UPDATE ".$this->table_name." i INNER JOIN sa_person_soldier s on i.person_id=s.person_id set i.soldier_id=s.id where admin_id=$admin_id");
            Db::query("UPDATE ".$this->table_name." i INNER JOIN sa_person_volunteer v on i.person_id=v.person_id set i.volunteer_id=v.id where admin_id=$admin_id");
            Db::close();
            $list = $PersonImp->where("admin_id",$admin_id)->where("community_id",0)->field("sort")->select()->toArray();
            foreach ($list as $item){
                $error_arr[$item['sort']+$this->row_index-1].="找不到对应的社区信息;";
            }
            $list = $PersonImp->where("admin_id",$admin_id)->where("grid_group_id",0)->field("sort")->select()->toArray();
            foreach ($list as $item){
                $error_arr[$item['sort']+$this->row_index-1].="找不到对应的网格组信息;";
            }
            $list = $PersonImp->where("admin_id",$admin_id)->where("grid_id",0)->field("sort")->select()->toArray();
            foreach ($list as $item){
                $error_arr[$item['sort']+$this->row_index-1].="找不到对应的网格信息;";
            }
            $list = $PersonImp->where("admin_id",$admin_id)->where("LENGTH(id_code) <> 18")->field("sort")->select()->toArray();
            foreach ($list as $item){
                $error_arr[$item['sort']+$this->row_index-1].="身份证号不合法;";
            }
            $list = $PersonImp->alias("i")
                ->join("sa_person p","i.id_code=p.id_code and p.delete_time is null")
                ->join("sa_community c", "p.community_id=c.id", "LEFT")
                ->join("sa_grid g", "p.grid_id=g.id", "LEFT")
                ->join("sa_grid gg", "p.grid_group_id=gg.id", "LEFT")
                ->field("i.sort,g.grid_name,gg.grid_name grid_group_name,c.community_name")
                ->where("admin_id",$admin_id)
                ->where("(p.community_id<>i.community_id or p.grid_group_id<>i.grid_group_id or p.grid_id <> p.grid_id) and admin_id=$admin_id")
                ->field("i.sort")->select()->toArray();
            foreach ($list as $item){
                $error_arr[$item['sort']+$this->row_index-1].="该人口已存在于{$item['community_name']},{$item['grid_group_name']},{$item['grid_name']};";
            }
            $list = $PersonImp->alias("i")
                ->join("sa_person p","i.id_code=p.id_code")
                ->where("i.admin_id",$admin_id)->where("p.person_type<4")->where("p.delete_time is null")->field("i.sort")->select()->toArray();
            foreach ($list as $item){
                $error_arr[$item['sort']+$this->row_index-1].="该人口已是实有人口;";
            }
            $list = $PersonImp->alias("i")
                ->join("sa_person p","i.id_code=p.id_code and p.delete_flag = 1")->where("i.admin_id",$admin_id)->field("i.sort")->select()->toArray();
            foreach ($list as $item){
                $error_arr[$item['sort']+$this->row_index-1].="该人口已是待归档人口;";
            }
            $list = $PersonImp->alias("i")->field("i.sort")->where("admin_id",$admin_id)->where("ifnull(insurance_worker,0)+ifnull(insurance_towns,0)+ifnull(insurance_village,0)>1")->select()->toArray();
            foreach ($list as $item){
                $error_arr[$item['sort']+$this->row_index-1].="医保只能选一项;";
            }
            if(count($error_arr)>0){
                return $this->error("信息校验未通过",'',['data'=>$error_arr]);
            }
            try{
                $sql = $this->buildUpdSql($admin_id);
                Db::query($sql);
                $field="community_id,grid_group_id,grid_id,building_id,house_id,
                name,id_code,nation,re_name,face,education,school,major,graduation_date,
                marry,marry_date,relation,rpr_code,consistency,dead,dead_date,blood,religion,
                house_type,house_area,rpr,rpr_nature,person_type,gestational_age,hometown,
                rpr_address,address,single_parent,phone,phone_1,phone_2,only_child,only_child_code,
                other_child,labor_capacity,health,disability,disability_type,disability_level,
                disability_code,poverty,extreme_poverty,retire,retire_unit,insurance_worker,
                insurance_towns,insurance_village,endowment_insurance,low_income_family,
                low_income_person,optimal,releaser,martyr,corrector,children_orphan,children_stay,
                children_abandoned,ex_serviceman,enlistment,old_oldest,temporary_relief,
                beyond_border,drugged,illness_mental,cult,monk,petition,help_poor,illness_aids,
                special_support,old_empty,old_lonely,old_single,overseas,representative_party,
                representative_people,committee_member,volunteer,urgent_phone,details,wechat_code,sort";
                $insert_sql = "INSERT INTO ".$this->table_source." ({$field},create_time,create_by) select {$field},'$create_time' create_time,'$create_by' create_by from ".$this->table_name." where admin_id = {$admin_id} and ".$this->pk." = 0";
                Db::query($insert_sql);
                Db::query("UPDATE sa_person p
INNER JOIN sa_person_imp i on p.id_code=i.id_code and i.admin_id = {$admin_id}
INNER JOIN sa_house h on p.house_id=h.id set i.person_id=p.id, p.house_area=h.area,p.age = FLOOR(DATEDIFF(CURDATE(), STR_TO_DATE(SUBSTRING(p.id_code, 7, 8), '%Y%m%d')) / 365.25),
p.birth = STR_TO_DATE(SUBSTRING(p.id_code, 7, 8), '%Y%m%d') , p.sex = MOD(SUBSTRING(p.id_code,17, 1), 2);");
                $personController = new Person();
                $insert_sql = "INSERT INTO sa_person_income (person_id,id_code,create_time,create_by) select i.person_id, i.id_code,'$create_time' create_time,'$create_by' create_by from sa_person_imp i left join sa_person_income e on i.person_id=e.person_id where i.admin_id = {$admin_id} and e.id is null";
                Db::query($insert_sql);
                $sql = $personController->buildUpdSqlIncome($admin_id);
                Db::query($sql);
                $this->model->rbacImp($admin_id);
                Db::query("delete from sa_person_imp where admin_id=".$admin_id);
            }catch (\Exception $e){
                Db::query("delete from sa_person_imp where admin_id=".$admin_id);
                return $this->error( '导入失败');
            }

            return $this->success( '导入成功');
        }
        return $this->view();
    }

    public function excelExport($list = [])
    {
        // 文件路径
        $filePath = 'public/tpl/'.$this->tpl_name.'.xlsx';

        // 读取Excel文件
        $spreadsheet = IOFactory::load($filePath);

        // 获取活动工作表
        $sheet = $spreadsheet->getActiveSheet();
        $excelService = new ExcelService();
        $integerValidation = $excelService->integerValidation();
        $decimalValidation = $excelService->decimalValidation();
        $idcardValidation = $excelService->idcardValidation();
        $count = 10;
        if($list){
            $count = count($list);
        }
        $dataList = ['是', '否'];
        $optionValidation = $excelService->optionValidation($dataList);
        $ids = [];
        foreach ($this->columnFormat as $item) {
            if($item[0]==3){
                $ids[] = $item[2];
            }
        }
        $Dictionary = getDictionaryGroup($ids);
        $sheet->getStyle('K:K')->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_TEXT);
        $sheet->getStyle('L2:L'. $count+10)->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_DATE_YYYYMMDD2);
        foreach ($this->columnFormat as $key=>$item) {
            $cellRange = $key.$this->row_index. ':' .$key. $count+10;
            if($item[0]==2||$item[0]==-2){
                $sheet->setDataValidation($cellRange, $optionValidation);
            }
            if($item[0]==3||$item[0]==-3){
                $dataList = array_values($Dictionary[$item[2]]);
                if($key=="T"){
                    $optionListValidation = $excelService->optionRelationValidation($dataList);
                }else{
                    $optionListValidation = $excelService->optionValidation($dataList);
                }
                $sheet->setDataValidation($cellRange, $optionListValidation);
            }
            if(array_key_exists('number',$item)){
                $sheet->setDataValidation($cellRange, $integerValidation);
            }
        }
        if (count($list) > 0) {
            $i = $this->row_index;
            foreach ($list as $item) {
                $id_info = getInfoFromIdCard($item['id_code']);
                $id_info['gender']=$id_info['gender']==1?'男':'女';
                foreach ($this->columnFormat as $column => $value) {
                    switch ($value[0]) {
                        case 1:
                            $item[$value[1]]=$item[$value[1]]=='0000-00-00'?'':$item[$value[1]];
                            $sheet->setCellValueExplicit($column . $i, trim((string)$item[$value[1]], "\""), DataType::TYPE_STRING);
                            break;
                        case 2:
                        case -2:
                            $sheet->setCellValue($column . $i, $item[$value[1]] == 1 ? '是' : '否');
                            break;
                        case 3:
                        case -3:
                            $index = $item[$value[1]];
                            if ($index||$index===0) {
                                $sheet->setCellValue($column . $i, empty($Dictionary[$value[2]][$index]) ? '' : $Dictionary[$value[2]][$index]);
                            }
                            break;
                        case 6:
                            $sheet->setCellValue($column . $i, $id_info[$value[1]]);
                            break;
                        case 5:
                            $sheet->setCellValue($column . $i, $item[$value[1]] != $value[2] ? '是' : '否');
                            break;
                        case 4:
                            if(array_key_exists("type",$value)&&$item[$value[1]]){
                                $value = $this->type[$value["type"]][$item[$value[1]]];
                            }else{
                                $value = $item[$value[1]];
                            }
                            $sheet->setCellValueExplicit($column . $i, trim((string)$value, "\""), DataType::TYPE_STRING);
                            break;
                        case 0:
                            $sheet->setCellValue($column . $i, $value[1]);
                            break;
                    }
                }
                $sheet->setCellValue("L" . $i, "=DATE(MID(K$i,7,4),MID(K$i,11,2),MID(K$i,13,2))");
                $sheet->setCellValue("M" . $i, "=INT((TODAY()-L$i)/365.25)");
                $sheet->setCellValue("N" . $i, "=IF(MOD(MID(K$i,17,1),2)=0,\"女\",\"男\")");
                $i++;
            }
            for($j=0;$j<10;$j++){
                $sheet->setCellValue("L" . $i, "=DATE(MID(K$i,7,4),MID(K$i,11,2),MID(K$i,13,2))");
                $sheet->setCellValue("M" . $i, "=INT((TODAY()-L$i)/365.25)");
                $sheet->setCellValue("N" . $i, "=IF(MOD(MID(K$i,17,1),2)=0,\"女\",\"男\")");
                $i++;
            }
            // 保存Excel文件
            $writer = new Xlsx($spreadsheet);
            $day = date('Ymd');
            $time = date('his');
            $dir = '/download/' . $day;
            if (!file_exists('public' . $dir)) {
                mkdir('public' . $dir, 0777, true);
            }
            $filePath = $dir . '/'.$this->tpl_name.'(' . $day . $time . ').xlsx';
            $writer->save('public' . $filePath);
            return $filePath;
        }
        // 保存Excel文件
        $writer = new Xlsx($spreadsheet);
        $writer->save('public/tpl/'.$this->tpl_name.'.xlsx');
        return '';
    }

    public function downloadExcelTpl()
    {
        $this->excelExport();
        return $this->redirect('/tpl/'.$this->tpl_name.'.xlsx');
    }

    public function buildUpdSql($admin_id){
        $this->tableFields = $this->model->getFields();
        $fieldColumn="UPDATE ".$this->table_source." b INNER JOIN ".$this->table_name." i on i.".$this->pk."=b.id and i.admin_id=".$admin_id." set ";
        foreach ($this->columnFormat as $item){
            $field = $item[1];
            if (!array_key_exists($field, $this->tableFields)) {
                continue;
            }
            if (array_key_exists('column',$item)){
                $field = $item['column'];
            }
            $fieldColumn.="b.".$field."=i.".$field.",";
        }
        $update_time=date('Y-m-d H:i:s');
        $update_by = $this->admin_info['name'];
        return $fieldColumn."b.sort=i.sort,b.update_time='$update_time',b.update_by='$update_by',b.person_type=4;";
    }

    public function dataToExcel()
    {
        $alias = "p";
        $where = (new Person)->getWhere($alias);
        $where[$alias.".delete_time"]=null;
        $whereOr = "($alias.person_type=4)";
        $fieldList = $this->model->getFields();
        $order = !array_key_exists('sort', $fieldList) ? 'id' : 'sort';
        $order =  "$alias.community_id,$alias.grid_group_id,$alias.grid_id,bsort,h.unit,h.floor,h.sort,$alias.relation,$alias.$order";
        $list = $this->model->alias($alias)->Distinct(true)
            ->join("sa_community c", $alias . ".community_id=c.id", "LEFT")
            ->join("sa_grid g", $alias . ".grid_id=g.id", "LEFT")
            ->join("sa_grid gg", $alias . ".grid_group_id=gg.id", "LEFT")
            ->join("sa_building b", $alias . ".building_id=b.id", "LEFT")
            ->join("sa_house h", $alias . ".house_id=h.id", "LEFT")
            ->join("sa_person_vehicle v", $alias . ".id_code=v.id_code", "LEFT")
            ->join("sa_person_soldier s", $alias . ".id=s.person_id and $alias.grid_id=s.grid_id", "LEFT")
            ->join("sa_person_volunteer vo", $alias . ".id=vo.person_id and $alias.grid_id=s.grid_id", "LEFT")
            ->join("sa_person_income i", $alias . ".id=i.person_id", "LEFT")
            ->field($alias . ".*,g.grid_name,gg.grid_name grid_group_name,c.community_name,b.building_name,b.sort bsort,h.code house_code,
            h.unit,h.floor,h.sort hsort,count(*) car_num,GROUP_CONCAT(v.vehicle_code SEPARATOR ',') vehicle_code,
            s.code soldier_code,vo.association,
            i.home_year_income,i.home_year_info,i.house income_house,i.house_number,i.house_info,i.plow_land,i.plow_land_info,
            i.forest_land,i.forest_land_info,i.agricultural_machinery,i.agricultural_machinery_info,i.equities,i.equities_info,
            i.stock_right,i.stock_right_info,i.firm,i.firm_info,i.other,i.other_info")
            ->where($where)->where($whereOr)
            ->group($alias . '.id,s.id,vo.id,i.id')
            ->order($order)->select()->toArray();
        if(count($list)>5000){
            return $this->error('导出的数据过多，建议分批导出');
        }
        $filePath = $this->excelExport($list);
        if(request()->isPost()){
            return $this->success('',$filePath);
        }
        return $this->redirect($filePath);
    }

}
