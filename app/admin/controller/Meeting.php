<?php

declare (strict_types = 1);

namespace app\admin\controller;

use app\AdminController;
use Webman\Http\Request;
use app\common\model\Meeting as MeetingModel;
use app\admin\service\MeetingConfereeService;
use think\helper\Str;

/**
 * meeting
 * 视频会议
 * <!---->
 * Class Meeting
 * @package app\admin\controller
 */

class Meeting extends AdminController
{

    /**
     * Meeting模型对象
     * @var \app\common\model\Meeting
     */

    public function __construct()
    {
        parent::__construct();
        $this->model = new MeetingModel;
    }
    
    /**
     * 获取资源列表
     * @return Response
     */
    public function index()
    {
        if (request()->isAjax()) {
            $page = (int)input('page', 1);
            $limit = (int)input('limit', 18);
            $where = $this->buildSelectParams();
            $count = $this->model->where($where)->where("deleted",'0')->count();
            //$page = $count <= $limit ? 1 : $page;
            $fieldList = $this->model->getFields();
            $order = !array_key_exists('sort', $fieldList) ? 'id' : 'sort';
            $subQuery = $this->model->field('id')->where($where)->where("deleted",'0')->order($order, 'desc')->limit($limit)->page($page)->buildSql();
            $subQuery = '( SELECT object.id FROM ' . $subQuery . ' AS object )';
            $list = $this->model->with($this->relationModel)->where('id in' . $subQuery)->order($order, 'desc')->select()->toArray();
            return $this->success('查询成功', '/', $list, $count);
        }

        return $this->view();
    }

    public function add()
    {
        if (request()->isPost()) {

            $post = $this->preRuleOutFields(\request()->post());
            if ($this->dataLimit) {
                $post[$this->dataLimitField] = get_admin_id();
            }

            $validate = $this->isValidate ? get_class($this->model) : $this->isValidate;
            $post = request_validate_rules($post, $validate, $this->scene);
            if (empty($post) || !is_array($post)) {
                return $this->error($post);
            }
            $post = dataArrayToJson($post);
            $post['admin_id'] = $this->admin_info['id'];
            
            // 自动生成会议室ID
            $milliseconds = round(microtime(true) * 1000);
            $post['room_id'] = strval($milliseconds);

            if(!empty($this->model->create_by)){
                $post[$this->model->create_by]=$this->admin_info['name'];
            }
            if(!empty($this->model->createTime)){
                $post[$this->model->createTime]=date('Y-m-d H:i:s');
            }

            $this->status = $this->model->create($post);

            // 添加参会人员
            if(!empty($post['admin_ids'])){
                $adminIds = explode(",",$post['admin_ids']);
                MeetingConfereeService::addConferees($this->admin_info,$this->status["id"],$adminIds);
            }
          
            return $this->status ? $this->success() : $this->error();
        }

        return $this->view('', ['data' => $this->getTableFields()]);
    }

    public function edit()
    {
        
        $id = input('id');
        $data = $this->model->where('id', $id)->findOrEmpty()->toArray();
        
        // 查询参会人员
        $confereeIds= MeetingConfereeService::getConfereeIds($id);
        if(!empty($confereeIds)){
            $data['admin_ids'] = implode(',',$confereeIds);
        }
      
        // 限制数据调用
        if (!$this->authService->SuperAdmin() && $this->dataLimit
        && in_array($this->dataLimitField, $this->model->getFields())) {
            if ($data[$this->dataLimitField] != get_admin_id()) {
                return $this->error('没有权限');
            }
        }
        
        if (request()->isPost()) {

            $post = $this->preRuleOutFields(\request()->post());
            if ($this->dataLimit) {
                $post[$this->dataLimitField] = get_admin_id();
            }

            $validate = $this->isValidate ? get_class($this->model) : $this->isValidate;
            $post = request_validate_rules($post, $validate, $this->scene);
            if (empty($post) || !is_array($post)) {
                return $this->error($post);
            }
            $post = dataArrayToJson($post);
            $post['admin_id'] = $this->admin_info['id'];
            
            if(!empty($this->model->update_by)){
                $post[$this->model->update_by]=$this->admin_info['name'];
            }
            if(!empty($this->model->updateTime)){
                $post[$this->model->updateTime]=date('Y-m-d H:i:s');
            }

            $this->status = $this->model->update($post);

             // 添加参会人员
            if(!empty($post['admin_ids'])){
                $adminIdsStr = explode(",",$post['admin_ids']);
                $result = MeetingConfereeService::appendConferees($this->admin_info,$data,array_map('intval', $adminIdsStr));
            }
          
            return $this->status ? $this->success() : $this->error();
        }

        $template = str_replace('/_', '/', Str::snake(request()->getController()));
        return $this->view($template . '/add', ['data' =>$data]);
    }
    
    
    // 删除会议
    public function del()
    {
        $id = input('id');
        try {
            $meeting = $this->model->where("id",$id)->find();
            if($meeting['status']==1){
                return $this->error('会议进行中，请勿删除！');
            }
        
            // if($meeting['status']==2){
            //     return $this->error('会议已结束，请勿删除！');
            // }

            $updateTime = date('Y-m-d H:i:s');
            $this->model->where("id",$id)->update(['deleted'=>1,'update_by'=>get_admin_id(),'update_time'=>$updateTime]);
        } catch (\Throwable $th) {
            return $this->error($th->getMessage());
        }

        return $this->success();
    }
}

