<?php
declare (strict_types = 1);
namespace app\admin\controller;

use app\admin\service\ExcelService;
use app\AdminController;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use support\Response;
use think\helper\Str;
use Webman\Http\Request;
use app\common\model\PersonVehicle as PersonVehicleModel;
use think\facade\Db;

/**
 * person_vehicle
 * 车辆管理
 * <!---->
 * Class PersonVehicle
 * @package app\admin\controller
 */
class PersonVehicle extends AdminController
{
    protected array $columnFormat;
    protected string $tpl_name;
    protected string $table_name;
    protected string $table_source;
    protected int $row_index;
    protected string $pk;
    /**
     * PersonVehicle模型对象
     * @var PersonVehicleModel
     */

    public function __construct()
    {
        parent::__construct();
        $this->model = new PersonVehicleModel;
        $this->columnFormat=[
            'A' => [0, '建三江街道办事处'],
            'B' => [1, 'community_name','column'=>'community_id'],
            'C' => [1, 'grid_group_name','column'=>'grid_group_id'],
            'D' => [1, 'grid_name','column'=>'grid_id'],
            'E' => [3, 'type',30,'required'=>'1'],
            'F' => [3, 'purpose',42,'required'=>'1'],
            'G' => [3, 'code_type',401,'required'=>'1'],
            'H' => [1, 'owner','required'=>'1'],
            'I' => [4, 'id_code','required'=>'1'],
            'J' => [4, 'phone','required'=>'1'],
            'K' => [1, 'vehicle_code','required'=>'1'],
            'L' => [1, 'brand']
        ];
        $this->tpl_name = "车辆模版";
        $this->table_name = 'sa_person_vehicle_imp';
        $this->table_source = "sa_person_vehicle";
        $this->pk = "person_vehicle_id";
        $this->row_index = 3;
        $this->filterWhere = ['road_id','street_id','road_start','road_end','street_start','street_end','type'];
        $this->checkRule=[
            ['type'=>'required','title'=>'必填项未填','columns'=>"community_id,grid_group_id,grid_id,vehicle_code,purpose,code_type,owner,id_code,phone"],
        ];
    }

    /**
     * 添加资源
     * @return Response|void
     */
    public function add()
    {
        if (request()->isPost()) {

            $post = $this->preRuleOutFields(\request()->post());
            if ($this->dataLimit) {
                $post[$this->dataLimitField] = get_admin_id();
            }

            $validate = $this->isValidate ? get_class($this->model) : $this->isValidate;
            $post = request_validate_rules($post, $validate, $this->scene);
            if (empty($post) || !is_array($post)) {
                return $this->error($post);
            }
            $post = dataArrayToJson($post);
            if(!empty($this->model->create_by)){
                $post[$this->model->create_by]=$this->admin_info['name'];
            }
            if(!empty($this->model->createTime)){
                $post[$this->model->createTime]=date('Y-m-d H:i:s');
            }

            // 2025-05-08 by 周洪利
            // 原始代码
            // $this->status = $this->model->create($post);
            // return $this->status ? $this->success() : $this->error();

            // 2025-05-08 by 周洪利
            // 根据车主身份证，查询实有人口数据，如果找不到这个人，就提示错误。
            // 修改后的代码
            $checkRow = Db::name("person")->where(["id_code"=>$post['id_code']])->find();
            if(!$checkRow) {
                return $this->error('找不到车主信息，请核实后再操作');
            } else {
                $this->status = $this->model->create($post);
                return $this->status ? $this->success() : $this->error();
            }
        }
        $data = $this->getTableFields();
        $Communities = Db::name("community")->select()->toArray();
        $grid_groups = Db::name("grid")->where(["community_id"=>$data['community_id'],'grid_group'=>0])->select()->toArray();
        $grids = Db::name("grid")->where(["grid_group"=>$data['grid_group_id']])->select()->toArray();
        return $this->view('', ['data' => $data,
            'Communities'=>$Communities,
            'grid_groups'=>$grid_groups,
            'grids'=>$grids
        ]);
    }

    /**
     * 编辑资源
     * @return Response|void
     */
    public function edit()
    {
        $id = input('id');
        $data = $this->model->where('id', $id)->findOrEmpty()->toArray();

        // 限制数据调用
        if (!$this->authService->SuperAdmin() && $this->dataLimit
            && in_array($this->dataLimitField, $this->model->getFields())) {
            if ($data[$this->dataLimitField] != get_admin_id()) {
                return $this->error('没有权限');
            }
        }

        if (request()->isPost()) {
            $post = $this->preRuleOutFields(\request()->post());
            $validate = $this->isValidate ? get_class($this->model) : $this->isValidate;
            $post = request_validate_rules($post, $validate, $this->scene);
            if (empty($post) || !is_array($post)) {
                return $this->error($post);
            }
            $post = dataArrayToJson($post);
            if(!empty($this->model->update_by)){
                $post[$this->model->update_by]=$this->admin_info['name'];
            }

            // 2025-05-08 by 周洪利
            // 原始代码
            // $this->status = $this->model->update($post);
            // return $this->status ? $this->success() : $this->error();

            // 2025-05-08 by 周洪利
            // 修改后的代码
            // 根据车主身份证，查询实有人口数据，如果找不到这个人，就提示错误。
            $checkRow = Db::name("person")->where(["id_code"=>$post['id_code']])->find();
            if(!$checkRow) {
                return $this->error('找不到车主信息，请核实后再操作');
            } else {
                $this->status = $this->model->update($post);
                return $this->status ? $this->success() : $this->error();
            }
        }

        /**
         * 默认共享模板
         */
        $template = str_replace('/_', '/', Str::snake(request()->getController()));
        $Communities = Db::name("community")->select()->toArray();
        $grid_groups = Db::name("grid")->where(["community_id"=>$data['community_id'],'grid_group'=>0])->select()->toArray();
        $grids = Db::name("grid")->where(["grid_group"=>$data['grid_group_id']])->select()->toArray();
        return $this->view($template . '/add', [
            'data' => $data,
            'Communities'=>$Communities,
            'grid_groups'=>$grid_groups,
            'grids'=>$grids
        ]);
    }

    /**
     * index 函数
     */
    public function index()
    {
        if (request()->isAjax()||request()->isPost()) {
            $page = (int)input('page', 1);
            $limit = (int)input('limit', 18);
            $alias = "e";
            $where = $this->buildSelectParams($alias.".");
            $keyword = input('keyword','');
            $type = (int)input('type',-1);
            if($keyword){
                $where[] = [$alias.".vehicle_code|".$alias.".id_code|".$alias.".owner", 'like', '%' . $keyword . '%'];
            }
            if($type>-1){
                $where['type'] = $type;
            }
            $count = $this->model->alias($alias)->where($where)->count();
            //$page = $count <= $limit ? 1 : $page;
            $fieldList = $this->model->getFields();
            $order = !array_key_exists('sort', $fieldList) ? $alias.'.id' : $alias.'.sort';
            $order = "$alias.community_id,$alias.grid_group_id,$alias.grid_id,".$order;
            $subQuery = $this->model->alias($alias)->field($alias.'.id')->where($where)->order($order)->limit($limit)->page($page)->buildSql();
            $subQuery = '( SELECT object.id FROM ' . $subQuery . ' AS object )';
            $list = $this->model->alias($alias)
                ->join("sa_community c",$alias.".community_id=c.id","LEFT")
                ->join("sa_grid g",$alias.".grid_id=g.id","LEFT")
                ->join("sa_grid gg",$alias.".grid_group_id=gg.id","LEFT")
                ->field($alias.".*,g.grid_name,gg.grid_name grid_group_name,c.community_name")
                ->where($alias.'.id in' . $subQuery)->order($order)->select()->toArray();
            return $this->success($this->model->getLastSql(), '/', $list, $count);
        }
        return $this->view();
    }

    public function proData()
    {
        if (request()->isAjax()) {
            $page = (int)input('page', 1);
            $limit = (int)input('limit', 18);
            $alias = "e";
            $where = $this->buildSelectParams($alias.".");
            $key = input("check_type",0);
            $check_where = $this->buildCheckWhere("$alias.");
            $count = $this->model->alias($alias)->where($where)->where($check_where)->count();
            //$page = $count <= $limit ? 1 : $page;
            $fieldList = $this->model->getFields();
            $order = !array_key_exists('sort', $fieldList) ? $alias.'.id' : $alias.'.sort';
            $order = "$alias.community_id,$alias.grid_group_id,$alias.grid_id,".$order;
            $subQuery = $this->model->alias($alias)->field($alias.'.id')->where($where)->where($check_where)->order($order)->limit($limit)->page($page)->buildSql();
            $subQuery = '( SELECT object.id FROM ' . $subQuery . ' AS object )';
            $list = $this->model->alias($alias)
                ->join("sa_community c",$alias.".community_id=c.id","LEFT")
                ->join("sa_grid g",$alias.".grid_id=g.id","LEFT")
                ->join("sa_grid gg",$alias.".grid_group_id=gg.id","LEFT")
                ->field("'{$this->checkRule[$key]['title']}' check_name, ".$alias.".*,g.grid_name,gg.grid_name grid_group_name,c.community_name")
                ->where($alias.'.id in' . $subQuery)->order($order)->select()->toArray();
            return $this->success($this->model->getLastSql(), '/', $list, $count);
        }
        return $this->view('',['checkRule'=>$this->checkRule]);
    }

    public function dataImport()
    {
        if (request()->isPost()) {
            set_time_limit(0);
            $filePath = input('file_1');
            $msg = "";
            $spreadsheet = IOFactory::load('public'.$filePath);
            //获取活动工作表
            $worksheet = $spreadsheet->getActiveSheet();
            $Dictionary = [];
            foreach ($this->columnFormat as $key => $item) {
                if($item[0]==3){
                    $dataList = getDictionary($item[2]);
                    $Dictionary[$item[2]] = $dataList;
                }
            }
            $admin_id = get_admin_id();
            $columns_arr=[];
            $update_field = [];
            foreach ($this->columnFormat as $vo){
                if ($vo[0] < 1 || $vo[0] > 4) {
                    continue;
                }
                $columns_arr[$vo[1]]=$vo[1];
                if(array_key_exists('column',$vo)){
                    $vo[1] = $vo['column'];
                }
                $update_field[] = $vo[1];
            }
            $create_time=date('Y-m-d H:i:s');
            $create_by = $this->admin_info['name'];
            $columns=implode(",",$columns_arr);
            $columns .= ",sort,admin_id";
            $insert_sql = "INSERT INTO ".$this->table_name."(".$columns.") VALUES ";
            $values = [];
            Db::query("delete from ".$this->table_name." where admin_id=".$admin_id);
            $error_arr = [];
            foreach ($worksheet->getRowIterator() as $i=>$row) {
                if ($i < $this->row_index||empty($worksheet->getCell('A'.$i)->getValue())) {
                    continue;
                }
                $cellIterator = $row->getCellIterator();
                $cellIterator->setIterateOnlyExistingCells(false); // 遍历所有单元格，包括空单元格
                $values_arr = [];
                foreach ($cellIterator as $key => $cell) {
                    $value = $cell->getValue();
                    if(empty($this->columnFormat[$key])){
                        return $this->error("存在未定义的excel列，请检查导入表格是否和模板表格格式相同");
                    }
                    if(array_key_exists('required',$this->columnFormat[$key])&&empty($value)){
                        $error_arr[$i].="第{$key}列必填项为空;";
                    }
                    $vo = $this->columnFormat[$key];
                    if (($vo[0] < 1&&$vo != -3) || $vo[0] > 5) {
                        continue;
                    } else {
                        switch ($vo[0]) {
                            case 2:
                                $value = $value == '是' ? 1 : 0;
                                break;
                            case 3:
                                $key = array_search($value, $Dictionary[$vo[2]]);
                                if ($key !== false) {
                                    $value = $key;
                                } else {
                                    $value = 0;
                                }
                                break;
                        }
                        if(array_key_exists($vo[1], $values_arr)&&empty($value)){
                            continue;
                        }
                        $values_arr[$vo[1]] = "\"".$value."\"";
                    }
                }
                $values_sql = "(".implode(",",$values_arr).",".($i-$this->row_index+1).",".$admin_id.")";
                $values[] = $values_sql;
                $values_arr = [];
                if($i%1000==0){
                    try{
                        Db::query($insert_sql . implode(",", $values));
                        $values = [];
                    }
                    catch (\Exception $e){
                        return $this->error($e->getMessage());
                    }
                }
            }
            if(count($values)){
                Db::query($insert_sql.implode(",",$values));
            }
            Db::query("UPDATE ".$this->table_name." i INNER JOIN sa_community c on i.community_name=c.community_name set i.community_id=c.id");
            Db::query("UPDATE ".$this->table_name." i INNER JOIN sa_grid g on i.community_id=g.community_id and i.grid_group_name=g.grid_name set i.grid_group_id=g.id");
            Db::query("UPDATE ".$this->table_name." i INNER JOIN sa_grid g on i.community_id=g.community_id and i.grid_name=g.grid_name set i.grid_id=g.id");
            Db::query("UPDATE ".$this->table_name." i INNER JOIN ".$this->table_source." b on i.id_code=b.id_code and i.vehicle_code=b.vehicle_code set i.".$this->pk."=b.id");
            $list = Db::table($this->table_name)->where("admin_id",$admin_id)->where("community_id",0)->field("sort")->select()->toArray();
            foreach ($list as $item){
                $error_arr[$item['sort']+$this->row_index-1].="找不到对应的社区信息;";
            }
            $list = Db::table($this->table_name)->where("admin_id",$admin_id)->where("grid_group_id",0)->field("sort")->select()->toArray();
            foreach ($list as $item){
                $error_arr[$item['sort']+$this->row_index-1].="找不到对应的网格组信息;";
            }
            $list = Db::table($this->table_name)->where("admin_id",$admin_id)->where("grid_id",0)->field("sort")->select()->toArray();
            foreach ($list as $item){
                $error_arr[$item['sort']+$this->row_index-1].="找不到对应的网格信息;";
            }
            if(count($error_arr)>0){
                return $this->error("信息校验未通过",'',['data'=>$error_arr]);
            }

            try{
                $sql = $this->buildUpdSql($admin_id);
                Db::query($sql);
                $field=implode(",",$update_field);
                $field.=",sort";
                $insert_sql = "INSERT INTO ".$this->table_source." ({$field},create_time,create_by) select {$field},'$create_time' create_time,'$create_by' create_by from ".$this->table_name." where admin_id = {$admin_id} and ".$this->pk." = 0";
                Db::query($insert_sql);
            }catch (\Exception $e){

                return $this->error($e->getMessage());
            }

            return $this->success( '导入成功');
        }
        return $this->view();
    }

    public function excelExport($list = [])
    {
        // 文件路径
        $filePath = 'public/tpl/'.$this->tpl_name.'.xlsx';

        // 读取Excel文件
        $spreadsheet = IOFactory::load($filePath);

        // 获取活动工作表
        $sheet = $spreadsheet->getActiveSheet();
        $excelService = new ExcelService();
        $integerValidation = $excelService->integerValidation();
        $decimalValidation = $excelService->decimalValidation();
        $idcardValidation = $excelService->idcardValidation();
        $count = 1000;
        $dataList = ['是', '否'];
        $optionValidation = $excelService->optionValidation($dataList);
        $Dictionary = [];
        foreach ($this->columnFormat as $key=>$item) {
            if($item[0]==2||$item[0]==-2){
                $cellRange = $key .$this->row_index. ':' . $key . $count;
                $sheet->setDataValidation($cellRange, $optionValidation);
            }
            if($item[0]==3||$item[0]==-3){
                $cellRange = $key . $this->row_index. ':' . $key . $count;
                $dataList = getDictionary($item[2]);
                $Dictionary[$item[2]] = $dataList;
                $dataList = array_values($dataList);
                $optionListValidation = $excelService->optionValidation($dataList);
                $sheet->setDataValidation($cellRange, $optionListValidation);
            }
            if(array_key_exists('number',$item)){
                $cellRange = $key.$this->row_index. ':' .$key. $count;
                $sheet->setDataValidation($cellRange, $integerValidation);
            }
        }
        if (count($list) > 0) {
            $i = $this->row_index;
            foreach ($list as $item) {
                $id_info = getInfoFromIdCard($item['id_code']);
                foreach ($this->columnFormat as $column => $value) {
                    switch ($value[0]) {
                        case 1:
                            $item[$value[1]]=$item[$value[1]]=='0000-00-00'?'':$item[$value[1]];
                            $sheet->setCellValueExplicit($column . $i, trim((string)$item[$value[1]], "\""), DataType::TYPE_STRING);
                            break;
                        case 2:
                        case -2:
                            $sheet->setCellValue($column . $i, $item[$value[1]] == 1 ? '是' : '否');
                            break;
                        case 3:
                        case -3:
                            $index = $item[$value[1]];
                            if ($index) {
                                $sheet->setCellValue($column . $i, empty($Dictionary[$value[2]][$index]) ? '' : $Dictionary[$value[2]][$index]);
                            }
                            break;
                        case 6:
                            $sheet->setCellValue($column . $i, $id_info[$value[1]]);
                            break;
                        case 5:
                            $sheet->setCellValue($column . $i, $item[$value[1]] != $value[2] ? '是' : '否');
                            break;
                        case 4:
                            $value = $item[$value[1]];
                            $sheet->setCellValueExplicit($column . $i, trim((string)$value, "\""), DataType::TYPE_STRING);
                            break;
                        case 0:
                            $sheet->setCellValue($column . $i, $value[1]);
                            break;
                    }
                }
                $i++;
            }
            // 保存Excel文件
            $writer = new Xlsx($spreadsheet);
            $day = date('Ymd');
            $time = date('his');
            $dir = '/download/' . $day;
            if (!file_exists('public' . $dir)) {
                mkdir('public' . $dir, 0777, true);
            }
            $filePath = $dir . '/'.$this->tpl_name.'(' . $day . $time . ').xlsx';
            $writer->save('public' . $filePath);
            return $filePath;
        }
        // 保存Excel文件
        $writer = new Xlsx($spreadsheet);
        $writer->save('public/tpl/'.$this->tpl_name.'.xlsx');
        return '';
    }

    public function downloadExcelTpl()
    {
        $this->excelExport();
        return $this->redirect('/tpl/'.$this->tpl_name.'.xlsx');
    }

    public function buildUpdSql($admin_id){
        $this->tableFields = $this->model->getFields();
        $fieldColumn="UPDATE ".$this->table_source." b INNER JOIN ".$this->table_name." i on i.".$this->pk."=b.id and i.admin_id=".$admin_id." set ";
        foreach ($this->columnFormat as $item){
            $field = $item[1];
            if (!array_key_exists($field, $this->tableFields)) {
                continue;
            }
            if (array_key_exists('column',$item)){
                $field = $item['column'];
            }
            $fieldColumn.="b.".$field."=i.".$field.",";
        }
        $update_time=date('Y-m-d H:i:s');
        $update_by = $this->admin_info['name'];
        return $fieldColumn."b.sort=i.sort,b.update_time='$update_time',b.update_by='$update_by';";
    }

    public function dataToExcel()
    {
        $alias = "h";
        $where = $this->buildSelectParams($alias.".");
        $keyword = input('keyword','');
        if($keyword){
            $where[] = [$alias.".vehicle_code|".$alias.".id_code|".$alias.".owner", 'like', '%' . $keyword . '%'];
        }
        $fieldList = $this->model->getFields();
        $order = !array_key_exists('sort', $fieldList) ? 'id' : 'sort';
        $order = "$alias.community_id,$alias.grid_group_id,$alias.grid_id,".$order;
        $list = $this->model->alias($alias)
            ->join("sa_community c",$alias.".community_id=c.id","LEFT")
            ->join("sa_grid g",$alias.".grid_id=g.id","LEFT")
            ->join("sa_grid gg",$alias.".grid_group_id=gg.id","LEFT")
            ->field($alias.".*,g.grid_name,gg.grid_name grid_group_name,c.community_name")
            ->where($where)->order($order)->select()->toArray();
        $filePath = $this->excelExport($list);
        if(request()->isPost()){
            return $this->success('',$filePath);
        }
        return $this->redirect($filePath);
    }



}
