<?php
declare (strict_types = 1);
namespace app\admin\controller;

use app\AdminController;
use support\Response;
use think\helper\Str;
use Webman\Http\Request;
use app\common\model\Organization as OrganizationModel;
use app\common\model\Community;
use app\common\model\OrganizationPerson;

/**
 * Organization
 * 街道管理
 * <!---->
 * Class Organization
 * @package app\admin\controller
 */
class Organization extends AdminController
{
    /**
     * Organization模型对象
     * @var OrganizationModel
     */

    public function __construct()
    {
        parent::__construct();
        $this->model = new OrganizationModel;
    }

    public function add()
    {
        if (request()->isPost()) {

            $post = $this->preRuleOutFields(\request()->post());
            if ($this->dataLimit) {
                $post[$this->dataLimitField] = get_admin_id();
            }

            $validate = $this->isValidate ? get_class($this->model) : $this->isValidate;
            $post = request_validate_rules($post, $validate, $this->scene);
            if (empty($post) || !is_array($post)) {
                return $this->error($post);
            }
            $post = dataArrayToJson($post);
            if(!empty($this->model->create_by)){
                $post[$this->model->create_by]=$this->admin_info['name'];
            }
            if(!empty($this->model->createTime)){
                $post[$this->model->createTime]=date('Y-m-d H:i:s');
            }
            $this->status = $this->model->create($post);
            return $this->status ? $this->success() : $this->error();
        }
        $params = request()->all();
        $pid = $params['pid'];
        if(empty($pid)){
            return $this->error("请选择上级");
        }
        $params['p_name']=$this->model->where("id",$pid)->value('org_name');
        return $this->view('', [
            'data' => $this->getTableFields(),
            'params'=>$params]);
    }

    public function edit()
    {
        $id = input('id');
        $data = $this->model->where('id', $id)->findOrEmpty()->toArray();

        // 限制数据调用
        if (!$this->authService->SuperAdmin() && $this->dataLimit
            && in_array($this->dataLimitField, $this->model->getFields())) {
            if ($data[$this->dataLimitField] != get_admin_id()) {
                return $this->error('没有权限');
            }
        }

        if (request()->isPost()) {
            $post = $this->preRuleOutFields(\request()->post());
            $validate = $this->isValidate ? get_class($this->model) : $this->isValidate;
            $post = request_validate_rules($post, $validate, $this->scene);
            if (empty($post) || !is_array($post)) {
                return $this->error($post);
            }
            $post = dataArrayToJson($post);
            if(!empty($this->model->update_by)){
                $post[$this->model->update_by]=$this->admin_info['name'];
            }
            $this->status = $this->model->update($post);
            return $this->status ? $this->success() : $this->error();
        }
        $params = request()->all();
        $params['p_name']=$this->model->where("id",$data['pid'])->value('org_name');
        /**
         * 默认共享模板
         */
        $template = str_replace('/_', '/', Str::snake(request()->getController()));
        return $this->view($template . '/add', [
            'data' => $data,
            'params'=>$params
        ]);
    }

    /**
     * 获取资源列表
     * @return Response
     */
    public function index()
    {
        if (request()->isAjax()) {
            $page = (int)input('page', 1);
            $limit = (int)input('limit', 18);
            $community_id = (int)input('community_id', 0);
            if(empty($community_id)){
                return $this->success('查询成功', '/', [], 0);
            }
            $where = $this->buildSelectParams();
            $where[] = ['id','not in', '5,6'];
            $count = $this->model->where($where)->count();
            //$page = $count <= $limit ? 1 : $page;
            $fieldList = $this->model->getFields();
            $order = !array_key_exists('sort', $fieldList) ? 'id' : 'sort';
            $subQuery = $this->model->field('id')->where($where)->order($order)->limit($limit)->page($page)->buildSql();
            $subQuery = '( SELECT object.id FROM ' . $subQuery . ' AS object )';
            $community_name = Community::where("id",$community_id)->value("community_name");
            $list = $this->model->field("id,org_name,pid, $community_id community_id,'$community_name' community_name" )->where('id in' . $subQuery)->order($order)->select()->toArray();
            $list[]=['id'=>5,'pid'=>0,'org_name'=>'网格组长','community_id'=>$community_id];
            $list[]=['id'=>6,'pid'=>0,'org_name'=>'网格员','community_id'=>$community_id];
            $list = list_to_tree2($list);
            return $this->success('查询成功', '/', $list, $count);
        }

        return $this->view();
    }

}
