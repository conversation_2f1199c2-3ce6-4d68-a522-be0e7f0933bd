<?php
declare (strict_types = 1);
namespace app\admin\controller;

use app\admin\service\ExcelService;
use app\AdminController;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use support\Response;
use think\helper\Str;
use Webman\Http\Request;
use app\common\model\PersonCommunist as PersonCommunistModel;
use app\admin\model\Person;
use think\facade\Db;

/**
 * person_vehicle
 * 车辆管理
 * <!---->
 * Class PersonVehicle
 * @package app\admin\controller
 */
class PersonCommunist extends AdminController
{
    /**
     * PersonVehicle模型对象
     * @var \app\common\model\PersonCommunist
     */

    public function __construct()
    {
        parent::__construct();
        $this->model = new PersonCommunistModel;
        $this->columnFormat=[
            'A' => [0, 'index'],
            'B' => [1, 'community_name','column'=>'community_id'],//所在社区
            'C' => [1, 'grid_group_name','column'=>'grid_group_id'],//所在网格组
            'D' => [1, 'grid_name','column'=>'grid_id'],//所在网格
            'E' => [3, 'jurisdiction',501,'required'=>1],//管辖类别
            'F' => [1, 'name'],//姓名
            'G' => [6, 'gender'],//性别
            'H' => [3, 'nation', 54],//民族
            'I' => [6, 'birthday'],//出生年月日
            'J' => [4, 'id_code'],//身份证号
            'K' => [6, 'age'],//年龄
            'L' => [1,'work_date'],//工作时间
            'M' => [1,'join_date'],//入党时间
            'N' => [1,'join_age'],
            'O' => [3, 'education', 56],//文化程度
            'P' => [1, 'school'],//毕业院校
            'Q' => [1, 'major'],//所学专业
            'R' => [1, 'technical_title'],//技术职称
            'S' => [1, 'hometown'],//籍贯
            'T' => [1, 'level'],//级别
            'U' => [2, 'working'],//在岗情况
            'V' => [1, 'address'],//居住地
            'W' => [1, 'post'],//职务
            'X' => [4, 'phone'],//电话
            'Y' => [1,'communist_post'],//党内职务
            'Z' => [1, 'old_unit'],//原或现单位
            'AA' => [1, 'communist_branch'],//所属党支部
            'AB' => [1, 'wechat_code'],//微信号
            // 'AC' => [1,'grid_user'],
            // 'AD' => [1,'grid_phone'],
        ];
        $this->tpl_name = "党员模版";
        $this->table_name = 'sa_person_communist_imp';
        $this->table_source = "sa_person_communist";
        $this->pk = "person_id";
        $this->row_index = 3;
    }

    /**
     * 添加资源
     * @return Response|void
     */
    public function add()
    {
        if (request()->isPost()) {

            $post = $this->preRuleOutFields(\request()->post());
            if ($this->dataLimit) {
                $post[$this->dataLimitField] = get_admin_id();
            }

            $validate = $this->isValidate ? get_class($this->model) : $this->isValidate;
            $post = request_validate_rules($post, $validate, $this->scene);
            if (empty($post) || !is_array($post)) {
                return $this->error($post);
            }
            if(empty($post['person_id'])){
                return $this->error('请调取人口数据！');
            }
            $post = dataArrayToJson($post);
            if(!empty($this->model->create_by)){
                $post[$this->model->create_by]=$this->admin_info['name'];
            }
            if(!empty($this->model->createTime)){
                $post[$this->model->createTime]=date('Y-m-d H:i:s');
            }
            $this->status = $this->model->create($post);
            $Person = new Person();
            $Person->rbacReverse($post[$this->pk],$this->table_source);
            return $this->status ? $this->success() : $this->error();
        }
        $data = $this->getTableFields();
        $Communities = Db::name("community")->select()->toArray();
        $grid_groups = Db::name("grid")->where(["community_id"=>$data['community_id'],'grid_group'=>0])->select()->toArray();
        $grids = Db::name("grid")->where(["grid_group"=>$data['grid_group_id']])->select()->toArray();
        return $this->view('', ['data' => $data,
            'Communities'=>$Communities,
            'grid_groups'=>$grid_groups,
            'grids'=>$grids
        ]);
    }

    /**
     * 编辑资源
     * @return Response|void
     */
    public function edit()
    {
        $id = input('id');
        $data = $this->model->where('id', $id)->findOrEmpty()->toArray();

        // 限制数据调用
        if (!$this->authService->SuperAdmin() && $this->dataLimit
            && in_array($this->dataLimitField, $this->model->getFields())) {
            if ($data[$this->dataLimitField] != get_admin_id()) {
                return $this->error('没有权限');
            }
        }

        if (request()->isPost()) {
            $post = $this->preRuleOutFields(\request()->post());
            $validate = $this->isValidate ? get_class($this->model) : $this->isValidate;
            $post = request_validate_rules($post, $validate, $this->scene);
            if (empty($post) || !is_array($post)) {
                return $this->error($post);
            }
            if(empty($post['person_id'])){
                return $this->error('请调取人口数据！');
            }
            $post = dataArrayToJson($post);
            if(!empty($this->model->update_by)){
                $post[$this->model->update_by]=$this->admin_info['name'];
            }
            $this->status = $this->model->update($post);
            $Person = new Person();
            $Person->rbacReverse($post[$this->pk],$this->table_source);
            return $this->status ? $this->success() : $this->error();
        }

        /**
         * 默认共享模板
         */
        $template = str_replace('/_', '/', Str::snake(request()->getController()));
        $Communities = Db::name("community")->select()->toArray();
        $grid_groups = Db::name("grid")->where(["community_id"=>$data['community_id'],'grid_group'=>0])->select()->toArray();
        $grids = Db::name("grid")->where(["grid_group"=>$data['grid_group_id']])->select()->toArray();
        return $this->view($template . '/add', [
            'data' => $data,
            'Communities'=>$Communities,
            'grid_groups'=>$grid_groups,
            'grids'=>$grids
        ]);
    }

    public function del()
    {
        $id = input('id');
        if (!is_array($id)) {
            $id = [$id];
        }
        try {
            $list = $this->model->whereIn('id', $id)->select()->toArray();
            foreach ($list as $item) {
                $count = $this->model->where("person_id",$item['person_id'])->count();
                if(empty($count)){
                    Db::name("person")->where("id",$item["person_id"])->update(['face'=>5]);
                }
                $this->model->where("id",$item['id'])->delete();
                $this->status = true;
            }
        } catch (\Throwable $th) {
            $this->status = false;
            return $this->error($th->getMessage());
        }

        return $this->status ? $this->success() : $this->error();
    }

    /**
     * index 函数
     */
    public function index()
    {
        if (request()->isAjax()||request()->isPost()) {
            $page = (int)input('page', 1);
            $limit = (int)input('limit', 18);
            $alias = "e";
            $this->keepField='sex';
            $this->filterWhere = ['page', 'limit', 'working','retire'];
            $where = $this->buildSelectParams($alias.".");
            $keyword = input('keyword','');
            $working = input('working',0);
            $retire = input('retire',0);
            if($keyword){
                $where[] = ["p.name|"."p.id_code", 'like', '%' . $keyword . '%'];
            }
            if($working){
                $working = $working -1;
                $where["$alias.working"] = $working;
            }
            if($retire){
                $retire = $retire - 1;
                $where["p.retire"] = $retire;
            }
            $count = $this->model->alias($alias)
                ->join("sa_person p",$alias.".person_id=p.id and (p.delete_time is null or (p.delete_flag=1 and p.grid_id <> $alias.grid_id)) and dead = 0")
                ->where($where)->count();
            //$page = $count <= $limit ? 1 : $page;
            $fieldList = $this->model->getFields();
            $order = !array_key_exists('sort', $fieldList) ? $alias.'.id' : $alias.'.sort';
            $order = "$alias.community_id,$alias.grid_group_id,$alias.grid_id,".$order;
            $subQuery = $this->model->alias($alias)
                ->join("sa_person p",$alias.".person_id=p.id")
                ->field($alias.'.id')->where($where)->order($order)->limit($limit)->page($page)->buildSql();
            $subQuery = '( SELECT object.id FROM ' . $subQuery . ' AS object )';
            $list = $this->model->alias($alias)
                ->join("sa_community c",$alias.".community_id=c.id","LEFT")
                ->join("sa_grid g",$alias.".grid_id=g.id","LEFT")
                ->join("sa_grid gg",$alias.".grid_group_id=gg.id","LEFT")
                ->join("sa_person p",$alias.".person_id=p.id and (p.delete_time is null or (p.delete_flag=1 and p.grid_id <> $alias.grid_id)) and dead = 0")
                ->join("sa_building b", "p.building_id=b.id","LEFT")
                ->field($alias.".*,g.grid_name,gg.grid_name grid_group_name,c.community_name,
                p.name,p.id_code,p.age,p.sex,p.address,p.face,p.nation,p.education,p.school,p.major,p.hometown,p.phone,b.building_name")
                ->where($alias.'.id in' . $subQuery)->order($order)->select()->toArray();
            return $this->success($this->model->getLastSql(), '/', $list, $count);
        }else{
            $this->noRepeat();
        }
        return $this->view();
    }


    public function dataToExcel()
    {
        $alias = "h";
        $this->filterWhere = ['page', 'limit', 'working','retire'];
        $where = $this->buildSelectParams($alias.".");
        $keyword = input('keyword','');
        $working = input('working',0);
        $retire = input('retire',0);
        if($keyword){
            $where[] = ["p.name|"."p.id_code", 'like', '%' . $keyword . '%'];
        }
        if($working){
            $working = $working -1;
            $where["$alias.working"] = $working;
        }
        if($retire){
            $retire = $retire - 1;
            $where["p.retire"] = $retire;
        }
        $fieldList = $this->model->getFields();
        $order = !array_key_exists('sort', $fieldList) ? $alias.'.id' : $alias.'.sort';
        $order = "$alias.community_id,$alias.grid_group_id,$alias.grid_id,".$order;
        $list = $this->model->alias($alias)
            ->join("sa_person p", $alias.".person_id=p.id and p.delete_time is null and dead = 0")
            ->join("sa_community c",$alias.".community_id=c.id","LEFT")
            ->join("sa_grid g",$alias.".grid_id=g.id","LEFT")
            ->join("sa_grid gg",$alias.".grid_group_id=gg.id","LEFT")
            ->field("$alias.id,$alias.jurisdiction,$alias.work_date,$alias.join_date,$alias.join_age,$alias.technical_title,$alias.level,
            $alias.working,$alias.post,$alias.communist_post,$alias.communist_branch,$alias.old_unit,$alias.grid_user,$alias.grid_phone,
            g.grid_name,gg.grid_name grid_group_name,c.community_name,
            p.name,p.id_code,p.birth,p.sex,p.age,p.nation,p.education,p.school,p.major,p.hometown,p.address,p.phone,p.wechat_code,p.health")
            ->where($where)->order($order)->select()->toArray();
        $filePath = $this->excelExport($list);
        if(request()->isPost()){
            return $this->success('',$filePath);
        }
        return $this->redirect($filePath);
    }

}
