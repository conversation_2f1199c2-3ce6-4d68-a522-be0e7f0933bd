<?php
declare (strict_types = 1);
namespace app\admin\controller;

use app\admin\service\ExcelService;
use app\AdminController;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use support\Response;
use think\helper\Str;
use Webman\Http\Request;
use app\common\model\House as HouseModel;
use think\facade\Db;

/**
 * house
 * 实有房屋管理
 * <!---->
 * Class House
 * @package app\admin\controller
 */
class House extends AdminController
{
    protected array $columnFormat;
    protected string $tpl_name;
    protected string $table_name;
    protected string $table_source;
    protected int $row_index;
    protected string $pk;

    /**
     * House模型对象
     * @var \app\common\model\House
     */

    public function __construct()
    {
        parent::__construct();
        $this->model = new HouseModel;
        $this->columnFormat=[
            'A' => [0, '建三江街道办事处'],
            'B' => [1, 'community_name','column'=>'community_id'],
            'C' => [1, 'grid_group_name','column'=>'grid_group_id'],
            'D' => [1, 'grid_name','column'=>'grid_id'],
            'E' => [1, 'building_name','column'=>'building_id'],
            'F' => [1, 'unit','number'=>'1','required'=>'1'],
            'G' => [1, 'floor','number'=>'1','required'=>'1'],
            'H' => [1, 'street'],
            'I' => [1, 'code','required'=>'1'],
            'J' => [1, 'area'],
            'K' => [3, 'property_right',349,'required'=>'1'],
            'L' => [3, 'usage_category',355,'required'=>'1'],
            'M' => [3, 'house_type',362,'required'=>'1'],
            'N' => [3, 'residence_nature',367,'required'=>'1'],
            'O' => [1, 'owner_name','required'=>'1'],
            'P' => [4, 'owner_code'],
            'Q' => [4, 'owner_phone'],
            'R' => [2, 'rent'],
            'S' => [1, 'rent_name'],
            'T' => [4, 'rent_code'],
            'U' => [4, 'rent_phone'],
            'V' => [1, 'rent_start','date'=>1],
            'W' => [1, 'rent_end','date'=>1],
            'X' => [3, 'rent_used',373],
            'Y' => [3, 'relation',378],
            'Z' => [1, 'details']
        ];
        $this->tpl_name = "房屋模版";
        $this->table_name = 'sa_house_imp';
        $this->table_source = "sa_house";
        $this->pk = "house_id";
        $this->row_index = 2;
        $this->filterWhere = ['road_start','road_end','street_start','street_end'];
        $this->checkRule=[
            ['type'=>'required','title'=>'必填项未填','columns'=>"community_id,grid_group_id,grid_id,building_id,code,owner_name,property_right,unit,floor,usage_category,house_type,residence_nature"],
            ['type'=>'compare','title'=>'日期范围','columns'=>['start'=>'rent_start','end'=>'rent_end']],
            ['type'=>'beforeToday','title'=>'日期(大于当日)','columns'=>"rent_start"]
        ];
    }

    /**
     * 添加资源
     * @return Response|void
     */
    public function add()
    {
        if (request()->isPost()) {

            $post = $this->preRuleOutFields(\request()->post());
            if ($this->dataLimit) {
                $post[$this->dataLimitField] = get_admin_id();
            }

            $validate = $this->isValidate ? get_class($this->model) : $this->isValidate;
            $post = request_validate_rules($post, $validate, $this->scene);
            if (empty($post) || !is_array($post)) {
                return $this->error($post);
            }
            $post = dataArrayToJson($post);
            if($post['owner_name']==$post['rent_name']){
                return $this->error("承租人与产权人不能为同一人");
            }
            if(!empty($this->model->create_by)){
                $post[$this->model->create_by]=$this->admin_info['name'];
            }
            if(!empty($this->model->createTime)){
                $post[$this->model->createTime]=date('Y-m-d H:i:s');
            }
            $sort_where['building_id']=$post['building_id'];
            $sort_where['unit']=$post['unit'];
            $sort_where['floor']=$post['floor'];
            $sort_where[]= ['code', "<=", $post['code']];
            $post['sort'] = $this->model->where($sort_where)->max("sort");
            $post['sort']++;
            $this->status = $this->model->create($post);
            return $this->status ? $this->success() : $this->error();
        }
        $data = $this->getTableFields();
        $Communities = Db::name("community")->select()->toArray();
        $grid_groups = Db::name("grid")->where(["community_id"=>$data['community_id'],'grid_group'=>0])->select()->toArray();
        $grids = Db::name("grid")->where(["grid_group"=>$data['grid_group_id']])->select()->toArray();
        return $this->view('', ['data' => $data,
            'Communities'=>$Communities,
            'grid_groups'=>$grid_groups,
            'grids'=>$grids
        ]);
    }

    /**
     * 编辑资源
     * @return Response|void
     */
    public function edit()
    {
        $id = input('id');
        $data = $this->model->where('id', $id)->findOrEmpty()->toArray();

        // 限制数据调用
        if (!$this->authService->SuperAdmin() && $this->dataLimit
            && in_array($this->dataLimitField, $this->model->getFields())) {
            if ($data[$this->dataLimitField] != get_admin_id()) {
                return $this->error('没有权限');
            }
        }

        if (request()->isPost()) {
            $post = $this->preRuleOutFields(\request()->post());
            $validate = $this->isValidate ? get_class($this->model) : $this->isValidate;
            $post = request_validate_rules($post, $validate, $this->scene);
            if (empty($post) || !is_array($post)) {
                return $this->error($post);
            }
            $post = dataArrayToJson($post);
            if($post['owner_name']==$post['rent_name']){
                return $this->error("承租人与产权人不能为同一人");
            }
            if($post['rent_start']&&strtotime($post['rent_start'])>time()){
                return $this->error("承租日期不能大于今日");
            }

            // 关闭是否出租后，复位承租用途、与产权人关系两个字段，将其值设置为null
            if(!$post['rent']) {
                $post['rent_used'] = null;
                $post['relation'] = null;
            }
            if(!empty($this->model->update_by)){
                $post[$this->model->update_by]=$this->admin_info['name'];
            }
            $this->status = $this->model->update($post);
            return $this->status ? $this->success() : $this->error();
        }

        /**
         * 默认共享模板
         */
        $template = str_replace('/_', '/', Str::snake(request()->getController()));
        $Communities = Db::name("community")->select()->toArray();
        $grid_groups = Db::name("grid")->where(["community_id"=>$data['community_id'],'grid_group'=>0])->select()->toArray();
        $grids = Db::name("grid")->where(["grid_group"=>$data['grid_group_id']])->select()->toArray();
        return $this->view($template . '/add', [
            'data' => $data,
            'Communities'=>$Communities,
            'grid_groups'=>$grid_groups,
            'grids'=>$grids
        ]);
    }

    /**
     * 删除资源
     * @return Response
     */
    public function del()
    {
        $id = input('id');
        if (!is_array($id)) {
            $id = [$id];
        }

        try {
            $list = $this->model->alias("h")
                ->join("sa_building b","h.building_id=b.id","left")
                ->join("sa_person p","h.id=p.house_id and p.delete_time is null","left")
                ->field("h.id,b.building_name,h.unit,h.floor,h.code,b.building_name,COUNT(DISTINCT p.id) AS person_count")
                ->group("h.id")
                ->whereIn('h.id', $id)->select()->toArray();
            foreach ($list as $item) {
                $msg = [];
                if($item['person_count']){
                    $msg[] = "人口:".$item['person_count'];
                }
                if($msg){
                    return $this->error("{$item['building_name']}{$item['floor']}楼{$item['unit']}单元{$item['code']}房间下存在关联信息无法删除(".implode(",",$msg).")");
                }
                $this->model->where("id",$item['id'])->delete();
                $this->status = true;
            }
        } catch (\Throwable $th) {
            $this->status = false;
            return $this->error($th->getMessage());
        }

        return $this->status ? $this->success() : $this->error();
    }

    /**
     * index 函数
     */
    public function index()
    {
        if (request()->isAjax()) {
            $page = (int)input('page', 1);
            $limit = (int)input('limit', 18);
            $alias = "h";
            $this->keepField="rent";
            $where = $this->buildSelectParams($alias.".");
            $road_id = (int)input('road_id');
            $street_id = (int)input('street_id');
            $road_start = (int)input('road_start');
            $road_end = (int)input('road_end');
            $street_start = (int)input('street_start');
            $street_end = (int)input('street_end');
            $keyword = input('keyword','');
            if($road_id){
                $where['b.road_id']=[$road_id];
            }
            if($street_id){
                $where['b.street_id']=[$street_id];
            }
            if($road_start){
                $where[]=['b.road_start','>=',$road_start];
            }
            if($road_end){
                $where[]=['b.road_end','<=',$road_end];
            }
            if($street_start){
                $where[]=['b.street_start','>=',$street_start];
            }
            if($street_end){
                $where[]=['b.street_end','<=',$street_end];
            }
            if($keyword){
                $where[] = [$alias.".code|b.building_name|".$alias.".owner_name|".$alias.".owner_code|".$alias.".rent_name|".$alias.".rent_code", 'like', '%' . $keyword . '%'];
            }
            $count = $this->model->alias($alias)
                ->join("sa_building b", $alias.".building_id=b.id")->where($where)->count();
            //$page = $count <= $limit ? 1 : $page;
            $fieldList = $this->model->getFields();
            $order = !array_key_exists('sort', $fieldList) ? 'id' : $alias.'.sort';
            $order = "$alias.community_id,$alias.grid_group_id,$alias.grid_id,$alias.building_id,$alias.unit,$alias.floor,".$order;
            $subQuery = $this->model->alias($alias)
                ->join("sa_building b", $alias.".building_id=b.id")
                ->field('h.id')->where($where)->order($order)->limit($limit)->page($page)->buildSql();
            $subQuery = '( SELECT object.id FROM ' . $subQuery . ' AS object )';
            $list = $this->model->alias($alias)
                ->join("sa_community c",$alias.".community_id=c.id","LEFT")
                ->join("sa_grid g",$alias.".grid_id=g.id","LEFT")
                ->join("sa_grid gg",$alias.".grid_group_id=gg.id","LEFT")
                ->join("sa_building b", $alias.".building_id=b.id")
                ->field($alias.".*,g.grid_name,gg.grid_name grid_group_name,c.community_name,b.building_name")
                ->where($alias.'.id in' . $subQuery)->order($order)->select()->toArray();
            foreach($list as $key=>$vo){
                $list[$key]['code']=$vo['code'].implode(" ", addTags($vo,'house'));
            }
            return $this->success($subQuery, '/', $list, $count);
        }
        return $this->view('',['params'=>request()->all()]);
    }

    public function proData()
    {
        if (request()->isAjax()) {
            $page = (int)input('page', 1);
            $limit = (int)input('limit', 18);
            $alias = "h";
            $this->keepField="rent";
            $where = $this->buildSelectParams($alias.".");
            $key = input("check_type",0);
            $check_where = $this->buildCheckWhere("$alias.");
            $count = $this->model->alias($alias)
                ->join("sa_building b", $alias.".building_id=b.id")->where($where)->where($check_where)->count();
            if($key==0&&$count==0){
                $key=1;
                for($key;$key<count($this->checkRule);$key++){
                    $check_where = $this->buildCheckWhere("$alias.",$key);
                    $count = $this->model->alias($alias)
                        ->join("sa_building b", $alias.".building_id=b.id")->where($where)->where($check_where)->count();
                    if($count){
                        break;
                    }
                }
            }
            //$page = $count <= $limit ? 1 : $page;
            $fieldList = $this->model->getFields();
            $order = !array_key_exists('sort', $fieldList) ? 'id' : $alias.'.sort';
            $order = "$alias.community_id,$alias.grid_group_id,$alias.grid_id,$alias.building_id,$alias.unit,$alias.floor,".$order;
            $subQuery = $this->model->alias($alias)
                ->join("sa_building b", $alias.".building_id=b.id")
                ->field('h.id')->where($where)->where($check_where)->order($order)->limit($limit)->page($page)->buildSql();
            $subQuery = '( SELECT object.id FROM ' . $subQuery . ' AS object )';
            $list = $this->model->alias($alias)
                ->join("sa_community c",$alias.".community_id=c.id","LEFT")
                ->join("sa_grid g",$alias.".grid_id=g.id","LEFT")
                ->join("sa_grid gg",$alias.".grid_group_id=gg.id","LEFT")
                ->join("sa_building b", $alias.".building_id=b.id")
                ->field("'{$this->checkRule[$key]['title']}' check_name, ".$alias.".*,g.grid_name,gg.grid_name grid_group_name,c.community_name,b.building_name")
                ->where($alias.'.id in' . $subQuery)->order($order)->select()->toArray();
            foreach($list as $key=>$vo){
                $list[$key]['code']=$vo['code'].implode(" ", addTags($vo,'house'));
            }
            return $this->success('', $key, $list, $count);
        }
        return $this->view('',['checkRule'=>$this->checkRule]);
    }

    public function dataImport()
    {
        if (request()->isPost()) {
            set_time_limit(0);
            $filePath = input('file_1');
            $msg = "";
            $spreadsheet = IOFactory::load('public'.$filePath);
            //获取活动工作表
            $worksheet = $spreadsheet->getActiveSheet();
            $Dictionary = [];
            foreach ($this->columnFormat as $key => $item) {
                if($item[0]==3){
                    $dataList = getDictionary($item[2]);
                    $Dictionary[$item[2]] = $dataList;
                }
            }
            $admin_id = get_admin_id();
            $columns_arr=[];
            $update_field = [];
            foreach ($this->columnFormat as $vo){
                if ($vo[0] < 1 || $vo[0] > 4) {
                    continue;
                }
                $columns_arr[$vo[1]]=$vo[1];
                if(array_key_exists('column',$vo)){
                    $vo[1] = $vo['column'];
                }
                $update_field[] = $vo[1];
            }
            $create_time=date('Y-m-d H:i:s');
            $create_by = $this->admin_info['name'];
            $columns=implode(",",$columns_arr);
            $columns .= ",sort,admin_id";
            $insert_sql = "INSERT INTO ".$this->table_name."(".$columns.") VALUES ";
            $values = [];

            Db::query("delete from ".$this->table_name." where admin_id=".$admin_id);
            $error_arr = [];
            foreach ($worksheet->getRowIterator() as $i=>$row) {
                if ($i < $this->row_index||empty($worksheet->getCell('A'.$i)->getValue())) {
                    continue;
                }
                $cellIterator = $row->getCellIterator();
                $cellIterator->setIterateOnlyExistingCells(false); // 遍历所有单元格，包括空单元格
                $values_arr = [];
                foreach ($cellIterator as $key => $cell) {
                    $value = $cell->getValue();
                    if(empty($this->columnFormat[$key])){
                        return $this->error("存在未定义的excel列，请检查导入表格是否和模板表格格式相同");
                    }
                    if(array_key_exists('required',$this->columnFormat[$key])&&empty($value)){
                        $error_arr[$i].="第{$key}列必填项为空;";
                    }
                    $vo = $this->columnFormat[$key];
                    if (($vo[0] < 1&&$vo != -3) || $vo[0] > 4) {
                        continue;
                    } else {
                        switch ($vo[0]) {
                            case 2:
                                $value = $value == '是' ? 1 : 0;
                                break;
                            case 3:
                                $key = array_search($value, $Dictionary[$vo[2]]);
                                if ($key !== false) {
                                    $value = $key;
                                } else {
                                    $value = 0;
                                }
                                break;
                        }
                        if(array_key_exists($vo[1], $values_arr)&&empty($value)){
                            continue;
                        }
                        $values_arr[$vo[1]] = "\"".$value."\"";
                    }
                }
                $values_sql = "(".implode(",",$values_arr).",".($i-$this->row_index+1).",".$admin_id.")";
                $values[] = $values_sql;
                $values_arr = [];
                if($i%1000==0){
                    Db::query($insert_sql.implode(",",$values));
                    $values = [];
                }
            }
            if(count($values)){
                Db::query($insert_sql.implode(",",$values));
            }
            Db::query("UPDATE ".$this->table_name." i INNER JOIN sa_community c on i.community_name=c.community_name set i.community_id=c.id");
            Db::query("UPDATE ".$this->table_name." i INNER JOIN sa_grid g on i.community_id=g.community_id and i.grid_group_name=g.grid_name set i.grid_group_id=g.id");
            Db::query("UPDATE ".$this->table_name." i INNER JOIN sa_grid g on i.community_id=g.community_id and i.grid_name=g.grid_name set i.grid_id=g.id");
            Db::query("UPDATE ".$this->table_name." i INNER JOIN sa_building b on i.grid_id = b.grid_id and i.building_name=b.building_name set i.building_id=b.id");
            Db::query("UPDATE ".$this->table_name." i INNER JOIN ".$this->table_source." b on i.building_id=b.building_id and i.unit=b.unit and i.code=b.code set i.".$this->pk."=b.id");
            $this->dateFormatUpd($admin_id);
            $list = Db::table($this->table_name)->where("admin_id",$admin_id)->where("community_id",0)->field("sort")->select()->toArray();
            foreach ($list as $item){
                $error_arr[$item['sort']+$this->row_index-1].="找不到对应的社区信息;";
            }
            $list = Db::table($this->table_name)->where("admin_id",$admin_id)->where("grid_group_id",0)->field("sort")->select()->toArray();
            foreach ($list as $item){
                $error_arr[$item['sort']+$this->row_index-1].="找不到对应的网格组信息;";
            }
            $list = Db::table($this->table_name)->where("admin_id",$admin_id)->where("grid_id",0)->field("sort")->select()->toArray();
            foreach ($list as $item){
                $error_arr[$item['sort']+$this->row_index-1].="找不到对应的网格信息;";
            }
            $list = Db::table($this->table_name)->where("admin_id",$admin_id)->where("building_id",0)->field("sort")->select()->toArray();
            foreach ($list as $item){
                $error_arr[$item['sort']+$this->row_index-1].="找不到对应的建筑信息;";
            }
            if(count($error_arr)>0){
                return $this->error("信息校验未通过",'',['data'=>$error_arr]);
            }
            
            try{
                $sql = $this->buildUpdSql($admin_id);
                Db::query($sql);
                $field=implode(",",$update_field);
                $field.=",sort";
                $insert_sql = "INSERT INTO ".$this->table_source." ({$field},create_time,create_by) select {$field},'$create_time' create_time,'$create_by' create_by from ".$this->table_name." where admin_id = {$admin_id} and ".$this->pk." is null or ".$this->pk." = 0";
                Db::query($insert_sql);
            }catch (\Exception $e){
                
                return $this->error($e->getMessage());
            }
            
            return $this->success( '导入成功');
        }
        return $this->view();
    }

    public function excelExport($list = [])
    {
        // 文件路径
        $filePath = 'public/tpl/'.$this->tpl_name.'.xlsx';

        // 读取Excel文件
        $spreadsheet = IOFactory::load($filePath);

        // 获取活动工作表
        $sheet = $spreadsheet->getActiveSheet();
        $excelService = new ExcelService();
        $integerValidation = $excelService->integerValidation();
        $decimalValidation = $excelService->decimalValidation();
        $idcardValidation = $excelService->idcardValidation();
        $count = 1000;
        $dataList = ['是', '否'];
        $optionValidation = $excelService->optionValidation($dataList);
        $Dictionary = [];
        foreach ($this->columnFormat as $key=>$item) {
            if($item[0]==2||$item[0]==-2){
                $cellRange = $key .$this->row_index. ':' . $key . $count;
                $sheet->setDataValidation($cellRange, $optionValidation);
            }
            if($item[0]==3||$item[0]==-3){
                $cellRange = $key . $this->row_index. ':' . $key . $count;
                $dataList = getDictionary($item[2]);
                $Dictionary[$item[2]] = $dataList;
                $dataList = array_values($dataList);
                $optionListValidation = $excelService->optionValidation($dataList);
                $sheet->setDataValidation($cellRange, $optionListValidation);
            }
            if(array_key_exists('number',$item)){
                $cellRange = $key.$this->row_index. ':' .$key. $count;
                $sheet->setDataValidation($cellRange, $integerValidation);
            }
        }
        if (count($list) > 0) {
            $i = $this->row_index;
            foreach ($list as $item) {
                foreach ($this->columnFormat as $column => $value) {
                    switch ($value[0]) {
                        case 1:
                            $item[$value[1]]=$item[$value[1]]=='0000-00-00'?'':$item[$value[1]];
                            $sheet->setCellValueExplicit($column . $i, trim((string)$item[$value[1]], "\""), DataType::TYPE_STRING);
                            break;
                        case 2:
                        case -2:
                            $sheet->setCellValue($column . $i, $item[$value[1]] == 1 ? '是' : '否');
                            break;
                        case 3:
                        case -3:
                            $index = $item[$value[1]];
                            if ($index) {
                                $sheet->setCellValue($column . $i, empty($Dictionary[$value[2]][$index]) ? '' : $Dictionary[$value[2]][$index]);
                            }
                            break;
//                        case 6:
//                            $sheet->setCellValue($column . $i, $id_info[$value[1]]);
//                            break;
                        case 5:
                            $sheet->setCellValue($column . $i, $item[$value[1]] != $value[2] ? '是' : '否');
                            break;
                        case 4:
                            $sheet->setCellValueExplicit($column . $i, $item[$value[1]], DataType::TYPE_STRING);
                            break;
                        case 0:
                            $sheet->setCellValue($column . $i, $value[1]);
                            break;
                    }
                }
                $i++;
            }
            // 保存Excel文件
            $writer = new Xlsx($spreadsheet);
            $day = date('Ymd');
            $time = date('his');
            $dir = '/download/' . $day;
            if (!file_exists('public' . $dir)) {
                mkdir('public' . $dir, 0777, true);
            }
            $filePath = $dir . '/'.$this->tpl_name.'(' . $day . $time . ').xlsx';
            $writer->save('public' . $filePath);
            return $filePath;
        }
        // 保存Excel文件
        $writer = new Xlsx($spreadsheet);
        $writer->save('public/tpl/'.$this->tpl_name.'.xlsx');
        return '';
    }

    public function downloadExcelTpl()
    {
        $this->excelExport();
        return $this->redirect('/tpl/'.$this->tpl_name.'.xlsx');
    }

    public function buildUpdSql($admin_id){
        $this->tableFields = $this->model->getFields();
        $fieldColumn="UPDATE ".$this->table_source." b INNER JOIN ".$this->table_name." i on i.".$this->pk."=b.id and i.admin_id=".$admin_id." set ";
        foreach ($this->columnFormat as $item){
            $field = $item[1];
            if (!array_key_exists($field, $this->tableFields)) {
                continue;
            }
            if (array_key_exists('column',$item)){
                $field = $item['column'];
            }
            $fieldColumn.="b.".$field."=i.".$field.",";
        }
        $update_time=date('Y-m-d H:i:s');
        $update_by = $this->admin_info['name'];
        return $fieldColumn."b.sort=i.sort,b.update_time='$update_time',b.update_by='$update_by';";
    }

    public function dataToExcel()
    {
        $alias = "h";
        $where = $this->buildSelectParams($alias.".");
        $fieldList = $this->model->getFields();
        $order = !array_key_exists('sort', $fieldList) ? 'id' : $alias.'.sort';
        $order = "$alias.community_id,$alias.grid_group_id,$alias.grid_id,$alias.building_id,$alias.unit,$alias.floor,".$order;
        $list = $this->model->alias($alias)
            ->join("sa_community c",$alias.".community_id=c.id","LEFT")
            ->join("sa_grid g",$alias.".grid_id=g.id","LEFT")
            ->join("sa_grid gg",$alias.".grid_group_id=gg.id","LEFT")
            ->join("sa_building b", $alias.".building_id=b.id")
            ->field($alias.".*,g.grid_name,gg.grid_name grid_group_name,c.community_name,b.building_name")
            ->where($where)->order($order)->select()->toArray();
        $filePath = $this->excelExport($list);
        if(request()->isPost()){
            return $this->success('',$filePath);
        }
        return $this->redirect($filePath);
    }

}
