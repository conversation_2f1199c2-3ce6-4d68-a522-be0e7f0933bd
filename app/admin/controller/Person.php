<?php
declare (strict_types=1);

namespace app\admin\controller;

use app\AdminController;
use app\common\model\PersonImp;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use support\Response;
use think\helper\Str;
use app\common\model\Person as PersonModel;
use app\admin\model\Person as PersonAdminModel;
use app\admin\controller\Pending;
use app\common\model\PersonHistory;
use app\common\model\PersonEmployment;
use app\common\model\PersonIncome;
use think\facade\Db;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\IOFactory;
use app\admin\service\ExcelService;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

/**
 * person
 * 实有人口管理
 * <!---->
 * Class Person
 * @package app\admin\controller
 */
class Person extends AdminController
{

    protected array $columnFormat = [];

    /**
     * Person模型对象
     * @var PersonModel
     */

    public function __construct()
    {
        parent::__construct();
        $this->model = new PersonModel;
        // 下方代码2025-05-07周洪利注释
        // $this->columnFormat=[
        //     'A' => [0, '建三江街道办事处'],
        //     'B' => [1, 'community_name','required'=>'1'],
        //     'C' => [1, 'grid_group_name','required'=>'1'],
        //     'D' => [1, 'grid_name','required'=>'1'],
        //     'E' => [1, 'building_name','required'=>'1'],
        //     'F' => [1, 'unit','required'=>'1'],
        //     'G' => [1, 'floor','required'=>'1'],
        //     'H' => [1, 'house_code','required'=>'1'],
        //     'I' => [-2, 'relation','cut'=>1,'required'=>'1'],
        //     'J' => [1, 'name','required'=>'1'],
        //     'K' => [4, 'id_code','required'=>'1'],
        //     'L' => [6, 'birthday'],
        //     'M' => [6, 'age'],
        //     'N' => [6, 'gender'],
        //     'O' => [3, 'nation', 54,'required'=>'1'],
        //     'P' => [1, 're_name'],
        //     'Q' => [3, 'face', 58,'required'=>'1'],
        //     'R' => [3, 'education', 56,'required'=>'1'],
        //     'S' => [1, 'school'],
        //     'T' => [1, 'major'],
        //     'U' => [1, 'graduation_date','date'=>1],
        //     'V' => [3, 'marry', 64,'required'=>'1'],
        //     'W' => [1, 'marry_date','date'=>1],
        //     'X' => [3, 'relation', 53,'required'=>'1'],
        //     'Y' => [4, 'rpr_code'],
        //     'Z' => [2, 'consistency','required'=>'1'],
        //     'AA' => [2, 'dead'],
        //     'AB' => [1, 'dead_date','date'=>1],
        //     'AC' => [3, 'blood', 62],
        //     'AD' => [3, 'religion', 55],
        //     'AE' => [3, 'house_type', 60,'required'=>'1'],
        //     'AF' => [1, 'house_area'],
        //     'AG' => [3, 'rpr', 404,'required'=>'1'],
        //     'AH' => [3, 'rpr_nature', 408,'required'=>'1'],
        //     'AI' => [3, 'person_type', 59,'required'=>'1'],
        //     'AJ' => [2, 'gestational_age'],
        //     'AK' => [1, 'hometown'],
        //     'AL' => [1, 'rpr_address','required'=>'1'],
        //     'AM' => [1, 'address','required'=>'1'],
        //     'AN' => [2, 'single_parent'],
        //     'AO' => [4, 'phone','required'=>'1'],
        //     'AP' => [4, 'phone_1'],
        //     'AQ' => [4, 'phone_2'],
        //     'AR' => [3, 'only_child',65],
        //     'AS' => [1, 'only_child_code'],
        //     'AT' => [3, 'other_child', 66],
        //     'AU' => [3, 'labor_capacity', 63,'required'=>'1'],
        //     'AV' => [3, 'health', 61],
        //     'AW' => [2, 'disability'],
        //     'AX' => [3, 'disability_type', 411],
        //     'AY' => [3, 'disability_level', 418],
        //     'AZ' => [1, 'disability_code'],
        //     'BA' => [2, 'poverty'],
        //     'BB' => [2, 'extreme_poverty'],
        //     'BC' => [3, 'employment_type',440],//从业状态
        //     'BD' => [3, 'not_employment', 529],//未就业原因
        //     'BE' => [1, 'not_employment_other'],//其他
        //     'BF' => [3, 'employment_before', 76],//就业前属于何种人员
        //     'BG' => [1, 'employment_before_other'],//其他
        //     'BH' => [3, 'employment_form', 77],//就业形式
        //     'BI' => [1, 'employment_date','date'=>1],//就业时间
        //     'BJ' => [1, 'employment_unit'],//就业单位
        //     'BK' => [3, 'employment_unit_property', 78],//单位性质
        //     'BL' => [2, 'employment_difficult_reemployment'],//就业困难再就业
        //     'BM' => [2, 'unemployment_reemployment'],//失业人员再就业
        //     'BN' => [3, 'unemployment_difficult_type',489],//就业困难人员类别
        //     'BO' => [1, 'skill_level_certificate'],//技能等级证书名称
        //     'BP' => [1, 'unemployment_unit'],//原工作单位
        //     'BQ' => [1, 'lost_employment_date','date'=>1],//失业时间
        //     'BR' => [3, 'employment_idea', 465],//拟求职就业意向
        //     'BS' => [3, 'accept_employment_service', 471],//拟接受公共就业人才服务内容
        //     'BT' => [1, 'accept_employment_service_other'],//其他
        //     'BU' => [3, 'unemployment_cause', 478],//失业登记原因
        //     'BV' => [1, 'unemployment_cause_other'],//其他
        //     'BW' => [-2, 'employment_difficult_reemployment'],//就业困难再就业
        //     'BX' => [-3, 'unemployment_difficult_type',489],//就业困难人员类别
        //     'BY' => [2, 'lost_employment_subsidy'],//申领失业保险金
        //     'BZ' => [2, 'certificate'],
        //     'CA' => [1, 'certificate_code'],
        //     'CB' => [2, 'retire'],
        //     'CC' => [1, 'retire_unit'],
        //     'CD' => [2, 'insurance_worker'],
        //     'CE' => [2, 'insurance_towns'],
        //     'CF' => [2, 'insurance_village'],
        //     'CG' => [3, 'endowment_insurance', 67],
        //     'CH' => [2, 'low_income_family'],
        //     'CI' => [2, 'low_income_person'],
        //     'CJ' => [2, 'optimal'],
        //     'CK' => [2, 'releaser'],
        //     'CL' => [2, 'martyr'],
        //     'CM' => [2, 'corrector'],
        //     'CN' => [2, 'children_orphan'],
        //     'CO' => [2, 'children_stay'],
        //     'CP' => [2, 'children_abandoned'],
        //     'CQ' => [2, 'children_disability'],
        //     'CR' => [2, 'children_subsistence_allowance'],
        //     'CS' => [2, 'ex_serviceman'],
        //     'CT' => [1, 'soldier_code'],
        //     'CU' => [3, 'enlistment', 57],
        //     'CV' => [2, 'old_oldest'],
        //     'CW' => [6, 'old'],
        //     'CX' => [2, 'temporary_relief'],
        //     'CY' => [2, 'beyond_border'],
        //     'CZ' => [2, 'drugged'],
        //     'DA' => [5, 'nation', 1],
        //     'DB' => [2, 'illness_mental'],
        //     'DC' => [2, 'cult'],
        //     'DD' => [2, 'monk'],
        //     'DE' => [2, 'petition'],
        //     'DF' => [2, 'help_poor'],
        //     'DG' => [2, 'illness_aids'],
        //     'DH' => [2, 'special_support'],
        //     'DI' => [2, 'old_empty'],
        //     'DJ' => [2, 'old_lonely'],
        //     'DK' => [2, 'old_single'],
        //     'DL' => [2, 'overseas'],
        //     'DM' => [2, 'representative_party'],
        //     'DN' => [2, 'representative_people'],
        //     'DO' => [2, 'committee_member'],
        //     'DP' => [2, 'volunteer'],
        //     'DQ' => [1, 'association'],
        //     'DR' => [1, 'home_year_income'],
        //     'DS' => [1, 'home_year_info'],
            
        //     // 'DT' => [2, 'income_house'],
        //     // 'DU' => [1, 'house_number'],
        //     // 'DV' => [1, 'house_info'],
        //     // 'DW' => [2, 'plow_land'],
        //     // 'DX' => [1, 'plow_land_info'],
        //     // 'DY' => [2, 'forest_land'],
        //     // 'DZ' => [1, 'forest_land_info'],
        //     // 'EA' => [2, 'agricultural_machinery'],
        //     // 'EB' => [1, 'agricultural_machinery_info'],
        //     // 'EC' => [2, 'equities'],
        //     // 'ED' => [1, 'equities_info'],
        //     // 'EE' => [2, 'stock_right'],
        //     // 'EF' => [1, 'stock_right_info'],
        //     // 'EG' => [2, 'firm'],
        //     // 'EH' => [1, 'firm_info'],
        //     // 'EI' => [2, 'other'],
        //     // 'EJ' => [1, 'other_info'],
        //     // 'EK' => [1, 'details'],
        //     // 'EL' => [1, 'wechat_code'],
            
        //     'DT' => [4, 'urgent_phone'],
        //     'DU' => [1, 'vehicle_code'],
        //     'DV' => [2, 'house'],
        //     'DW' => [1, 'house_number'],
        //     'DX' => [1, 'house_info'],
        //     'DY' => [2, 'plow_land'],
        //     'DZ' => [1, 'plow_land_info'],
        //     'EA' => [2, 'forest_land'],
        //     'EB' => [1, 'forest_land_info'],
        //     'EC' => [2, 'agricultural_machinery'],
        //     'ED' => [1, 'agricultural_machinery_info'],
        //     'EE' => [2, 'equities'],
        //     'EF' => [1, 'equities_info'],
        //     'EG' => [2, 'stock_right'],
        //     'EH' => [1, 'stock_right_info'],
        //     'EI' => [2, 'firm'],
        //     'EJ' => [1, 'firm_info'],
        //     'EK' => [2, 'other'],
        //     'EL' => [1, 'other_info'],
        //     'EM' => [1, 'details'],
        //     'EN' => [1, 'wechat_code'],
        // ];
        
        
        $this->columnFormat=[
            'A' => [0, '建三江街道办事处'],
            'B' => [1, 'community_name','required'=>'1'],
            'C' => [1, 'grid_group_name','required'=>'1'],
            'D' => [1, 'grid_name','required'=>'1'],
            'E' => [1, 'building_name','required'=>'1'],
            'F' => [1, 'unit','required'=>'1'],
            'G' => [1, 'floor','required'=>'1'],
            'H' => [1, 'house_code','required'=>'1'],
            'I' => [-2, 'relation','cut'=>1,'required'=>'1'],
            'J' => [1, 'name','required'=>'1'],
            'K' => [4, 'id_code','required'=>'1'],
            'L' => [6, 'birthday'],
            'M' => [6, 'age'],
            'N' => [6, 'gender'],
            'O' => [3, 'nation', 54,'required'=>'1'],
            'P' => [1, 're_name'],
            'Q' => [3, 'face', 58,'required'=>'1'],
            'R' => [3, 'education', 56,'required'=>'1'],
            'S' => [1, 'school'],
            'T' => [1, 'major'],
            'U' => [1, 'graduation_date','date'=>1],
            'V' => [3, 'marry', 64,'required'=>'1'],
            'W' => [1, 'marry_date','date'=>1],
            'X' => [3, 'relation', 53,'required'=>'1'],
            'Y' => [4, 'rpr_code'],
            'Z' => [2, 'consistency','required'=>'1'],
            'AA' => [2, 'dead'],
            'AB' => [1, 'dead_date','date'=>1],
            'AC' => [3, 'blood', 62],
            'AD' => [3, 'religion', 55],
            'AE' => [3, 'house_type', 60,'required'=>'1'],
            'AF' => [1, 'house_area'],
            'AG' => [3, 'rpr', 404,'required'=>'1'],
            'AH' => [3, 'rpr_nature', 408,'required'=>'1'],
            'AI' => [3, 'person_type', 59,'required'=>'1'],
            'AJ' => [2, 'gestational_age'],
            'AK' => [1, 'hometown'],
            'AL' => [1, 'rpr_address','required'=>'1'],
            'AM' => [1, 'address','required'=>'1'],
            'AN' => [2, 'single_parent'],
            'AO' => [4, 'phone','required'=>'1'],
            'AP' => [4, 'phone_1'],
            'AQ' => [4, 'phone_2'],
            'AR' => [3, 'only_child',65],
            'AS' => [1, 'only_child_code'],
            'AT' => [3, 'other_child', 66],
            'AU' => [3, 'labor_capacity', 63,'required'=>'1'],
            'AV' => [3, 'health', 61],
            'AW' => [2, 'disability'],
            'AX' => [3, 'disability_type', 411],
            'AY' => [3, 'disability_level', 418],
            'AZ' => [1, 'disability_code'],
            'BA' => [2, 'poverty'],
            'BB' => [2, 'extreme_poverty'],
            'BC' => [3, 'employment_type',440],//从业状态
            'BD' => [3, 'not_employment', 529],//未就业原因
            'BE' => [1, 'not_employment_other'],//其他
            'BF' => [3, 'employment_before', 76],//就业前属于何种人员
            'BG' => [1, 'employment_before_other'],//其他
            'BH' => [3, 'employment_form', 77],//就业形式
            'BI' => [1, 'employment_date','date'=>1],//就业时间
            'BJ' => [1, 'employment_unit'],//就业单位
            'BK' => [3, 'employment_unit_property', 78],//单位性质
            'BL' => [2, 'employment_difficult_reemployment'],//就业困难再就业
            'BM' => [2, 'unemployment_reemployment'],//失业人员再就业
            'BN' => [3, 'unemployment_difficult_type',489],//就业困难人员类别
            'BO' => [1, 'skill_level_certificate'],//技能等级证书名称
            'BP' => [1, 'unemployment_unit'],//原工作单位
            'BQ' => [1, 'lost_employment_date','date'=>1],//失业时间
            'BR' => [3, 'employment_idea', 465],//拟求职就业意向
            'BS' => [3, 'accept_employment_service', 471],//拟接受公共就业人才服务内容
            'BT' => [1, 'accept_employment_service_other'],//其他
            'BU' => [3, 'unemployment_cause', 478],//失业登记原因
            'BV' => [1, 'unemployment_cause_other'],//其他
            'BW' => [-2, 'employment_difficult_reemployment'],//就业困难再就业
            'BX' => [-3, 'unemployment_difficult_type',489],//就业困难人员类别
            'BY' => [2, 'lost_employment_subsidy'],//申领失业保险金
            'BZ' => [2, 'certificate'],
            'CA' => [1, 'certificate_code'],
            'CB' => [2, 'retire'],
            'CC' => [1, 'retire_unit'],
            'CD' => [2, 'insurance_worker'],
            'CE' => [2, 'insurance_towns'],
            'CF' => [2, 'insurance_village'],
            'CG' => [3, 'endowment_insurance', 67],
            'CH' => [2, 'low_income_family'],
            'CI' => [2, 'low_income_person'],
            'CJ' => [2, 'optimal'],
            'CK' => [2, 'releaser'],
            'CL' => [2, 'martyr'],
            'CM' => [2, 'corrector'],
            'CN' => [2, 'children_orphan'],
            'CO' => [2, 'children_stay'],
            'CP' => [2, 'children_abandoned'],

            'CQ' => [2, 'children_disability'],
            'CR' => [2, 'children_subsistence_allowance'],

            'CS' => [2, 'ex_serviceman'],
            'CT' => [1, 'soldier_code'],
            'CU' => [3, 'enlistment', 57],
            'CV' => [2, 'old_oldest'],
            'CW' => [6, 'old'],
            'CX' => [2, 'temporary_relief'],
            'CY' => [2, 'beyond_border'],
            'CZ' => [2, 'drugged'],
            'DA' => [5, 'nation', 1],
            'DB' => [2, 'illness_mental'],
            'DC' => [2, 'cult'],
            'DD' => [2, 'monk'],
            'DE' => [2, 'petition'],
            'DF' => [2, 'help_poor'],
            'DG' => [2, 'illness_aids'],
            'DH' => [2, 'special_support'],
            'DI' => [2, 'old_empty'],
            'DJ' => [2, 'old_lonely'],
            'DK' => [2, 'old_single'],
            'DL' => [2, 'overseas'],
            'DM' => [2, 'representative_party'],
            'DN' => [2, 'representative_people'],
            'DO' => [2, 'committee_member'],
            'DP' => [2, 'volunteer'],
            'DQ' => [1, 'association'],
            'DR' => [1, 'home_year_income'],
            'DS' => [1, 'home_year_info'],
            
            // 'DT' => [2, 'income_house'],
            // 'DU' => [1, 'house_number'],
            // 'DV' => [1, 'house_info'],
            // 'DW' => [2, 'plow_land'],
            // 'DX' => [1, 'plow_land_info'],
            // 'DY' => [2, 'forest_land'],
            // 'DZ' => [1, 'forest_land_info'],
            // 'EA' => [2, 'agricultural_machinery'],
            // 'EB' => [1, 'agricultural_machinery_info'],
            // 'EC' => [2, 'equities'],
            // 'ED' => [1, 'equities_info'],
            // 'EE' => [2, 'stock_right'],
            // 'EF' => [1, 'stock_right_info'],
            // 'EG' => [2, 'firm'],
            // 'EH' => [1, 'firm_info'],
            // 'EI' => [2, 'other'],
            // 'EJ' => [1, 'other_info'],
            // 'EK' => [1, 'details'],
            // 'EL' => [1, 'wechat_code'],
            
            'DT' => [4, 'urgent_phone'],
            'DU' => [1, 'vehicle_code'],
            'DV' => [2, 'income_house'],
            'DW' => [1, 'house_number'],
            'DX' => [1, 'house_info'],
            'DY' => [2, 'plow_land'],
            'DZ' => [1, 'plow_land_info'],
            'EA' => [2, 'forest_land'],
            'EB' => [1, 'forest_land_info'],
            'EC' => [2, 'agricultural_machinery'],
            'ED' => [1, 'agricultural_machinery_info'],
            'EE' => [2, 'equities'],
            'EF' => [1, 'equities_info'],
            'EG' => [2, 'stock_right'],
            'EH' => [1, 'stock_right_info'],
            'EI' => [2, 'firm'],
            'EJ' => [1, 'firm_info'],
            'EK' => [2, 'other'],
            'EL' => [1, 'other_info'],
            'EM' => [1, 'details'],
            'EN' => [1, 'wechat_code'],
        ];
        
        $this->table_name="sa_person_imp";
        $this->table_source="sa_person";
        $this->row_index = 3;
        $this->filterWhere = ['road_id','street_id','road_start','road_end','street_start','street_end','other_child','endowment_insurance'];
        $this->checkRule=[
            ['type'=>'required','title'=>'必填项未填','columns'=>"community_id,grid_group_id,grid_id,building_id,house_id,relation,name,id_code,nation,face,education,marry,house_type,person_type,rpr_address,address,phone,labor_capacity"],
            ['type'=>'beforeToday','title'=>'日期数据(大于当日)','columns'=>"graduation_date,marry_date"],
            ['type'=>'idCard','title'=>'无效身份证号','columns'=>"id_code"],
            ['type'=>'phone','title'=>'无效手机号','columns'=>"phone"],
            ['type'=>'compareVal','title'=>'多户主家庭','columns'=>"p.relation_num > 1 and p.relation = 1"],
            ['type'=>'compareVal','title'=>'无户主家庭','columns'=>"p.relation_num < 1"]
        ];
    }

    /**
     * 添加资源
     * @return Response|void
     */
    public function add()
    {

        $PersonEmployment = new PersonEmployment();
        $PersonIncome = new PersonIncome();
        $this->model = new \app\admin\model\Person();
        if (request()->isPost()) {

            $post = $this->preRuleOutFields(\request()->post());
            $emp = dataArrayToJson($post['emp']);
            $income = dataArrayToJson($post['income']);
            if ($this->dataLimit) {
                $post[$this->dataLimitField] = get_admin_id();
            }

            $validate = $this->isValidate ? get_class($this->model) : $this->isValidate;
            $post = request_validate_rules($post, $validate, $this->scene);
            if (empty($post) || !is_array($post)) {
                return $this->error($post);
            }
            unset($post['emp']);
            unset($post['income']);
            $post = dataArrayToJson($post);
            if(!empty($this->model->create_by)){
                $post[$this->model->create_by]=$this->admin_info['name'];
            }
            if(!empty($this->model->__get("createTime"))){
                $post[$this->model->__get("createTime")]=date('Y-m-d H:i:s');
            }
            $id = $this->model->where("id_code",$post['id_code'])->value("id");
            if($id){
                $alias = "p";
                $data = $this->model->alias($alias)
                    ->join("sa_community c",$alias.".community_id=c.id","LEFT")
                    ->join("sa_grid g",$alias.".grid_id=g.id","LEFT")
                    ->join("sa_grid gg",$alias.".grid_group_id=gg.id","LEFT")
                    ->field($alias.".*,g.grid_name,gg.grid_name grid_group_name,c.community_name")
                    ->where($alias.".id",$id)->find();
                if($data['delete_flag']==1){
                    return $this->error("已存在于待归档人口");
                }
                return $this->error("已存在于{$data['community_name']}-{$data['grid_group_name']}-{$data['grid_name']}");
            }
            $id_info = getInfoFromIdCard($post['id_code']);
            $post['birth']=$id_info['birthday'];
            $post['age']=$id_info['age'];
            $post['sex']=$id_info['gender'];
            $post['dead']=empty($post['dead'])?0:$post['dead'];
            $post['id'] = $this->model->insertGetId($post);
            $this->addBat($post['id'], $PersonEmployment, $post, $emp);
            $this->addBat($post['id'], $PersonIncome, $post, $income);
            $this->model->rbac($post);
            return $post['id'] ? $this->success() : $this->error();
        }
        $data = $this->getTableFields();
        $emp = $this->getTableFields($PersonEmployment);
        $income = $this->getTableFields($PersonIncome);
        $Communities = Db::name("community")->select()->toArray();
        $grid_groups = Db::name("grid")->where(["community_id" => $data['community_id'], 'grid_group' => 0])->select()->toArray();
        $grids = Db::name("grid")->where(["grid_group" => $data['grid_group_id']])->select()->toArray();
        $type = 0;
        if((int)input('person_type',0)==4){
            $type = 2;
        }else if((int)input('rpr_nature',0)==1){
            $type = 1;
        }
        return $this->view('', ['data' => $data, 'emp' => $emp, 'income' => $income,
            'Communities' => $Communities,
            'grid_groups' => $grid_groups,
            'grids' => $grids,
            'type'  => $type
        ]);
    }

    /**
     * 编辑资源
     * @return Response|void
     */
    public function edit()
    {
        $PersonEmployment = new PersonEmployment();
        $PersonIncome = new PersonIncome();
        $id = input('id');
        $this->model = new \app\admin\model\Person();
        $data = $this->model->where('id', $id)->findOrEmpty()->toArray();
        // 限制数据调用
        if (!$this->authService->SuperAdmin() && $this->dataLimit
            && in_array($this->dataLimitField, $this->model->getFields())) {
            if ($data[$this->dataLimitField] != get_admin_id()) {
                return $this->error('没有权限');
            }
        }

        if (request()->isPost()) {
            $post = $this->preRuleOutFields(\request()->post());
            $emp = dataArrayToJson($post['emp']);
            $income = dataArrayToJson($post['income']);
            $validate = $this->isValidate ? get_class($this->model) : $this->isValidate;
            $post = request_validate_rules($post, $validate, $this->scene);
            if (empty($post) || !is_array($post)) {
                return $this->error($post);
            }
            unset($post['emp']);
            unset($post['income']);
            $post = dataArrayToJson($post);
            if($post['graduation_date']&&strtotime($post['graduation_date'])>time()){
                return $this->error("毕业时间不能大于今日");
            }
            if($post['marry_date']&&strtotime($post['marry_date'])>time()){
                return $this->error("初婚日期不能大于今日");
            }
            if($post['dead_date']&&strtotime($post['dead_date'])>time()){
                return $this->error("死亡日期不能大于今日");
            }
            if($emp['employment_date']&&strtotime($emp['employment_date'])>time()){
                return $this->error("就业时间不能大于今日");
            }
            if($emp['lost_employment_date']&&strtotime($emp['lost_employment_date'])>time()){
                return $this->error("失业时间不能大于今日");
            }
            if (!empty($this->model->update_by)) {
                $post[$this->model->update_by] = $this->admin_info['name'];
            }
            $id_info = getInfoFromIdCard($post['id_code']);
            $post['birth']=$id_info['birthday'];
            $post['age']=$id_info['age'];
            $post['sex']=$id_info['gender'];
            $post['dead']=empty($post['dead'])?0:$post['dead'];
            $id = $this->model->where("id_code",$post['id_code'])->where("delete_time",null)->where("id <> ".$post['id'])->value("id");
            if($id){
                $alias = "p";
                $data = $this->model->alias($alias)
                    ->join("sa_community c",$alias.".community_id=c.id","LEFT")
                    ->join("sa_grid g",$alias.".grid_id=g.id","LEFT")
                    ->join("sa_grid gg",$alias.".grid_group_id=gg.id","LEFT")
                    ->field($alias.".*,g.grid_name,gg.grid_name grid_group_name,c.community_name")
                    ->where($alias.".id",$id)->find();
                return $this->error("已存在于{$data['community_name']}-{$data['grid_group_name']}-{$data['grid_name']}");
            }
            $this->status = $this->model->update($post);
            if($post['house_id']){
                $relation_num=$this->model->where(['house_id'=>$post['house_id'],'relation'=>1])->count();
                $this->model->where(['house_id'=>$post['house_id']])->update(['relation_num'=>$relation_num]);
            }
            if (empty($emp['id'])) {
                $this->addBat($post['id'], $PersonEmployment, $post, $emp);
            } else {
                if (!empty($PersonEmployment->update_by)) {
                    $emp[$PersonEmployment->update_by] = $this->admin_info['name'];
                }
                $PersonEmployment->update($emp);
            }
            if (empty($income['id'])) {
                $this->addBat($post['id'], $PersonIncome, $post, $income);
            } else {
                if (!empty($PersonIncome->update_by)) {
                    $emp[$PersonIncome->update_by] = $this->admin_info['name'];
                }
                $PersonIncome->update($income);
            }
            $this->model->rbac($post);
            $data['person_id']=$data['id'];
            unset($data['id']);
            $personHistory = new PersonHistory();
            $personHistory->insert($data);
            return $this->status ? $this->success('','',$id_info) : $this->error();
        }
        /**
         * 默认共享模板
         */
        $template = str_replace('/_', '/', Str::snake(request()->getController()));
        $Communities = Db::name("community")->select()->toArray();
        $grid_groups = Db::name("grid")->where(["community_id" => $data['community_id'], 'grid_group' => 0])->select()->toArray();
        $grids = Db::name("grid")->where(["grid_group" => $data['grid_group_id']])->select()->toArray();
        $emp = $PersonEmployment->where("person_id", $data['id'])->find();
        $emp = empty($emp) ? $this->getTableFields($PersonEmployment) : $emp;
        $income = $PersonIncome->where("person_id", $data['id'])->find();
        $income = empty($income) ? $this->getTableFields($PersonIncome) : $income;
        $type = 0;
        if((int)input('person_type',0)==4){
            $type = 2;
        }else if((int)input('rpr_nature',0)==1){
            $type = 1;
        }
        return $this->view($template . '/add', [
            'data' => $data,
            'emp' => $emp,
            'income' => $income,
            'Communities' => $Communities,
            'grid_groups' => $grid_groups,
            'grids' => $grids,
            'type'  => $type,
            'actionName'  => request()->getAction()
        ]);
    }

    public function editEmp()
    {
        $PersonEmployment = new PersonEmployment();
        $PersonIncome = new PersonIncome();
        $id = input('id');
        $this->model = new \app\admin\model\Person();
        $data = $this->model->where('id', $id)->findOrEmpty()->toArray();
        // 限制数据调用
        if (!$this->authService->SuperAdmin() && $this->dataLimit
            && in_array($this->dataLimitField, $this->model->getFields())) {
            if ($data[$this->dataLimitField] != get_admin_id()) {
                return $this->error('没有权限');
            }
        }

        if (request()->isPost()) {
            $post = $this->preRuleOutFields(\request()->post());
            $emp = dataArrayToJson($post['emp']);
            $validate = $this->isValidate ? get_class($this->model) : $this->isValidate;
            $post = request_validate_rules($post, $validate, $this->scene);
            if (empty($post) || !is_array($post)) {
                return $this->error($post);
            }
            unset($post['emp']);
            $post = dataArrayToJson($post);
            if($emp['employment_date']&&strtotime($emp['employment_date'])>time()){
                return $this->error("就业时间不能大于今日");
            }
            if($emp['lost_employment_date']&&strtotime($emp['lost_employment_date'])>time()){
                return $this->error("失业时间不能大于今日");
            }
            if (!empty($this->model->update_by)) {
                $post[$this->model->update_by] = $this->admin_info['name'];
            }
            $id_info = getInfoFromIdCard($post['id_code']);
            $post['birth']=$id_info['birthday'];
            $post['age']=$id_info['age'];
            $post['sex']=$id_info['gender'];
            $post['dead']=empty($post['dead'])?0:$post['dead'];
            $id = $this->model->where("id_code",$post['id_code'])->where("delete_time",null)->where("id <> ".$post['id'])->value("id");
            if($id){
                $alias = "p";
                $data = $this->model->alias($alias)
                    ->join("sa_community c",$alias.".community_id=c.id","LEFT")
                    ->join("sa_grid g",$alias.".grid_id=g.id","LEFT")
                    ->join("sa_grid gg",$alias.".grid_group_id=gg.id","LEFT")
                    ->field($alias.".*,g.grid_name,gg.grid_name grid_group_name,c.community_name")
                    ->where($alias.".id",$id)->find();
                return $this->error("已存在于{$data['community_name']}-{$data['grid_group_name']}-{$data['grid_name']}");
            }
            $this->status = $this->model->update($post);
            if($post['house_id']){
                $relation_num=$this->model->where(['house_id'=>$post['house_id'],'relation'=>1])->count();
                $this->model->where(['house_id'=>$post['house_id']])->update(['relation_num'=>$relation_num]);
            }
            if (empty($emp['id'])) {
                $this->addBat($post['id'], $PersonEmployment, $post, $emp);
            } else {
                if (!empty($PersonEmployment->update_by)) {
                    $emp[$PersonEmployment->update_by] = $this->admin_info['name'];
                }
                $PersonEmployment->update($emp);
            }
            return $this->status ? $this->success('','',$id_info) : $this->error();
        }
        /**
         * 默认共享模板
         */
        $template = str_replace('/_', '/', Str::snake(request()->getController()));
        $Communities = Db::name("community")->select()->toArray();
        $grid_groups = Db::name("grid")->where(["community_id" => $data['community_id'], 'grid_group' => 0])->select()->toArray();
        $grids = Db::name("grid")->where(["grid_group" => $data['grid_group_id']])->select()->toArray();
        $emp = $PersonEmployment->where("person_id", $data['id'])->find();
        $emp = empty($emp) ? $this->getTableFields($PersonEmployment) : $emp;
        $type = 0;
        return $this->view($template . '/add_emp', [
            'data' => $data,
            'emp' => $emp,
            'Communities' => $Communities,
            'grid_groups' => $grid_groups,
            'grids' => $grids,
            'type'  => $type
        ]);
    }

    public function detailEmp(){
        return $this->editEmp();
    }
    /**
     * 删除资源
     * @return Response
     */
    public function del()
    {
        $Pending = new Pending();
        return $Pending->del();
    }

    public function test(){
        $person = $this->model->where("id",57183)->find()->toArray();
        return $this->model->rbac($person);
    }

    /**
     * index 函数
     */
    public function index()
    {
        if (request()->isAjax()) {
            $page = (int)input('page', 1);
            $limit = (int)input('limit', 18);
            $alias = "p";
            $where = $this->getWhere($alias);
            $where[$alias.".delete_time"]=null;
            $where[]=[$alias.".person_type",'<', 4];
            $where[]=[$alias.".household",'=', 0];

            // 2025-05-13 by 周洪利
            // 此处加入了一个查询条件的限制，防止无孩查询时显示独生子女数据
            $other_child = (string)input('other_child');
  

            // 2025-05-08 by 周洪利 注释掉以下代码
            // $where['99'] = [$alias.".dead",'<>', 1];
            // foreach($where as $value){
            //     if($value[0] == 'p.dead' and $value[1] == '='){
            //         unset($where['99']);
            //     }
            // }

            // 2025-05-08 by 周洪利
            // 用于实现默认只查询未死亡人员数据
            // 只在dead=0或未设置时，才添加查询条件。当dead=1时，则不添加查询条件
            $dead = (int)input('dead', 0);
            if(!$dead) {
                $where[]=[$alias.".dead",'=', 0];
            }


            // return json($where[0]);
            // return json($where);
            // foreach($where as $key=>$value){
            //     return $value;
            //     // if($value['0'] != 'p.dead' and $value['1'] != '='){
            //     //     $where[$key] = [];
            //     // }
            // }
            
            $count = $this->model->alias($alias)
                ->join("sa_building b", $alias.".building_id=b.id")
                ->join("sa_house h",$alias.".house_id=h.id")
                // 2025-05-13 by 周洪利
                // 此处加入了一个查询条件的限制，防止无孩查询时显示独生子女数据
                ->where(function($query) use($other_child, $alias){
                    if($other_child!=''){
                        $query->where('('.$alias.'.only_child is null or '.$alias.'.only_child =2 or '.$alias.'.only_child =3) ');
                        return $query;
                    }
                })
                ->where($where)->count();
            //$page = $count <= $limit ? 1 : $page;
            $fieldList = $this->model->getFields();
            $order = !array_key_exists('sort', $fieldList) ? 'id' : $alias.'.sort';

            $community_id = (int) input('community_id');
            if($community_id==7) {
                $order = "$alias.community_id,$alias.grid_group_id,
                CASE WHEN $alias.grid_id = 17 THEN 13 
                WHEN $alias.grid_id = 18 THEN 17 
                WHEN $alias.grid_id = 19 THEN 18 
                WHEN $alias.grid_id = 20 THEN 19 
                WHEN $alias.grid_id = 22 THEN 20 
                WHEN $alias.grid_id = 23 THEN 22 
                WHEN $alias.grid_id = 24 THEN 23 
                WHEN $alias.grid_id = 25 THEN 24 
                WHEN $alias.grid_id = 13 THEN 25 
                ELSE $alias.grid_id END,b.sort,h.unit,h.floor,h.sort,$alias.relation," . $order;
            } else {
                $order = "$alias.community_id,$alias.grid_group_id,$alias.grid_id,b.sort,h.unit,h.floor,h.sort,$alias.relation," . $order;
            }

            $house_id = input('house_id', 0);
            if($house_id>0){
                $order = "$alias.relation";
            }
            $subQuery = $this->model->alias($alias)
                ->join("sa_building b", $alias.".building_id=b.id")
                ->join("sa_house h",$alias.".house_id=h.id")
                ->field("$alias.id")->where($where)->orderRaw($order)->limit($limit)->page($page)->buildSql();
            $subQuery = '( SELECT object.id FROM ' . $subQuery . ' AS object )';
            $list = $this->model->alias($alias)
                ->join("sa_community c", $alias . ".community_id=c.id", "LEFT")
                ->join("sa_grid g", $alias . ".grid_id=g.id", "LEFT")
                ->join("sa_grid gg", $alias . ".grid_group_id=gg.id", "LEFT")
                ->join("sa_building b", $alias . ".building_id=b.id")
                ->join("sa_house h",$alias.".house_id=h.id")
                ->field($alias . ".*,g.grid_name,gg.grid_name grid_group_name,c.community_name,b.building_name")
                // 2025-05-13 by 周洪利
                // 此处加入了一个查询条件的限制，防止无孩查询时显示独生子女数据
                ->where(function($query) use($other_child, $alias){
                    if($other_child!=''){
                        $query->where('('.$alias.'.only_child is null or '.$alias.'.only_child =2 or '.$alias.'.only_child =3) ');
                        return $query;
                    }
                })
                ->where($alias . '.id in' . $subQuery)->orderRaw($order)->select()->toArray();
            foreach($list as $key=>$vo){
                $list[$key]['name']=$vo['name'].implode(" ", addTags($vo));
            }
            return $this->success($this->model->getLastSql(), '/', $list, $count);
        }
        return $this->view('',['params'=>request()->all()]);
    }

    public function proData()
    {
        $params = request()->all();
        if (request()->isAjax()) {
            $page = (int)input('page', 1);
            $limit = (int)input('limit', 18);
            $alias = "p";
            $where = $this->getWhere($alias);
            $where[$alias.".delete_time"]=null;
            $key = input("check_type",0);
            $check_where = $this->buildCheckWhere("$alias.");
            $count = $this->model->alias($alias)
                ->join("sa_building b", $alias.".building_id=b.id")
                ->join("sa_house h",$alias.".house_id=h.id")
                ->where($where)->where($check_where)->count();
            if($key==0&&$count==0){
                $key=1;
                for($key;$key<count($this->checkRule);$key++){
                    $check_where = $this->buildCheckWhere("$alias.",$key);
                    $count = $this->model->alias($alias)
                        ->join("sa_building b", $alias.".building_id=b.id")
                        ->join("sa_house h",$alias.".house_id=h.id")
                        ->where($where)->where($check_where)->count();
                    if($count){
                        break;
                    }
                }
            }
            //$page = $count <= $limit ? 1 : $page;
            $fieldList = $this->model->getFields();
            $order = !array_key_exists('sort', $fieldList) ? 'id' : $alias.'.sort';
            $order =  "$alias.community_id,$alias.grid_group_id,$alias.grid_id,b.sort,h.unit,h.floor,h.sort,$alias.relation,".$order;
            $house_id = input('house_id', 0);
            if($house_id>0){
                $order = "$alias.relation";
            }
            $subQuery = $this->model->alias($alias)
                ->join("sa_building b", $alias.".building_id=b.id")
                ->join("sa_house h",$alias.".house_id=h.id")
                ->field("$alias.id")->where($where)->where($check_where)->order($order)->limit($limit)->page($page)->buildSql();
            $subQuery = '( SELECT object.id FROM ' . $subQuery . ' AS object )';
            $list = $this->model->alias($alias)
                ->join("sa_community c", $alias . ".community_id=c.id", "LEFT")
                ->join("sa_grid g", $alias . ".grid_id=g.id", "LEFT")
                ->join("sa_grid gg", $alias . ".grid_group_id=gg.id", "LEFT")
                ->join("sa_building b", $alias . ".building_id=b.id")
                ->join("sa_house h",$alias.".house_id=h.id")
                ->field("'{$this->checkRule[$key]['title']}' check_name, ".$alias . ".*,g.grid_name,gg.grid_name grid_group_name,c.community_name,b.building_name")
                ->where($alias . '.id in' . $subQuery)->order($order)->select()->toArray();
            foreach($list as &$vo){
                $vo['name']=$vo['name'].implode(" ", addTags($vo));
            }
            return $this->success('', $key, $list, $count);
        }
        return $this->view('',['checkRule'=>$this->checkRule,'params'=>$params]);
    }

    public function out()
    {
        if (request()->isAjax()) {
            $page = (int)input('page', 1);
            $limit = (int)input('limit', 18);
            $alias = "p";
            $where = $this->getWhere($alias);
            $where[$alias.".delete_time"]=null;
            $whereOr = "(person_type=4)";
            $count = $this->model->alias($alias)
                ->join("sa_building b", $alias.".building_id=b.id","LEFT")
                ->join("sa_house h",$alias.".house_id=h.id","LEFT")
                ->where($where)->where($whereOr)->count();
            //$page = $count <= $limit ? 1 : $page;
            $fieldList = $this->model->getFields();
            $order = !array_key_exists('sort', $fieldList) ? 'id' : $alias.'.sort';
            $order =  "$alias.community_id,$alias.grid_group_id,$alias.grid_id,b.sort,h.unit,h.floor,h.sort,$alias.relation,".$order;
            $house_id = input('house_id', 0);
            if($house_id>0){
                $order = "$alias.relation";
            }
            $subQuery = $this->model->alias($alias)
                ->join("sa_building b", $alias.".building_id=b.id","LEFT")
                ->join("sa_house h",$alias.".house_id=h.id","LEFT")
                ->field("$alias.id")->where($where)->where($whereOr)->order($order)->limit($limit)->page($page)->buildSql();
            $subQuery = '( SELECT object.id FROM ' . $subQuery . ' AS object )';
            $list = $this->model->alias($alias)
                ->join("sa_community c", $alias . ".community_id=c.id", "LEFT")
                ->join("sa_grid g", $alias . ".grid_id=g.id", "LEFT")
                ->join("sa_grid gg", $alias . ".grid_group_id=gg.id", "LEFT")
                ->join("sa_building b", $alias . ".building_id=b.id", "LEFT")
                ->join("sa_house h",$alias.".house_id=h.id","LEFT")
                ->field($alias . ".*,g.grid_name,gg.grid_name grid_group_name,c.community_name,b.building_name")
                ->where($alias . '.id in' . $subQuery)->order($order)->select()->toArray();
            foreach($list as &$vo){
                $vo['name']=$vo['name'].implode(" ", addTags($vo));
            }
            return $this->success($this->model->getLastSql(), '/', $list, $count);
        }
        return $this->view('');
    }

    public function leave()
    {
        if (request()->isAjax()) {
            $page = (int)input('page', 1);
            $limit = (int)input('limit', 18);
            $alias = "p";
            $where = $this->getWhere($alias);
            $where[]=[$alias.".household",'=', 0];
            $count = $this->model->alias($alias)
                ->join("sa_building b", $alias.".building_id=b.id")
                ->join("sa_house h",$alias.".house_id=h.id")
                ->where($where)->count();
            //$page = $count <= $limit ? 1 : $page;
            $fieldList = $this->model->getFields();
            $order = !array_key_exists('sort', $fieldList) ? 'id' : $alias.'.sort';
            $order =  "$alias.community_id,$alias.grid_group_id,$alias.grid_id,b.sort,h.unit,h.floor,h.sort,$alias.relation,".$order;
            $house_id = input('house_id', 0);
            if($house_id>0){
                $order = "$alias.relation";
            }
            
            $subQuery = $this->model->alias($alias)
                ->join("sa_building b", $alias.".building_id=b.id")
                ->join("sa_house h",$alias.".house_id=h.id")
                ->field("$alias.id")->where($where)->order($order)->limit($limit)->page($page)->buildSql();
            $subQuery = '( SELECT object.id FROM ' . $subQuery . ' AS object )';
            
            $list = $this->model->alias($alias)
                ->join("sa_community c", $alias . ".community_id=c.id", "LEFT")
                ->join("sa_grid g", $alias . ".grid_id=g.id", "LEFT")
                ->join("sa_grid gg", $alias . ".grid_group_id=gg.id", "LEFT")
                ->join("sa_building b", $alias . ".building_id=b.id")
                ->join("sa_house h",$alias.".house_id=h.id")
                ->field($alias . ".*,g.grid_name,gg.grid_name grid_group_name,c.community_name,b.building_name")
                ->where($alias . '.id in' . $subQuery)->order($order)->select()->toArray();
            foreach($list as &$vo){
                $vo['name']=$vo['name'].implode(" ", addTags($vo));
            }
            return $this->success($this->model->getLastSql(), '/', $list, $count);
        }
        return $this->view('');
    }

    public function household()
    {
        if (request()->isAjax()) {
            $page = (int)input('page', 1);
            $limit = (int)input('limit', 18);
            $house_id = input('house_id', 0);
            $alias = "p";
            $where = $this->getWhere($alias);
            $where[$alias.".delete_time"]=null;
            if($house_id>0){
                $where[$alias.".household"]=1;
            }
            $count = $this->model->alias($alias)
                ->join("sa_building b", $alias.".building_id=b.id","LEFT")
                ->join("sa_house h",$alias.".house_id=h.id","LEFT")
                ->where($where)->count();
            //$page = $count <= $limit ? 1 : $page;
            $fieldList = $this->model->getFields();
            $order = !array_key_exists('sort', $fieldList) ? 'id' : $alias.'.sort';
            $order = "$alias.community_id,$alias.grid_group_id,$alias.grid_id,$alias.building_id,".$order;
            if($house_id>0){
                $order = "$alias.relation";
            }
            $subQuery = $this->model->alias($alias)
                ->join("sa_building b", $alias.".building_id=b.id","LEFT")
                ->join("sa_house h",$alias.".house_id=h.id","LEFT")
                ->field("$alias.id")->where($where)->order($order)->limit($limit)->page($page)->buildSql();
            $subQuery = '( SELECT object.id FROM ' . $subQuery . ' AS object )';
            $list = $this->model->alias($alias)
                ->join("sa_community c", $alias . ".community_id=c.id", "LEFT")
                ->join("sa_grid g", $alias . ".grid_id=g.id", "LEFT")
                ->join("sa_grid gg", $alias . ".grid_group_id=gg.id", "LEFT")
                ->join("sa_building b", $alias . ".building_id=b.id", "LEFT")
                ->join("sa_house h",$alias.".house_id=h.id","LEFT")
                ->field($alias . ".*,g.grid_name,gg.grid_name grid_group_name,c.community_name,b.building_name")
                ->where($alias . '.id in' . $subQuery)->order($order)->select()->toArray();
            foreach($list as &$vo){
                $vo['name']=$vo['name'].implode(" ", addTags($vo));
            }
            return $this->success($this->model->getLastSql(), '/', $list, $count);
        }
        return $this->view('');
    }

    public function pending()
    {
        if (request()->isAjax()) {
            $page = (int)input('page', 1);
            $limit = (int)input('limit', 18);
            $this->model = new PersonAdminModel;
            $alias = "p";
            $where = $this->getWhere($alias,false);
            $where[$alias.'.delete_flag'] = 1;
            $count = $this->model->alias($alias)->where($where)->count();
            //$page = $count <= $limit ? 1 : $page;
            $fieldList = $this->model->getFields();
            $order = !array_key_exists('sort', $fieldList) ? 'id' : $alias.'.sort';
            $order =  "$alias.community_id,$alias.grid_group_id,$alias.grid_id,$alias.relation,".$order;
            $house_id = input('house_id', 0);
            if($house_id>0){
                $order = "$alias.relation";
            }
            $subQuery = $this->model->alias($alias)->field("$alias.id")->where($where)->order($order)->limit($limit)->page($page)->buildSql();
            $subQuery = '( SELECT object.id FROM ' . $subQuery . ' AS object )';
            $list = $this->model->alias($alias)
                ->join("sa_community c", $alias . ".community_id=c.id", "LEFT")
                ->join("sa_grid g", $alias . ".grid_id=g.id", "LEFT")
                ->join("sa_grid gg", $alias . ".grid_group_id=gg.id", "LEFT")
                ->field($alias . ".*,g.grid_name,gg.grid_name grid_group_name,c.community_name")
                ->where($alias . '.id in' . $subQuery)->order($order)->select()->toArray();
            foreach($list as &$vo){
                $vo['name']=$vo['name'].implode(" ", addTags($vo));
            }
            return $this->success($this->model->getLastSql(), '/', $list, $count);
        }
        return $this->view();
    }

    public function getWhere($alias,$allow=true){
        $where = $this->buildSelectParams($alias . ".", $allow);
        $road_id = (int)input('road_id');
        $street_id = (int)input('street_id');
        $road_start = (int)input('road_start');
        $road_end = (int)input('road_end');
        $street_start = (int)input('street_start');
        $street_end = (int)input('street_end');
        $age_start = (int)input('age_start',-1);
        $age_end = (int)input('age_end',-1);
        $unit = (int)input('unit',0);
        $other_child = input("other_child");
        $endowment_insurance = input("endowment_insurance");
        $keyword = input('keyword','');
        $this->keepField='sex';
        if(isset($other_child)){
            // $other_child--;
            $where[$alias.".other_child"]=$other_child;
        }
        if($endowment_insurance){
            $endowment_insurance--;
            $where[$alias.".endowment_insurance"]=$endowment_insurance;
        }
        if($age_start>=0||$age_end>=0){
            $exp = 'between';
            $arr = [$age_start,$age_end];
            if($age_start === 0&&$age_end === 0){

            } elseif ($age_start === 0) {
                $exp = '<=';
                $arr = $age_end;
            } elseif ($age_end === 0) {
                $exp = '>=';
                $arr = $age_start;
            }
            $where[] = [$alias.".age", $exp, $arr];
        }
        $birth_start = input('birth_start','');
        $birth_end = input('birth_end','');
        if($birth_start||$birth_end){
            $exp = 'between';
            $arr = [$birth_start,$birth_end];
            if ($birth_start === '') {
                $exp = '<=';
                $arr = $birth_end;
            } elseif ($birth_end === '') {
                $exp = '>=';
                $arr = $birth_start;
            }
            $where[] = [$alias.".birth", $exp, $arr];
        }

        if($road_id){
            $where['b.road_id']=[$road_id];
        }
        if($street_id){
            $where['b.street_id']=[$street_id];
        }
        if($road_start){
            $where[]=['b.road_start','>=',$road_start];
        }
        if($road_end){
            $where[]=['b.road_end','<=',$road_end];
        }
        if($street_start){
            $where[]=['b.street_start','>=',$street_start];
        }
        if($street_end){
            $where[]=['b.street_end','<=',$street_end];
        }
        if($unit){
            $where['h.unit']=$unit;
        }
        if($keyword){
            $where[] = [$alias.".name|".$alias.".id_code|".$alias.".phone|".$alias.".address", 'like', '%' . $keyword . '%'];
        }
        return $where;
    }

    public function rbac(){
        $status = $this->model->rbac();
        if($status===true){
            return "成功";
        }else{
            return $status;
        }
    }

    protected function addBat($id, $model, $post, $data)
    {
        $data['person_id'] = $id;
        $data['community_id'] = $post['community_id'];
        $data['grid_group_id'] = $post['grid_group_id'];
        $data['grid_id'] = $post['grid_id'];
        if (!empty($model->create_by)) {
            $data[$model->create_by] = $this->admin_info['name'];
        }
        $model->create($data);
    }

    public function dataImport()
    {
        if (request()->isPost()) {
            set_time_limit(600);
            ini_set('memory_limit', '4096M');
            
            // 2025-05-08 by 周洪利
            // 将income_house替换成house，因为导入的表中没有income_house字段，
            // 而导出时，有两个house字段又不行，所以导出时用的income_house字段
            // 也可以手动将要查询的字段全写在导入、导出表中，但比较麻烦，所以这里偷个懒。
            $this->columnFormat['DV'] = [2, 'house'];
            
            $filePath = input('file_1');
            $spreadsheet = IOFactory::load('public'.$filePath);
            //获取活动工作表
            $worksheet = $spreadsheet->getActiveSheet();
            $sheetNames = $spreadsheet->getSheetNames();
            if($sheetNames[0]!='实有人口'){
                return $this->error("请使用正确的模板导入");
            }
            $ids = [];
            foreach ($this->columnFormat as $key => $item) {
                if($item[0]==3){
                    $ids[] = $item[2];
                }
            }
            $Dictionary = getDictionaryGroup($ids);

            $admin_id = get_admin_id();
            $columns_arr=[];
            foreach ($this->columnFormat as $vo){
                if ($vo[0] < 1 || $vo[0] > 4) {
                    continue;
                }
                $columns_arr[$vo[1]]=$vo[1];
            }
            $create_time=date('Y-m-d H:i:s');
            $create_by = $this->admin_info['name'];
            $columns=implode(",",$columns_arr);
            $columns .= ",sort,admin_id";
            $insert_sql = "INSERT INTO sa_person_imp(".$columns.") VALUES ";
            $values = [];
            $PersonImp = new PersonImp();
            delRepeat();
            $error_arr = [];
            foreach ($worksheet->getRowIterator() as $i=>$row) {
                if ($i < 3||empty($worksheet->getCell('K'.$i)->getValue())) {
                    continue;
                }
            
                $cellIterator = $row->getCellIterator();
                $cellIterator->setIterateOnlyExistingCells(false); // 遍历所有单元格，包括空单元格
                $values_arr = [];
                foreach ($cellIterator as $key => $cell) {
                    $value = $cell->getValue();
                    if($this->columnFormat[$key]=='id_code'&&empty($value)){
                        break;
                    }
                    if(empty($this->columnFormat[$key])){
                        return $this->error("存在未定义的excel列，请检查导入表格是否和模板表格格式相同");
                    }
                    if(array_key_exists('required',$this->columnFormat[$key])&&empty($value)){
                        $error_arr[$i].="第{$key}列必填项为空;";
                    }
                    $vo = $this->columnFormat[$key];
                    if (($vo[0] < 1&&$vo != -3) || $vo[0] > 4) {
                        continue;
                    } else {
                        switch ($vo[0]) {
                            case 2:
                                $value = $value == '是' ? 1 : 0;
                                break;
                            case 3:
                                $val = array_search($value, $Dictionary[$vo[2]]);
                                if ($val !== false) {
                                    $value = $val;
                                } else {
                                    // 2025-05-08 by 周洪利
                                    // 因为AT其他子女家庭列默认值为null时，会被重新赋值为0，而0在字典中表示无孩，所以这里手动处理一下此列。
                                    if($key=='AT') {
                                        $value = 'NULL';
                                    } else {
                                        $value = $val;
                                    }
                                }
                                break;
                        }
                        if(array_key_exists($vo[1], $values_arr)&&empty($value)){
                            continue;
                        }
                        // 2025-05-08 by 周洪利
                        // $values_arr[$vo[1]] = "\"".$value."\"";
                        
                        // 2025-05-08 by 周洪利
                        if($value!=='NULL') {
                            $values_arr[$vo[1]] = "\"".$value."\"";
                        } else {
                            $values_arr[$vo[1]] = $value;
                        }
                    }
                }
                
                 
                $values_sql = "(".implode(",",$values_arr).",".($i-2).",".$admin_id.")";
                $values[] = $values_sql;
                $values_arr = [];
                if($i%500==0){
                    Db::query($insert_sql.implode(",",$values));
                    $values = [];
                }
            }
            if(count($values)){
                Db::query($insert_sql.implode(",",$values));
            }
            Db::query("DELETE FROM ".$this->table_name." WHERE admin_id=$admin_id and id_code IS NULL");
            // Db::query("UPDATE sa_person_imp SET age = FLOOR(DATEDIFF(CURDATE(), STR_TO_DATE(SUBSTRING(id_code, 7, 8), '%Y%m%d')) / 365.25),birth = STR_TO_DATE(SUBSTRING(id_code, 7, 8), '%Y%m%d') WHERE admin_id=$admin_id and id_code IS NOT NULL");
            Db::query("UPDATE sa_person_imp SET age = FLOOR(DATEDIFF(CURDATE(), STR_TO_DATE(SUBSTRING(id_code, 7, 8), '%Y%m%d')) / 365.25),birth = STR_TO_DATE(SUBSTRING(id_code, 7, 8), '%Y%m%d'),sex = MOD(SUBSTRING(id_code,17, 1), 2) WHERE admin_id=$admin_id and id_code IS NOT NULL");
            Db::query("UPDATE sa_person_imp i INNER JOIN sa_person p on i.id_code=p.id_code set i.person_id=p.id where admin_id=$admin_id and p.delete_time is null");
            Db::query("UPDATE sa_person_imp i INNER JOIN sa_community c on i.community_name=c.community_name set i.community_id=c.id where admin_id=$admin_id");
            Db::query("UPDATE sa_person_imp i INNER JOIN sa_grid g on i.community_id=g.community_id and i.grid_group_name=g.grid_name set i.grid_group_id=g.id where admin_id=$admin_id");
            Db::query("UPDATE sa_person_imp i INNER JOIN sa_grid g on i.community_id=g.community_id and i.grid_name=g.grid_name set i.grid_id=g.id where admin_id=$admin_id");
            Db::query("UPDATE sa_person_imp i INNER JOIN sa_building b on i.grid_id = b.grid_id and i.building_name=b.building_name set i.building_id=b.id where admin_id=$admin_id");
            Db::query("UPDATE sa_person_imp i INNER JOIN sa_house h on i.building_id=h.building_id and i.unit = h.unit and i.floor=h.floor and i.house_code = h.code set i.house_id=h.id where admin_id=$admin_id");
            Db::query("UPDATE sa_person_imp i INNER JOIN sa_person_soldier s on i.person_id=s.person_id set i.soldier_id=s.id where admin_id=$admin_id");
            Db::query("UPDATE sa_person_imp i INNER JOIN sa_person_volunteer v on i.person_id=v.person_id set i.volunteer_id=v.id where admin_id=$admin_id");
            Db::close();
            $this->dateFormatUpd($admin_id);
            $list = $PersonImp->where("admin_id",$admin_id)->where("community_id",0)->field("sort")->select()->toArray();
            foreach ($list as $item){
                $error_arr[$item['sort']+$this->row_index-1].="找不到对应的社区信息;";
            }
            $list = $PersonImp->where("admin_id",$admin_id)->where("grid_group_id",0)->field("sort")->select()->toArray();
            foreach ($list as $item){
                $error_arr[$item['sort']+$this->row_index-1].="找不到对应的网格组信息;";
            }
            $list = $PersonImp->where("admin_id",$admin_id)->where("grid_id",0)->field("sort")->select()->toArray();
            foreach ($list as $item){
                $error_arr[$item['sort']+$this->row_index-1].="找不到对应的网格信息;";
            }
            $list = $PersonImp->where("admin_id",$admin_id)->where("building_id",0)->field("sort")->select()->toArray();
            foreach ($list as $item){
                $error_arr[$item['sort']+$this->row_index-1].="找不到对应的建筑信息;";
            }
            $list = $PersonImp->where("admin_id",$admin_id)->where("house_id",0)->field("sort")->select()->toArray();
            foreach ($list as $item){
                $error_arr[$item['sort']+$this->row_index-1].="找不到对应的房屋信息;";
            }
            $list = $PersonImp->where("admin_id",$admin_id)->where("LENGTH(id_code) <> 18")->field("sort")->select()->toArray();
            foreach ($list as $item){
                $error_arr[$item['sort']+$this->row_index-1].="身份证号不合法;";
            }
            $list = $PersonImp->alias("i")
                ->join("sa_person p","i.id_code=p.id_code and p.delete_time is null")
                ->join("sa_community c", "p.community_id=c.id", "LEFT")
                ->join("sa_grid g", "p.grid_id=g.id", "LEFT")
                ->join("sa_grid gg", "p.grid_group_id=gg.id", "LEFT")
                ->field("i.name,i.sort,g.grid_name,gg.grid_name grid_group_name,c.community_name")
                ->where("admin_id",$admin_id)
                ->where("(p.grid_id <> i.grid_id)")
                ->field("i.sort")->select()->toArray();
            foreach ($list as $item){
                $error_arr[$item['sort']+$this->row_index-1].="{$item['name']}已存在于{$item['community_name']},{$item['grid_group_name']},{$item['grid_name']};";
            }
            $list = $PersonImp->alias("i")
                ->join("sa_person p","i.id_code=p.id_code")
                ->where("i.admin_id",$admin_id)->where("p.person_type=4")->where("p.delete_time is null")->field("i.sort")->select()->toArray();
            foreach ($list as $item){
                $error_arr[$item['sort']+$this->row_index-1].="该人口已是委外人口;";
            }
            $list = $PersonImp->alias("i")
                ->join("sa_person p","i.id_code=p.id_code and p.delete_flag = 1")->where("i.admin_id",$admin_id)->field("i.sort")->select()->toArray();
            foreach ($list as $item){
                $error_arr[$item['sort']+$this->row_index-1].="该人口已是待归档人口;";
            }
            $list = $PersonImp->alias("i")->field("i.sort")->where("admin_id",$admin_id)->where("ifnull(insurance_worker,0)+ifnull(insurance_towns,0)+ifnull(insurance_village,0)>1")->select()->toArray();
            foreach ($list as $item){
                $error_arr[$item['sort']+$this->row_index-1].="医保只能选一项;";
            }
            
            // 验证是否为孕龄妇女(15-49岁)
            $list = $PersonImp->alias("i")->field("i.sort")->where("admin_id",$admin_id)->where("!(i.sex=0 and i.age>=15 and i.age<=49) and i.gestational_age=1")->select()->toArray();
            foreach ($list as $item){
                $error_arr[$item['sort']+$this->row_index-1].="该人口不属于孕龄妇女，孕龄妇女应为：15-49岁女性";
            }
            
            if(count($error_arr)>0){
                Db::query("delete from sa_person_imp where admin_id=".$admin_id);
                return $this->error("信息校验未通过",'',['data'=>$error_arr]);
            }

            try{
                $sql = $this->buildUpdSql($admin_id);
                Db::query($sql);
                $field="community_id,grid_group_id,grid_id,building_id,house_id,name,id_code,nation,re_name,face,education,school,major,graduation_date,marry,marry_date,relation,rpr_code,consistency,dead,dead_date,blood,religion,house_type,house_area,rpr,rpr_nature,person_type,gestational_age,hometown,rpr_address,address,single_parent,phone,phone_1,phone_2,only_child,only_child_code,other_child,labor_capacity,health,disability,disability_type,disability_level,disability_code,poverty,extreme_poverty,retire,retire_unit,insurance_worker,insurance_towns,insurance_village,endowment_insurance,low_income_family,low_income_person,optimal,releaser,martyr,corrector,children_orphan,children_stay,children_abandoned,ex_serviceman,enlistment,old_oldest,temporary_relief,beyond_border,drugged,illness_mental,cult,monk,petition,help_poor,illness_aids,special_support,old_empty,old_lonely,old_single,overseas,representative_party,representative_people,committee_member,volunteer,urgent_phone,details,wechat_code,sort";
                $insert_sql = "INSERT INTO sa_person ({$field},create_time,create_by) select {$field},'$create_time' create_time,'$create_by' create_by from sa_person_imp where admin_id = {$admin_id} and person_id = 0";
                Db::query($insert_sql);
                
                Db::query("UPDATE sa_person p
INNER JOIN sa_person_imp i on p.id_code=i.id_code and i.admin_id = {$admin_id}
INNER JOIN sa_house h on p.house_id=h.id set i.person_id=p.id, p.house_area=h.area,p.age = FLOOR(DATEDIFF(CURDATE(), STR_TO_DATE(SUBSTRING(p.id_code, 7, 8), '%Y%m%d')) / 365.25),
p.birth = STR_TO_DATE(SUBSTRING(p.id_code, 7, 8), '%Y%m%d') , p.sex = MOD(SUBSTRING(p.id_code,17, 1), 2);");

                $insert_sql = "INSERT INTO sa_person_employment (person_id,id_code,create_time,create_by) select i.person_id, i.id_code,'$create_time' create_time,'$create_by' create_by from sa_person_imp i left join sa_person_employment e on i.person_id=e.person_id where i.admin_id = {$admin_id} and e.id is null";
                Db::query($insert_sql);
                $sql = $this->buildUpdSqlEmp($admin_id);
                Db::query($sql);
                $insert_sql = "INSERT INTO sa_person_income (person_id,id_code,create_time,create_by) select i.person_id, i.id_code,'$create_time' create_time,'$create_by' create_by from sa_person_imp i left join sa_person_income e on i.person_id=e.person_id where i.admin_id = {$admin_id} and e.id is null";
                Db::query($insert_sql);
                $sql = $this->buildUpdSqlIncome($admin_id);
                Db::query($sql);
                $this->model->rbacImp($admin_id);
                Db::query("delete from sa_person_imp where admin_id=".$admin_id);
            }catch (\Exception $e){
                Db::query("delete from sa_person_imp where admin_id=".$admin_id);
                return $this->error( '导入失败');
            }
            return $this->success( '导入成功');
        }
        return $this->view();
    }

    public function excelExport($list = [])
    {
        // 文件路径
        $filePath = 'public/tpl/实有人口模版.xlsx';
        $msg = "执行".time()."<br/>";
        // 读取Excel文件
        try{
            $spreadsheet = IOFactory::load($filePath);
            $msg .= "加载".time()."<br/>";
            // 获取活动工作表
            $sheet = $spreadsheet->getActiveSheet();
            $excelService = new ExcelService();
            $integerValidation = $excelService->integerValidation();
            $decimalValidation = $excelService->decimalValidation();
            $idcardValidation = $excelService->idcardValidation();
            $count = 10;
            if($list){
                $count = count($list);
            }
            $cellRange = 'F3:F' . $count+10;
            $sheet->setDataValidation($cellRange, $integerValidation);
            $cellRange = 'G3:G' . $count+10;
            $sheet->setDataValidation($cellRange, $integerValidation);
            $cellRange = 'AF3:AF' . $count+10;
            $sheet->setDataValidation($cellRange, $decimalValidation);
            $sheet->getStyle('K:K')->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_TEXT);
            $sheet->getStyle('L3:L'. $count+10)->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_DATE_YYYYMMDD2);
            $sheet->setDataValidation($cellRange, $idcardValidation);
            $dataList = ['是', '否'];
            $optionValidation = $excelService->optionValidation($dataList);
            $ids = [];
            foreach ($this->columnFormat as $item) {
                if($item[0]==3){
                    $ids[] = $item[2];
                }
            }
            $Dictionary = getDictionaryGroup($ids);
            foreach ($this->columnFormat as $key=>$item) {
                if($item[0]==2||$item[0]==-2){
                    $cellRange = $key . '3:' . $key . $count+10;
                    $sheet->setDataValidation($cellRange, $optionValidation);
                }else if($item[0]==3||$item[0]==-3){
                    $cellRange = $key . '3:' . $key . $count+10;
                    $dataList = $Dictionary[$item[2]];
                    $dataList = array_values($dataList);
                    if($key=="X"){
                        $optionListValidation = $excelService->optionRelationValidation($dataList);
                    }else{
                        $optionListValidation = $excelService->optionValidation($dataList);
                    }
                    $sheet->setDataValidation($cellRange, $optionListValidation);
                }
            }
            $msg .= "格式化".time()."<br/>";
            if (count($list) > 0) {
                $i = 3;
                foreach ($list as $item) {
                    $id_info = getInfoFromIdCard($item['id_code']);
                    $id_info['gender']=$id_info['gender']==1?'男':'女';
                    foreach ($this->columnFormat as $column => $value) {
                        switch ($value[0]) {
                            case 1:
                                $item[$value[1]]=$item[$value[1]]=='0000-00-00'?'':$item[$value[1]];
                                $sheet->setCellValueExplicit($column . $i, trim((string)$item[$value[1]], "\""), DataType::TYPE_STRING);
                                break;
                            case 2:
                            case -2:
                                $sheet->setCellValue($column . $i, $item[$value[1]] == 1 ? '是' : '否');
                                break;
                            case 3:
                            case -3:
                                // 2025-05-08 by 周洪利
                                // $index = $item[$value[1]];
                                // if ($index) {
                                //     $sheet->setCellValue($column . $i, empty($Dictionary[$value[2]][$index]) ? '' : $Dictionary[$value[2]][$index]);
                                // }
                                
                                // 2025-05-08 by 周洪利
                                // 此处单独处理了AT列（即其他子女家庭），因为选择了无孩，所以$index是0，所以原有代码会无法输出该字段信息。
                                $index = $item[$value[1]];
                                if($column=='AT') {
                                    $sheet->setCellValue($column . $i, empty($Dictionary[$value[2]][$index]) ? '' : $Dictionary[$value[2]][$index]);
                                } else {
                                    if ($index) {
                                        $sheet->setCellValue($column . $i, empty($Dictionary[$value[2]][$index]) ? '' : $Dictionary[$value[2]][$index]);
                                    }
                                }
                                break;
                            case 6:
                                $sheet->setCellValue($column . $i, $id_info[$value[1]]);
                                break;
                            case 5:
                                $sheet->setCellValue($column . $i, $item[$value[1]] != $value[2] ? '是' : '否');
                                break;
                            case 4:
                                $sheet->setCellValueExplicit($column . $i, $item[$value[1]], DataType::TYPE_STRING);
                                break;
                            case 0:
                                $sheet->setCellValue($column . $i, $value[1]);
                                break;
                        }
                    }
                    $sheet->setCellValue("L" . $i, "=DATE(MID(K$i,7,4),MID(K$i,11,2),MID(K$i,13,2))");
                    $sheet->setCellValue("M" . $i, "=INT((TODAY()-L$i)/365.25)");
                    $sheet->setCellValue("N" . $i, "=IF(MOD(MID(K$i,17,1),2)=0,\"女\",\"男\")");
                    $i++;
                }
                // 下方代码2025-05-07周洪利注释，看起来没什么实际作用的代码。
                // for($j=0;$j<10;$j++){
                //     $sheet->setCellValue("L" . $i, "=DATE(MID(K$i,7,4),MID(K$i,11,2),MID(K$i,13,2))");
                //     $sheet->setCellValue("M" . $i, "=INT((TODAY()-L$i)/365.25)");
                //     $sheet->setCellValue("N" . $i, "=IF(MOD(MID(K$i,17,1),2)=0,\"女\",\"男\")");
                //     $i++;
                // }
                // 保存Excel文件
                $writer = new Xlsx($spreadsheet);
                $day = date('Ymd');
                $time = date('his');
                $dir = '/download/' . $day;
                if (!file_exists('public' . $dir)) {
                    mkdir('public' . $dir, 0755, true);
                }
                $filePath = $dir . '/实有人口数据导出表(' . $day . $time . ').xlsx';

                $msg .= "遍历".time()."<br/>";
                $writer->setUseDiskCaching(true);
                $writer->save('public' . $filePath);
                $msg .= "保存".time()."<br/>";
                return $filePath;
//                 return $msg;
            }
            // 保存Excel文件
            $writer = new Xlsx($spreadsheet);
            $writer->save('public/tpl/实有人口模版.xlsx');
            return '';
        } catch (Exception $e) {
            return $e->getMessage();
        }
    }

    public function downloadExcelTpl()
    {
        $this->excelExport();
        return $this->redirect('/tpl/实有人口模版.xlsx');
    }

    public function buildUpdSql($admin_id){
        $this->tableFields = $this->model->getFields();
        $fieldColumn="UPDATE sa_person p INNER JOIN sa_person_imp i on i.person_id=p.id and i.admin_id=".$admin_id." set ";
        foreach ($this->columnFormat as $item){
            $field = $item[1];
            if (!array_key_exists($field, $this->tableFields)) {
                continue;
            }
            $fieldColumn.="p.".$field."=i.".$field.",";
        }
        $update_time=date('Y-m-d H:i:s');
        $update_by = $this->admin_info['name'];
        return $fieldColumn."p.sort=i.sort,p.update_time='$update_time',p.update_by='$update_by';";
    }

    public function buildUpdSqlEmp($admin_id){
        $emp = new PersonEmployment();
        $this->tableFields = $emp->getFields();
        $fieldColumn="UPDATE sa_person_employment p INNER JOIN sa_person_imp i on i.person_id=p.person_id and i.admin_id=".$admin_id." set ";
        foreach ($this->columnFormat as $item){
            $field = $item[1];
            if (!array_key_exists($field, $this->tableFields)) {
                continue;
            }
            $fieldColumn.="p.".$field."=i.".$field.",";
        }
        $update_time=date('Y-m-d H:i:s');
        $update_by = $this->admin_info['name'];
        return $fieldColumn."p.type=i.employment_type,p.update_time='$update_time',p.update_by='$update_by';";
    }
    
    public function buildUpdSqlIncome($admin_id){
        $emp = new PersonIncome();
        $this->tableFields = $emp->getFields();
        $fieldColumn="UPDATE sa_person_income p INNER JOIN sa_person_imp i on i.person_id=p.person_id and i.admin_id=".$admin_id." set ";
        foreach ($this->columnFormat as $item){
            $field = $item[1];
            if (!array_key_exists($field, $this->tableFields)) {
                continue;
            }
            $fieldColumn.="p.".$field."=i.".$field.",";
        }
        $update_time=date('Y-m-d H:i:s');
        $update_by = $this->admin_info['name'];
        return $fieldColumn."p.update_time='$update_time',p.update_by='$update_by';";
    }

    public function dataToExcel()
    {
        set_time_limit(600);
        ini_set('memory_limit', '4096M');
        delRepeat();
        $alias = "p";
        $where = $this->getWhere($alias);
        $where[$alias.".delete_time"]=null;
        $where[]=[$alias.".person_type",'<', 4];
        $where[]=[$alias.".household",'=', 0];
        $fieldList = $this->model->getFields();
        $order = !array_key_exists('sort', $fieldList) ? 'id' : 'sort';
        $order =  "$alias.community_id,$alias.grid_group_id,$alias.grid_id,b.sort,h.unit,h.floor,h.sort,$alias.relation,$alias.$order";
        $time = time()."<br/>";
        $dead = input('dead');
        if(isset($dead)) {
            $where[] = [$alias.".dead", $dead];
        } else {
            $where[] = [$alias.".dead", '=', 0];
        }
        $other_child = (string)input('other_child');
        $list = $this->model->alias($alias)->Distinct(true)
            ->join("sa_community c", $alias . ".community_id=c.id")
            ->join("sa_grid g", $alias . ".grid_id=g.id")
            ->join("sa_grid gg", $alias . ".grid_group_id=gg.id")
            ->join("sa_building b", $alias . ".building_id=b.id")
            ->join("sa_house h", $alias . ".house_id=h.id")
            ->join("sa_person_employment e", $alias . ".id=e.person_id", "LEFT")
            ->join("sa_person_vehicle v", $alias . ".id_code=v.id_code", "LEFT")
            ->join("sa_person_soldier s", $alias . ".id=s.person_id and $alias.grid_id=s.grid_id", "LEFT")
            ->join("sa_person_volunteer vo", $alias . ".id=vo.person_id and $alias.grid_id=s.grid_id", "LEFT")
            ->join("sa_person_income i", $alias . ".id=i.person_id", "LEFT")
            ->field($alias . ".*,g.grid_name,gg.grid_name grid_group_name,c.community_name,b.building_name,h.code house_code,
            h.unit,h.floor,count(*) car_num,GROUP_CONCAT(v.vehicle_code SEPARATOR ',') vehicle_code,
            e.type employment_type,e.not_employment,e.not_employment_other,e.employment_before,e.employment_before_other,e.employment_form,e.employment_date,e.employment_unit,e.employment_unit_property,
            e.unemployment_reemployment,e.employment_difficult_reemployment,e.employment_difficult_type,e.skill_level_certificate,
            e.unemployment_unit,e.lost_employment_date,e.employment_idea,e.accept_employment_service,e.accept_employment_service_other,
            e.unemployment_cause,e.unemployment_cause_other,e.unemployment_difficult,e.unemployment_difficult_type,e.lost_employment_subsidy,
            e.certificate,e.certificate_code,s.code soldier_code,vo.association,
            i.home_year_income,i.home_year_info,i.house income_house,i.house_number,i.house_info,i.plow_land,i.plow_land_info,
            i.forest_land,i.forest_land_info,i.agricultural_machinery,i.agricultural_machinery_info,i.equities,i.equities_info,
            i.stock_right,i.stock_right_info,i.firm,i.firm_info,i.other,i.other_info,b.sort,h.unit,h.floor,h.sort")
            ->where($where)
            // 2025-05-13 by 周洪利
            // 此处加入了一个查询条件的限制，防止无孩查询时显示独生子女数据
            ->where(function($query) use($other_child, $alias){
                if($other_child!=''){
                    $query->where('('.$alias.'.only_child is null or '.$alias.'.only_child =2 or '.$alias.'.only_child =3) ');
                    return $query;
                }
            })
            ->group($alias . '.id,e.id,s.id,vo.id,i.id')
            ->order($order)->select()->toArray();
        if(count($list)<1) {
            return $this->error('查询无数据');
        }
        if(count($list)>5000){
            return $this->error('导出的数据过多，建议分批导出');
        }
        $time .= "查询".time()."<br/>";
        $filePath = $this->excelExport($list);
//          return $time .$filePath;
        if(request()->isPost()){
            return $this->success('',$filePath);
        }
        return $this->redirect($filePath);
    }

    /**
     * 获取资源列表
     * @return Response
     */
    public function history()
    {
        $page = (int)input('page', 1);
        $limit = (int)input('limit', 18);
        $person_id = input("person_id",0);
        if(empty($person_id)){
            return $this->error("参数错误");
        }
        $alias = "p";
        $where[$alias.'.person_id'] = [$person_id ];
        $personHistory = new PersonHistory();
        $count = $personHistory->alias($alias)
            ->join("sa_building b", $alias.".building_id=b.id","LEFT")
            ->join("sa_house h",$alias.".house_id=h.id","LEFT")
            ->where($where)->count();
        //$page = $count <= $limit ? 1 : $page;
        $fieldList = $personHistory->getFields();
        $order = $alias.".id";
        $subQuery = $personHistory->alias($alias)
            ->join("sa_building b", $alias.".building_id=b.id","LEFT")
            ->join("sa_house h",$alias.".house_id=h.id","LEFT")
            ->field("$alias.id")->where($where)->order($order)->limit($limit)->page($page)->buildSql();
        $subQuery = '( SELECT object.id FROM ' . $subQuery . ' AS object )';
        $list = $personHistory->alias($alias)
            ->join("sa_community c", $alias . ".community_id=c.id", "LEFT")
            ->join("sa_grid g", $alias . ".grid_id=g.id", "LEFT")
            ->join("sa_grid gg", $alias . ".grid_group_id=gg.id", "LEFT")
            ->join("sa_building b", $alias . ".building_id=b.id")
            ->field($alias . ".*,g.grid_name,gg.grid_name grid_group_name,c.community_name,b.building_name")
            ->where($alias . '.id in' . $subQuery)->order($order." desc")->select()->toArray();
        return $this->success($personHistory->getLastSql(), '/', $list, $count);

    }

    public function contrast(){
        $id = (int)input("id",0);
        $person_id = (int)input("person_id",0);

        $alias = "p";
        $PersonHistory = new PersonHistory();
        $history_data = $PersonHistory->alias($alias)
            ->join("sa_community c", $alias . ".community_id=c.id", "LEFT")
            ->join("sa_grid g", $alias . ".grid_id=g.id", "LEFT")
            ->join("sa_grid gg", $alias . ".grid_group_id=gg.id", "LEFT")
            ->join("sa_building b", $alias . ".building_id=b.id", "LEFT")
            ->join("sa_house h", $alias . ".house_id=h.id", "LEFT")
            ->field($alias . ".*,g.grid_name,gg.grid_name grid_group_name,c.community_name,b.building_name,h.code house_code,
            h.unit house_unit,h.floor house_floor,h.street house_street")
            ->where($alias .'.id', $id)->findOrEmpty()->toArray();
        $data = $this->model->alias($alias)
            ->join("sa_community c", $alias . ".community_id=c.id", "LEFT")
            ->join("sa_grid g", $alias . ".grid_id=g.id", "LEFT")
            ->join("sa_grid gg", $alias . ".grid_group_id=gg.id", "LEFT")
            ->join("sa_building b", $alias . ".building_id=b.id", "LEFT")
            ->join("sa_house h", $alias . ".house_id=h.id", "LEFT")
            ->field($alias . ".*,g.grid_name,gg.grid_name grid_group_name,c.community_name,b.building_name,h.code house_code,
            h.unit house_unit,h.floor house_floor,h.street house_street")
            ->where($alias .'.id', $person_id)->findOrEmpty()->toArray();
        return $this->view('',['data'=>$data,'history_data'=>$history_data]);
    }

}