<?php
declare (strict_types = 1);
namespace app\admin\controller;

use app\AdminController;
use support\Response;
use think\helper\Str;
use Webman\Http\Request;
use app\common\model\OrganizationPerson as OrganizationPersonModel;
use app\common\model\Community;
use app\common\model\OrganizationPersonPerson;

/**
 * OrganizationPerson
 * 街道管理
 * <!---->
 * Class OrganizationPerson
 * @package app\admin\controller
 */
class OrganizationPerson extends AdminController
{
    /**
     * OrganizationPerson模型对象
     * @var OrganizationPersonModel
     */

    public function __construct()
    {
        parent::__construct();
        $this->model = new OrganizationPersonModel;
    }

    /**
     * 添加资源
     * @return Response|void
     */
    public function add()
    {
        if (request()->isPost()) {

            $post = $this->preRuleOutFields(\request()->post());
            if ($this->dataLimit) {
                $post[$this->dataLimitField] = get_admin_id();
            }

            $validate = $this->isValidate ? get_class($this->model) : $this->isValidate;
            $post = request_validate_rules($post, $validate, $this->scene);
            if (empty($post) || !is_array($post)) {
                return $this->error($post);
            }
            $post = dataArrayToJson($post);
            if(!empty($this->model->create_by)){
                $post[$this->model->create_by]=$this->admin_info['name'];
            }
            if(!empty($this->model->createTime)){
                $post[$this->model->createTime]=date('Y-m-d H:i:s');
            }
            $this->status = $this->model->create($post);
            return $this->status ? $this->success() : $this->error();
        }
        $params = request()->all();
        return $this->view('', [
            'data' => $this->getTableFields(),
            'params'=>$params]);
    }

    /**
     * 获取资源列表
     * @return Response
     */
    public function index()
    {
        $params = request()->all();
        if (request()->isAjax()) {
            $page = (int)input('page', 1);
            $limit = (int)input('limit', 18);
            $where = $this->buildSelectParams();
            $count = $this->model->where($where)->count();
            //$page = $count <= $limit ? 1 : $page;
            $fieldList = $this->model->getFields();
            $order = !array_key_exists('sort', $fieldList) ? 'id' : 'sort';
            $subQuery = $this->model->field('id')->where($where)->order($order)->limit($limit)->page($page)->buildSql();
            $subQuery = '( SELECT object.id FROM ' . $subQuery . ' AS object )';
            $list = $this->model->alias("p")
            ->join("sa_community c","p.community_id=c.id","LEFT")
            ->join("sa_grid gg","p.grid_group_id=gg.id","LEFT")
            ->join("sa_grid g","p.grid_id=g.id","LEFT")
            ->field("p.id,p.name,p.avatar,p.post,p.phone,c.community_name,gg.grid_name grid_group_name,g.grid_name")
             ->where('p.id in' . $subQuery)->order($order, 'desc')->select()->toArray();
            return $this->success($this->model->getLastSql(), '/', $list, $count);
        }

        return $this->view('',['params'=>$params]);
    }

}
