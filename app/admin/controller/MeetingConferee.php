<?php 
    declare(strict_types = 1);

    namespace app\admin\controller;

    use app\admin\service\MeetingConfereeService;
    use app\AdminController;
    use Webman\Http\Request;
    use app\common\model\MeetingConferee as MeetingConfereeModel;

    class MeetingConferee extends AdminController{

        public function __construct() {
            parent::__construct();
            $this->model = new MeetingConfereeModel;
        }

        public function batchAdd(){
            if (request()->isPost()) {
                $post = $this->preRuleOutFields(\request()->post());
                if ($this->dataLimit) {
                    $post[$this->dataLimitField] = get_admin_id();
                }

                try {
                    $meetingId = $post["meeting_id"];
                    $adminIds = $post["admin_ids"];

                    MeetingConfereeService::addConferees($this->admin_info,$meetingId,$adminIds);
                    return $this->success();
                } catch (\Throwable $th) {
                    return $th->getMessage();
                    return $this->error($th->getMessage());
                }
            }
            return $this->error("请使用Post方法提交");
        }
    }
?>