<?php
declare (strict_types = 1);
namespace app\admin\controller;

use app\AdminController;
use support\Response;
use think\helper\Str;
use Webman\Http\Request;
use app\common\model\PersonAffordableHousing as PersonAffordableHousingModel;
use think\facade\Db;

/**
 * person_vehicle
 * 车辆管理
 * <!---->
 * Class PersonVehicle
 * @package app\admin\controller
 */
class PersonAffordableHousing extends AdminController
{
    /**
     * PersonVehicle模型对象
     * @var \app\common\model\PersonAffordableHousing
     */

    public function __construct()
    {
        parent::__construct();
        $this->model = new PersonAffordableHousingModel;
        $this->columnFormat=[
            'A' => [0, 'index'],
            'B' => [1, 'community_name','column'=>'community_id'],
            'C' => [1, 'grid_group_name','column'=>'grid_group_id'],
            'D' => [1, 'grid_name','column'=>'grid_id'],
            'E' => [3, 'jurisdiction',501,'required'=>1],//管辖类别
            'F' => [1,'name'],//姓名
            'G' => [4, 'id_code'],//身份证号码
            'H' => [3, 'group_0',434],//所属群体
            'I' => [2,'income_low'],//是否低保
            'J' => [1, 'house_address'],//房屋地址
            'K' => [1, 'building_area'],//建筑面积
            'L' => [1,'lease_from'],//出租人
            'M' => [1,'lease_to'],//承租人
            'N' => [1,'lease_start','date'=>1],//承租开始日期
            'O' => [1,'lease_end','date'=>1],//承租结束日期
            'P' => [1,'rent_month'],//月租金
            'Q' => [1,'deposit'],//保证金（押金）
            'R' => [1,'transaction','date'=>1],//合同签订日期
            'S' => [1,'details'],

            'T' => [1, 'member_name_1'],
            'U' => [4,'member_id_code_1'],
            'V' => [3, 'relation_1', 53],
            // 'Y' => [6, 'birth_1','id_code_type'=>'birthday','id_code_key'=>'W'],
            'W' => [3, 'nation_1', 54],
            'X' => [3,'education_1', 56],
            'Y' => [3, 'marry_1', 64],
            'Z' => [3, 'employment_1',440],
            'AA' => [3, 'group_1',434],
            'AB' => [4,'phone_1'],

            'AC' => [1, 'member_name_2'],
            'AD' => [4,'member_id_code_2'],
            'AE' => [3, 'relation_2', 53],
            // 'AI' => [6, 'birth_2','id_code_type'=>'birthday','id_code_key'=>'AG'],
            'AF' => [3, 'nation_2', 54],
            'AG' => [3,'education_2', 56],
            'AH' => [3, 'marry_2', 64],
            'AI' => [3, 'employment_2',440],
            'AJ' => [3, 'group_2',434],
            'AK' => [4,'phone_2'],

            'AL' => [1, 'member_name_3'],
            'AM' => [4,'member_id_code_3'],
            'AN' => [3, 'relation_3', 53],
            // 'AS' => [6, 'birth_3','id_code_type'=>'birthday','id_code_key'=>'AQ'],
            'AO' => [3, 'nation_3', 54],
            'AP' => [3,'education_3', 56],
            'AQ' => [3, 'marry_3', 64],
            'AR' => [3, 'employment_3',440],
            'AS' => [3, 'group_3',434],
            'AT' => [4,'phone_3'],

            'AU' => [1, 'member_name_4'],
            'AV' => [4,'member_id_code_4'],
            'AW' => [3, 'relation_4', 53],
            // 'BC' => [6, 'birth_4','id_code_type'=>'birthday','id_code_key'=>'BA'],
            'AX' => [3, 'nation_4', 54],
            'AY' => [3,'education_4', 56],
            'AZ' => [3, 'marry_4', 64],
            'BA' => [3, 'employment_4',440],
            'BB' => [3, 'group_4',434],
            'BC' => [4,'phone_4'],
           
            'BD' => [1, 'wechat_code'],
            'BE' => [1,'grid_user'],
            'BF' => [1,'grid_phone'],
        ];
        $this->tpl_name = "保障房";
        $this->table_name = 'sa_person_affordable_housing_imp';
        $this->table_source = "sa_person_affordable_housing";
        $this->pk = "person_id";
        $this->row_index = 4;
    }

    /**
     * 添加资源
     * @return Response|void
     */
    public function add()
    {
        if (request()->isPost()) {

            $post = $this->preRuleOutFields(\request()->post());
            if ($this->dataLimit) {
                $post[$this->dataLimitField] = get_admin_id();
            }

            $validate = $this->isValidate ? get_class($this->model) : $this->isValidate;
            $post = request_validate_rules($post, $validate, $this->scene);
            if (empty($post) || !is_array($post)) {
                return $this->error($post);
            }
            if(empty($post['person_id'])){
                return $this->error('请调取人口数据！');
            }
            $post = dataArrayToJson($post);
            if(!empty($this->model->create_by)){
                $post[$this->model->create_by]=$this->admin_info['name'];
            }
            if(!empty($this->model->createTime)){
                $post[$this->model->createTime]=date('Y-m-d H:i:s');
            }
            $this->status = $this->model->create($post);
            return $this->status ? $this->success() : $this->error();
        }
        $data = $this->getTableFields();
        $Communities = Db::name("community")->select()->toArray();
        $grid_groups = Db::name("grid")->where(["community_id"=>$data['community_id'],'grid_group'=>0])->select()->toArray();
        $grids = Db::name("grid")->where(["grid_group"=>$data['grid_group_id']])->select()->toArray();
        return $this->view('', ['data' => $data,
            'Communities'=>$Communities,
            'grid_groups'=>$grid_groups,
            'grids'=>$grids
        ]);
    }

    /**
     * 编辑资源
     * @return Response|void
     */
    public function edit()
    {
        $id = input('id');
        $data = $this->model->where('id', $id)->findOrEmpty()->toArray();

        // 限制数据调用
        if (!$this->authService->SuperAdmin() && $this->dataLimit
            && in_array($this->dataLimitField, $this->model->getFields())) {
            if ($data[$this->dataLimitField] != get_admin_id()) {
                return $this->error('没有权限');
            }
        }

        if (request()->isPost()) {
            $post = $this->preRuleOutFields(\request()->post());
            $validate = $this->isValidate ? get_class($this->model) : $this->isValidate;
            $post = request_validate_rules($post, $validate, $this->scene);
            if (empty($post) || !is_array($post)) {
                return $this->error($post);
            }
            if(empty($post['person_id'])){
                return $this->error('请调取人口数据！');
            }
            $post = dataArrayToJson($post);
            if(!empty($this->model->update_by)){
                $post[$this->model->update_by]=$this->admin_info['name'];
            }
            $this->status = $this->model->update($post);
            return $this->status ? $this->success() : $this->error();
        }

        /**
         * 默认共享模板
         */
        $template = str_replace('/_', '/', Str::snake(request()->getController()));
        $Communities = Db::name("community")->select()->toArray();
        $grid_groups = Db::name("grid")->where(["community_id"=>$data['community_id'],'grid_group'=>0])->select()->toArray();
        $grids = Db::name("grid")->where(["grid_group"=>$data['grid_group_id']])->select()->toArray();
        return $this->view($template . '/add', [
            'data' => $data,
            'Communities'=>$Communities,
            'grid_groups'=>$grid_groups,
            'grids'=>$grids
        ]);
    }

    /**
     * index 函数
     */
    public function index()
    {
        if (request()->isAjax()||request()->isPost()) {
            $page = (int)input('page', 1);
            $limit = (int)input('limit', 18);
            $alias = "e";
            $where = $this->buildSelectParams($alias.".");

            $keyword = input('keyword','');
            if($keyword){
                $where[] = ["p.name|"."p.id_code", 'like', '%' . $keyword . '%'];
            }
            $count = $this->model->alias($alias)
                // ->join("sa_person p",$alias.".person_id=p.id and (p.delete_time is null or (p.delete_flag=1 and p.grid_id <> $alias.grid_id)) and dead = 0")
                ->join("sa_person p",$alias.".person_id=p.id and (p.delete_time is null) and dead = 0")
                ->where($where)->count();
            //$page = $count <= $limit ? 1 : $page;
            $fieldList = $this->model->getFields();
            $order = !array_key_exists('sort', $fieldList) ? $alias.'.id' : $alias.'.sort';
            $order = "$alias.community_id,$alias.grid_group_id,$alias.grid_id,".$order;
            $subQuery = $this->model->alias($alias)
                ->join("sa_person p",$alias.".person_id=p.id")
                ->field($alias.'.id')->where($where)->order($order)->limit($limit)->page($page)->buildSql();
            $subQuery = '( SELECT object.id FROM ' . $subQuery . ' AS object )';
            $list = $this->model->alias($alias)
                ->join("sa_community c",$alias.".community_id=c.id","LEFT")
                ->join("sa_grid g",$alias.".grid_id=g.id","LEFT")
                ->join("sa_grid gg",$alias.".grid_group_id=gg.id","LEFT")
                // ->join("sa_person p",$alias.".person_id=p.id and (p.delete_time is null or (p.delete_flag=1 and p.grid_id <> $alias.grid_id)) and dead = 0")
                ->join("sa_person p",$alias.".person_id=p.id and (p.delete_time is null) and dead = 0")
                ->join("sa_building b", "p.building_id=b.id","LEFT")
                ->field($alias.".*,g.grid_name,gg.grid_name grid_group_name,c.community_name,
                p.name,p.id_code,p.age,p.sex,p.address,p.face,p.nation,p.education,p.school,p.major,p.hometown,p.phone,b.building_name")
                ->where($alias.'.id in' . $subQuery)->order($order)->select()->toArray();
            return $this->success($this->model->getLastSql(), '/', $list, $count);
        }else{
            $this->noRepeat();
        }
        return $this->view();
    }



}
