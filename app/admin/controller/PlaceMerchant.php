<?php
declare (strict_types = 1);
namespace app\admin\controller;

use app\admin\service\ExcelService;
use app\AdminController;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use support\Response;
use think\helper\Str;
use Webman\Http\Request;
use app\common\model\PlaceMerchant as PlaceMerchantModel;
use think\facade\Db;

/**
 * place_merchant
 * 个体工商户
 * <!---->
 * Class PlaceMerchant
 * @package app\admin\controller
 */
class PlaceMerchant extends AdminController
{
    protected array $columnFormat;
    protected string $tpl_name;
    protected string $table_name;
    protected string $table_source;
    protected int $row_index;
    protected string $pk;
    private mixed $type;
    /**
     * PlaceMerchant模型对象
     * @var PlaceMerchantModel
     */

    public function __construct()
    {
        parent::__construct();
        $this->model = new PlaceMerchantModel;
        $this->columnFormat=[
            'A' => [0, '建三江街道办事处'],
            'B' => [1, 'community_name','column'=>'community_id'],
            'C' => [1, 'grid_group_name','column'=>'grid_group_id'],
            'D' => [1, 'grid_name','column'=>'grid_id'],
            'E' => [1, 'building_name','column'=>'building_id'],
            'F' => [1, 'unit','number'=>'1'],
            'G' => [1, 'floor','number'=>'1'],
            'H' => [1, 'road_name','column'=>'road_id'],
            'I' => [1, 'road_start','number'=>'1'],
            'J' => [1, 'road_end','number'=>'1'],
            'K' => [1, 'road_sub','number'=>'1'],
            'L' => [1, 'street_name','column'=>'street_id'],
            'M' => [1, 'street_start','number'=>'1'],
            'N' => [1, 'street_end','number'=>'1'],
            'O' => [1, 'street_sub','number'=>'1'],
            'P' => [4, 'type_1_id','type'=>1,'required'=>'1'],
            'Q' => [4, 'type_2_id','type'=>2,'required'=>'1'],
            'R' => [1, 'merchant_name','required'=>'1'],
            'S' => [1, 'address','required'=>'1'],
            'T' => [4, 'code'],//营业执照号码
            'U' => [1, 'create_date'],//成立日期
            'V' => [1, 'corporation','required'=>'1'],//法人姓名
            'W' => [4, 'id_code'],//法人身份证号
            'X' => [6, 'gender'],
            'Y' => [4, 'phone'],
            'Z' => [2, 'is_live'],
            'AA' => [1, 'owner'],//户主
            'AB' => [4, 'owner_code'],//户主身份证号
            'AC' => [1, 'staff_number','number'=>'1'],//工作人员数量，2025-05-07由周洪利添加，同时修改了工商户模板
            'AD' => [1, 'details'],//备注
        ];
        $this->tpl_name = "工商户模版";
        $this->table_name = 'sa_place_merchant_imp';
        $this->table_source = "sa_place_merchant";
        $this->pk = "place_merchant_id";
        $this->row_index = 4;
        $list_type_1 = Db::name("place_enterprise_type")->where("pid",0)->field("id,name")->order("sort")->select()->toArray();
        $list_type_2 = Db::name("place_enterprise_type")->where("pid > 0")->field("id,name")->order("sort")->select()->toArray();
        $this->type = [1=>[],2=>[]];
        foreach ($list_type_1 as $item){
            $this->type[1][$item['id']]=$item['name'];
        }
        foreach ($list_type_2 as $item){
            $this->type[2][$item['id']]=$item['name'];
        }
        $this->filterWhere = ['road_id','street_id','road_start','road_end','street_start','street_end'];
        $this->checkRule=[
            ['type'=>'required','title'=>'必填项未填','columns'=>"community_id,grid_group_id,grid_id,building_id,address,merchant_name,type_1_id,type_2_id,corporation"],
        ];
    }

    /**
     * 添加资源
     * @return Response|void
     */
    public function add()
    {
        if (request()->isPost()) {

            $post = $this->preRuleOutFields(\request()->post());
            if ($this->dataLimit) {
                $post[$this->dataLimitField] = get_admin_id();
            }

            $validate = $this->isValidate ? get_class($this->model) : $this->isValidate;
            $post = request_validate_rules($post, $validate, $this->scene);
            if (empty($post) || !is_array($post)) {
                return $this->error($post);
            }
            $post = dataArrayToJson($post);
            if(!empty($this->model->create_by)){
                $post[$this->model->create_by]=$this->admin_info['name'];
            }
            if(!empty($this->model->createTime)){
                $post[$this->model->createTime]=date('Y-m-d H:i:s');
            }
            $this->status = $this->model->create($post);
            return $this->status ? $this->success() : $this->error();
        }
        $data = $this->getTableFields();
        $Communities = Db::name("community")->select()->toArray();
        $grid_groups = Db::name("grid")->where(["community_id"=>$data['community_id'],'grid_group'=>0])->select()->toArray();
        $grids = Db::name("grid")->where(["grid_group"=>$data['grid_group_id']])->select()->toArray();
        $streets = Db::name("street")->where("type",2)->select()->toArray();
        $roads = Db::name("street")->where("type",1)->select()->toArray();
        return $this->view('', ['data' => $data,
            'Communities'=>$Communities,
            'grid_groups'=>$grid_groups,
            'grids'=>$grids,
            'streets'=>$streets,
            'roads'=>$roads
        ]);
    }

    /**
     * 编辑资源
     * @return Response|void
     */
    public function edit()
    {
        $id = input('id');
        $data = $this->model->where('id', $id)->findOrEmpty()->toArray();

        // 限制数据调用
        if (!$this->authService->SuperAdmin() && $this->dataLimit
            && in_array($this->dataLimitField, $this->model->getFields())) {
            if ($data[$this->dataLimitField] != get_admin_id()) {
                return $this->error('没有权限');
            }
        }

        if (request()->isPost()) {
            $post = $this->preRuleOutFields(\request()->post());
            $validate = $this->isValidate ? get_class($this->model) : $this->isValidate;
            $post = request_validate_rules($post, $validate, $this->scene);
            if (empty($post) || !is_array($post)) {
                return $this->error($post);
            }
            $post = dataArrayToJson($post);
            if(!empty($this->model->update_by)){
                $post[$this->model->update_by]=$this->admin_info['name'];
            }
            $this->status = $this->model->update($post);
            return $this->status ? $this->success() : $this->error();
        }

        /**
         * 默认共享模板
         */
        $template = str_replace('/_', '/', Str::snake(request()->getController()));
        $Communities = Db::name("community")->select()->toArray();
        $grid_groups = Db::name("grid")->where(["community_id"=>$data['community_id'],'grid_group'=>0])->select()->toArray();
        $grids = Db::name("grid")->where(["grid_group"=>$data['grid_group_id']])->select()->toArray();
        $streets = Db::name("street")->where("type",2)->select()->toArray();
        $roads = Db::name("street")->where("type",1)->select()->toArray();
        return $this->view($template . '/add', [
            'data' => $data,
            'Communities'=>$Communities,
            'grid_groups'=>$grid_groups,
            'grids'=>$grids,
            'streets'=>$streets,
            'roads'=>$roads
        ]);
    }

    /**
     * index 函数
     */
    public function index()
    {
        if (request()->isAjax()) {
            $page = (int)input('page', 1);
            $limit = (int)input('limit', 18);
            $alias = "p";
            $where = $this->buildSelectParams($alias.".");
            $road_id = (int)input('road_id');
            $street_id = (int)input('street_id');
            $road_start = (int)input('road_start');
            $road_end = (int)input('road_end');
            $street_start = (int)input('street_start');
            $street_end = (int)input('street_end');
            $keyword = input('keyword','');
            if($road_id){
                $where['b.road_id']=[$road_id];
            }
            if($street_id){
                $where['b.street_id']=[$street_id];
            }
            if($road_start){
                $where[]=['b.road_start','>=',$road_start];
            }
            if($road_end){
                $where[]=['b.road_end','<=',$road_end];
            }
            if($street_start){
                $where[]=['b.street_start','>=',$street_start];
            }
            if($street_end){
                $where[]=['b.street_end','<=',$street_end];
            }
            if($keyword){
                $where[] = [$alias.".merchant_name|".$alias.".code|".$alias.".corporation|".$alias.".id_code", 'like', '%' . $keyword . '%'];
            }
            $create_start = input("create_start");
            $create_end = input("create_end");
            if($create_start||$create_end){
                if($create_start&&$create_end){
                    $where[] = [$alias.".create_date", "between", [$create_start,$create_end] ];
                }else{
                    if($create_start){
                        $where[] = [$alias.".create_date", ">=", $create_start ];
                    }else{
                        $where[] = [$alias.".create_date", "<=", $create_end ];
                    }
                }
            }
            $count = $this->model->alias($alias)
                ->join("sa_building b", $alias.".building_id=b.id","LEFT")->where($where)->count();
            //$page = $count <= $limit ? 1 : $page;
            $fieldList = $this->model->getFields();
            $order = !array_key_exists('sort', $fieldList) ? 'id' : $alias.'.sort';
            $order = "$alias.community_id,$alias.grid_group_id,$alias.grid_id,".$order;
            $subQuery = $this->model->alias($alias)
                ->join("sa_building b", $alias.".building_id=b.id","LEFT")
                ->field($alias.'.id')->where($where)->order($order)->limit($limit)->page($page)->buildSql();
            $subQuery = '( SELECT object.id FROM ' . $subQuery . ' AS object )';
            $list = $this->model->alias($alias)
                ->join("sa_community c",$alias.".community_id=c.id","LEFT")
                ->join("sa_grid g",$alias.".grid_id=g.id","LEFT")
                ->join("sa_grid gg",$alias.".grid_group_id=gg.id","LEFT")
                ->join("sa_building b", $alias.".building_id=b.id","LEFT")
                ->join("sa_street s",$alias.".street_id=s.id","LEFT")
                ->join("sa_street r",$alias.".road_id=r.id","LEFT")
                ->field($alias.".*,g.grid_name,gg.grid_name grid_group_name,c.community_name,b.building_name,s.name street_name,r.name road_name")
                ->where($alias.'.id in' . $subQuery)->order($order)->select()->toArray();
            return $this->success($this->model->getLastSql(), '/', $list, $count);
        }
        return $this->view('',['params'=>request()->all()]);
    }

    public function proData()
    {
        if (request()->isAjax()) {
            $page = (int)input('page', 1);
            $limit = (int)input('limit', 18);
            $alias = "p";
            $where = $this->buildSelectParams($alias.".");
            $key = input("check_type",0);
            $check_where = $this->buildCheckWhere("$alias.");
            $count = $this->model->alias($alias)
                ->join("sa_building b", $alias.".building_id=b.id","LEFT")->where($where)->where($check_where)->count();
            //$page = $count <= $limit ? 1 : $page;
            $fieldList = $this->model->getFields();
            $order = !array_key_exists('sort', $fieldList) ? 'id' : $alias.'.sort';
            $order = "$alias.community_id,$alias.grid_group_id,$alias.grid_id,".$order;
            $subQuery = $this->model->alias($alias)
                ->join("sa_building b", $alias.".building_id=b.id","LEFT")
                ->field($alias.'.id')->where($where)->where($check_where)->order($order)->limit($limit)->page($page)->buildSql();
            $subQuery = '( SELECT object.id FROM ' . $subQuery . ' AS object )';
            $list = $this->model->alias($alias)
                ->join("sa_community c",$alias.".community_id=c.id","LEFT")
                ->join("sa_grid g",$alias.".grid_id=g.id","LEFT")
                ->join("sa_grid gg",$alias.".grid_group_id=gg.id","LEFT")
                ->join("sa_building b", $alias.".building_id=b.id","LEFT")
                ->join("sa_street s",$alias.".street_id=s.id","LEFT")
                ->join("sa_street r",$alias.".road_id=r.id","LEFT")
                ->field("'{$this->checkRule[$key]['title']}' check_name, ".$alias.".*,g.grid_name,gg.grid_name grid_group_name,c.community_name,b.building_name,s.name street_name,r.name road_name")
                ->where($alias.'.id in' . $subQuery)->order($order)->select()->toArray();
            return $this->success($this->model->getLastSql(), '/', $list, $count);
        }
        return $this->view('',['checkRule'=>$this->checkRule]);
    }

    public function dataImport()
    {
        if (request()->isPost()) {
            set_time_limit(0);
            $filePath = input('file_1');
            $msg = "";
            $spreadsheet = IOFactory::load('public'.$filePath);
            //获取活动工作表
            $worksheet = $spreadsheet->getActiveSheet();
            $Dictionary = [];
            foreach ($this->columnFormat as $key => $item) {
                if($item[0]==3){
                    $dataList = getDictionary($item[2]);
                    $Dictionary[$item[2]] = $dataList;
                }
            }
            $admin_id = get_admin_id();
            $columns_arr=[];
            $update_field = [];
            foreach ($this->columnFormat as $vo){
                if ($vo[0] < 1 || $vo[0] > 4) {
                    continue;
                }
                $columns_arr[$vo[1]]=$vo[1];
                if(array_key_exists('column',$vo)){
                    $vo[1] = $vo['column'];
                }
                $update_field[] = $vo[1];
            }
            $create_time=date('Y-m-d H:i:s');
            $create_by = $this->admin_info['name'];
            $columns=implode(",",$columns_arr);
            $columns .= ",sort,admin_id";
            $insert_sql = "INSERT INTO ".$this->table_name."(".$columns.") VALUES ";
            $values = [];
            Db::query("delete from ".$this->table_name." where admin_id=".$admin_id);
            $error_arr = [];
            foreach ($worksheet->getRowIterator() as $i=>$row) {
                if ($i < $this->row_index||empty($worksheet->getCell('A'.$i)->getValue())) {
                    continue;
                }
                $cellIterator = $row->getCellIterator();
                $cellIterator->setIterateOnlyExistingCells(false); // 遍历所有单元格，包括空单元格
                $values_arr = [];
                foreach ($cellIterator as $key => $cell) {
                    $value = $cell->getValue();
                    if(empty($this->columnFormat[$key])){
                        return $this->error("存在未定义的excel列，请检查导入表格是否和模板表格格式相同");
                    }
                    if(array_key_exists('required',$this->columnFormat[$key])&&empty($value)){
                        $error_arr[$i].="第{$key}列必填项为空;";
                    }
                    $vo = $this->columnFormat[$key];
                    if (($vo[0] < 1&&$vo != -3) || $vo[0] > 5) {
                        continue;
                    } else {
                        switch ($vo[0]) {
                            case 1:
                                $value = empty($value)?'':addslashes((string)$value);
                                break;
                            case 2:
                                $value = $value == '是' ? 1 : 0;
                                break;
                            case 3:
                                $key = array_search($value, $Dictionary[$vo[2]]);
                                if ($key !== false) {
                                    $value = $key;
                                } else {
                                    $value = 0;
                                }
                                break;
                            case 4:
                                if(array_key_exists('type', $vo)){
                                    $key = array_search($value, $this->type[$vo['type']]);
                                    if ($key !== false) {
                                        $value = $key;
                                    } else {
                                        $value = 0;
                                    }
                                }
                        }
                        if(array_key_exists($vo[1], $values_arr)&&empty($value)){
                            continue;
                        }
                        $values_arr[$vo[1]] = "\"".$value."\"";
                    }
                }
                $values_sql = "(".implode(",",$values_arr).",".($i-$this->row_index+1).",".$admin_id.")";
                $values[] = $values_sql;
                $values_arr = [];
                if($i%1000==0){
                        Db::query($insert_sql . implode(",", $values));
                        $values = [];
                }
            }
            if(count($values)){
                Db::query($insert_sql.implode(",",$values));
            }
            Db::query("UPDATE ".$this->table_name." i INNER JOIN sa_community c on i.community_name=c.community_name set i.community_id=c.id");
            Db::query("UPDATE ".$this->table_name." i INNER JOIN sa_grid g on i.community_id=g.community_id and i.grid_group_name=g.grid_name set i.grid_group_id=g.id");
            Db::query("UPDATE ".$this->table_name." i INNER JOIN sa_grid g on i.community_id=g.community_id and i.grid_name=g.grid_name set i.grid_id=g.id");
            Db::query("UPDATE ".$this->table_name." i INNER JOIN sa_building b on i.grid_id = b.grid_id and i.building_name=b.building_name set i.building_id=b.id");
            Db::query("UPDATE ".$this->table_name." i INNER JOIN ".$this->table_source." b on i.grid_id=b.grid_id and i.merchant_name=b.merchant_name set i.".$this->pk."=b.id");
            Db::query("UPDATE ".$this->table_name." i INNER JOIN sa_street s on i.street_name=s.name set i.street_id=s.id");
            Db::query("UPDATE ".$this->table_name." i INNER JOIN sa_street s on i.road_name=s.name set i.road_id=s.id");
            $list = Db::table($this->table_name)->where("admin_id",$admin_id)->where("community_id",0)->field("sort")->select()->toArray();
            foreach ($list as $item){
                $error_arr[$item['sort']+$this->row_index-1].="找不到对应的社区信息;";
            }
            $list = Db::table($this->table_name)->where("admin_id",$admin_id)->where("grid_group_id",0)->field("sort")->select()->toArray();
            foreach ($list as $item){
                $error_arr[$item['sort']+$this->row_index-1].="找不到对应的网格组信息;";
            }
            $list = Db::table($this->table_name)->where("admin_id",$admin_id)->where("grid_id",0)->field("sort")->select()->toArray();
            foreach ($list as $item){
                $error_arr[$item['sort']+$this->row_index-1].="找不到对应的网格信息;";
            }
            // $list = Db::table($this->table_name)->where("admin_id",$admin_id)->where("building_id",0)->field("sort")->select()->toArray();
            // foreach ($list as $item){
            //     $error_arr[$item['sort']+$this->row_index-1].="找不到对应的建筑信息;";
            // }
            if(count($error_arr)>0){
                return $this->error("信息校验未通过",'',['data'=>$error_arr]);
            }
            
            try{
                $sql = $this->buildUpdSql($admin_id);
                Db::query($sql);
                $field=implode(",",$update_field);
                $field.=",sort";
                $insert_sql = "INSERT INTO ".$this->table_source." ({$field},create_time,create_by) select {$field},'$create_time' create_time,'$create_by' create_by from ".$this->table_name." where admin_id = {$admin_id} and ".$this->pk." = 0";
                Db::query($insert_sql);
            }catch (\Exception $e){
                
                return $this->error($e->getMessage());
            }
            
            return $this->success( '导入成功');
        }
        return $this->view();
    }

    public function excelExport($list = [])
    {
        // 文件路径
        $filePath = 'public/tpl/'.$this->tpl_name.'.xlsx';

        // 读取Excel文件
        $spreadsheet = IOFactory::load($filePath);

        // 获取活动工作表
        $sheet = $spreadsheet->getActiveSheet();
        $excelService = new ExcelService();
        $integerValidation = $excelService->integerValidation();
        $decimalValidation = $excelService->decimalValidation();
        $idcardValidation = $excelService->idcardValidation();
        $count = 1000;
        $dataList = ['是', '否'];
        $optionValidation = $excelService->optionValidation($dataList);
        $Dictionary = [];
        $id_code_key = "";
        $birth_key = "";
        $age_key = "";
        $sex_key = "";
        $id_code_item = [];
        foreach ($this->columnFormat as $key=>$item) {
            if($item[0]==2||$item[0]==-2){
                $cellRange = $key .$this->row_index. ':' . $key . $count;
                $sheet->setDataValidation($cellRange, $optionValidation);
            }
            if($item[0]==3||$item[0]==-3){
                $cellRange = $key . $this->row_index. ':' . $key . $count;
                $dataList = getDictionary($item[2]);
                $Dictionary[$item[2]] = $dataList;
                $dataList = array_values($dataList);
                $optionListValidation = $excelService->optionValidation($dataList);
                $sheet->setDataValidation($cellRange, $optionListValidation);
            }
            if(array_key_exists('number',$item)){
                $cellRange = $key.$this->row_index. ':' .$key. $count;
                $sheet->setDataValidation($cellRange, $integerValidation);
            }
            if($item[1]=='id_code'){
                $id_code_key = $key;
            }else if($item[1]=='birthday'){
                $birth_key = $key;
                $sheet->getStyle("$birth_key".$this->row_index.":$birth_key". ($this->row_index+$count)+10)->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_DATE_YYYYMMDD2);
            }else if($item[1]=='age'){
                $age_key = $key;
                $sheet->getStyle("$age_key".$this->row_index.":$age_key". ($this->row_index+$count)+10)->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_GENERAL);
            }else if($item[1]=='gender'){
                $sex_key = $key;
            }else if(array_key_exists('id_code_type',$item)){
                $id_code_item[$key] =$item;
                if($item['id_code_type']=='birthday'){
                    $sheet->getStyle("$key".$this->row_index.":$key". ($this->row_index+$count)+10)->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_DATE_YYYYMMDD2);
                }else if($item['id_code_type']=='age'){
                    $sheet->getStyle("$key".$this->row_index.":$key". ($this->row_index+$count)+10)->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_GENERAL);
                }
            }
        }
        if (count($list) > 0) {
            $i = $this->row_index;
            foreach ($list as $item) {
                foreach ($this->columnFormat as $column => $value) {
                    switch ($value[0]) {
                        case 1:
                            $item[$value[1]]=$item[$value[1]]=='0000-00-00'?'':$item[$value[1]];
                            $sheet->setCellValueExplicit($column . $i, trim((string)$item[$value[1]], "\""), DataType::TYPE_STRING);
                            break;
                        case 2:
                        case -2:
                            $sheet->setCellValue($column . $i, $item[$value[1]] == 1 ? '是' : '否');
                            break;
                        case 3:
                        case -3:
                            $index = $item[$value[1]];
                            if ($index) {
                                $sheet->setCellValue($column . $i, empty($Dictionary[$value[2]][$index]) ? '' : $Dictionary[$value[2]][$index]);
                            }
                            break;
                        case 6:
                            break;
                        case 5:
                            $sheet->setCellValue($column . $i, $item[$value[1]] != $value[2] ? '是' : '否');
                            break;
                        case 4:
                            if(array_key_exists("type",$value)&&$item[$value[1]]){
                                $value = $this->type[$value["type"]][$item[$value[1]]];
                            }else{
                                $value = $item[$value[1]];
                            }
                            $sheet->setCellValueExplicit($column . $i, trim((string)$value, "\""), DataType::TYPE_STRING);
                            break;
                        case 0:
                            $sheet->setCellValue($column . $i, $value[1]);
                            break;
                    }
                }
                if($id_code_key){
                    if($birth_key){
                        $sheet->setCellValue($birth_key . $i, "=DATE(MID($id_code_key$i,7,4),MID($id_code_key$i,11,2),MID($id_code_key$i,13,2))");
                    }
                    if($age_key){
                        $sheet->setCellValue($age_key . $i, "=INT((TODAY()-DATE(MID($id_code_key$i,7,4),MID($id_code_key$i,11,2),MID($id_code_key$i,13,2)))/365.25)");
                    }
                    if($sex_key){
                        $sheet->setCellValue($sex_key . $i, "=IF(MOD(MID($id_code_key$i,17,1),2)=0,\"女\",\"男\")");
                    }
                }
                foreach ($id_code_item as $key =>$item){
                    if($item['id_code_type']=='birthday'){
                        $sheet->setCellValue($key . $i, "=DATE(MID(".$item['id_code_key']."$i,7,4),MID(".$item['id_code_key']."$i,11,2),MID(".$item['id_code_key']."$i,13,2))");
                    }else if($item['id_code_type']=='age'){
                        $sheet->setCellValue($key . $i, "=INT((TODAY()-DATE(MID(".$item['id_code_key']."$i,7,4),MID(".$item['id_code_key']."$i,11,2),MID(".$item['id_code_key']."$i,13,2)))/365.25)");
                    }else if($item['id_code_type']=='gender'){
                        $sheet->setCellValue($sex_key . $i, "=IF(MOD(MID(".$item['id_code_key']."$i,17,1),2)=0,\"女\",\"男\")");
                    }
                }
                $i++;
            }
            for($j=0;$j<10;$j++){
                if($id_code_key){
                    if($birth_key){
                        $sheet->setCellValue($birth_key . $i, "=DATE(MID($id_code_key$i,7,4),MID($id_code_key$i,11,2),MID($id_code_key$i,13,2))");
                    }
                    if($age_key){
                        $sheet->setCellValue($age_key . $i, "=INT((TODAY()-DATE(MID($id_code_key$i,7,4),MID($id_code_key$i,11,2),MID($id_code_key$i,13,2)))/365.25)");
                    }
                    if($sex_key){
                        $sheet->setCellValue($sex_key . $i, "=IF(MOD(MID($id_code_key$i,17,1),2)=0,\"女\",\"男\")");
                    }
                }
                foreach ($id_code_item as $key =>$item){
                    if($item['id_code_type']=='birthday'){
                        $sheet->setCellValue($key . $i, "=DATE(MID(".$item['id_code_key']."$i,7,4),MID(".$item['id_code_key']."$i,11,2),MID(".$item['id_code_key']."$i,13,2))");
                    }else if($item['id_code_type']=='age'){
                        $sheet->setCellValue($key . $i, "=INT((TODAY()-DATE(MID(".$item['id_code_key']."$i,7,4),MID(".$item['id_code_key']."$i,11,2),MID(".$item['id_code_key']."$i,13,2)))/365.25)");
                    }else if($item['id_code_type']=='gender'){
                        $sheet->setCellValue($sex_key . $i, "=IF(MOD(MID(".$item['id_code_key']."$i,17,1),2)=0,\"女\",\"男\")");
                    }
                }
                $i++;
            }
            // 保存Excel文件
            $writer = new Xlsx($spreadsheet);
            $day = date('Ymd');
            $time = date('his');
            $dir = '/download/' . $day;
            if (!file_exists('public' . $dir)) {
                mkdir('public' . $dir, 0777, true);
            }
            $filePath = $dir . '/'.$this->tpl_name.'(' . $day . $time . ').xlsx';
            $writer->save('public' . $filePath);
            return $filePath;
        }
        // 保存Excel文件
        $writer = new Xlsx($spreadsheet);
        $writer->save('public/tpl/'.$this->tpl_name.'.xlsx');
        return '';
    }

    public function downloadExcelTpl()
    {
        $this->excelExport();
        return $this->redirect('/tpl/'.$this->tpl_name.'.xlsx');
    }


    public function dataToExcel()
    {
        $alias = "h";
        $where = $this->buildSelectParams($alias.".");
        $road_id = (int)input('road_id');
        $street_id = (int)input('street_id');
        $road_start = (int)input('road_start');
        $road_end = (int)input('road_end');
        $street_start = (int)input('street_start');
        $street_end = (int)input('street_end');
        $keyword = input('keyword','');
        if($road_id){
            $where['b.road_id']=[$road_id];
        }
        if($street_id){
            $where['b.street_id']=[$street_id];
        }
        if($road_start){
            $where[]=['b.road_start','>=',$road_start];
        }
        if($road_end){
            $where[]=['b.road_end','<=',$road_end];
        }
        if($street_start){
            $where[]=['b.street_start','>=',$street_start];
        }
        if($street_end){
            $where[]=['b.street_end','<=',$street_end];
        }
        if($keyword){
            $where[] = [$alias.".merchant_name|".$alias.".code|".$alias.".corporation|".$alias.".id_code", 'like', '%' . $keyword . '%'];
        }
        $fieldList = $this->model->getFields();
        $order = !array_key_exists('sort', $fieldList) ? 'id' : $alias.'.sort';
        $order = "$alias.community_id,$alias.grid_group_id,$alias.grid_id,".$order;
        $list = $this->model->alias($alias)
            ->join("sa_community c",$alias.".community_id=c.id","LEFT")
            ->join("sa_grid g",$alias.".grid_id=g.id","LEFT")
            ->join("sa_grid gg",$alias.".grid_group_id=gg.id","LEFT")
            ->join("sa_building b", $alias.".building_id=b.id","LEFT")
            ->join("sa_street s",$alias.".street_id=s.id","LEFT")
            ->join("sa_street r",$alias.".road_id=r.id","LEFT")
            ->field($alias.".*,g.grid_name,gg.grid_name grid_group_name,c.community_name,b.building_name,s.name street_name,r.name road_name")
            ->where($where)->order($order)->select()->toArray();
        $filePath = $this->excelExport($list);
        if(request()->isPost()){
            return $this->success('',$filePath);
        }
        return $this->redirect($filePath);
    }

}
