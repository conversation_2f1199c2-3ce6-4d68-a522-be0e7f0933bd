<?php
declare (strict_types = 1);
namespace app\admin\controller;

use app\AdminController;
use Webman\Http\Request;
use app\common\model\Addmap as AddmapModel;

/**
 * addmap
 * 打卡记录
 * <!---->
 * Class Addmap
 * @package app\admin\controller
 */
class Addmap extends AdminController
{
    /**
     * Addmap模型对象
     * @var \app\common\model\Addmap
     */

    public function __construct()
    {
        parent::__construct();
        $this->model = new AddmapModel;
    }

    /**
     * 默认生成的方法为index/add/edit/del/status 五个方法
     * 当创建CURD的时候，DIY的函数体和模板为空，请自行编写代码
     */

    public function index()
    {
        if (request()->isAjax()) {
            $page = (int)input('page', 1);
            $limit = (int)input('limit', 18);
            $keyword_community_id = (int)input('community_id', '');
            $keyword_grid_id = (int)input('grid_id', '');
            $keyword_grid_group_id = (int)input('grid_group_id', '');
            
            if ($keyword_community_id) {
                $wheres['c.id'] = $keyword_community_id;
            }
            if ($keyword_grid_id) {
                $wheres['a.grid_id'] = $keyword_grid_id;
            }
            if ($keyword_grid_group_id) {
                $wheres['g.grid_group_id'] = $keyword_grid_group_id;
            }
            $keyword_nickname = input('nickname','');
            $keyword_title = input('title','');
            if($keyword_nickname){
	            $wheres[] = ["a.nickname", 'like', '%' . $keyword_nickname . '%'];
	        }
	        if($keyword_title){
	            $wheres[] = ["b.title", 'like', '%' . $keyword_title . '%'];
	        }
	        $keyword = input('keyword','');
	        $wheres[] = ["a.nickname|"."b.title", 'like', '%' . $keyword . '%'];
            $where = $this->buildSelectParams("b.",false);
            $count = $this->model->alias("b")->where($where)->count();
            //$page = $count <= $limit ? 1 : $page;
            $fieldList = $this->model->getFields();
            $order = !array_key_exists('sort', $fieldList) ? 'id' : 'sort';
            $subQuery = $this->model->alias("b")->field('b.id')->where($where)->buildSql();
            $subQuery = '( SELECT object.id FROM ' . $subQuery . ' AS object )';
            $list = $this->model->alias("b")
            	->join("sa_admin a","a.id=b.admin_id")
            	->join("sa_community c","a.community_id=c.id","LEFT")
                ->join("sa_grid g","a.grid_id=g.id","LEFT")
                ->join("sa_grid gg","a.grid_group_id=gg.id","LEFT")
                ->field("b.*,g.grid_name,gg.grid_name grid_group_name,c.community_name,a.nickname")
                ->where('b.id in' . $subQuery)
                // ->limit($limit)
                ->where($wheres)->order($order, 'desc')
                ->limit($limit)->page($page)->select()->toArray();
            return $this->success($this->model->getLastSql(), '/', $list, $count);
        }
        return $this->view();
    }
    


}
