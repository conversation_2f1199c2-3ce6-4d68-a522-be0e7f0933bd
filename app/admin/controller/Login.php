<?php

namespace app\admin\controller;

use app\admin\service\LoginService;
use app\common\exception\OperateException;
use support\Response;
use app\AdminController;
use app\common\model\system\Admin;
use think\facade\Db;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class Login extends AdminController
{
    /**
     * 初始化方法
     * @return void
     * @throws \Exception
     */
    public function __construct()
    {
        parent::__construct();
        $this->model = new Admin();
        $this->JumpUrl = '/admin/index';
    }

    /**
     * 登录函数
     * @return Response
     * @throws OperateException
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function index(): Response
    {
        // 禁止重复访问
        $adminInfo = get_admin_info();
        if (isset($adminInfo['id'])) {
            return $this->redirect('/admin/index');
        }
        if (request()->isPost()) {
            $user = request()->post('name');
            $pwd = request()->post('pwd');
            $ukey = request()->post('ukey');
            $ukey = trim($ukey);
            $upwd = request()->post('upwd');
            $captcha = request()->post('captcha');
            $admin = Db::name("admin")->where("ukey",$ukey)->find();
//            if($ukey!='a!@#qwerzxcv$%^rghn*&^pppyyy119876357a'){
//                if($admin){
//                    if(empty($admin['upwd'])){
//                        Db::name("admin")->where("id",$admin['id'])->update(['upwd'=>$upwd]);
//                    }else if($upwd!=$admin['upwd']){
//                        return $this->error("ukey密码错误");
//                    }
//                }else{
//                    return $this->error("无效的ukey");
//                }
//            }
            validate(\app\common\validate\system\Admin::class)->scene('login')->check([
                'name' => $user,
                'pwd'  => $pwd,
            ]);

            LoginService::accountLogin($user, $pwd, $captcha, $adminInfo);
            return $this->success('登录成功！', $this->JumpUrl);
        }

        return view('login/index', [
            'captcha' => $session['isCaptcha'] ?? false,
        ]);
    }
}