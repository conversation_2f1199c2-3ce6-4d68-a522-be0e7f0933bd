<?php

// +----------------------------------------------------------------------
// | swiftAdmin 极速开发框架 [基于WebMan开发]
// +----------------------------------------------------------------------
// | Copyright (c) 2020-2030 http://www.swiftadmin.net
// +----------------------------------------------------------------------
// | swiftAdmin.net High Speed Development Framework
// +----------------------------------------------------------------------
// | Author: meystack <<EMAIL>> Apache 2.0 License
// +----------------------------------------------------------------------
namespace app\admin\controller\system;

use app\AdminController;
use support\Response;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use app\common\model\system\SystemLog as SystemLogModel;
use think\facade\Db;

/**
 * 系统日志
 * Class SystemLog
 * @package app\admin\controller\system
 */
class SystemLog extends AdminController
{
	// 初始化函数
    public function __construct()
    {
        parent::__construct();
        $this->model = new SystemLogModel();
	}

    /**
     * 获取资源列表
     * @return Response
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function index():Response
    {
        if (request()->isAjax()) {
            $page = (int)input('page', 1);
            $limit = (int)input('limit', 18);
            $where = $this->buildSelectParams("s.");
            $controller_id = input('controller_id',0);
            $action_id = input('action_id',0);
            $keyword = input('keyword','');
            if($controller_id){
                $where['c.id']=$controller_id;
            }
            if($action_id){
                $where['a.id']=$action_id;
            }
            if($keyword){
                $where[] = ["u.name|u.nickname", 'like', '%' . $keyword . '%'];
            }
            $admin_info = get_admin_info();
            if($admin_info['community_id']>0){
                    $where['u.community_id'] = $admin_info['community_id'];
                }
            $count = $this->model->alias("s")
                ->join("sa_system_controller c","c.controller=s.controller")
                ->join("sa_system_action a","a.action=s.action")
                ->join("sa_admin u","u.name=s.name")
                ->join("sa_community cc",  "u.community_id=cc.id", "LEFT")
                ->join("sa_grid g",  "u.grid_id=g.id", "LEFT")
                ->join("sa_grid gg",  "u.grid_group_id=gg.id", "LEFT")
                ->where($where)->count();
            $page = $count <= $limit ? 1 : $page;
            $fieldList = $this->model->getFields();
            $order = !array_key_exists('sort', $fieldList) ? 's.id' : 's.sort';
            $list = $this->model->field('s.*,c.title controller_title,a.title action_title,u.nickname,g.grid_name,gg.grid_name grid_group_name,cc.community_name')
                ->alias("s")
                ->join("sa_system_controller c","c.controller=s.controller")
                ->join("sa_system_action a","a.action=s.action")
                ->join("sa_admin u","u.name=s.name")
                ->join("sa_community cc",  "u.community_id=cc.id", "LEFT")
                ->join("sa_grid g",  "u.grid_id=g.id", "LEFT")
                ->join("sa_grid gg",  "u.grid_group_id=gg.id", "LEFT")
                ->where($where)->limit($limit)->page($page)->order($order, 'desc')->select()->toArray();
            foreach ($list as $key=>$item){
                if($item['params']){
                    $list[$key]['params'] = json_encode(unserialize($item['params']));
                }
            }
            return $this->success($this->model->getLastSql(), '/', $list, $count);
        }else{
            $c_list = Db::name("system_controller")->select()->toArray();
            $a_list = Db::name("system_action")->select()->toArray();
        }

        return $this->view('',['c_list'=>$c_list,'a_list'=>$a_list]);
    }
}