<include file="/public/header"/>
<php>
    $community_id = empty($data['community_id'])?$params['community_id']:$data['community_id'];
    $organization_id = empty($data['organization_id'])?$params['organization_id']:$data['organization_id'];
    if($organization_id==5||$organization_id==6){
        $GridGroups = getGridGroup($community_id);
        $grid_group_id = empty($data['grid_group_id'])?$GridGroups[0]['id']:$data['grid_group_id'];
        if($organization_id==6){
            $Grids = getGrid($grid_group_id);
            $grid_id = empty($data['grid_id'])?$Grids[0]['id']:$data['grid_id'];
        }
    }
</php>
<link href="__STATICADMIN__css/content.css" rel="stylesheet" type="text/css"/>
<style>
    .layui-imagesbox img{height:200px; width:auto;}
    .layui-imagesbox input{width:144px;}
</style>
<div class="layui-fluid">
    <form class="layui-form layui-card">

        <div class="layui-card-body">
            <gt name="$data.id" value="0">
                <input type="text" name="id" value="{$data.id}" hidden="">
                <else/>
                <input type="text" name="id" value="" hidden="">
                <input type="text" name="organization_id" value="{$params.organization_id}" hidden="">
                <input type="text" name="community_id" value="{$params.community_id}" hidden="">
            </gt>
            <div class="layui-form-item layui-row">
                <div class="layui-col-md4 layui-grid-0" data-index="0">
                    <div class="layui-form-item">
                        <div class="layui-input-block" style="margin-left: 0;">
                            <blockquote class="layui-elem-quote">人员头像</blockquote>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="layui-input-block" style="margin-left:0">
                            <div class="layui-imagesbox">
                                <php>
                                    if (strpos($data['avatar'], 'http') === 0) {
                                    $data['avatar'] = [['src'=>$data['avatar'],'title'=>'图片']];
                                    }else{
                                    $data['avatar'] = json_decode($data['avatar'],true);
                                    }
                                </php>
                                <!-- // 循环输出代码 -->
                                <notempty name="$data['avatar']">
                                    <volist name="$data['avatar']" id="vo">
                                        <div class="layui-input-inline layui-uplpad-image">
                                            <img src="{$vo.src}" lay-image-click>
                                            <input type="text" name="avatar[{$key}][src]" class="layui-hide"
                                                   value="{$vo.src}">
                                            <input type="text" name="avatar[{$key}][title]"
                                                   class="layui-input  layui-image" value="{$vo.title}"
                                                   placeholder="图片简介">
                                            <span class="layui-badge layui-badge-red" data-name="avatar"
                                                  onclick="layui.admin.resetInput(this,'images');">删除</span>
                                        </div>
                                    </volist>
                                </notempty>
                                <div class="layui-input-inline layui-uplpad-image">
                                    <div class="layui-upload-drag" lay-upload="avatar" data-type="multiple"
                                         >
                                        <i class="layui-icon layui-icon-upload"></i>
                                        <p>点击上传，或将文件拖拽到此处</p>
                                        <div class="layui-hide"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md8 layui-grid-1" data-index="1">
                    <div class="layui-input-block" style="margin-left: 0;">
                        <blockquote class="layui-elem-quote">人员信息</blockquote>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">* </font>人员姓名</label>
                        <div class="layui-input-block">
                            <input class="layui-input" name="name" placeholder="请输入"
                                   lay-verify="required" value="{$data.name}">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">* </font>手机号码</label>
                        <div class="layui-input-block">
                            <input class="layui-input" name="phone" placeholder="请输入"
                                   lay-verify="required|phone" value="{$data.phone}">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">* </font>职务</label>
                        <div class="layui-input-block">
                            <input class="layui-input" name="post" placeholder="请输入"
                                   lay-verify="" value="{$data.post}">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">人员详情</label>
                        <div class="layui-input-block">
                            <textarea name="person_details" class="layui-textarea" placeholder="请输入人员详情">{$data.person_details}</textarea>
                        </div>
                    </div>
                    <gt name="$grid_group_id" value="0">
                        <div class="layui-form-item">
                            <label class="layui-form-label"><font color="red">* </font>网格组</label>
                            <div class="layui-input-block">
                                <select id="grid_group" name="grid_group_id" lay-search lay-filter="gridGroupChange" lay-verify="required">
                                    <foreach name="GridGroups" item="vo">
                                        <option value="{$vo.id}"
                                        <if (isset($data['id']) && $data['id']) >
                                        <in name="$vo.id" value="$data.grid_group_id">selected</in>
                                        </if>
                                        >{$vo.grid_name}</option>
                                    </foreach>
                                </select>
                            </div>
                        </div>
                    </gt>
                    <gt name="$grid_id" value="0">
                    <div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">* </font>网格</label>
                        <div class="layui-input-block">
                            <select id="grid" name="grid_id" lay-search lay-filter="gridChange" lay-verify="required">
                                <foreach name="Grids" item="vo">
                                    <option value="{$vo.id}"
                                    <if (isset($data['id']) && $data['id']) >
                                    <in name="$vo.id" value="$data.grid_id">selected</in>
                                    </if>
                                    >{$vo.grid_name}</option>
                                </foreach>
                            </select>
                        </div>
                    </div>
                    </gt>
                </div>
            </div>
            <div class="layui-footer layui-form-footer">
                <button class="layui-btn layui-btn-primary" type="button" sa-event="closeDialog">{:__('取消')}</button>
                <button class="layui-btn" type="button" lay-filter="submitIframe" lay-submit>{:__('提交')}</button>
            </div>
    </form>
</div>
<include file="/public/static"/>
<include file="/public/footer"/>
<include file="/public/select"/>