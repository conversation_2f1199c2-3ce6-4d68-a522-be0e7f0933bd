<include file="/public/header"/>

<php>
  $Communities = getCommunity();
  if($Communities[0]['id']==1){
  unset($Communities[0]);
  $Communities = array_values($Communities);
  }
  $community_id = empty($data['community_id'])?$Communities[0]['id']:$data['community_id'];
  $GridGroups = getGridGroup($community_id);
  $grid_group_id = empty($data['grid_group_id'])?$GridGroups[0]['id']:$data['grid_group_id'];
  $Grids = getGrid($grid_group_id);
  if($data['person_id']>0){
    $person = getPerson(0,$data['person_id']);
  }
  $jurisdiction = getDictionary(501);
</php>
<!---->

<link href="__STATICADMIN__css/content.css" rel="stylesheet" type="text/css"/>
<div class="layui-fluid">
  <form class="layui-form layui-card">

    <div class="layui-card-body">
      <gt name="$data.id" value="0">
        <input type="text" name="id" value="{$data.id}" hidden="">
        <else/>
        <input type="text" name="id" value="" hidden="">
      </gt>
      <input type="text" id="person_id" name="person_id" value="{$data.person_id??''}" lay-verify="required" hidden="">
      <div class="layui-input-block" style="margin-left: 0;"><blockquote class="layui-elem-quote">基本信息</blockquote></div>
      <include file="/public/person_info"/>
      <div class="layui-input-block" style="margin-left: 0;"><blockquote class="layui-elem-quote">刑满释放人员信息</blockquote></div>
      <div class="layui-form-item layui-row">
        <div class="layui-col-md4 layui-grid-0" data-index="0">
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;"><font color="red">* </font>所属社区</label>
            <div class="layui-input-block" style="margin-left:150px">
              <select  name="community_id" lay-search lay-filter="communityChange" lay-verify="required">
                <foreach name="Communities" item="vo">
                  <option value="{$vo.id}"
                  <if (isset($data['id']) && $data['id']) >
                  <in name="$vo.id" value="$data.community_id">selected</in>
                  </if>
                  >{$vo.community_name}</option>
                </foreach>
              </select>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;"><font color="red">* </font>所属网格组</label>
            <div class="layui-input-block" style="margin-left:150px">
              <select id="grid_group" name="grid_group_id" lay-search lay-filter="gridGroupChange" lay-verify="required">
                <foreach name="GridGroups" item="vo">
                  <option value="{$vo.id}"
                  <if (isset($data['id']) && $data['id']) >
                  <in name="$vo.id" value="$data.grid_group_id">selected</in>
                  </if>
                  >{$vo.grid_name}</option>
                </foreach>
              </select>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;"><font color="red">* </font>所属网格</label>
            <div class="layui-input-block" style="margin-left:150px">
              <select id="grid" name="grid_id" lay-search lay-filter="gridChange" lay-verify="required">
                <foreach name="Grids" item="vo">
                  <option value="{$vo.id}"
                  <if (isset($data['id']) && $data['id']) >
                  <in name="$vo.id" value="$data.grid_id">selected</in>
                  </if>
                  >{$vo.grid_name}</option>
                </foreach>
              </select>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;"><font color="red">*</font>管理类型</label>
            <div class="layui-input-block" style="margin-left:150px">
              <select name="jurisdiction" id="jurisdiction" lay-filter="jurisdiction"  lay-verify="required">
                <option value=''>请选择</option>
                <foreach name="jurisdiction" item="vo" key="key">
                  <option value="{$key}"
                  <if (isset($data['id']) && $data['id']) >
                  <in name="$key" value="$data.jurisdiction">selected</in>
                  </if>
                  >{$vo}</option>
                </foreach>
              </select>
            </div>
          </div>
          <if (array_key_exists('grid_user', $data)) >
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">网格员</label>
            <div class="layui-input-block" style="margin-left:150px">
              <input class="layui-input" name="grid_user" value="{$data.grid_user}">
            </div>
          </div>
          </if>
          <if (array_key_exists('grid_phone', $data)) >
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">网格员电话</label>
            <div class="layui-input-block" style="margin-left:150px">
              <input class="layui-input" name="grid_phone" value="{$data.grid_phone}" lay-verify="phone">
            </div>
          </div>
          </if>
        </div>
        <div class="layui-col-md4 layui-grid-1" data-index="1">
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">刑科</label>
            <div class="layui-input-block" style="margin-left:150px">
              <input class="layui-input" name="section" value="{$data.section}">
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">刑期</label>
            <div class="layui-input-block" style="margin-left:150px">
              <input class="layui-input" name="releases" value="{$data.releases}">
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">释放日期</label>
            <div class="layui-input-block" style="margin-left:150px">
              <input class="layui-input datepicker"
                     lay-datetime="" name="release_date"
                     data-datetype="date"
                     data-dateformat="yyyy-MM-dd"
                     placeholder="yyyy-MM-dd"
                     data-maxvalue="9999-12-31"
                     data-minvalue="1900-01-01"
                     data-range=""
                     value="{$data.release_date}">
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">安置帮教(开始)</label>
            <div class="layui-input-block" style="margin-left:150px">
              <input class="layui-input datepicker"
                     lay-datetime="" name="help_start"
                     data-datetype="date"
                     data-dateformat="yyyy-MM-dd"
                     placeholder="yyyy-MM-dd"
                     data-range=""
                     value="{$data.help_start}">
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">安置帮教(截止)</label>
            <div class="layui-input-block" style="margin-left:150px">
              <input class="layui-input datepicker"
                     lay-datetime="" name="help_end"
                     data-datetype="date"
                     data-dateformat="yyyy-MM-dd"
                     placeholder="yyyy-MM-dd"
                     data-maxvalue="9999-12-31"
                     data-minvalue="{:date('Y-m-d')}"
                     data-range=""
                     value="{$data.help_end}">
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">备注</label>
            <div class="layui-input-block" style="margin-left:150px">
              <input class="layui-input" name="details" value="{$data.details}">
            </div>
          </div>
        </div>
        <div class="layui-col-md4 layui-grid-2" data-index="2">

        </div>
      </div>
      <div class="layui-footer layui-form-footer">
        <button class="layui-btn layui-btn-primary" type="button" sa-event="closeDialog">{:__('取消')}</button>
        <button class="layui-btn" type="button" lay-filter="submitIframe" lay-submit>{:__('提交')}</button>
      </div>
  </form>
</div>
<include file="/public/static"/>
<include file="/public/footer"/>
<include file="/public/select"/>
<include file="/public/person_common"/>