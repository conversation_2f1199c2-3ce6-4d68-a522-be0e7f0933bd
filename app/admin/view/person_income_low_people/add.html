<include file="/public/header"/>

<php>
  $Communities = getCommunity();
  if($Communities[0]['id']==1){
  unset($Communities[0]);
  $Communities = array_values($Communities);
  }
  $community_id = empty($data['community_id'])?$Communities[0]['id']:$data['community_id'];
  $GridGroups = getGridGroup($community_id);
  $grid_group_id = empty($data['grid_group_id'])?$GridGroups[0]['id']:$data['grid_group_id'];
  $Grids = getGrid($grid_group_id);
  if($data['person_id']>0){
    $person = getPerson(0,$data['person_id']);
  }
  $jurisdiction = getDictionary(501);
  $household_type = getDictionary(447);
  $household = getDictionary(453);
</php>
<!---->

<link href="__STATICADMIN__css/content.css" rel="stylesheet" type="text/css"/>
<div class="layui-fluid">
  <form class="layui-form layui-card">

    <div class="layui-card-body">
      <gt name="$data.id" value="0">
        <input type="text" name="id" value="{$data.id}" hidden="">
        <else/>
        <input type="text" name="id" value="" hidden="">
      </gt>
      <input type="text" id="person_id" name="person_id" value="{$data.person_id??''}" lay-verify="required" hidden="">
      <div class="layui-input-block" style="margin-left: 0;"><blockquote class="layui-elem-quote">基本信息</blockquote></div>
      <include file="/public/person_info"/>
      <div class="layui-input-block" style="margin-left: 0;"><blockquote class="layui-elem-quote">低保人群信息</blockquote></div>
      <div class="layui-form-item layui-row">
        <div class="layui-col-md4 layui-grid-0" data-index="0">
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;"><font color="red">* </font>所属社区</label>
            <div class="layui-input-block" style="margin-left:150px">
              <select  name="community_id" lay-search lay-filter="communityChange" lay-verify="required">
                <foreach name="Communities" item="vo">
                  <option value="{$vo.id}"
                  <if (isset($data['id']) && $data['id']) >
                  <in name="$vo.id" value="$data.community_id">selected</in>
                  </if>
                  >{$vo.community_name}</option>
                </foreach>
              </select>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;"><font color="red">* </font>所属网格组</label>
            <div class="layui-input-block" style="margin-left:150px">
              <select id="grid_group" name="grid_group_id" lay-search lay-filter="gridGroupChange" lay-verify="required">
                <foreach name="GridGroups" item="vo">
                  <option value="{$vo.id}"
                  <if (isset($data['id']) && $data['id']) >
                  <in name="$vo.id" value="$data.grid_group_id">selected</in>
                  </if>
                  >{$vo.grid_name}</option>
                </foreach>
              </select>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;"><font color="red">* </font>所属网格</label>
            <div class="layui-input-block" style="margin-left:150px">
              <select id="grid" name="grid_id" lay-search lay-filter="gridChange" lay-verify="required">
                <foreach name="Grids" item="vo">
                  <option value="{$vo.id}"
                  <if (isset($data['id']) && $data['id']) >
                  <in name="$vo.id" value="$data.grid_id">selected</in>
                  </if>
                  >{$vo.grid_name}</option>
                </foreach>
              </select>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;"><font color="red">*</font>管理类型</label>
            <div class="layui-input-block" style="margin-left:150px">
              <select name="jurisdiction" id="jurisdiction" lay-filter="jurisdiction"  lay-verify="required">
                <option value=''>请选择</option>
                <foreach name="jurisdiction" item="vo" key="key">
                  <option value="{$key}"
                  <if (isset($data['id']) && $data['id']) >
                  <in name="$key" value="$data.jurisdiction">selected</in>
                  </if>
                  >{$vo}</option>
                </foreach>
              </select>
            </div>
          </div>
          <if (array_key_exists('grid_user', $data)) >
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">网格员</label>
            <div class="layui-input-block" style="margin-left:150px">
              <input class="layui-input" name="grid_user" value="{$data.grid_user}">
            </div>
          </div>
          </if>
          <if (array_key_exists('grid_phone', $data)) >
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">网格员电话</label>
            <div class="layui-input-block" style="margin-left:150px">
              <input class="layui-input" name="grid_phone" value="{$data.grid_phone}" lay-verify="phone">
            </div>
          </div>
          </if>
        </div>
        <div class="layui-col-md4 layui-grid-1" data-index="1">
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">家庭人口数</label>
            <div class="layui-input-block" style="margin-left:150px">
              <input class="layui-input" name="people_number" value="{$data.people_number}" lay-verify="number">
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">月总收入</label>
            <div class="layui-input-block" style="margin-left:150px">
              <input class="layui-input" name="income_month" value="{$data.income_month}" lay-verify="number">
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">人均月收入</label>
            <div class="layui-input-block" style="margin-left:150px">
              <input class="layui-input" name="income_avg" value="{$data.income_avg}" lay-verify="number">
            </div>
          </div>

          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">家庭类别</label>
            <div class="layui-input-block" style="margin-left:150px">
              <select name="household_type" >
                <option value=''>请选择</option>
                <foreach name="household_type" item="vo" key="key">
                  <option value="{$key}"
                  <if (isset($data['id']) && $data['id']) >
                  <in name="$key" value="$data.household_type">selected</in>
                  </if>
                  >{$vo}</option>
                </foreach>
              </select>
            </div>
          </div>

          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">户口</label>
            <div class="layui-input-block" style="margin-left:150px">
              <select name="household" >
                <option value=''>请选择</option>
                <foreach name="household" item="vo" key="key">
                  <option value="{$key}"
                  <if (isset($data['id']) && $data['id']) >
                  <in name="$key" value="$data.household">selected</in>
                  </if>
                  >{$vo}</option>
                </foreach>
              </select>
            </div>
          </div>

          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">所在单位</label>
            <div class="layui-input-block" style="margin-left:150px">
              <input class="layui-input" name="unit" value="{$data.unit}">
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">备注</label>
            <div class="layui-input-block" style="margin-left:150px">
              <input class="layui-input" name="details" value="{$data.details}">
            </div>
          </div>
        </div>
        <div class="layui-col-md4 layui-grid-2" data-index="2">
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">家庭成员1姓名</label>
            <div class="layui-input-block" style="margin-left:150px">
              <input class="layui-input" name="member_name_1" value="{$data.member_name_1}">
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">家庭成员1身份证号</label>
            <div class="layui-input-block" style="margin-left:150px">
              <input class="layui-input" name="member_id_code_1" value="{$data.member_id_code_1}" lay-verify="identify">
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">家庭成员2姓名</label>
            <div class="layui-input-block" style="margin-left:150px">
              <input class="layui-input" name="member_name_2" value="{$data.member_name_2}">
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">家庭成员2身份证号</label>
            <div class="layui-input-block" style="margin-left:150px">
              <input class="layui-input" name="member_id_code_2" value="{$data.member_id_code_2}" lay-verify="identify">
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">家庭成员3姓名</label>
            <div class="layui-input-block" style="margin-left:150px">
              <input class="layui-input" name="member_name_3" value="{$data.member_name_3}">
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">家庭成员3身份证号</label>
            <div class="layui-input-block" style="margin-left:150px">
              <input class="layui-input" name="member_id_code_3" value="{$data.member_id_code_3}" lay-verify="identify">
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">家庭成员4姓名</label>
            <div class="layui-input-block" style="margin-left:150px">
              <input class="layui-input" name="member_name_4" value="{$data.member_name_4}">
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">家庭成员4身份证号</label>
            <div class="layui-input-block" style="margin-left:150px">
              <input class="layui-input" name="member_id_code_4" value="{$data.member_id_code_4}" lay-verify="identify">
            </div>
          </div>
        </div>
      </div>
      <div class="layui-footer layui-form-footer">
        <button class="layui-btn layui-btn-primary" type="button" sa-event="closeDialog">{:__('取消')}</button>
        <button class="layui-btn" type="button" lay-filter="submitIframe" lay-submit>{:__('提交')}</button>
      </div>
  </form>
</div>
<include file="/public/static"/>
<include file="/public/footer"/>
<include file="/public/select"/>
<include file="/public/person_common"/>