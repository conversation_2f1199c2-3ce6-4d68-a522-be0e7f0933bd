<include file="/public/header" />
<!---->
<php>
    $ids = [54,58,56,501];
    $dictionaryGroup = getDictionaryGroup($ids);
    $nation = $dictionaryGroup[54];
    $face = $dictionaryGroup[58];
    $education = $dictionaryGroup[56];
    $jurisdiction = $dictionaryGroup[501];
    $sex = [2=>'男',1=>'女'];
</php>
<div class="layui-fluid">
    <div class="layui-card">
        <!-- // 默认操作按钮 -->
        <div class="layui-card-header layadmin-card-header-auto ">
            <div class="layui-form">

                <div class="layui-form-item">
                    <include file="/public/search" />
                    <div class="layui-inline" style="width: 300px;">
                        <div class="layui-input-inline" style="width: 300px;">
                            <input name="keyword" class="layui-input" type="text" placeholder="{:__('按姓名或身份证号查询')}"/>
                        </div>
                    </div>
                    <div class="layui-inline" >
                        <!-- // 默认搜索 -->
                        <button id="search" class="layui-btn icon-btn" lay-filter="formSearch" lay-submit><i class="layui-icon layui-icon-search"></i>{:__('搜索')}</button>
                        <button class="layui-btn more">展开</button>
                    </div>
                </div>
                <!-- // 自定义搜索参数 -->
                <div id="laytable-search" class="layui-form-item">
                    <div class="layui-inline">
                        <div class="layui-form-label">管理类别</div>
                        <div class="layui-input-inline">
                            <select name="jurisdiction"  lay-filter="search">
                                <option value="">全部</option>
                                <foreach name="jurisdiction" item="vo" key="key">
                                    <option value="{$key}">{$vo}</option>
                                </foreach>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-form-label">文化程度</div>
                        <div class="layui-input-inline">
                            <select name="p__education"  lay-filter="search">
                                <option value="">全部</option>
                                <foreach name="education" item="vo" key="key">
                                    <option value="{$key}">{$vo}</option>
                                </foreach>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-form-label">性别</div>
                        <div class="layui-input-inline">
                            <select name="p__sex"  lay-filter="search">
                                <option value="">全部</option>
                                <foreach name="sex" item="vo" key="key">
                                    <option value="{$key}">{$vo}</option>
                                </foreach>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-form-label">民族</div>
                        <div class="layui-input-inline">
                            <select name="p__nation"  lay-filter="search">
                                <option value="">全部</option>
                                <foreach name="nation" item="vo" key="key">
                                    <option value="{$key}">{$vo}</option>
                                </foreach>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- // 创建数据实例 -->
        <table id="lay-tableList" lay-filter="lay-tableList"></table>
    </div>
</div>

<!-- // 列表状态栏 -->
<include file="/public/toolBar" />

<!-- // 列表工具栏 -->
<script type="text/html" id="tableBar">
    <!--formBegin-->
    <a class="layui-table-text" data-title="{:__('查看')}" data-area="100%,100%" data-maxmin="true"
       data-url="{:url('/'.$controller.'/detail')}?id={{d.id}}" lay-event="edit" >{:__('查看')}</a>
    <div class="layui-divider layui-divider-vertical"></div>
    <a class="layui-table-text" data-title="{:__('编辑')}" data-area="100%,100%" data-maxmin="true"
       data-url="{:url('/PersonAffordableHousing/edit')}?id={{d.id}}" lay-event="edit" >{:__('编辑')}</a>
    <div class="layui-divider layui-divider-vertical"></div>
    <!--formEnd-->
    <a class="layui-table-text"  data-url="{:url('/PersonAffordableHousing/del')}?id={{d.id}}" lay-event="del" >{:__('删除')}</a>
</script>





<include file="/public/footer" />
<script>
    layui.use(['admin','table'], function () {

        var admin = layui.admin;
        var table = layui.table;

        /*
         * 初始化表格
        */
        var isTable = table.render({
            elem: "#lay-tableList"
            ,url: "{:url('/PersonAffordableHousing/index')}"
            ,toolbar: '#tableButton'
            ,defaultToolbar: ['filter', 'exports', 'print','search']
            ,cellMinWidth: 60
            ,page: true
            ,limit: 50,limits: [50, 100, 200, 500]
            ,height: 'full-150' //设置表格高度，减去固定的底部高度
            ,cols: [[
                {type: 'checkbox', width: 50},
                {field:'name',width:80,title:'{:__("姓名")}'},
                {field:'id_code',title:'{:__("年龄")}',width:50,templet:function(d){
                        return d.age
                    }},
                {field:'sex',title:'{:__("性别")}',width:50,templet:function(d){
                        return d.sex==1?'男':'女'
                    }},
                {field: 'jurisdiction',width:80, title: '{:__("管理类别")}',templet:function(d){
                        var resData = {:json_encode($jurisdiction)};
                        return d.jurisdiction > 0 ?resData[d.jurisdiction]:''
                    }},
                {field: 'community_id',width: 320, title: '{:__("所在区域")}',templet:function(d){
                        return d.community_id>0?d.community_name+","+d.grid_group_name+","+d.grid_name:''
                    }},
                {field: 'building_name',width:160, title: '{:__("所在房屋")}'},
                {field:'address',width:200,title:'{:__("房屋地址")}'},
                {field:'nation',width:80,title:'{:__("民族")}',templet:function(d){
                        var resData = {:json_encode($nation)};
                        return d.nation > 0 ?resData[d.nation]:''
                    }},
                {field:'face',width:80,title:'{:__("政治面貌")}',templet:function(d){
                        var resData = {:json_encode($face)};
                        return d.face > 0 ?resData[d.face]:''
                    }},
                {field:'education',width:80,title:'{:__("学历")}',templet:function(d){
                        var resData = {:json_encode($education)};
                        return d.education > 0 ?resData[d.education]:''
                    }},
                {field: 'create_by',width: 120,title:'{:__("创建者")}'},
                {field: 'create_time',width: 160,title:'{:__("创建时间")}'},
                {field: 'update_by',width: 120,title:'{:__("更新者")}'},
                {field: 'update_time',width: 160,title:'{:__("更新时间")}'},
                {align: 'center', toolbar: '#tableBar', width:200, fixed: 'right', title: '{:__("操作")}'},
            ]]
        })

    })
</script>
<include file="/public/select" />
