<include file="/public/header"/>

<php>
  $Communities = getCommunity();
  if($Communities[0]['id']==1){
  unset($Communities[0]);
  $Communities = array_values($Communities);
  }
  $community_id = empty($data['community_id'])?$Communities[0]['id']:$data['community_id'];
  $GridGroups = getGridGroup($community_id);
  $grid_group_id = empty($data['grid_group_id'])?$GridGroups[0]['id']:$data['grid_group_id'];
  $Grids = getGrid($grid_group_id);
  if($data['person_id']>0){
    $person = getPerson(0,$data['person_id']);
  }
  $jurisdiction = getDictionary(501);
  $group = getDictionary(434);
  $relation = getDictionary(53);
  $nation = getDictionary(54);
  $education = getDictionary(56);
  $marry = getDictionary(64);
  $employment = getDictionary(522);
</php>
<!---->

<link href="__STATICADMIN__css/content.css" rel="stylesheet" type="text/css"/>
<div class="layui-fluid">
  <form class="layui-form layui-card">

    <div class="layui-card-body">
      <gt name="$data.id" value="0">
        <input type="text" name="id" value="{$data.id}" hidden="">
        <else/>
        <input type="text" name="id" value="" hidden="">
      </gt>
      <input type="text" id="person_id" name="person_id" value="{$data.person_id??''}" lay-verify="required" hidden="">
      <div class="layui-input-block" style="margin-left: 0;"><blockquote class="layui-elem-quote">基本信息</blockquote></div>
      <include file="/public/person_info"/>
      <div class="layui-input-block" style="margin-left: 0;"><blockquote class="layui-elem-quote">保障房信息</blockquote></div>
      <div class="layui-form-item layui-row">
        <div class="layui-col-md4 layui-grid-0" data-index="0">
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;"><font color="red">* </font>所属社区</label>
            <div class="layui-input-block" style="margin-left:150px">
              <select  name="community_id" lay-search lay-filter="communityChange" lay-verify="required">
                <foreach name="Communities" item="vo">
                  <option value="{$vo.id}"
                  <if (isset($data['id']) && $data['id']) >
                  <in name="$vo.id" value="$data.community_id">selected</in>
                  </if>
                  >{$vo.community_name}</option>
                </foreach>
              </select>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;"><font color="red">* </font>所属网格组</label>
            <div class="layui-input-block" style="margin-left:150px">
              <select id="grid_group" name="grid_group_id" lay-search lay-filter="gridGroupChange" lay-verify="required">
                <foreach name="GridGroups" item="vo">
                  <option value="{$vo.id}"
                  <if (isset($data['id']) && $data['id']) >
                  <in name="$vo.id" value="$data.grid_group_id">selected</in>
                  </if>
                  >{$vo.grid_name}</option>
                </foreach>
              </select>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;"><font color="red">* </font>所属网格</label>
            <div class="layui-input-block" style="margin-left:150px">
              <select id="grid" name="grid_id" lay-search lay-filter="gridChange" lay-verify="required">
                <foreach name="Grids" item="vo">
                  <option value="{$vo.id}"
                  <if (isset($data['id']) && $data['id']) >
                  <in name="$vo.id" value="$data.grid_id">selected</in>
                  </if>
                  >{$vo.grid_name}</option>
                </foreach>
              </select>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;"><font color="red">*</font>管理类型</label>
            <div class="layui-input-block" style="margin-left:150px">
              <select name="jurisdiction" id="jurisdiction" lay-filter="jurisdiction"  lay-verify="required">
                <option value=''>请选择</option>
                <foreach name="jurisdiction" item="vo" key="key">
                  <option value="{$key}"
                  <if (isset($data['id']) && $data['id']) >
                  <in name="$key" value="$data.jurisdiction">selected</in>
                  </if>
                  >{$vo}</option>
                </foreach>
              </select>
            </div>
          </div>
          <if (array_key_exists('grid_user', $data)) >
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">网格员</label>
            <div class="layui-input-block" style="margin-left:150px">
              <input class="layui-input" name="grid_user" value="{$data.grid_user}">
            </div>
          </div>
          </if>
          <if (array_key_exists('grid_phone', $data)) >
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">网格员电话</label>
            <div class="layui-input-block" style="margin-left:150px">
              <input class="layui-input" name="grid_phone" value="{$data.grid_phone}" lay-verify="phone">
            </div>
          </div>
          </if>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">所属群体</label>
            <div class="layui-input-block" style="margin-left:150px">
              <select name="group_0" >
                <option value=''>请选择</option>
                <foreach name="group" item="vo" key="key">
                  <option value="{$key}"
                  <if (isset($data['id']) && $data['id']) >
                  <in name="$key" value="$data.group_0">selected</in>
                  </if>
                  >{$vo}</option>
                </foreach>
              </select>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">是否低保</label>
            <div class="layui-input-block" style="margin-left:150px">
              <input type="checkbox" name="income_low"  lay-skin="switch"
                     value="1" title="是"
              <if (isset($data['income_low']) && $data['income_low'] egt 1) >
              checked
              </if>
              >
            </div>
          </div>
        </div>
        <div class="layui-col-md4 layui-grid-1" data-index="1">
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">房屋地址</label>
            <div class="layui-input-block" style="margin-left:150px">
              <input class="layui-input" name="house_address" value="{$data.house_address}">
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">房屋面积</label>
            <div class="layui-input-block" style="margin-left:150px">
              <input class="layui-input" name="building_area" value="{$data.building_area}" lay-verify="number">
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">出租人</label>
            <div class="layui-input-block" style="margin-left:150px">
              <input class="layui-input" name="lease_from" value="{$data.lease_from}">
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">承租人</label>
            <div class="layui-input-block" style="margin-left:150px">
              <input class="layui-input" name="lease_to" value="{$data.lease_to}">
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">承租开始</label>
            <div class="layui-input-block" style="margin-left:150px">
              <input class="layui-input datepicker"
                     lay-datetime="" name="lease_start"
                     data-datetype="date"
                     data-dateformat="yyyy-MM-dd"
                     placeholder="yyyy-MM-dd"
                     data-maxvalue="9999-12-31"
                     data-minvalue="1900-01-01"
                     data-range=""
                     value="{$data.lease_start}">
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">承租结束</label>
            <div class="layui-input-block" style="margin-left:150px">
              <input class="layui-input datepicker"
                     lay-datetime="" name="lease_end"
                     data-datetype="date"
                     data-dateformat="yyyy-MM-dd"
                     placeholder="yyyy-MM-dd"
                     data-maxvalue="9999-12-31"
                     data-minvalue="1900-01-01"
                     data-range=""
                     value="{$data.lease_end}">
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">月租金</label>
            <div class="layui-input-block" style="margin-left:150px">
              <input class="layui-input" name="rent_month" value="{$data.rent_month}" lay-verify="number">
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">押金</label>
            <div class="layui-input-block" style="margin-left:150px">
              <input class="layui-input" name="deposit" value="{$data.deposit}" lay-verify="number">
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">合同签订日期</label>
            <div class="layui-input-block" style="margin-left:150px">
              <input class="layui-input datepicker"
                     lay-datetime="" name="transaction"
                     data-datetype="date"
                     data-dateformat="yyyy-MM-dd"
                     placeholder="yyyy-MM-dd"
                     data-maxvalue="9999-12-31"
                     data-minvalue="1900-01-01"
                     data-range=""
                     value="{$data.transaction}">
            </div>
          </div>
        </div>
        <div class="layui-col-md4 layui-grid-2" data-index="2">
          <div class="layui-input-block" style="margin-left: 0;"><blockquote class="layui-elem-quote">家庭成员1</blockquote></div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">姓名</label>
            <div class="layui-input-block" style="margin-left:150px">
              <input class="layui-input" name="member_name_1" value="{$data.member_name_1}">
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">成员身份证号</label>
            <div class="layui-input-block" style="margin-left:150px">
              <input class="layui-input" name="member_id_code_1" value="{$data.member_id_code_1}" lay-verify="identity">
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">与户主关系</label>
            <div class="layui-input-block" style="margin-left:150px">
              <select name="relation_1" >
                <option value=''>请选择</option>
                <foreach name="relation" item="vo" key="key">
                  <option value="{$key}"
                  <if (isset($data['id']) && $data['id']) >
                  <in name="$key" value="$data.relation_1">selected</in>
                  </if>
                  >{$vo}</option>
                </foreach>
              </select>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">民族</label>
            <div class="layui-input-block" style="margin-left:150px">
              <select name="nation_1" >
                <option value=''>请选择</option>
                <foreach name="nation" item="vo" key="key">
                  <option value="{$key}"
                  <if (isset($data['id']) && $data['id']) >
                  <in name="$key" value="$data.nation_1">selected</in>
                  </if>
                  >{$vo}</option>
                </foreach>
              </select>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">文化程度</label>
            <div class="layui-input-block" style="margin-left:150px">
              <select name="education_1" >
                <option value=''>请选择</option>
                <foreach name="education" item="vo" key="key">
                  <option value="{$key}"
                  <if (isset($data['id']) && $data['id']) >
                  <in name="$key" value="$data.education_1">selected</in>
                  </if>
                  >{$vo}</option>
                </foreach>
              </select>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">婚姻状况</label>
            <div class="layui-input-block" style="margin-left:150px">
              <select name="marry_1" >
                <option value=''>请选择</option>
                <foreach name="marry" item="vo" key="key">
                  <option value="{$key}"
                  <if (isset($data['id']) && $data['id']) >
                  <in name="$key" value="$data.marry_1">selected</in>
                  </if>
                  >{$vo}</option>
                </foreach>
              </select>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">就业状况</label>
            <div class="layui-input-block" style="margin-left:150px">
              <select name="employment_1" >
                <option value=''>请选择</option>
                <foreach name="employment" item="vo" key="key">
                  <option value="{$key}"
                  <if (isset($data['id']) && $data['id']) >
                  <in name="$key" value="$data.employment_1">selected</in>
                  </if>
                  >{$vo}</option>
                </foreach>
              </select>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">所属群体</label>
            <div class="layui-input-block" style="margin-left:150px">
              <select name="group_1" >
                <option value=''>请选择</option>
                <foreach name="group" item="vo" key="key">
                  <option value="{$key}"
                  <if (isset($data['id']) && $data['id']) >
                  <in name="$key" value="$data.group_1">selected</in>
                  </if>
                  >{$vo}</option>
                </foreach>
              </select>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">联系方式</label>
            <div class="layui-input-block" style="margin-left:150px">
              <input class="layui-input" name="phone_1" value="{$data.phone_1}" lay-verify="phone">
            </div>
          </div>
          <div class="layui-input-block" style="margin-left: 0;"><blockquote class="layui-elem-quote">家庭成员2</blockquote></div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">姓名</label>
            <div class="layui-input-block" style="margin-left:150px">
              <input class="layui-input" name="member_name_2" value="{$data.member_name_2}">
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">成员身份证号</label>
            <div class="layui-input-block" style="margin-left:150px">
              <input class="layui-input" name="member_id_code_2" value="{$data.member_id_code_2}" lay-verify="identity">
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">与户主关系</label>
            <div class="layui-input-block" style="margin-left:150px">
              <select name="relation_2" >
                <option value=''>请选择</option>
                <foreach name="relation" item="vo" key="key">
                  <option value="{$key}"
                  <if (isset($data['id']) && $data['id']) >
                  <in name="$key" value="$data.relation_2">selected</in>
                  </if>
                  >{$vo}</option>
                </foreach>
              </select>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">民族</label>
            <div class="layui-input-block" style="margin-left:150px">
              <select name="nation_2" >
                <option value=''>请选择</option>
                <foreach name="nation" item="vo" key="key">
                  <option value="{$key}"
                  <if (isset($data['id']) && $data['id']) >
                  <in name="$key" value="$data.nation_2">selected</in>
                  </if>
                  >{$vo}</option>
                </foreach>
              </select>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">文化程度</label>
            <div class="layui-input-block" style="margin-left:150px">
              <select name="education_2" >
                <option value=''>请选择</option>
                <foreach name="education" item="vo" key="key">
                  <option value="{$key}"
                  <if (isset($data['id']) && $data['id']) >
                  <in name="$key" value="$data.education_2">selected</in>
                  </if>
                  >{$vo}</option>
                </foreach>
              </select>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">婚姻状况</label>
            <div class="layui-input-block" style="margin-left:150px">
              <select name="marry_2" >
                <option value=''>请选择</option>
                <foreach name="marry" item="vo" key="key">
                  <option value="{$key}"
                  <if (isset($data['id']) && $data['id']) >
                  <in name="$key" value="$data.marry_2">selected</in>
                  </if>
                  >{$vo}</option>
                </foreach>
              </select>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">就业状况</label>
            <div class="layui-input-block" style="margin-left:150px">
              <select name="employment_2" >
                <option value=''>请选择</option>
                <foreach name="employment" item="vo" key="key">
                  <option value="{$key}"
                  <if (isset($data['id']) && $data['id']) >
                  <in name="$key" value="$data.employment_2">selected</in>
                  </if>
                  >{$vo}</option>
                </foreach>
              </select>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">所属群体</label>
            <div class="layui-input-block" style="margin-left:150px">
              <select name="group_2" >
                <option value=''>请选择</option>
                <foreach name="group" item="vo" key="key">
                  <option value="{$key}"
                  <if (isset($data['id']) && $data['id']) >
                  <in name="$key" value="$data.group_2">selected</in>
                  </if>
                  >{$vo}</option>
                </foreach>
              </select>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">联系方式</label>
            <div class="layui-input-block" style="margin-left:150px">
              <input class="layui-input" name="phone_2" value="{$data.phone_2}" lay-verify="phone">
            </div>
          </div>
          <div class="layui-input-block" style="margin-left: 0;"><blockquote class="layui-elem-quote">家庭成员3</blockquote></div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">姓名</label>
            <div class="layui-input-block" style="margin-left:150px">
              <input class="layui-input" name="member_name_3" value="{$data.member_name_3}">
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">成员身份证号</label>
            <div class="layui-input-block" style="margin-left:150px">
              <input class="layui-input" name="member_id_code_3" value="{$data.member_id_code_3}" lay-verify="identity">
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">与户主关系</label>
            <div class="layui-input-block" style="margin-left:150px">
              <select name="relation_3" >
                <option value=''>请选择</option>
                <foreach name="relation" item="vo" key="key">
                  <option value="{$key}"
                  <if (isset($data['id']) && $data['id']) >
                  <in name="$key" value="$data.relation_3">selected</in>
                  </if>
                  >{$vo}</option>
                </foreach>
              </select>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">民族</label>
            <div class="layui-input-block" style="margin-left:150px">
              <select name="nation_3" >
                <option value=''>请选择</option>
                <foreach name="nation" item="vo" key="key">
                  <option value="{$key}"
                  <if (isset($data['id']) && $data['id']) >
                  <in name="$key" value="$data.nation_3">selected</in>
                  </if>
                  >{$vo}</option>
                </foreach>
              </select>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">文化程度</label>
            <div class="layui-input-block" style="margin-left:150px">
              <select name="education_3" >
                <option value=''>请选择</option>
                <foreach name="education" item="vo" key="key">
                  <option value="{$key}"
                  <if (isset($data['id']) && $data['id']) >
                  <in name="$key" value="$data.education_3">selected</in>
                  </if>
                  >{$vo}</option>
                </foreach>
              </select>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">婚姻状况</label>
            <div class="layui-input-block" style="margin-left:150px">
              <select name="marry_3" >
                <option value=''>请选择</option>
                <foreach name="marry" item="vo" key="key">
                  <option value="{$key}"
                  <if (isset($data['id']) && $data['id']) >
                  <in name="$key" value="$data.marry_3">selected</in>
                  </if>
                  >{$vo}</option>
                </foreach>
              </select>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">就业状况</label>
            <div class="layui-input-block" style="margin-left:150px">
              <select name="employment_3" >
                <option value=''>请选择</option>
                <foreach name="employment" item="vo" key="key">
                  <option value="{$key}"
                  <if (isset($data['id']) && $data['id']) >
                  <in name="$key" value="$data.employment_3">selected</in>
                  </if>
                  >{$vo}</option>
                </foreach>
              </select>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">所属群体</label>
            <div class="layui-input-block" style="margin-left:150px">
              <select name="group_3" >
                <option value=''>请选择</option>
                <foreach name="group" item="vo" key="key">
                  <option value="{$key}"
                  <if (isset($data['id']) && $data['id']) >
                  <in name="$key" value="$data.group_3">selected</in>
                  </if>
                  >{$vo}</option>
                </foreach>
              </select>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">联系方式</label>
            <div class="layui-input-block" style="margin-left:150px">
              <input class="layui-input" name="phone_3" value="{$data.phone_3}" lay-verify="phone">
            </div>
          </div>
          <div class="layui-input-block" style="margin-left: 0;"><blockquote class="layui-elem-quote">家庭成员4</blockquote></div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">姓名</label>
            <div class="layui-input-block" style="margin-left:150px">
              <input class="layui-input" name="member_name_4" value="{$data.member_name_4}">
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">成员身份证号</label>
            <div class="layui-input-block" style="margin-left:150px">
              <input class="layui-input" name="member_id_code_4" value="{$data.member_id_code_4}" lay-verify="identity">
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">与户主关系</label>
            <div class="layui-input-block" style="margin-left:150px">
              <select name="relation_4" >
                <option value=''>请选择</option>
                <foreach name="relation" item="vo" key="key">
                  <option value="{$key}"
                  <if (isset($data['id']) && $data['id']) >
                  <in name="$key" value="$data.relation_4">selected</in>
                  </if>
                  >{$vo}</option>
                </foreach>
              </select>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">民族</label>
            <div class="layui-input-block" style="margin-left:150px">
              <select name="nation_4" >
                <option value=''>请选择</option>
                <foreach name="nation" item="vo" key="key">
                  <option value="{$key}"
                  <if (isset($data['id']) && $data['id']) >
                  <in name="$key" value="$data.nation_4">selected</in>
                  </if>
                  >{$vo}</option>
                </foreach>
              </select>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">文化程度</label>
            <div class="layui-input-block" style="margin-left:150px">
              <select name="education_4" >
                <option value=''>请选择</option>
                <foreach name="education" item="vo" key="key">
                  <option value="{$key}"
                  <if (isset($data['id']) && $data['id']) >
                  <in name="$key" value="$data.education_4">selected</in>
                  </if>
                  >{$vo}</option>
                </foreach>
              </select>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">婚姻状况</label>
            <div class="layui-input-block" style="margin-left:150px">
              <select name="marry_4" >
                <option value=''>请选择</option>
                <foreach name="marry" item="vo" key="key">
                  <option value="{$key}"
                  <if (isset($data['id']) && $data['id']) >
                  <in name="$key" value="$data.marry_4">selected</in>
                  </if>
                  >{$vo}</option>
                </foreach>
              </select>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">就业状况</label>
            <div class="layui-input-block" style="margin-left:150px">
              <select name="employment_4" >
                <option value=''>请选择</option>
                <foreach name="employment" item="vo" key="key">
                  <option value="{$key}"
                  <if (isset($data['id']) && $data['id']) >
                  <in name="$key" value="$data.employment_4">selected</in>
                  </if>
                  >{$vo}</option>
                </foreach>
              </select>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">所属群体</label>
            <div class="layui-input-block" style="margin-left:150px">
              <select name="group_4" >
                <option value=''>请选择</option>
                <foreach name="group" item="vo" key="key">
                  <option value="{$key}"
                  <if (isset($data['id']) && $data['id']) >
                  <in name="$key" value="$data.group_4">selected</in>
                  </if>
                  >{$vo}</option>
                </foreach>
              </select>
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label" style="width:120px;">联系方式</label>
            <div class="layui-input-block" style="margin-left:150px">
              <input class="layui-input" name="phone_4" value="{$data.phone_4}" lay-verify="phone">
            </div>
          </div>
        </div>
      </div>
      <div class="layui-footer layui-form-footer">
        <button class="layui-btn layui-btn-primary" type="button" sa-event="closeDialog">{:__('取消')}</button>
        <button class="layui-btn" type="button" lay-filter="submitIframe" lay-submit>{:__('提交')}</button>
      </div>
  </form>
</div>
<include file="/public/static"/>
<include file="/public/footer"/>
<include file="/public/select"/>
<include file="/public/person_common"/>