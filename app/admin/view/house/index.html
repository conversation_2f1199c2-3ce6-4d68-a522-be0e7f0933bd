<include file="/public/header"/>
<php>
    $property_right = getDictionary(349);
    $usage_category = getDictionary(355);
    $house_type = getDictionary(362);
    $residence_nature = getDictionary(367);
    $build_types = getDictionary(13);
</php>
<div class="layui-fluid">
    <div class="layui-card">
        <!-- // 默认操作按钮 -->
        <div class="layui-card-header layadmin-card-header-auto ">
            <div class="layui-form">
                <div class="layui-form-item">
                    <include file="/public/search" />
                    <div class="layui-inline" style="width: 300px;">
                        <div class="layui-input-inline" style="width: 300px;">
                            <input name="keyword" class="layui-input" type="text" placeholder="{:__('按产权人/承租人姓名/身份证号或门牌号查询')}"/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <!-- // 默认搜索 -->
                        <button id="search" class="layui-btn icon-btn" lay-filter="formSearch" lay-submit><i
                                class="layui-icon layui-icon-search"></i>{:__('搜索')}
                        </button>
                        <!--formBegin-->
                        <button class="layui-btn more">展开</button>
                        <!--formEnd-->
                    </div>
                </div>
                <!-- // 自定义搜索参数 -->
                <div id="laytable-search" class="layui-form-item">

                    <include file="/public/search_road" />
                    <div class="layui-inline">
                        <div class="layui-form-label  short-label">{:__('实有建筑')}</div>
                        <div class="layui-input-inline">
                            <select id="building" name="building_id" lay-search  lay-filter="buildingChange">
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-form-label  short-label">{:__('单元')}</div>
                        <div class="layui-input-inline">
                            <select id="unit" name="unit" lay-filter="search">
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-form-label">{:__('建筑类别')}</div>
                        <div class="layui-input-inline ">
                            <select name="b__type" lay-search="1" lay-filter="search">
                                <option value="">全部</option>
                                <foreach name="build_types" item="vo" key="key">
                                    <option value="{$key}">{$vo}</option>
                                </foreach>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-form-label">产权性质</div>
                        <div class="layui-input-inline">
                            <select name="property_right"  lay-filter="search">
                                <option value="">全部</option>
                                <foreach name="property_right" item="vo" key="key">
                                    <option value="{$key}">{$vo}</option>
                                </foreach>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-form-label">使用类别</div>
                        <div class="layui-input-inline">
                            <select name="usage_category" lay-filter="search">
                                <option value="">全部</option>
                                <foreach name="usage_category" item="vo" key="key">
                                    <option value="{$key}">{$vo}</option>
                                </foreach>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-form-label">房屋类型</div>
                        <div class="layui-input-inline">
                            <select name="house_type"  lay-filter="search">
                                <option value="">全部</option>
                                <foreach name="house_type" item="vo" key="key">
                                    <option value="{$key}">{$vo}</option>
                                </foreach>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-form-label">住所性质</div>
                        <div class="layui-input-inline">
                            <select name="residence_nature" lay-filter="search">
                                <option value="">全部</option>
                                <foreach name="residence_nature" item="vo" key="key">
                                    <option value="{$key}">{$vo}</option>
                                </foreach>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-form-label">出租</div>
                        <div class="layui-input-inline">
                            <select name="rent" lay-filter="search">
                                <option value="">全部</option>
                                <option value="2">是</option>
                                <option value="1">否</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- // 创建数据实例 -->
        <table id="lay-tableList" lay-filter="lay-tableList"></table>
    </div>
</div>

<include file="/public/toolBar"/>

<!-- // 列表工具栏 -->
<script type="text/html" id="tableBar">
    <!--formBegin-->
    <a class="layui-table-text" data-title="{:__('查看')}" data-area="100%,100%" data-maxmin="true"
       data-url="{:url('/'.$controller.'/detail')}?id={{d.id}}" lay-event="edit" >{:__('查看')}</a>
    <div class="layui-divider layui-divider-vertical"></div>
    <a class="layui-table-text" data-title="{:__('编辑')}" data-area="1100px,750px" data-maxmin="true"
       data-url="{:url('/House/edit')}?id={{d.id}}" lay-event="edit">{:__('编辑')}</a>
    <div class="layui-divider layui-divider-vertical"></div>
    <!--formEnd-->
    <a class="layui-table-text" data-url="{:url('/House/del')}?id={{d.id}}" lay-event="del">{:__('删除')}</a>
</script>




<include file="/public/footer"/>
<script>
    layui.use(['admin', 'table'], function () {

        var admin = layui.admin;
        var table = layui.table;

        /*
         * 初始化表格
        */
        var isTable = table.render({
            elem: "#lay-tableList"
            , url: "{:url('/House/index',$params)}"
            , toolbar: '#tableButton'
            , defaultToolbar: ['filter', 'exports', 'print', 'search']
            , cellMinWidth: 50
            ,page: true
            ,limit: 50,limits: [50, 100, 200, 500]
            ,height: 'full-150' //设置表格高度，减去固定的底部高度
            , cols: [[
                {type: 'checkbox', width: 50},
                {field: 'community_id',width: 320, title: '{:__("所在区域")}',templet:function(d){
                    return d.community_id>0?d.community_name+","+d.grid_group_name+","+d.grid_name:''
                }},
                {field: 'building_name',width:160, title: '{:__("所在建筑")}'},
                {field: 'unit',width:100, title: '{:__("单元楼层")}',templet:function(d){
                        return d.unit+"单元,"+d.floor+"楼"
                }},
                {field: 'code',width:200, title: '{:__("门牌号")}', templet: function(d){
                        return d.code;
                    }},
                {field: 'area',width:80, title: '{:__("房屋面积")}',templet:function(d){
                        return d.area?d.area+"㎡":0+"㎡"
                }},
                {field: 'owner_name',width:80, title: '{:__("产权人姓名")}'},
                {field: 'owner_code',width:180, title: '{:__("身份证号")}'},
                {field: 'owner_phone',width:120, title: '{:__("手机号码")}'},
                {field: 'property_right',width:80, title: '{:__("产权性质")}',templet:function(d){
                        var resData = {:json_encode(getDictionary(349))};
                        return d.property_right > 0 ?resData[d.property_right]:''
                }},
                {field: 'usage_category',width:80, title: '{:__("使用类别")}',templet:function(d){
                        var resData = {:json_encode(getDictionary(355))};
                        return d.usage_category > 0 ?resData[d.usage_category]:''
                }},
                {field: 'house_type',width:80, title: '{:__("房屋类型")}',templet:function(d){
                        var resData = {:json_encode(getDictionary(362))};
                        return d.house_type > 0 ?resData[d.house_type]:''
                }},
                {field: 'residence_nature',width:80, title: '{:__("住所性质")}',templet:function(d){
                        var resData = {:json_encode(getDictionary(367))};
                        return d.residence_nature > 0 ?resData[d.residence_nature]:''
                }},
                {field: 'rent', title: '{:__("是否出租")}',templet:function(d){
                        return d.rent==1?'是':'否'
                }},
                {field: 'create_time',width:160, title: '{:__("创建时间")}'},
                {field: 'create_by',width: 120, title: '{:__("创建人")}'},
                {field: 'update_time',width: 160, title: '{:__("修改时间")}'},
                {field: 'update_by',width: 120, title: '{:__("修改人")}'},
                {align: 'center', toolbar: '#tableBar', width: 200, fixed: 'right', title: '{:__("操作")}'},
            ]]
        })

    })
</script>
<include file="/public/select" />
