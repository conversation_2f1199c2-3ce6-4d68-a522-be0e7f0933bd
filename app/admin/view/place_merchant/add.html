<include file="/public/header"/>
<php>
    $Communities = getCommunity();
    if($Communities[0]['id']==1){
    unset($Communities[0]);
    $Communities = array_values($Communities);
    }
    $community_id = empty($data['community_id'])?$Communities[0]['id']:$data['community_id'];
    $GridGroups = getGridGroup($community_id);
    $grid_group_id = empty($data['grid_group_id'])?$GridGroups[0]['id']:$data['grid_group_id'];
    $Grids = getGrid($grid_group_id);
    $grid_id = empty($data['grid_id'])?$Grids[0]['id']:$data['grid_id'];
    $Streets = getStreet(0,$grid_id);
    $Buildings = getBuilding($grid_id);
    $street_min = 0;
    $street_max = 0;
    $road_min = 0;
    $road_max = 0;
    $place_merchant_type = getCateTree('place_merchant_type');
</php>
<!---->
<link href="__STATICADMIN__css/content.css" rel="stylesheet" type="text/css"/>
<div class="layui-fluid">
    <form class="layui-form layui-card">

        <div class="layui-card-body">
            <gt name="$data.id" value="0">
                <input type="text" name="id" value="{$data.id}" hidden="">
                <else/>
                <input type="text" name="id" value="" hidden="">
            </gt>
            <div class="layui-form-item layui-row">
                <div class="layui-col-md6 layui-grid-0" data-index="0">
                    <div class="layui-form-item">
                        <div class="layui-input-block" style="margin-left: 0;">
                            <blockquote class="layui-elem-quote">图片</blockquote>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label layui-hide">图片</label>
                        <div class="layui-input-block" style="margin-left:0">
                            <div class="layui-imagesbox">

                                <php>
                                    if (strpos($data['image'], 'http') === 0) {
                                    $data['image'] = [['src'=>$data['image'],'title'=>'图片']];
                                    }else{
                                    $data['image'] = json_decode($data['image'],true);
                                    }
                                </php>
                                <!-- // 循环输出代码 -->
                                <notempty name="$data['image']">
                                    <foreach name="$data['image']" item="vo">
                                        <div class="layui-input-inline layui-uplpad-image">
                                            <img src="{$vo.src}" lay-image-click>
                                            <input type="text" name="image[{$key}][src]" class="layui-hide"
                                                   value="{$vo.src}">
                                            <input type="text" name="image[{$key}][title]" class="layui-input  layui-image"
                                                   value="{$vo.title}" placeholder="图片简介">
                                            <span class="layui-badge layui-badge-red" data-name="image"
                                                  onclick="layui.admin.resetInput(this,'images');">删除</span>
                                        </div>
                                    </foreach>
                                </notempty>
                                <div class="layui-input-inline layui-uplpad-image">
                                    <div class="layui-upload-drag" lay-upload="image" data-type="multiple" >
                                        <i class="layui-icon layui-icon-upload"></i>
                                        <p>点击上传，或将文件拖拽到此处</p>
                                        <div class="layui-hide"></div>
                                    </div>
                                    
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="width:120px;"><font color="red">* </font>所属社区</label>
                        <div class="layui-input-block" style="margin-left:150px">
                            <select  name="community_id" lay-search lay-filter="communityChange" lay-verify="required">
                                <foreach name="Communities" item="vo">
                                    <option value="{$vo.id}"
                                    <if (isset($data['id']) && $data['id']) >
                                    <in name="$vo.id" value="$data.community_id">selected</in>
                                    </if>
                                    >{$vo.community_name}</option>
                                </foreach>
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="width:120px;"><font color="red">* </font>所属网格组</label>
                        <div class="layui-input-block" style="margin-left:150px">
                            <select id="grid_group" name="grid_group_id" lay-search lay-filter="gridGroupChange" lay-verify="required">
                                <foreach name="GridGroups" item="vo">
                                    <option value="{$vo.id}"
                                    <if (isset($data['id']) && $data['id']) >
                                    <in name="$vo.id" value="$data.grid_group_id">selected</in>
                                    </if>
                                    >{$vo.grid_name}</option>
                                </foreach>
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="width:120px;"><font color="red">* </font>所属网格</label>
                        <div class="layui-input-block" style="margin-left:150px">
                            <select id="grid" name="grid_id" lay-search lay-filter="gridChange" lay-verify="required">
                                <foreach name="Grids" item="vo">
                                    <option value="{$vo.id}"
                                    <if (isset($data['id']) && $data['id']) >
                                    <in name="$vo.id" value="$data.grid_id">selected</in>
                                    </if>
                                    >{$vo.grid_name}</option>
                                </foreach>
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="width:120px;"><font color="red">* </font>所属建筑</label>
                        <div class="layui-input-block" style="margin-left: 150px;">
                            <select id="building" name="building_id" lay-search lay-verify="required">
                                <option value=''>请选择</option>
                                <foreach name="Buildings" item="vo">
                                    <option value="{$vo.id}"
                                    <if (isset($data['id']) && $data['id']) >
                                    <in name="$vo.id" value="$data.building_id">selected</in>
                                    </if>
                                    >{$vo.building_name}</option>
                                </foreach>
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="width:120px;">单元</label>
                        <div class="layui-input-block" style="margin-left: 150px;">
                            <input class="layui-input" name="unit" placeholder="请输入"
                                   lay-verify="number" value="{$data.unit}">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="width:120px;">楼层</label>
                        <div class="layui-input-block" style="margin-left: 150px;">
                            <input class="layui-input" name="floor" placeholder="请输入"
                                   lay-verify="number" value="{$data.floor}">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="width:120px;">所在路段</label>
                        <div class="layui-input-block" style="margin-left:150px">
                            <select id="road" name="road_id" lay-search lay-verify="" lay-filter="roadChange">
                                <option value="">请选择</option>
                                <foreach name="Streets" item="vo">
                                    <if ($vo['type']===1) >
                                    <option value="{$vo.id}"
                                    <if (isset($data['id']) && $data['id']) >
                                    <in name="$vo.id" value="$data.road_id">
                                        <php>
                                            if(($vo['typeNo']==1&&$data['road_start']%2==1)||($vo['typeNo']==2&&$data['road_start']%2==0)){
                                            $road_min = $vo['start'];
                                            $road_max = $vo['end'];
                                            echo 'selected';
                                        }
                                        </php>
                                    </in>
                                    </if>
 min="{$vo.start}" max = "{$vo.end}">
{$vo.name}({$vo.typeNo==1?'单号':'双号'})</option>
                                    </if>
                                </foreach>
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label" style="width:120px;">路段起止号</label>
                            <div class="layui-input-inline" style="width: 100px;">
                                <input type="number" class="layui-input StreetNo" id="road_start" name="road_start"
                                       min="{$road_min}" max="{$road_max}"
                                       placeholder="请输入" lay-verify="number"value="{$data.road_start}" >
                            </div>
                            <div class="layui-form-mid">
                                -
                            </div>
                            <div class="layui-input-inline" style="width: 100px;">
                                <input type="number" class="layui-input StreetNo" id="road_end" name="road_end"
                                       min="{$road_min}" max="{$road_max}"
                                       placeholder="请输入" lay-verify="number"value="{$data.road_end}">
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="width:120px;">副号(路)</label>
                        <div class="layui-input-block" style="margin-left:150px"><input class="layui-input"
                                                                                        name="road_sub"
                                                                                        placeholder="请输入" lay-verify=""
                                                                                        value="{$data.road_sub}"></div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="width:120px;">所在街段</label>
                        <div class="layui-input-block" style="margin-left:150px">
                            <select id="street" name="street_id"  lay-search lay-verify="" lay-filter="streetChange">
                                <option value="">请选择</option>
                                <foreach name="Streets" item="vo">
                                    <if ($vo['type']==2) >
                                    <option value="{$vo.id}"
                                    <if (isset($data['id']) && $data['id']) >
                                    <in name="$vo.id" value="$data.street_id">
                                        <php>
                                            if(($vo['typeNo']==1&&$data['street_start']%2==1)||($vo['typeNo']==2&&$data['street_start']%2==0)){
                                            $street_min = $vo['start'];
                                            $street_max = $vo['end'];
                                            echo 'selected';
                                            }
                                        </php>
                                    </in>
                                    </if>
 min="{$vo.start}" max = "{$vo.end}">
{$vo.name}({$vo.typeNo==1?'单号':'双号'})</option>
                                    </if>
                                </foreach>
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label" style="width:120px;">街段起止号</label>
                            <div class="layui-input-inline" style="width: 100px;">
                                <input type="number" class="layui-input StreetNo" id="street_start" name="street_start"
                                       min="{$street_min}" max="{$street_max}"
                                       placeholder="请输入" lay-verify="number"value="{$data.street_start}" >
                            </div>
                            <div class="layui-form-mid">
                                -
                            </div>
                            <div class="layui-input-inline" style="width: 100px;">
                                <input type="number" class="layui-input StreetNo" id="street_end" name="street_end"
                                       min="{$street_min}" max="{$street_max}"
                                       placeholder="请输入" lay-verify="number"value="{$data.street_end}">
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="width:120px;">副号(街)</label>
                        <div class="layui-input-block" style="margin-left:150px"><input class="layui-input"
                                                                                        name="street_sub"
                                                                                        placeholder="请输入" lay-verify=""
                                                                                        value="{$data.street_sub}">
                        </div>
                    </div>
                </div>
                <div class="layui-col-md6 layui-grid-1" data-index="1">
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="width:106px;"><font color="red">* </font>商户名称</label>
                        <div class="layui-input-block" style="margin-left:136px"><input class="layui-input"
                                                                                        name="merchant_name"
                                                                                        placeholder="请输入" required="1"
                                                                                        lay-verify="required"
                                                                                        value="{$data.merchant_name}">
                        </div>
                    </div>

                    <div class="layui-form-item">
                        <label class="layui-form-label" style="width:106px;"><font color="red">* </font>商户地址</label>
                        <div class="layui-input-block" style="margin-left:136px"><input class="layui-input"
                                                                                        name="address" placeholder="请输入"
                                                                                        required="1" lay-verify="required"
                                                                                        value="{$data.address}"></div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="width:106px;"><font color="red">* </font>行业大类</label>
                        <div class="layui-input-block" style="margin-left:136px">
                            <select name="type_1_id" lay-verify="required" lay-filter="typeChange">
                                <option value=''>请选择</option>
                                <foreach name="place_merchant_type" item="vo" key="key">
                                    <option value="{$vo.id}"
                                    <if (isset($data['id']) && $data['id']) >
                                    <in name="$vo.id" value="$data.type_1_id">selected</in>
                                    </if>
                                    >{$vo.name}</option>
                                </foreach>
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="width:106px;"><font color="red">* </font>行业类别</label>
                        <div class="layui-input-block" style="margin-left:136px">
                            <select id="type_2_id" name="type_2_id" lay-verify="required">
                                <option value=''>请选择</option>
                                <if (isset($data['type_1_id']) && $data['type_1_id']) >
                                <foreach name="place_merchant_type[$data['type_1_id']]['children']" item="vo" key="key">
                                    <option value="{$vo.id}"
                                    <in name="$vo.id" value="$data.type_2_id">selected</in>
                                    >{$vo.name}</option>
                                </foreach>
                                </if>
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="width:106px;">营业执照号码</label>
                        <div class="layui-input-block" style="margin-left:136px"><input class="layui-input noChinese" name="code"
                                                                                        placeholder="请输入" lay-verify=""
                                                                                        value="{$data.code}"></div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="width:106px;">成立日期</label>
                        <div class="layui-input-block" style="margin-left:136px"><input class="layui-input datepicker"
                                                                                        lay-datetime="" name="create_date"
                                                                                        data-datetype="date"
                                                                                        data-dateformat="yyyy-MM-dd"
                                                                                        placeholder="yyyy-MM-dd"
                                                                                        data-maxvalue="{:date('Y-m-d')}"
                                                                                        data-minvalue="1900-01-01"
                                                                                        data-range=""
                                                                                        value="{$data.create_date}"></div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="width:106px;"><font color="red">* </font>法人名称</label>
                        <div class="layui-input-block" style="margin-left:136px"><input class="layui-input noNum"
                                                                                        name="corporation"
                                                                                        placeholder="请输入" required="1"
                                                                                        lay-verify="required"
                                                                                        value="{$data.corporation}">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="width:106px;">法人身份证号</label>
                        <div class="layui-input-block" style="margin-left:136px"><input class="layui-input"
                                                                                        name="id_code"
                                                                                        placeholder="请输入" required="1"
                                                                                        lay-verify="identity"
                                                                                        value="{$data.id_code}">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="width:106px;">联系电话</label>
                        <div class="layui-input-block" style="margin-left:136px"><input class="layui-input" name="phone"
                                                                                        placeholder="请输入" required="1"
                                                                                        lay-verify=""
                                                                                        value="{$data.phone}"></div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="width:106px;">是否住人</label>
                        <div class="layui-input-block" style="margin-left:136px"><input type="hidden" type="checkbox"
                                                                                        name="is_live" value="0"/>
                            <input type="checkbox" name="is_live" value="1" lay-filter="liveChange"
                            <eq name="$data.is_live" value="1"> checked</eq>
                            lay-skin="switch" />
                        </div>
                    </div>
                    <div id="live-div" style="display:<eq name='data.is_live' value='1'>display<else/>none</eq>;">
                        <div class="layui-form-item">
                                <label class="layui-form-label" style="width:106px;">住户</label>
                                <div class="layui-input-block" style="margin-left:136px"><input class="layui-input" name="owner"
                                                                                                    placeholder="请输入" lay-verify=""
                                                                                                    value="{$data.is_live==1?$data.owner:''}"></div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label" style="width:106px;">住户身份证号</label>
                                <div class="layui-input-block" style="margin-left:136px"><input class="layui-input"
                                                                                                    name="owner_code"
                                                                                                    placeholder="请输入" lay-verify=""
                                                                                                    value="{$data.is_live==1?$data.owner_code:''}">
                                </div>
                            </div>
                    </div>
                    
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="width:120px;">工作人员数量</label>
                        <div class="layui-input-block" style="margin-left: 150px;">
                            <input class="layui-input" name="staff_number" placeholder="请输入"
                                   lay-verify="number" value="{$data.staff_number}">
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="width:106px;">备注</label>
                        <div class="layui-input-block" style="margin-left:136px"><input class="layui-input"
                                                                                        name="details" placeholder="请输入"
                                                                                        lay-verify=""
                                                                                        value="{$data.details}"></div>
                    </div>
                </div>
            </div>
            <div class="layui-footer layui-form-footer">
                <button class="layui-btn layui-btn-primary" type="button" sa-event="closeDialog">{:__('取消')}</button>
                <button class="layui-btn" type="button" lay-filter="submitIframe" lay-submit>{:__('提交')}</button>
            </div>
    </form>
</div>
<include file="/public/static"/>
<include file="/public/footer"/>
<script>
    layui.use(['layer','form'], function () {
        var form = layui.form;
        var $ = layui.jquery;
        var type_json = {:json_encode($place_merchant_type)};
        form.on('switch(liveChange)', function(data){
            if(data.elem.checked){
                $("#live-div").show();
            }else{
                $("#live-div input").each(function(){
                    $(this).val("");
                });
                $("#live-div").hide();
            }
        });
        form.on('select(typeChange)', function(data){
            $('#type_2_id').empty();
            $('#type_2_id').append(new Option("请选择", ""));// 下拉菜单里添加元素
            $.each(type_json[data.value]['children'], function (index, value) {
                $('#type_2_id').append(new Option(value.name, value.id));// 下拉菜单里添加元素
            });
            form.render('select');
        });
    });
</script>
<include file="/public/select"/>