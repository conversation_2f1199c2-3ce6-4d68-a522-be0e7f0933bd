<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>{:saenv('site_name')}_{:__('后台登录')}</title>
    <link href="/favicon.ico" rel="icon">
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <link href="__STATICADMIN__layui/css/layui.css?v={:release()}" rel="stylesheet" type="text/css" />
    <link href="__STATICADMIN__css/login.css?v={:release()}" rel="stylesheet" type="text/css" />
</head>
<body>
<div class="swiftadmin-login">
    <div class="swiftadmin-login-main">
        <!-- // 头部信息 -->
        <div class="swiftadmin-login-box swiftadmin-login-header">
            <h2><img  src="__ADMINIMAGES__logo.png" alt="logo" class="logo">建三江智慧街道管理系统</h2>
            <!--<p>SwiftAdmin.NET 最懂你的极速开发框架管理后台</p>-->
        </div>
        <!-- // 登录页面 -->
        <div id="login" class="swiftadmin-login-box swiftadmin-login-body layui-form" style="display: block;">
            <form action="{:url('/login/index')}" method="post" class="layui-form layui-form-pane login" >
                <input type="hidden" name="__token__" value="{:token()}" />
                <div class="layui-form-item">
                    <div class="item">
                        <label class="swiftadmin-login-icon layui-icon layui-icon-username" ></label>
                        <input type="text" name="name" lay-verify="required" placeholder="{:__('用户名')}" class="layui-input layui-form-danger">
                    </div>
                </div>

                <div class="layui-form-item">
                    <div class="item">
                        <label class="swiftadmin-login-icon layui-icon layui-icon-password" ></label>
                        <input type="password" name="pwd" lay-verify="required" placeholder="{:__('密码')}" class="layui-input">
                    </div>
                </div>
                <div class="layui-form-item" <empty name="$captcha">style="display:none;" </empty> >
                <div class="layui-row">
                    <div class="layui-col-xs7 item">
                        <label class="swiftadmin-login-icon layui-icon layui-icon-vercode" ></label>
                        <input type="text" name="captcha"  placeholder="{:__('图形验证码')}" class="layui-input">
                    </div>
                    <div class="layui-col-xs3 fr">
                        <div class="captcha fr" ><a href="javascript:;" ><img src="{:captcha_src()}" height="36" id="captchaImg" alt="{:__('验证码')}"  /></a></div>
                    </div>
                </div>
        </div>

        <div class="layui-form-item" style="margin-bottom: 20px;">
            <div class="fl">
                <input type="checkbox" name="remember" lay-skin="primary" title="{:__('记住密码')}" checked>
            </div>
            <a id="forget-alert" href="javascript:;" class="swiftadmin-user-jump-change swiftadmin-link" style="margin-top: 7px;">{:__('忘记密码？')}</a>
        </div>

        <div class="layui-form-item">
            <input type="submit" value="{:__('登录')}" lay-submit="" lay-filter="login" class="layui-btn layui-btn-fluid layui-btn-normal">
        </div>
        </form>
    </div>

    <!-- // 注册页面 -->
    <div id="register" class="swiftadmin-login-box swiftadmin-login-body layui-form" style="display: none;">
        <form action="{:url('/login/register')}" method="post" class="layui-form layui-form-pane register" >
            <div class="layui-form-item item">
                <label class="swiftadmin-login-icon layui-icon layui-icon-cellphone" ></label>
                <input type="text" name="email" lay-verify="required|email" placeholder="{:__('请输入邮箱')}" class="layui-input">
            </div>
            <div class="layui-form-item">

                <div class="layui-row">
                    <div class="layui-col-xs7 item">
                        <label class="swiftadmin-login-icon layui-icon layui-icon-vercode" ></label>
                        <input type="text" name="captchar" lay-verify="required" placeholder="{:__('验证码')}" class="layui-input">
                    </div>
                    <div class="layui-col-xs3 fr">
                        <div class="captcha fr" ><a href="javascript:;" ><img src="{:captcha_src()}" height="36" id="captchaImg2" alt="{:__('验证码')}"  /></a></div>
                    </div>
                </div>
            </div>
            <div class="layui-form-item item">
                <label class="swiftadmin-login-icon layui-icon layui-icon-password" ></label>
                <input type="password" name="pass" lay-verify="required" placeholder="{:__('密码')}" class="layui-input">
            </div>
            <div class="layui-form-item item">
                <label class="swiftadmin-login-icon layui-icon layui-icon-password" ></label>
                <input type="password" name="repass" lay-verify="required" placeholder="{:__('确认密码')}" class="layui-input">
            </div>
            <div class="layui-form-item item">
                <label class="swiftadmin-login-icon layui-icon layui-icon-username" ></label>
                <input type="text" name="nickname" lay-verify="required|nickname" placeholder="{:__('昵称')}" class="layui-input">
            </div>
            <div class="layui-form-item">
                <div class="fl">
                    <input type="checkbox" name="agreement" lay-skin="primary" title="{:__('同意用户协议')}" checked="">
                </div>
            </div>
            <div class="layui-form-item">
                <input type="submit" value="{:__('注 册')}" lay-submit="" lay-filter="register" class="layui-btn layui-btn-fluid layui-btn-normal">
            </div>
            <div class="layui-trans layui-form-item swiftadmin-login-other">
                <div class="other-login fl">
                    <label>{:__('社交账号注册')}</label>
                    <a href="javascript:;"><i class="layui-icon layui-icon-login-qq"></i></a>
                    <a href="javascript:;"><i class="layui-icon layui-icon-login-wechat"></i></a>
                    <a href="javascript:;"><i class="layui-icon layui-icon-login-weibo"></i></a>
                </div>
                <a href="javascript:;" sa-event-type="login" class="swiftadmin-user-jump-change swiftadmin-link layui-hide-xs">{:__('用已有帐号登入')}</a>
                <a href="javascript:;" sa-event-type="login" class="swiftadmin-user-jump-change swiftadmin-link layui-hide-sm layui-show-xs-inline-block">{:__('登入')}</a>
            </div>
        </form>
    </div>
    <!-- // 找回密码页面 -->
    <div id="forget" class="swiftadmin-login-box swiftadmin-login-body layui-form"  style="display: none;" >
        <form action="{:url('/login/forget')}" method="post" class="layui-form layui-form-pane register" >
            <div class="layui-form-item item">
                <label class="swiftadmin-login-icon layui-icon layui-icon-cellphone"></label>
                <input type="text" name="emailphone" lay-verify="required" placeholder="{:__('请输入手机号或者邮箱')}" class="layui-input">
            </div>
            <div class="layui-form-item imgcode">
                <div class="layui-row">
                    <div class="layui-col-xs7 item">
                        <label class="swiftadmin-login-icon layui-icon layui-icon-vercode" ></label>
                        <input type="text" name="imgcode" lay-verify="required" placeholder="{:__('图形验证码')}" class="layui-input">
                    </div>

                    <div class="layui-col-xs3 fr">
                        <div class="captcha fr" ><a href="javascript:;" ><img src="{:captcha_src()}" height="36" id="captchaImg3" alt="{:__('验证码')}"  /></a></div>
                    </div>
                </div>
            </div>

            <div class="layui-form-item valicode item" style="display: none">
                <div class="layui-row">
                    <label class="swiftadmin-login-icon layui-icon layui-icon-vercode" ></label>
                    <input type="text" name="valicode" placeholder="{:__('短信邮件验证码')}" class="layui-input">
                </div>
            </div>

            <div class="settingPwd" style="display: none;">
                <div class="layui-form-item item">
                    <label class="swiftadmin-login-icon layui-icon layui-icon-password" ></label>
                    <input type="password" name="pwds"  placeholder="{:__('密码')}" class="layui-input">
                </div>
                <div class="layui-form-item item">
                    <label class="swiftadmin-login-icon layui-icon layui-icon-password" ></label>
                    <input type="password" name="repwds"  placeholder="{:__('确认密码')}" class="layui-input">
                </div>
            </div>

            <div class="layui-form-item">
                <button class="layui-btn layui-btn-fluid layui-btn-normal layui-btn-fixed" lay-submit="">{:__('获取验证码')}</button>
            </div>
            <div class="layui-trans layui-form-item swiftadmin-login-other">
                <div class="other-login fl">
                    <label>{:__('社交账号注册')}</label>
                    <a href="javascript:;"><i class="layui-icon layui-icon-login-qq"></i></a>
                    <a href="javascript:;"><i class="layui-icon layui-icon-login-wechat"></i></a>
                    <a href="javascript:;"><i class="layui-icon layui-icon-login-weibo"></i></a>
                </div>
                <a href="javascript:;" sa-event-type="login" class="swiftadmin-user-jump-change swiftadmin-link layui-hide-xs">{:__('用已有帐号登入')}</a>
                <a href="javascript:;" sa-event-type="login" class="swiftadmin-user-jump-change swiftadmin-link layui-hide-sm layui-show-xs-inline-block">{:__('登入')}</a>
            </div>
        </form>
    </div>
</div>

<div class="layui-trans swiftadmin-login-footer">
    <p>
        <a href="#" target="_blank">{:__('帮助')}</a>
        <a href="#" target="_blank">{:__('隐私')}</a>
        <a href="#" target="_blank">{:__('条款')}</a>
    </p>
    <p><div class="copyright"> Copyright © 2020 swiftAdmin.net co., ltd</div></p>
</div>
</div>
</body>
<script src="__STATICADMIN__layui/layui.js?v={:release()}"></script>
<script src="__STATICADMIN__js/common.js?v={:release()}"></script>
<script>
    window.sessionStorage.clear();
    layui.use(['layer','form','show'],function() {
        var $ = layui.jquery,
            layer = layui.layer,
            form = layui.form,
            show = layui.show,
            captchaUrl = '{:captcha_src()}';

        // 登录操作
        form.on('submit(login)', function(data) {
            var that = $(this), _form = that.parents('form'),
                name = $('input[name="name"]').val(),
                pwd = $('input[name="pwd"]').val(),
                captcha = $('input[name="captcha"]').val();
            that.prop('disabled', true);
            $_ajax(that,{name: name, pwd: pwd, captcha: captcha});
            return false;
        })

        $("#forget-alert").on("click",function(){
            layer.alert("请联系管理员重置密码")
        });

        /**
         * 注册账户$(this).serialize(),
         */
        form.on('submit(register)',function(data){

            var that = $(this), _form = that.parents('form'),
                name = $('input[name="nickname"]').val(),
                pass = $('input[name="pass"]').val(),
                repass = $('input[name="repass"]').val(),
                captcha = $('input[name="captchar"]').val(),
                email = $('input[name="email"]').val();

            if (pass !== repass) {
                show.error('两次输入密码不同');
                return false;
            }

            show.info('数据提交中...');
            that.prop('disabled', true);
            $_ajax(that,{name: name, pwd: pass, email:email,captcha: captcha});
            return false;
        })

        /*
         * 分步表单1 获取验证码
        */
        $('.layui-btn-fixed').click(function() {
            if ($(this).hasClass('layui-btn-fixed') ==  false) {

                return false;
            }
            var name = $('input[name="emailphone"]').val(),
                captcha = $('input[name="imgcode"]').val();
            $.ajax({
                type: "POST",
                url: "{:url('/login/valicode')}",
                data: {name:name,captcha:captcha},
                success: function (res) {
                    if(res.code == 200){
                        layer.msg(res.msg,function(){
                            $('.imgcode').css('display','none');
                            $('.valicode').css('display','block');
                            $('.layui-btn-fixed').attr('lay-filter','forget');
                            // 移除click事件
                            $('.layui-btn-fixed').text("{:__('下一步')}").off('click');
                            $('.layui-btn-fixed').removeClass('layui-btn-fixed');
                        });

                    }else {
                        layer.msg(res.msg,function() {
                            $("#captchaImg,#captchaImg2,#captchaImg3").attr('src',captchaUrl+'?rand='+Math.random()).parents('.layui-form-item').show();
                        });
                    }
                }
            })

            return false;
        })


        /**
         * 分步表单2 验证账户权限
         */
        form.on('submit(forget)',function(data){

            var that = $(this),
                name = $('input[name="emailphone"]').val(),
                valicode = $('input[name="valicode"]').val();

            $.ajax({
                type: "POST",
                url: "{:url('/login/forget')}",
                data: {name:name,valicode:valicode},
                success: function (res) {
                    if(res.code == 200){
                        $('.valicode').css('display','none');
                        $('.settingPwd').css('display','block');
                        that.attr('lay-filter','settingPwd');
                        that.text("{:__('立即设置')}");
                    }else {
                        layer.msg(res.msg);
                    }
                }
            })

            return false;
        })


        /*
         * 分步表单3 提交设置密码
        */
        form.on('submit(settingPwd)',function(data){

            var name = $('input[name="emailphone"]').val(),
                pass = $('input[name="pwds"]').val(),
                repass = $('input[name="repwds"]').val(),
                valicode = $('input[name="valicode"]').val();

            if (pass != repass) {
                show.error('两次输入密码不同');
                return false;
            }

            $.ajax({
                type: "POST",
                url: "{:url('/login/setpwd')}",
                data: {name:name,pass:pass,valicode:valicode},
                success: function (res) {
                    if(res.code === 200){
                        layer.msg(res.msg,function(){
                            location.reload();
                        });
                    }else {
                        show.error(res.msg);
                    }
                }
            })

            return false;
        })


        /**
         * 提交数据
         */
        function $_ajax(that,data,jump) {

            var _form = that.attr('lay-filter'),
                _urls = $('.'+_form).attr('action');
            $.ajax({
                type: "POST",
                url: _urls,
                data: data,
                success: function (res) {
                    console.log(res)
                    if(res.code === 200){
                        layer.msg(res.msg);
                        if (jump === undefined) {
                            window.location = res.url; // 跳转到主页
                        }

                    }else {
                        layui.show.error(res.msg);
                        that.prop('disabled', false);
                        $("#captchaImg,#captchaImg2,#captchaImg3").attr('src',captchaUrl+'?rand='+Math.random()).parents('.layui-form-item').show();
                        return false;
                    }
                },
                error: function() {
                    that.prop('disabled', false);
                    layer.msg("{:__('好像是网络出错了...')}");
                }
            })

            return false;
        }

        /**
         * 切换功能
         */
        $(document).on("click","*[sa-event-type]",function(){
            var array = ['login','register','forget'],
                event = $(this).attr("sa-event-type");
            for (var i in array) {
                if (array[i] !== event) {
                    $('#' + array[i]).css('display','none');
                }
            }
            $('#' + event).css('display','block');
        })

        $(document).on('click', '#captchaImg,#captchaImg2,#captchaImg3', function(){
            $(this).attr('src', captchaUrl+'?rand='+Math.random())
        })
    })
</script>
</html>
