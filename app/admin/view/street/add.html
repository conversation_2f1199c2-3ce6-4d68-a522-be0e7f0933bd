<include file="/public/header" />
<!-- // 重定位style -->
<!---->
<link href="__STATICADMIN__css/content.css" rel="stylesheet" type="text/css" />
<div class="layui-fluid">
    <form class="layui-form layui-card" >

        <div class="layui-card-body">
        <gt name="$data.id" value="0">
            <input type="text" name="id" value="{$data.id}" hidden="">
            <else/>
            <input type="text" name="id" value="" hidden="">
        </gt>
            <div class="layui-form-item" >
                <div class="layui-input-block" style="margin-left: 0;"><blockquote class="layui-elem-quote">图片</blockquote></div>
            </div>
            <div class="layui-form-item" >
                <div class="layui-input-block" style="margin-left:0">
                    <div class="layui-imagesbox">
                        <php>
                            if (strpos($data['image'], 'http') === 0) {
                            $data['image'] = [['src'=>$data['image'],'title'=>'图片']];
                            }else{
                            $data['image'] = json_decode($data['image'],true);
                            }
                        </php>
                        <!-- // 循环输出代码 -->
                        <notempty name="$data['image']" >
                            <volist name="$data['image']" id="vo">
                                <div class="layui-input-inline layui-uplpad-image">
                                    <img src="{$vo.src}" lay-image-click >
                                    <input type="text" name="image[{$key}][src]" class="layui-hide" value="{$vo.src}" >
                                    <input type="text" name="image[{$key}][title]" class="layui-input  layui-image" value="{$vo.title}" placeholder="图片简介">
                                    <span class="layui-badge layui-badge-red" data-name="image" onclick="layui.admin.resetInput(this,'images');">删除</span>
                                </div>
                            </volist>
                        </notempty>
                        <div class="layui-input-inline layui-uplpad-image">
                            <div class="layui-upload-drag" lay-upload="image" data-type="multiple" >
                                <i class="layui-icon layui-icon-upload"></i>
                                <p>点击上传，或将文件拖拽到此处</p>
                                <div class="layui-hide"></div>
                            </div>
                        </div>
                    </div></div>
            </div>
        <div class="layui-form-item" >
<label class="layui-form-label"><font color="red">* </font>街路名称</label>
<div class="layui-input-block"><input class="layui-input"  name="name" placeholder="请输入" required="1" lay-verify=""value="{$data.name}" ></div>
</div>

<div class="layui-form-item" >
<label class="layui-form-label"><font color="red">* </font>街路类别</label>
<div class="layui-input-block">
<php>$Type_LIST = [0=>['title'=>'路','value'=>'1','checked'=>false,],1=>['title'=>'街','value'=>'2','checked'=>true,],];</php>        
        
        <select  name="type" lay-search="" required="1" >
            <volist name="Type_LIST" id="vo">
                <option value="{$vo.value}"
                <if (isset($data['id']) && $data['id']) >
                    <in name="$vo.value" value="$data.type">selected</in>
                    <else/>
                    <eq name="$vo.checked" value="true">selected</eq>
                    </if>
                >{$vo.title}</option>
            </volist>
        </select></div>
</div>

<div class="layui-form-item" >
    <div class="layui-inline">
<label class="layui-form-label"><font color="red">* </font>街路起止号</label>
    <div class="layui-input-inline" style="width: 100px;">
        <input class="layui-input"  name="start" placeholder="请输入" required="1" lay-verify="number"value="{$data.start}" >
    </div>
    <div class="layui-form-mid">
        -
    </div>
    <div class="layui-input-inline" style="width: 100px;">
        <input class="layui-input"  name="end" placeholder="请输入" required="1" lay-verify="number"value="{$data.end}">
    </div>
    </div>
</div>

<div class="layui-form-item" >
<label class="layui-form-label">备注</label>
<div class="layui-input-block"><textarea class="layui-textarea" name="details" placeholder="请输入" >{$data.details}</textarea></div>
</div>

        <div class="layui-footer layui-form-footer">
            <button class="layui-btn layui-btn-primary" type="button" sa-event="closeDialog">{:__('取消')}</button>
            <button class="layui-btn" type="button" lay-filter="submitIframe" lay-submit>{:__('提交')}</button>
        </div>
    </form>
</div>
<include file="/public/static" />
<include file="/public/footer" />
<include file="/public/select" />