<include file="/public/header" />
<php>
    $lawtype_id = getdictionary(534);
</php>
<!-- // 重定位style -->
<!---->
<link href="__STATICADMIN__css/content.css" rel="stylesheet" type="text/css" />
<div class="layui-fluid">
    <form class="layui-form layui-card" >

        <div class="layui-card-body">
        <gt name="$data.id" value="0">
            <input type="text" name="id" value="{$data.id}" hidden="">
            <else/>
            <input type="text" name="id" value="" hidden="">
        </gt>
        <div class="layui-form-item" >

<div class="layui-form-item">
            <label class="layui-form-label"><font color="red">* </font>栏目分类</label>
            <div class="layui-input-block">
                <select name="lawtype_id" lay-verify="required">
                    <if empty($data['id']) >
                        <option value=''>请选择</option>
                    </if>
                    <!-- <option value=''>请选择</option> -->
                    <foreach name="lawtype_id" item="vo" key="key">
                        <option value="{$vo}"
                        <if (isset($data['id']) && $data['id']) >
                        <in name="$key" value="$data.lawtype_id">selected</in>
                        </if>
                        >{$vo}</option>
                    </foreach>
                </select>
            </div>
</div>

<div class="layui-form-item" >
<label class="layui-form-label"><font color="red">* </font>标题</label>
<div class="layui-input-block"><input class="layui-input"  name="law_name" placeholder="请输入" lay-verify="required" value="{$data.law_name}" ></div>
</div>

<div class="layui-form-item" >
<label class="layui-form-label"><font color="red">* </font>简介</label>
<div class="layui-input-block"><textarea class="layui-textarea" name="introduction" lay-verify="required" placeholder="请输入" >{$data.introduction}</textarea></div>
</div>

<div class="layui-form-item" >
<label class="layui-form-label"><font color="red">* </font>内容</label>
<div class="layui-input-block">    <textarea id="law_text" lay-editor class="layui-hide" lay-verify="required"  name="law_text" type="layui-textarea" >{$data.law_text}</textarea></div>
</div>

<div class="layui-form-item" >
<label class="layui-form-label"><font color="red">* </font>封面</label>
<div class="layui-input-block">
            <div class="layui-imagesbox">
                <php>
                    if (strpos($data['image'], 'http') === 0) {
                    $data['image'] = [['src'=>$data['image'],'title'=>'图片']];
                    }else{
                    $data['image'] = json_decode($data['image'],true);
                    }
                </php>
                <!-- // 循环输出代码 -->
                <notempty name="$data['image']" >
                <volist name="$data['image']" id="vo">
                    <div class="layui-input-inline layui-uplpad-image">
                        <img src="{$vo.src}" lay-image-hover >
                        <input type="text" name="image[{$key}][src]" class="layui-hide" value="{$vo.src}" >
                        <input type="text" name="image[{$key}][title]" class="layui-input" value="{$vo.title}" placeholder="图片简介">
                        <span class="layui-badge layui-badge-red" data-name="image" onclick="layui.admin.resetInput(this,'images');">删除</span>
                    </div>
                </volist>
                </notempty>
                <div class="layui-input-inline layui-uplpad-image">
                    <div class="layui-upload-drag" lay-upload="image" data-type="multiple"  data-accept="file" data-size="102400">
                        <i class="layui-icon layui-icon-upload"></i>
                        <p>点击上传，或将文件拖拽到此处</p>
                        <div class="layui-hide"></div>
                    </div>
                    <button type="button" class="layui-btn layui-btn-xs layui-btn-fluid"  lay-choose="image" data-name="image" data-type="multiple">
                        <i class="layui-icon layui-icon-windows"></i> 选择
                    </button>
                </div>
            </div></div>
</div>

<div class="layui-form-item" >
<label class="layui-form-label">是否展示</label>
<div class="layui-input-block">    <input  type="hidden" type="checkbox" name="status" value="0" />
    <input type="checkbox" name="status" value="1" <eq name="$data.status" value="1" > checked </eq> lay-skin="switch" /></div>
</div>

        <div class="layui-footer layui-form-footer">
            <button class="layui-btn layui-btn-primary" type="button" sa-event="closeDialog">{:__('取消')}</button>
            <button class="layui-btn" type="button" lay-filter="submitIframe" lay-submit>{:__('提交')}</button>
        </div>
    </form>
</div>
<include file="/public/static"/>
<include file="/public/footer"/>
<include file="/public/select"/>