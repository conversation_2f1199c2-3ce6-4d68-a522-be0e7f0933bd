<include file="/public/header"/>
<php>
    $Communities = getCommunity();
    if($Communities[0]['id']==1){
    unset($Communities[0]);
    $Communities = array_values($Communities);
    }
    $community_id = empty($data['community_id'])?$Communities[0]['id']:$data['community_id'];
    $GridGroups = getGridGroup($community_id);
    $grid_group_id = empty($data['grid_group_id'])?$GridGroups[0]['id']:$data['grid_group_id'];
    $Grids = getGrid($grid_group_id);
    $grid_id = empty($data['grid_id'])?$Grids[0]['id']:$data['grid_id'];
    $Buildings = getBuilding($grid_id);
    $building_id = empty($data['building_id'])?$Buildings[0]['id']:$data['building_id'];
    $Houses = getHouse($building_id);
    $ids = [404,408,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,
    411,418,522,465,471,478,489,30,42,401];
    $dictionaryGroup = getDictionaryGroup($ids);
    $rpr = $dictionaryGroup[404];
    $rpr_nature = $dictionaryGroup[408];
    $relation = $dictionaryGroup[53];
    $nation = $dictionaryGroup[54];
    $religion = $dictionaryGroup[55];
    $education = $dictionaryGroup[56];
    $enlistment = $dictionaryGroup[57];
    $face = $dictionaryGroup[58];
    $person_type = $dictionaryGroup[59];
    $house_type = $dictionaryGroup[60];
    $health = $dictionaryGroup[61];
    $blood = $dictionaryGroup[62];
    $labor_capacity = $dictionaryGroup[63];
    $marry = $dictionaryGroup[64];
    $only_child = $dictionaryGroup[65];
    $other_child = $dictionaryGroup[66];
    $endowment_insurance = $dictionaryGroup[67];
    $insurance = $dictionaryGroup[68];
    $representative = $dictionaryGroup[69];
    $elderly = $dictionaryGroup[70];
    $dilemma = $dictionaryGroup[71];
    $dilemma_children = $dictionaryGroup[72];
    $patient = $dictionaryGroup[73];
    $bad = $dictionaryGroup[74];
    $other = $dictionaryGroup[75];
    $disabilityType = $dictionaryGroup[411];
    $disabilityLevel = $dictionaryGroup[418];
    $employment = $dictionaryGroup[522];
    $employment_before = $dictionaryGroup[76];
    $employment_form = $dictionaryGroup[77];
    $employment_unit_property = $dictionaryGroup[78];
    $employment_difficult_type = $dictionaryGroup[79];
    $employment_idea = $dictionaryGroup[465];
    $accept_employment_service = $dictionaryGroup[471];
    $unemployment_cause = $dictionaryGroup[478];
    $unemployment_difficult_type = $dictionaryGroup[489];
</php>
<!---->
<link href="__STATICADMIN__css/content.css" rel="stylesheet" type="text/css"/>
<div class="layui-fluid">
    <neq name="action" value="detail">
    <form class="layui-form layui-card">
        <else/>
        <div class="layui-form layui-card">
    </neq>
        <div class="layui-card-body">
            <gt name="$data.id" value="0">
                <input type="text" name="id" value="{$data.id}" hidden="">
                <else/>
                <input type="text" name="id" value="" hidden="">
            </gt>
            <eq name="type" value="1">
                <input type="text" name="rpr_nature" value="1" hidden="">
            </eq>
            <eq name="type" value="2">
                <input type="text" name="person_type" value="4" hidden="">
            </eq>
            <div id="layui-tab" id="tab_0" class="layui-tab layui-tab-brief">

                <div class="layui-form-item layui-row">
                    <div class="layui-col-md6 layui-grid-0" data-index="0">
                        <div class="layui-input-block" style="margin-left: 0;">
                            <blockquote class="layui-elem-quote">当前数据</blockquote>
                        </div>
                        <div class="layui-form-item layui-row">
                            <div class="layui-col-md12 layui-grid-0" data-index="0">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">所属社区</label>
                                    <div class="layui-input-block">
                                        <div class="layui-form-label" style="width: auto; text-align:left;">{$data.community_name}</div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">所属网格组</label>
                                    <div class="layui-input-block">
                                        
                                        <div class="layui-form-label" style="width: auto; text-align:left;">{$data.grid_group_name}</div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">所属网格</label>
                                    <div class="layui-input-block">
                                        
                                        <div class="layui-form-label" style="width: auto; text-align:left;">{$data.grid_name}</div>
                                    </div>
                                </div>
                                <lt name="type" value="2">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">所在建筑</label>
                                        <div class="layui-input-block">
                                            
                                        <div class="layui-form-label" style="width: auto; text-align:left;">{$data.building_name}</div>
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">所属房屋</label>
                                        <div class="layui-input-block">
                                            
                                        <div class="layui-form-label" style="width: auto; text-align:left;">
                                            {$data.house_code}<notempty name="$data.street">({$data.house_street})</notempty>
                                            </div>
                                            
                                        </div>
                                    </div>
                                </lt>
                                <div class="layui-form-item">
                                    <label class="layui-form-label" style="width:106px;">户口一致性标识</label>
                                    <div class="layui-input-block" style="margin-left:136px">
                                        <input type="checkbox" name="consistency" lay-skin="primary"
                                               value="1" title="户口一致"
                                        <if (isset($data['consistency']) && $data['consistency'] == 1) >
                                        checked
                                        </if>
                                        >
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">户口类型</label>
                                    <div class="layui-input-block">
                                        <select name="rpr">
                                            <option value=''>请选择</option>
                                            <foreach name="rpr" item="vo" key="key">
                                                <option value="{$key}"
                                                <if (isset($data['id']) && $data['id']) >
                                                <in name="$key" value="$data.rpr">selected</in>
                                                </if>
                                                >{$vo}</option>
                                            </foreach>
                                        </select>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">户口性质</label>
                                    <div class="layui-input-block">
                                        <neq name="type" value="1">
                                            <select name="rpr_nature" >
                                                <option value=''>请选择</option>
                                                <foreach name="rpr_nature" item="vo" key="key">
                                                    <option value="{$key}"
                                                    <if (isset($data['id']) && $data['id']) >
                                                    <in name="$key" value="$data.rpr_nature">selected</in>
                                                    </if>
                                                    >{$vo}</option>
                                                </foreach>
                                            </select>
                                            <else/>
                                            <div class="layui-form-label">本地</div>
                                        </neq>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">户口编号</label>
                                    <div class="layui-input-block"><input class="layui-input" name="rpr_code"
                                                                          placeholder="请输入" lay-verify=""
                                                                          value="{$data.rpr_code}"></div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">备注</label>
                                    <div class="layui-input-block"><input class="layui-input" name="details"
                                                                          placeholder="请输入" lay-verify=""
                                                                          value="{$data.details}"></div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">微信号</label>
                                    <div class="layui-input-block"><input class="layui-input" name="wechat_code"
                                                                          placeholder="请输入" lay-verify=""
                                                                          value="{$data.wechat_code}"></div>
                                </div>
                            </div>
                            <div class="layui-col-md12 layui-grid-1" data-index="1">
                                <div class="layui-form-item">
                                    <label class="layui-form-label"><font color="red">* </font>姓名</label>
                                    <div class="layui-input-block"><input class="layui-input" name="name"
                                                                          placeholder="请输入" lay-verify="required"
                                                                          value="{$data.name}"></div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label"><font color="red">* </font>与户主关系</label>
                                    <div class="layui-input-block">
                                        <select name="relation" lay-verify="required">
                                            <option value=''>请选择</option>
                                            <foreach name="relation" item="vo" key="key">
                                                <option value="{$key}"
                                                <if (isset($data['id']) && $data['id']) >
                                                <in name="$key" value="$data.relation">selected</in>
                                                </if>
                                                >{$vo}</option>
                                            </foreach>
                                        </select>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">曾用名</label>
                                    <div class="layui-input-block"><input class="layui-input" name="re_name"
                                                                          placeholder="请输入" lay-verify=""
                                                                          value="{$data.re_name}"></div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label"><font color="red">* </font>身份证号</label>
                                    <div class="layui-input-block"><input class="layui-input" name="id_code"
                                                                          placeholder="请输入"
                                                                          lay-verify="required|identity" value="{$data.id_code}">
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label"><font color="red">* </font>民族</label>
                                    <div class="layui-input-block">
                                        <select name="nation" lay-verify="required">
                                            <option value=''>请选择</option>
                                            <foreach name="nation" item="vo" key="key">
                                                <option value="{$key}"
                                                <if (isset($data['id']) && $data['id']) >
                                                <in name="$key" value="$data.nation">selected</in>
                                                </if>
                                                >{$vo}</option>
                                            </foreach>
                                        </select>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">籍贯</label>
                                    <div class="layui-input-block"><input class="layui-input" name="hometown"
                                                                          placeholder="请输入" lay-verify=""
                                                                          value="{$data.hometown}"></div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label"><font color="red">* </font>户籍所在地</label>
                                    <div class="layui-input-block"><input class="layui-input" name="rpr_address"
                                                                          placeholder="请输入" lay-verify="required"
                                                                          value="{$data.rpr_address}"></div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">宗教信仰</label>
                                    <div class="layui-input-block">
                                        <select name="religion">
                                            <option value=''>请选择</option>
                                            <foreach name="religion" item="vo" key="key">
                                                <option value="{$key}"
                                                <if (isset($data['id']) && $data['id']) >
                                                <in name="$key" value="$data.religion">selected</in>
                                                </if>
                                                >{$vo}</option>
                                            </foreach>
                                        </select>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label"><font color="red">* </font>文化程度</label>
                                    <div class="layui-input-block">
                                        <select name="education" lay-verify="required">
                                            <option value=''>请选择</option>
                                            <foreach name="education" item="vo" key="key">
                                                <option value="{$key}"
                                                <if (isset($data['id']) && $data['id']) >
                                                <in name="$key" value="$data.education">selected</in>
                                                </if>
                                                >{$vo}</option>
                                            </foreach>
                                        </select>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">毕业时间</label>
                                    <div class="layui-input-block">
                                        <input class="layui-input datepicker"
                                               lay-datetime=""
                                               name="graduation_date"
                                               data-datetype="date"
                                               data-dateformat="yyyy-MM-dd"
                                               placeholder="yyyy-MM-dd"
                                               data-maxvalue="9999-12-31"
                                               data-minvalue="1900-01-01"
                                               data-range=""
                                               value="{$data.graduation_date}">
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">毕业院校</label>
                                    <div class="layui-input-block"><input class="layui-input" name="school"
                                                                          placeholder="请输入" lay-verify=""
                                                                          value="{$data.school}"></div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">所在专业</label>
                                    <div class="layui-input-block"><input class="layui-input" name="major"
                                                                          placeholder="请输入" lay-verify=""
                                                                          value="{$data.major}"></div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">兵役情况</label>
                                    <div class="layui-input-block">
                                        <select name="enlistment">
                                            <option value=''>请选择</option>
                                            <foreach name="enlistment" item="vo" key="key">
                                                <option value="{$key}"
                                                <if (isset($data['id']) && $data['id']) >
                                                <in name="$key" value="$data.enlistment">selected</in>
                                                </if>
                                                >{$vo}</option>
                                            </foreach>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-col-md12 layui-grid-2" data-index="2">
                                <div class="layui-form-item">
                                    <label class="layui-form-label"><font color="red">* </font>政治面貌</label>
                                    <div class="layui-input-block">
                                        <select name="face" lay-verify="required">
                                            <option value=''>请选择</option>
                                            <foreach name="face" item="vo" key="key">
                                                <option value="{$key}"
                                                <if (isset($data['id']) && $data['id']) >
                                                <in name="$key" value="$data.face">selected</in>
                                                </if>
                                                >{$vo}</option>
                                            </foreach>
                                        </select>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label"><font color="red">* </font>家庭住址</label>
                                    <div class="layui-input-block"><input class="layui-input" name="address"
                                                                          placeholder="请输入" lay-verify="required"
                                                                          value="{$data.address}"></div>
                                </div>
                                <neq name="type" value="2">
                                <div class="layui-form-item">
                                    <label class="layui-form-label"><font color="red">* </font>人口类型</label>
                                    <div class="layui-input-block">
                                        <select name="person_type" lay-verify="required">
                                            <option value=''>请选择</option>
                                            <foreach name="person_type" item="vo" key="key">
                                                <option value="{$key}"
                                                <if (isset($data['id']) && $data['id']) >
                                                <in name="$key" value="$data.person_type">selected</in>
                                                </if>
                                                >{$vo}</option>
                                            </foreach>
                                            <eq name="type" value="1">
                                                <option value='4'
                                                <if (isset($data['id']) && $data['id']) >
                                                <in name="$key" value="$data.person_type">selected</in>
                                                </if>
                                                >委外人口</option>
                                            </eq>
                                        </select>
                                    </div>
                                </div>
                                </neq>
                                <div class="layui-form-item">
                                    <label class="layui-form-label"><font color="red">* </font>住宅类型</label>
                                    <div class="layui-input-block">
                                        <select name="house_type" lay-verify="required">
                                            <option value=''>请选择</option>
                                            <foreach name="house_type" item="vo" key="key">
                                                <option value="{$key}"
                                                <if (isset($data['id']) && $data['id']) >
                                                <in name="$key" value="$data.house_type">selected</in>
                                                </if>
                                                >{$vo}</option>
                                            </foreach>
                                        </select>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">住宅面积</label>
                                    <div class="layui-input-block"><input class="layui-input" id="area" name="house_area"
                                                                          placeholder="请输入" readonly
                                                                          value="{$data.house_area}"></div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">健康状况</label>
                                    <div class="layui-input-block">
                                        <select name="health">
                                            <option value=''>请选择</option>
                                            <foreach name="health" item="vo" key="key">
                                                <option value="{$key}"
                                                <if (isset($data['id']) && $data['id']) >
                                                <in name="$key" value="$data.health">selected</in>
                                                </if>
                                                >{$vo}</option>
                                            </foreach>
                                        </select>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">血型</label>
                                    <div class="layui-input-block">
                                        <select name="blood">
                                            <option value=''>请选择</option>
                                            <foreach name="blood" item="vo" key="key">
                                                <option value="{$key}"
                                                <if (isset($data['id']) && $data['id']) >
                                                <in name="$key" value="$data.blood">selected</in>
                                                </if>
                                                >{$vo}</option>
                                            </foreach>
                                        </select>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label"><font color="red">* </font>劳动能力</label>
                                    <div class="layui-input-block">
                                        <select name="labor_capacity" lay-verify="required">
                                            <option value=''>请选择</option>
                                            <foreach name="labor_capacity" item="vo" key="key">
                                                <option value="{$key}"
                                                <if (isset($data['id']) && $data['id']) >
                                                <in name="$key" value="$data.labor_capacity">selected</in>
                                                </if>
                                                >{$vo}</option>
                                            </foreach>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-col-md12 layui-grid-3" data-index="3">
                                <div class="layui-form-item">
                                    <label class="layui-form-label" style="width:106px;"><font color="red">* </font>婚姻状况</label>
                                    <div class="layui-input-block" style="margin-left:136px">
                                        <select name="marry" lay-verify="required">
                                            <option value=''>请选择</option>
                                            <foreach name="marry" item="vo" key="key">
                                                <option value="{$key}"
                                                <if (isset($data['id']) && $data['id']) >
                                                <in name="$key" value="$data.marry">selected</in>
                                                </if>
                                                >{$vo}</option>
                                            </foreach>
                                        </select>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label" style="width:106px;">独生子女家庭</label>
                                    <div class="layui-input-block" style="margin-left:136px">
                                        <select name="only_child">
                                            <option value=''>请选择</option>
                                            <foreach name="only_child" item="vo" key="key">
                                                <option value="{$key}"
                                                <if (isset($data['id']) && $data['id']) >
                                                <in name="$key" value="$data.only_child">selected</in>
                                                </if>
                                                >{$vo}</option>
                                            </foreach>
                                        </select>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label" style="width:106px;">其他子女家庭</label>
                                    <div class="layui-input-block" style="margin-left:136px">
                                        <select name="other_child">
                                            <option value=''>请选择</option>
                                            <foreach name="other_child" item="vo" key="key">
                                                <option value="{$key}"
                                                <if (isset($data['id']) && $data['id']) >
                                                <in name="$key" value="$data.other_child">selected</in>
                                                </if>
                                                >{$vo}</option>
                                            </foreach>
                                        </select>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label" style="width:106px;"><font color="red">* </font>联系方式</label>
                                    <div class="layui-input-block" style="margin-left:136px"><input class="layui-input"
                                                                                                    name="phone"
                                                                                                    placeholder="请输入"
                                                                                                    lay-verify="required"
                                                                                                    value="{$data.phone}">
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label" style="width:106px;">联系方式1</label>
                                    <div class="layui-input-block" style="margin-left:136px"><input class="layui-input"
                                                                                                    name="phone_1"
                                                                                                    placeholder="请输入"
                                                                                                    lay-verify=""
                                                                                                    value="{$data.phone_1}">
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label" style="width:106px;">联系方式2</label>
                                    <div class="layui-input-block" style="margin-left:136px"><input class="layui-input"
                                                                                                    name="phone_2"
                                                                                                    placeholder="请输入"
                                                                                                    lay-verify=""
                                                                                                    value="{$data.phone_2}">
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label" style="width:106px;">紧急联系手机</label>
                                    <div class="layui-input-block" style="margin-left:136px"><input class="layui-input"
                                                                                                    name="urgent_phone"
                                                                                                    placeholder="请输入"
                                                                                                    lay-verify=""
                                                                                                    value="{$data.urgent_phone}">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item layui-row">
                            <div class="layui-col-md12 layui-grid-0" data-index="0">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">是否残疾</label>
                                    <div class="layui-input-block">
                                        <input type="checkbox" id="disability" name="disability" lay-filter="tplChange" lay-skin="switch"
                                               value="1" title="是"
                                        <if (isset($data['disability']) && $data['disability'] == 1) >
                                        checked
                                        </if>
                                        >
                                    </div>
                                </div>
                                <eq name="history_data.disability" value="1">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">残疾类型</label>
                                        <div class="layui-input-block">
                                            <select name="disability_type">
                                                <foreach name="disabilityType" item="vo" key="key">
                                                    <option value="{$key}"
                                                    <if (isset($data['id']) && $data['id']) >
                                                    <in name="$key" value="$data.disability_type">selected</in>
                                                    </if>
                                                    >{$vo}</option>
                                                </foreach>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">残疾等级</label>
                                        <div class="layui-input-block">
                                            <select name="disability_level">
                                                <foreach name="disabilityLevel" item="vo" key="key">
                                                    <option value="{$key}"
                                                    <if (isset($data['id']) && $data['id']) >
                                                    <in name="$key" value="$data.disability_level">selected</in>
                                                    </if>
                                                    >{$vo}</option>
                                                </foreach>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">残疾证号码</label>
                                        <div class="layui-input-block">
                                            <input class="layui-input"
                                                   name="disability_code"
                                                   placeholder="请输入"
                                                   value="{$data.disability_code}">
                                        </div>
                                    </div>
                                </eq>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">是否退休</label>
                                    <div class="layui-input-block">
                                            <input type="checkbox" id="retire" name="retire" lay-filter="tplChange" lay-skin="switch"
                                                   value="1" title="是"
                                            <if (isset($data['retire']) && $data['retire'] == 1) >
                                            checked
                                            </if>
                                            >
                                    </div>
                                </div>
                                <eq name="history_data.retire" value="1">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">退休单位</label>
                                        <div class="layui-input-block">
                                            <input class="layui-input"
                                                   name="retire_unit"
                                                   placeholder="请输入"
                                                   value="{$data.retire_unit}">
                                        </div>
                                    </div>
                                </eq>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">是否死亡</label>
                                    <div class="layui-input-block">
                                            <input type="checkbox" id="dead" name="dead" lay-filter="tplChange" lay-skin="switch"
                                                   value="1" title="是"
                                            <if (isset($data['dead']) && $data['dead'] == 1) >
                                            checked
                                            </if>
                                            >
                                    </div>
                                </div>
                                <eq name="history_data.dead">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">死亡日期</label>
                                        <div class="layui-input-block">
                                            <input class="layui-input datepicker"
                                                   lay-datetime="" name="dead_date"
                                                   data-datetype="date"
                                                   data-dateformat="yyyy-MM-dd"
                                                   placeholder="yyyy-MM-dd"
                                                   data-maxvalue="9999-12-31"
                                                   data-minvalue="1900-01-01"
                                                   data-range=""
                                                   value="{$data.dead_date}">
                                        </div>
                                    </div>
                                </eq>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">养老保险</label>
                                    <div class="layui-input-block">
                                        <select name="endowment_insurance">
                                            <option value=''>请选择</option>
                                            <foreach name="endowment_insurance" item="vo" key="key">
                                                <option value="{$key}"
                                                <if (isset($data['id']) && $data['id']) >
                                                <in name="$key" value="$data.endowment_insurance">selected</in>
                                                </if>
                                                >{$vo}</option>
                                            </foreach>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-col-md12 layui-grid-1" data-index="1">
                                <div class="layui-form-item">
                                    <label class="layui-form-label" style="width:60px;">医保</label>
                                    <div class="layui-input-block" style="margin-left:90px">
                                        <foreach name="insurance"  item="vo" key="key">
                                            <input type="checkbox"  name="{$key}" lay-skin="primary"
                                                   value="1" title="{$vo}"
                                            <if (isset($data[$key]) && $data[$key] == 1) >
                                            checked
                                            </if>
                                            >
                                        </foreach>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label" style="width:60px;">政务人群</label>
                                    <div class="layui-input-block" style="margin-left:90px">
                                        <foreach name="representative"  item="vo" key="key">
                                            <php>
                                                $key = camelCaseToUnderscore($key);
                                            </php>
                                            <input type="checkbox"  name="{$key}" lay-skin="primary"
                                                   value="1" title="{$vo}"
                                            <if (isset($data[$key]) && $data[$key] == 1) >
                                            checked
                                            </if>
                                            >
                                        </foreach>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-col-md12 layui-grid-2" data-index="2">
                                <div class="layui-form-item">
                                    <label class="layui-form-label" style="width:60px;">老人</label>
                                    <div class="layui-input-block" style="margin-left:90px">
                                        <foreach name="elderly"  item="vo" key="key">
                                            <php>
                                                $key = camelCaseToUnderscore($key);
                                            </php>
                                            <input type="checkbox"  name="{$key}" lay-skin="primary"
                                                   value="1" title="{$vo}"
                                            <if (isset($data[$key]) && $data[$key] == 1) >
                                            checked
                                            </if>
                                            >
                                        </foreach>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label" style="width:60px;">困境人群</label>
                                    <div class="layui-input-block" style="margin-left:90px">
                                        <foreach name="dilemma"  item="vo" key="key">
                                            <php>
                                                $key = camelCaseToUnderscore($key);
                                            </php>
                                            <input type="checkbox"  name="{$key}" lay-skin="primary"
                                                   value="1" title="{$vo}"
                                            <if (isset($data[$key]) && $data[$key] == 1) >
                                            checked
                                            </if>
                                            >
                                        </foreach>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label" style="width:60px;">困境儿童</label>
                                    <div class="layui-input-block" style="margin-left:90px">
                                        <foreach name="dilemma_children"  item="vo" key="key">
                                            <php>
                                                $key = camelCaseToUnderscore($key);
                                            </php>
                                            <input type="checkbox"  name="{$key}" lay-skin="primary"
                                                   value="1" title="{$vo}"
                                            <if (isset($data[$key]) && $data[$key] == 1) >
                                            checked
                                            </if>
                                            >
                                        </foreach>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label" style="width:60px;">病人</label>
                                    <div class="layui-input-block" style="margin-left:90px">
                                        <foreach name="patient"  item="vo" key="key">
                                            <php>
                                                $key = camelCaseToUnderscore($key);
                                            </php>
                                            <input type="checkbox"  name="{$key}" lay-skin="primary"
                                                   value="1" title="{$vo}"
                                            <if (isset($data[$key]) && $data[$key] == 1) >
                                            checked
                                            </if>
                                            >
                                        </foreach>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-col-md12 layui-grid-3" data-index="3">
                                <div class="layui-form-item">
                                    <label class="layui-form-label" style="width:60px;">劣迹人群</label>
                                    <div class="layui-input-block" style="margin-left:90px">
                                        <foreach name="bad"  item="vo" key="key">
                                            <php>
                                                $key = camelCaseToUnderscore($key);
                                            </php>
                                            <input type="checkbox"  name="{$key}" lay-skin="primary"
                                                   value="1" title="{$vo}"
                                            <if (isset($data[$key]) && $data[$key] == 1) >
                                                checked
                                            </if>
                                            >
                                        </foreach>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label" style="width:60px;">其他人群</label>
                                    <div class="layui-input-block" style="margin-left:90px">
                                        <foreach name="other"  item="vo" key="key">
                                            <php>
                                                $key = camelCaseToUnderscore($key);
                                            </php>
                                            <input type="checkbox"  name="{$key}" lay-skin="primary"
                                                   value="1" title="{$vo}"
                                            <if (isset($data[$key]) && $data[$key] == 1) >
                                                checked
                                            </if>
                                            >
                                        </foreach>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md6 layui-grid-1" data-index="1">
                        <div class="layui-input-block" style="margin-left: 0;">
                            <blockquote class="layui-elem-quote">历史数据</blockquote>
                        </div>
                        <div class="layui-form-item layui-row">
                            <div class="layui-col-md12 layui-grid-0" data-index="0">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">所属社区</label>
                                    <div class="layui-input-block">
                                        <div class="layui-form-label" style="width: auto; text-align:left;">{$history_data.community_name}</div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">所属网格组</label>
                                    <div class="layui-input-block">
                                        
                                        <div class="layui-form-label" style="width: auto; text-align:left;">{$history_data.grid_group_name}</div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">所属网格</label>
                                    <div class="layui-input-block">
                                        
                                        <div class="layui-form-label" style="width: auto; text-align:left;">{$history_data.grid_name}</div>
                                    </div>
                                </div>
                                <lt name="type" value="2">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">所在建筑</label>
                                        <div class="layui-input-block">
                                            
                                        <div class="layui-form-label" style="width: auto; text-align:left;">{$history_data.building_name}</div>
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">所属房屋</label>
                                        <div class="layui-input-block">
                                            
                                        <div class="layui-form-label" style="width: auto; text-align:left;">
                                            {$history_data.house_code}<notempty name="$history_data.street">({$history_data.house_street})</notempty>
                                            </div>
                                            
                                        </div>
                                    </div>
                                </lt>
                                <div class="layui-form-item">
                                    <label class="layui-form-label" style="width:106px;">户口一致性标识</label>
                                    <div class="layui-input-block" style="margin-left:136px">
                                        <input type="checkbox" name="consistency" lay-skin="primary"
                                               value="1" title="户口一致"
                                        <if (isset($history_data['consistency']) && $history_data['consistency'] == 1) >
                                        checked
                                        </if>
                                        >
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">户口类型</label>
                                    <div class="layui-input-block">
                                        <select name="rpr">
                                            <option value=''>请选择</option>
                                            <foreach name="rpr" item="vo" key="key">
                                                <option value="{$key}"
                                                <if (isset($history_data['id']) && $history_data['id']) >
                                                <in name="$key" value="$history_data.rpr">selected</in>
                                                </if>
                                                >{$vo}</option>
                                            </foreach>
                                        </select>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">户口性质</label>
                                    <div class="layui-input-block">
                                        <neq name="type" value="1">
                                            <select name="rpr_nature" >
                                                <option value=''>请选择</option>
                                                <foreach name="rpr_nature" item="vo" key="key">
                                                    <option value="{$key}"
                                                    <if (isset($history_data['id']) && $history_data['id']) >
                                                    <in name="$key" value="$history_data.rpr_nature">selected</in>
                                                    </if>
                                                    >{$vo}</option>
                                                </foreach>
                                            </select>
                                            <else/>
                                            <div class="layui-form-label">本地</div>
                                        </neq>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">户口编号</label>
                                    <div class="layui-input-block"><input class="layui-input" name="rpr_code"
                                                                          placeholder="请输入" lay-verify=""
                                                                          value="{$history_data.rpr_code}"></div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">备注</label>
                                    <div class="layui-input-block"><input class="layui-input" name="details"
                                                                          placeholder="请输入" lay-verify=""
                                                                          value="{$history_data.details}"></div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">微信号</label>
                                    <div class="layui-input-block"><input class="layui-input" name="wechat_code"
                                                                          placeholder="请输入" lay-verify=""
                                                                          value="{$history_data.wechat_code}"></div>
                                </div>
                            </div>
                            <div class="layui-col-md12 layui-grid-1" data-index="1">
                                <div class="layui-form-item">
                                    <label class="layui-form-label"><font color="red">* </font>姓名</label>
                                    <div class="layui-input-block"><input class="layui-input" name="name"
                                                                          placeholder="请输入" lay-verify="required"
                                                                          value="{$history_data.name}"></div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label"><font color="red">* </font>与户主关系</label>
                                    <div class="layui-input-block">
                                        <select name="relation" lay-verify="required">
                                            <option value=''>请选择</option>
                                            <foreach name="relation" item="vo" key="key">
                                                <option value="{$key}"
                                                <if (isset($history_data['id']) && $history_data['id']) >
                                                <in name="$key" value="$history_data.relation">selected</in>
                                                </if>
                                                >{$vo}</option>
                                            </foreach>
                                        </select>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">曾用名</label>
                                    <div class="layui-input-block"><input class="layui-input" name="re_name"
                                                                          placeholder="请输入" lay-verify=""
                                                                          value="{$history_data.re_name}"></div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label"><font color="red">* </font>身份证号</label>
                                    <div class="layui-input-block"><input class="layui-input" name="id_code"
                                                                          placeholder="请输入"
                                                                          lay-verify="required|identity" value="{$history_data.id_code}">
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label"><font color="red">* </font>民族</label>
                                    <div class="layui-input-block">
                                        <select name="nation" lay-verify="required">
                                            <option value=''>请选择</option>
                                            <foreach name="nation" item="vo" key="key">
                                                <option value="{$key}"
                                                <if (isset($history_data['id']) && $history_data['id']) >
                                                <in name="$key" value="$history_data.nation">selected</in>
                                                </if>
                                                >{$vo}</option>
                                            </foreach>
                                        </select>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">籍贯</label>
                                    <div class="layui-input-block"><input class="layui-input" name="hometown"
                                                                          placeholder="请输入" lay-verify=""
                                                                          value="{$history_data.hometown}"></div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label"><font color="red">* </font>户籍所在地</label>
                                    <div class="layui-input-block"><input class="layui-input" name="rpr_address"
                                                                          placeholder="请输入" lay-verify="required"
                                                                          value="{$history_data.rpr_address}"></div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">宗教信仰</label>
                                    <div class="layui-input-block">
                                        <select name="religion">
                                            <option value=''>请选择</option>
                                            <foreach name="religion" item="vo" key="key">
                                                <option value="{$key}"
                                                <if (isset($history_data['id']) && $history_data['id']) >
                                                <in name="$key" value="$history_data.religion">selected</in>
                                                </if>
                                                >{$vo}</option>
                                            </foreach>
                                        </select>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label"><font color="red">* </font>文化程度</label>
                                    <div class="layui-input-block">
                                        <select name="education" lay-verify="required">
                                            <option value=''>请选择</option>
                                            <foreach name="education" item="vo" key="key">
                                                <option value="{$key}"
                                                <if (isset($history_data['id']) && $history_data['id']) >
                                                <in name="$key" value="$history_data.education">selected</in>
                                                </if>
                                                >{$vo}</option>
                                            </foreach>
                                        </select>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">毕业时间</label>
                                    <div class="layui-input-block">
                                        <input class="layui-input datepicker"
                                               lay-datetime=""
                                               name="graduation_date"
                                               data-datetype="date"
                                               data-dateformat="yyyy-MM-dd"
                                               placeholder="yyyy-MM-dd"
                                               data-maxvalue="9999-12-31"
                                               data-minvalue="1900-01-01"
                                               data-range=""
                                               value="{$history_data.graduation_date}">
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">毕业院校</label>
                                    <div class="layui-input-block"><input class="layui-input" name="school"
                                                                          placeholder="请输入" lay-verify=""
                                                                          value="{$history_data.school}"></div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">所在专业</label>
                                    <div class="layui-input-block"><input class="layui-input" name="major"
                                                                          placeholder="请输入" lay-verify=""
                                                                          value="{$history_data.major}"></div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">兵役情况</label>
                                    <div class="layui-input-block">
                                        <select name="enlistment">
                                            <option value=''>请选择</option>
                                            <foreach name="enlistment" item="vo" key="key">
                                                <option value="{$key}"
                                                <if (isset($history_data['id']) && $history_data['id']) >
                                                <in name="$key" value="$history_data.enlistment">selected</in>
                                                </if>
                                                >{$vo}</option>
                                            </foreach>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-col-md12 layui-grid-2" data-index="2">
                                <div class="layui-form-item">
                                    <label class="layui-form-label"><font color="red">* </font>政治面貌</label>
                                    <div class="layui-input-block">
                                        <select name="face" lay-verify="required">
                                            <option value=''>请选择</option>
                                            <foreach name="face" item="vo" key="key">
                                                <option value="{$key}"
                                                <if (isset($history_data['id']) && $history_data['id']) >
                                                <in name="$key" value="$history_data.face">selected</in>
                                                </if>
                                                >{$vo}</option>
                                            </foreach>
                                        </select>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label"><font color="red">* </font>家庭住址</label>
                                    <div class="layui-input-block"><input class="layui-input" name="address"
                                                                          placeholder="请输入" lay-verify="required"
                                                                          value="{$history_data.address}"></div>
                                </div>
                                <neq name="type" value="2">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label"><font color="red">* </font>人口类型</label>
                                        <div class="layui-input-block">
                                            <select name="person_type" lay-verify="required">
                                                <option value=''>请选择</option>
                                                <foreach name="person_type" item="vo" key="key">
                                                    <option value="{$key}"
                                                    <if (isset($history_data['id']) && $history_data['id']) >
                                                    <in name="$key" value="$history_data.person_type">selected</in>
                                                    </if>
                                                    >{$vo}</option>
                                                </foreach>
                                                <eq name="type" value="1">
                                                    <option value='4'
                                                    <if (isset($history_data['id']) && $history_data['id']) >
                                                    <in name="$key" value="$history_data.person_type">selected</in>
                                                    </if>
                                                    >委外人口</option>
                                                </eq>
                                            </select>
                                        </div>
                                    </div>
                                </neq>
                                <div class="layui-form-item">
                                    <label class="layui-form-label"><font color="red">* </font>住宅类型</label>
                                    <div class="layui-input-block">
                                        <select name="house_type" lay-verify="required">
                                            <option value=''>请选择</option>
                                            <foreach name="house_type" item="vo" key="key">
                                                <option value="{$key}"
                                                <if (isset($history_data['id']) && $history_data['id']) >
                                                <in name="$key" value="$history_data.house_type">selected</in>
                                                </if>
                                                >{$vo}</option>
                                            </foreach>
                                        </select>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">住宅面积</label>
                                    <div class="layui-input-block"><input class="layui-input" id="area" name="house_area"
                                                                          placeholder="请输入" readonly
                                                                          value="{$history_data.house_area}"></div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">健康状况</label>
                                    <div class="layui-input-block">
                                        <select name="health">
                                            <option value=''>请选择</option>
                                            <foreach name="health" item="vo" key="key">
                                                <option value="{$key}"
                                                <if (isset($history_data['id']) && $history_data['id']) >
                                                <in name="$key" value="$history_data.health">selected</in>
                                                </if>
                                                >{$vo}</option>
                                            </foreach>
                                        </select>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">血型</label>
                                    <div class="layui-input-block">
                                        <select name="blood">
                                            <option value=''>请选择</option>
                                            <foreach name="blood" item="vo" key="key">
                                                <option value="{$key}"
                                                <if (isset($history_data['id']) && $history_data['id']) >
                                                <in name="$key" value="$history_data.blood">selected</in>
                                                </if>
                                                >{$vo}</option>
                                            </foreach>
                                        </select>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label"><font color="red">* </font>劳动能力</label>
                                    <div class="layui-input-block">
                                        <select name="labor_capacity" lay-verify="required">
                                            <option value=''>请选择</option>
                                            <foreach name="labor_capacity" item="vo" key="key">
                                                <option value="{$key}"
                                                <if (isset($history_data['id']) && $history_data['id']) >
                                                <in name="$key" value="$history_data.labor_capacity">selected</in>
                                                </if>
                                                >{$vo}</option>
                                            </foreach>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-col-md12 layui-grid-3" data-index="3">
                                <div class="layui-form-item">
                                    <label class="layui-form-label" style="width:106px;"><font color="red">* </font>婚姻状况</label>
                                    <div class="layui-input-block" style="margin-left:136px">
                                        <select name="marry" lay-verify="required">
                                            <option value=''>请选择</option>
                                            <foreach name="marry" item="vo" key="key">
                                                <option value="{$key}"
                                                <if (isset($history_data['id']) && $history_data['id']) >
                                                <in name="$key" value="$history_data.marry">selected</in>
                                                </if>
                                                >{$vo}</option>
                                            </foreach>
                                        </select>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label" style="width:106px;">独生子女家庭</label>
                                    <div class="layui-input-block" style="margin-left:136px">
                                        <select name="only_child">
                                            <option value=''>请选择</option>
                                            <foreach name="only_child" item="vo" key="key">
                                                <option value="{$key}"
                                                <if (isset($history_data['id']) && $history_data['id']) >
                                                <in name="$key" value="$history_data.only_child">selected</in>
                                                </if>
                                                >{$vo}</option>
                                            </foreach>
                                        </select>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label" style="width:106px;">其他子女家庭</label>
                                    <div class="layui-input-block" style="margin-left:136px">
                                        <select name="other_child">
                                            <option value=''>请选择</option>
                                            <foreach name="other_child" item="vo" key="key">
                                                <option value="{$key}"
                                                <if (isset($history_data['id']) && $history_data['id']) >
                                                <in name="$key" value="$history_data.other_child">selected</in>
                                                </if>
                                                >{$vo}</option>
                                            </foreach>
                                        </select>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label" style="width:106px;"><font color="red">* </font>联系方式</label>
                                    <div class="layui-input-block" style="margin-left:136px"><input class="layui-input"
                                                                                                    name="phone"
                                                                                                    placeholder="请输入"
                                                                                                    lay-verify="required"
                                                                                                    value="{$history_data.phone}">
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label" style="width:106px;">联系方式1</label>
                                    <div class="layui-input-block" style="margin-left:136px"><input class="layui-input"
                                                                                                    name="phone_1"
                                                                                                    placeholder="请输入"
                                                                                                    lay-verify=""
                                                                                                    value="{$history_data.phone_1}">
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label" style="width:106px;">联系方式2</label>
                                    <div class="layui-input-block" style="margin-left:136px"><input class="layui-input"
                                                                                                    name="phone_2"
                                                                                                    placeholder="请输入"
                                                                                                    lay-verify=""
                                                                                                    value="{$history_data.phone_2}">
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label" style="width:106px;">紧急联系手机</label>
                                    <div class="layui-input-block" style="margin-left:136px"><input class="layui-input"
                                                                                                    name="urgent_phone"
                                                                                                    placeholder="请输入"
                                                                                                    lay-verify=""
                                                                                                    value="{$history_data.urgent_phone}">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="layui-form-item layui-row">
                            <div class="layui-col-md12 layui-grid-0" data-index="0">
                                <div class="layui-form-item">
                                    <label class="layui-form-label">是否残疾</label>
                                    <div class="layui-input-block">
                                        <input type="checkbox"  name="disability" lay-filter="tplChange" lay-skin="switch"
                                               value="1" title="是"
                                        <if (isset($history_data['disability']) && $history_data['disability'] == 1) >
                                        checked
                                        </if>
                                        >
                                    </div>
                                </div>
                                <eq name="history_data.disability" value="1">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">残疾类型</label>
                                        <div class="layui-input-block">
                                            <select name="disability_type">
                                                <foreach name="disabilityType" item="vo" key="key">
                                                    <option value="{$key}"
                                                    <if (isset($history_data['id']) && $history_data['id']) >
                                                    <in name="$key" value="$history_data.disability_type">selected</in>
                                                    </if>
                                                    >{$vo}</option>
                                                </foreach>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">残疾等级</label>
                                        <div class="layui-input-block">
                                            <select name="disability_level">
                                                <foreach name="disabilityLevel" item="vo" key="key">
                                                    <option value="{$key}"
                                                    <if (isset($history_data['id']) && $history_data['id']) >
                                                    <in name="$key" value="$history_data.disability_level">selected</in>
                                                    </if>
                                                    >{$vo}</option>
                                                </foreach>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">残疾证号码</label>
                                        <div class="layui-input-block">
                                            <input class="layui-input"
                                                   name="disability_code"
                                                   placeholder="请输入"
                                                   value="{$history_data.disability_code}">
                                        </div>
                                    </div>
                                </eq>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">是否退休</label>
                                    <div class="layui-input-block">
                                        <input type="checkbox" id="retire" name="retire" lay-filter="tplChange" lay-skin="switch"
                                               value="1" title="是"
                                        <if (isset($history_data['retire']) && $history_data['retire'] == 1) >
                                        checked
                                        </if>
                                        >
                                    </div>
                                </div>
                                <eq name="history_data.retire" value="1">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">退休单位</label>
                                        <div class="layui-input-block">
                                            <input class="layui-input"
                                                   name="retire_unit"
                                                   placeholder="请输入"
                                                   value="{$history_data.retire_unit}">
                                        </div>
                                    </div>
                                </eq>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">是否死亡</label>
                                    <div class="layui-input-block">
                                        <input type="checkbox" id="dead" name="dead" lay-filter="tplChange" lay-skin="switch"
                                               value="1" title="是"
                                        <if (isset($history_data['dead']) && $history_data['dead'] == 1) >
                                        checked
                                        </if>
                                        >
                                    </div>
                                </div>
                                <eq name="history_data.dead">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">死亡日期</label>
                                        <div class="layui-input-block">
                                            <input class="layui-input datepicker"
                                                   lay-datetime="" name="dead_date"
                                                   data-datetype="date"
                                                   data-dateformat="yyyy-MM-dd"
                                                   placeholder="yyyy-MM-dd"
                                                   data-maxvalue="9999-12-31"
                                                   data-minvalue="1900-01-01"
                                                   data-range=""
                                                   value="{$history_data.dead_date}">
                                        </div>
                                    </div>
                                </eq>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">养老保险</label>
                                    <div class="layui-input-block">
                                        <select name="endowment_insurance">
                                            <option value=''>请选择</option>
                                            <foreach name="endowment_insurance" item="vo" key="key">
                                                <option value="{$key}"
                                                <if (isset($history_data['id']) && $history_data['id']) >
                                                <in name="$key" value="$history_data.endowment_insurance">selected</in>
                                                </if>
                                                >{$vo}</option>
                                            </foreach>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-col-md12 layui-grid-1" data-index="1">
                                <div class="layui-form-item">
                                    <label class="layui-form-label" style="width:60px;">医保</label>
                                    <div class="layui-input-block" style="margin-left:90px">
                                        <foreach name="insurance"  item="vo" key="key">
                                            <input type="checkbox"  name="{$key}" lay-skin="primary"
                                                   value="1" title="{$vo}"
                                            <if (isset($history_data[$key]) && $history_data[$key] == 1) >
                                                checked
                                            </if>
                                            >
                                        </foreach>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label" style="width:60px;">政务人群</label>
                                    <div class="layui-input-block" style="margin-left:90px">
                                        <foreach name="representative"  item="vo" key="key">
                                            <php>
                                                $key = camelCaseToUnderscore($key);
                                            </php>
                                            <input type="checkbox"  name="{$key}" lay-skin="primary"
                                                   value="1" title="{$vo}"
                                            <if (isset($history_data[$key]) && $history_data[$key] == 1) >
                                                checked
                                            </if>
                                            >
                                        </foreach>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-col-md12 layui-grid-2" data-index="2">
                                <div class="layui-form-item">
                                    <label class="layui-form-label" style="width:60px;">老人</label>
                                    <div class="layui-input-block" style="margin-left:90px">
                                        <foreach name="elderly"  item="vo" key="key">
                                            <php>
                                                $key = camelCaseToUnderscore($key);
                                            </php>
                                            <input type="checkbox"  name="{$key}" lay-skin="primary"
                                                   value="1" title="{$vo}"
                                            <if (isset($history_data[$key]) && $history_data[$key] == 1) >
                                                checked
                                            </if>
                                            >
                                        </foreach>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label" style="width:60px;">困境人群</label>
                                    <div class="layui-input-block" style="margin-left:90px">
                                        <foreach name="dilemma"  item="vo" key="key">
                                            <php>
                                                $key = camelCaseToUnderscore($key);
                                            </php>
                                            <input type="checkbox"  name="{$key}" lay-skin="primary"
                                                   value="1" title="{$vo}"
                                            <if (isset($history_data[$key]) && $history_data[$key] == 1) >
                                                checked
                                            </if>
                                            >
                                        </foreach>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label" style="width:60px;">困境儿童</label>
                                    <div class="layui-input-block" style="margin-left:90px">
                                        <foreach name="dilemma_children"  item="vo" key="key">
                                            <php>
                                                $key = camelCaseToUnderscore($key);
                                            </php>
                                            <input type="checkbox"  name="{$key}" lay-skin="primary"
                                                   value="1" title="{$vo}"
                                            <if (isset($history_data[$key]) && $history_data[$key] == 1) >
                                                checked
                                            </if>
                                            >
                                        </foreach>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label" style="width:60px;">病人</label>
                                    <div class="layui-input-block" style="margin-left:90px">
                                        <foreach name="patient"  item="vo" key="key">
                                            <php>
                                                $key = camelCaseToUnderscore($key);
                                            </php>
                                            <input type="checkbox"  name="{$key}" lay-skin="primary"
                                                   value="1" title="{$vo}"
                                            <if (isset($history_data[$key]) && $history_data[$key] == 1) >
                                                checked
                                            </if>
                                            >
                                        </foreach>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-col-md12 layui-grid-3" data-index="3">
                                <div class="layui-form-item">
                                    <label class="layui-form-label" style="width:60px;">劣迹人群</label>
                                    <div class="layui-input-block" style="margin-left:90px">
                                        <foreach name="bad"  item="vo" key="key">
                                            <php>
                                                $key = camelCaseToUnderscore($key);
                                            </php>
                                            <input type="checkbox"  name="{$key}" lay-skin="primary"
                                                   value="1" title="{$vo}"
                                            <if (isset($history_data[$key]) && $history_data[$key] == 1) >
                                                checked
                                            </if>
                                            >
                                        </foreach>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label" style="width:60px;">其他人群</label>
                                    <div class="layui-input-block" style="margin-left:90px">
                                        <foreach name="other"  item="vo" key="key">
                                            <php>
                                                $key = camelCaseToUnderscore($key);
                                            </php>
                                            <input type="checkbox"  name="{$key}" lay-skin="primary"
                                                   value="1" title="{$vo}"
                                            <if (isset($history_data[$key]) && $history_data[$key] == 1) >
                                                checked
                                            </if>
                                            >
                                        </foreach>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layui-footer layui-form-footer">
                <button class="layui-btn layui-btn-primary" type="button" sa-event="closeDialog">{:__('取消')}</button>
                <button class="layui-btn" type="button" lay-filter="submitIframe" lay-submit>{:__('提交')}</button>
            </div>
    <neq name="action" value="detail">
    </form>
        <else/>
        </div>
    </neq>
</div>
<include file="/public/static"/>
<include file="/public/footer"/>
<include file="/public/select"/>

<script>
    layui.use(['layer','form','laydate','table'], function () {
        var form = layui.form;
        var $ = layui.jquery;
        convertFormToText($,form)
    });
</script>