<include file="/public/header" />
<include file="/person/param" />
<div class="layui-fluid">
    <div class="layui-card">
        <!-- // 默认操作按钮 -->
        <div class="layui-card-header layadmin-card-header-auto ">
            <div class="layui-form">
                <div class="layui-form-item">
                    <include file="/public/search" />
                    <div class="layui-inline">
                        <div class="layui-input-inline ">
                            <input name="keyword" class="layui-input" type="text" placeholder="{:__('按姓名或身份证号或家庭地址查询')}"/>
                        </div>
                    </div>
                    <div class="layui-inline" >
                        <!-- // 默认搜索 -->
                        <button id="search" class="layui-btn icon-btn" lay-filter="formSearch" lay-submit><i
                                class="layui-icon layui-icon-search"></i>{:__('搜索')}
                        </button>
                        <!--formBegin-->
                        <button class="layui-btn more">展开</button>
                        <!--formEnd-->
                    </div>
                </div>
                <!-- // 自定义搜索参数 -->
                <div id="laytable-search" class="layui-form-item">
                    <include file="/public/search_road" />
                    <div class="layui-inline">
                        <div class="layui-form-label  short-label">{:__('实有建筑')}</div>
                        <div class="layui-input-inline">
                            <select id="building" name="building_id" lay-search  lay-filter="buildingChange">
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-form-label  short-label">{:__('单元')}</div>
                        <div class="layui-input-inline">
                            <select id="unit" name="unit" lay-filter="search">
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-form-label">养老保险</div>
                        <div class="layui-input-inline">
                            <select name="endowment_insurance"  lay-filter="search">
                                <option value="">全部</option>
                                <foreach name="endowment_insurance" item="vo" key="key">
                                    <option value="{$key+1}">{$vo}</option>
                                </foreach>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-form-label">户口类型</div>
                        <div class="layui-input-inline">
                            <select name="rpr"  lay-filter="search">
                                <option value="">全部</option>
                                <foreach name="rpr" item="vo" key="key">
                                    <option value="{$key}">{$vo}</option>
                                </foreach>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-form-label">人口类型</div>
                        <div class="layui-input-inline">
                            <select name="person_type"  lay-filter="search">
                                <option value="">全部</option>
                                <foreach name="person_type" item="vo" key="key">
                                    <option value="{$key}">{$vo}</option>
                                </foreach>
                                <option value='4'>委外人口</option>
                                <option value='-1'>流出人口</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-form-label">兵役状况</div>
                        <div class="layui-input-inline">
                            <select name="enlistment"  lay-filter="search">
                                <option value="">全部</option>
                                <foreach name="enlistment" item="vo" key="key">
                                    <option value="{$key}">{$vo}</option>
                                </foreach>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-form-label">政治面貌</div>
                        <div class="layui-input-inline">
                            <select name="face"  lay-filter="search">
                                <option value="">全部</option>
                                <foreach name="face" item="vo" key="key">
                                    <option value="{$key}">{$vo}</option>
                                </foreach>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-form-label">文化程度</div>
                        <div class="layui-input-inline">
                            <select name="education"  lay-filter="search">
                                <option value="">全部</option>
                                <foreach name="education" item="vo" key="key">
                                    <option value="{$key}">{$vo}</option>
                                </foreach>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-form-label">民族</div>
                        <div class="layui-input-inline">
                            <select name="nation"  lay-filter="search">
                                <option value="">全部</option>
                                <foreach name="nation" item="vo" key="key">
                                    <option value="{$key}">{$vo}</option>
                                </foreach>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-form-label">性别</div>
                        <div class="layui-input-inline">
                            <select name="sex"  lay-filter="search">
                                <option value="">全部</option>
                                <foreach name="sex" item="vo" key="key">
                                    <option value="{$key}">{$vo}</option>
                                </foreach>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-form-label">婚姻状况</div>
                        <div class="layui-input-inline">
                            <select name="marry"  lay-filter="search">
                                <option value="">全部</option>
                                <foreach name="marry" item="vo" key="key">
                                    <option value="{$key}">{$vo}</option>
                                </foreach>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-form-label">独生子女</div>
                        <div class="layui-input-inline">
                            <select name="only_child"  lay-filter="search">
                                <option value="">全部</option>
                                <foreach name="only_child" item="vo" key="key">
                                    <option value="{$key}">{$vo}</option>
                                </foreach>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-form-label">子女家庭</div>
                        <div class="layui-input-inline">
                            <select name="other_child"  lay-filter="search">
                                <option value="">全部</option>
                                <foreach name="other_child" item="vo" key="key">
                                    <option value="{$key}">{$vo}</option>
                                </foreach>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-form-label">健康状况</div>
                        <div class="layui-input-inline">
                            <select name="health"  lay-filter="search">
                                <option value="">全部</option>
                                <foreach name="health" item="vo" key="key">
                                    <option value="{$key}">{$vo}</option>
                                </foreach>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-form-label">劳动能力</div>
                        <div class="layui-input-inline">
                            <select name="labor_capacity"  lay-filter="search">
                                <option value="">全部</option>
                                <foreach name="labor_capacity" item="vo" key="key">
                                    <option value="{$key}">{$vo}</option>
                                </foreach>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-form-label">宗教</div>
                        <div class="layui-input-inline">
                            <select name="religion"  lay-filter="search">
                                <option value="">全部</option>
                                <foreach name="religion" item="vo" key="key">
                                    <option value="{$key}">{$vo}</option>
                                </foreach>
                            </select>
                        </div>
                    </div>
                    <div class="clear"></div>
                    <div class="layui-inline">
                        <div class="layui-form-label">出生年月日</div>
                        <div class="layui-input-inline" style="width: 236px;">
                            <div class="layui-input-inline" style="width: 100px;">
                                <input class="layui-input" lay-datetime="" name="birth_start"
                                       data-datetype="date" data-dateformat="yyyy-MM-dd"
                                       placeholder="yyyy-MM-dd">
                            </div>
                            <div class="layui-form-mid">-</div>
                            <div class="layui-input-inline" style="width: 100px;">
                                <input class="layui-input" lay-datetime="" name="birth_end"
                                       data-datetype="date" data-dateformat="yyyy-MM-dd"
                                       placeholder="yyyy-MM-dd">
                            </div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-form-label">年龄</div>
                        <div class="layui-input-inline" style="width: 80px;">
                            <input type="number" name="age_start" autocomplete="off" class="layui-input">
                        </div>
                        <div class="layui-form-mid">-</div>
                        <div class="layui-input-inline" style="width: 80px;">
                            <input type="number" name="age_end" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-card" style="border: 1px solid #e3e3e3;">
                        <div class="layui-card-header">特殊人群</div>
                        <div class="layui-card-body">
                            <input type="checkbox" name="consistency" value="1" title="人户一致" lay-filter="search">
                            <input type="checkbox" name="retire" value="1" title="退休" lay-filter="search">
                            <foreach name="insurance"  item="vo" key="key">
                                <input type="checkbox"  name="{$key}"  lay-filter="search"
                                       value="1" title="{$vo}">
                            </foreach>
                            <foreach name="representative"  item="vo" key="key">
                                <php>
                                    $key = camelCaseToUnderscore($key);
                                </php>
                                <input type="checkbox"  name="{$key}"  lay-filter="search"
                                       value="1" title="{$vo}">
                            </foreach>
                            <foreach name="elderly"  item="vo" key="key">
                                <php>
                                    $key = camelCaseToUnderscore($key);
                                </php>
                                <input type="checkbox"  name="{$key}"  lay-filter="search"
                                       value="1" title="{$vo}">
                            </foreach>
                            <foreach name="dilemma"  item="vo" key="key">
                                <php>
                                    $key = camelCaseToUnderscore($key);
                                </php>
                                <input type="checkbox"  name="{$key}"  lay-filter="search"
                                       value="1" title="{$vo}">
                            </foreach>
                            <foreach name="dilemma_children"  item="vo" key="key">
                                <php>
                                    $key = camelCaseToUnderscore($key);
                                </php>
                                <input type="checkbox"  name="{$key}"  lay-filter="search"
                                       value="1" title="{$vo}">
                            </foreach>
                            <foreach name="patient"  item="vo" key="key">
                                <php>
                                    $key = camelCaseToUnderscore($key);
                                </php>
                                <input type="checkbox"  name="{$key}"  lay-filter="search"
                                       value="1" title="{$vo}">
                            </foreach>
                            <foreach name="bad"  item="vo" key="key">
                                <php>
                                    $key = camelCaseToUnderscore($key);
                                </php>
                                <input type="checkbox"  name="{$key}"  lay-filter="search"
                                       value="1" title="{$vo}">
                            </foreach>
                            <foreach name="other"  item="vo" key="key">
                                <php>
                                    $key = camelCaseToUnderscore($key);
                                </php>
                                <input type="checkbox"  name="{$key}"  lay-filter="search"
                                       value="1" title="{$vo}">
                            </foreach>
                            <input type="checkbox" name="disability" value="1" title="残疾" lay-filter="search">
                            <input type="checkbox" name="dead" value="1" title="死亡" lay-filter="search">

                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- // 创建数据实例 -->
        <table id="lay-tableList" lay-filter="lay-tableList"></table>
    </div>
</div>

<script type="text/html" id="tableButton">
    <div class="layui-btn-container">
        <button class="layui-btn icon-btn" lay-open="" data-title="{:__('添加')}" data-area="100%,100%"
                data-maxmin="true" data-url="{:url('/'.$controller.'/add')}?rpr_nature=1">
            <i class="layui-icon layui-icon-add-1"></i>{:__('添加')}
        </button>
        <button class="layui-btn icon-btn" data-url="/admin/PersonHousehold/dataToExcel" lay-filter="formExport" lay-submit><i class="layui-icon layui-icon-export"></i>{:__('导出')}</button>
        <button id="formExport" class="layui-btn" data-url="{:url('PersonHousehold/dataToExcel')}" lay-event="formExport">批量导出</button>
        <button class="layui-btn icon-btn" lay-open="" data-title="{:__('数据导入')}" data-area="750px,500px"
                data-maxmin="true" data-url="/admin/PersonHousehold/dataImport" >
            <i class="layui-icon layui-icon-import"></i>{:__('导入')}
        </button>
        <button id="delete-btn" class="layui-btn layui-btn-danger" data-url="{:url('Pending/del')}" lay-event="delete">批量删除</button>
        <button class="layui-btn icon-btn" lay-open="" data-title="{:__('问题数据')}" data-area="100%,100%"
                data-maxmin="true" data-url="{:url('/'.$controller.'/proData')}?rpr_nature=1">
            {:__('问题数据')}
        </button>
    </div>
</script>
<!-- // 列表工具栏 -->
<script type="text/html" id="tableBar">
    <!--formBegin-->
    <a class="layui-table-text" data-title="{:__('查看')}" data-area="100%,100%" data-maxmin="true"
       data-url="{:url('/'.$controller.'/detail')}?id={{d.id}}" lay-event="edit" >{:__('查看')}</a>
    <div class="layui-divider layui-divider-vertical"></div>
    <a class="layui-table-text" data-title="{:__('编辑')}" data-area="100%,100%" data-maxmin="true"
       data-url="{:url('/Person/edit')}?id={{d.id}}&rpr_nature=1" lay-event="edit" >{:__('编辑')}</a>
    <div class="layui-divider layui-divider-vertical"></div>
    <!--formEnd-->
    <a class="layui-table-text"  data-url="{:url('/Person/del')}?id={{d.id}}" lay-event="del" >{:__('删除')}</a>
</script>





<include file="/public/footer" />
<script>
    layui.use(['admin','table','form'], function () {

        var admin = layui.admin;
        var table = layui.table;
        var form = layui.form;

        /*
         * 初始化表格
        */
        var isTable = table.render({
            elem: "#lay-tableList"
            ,url: "{:url('/Person/household')}?rpr_nature=1"
            ,toolbar: '#tableButton'
            ,defaultToolbar: ['filter', 'exports', 'print','search']
            ,cellMinWidth: 60
            ,page: true
            ,limit: 50,limits: [50, 100, 200, 500]
            ,height: 'full-150' //设置表格高度，减去固定的底部高度
            ,cols: [[
                {type: 'checkbox', width: 50},
                {field:'name',align: 'left',width:180,title:'{:__("姓名")}', templet: function(d){
                        return d.name;
                    }},
                {field:'id_code',title:'{:__("年龄")}',width:50,templet:function(d){
                        return d.age
                    }},
                {field:'sex',title:'{:__("性别")}',width:50,templet:function(d){
                        return d.sex==1?'男':'女'
                    }},
                {field: 'community_id',width: 320, minWidth: 300, title: '{:__("所在区域")}',templet:function(d){
                        return d.community_id>0?d.community_name+","+d.grid_group_name+","+d.grid_name:''
                    }},
                {field: 'building_name',width:160, title: '{:__("所在房屋")}'},
                {field:'address',width:200,title:'{:__("房屋地址")}'},
                {field:'id_code',width:180,title:'{:__("身份证号")}'},
                {field:'phone',width:120,title:'{:__("联系方式")}'},
                {field:'rpr_address',width:200,title:'{:__("户籍地址")}'},
                {field:'nation',width:80,title:'{:__("民族")}',templet:function(d){
                        var resData = {:json_encode($nation)};
                        return d.nation > 0 ?resData[d.nation]:''
                    }},
                {field:'face',width:80,title:'{:__("政治面貌")}',templet:function(d){
                        var resData = {:json_encode($face)};
                        return d.face > 0 ?resData[d.face]:''
                    }},
                {field:'education',width:80,title:'{:__("学历")}',templet:function(d){
                        var resData = {:json_encode($education)};
                        return d.education > 0 ?resData[d.education]:''
                    }},
                {field: 'create_by',width: 120,title:'{:__("创建者")}'},
                {field: 'create_time',width: 160,title:'{:__("创建时间")}'},
                {field: 'update_by',width: 120,title:'{:__("更新者")}'},
                {field: 'update_time',width: 160,title:'{:__("更新时间")}'},
                {align: 'center', toolbar: '#tableBar', width:200, fixed: 'right', title: '{:__("操作")}'},
            ]]
        })
    })
</script>
<include file="/public/select" />
