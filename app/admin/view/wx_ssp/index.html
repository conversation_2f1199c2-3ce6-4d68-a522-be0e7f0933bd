<include file="/public/header" />
<php>
    $build_types = getDictionary(13);
    $event = getDictionary(610);
</php>
<!---->
<div class="layui-fluid">
    <div class="layui-card">
        <!-- // 默认操作按钮 -->
        <div class="layui-card-header layadmin-card-header-auto ">
            <div class="layui-form">
                <!-- // 自定义搜索参数 -->
                <!-- <include file="/public/search"/> -->
                    <div class="layui-inline" >
                        <!-- // 默认搜索 -->
                        <div class="layui-inline">
                            <div class="layui-form-label">{:__('状态')}</div>
                            <div class="layui-input-inline ">
                            <!--<input name="status" class="layui-input" type="text" placeholder="{:__('状态')}"/>-->
                            <select name="status" id="">
                                <option value="">请选择</option>
                                <option value="未处理">未处理</option>
                                <option value="已处理">已处理</option>
                                <option value="已挂起">已挂起</option>
                                <option value="已驳回">已驳回</option>
                            </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <div class="layui-form-label">{:__('事件类型')}</div>
                            <div class="layui-input-inline ">
                            <!--<input name="event" class="layui-input" type="text" placeholder="{:__('事件类型')}"/>-->
                            <select name="event" lay-verify="" lay-filter="">
                                
                                    <option value=''>请选择</option>
                             
                                
                                <foreach name="event" item="vo" >
                                    
                                     <option value="{$vo}">{$vo}</option>
                                    
                                    
            
                                </foreach>
                            </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <div class="layui-form-label">{:__('评价')}</div>
                            <div class="layui-input-inline ">
                            <!--<input name="rate" class="layui-input" type="text" placeholder="{:__('评价')}"/>-->
                            <select name="rate" id="">
                                <option value="">请选择</option>
                                <option value="好评">好评</option>
                                <option value="一般">一般</option>
                                <option value="差评">差评</option>
                            </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <div class="layui-form-label">{:__('开始时间')}</div>
                            <div class="layui-input-inline ">
                            <!--<input name="begin" class="layui-input" type="text" placeholder="{:__('开始时间')}"/>-->
                                <input type="text" name="begin" class="layui-input" lay-datetime placeholder="yyyy-MM-dd">
                            
                            </div>
                        </div>
                        <div class="layui-inline">
                            <div class="layui-form-label">{:__('结束时间')}</div>
                            <div class="layui-input-inline ">
                            <!--<input name="over" class="layui-input" type="text" placeholder="{:__('结束时间')}"/>-->
                            <input type="text" name="over" class="layui-input" lay-datetime placeholder="yyyy-MM-dd">
                            </div>
                        </div>
                        <button class="layui-btn icon-btn" lay-filter="formSearch" lay-submit><i class="layui-icon layui-icon-search"></i>{:__('搜索')}</button>
                        <!--formBegin-->
                        <button class="layui-btn icon-btn" lay-open="" data-title="{:__('添加')}" data-area="1100px,750px" data-maxmin="true" data-url="{:url('/WxSsp/add')}" >
                            <i class="layui-icon layui-icon-add-1"></i>{:__('添加')}
                        </button>
                        <!--formEnd-->
                    </div>
                <!-- </div> -->
            <!-- </div>   -->
        </div>
        <!-- // 创建数据实例 -->
        <table id="lay-tableList" lay-filter="lay-tableList"></table>        
    </div>
</div>

<!-- // 列表状态栏 -->
<script type="text/html" id="columnStatus">
    <input type="checkbox" lay-filter="switchStatus" data-url="{:url('/WxSsp/status')}" value="{{d.id}}" lay-skin="switch" {{d.status==1?'checked':''}}  />
</script>
 
<!-- // 列表工具栏 -->
<script type="text/html" id="tableBar">
    <!--formBegin-->
    <a class="layui-table-text" data-title="{:__('编辑')}" data-area="1100px,750px" data-maxmin="true"
        data-url="{:url('/WxSsp/edit')}?id={{d.id}}" lay-event="edit" >{:__('编辑')}</a>
    <div class="layui-divider layui-divider-vertical"></div>
    <!--formEnd-->
    <a class="layui-table-text"  data-url="{:url('/WxSsp/del')}?id={{d.id}}" lay-event="del" >{:__('删除')}</a>
</script>



<script type="text/html" id="tableButton"></script>

<include file="/public/footer" />
<script>
    layui.use(['admin','table'], function () {

        var admin = layui.admin;
        var table = layui.table;

        /*
         * 初始化表格
        */
        var isTable = table.render({
            elem: "#lay-tableList"
            ,url: "{:url('/WxSsp/index')}"
            ,toolbar: '#tableButton'
            ,defaultToolbar: ['filter', 'exports', 'print','search']
            ,cellMinWidth: 160
            ,page: true
            ,limit: 18
            ,cols: [[
                {type: 'checkbox', width: 50},
                {field: 'id', align: 'center',sort: true,width: 80, title: 'ID'},
                // {type: 'checkbox', width: 50},
                {field: 'community_name', title: '{:__("所在社区")}'},
                {field: 'grid_group_name', title: '{:__("所在网格组")}'},
                {field: 'grid_name', title: '{:__("所在网格")}'},
                // {field:'id',title:'{:__("id")}'},
{
                    field: 'image', templet: function (d) {
                        if(d.image==''){
                            return '';
                        }
                        var album = [];
                        try {
                            d.image = JSON.parse(d.image);
                            for (var i in d.image) {
                                album[i] = '<a href="javascript:" class="fileslink" ><img class="filesuffix" lay-image-click src="' + d.image[i].src + '"></a>'
                            }
                            return album.join('');
                        } catch (e) {
                            return '<a href="javascript:" class="fileslink" ><img class="filesuffix" lay-image-click src="' + d.image + '"></a>'
                        }
                    }, title: '{:__("随手拍")}'
                },
{field:'msg',title:'{:__("描述信息")}'},
{field:'mobile',title:'{:__("联系方式")}'},
{field:'status_res',title:'{:__("状态")}'},
{field:'rate',title:'{:__("评价")}'},
{field:'event',title:'{:__("事件类型")}'},
{field:'user_id',title:'{:__("用户id")}'},
{field:'create_time',title:'{:__("创造时间")}'},
{field:'create_by',title:'{:__("创建人")}'},
{field:'update_time',title:'{:__("修改时间")}'},
{field:'update_by',title:'{:__("修改人")}'},
                {align: 'center', toolbar: '#tableBar', width:160, fixed: 'right', title: '{:__("操作")}'},
            ]]
        })

    })
</script>
<script>
    layui.use(['layer'], function () {
        var $ = layui.jquery;
        var layer = layui.layer;


    });
</script>
<include file="/public/select"/>