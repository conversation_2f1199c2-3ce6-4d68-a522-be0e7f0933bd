<include file="/public/header" />

<php>

    $Communities = getCommunity();

    if($Communities[0]['id']==1){

    unset($Communities[0]);

    $Communities = array_values($Communities);

    }

    $community_id = empty($data['community_id'])?$Communities[0]['id']:$data['community_id'];

    $GridGroups = getGridGroup($community_id);

    $grid_group_id = empty($data['grid_group_id'])?$GridGroups[0]['id']:$data['grid_group_id'];

    $Grids = getGrid($grid_group_id);

    $grid_id = empty($data['grid_id'])?$Grids[0]['id']:$data['grid_id'];

    

    $xf_Communities = getCommunity();

    if($xf_Communities[0]['id']==1){

    unset($xf_Communities[0]);

    $xf_Communities = array_values($xf_Communities);

    }

    $xf_community_id = empty($data['xf_community_id'])?$xf_Communities[0]['id']:$data['xf_community_id'];

    $xf_GridGroups = getGridGroup($xf_community_id);

    $xf_grid_group_id = empty($data['xf_grid_group_id'])?$xf_GridGroups[0]['id']:$data['xf_grid_group_id'];

    $xf_Grids = getGrid($xf_grid_group_id);

    $xf_grid_id = empty($data['xf_grid_id'])?$xf_Grids[0]['id']:$data['xf_grid_id'];

    



    $Buildings = getBuilding($grid_id);

    $ids = [53,349,355,362,367,373,610];

    $dictionaryGroup = getDictionaryGroup($ids);

    $property_right = $dictionaryGroup[349];

    $usage_category = $dictionaryGroup[355];

    $house_type = $dictionaryGroup[362];

    $residence_nature = $dictionaryGroup[367];

    $rent_used = $dictionaryGroup[373];

    $relation = $dictionaryGroup[53];



    $event = $dictionaryGroup[610];

</php>



<!-- // 重定位style -->

<!---->

<link href="__STATICADMIN__css/content.css" rel="stylesheet" type="text/css" />

<div class="layui-fluid">

    <form class="layui-form layui-card" >



        <div class="layui-card-body">

        <gt name="$data.id" value="0">

            <input type="text" name="id" value="{$data.id}" hidden="">

            <else/>

            <input type="text" name="id" value="" hidden="">

        </gt>

        <div class="layui-form-item">

            <label class="layui-form-label"><font color="red">* </font>事件分类</label>

            <div class="layui-input-block">

                <select name="event" lay-verify="required" lay-filter="ssptypeChange">

                    <if empty($data['id']) >

                        <option value=''>请选择</option>

                    </if>

                    <!-- <option value=''>请选择</option> -->

                    <if (isset($data['id']) && $data['event']) >

                        <option value="{$data.event}">{$data.event}</option>

                        </if>

                    <foreach name="event" item="vo" >

                        

                         <option value="{$vo}">{$vo}</option>

                        

                        



                    </foreach>

                </select>



            </div>

        </div>

        <div class="layui-form-item">

            <label class="layui-form-label">下发部门</label>

            <div class="layui-input-block">

                <select name="xf_department"  id="xiafa" lay-filter="xiafaChange">

                        <if empty($data['xf_department']) >

                            <option value=''>请选择</option>

                        </if>

                    <foreach name="$data['department']" item="vo">



                        <option value="{$vo.name}"

                        <if (isset($data['id']) && $data['xf_department']) >

                        <in name="$vo.id" value="$data.xf_department">selected</in>

                        </if>

                        

                        >{$vo.name}</option>

                    </foreach>

                </select>

                

            </div>

        </div>

        
<!--
        <div class="layui-form-item">

            <label class="layui-form-label">下发社区</label>

            <div class="layui-input-block">

                <select id="xfcommunity_id"  name="xf_community_id" lay-search lay-filter="xfcommunityChange" lay-verify="">

                <if empty($data['xf_community_id']) >

                    <option value=''>请选择</option>

                </if>

                    <foreach name="xf_Communities" item="vo">

                        <option value="{$vo.id}"

                        <if (isset($data['id']) && $data['xf_community_id']) >

                        <in name="$vo.id" value="$data.xf_community_id">selected</in>

                        </if>

                        >{$vo.community_name}</option>

                    </foreach>

                </select>

            </div>

        </div>

                    <div class="layui-form-item">

                        <label class="layui-form-label">下发网格组</label>

                        <div class="layui-input-block">

                            <select id="xfgrid_group" name="xf_grid_group_id" lay-search lay-filter="xfgridGroupChange" lay-verify="">

                            <if empty($data['xf_grid_group_id']) >

                                <option value=''>请选择</option>

                            </if>

                                <foreach name="xf_GridGroups" item="vo">

                                    <option value="{$vo.id}"

                                    <if (isset($data['id']) && $data['xf_grid_group_id']) >

                                    <in name="$vo.id" value="$data.xf_grid_group_id">selected</in>

                                    </if>

                                    >{$vo.grid_name}</option>

                                </foreach>

                            </select>

                        </div>

                    </div>

                    <div class="layui-form-item">

                        <label class="layui-form-label">下发网格</label>

                        <div class="layui-input-block">

                            <select id="xfgrid" name="xf_grid_id" lay-search lay-filter="xfgridChange" lay-verify="">

                            <if empty($data['xf_grid_id']) >

                                <option value=''>请选择</option>

                            </if>

                                <foreach name="xf_Grids" item="vo">

                                    <option value="{$vo.id}"

                                    <if (isset($data['id']) && $data['xf_grid_id']) >

                                    <in name="$vo.id" value="$data.xf_grid_id">selected</in>

                                    </if>

                                    >{$vo.grid_name}</option>

                                </foreach>

                            </select>

                        </div>

                    </div>
-->
        <div class="layui-form-item">

                        <label class="layui-form-label">所属社区</label>

                        <div class="layui-input-block">

                            <select  name="community_id" lay-search lay-filter="communityChange" lay-verify="required">

                                <foreach name="Communities" item="vo">

                                    <option value="{$vo.id}"

                                    <if (isset($data['id']) && $data['id']) >

                                    <in name="$vo.id" value="$data.community_id">selected</in>

                                    </if>

                                    >{$vo.community_name}</option>

                                </foreach>

                            </select>

                        </div>

                    </div>

                    <div class="layui-form-item">

                        <label class="layui-form-label">所属网格组</label>

                        <div class="layui-input-block">

                            <select id="grid_group" name="grid_group_id" lay-search lay-filter="gridGroupChange" lay-verify="">

                                <foreach name="GridGroups" item="vo">

                                    <option value="{$vo.id}"

                                    <if (isset($data['id']) && $data['id']) >

                                    <in name="$vo.id" value="$data.grid_group_id">selected</in>

                                    </if>

                                    >{$vo.grid_name}</option>

                                </foreach>

                            </select>

                        </div>

                    </div>

                    <div class="layui-form-item">

                        <label class="layui-form-label">所属网格</label>

                        <div class="layui-input-block">

                            <select id="grid" name="grid_id" lay-search lay-filter="gridChange" lay-verify="">

                                <foreach name="Grids" item="vo">

                                    <option value="{$vo.id}"

                                    <if (isset($data['id']) && $data['id']) >

                                    <in name="$vo.id" value="$data.grid_id">selected</in>

                                    </if>

                                    >{$vo.grid_name}</option>

                                </foreach>

                            </select>

                        </div>

                    </div>



<div class="layui-form-item" >

<label class="layui-form-label">图片路径</label>

<div class="layui-input-block">

            <div class="layui-imagesbox">



                <!-- // 循环输出代码 -->

                <notempty name="$data['image']" >

                <volist name="$data['image']" id="vo">

                    <div class="layui-input-inline layui-uplpad-image">

                        <img src="{$vo.src}" lay-image-hover >

                        <input type="text" name="image[{$key}][src]" class="layui-hide" value="{$vo.src}" >

                        <input type="text" name="image[{$key}][title]" class="layui-input" value="{$vo.title}" placeholder="图片简介">

                        <span class="layui-badge layui-badge-red" data-name="image" onclick="layui.admin.resetInput(this,'images');">删除</span>

                    </div>

                </volist>

                </notempty>

                <div class="layui-input-inline layui-uplpad-image">

                    <div class="layui-upload-drag" lay-upload="image" data-type="multiple" data-accept="file" data-size="102400">

                        <i class="layui-icon layui-icon-upload"></i>

                        <p>点击上传，或将文件拖拽到此处</p>

                        <div class="layui-hide"></div>

                    </div>

                    <button type="button" class="layui-btn layui-btn-xs layui-btn-fluid" lay-choose="image" data-name="image" data-type="multiple">

                        <i class="layui-icon layui-icon-windows"></i> 选择

                    </button>

                </div>

            </div></div>

</div>



<div class="layui-form-item" >

<label class="layui-form-label">问题详情</label>

<div class="layui-input-block"><textarea class="layui-textarea" name="msg" placeholder="请输入" >{$data.msg}</textarea></div>

</div>

<!--
<div class="layui-form-item" >

<label class="layui-form-label">反馈</label>

<div class="layui-input-block"><textarea class="layui-textarea" name="feedback" placeholder="请输入" >{$data.feedback}</textarea></div>

</div>





<div class="layui-form-item" >

    <label class="layui-form-label">评分</label>

    <div class="layui-input-block">    

        

    <select name="rate" lay-verify="">

                    <if empty($data['id']) >

                        <option value='请选择'>请选择</option>

                    </if>

                    <if $data['id'] && $data['rate'] >

                        <option value="{$data['rate']}">{$data['rate']}</option>

                    </if>

                    <option value=''>请选择</option>

                    <option value='好评'>好评</option>

                    <option value='一般'>一般</option>

                    <option value='差评'>差评</option>

                </select>

    </div>

</div>
-->


<!-- <div class="layui-form-item" >

<label class="layui-form-label">开关</label>

<div class="layui-input-block">    <input  type="hidden" type="checkbox" name="switch_4" value="0" />

    <input type="checkbox" name="switch_4" value="1" <eq name="$data.switch_4" value="1" > checked </eq> lay-skin="switch" /></div>

</div> -->

<div class="layui-form-item">

                        <label class="layui-form-label">状态</label>

                        <div class="layui-input-block">

                            <select id="status" name="status" lay-search lay-filter="status" lay-verify="required">

                                <if (isset($data['id']) && $data['id']) >

                                    <option value="{$data.status}">

                                        <if $data.status==0>

                                            未处理

                                        </if>

                                        <if $data.status==1>

                                            已处理

                                        </if>

                                        <if $data.status==-1>

                                            已挂起

                                        </if>

                                        <if $data.status==-2>

                                            已驳回

                                        </if>

                                        

                        </option>

                                </if>

                                    <option value="0">未处理</option>

                                    <option value="1">已处理</option>

                                    <option value="-1">已挂起</option>

                                    <option value="-2">已驳回</option>

                            </select>

                        </div>

                    </div>

        <div class="layui-footer layui-form-footer">

            <button class="layui-btn layui-btn-primary" type="button" sa-event="closeDialog">{:__('取消')}</button>

            <button class="layui-btn" type="button" lay-filter="submitIframe" lay-submit>{:__('提交')}</button>

        </div>

    </form>

</div>

<script>

        layui.use('table', function(){

            var table = layui.table;

            

            // 初始化表格

            table.render({

                elem: '#demoTable', // 绑定到表格容器

                url: '/path/to/your/data/source', // 数据源URL

                cols: [[

                    {field: 'id', title: 'ID', width: 80},

                    {field: 'username', title: 'Username', width: 120}

                ]],

                page: true // 开启分页

            });

        });

</script>

<include file="/public/static" />

<include file="/public/footer" />

<include file="/public/select"/>

