<include file="/public/header"/>

<link href="__STATICADMIN__css/content.css" rel="stylesheet" type="text/css"/>
<div class="layui-fluid">
    <form class="layui-form layui-card">

        <div class="layui-card-body">
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <input type="text" name="file_1" value=""
                           class="layui-input layui-input-upload file_1" readonly="1">
                    <button type="button" class="layui-btn" lay-upload="file_1" data-type="normal"
                            data-accept="file" >
                        <i class="layui-icon layui-icon-upload"></i> 上传
                    </button>
                    <a href="/admin/{$controller}/downloadExcelTpl"  class="layui-btn ml10">
                        <i class="layui-icon fa-download"></i> 模板下载
                    </a>
                </div>
            </div>
            <div class="layui-form-item" id="error_info">
            </div>
            <div class="layui-footer layui-form-footer">
                <button class="layui-btn layui-btn-primary" type="button" sa-event="closeDialog">{:__('取消')}</button>
                <button class="layui-btn" type="button" lay-filter="formImport" lay-submit>{:__('提交')}</button>
            </div>
        </div>
    </form>
</div>

<script id="certificate-tpl" type="text/html"></script>
<include file="/public/static"/>
<include file="/public/footer"/>
<include file="/public/select"/>
<script>
    layui.use(['admin','table','form','jquery'], function () {

        var form = layui.form;
        var $ = layui.jquery;
        var admin = layui.admin;
        var show = layui.show;

        form.on("submit(formImport)",function(post){
            let that = $(this), _form = that.parents('form'),
            _close = that.data("close") || undefined,
            _url = _form.attr("action") || false;
            let _parent = that.data('reload') || false;
            // 开始POST提交数据
            var index = layer.load(2, {shade: [0.5, '#393D49'], content: '导入中...'});
            $.ajax({
                type: 'POST',
                url: '/admin/{$controller}/dataImport',
                data: post.field,
                timeout: 600000, // 设置超时时间为5秒
                success: function(res) {
                    layer.close(index);
                    if (res.code === 200) {
                        $("#error_info").empty();
                        show.msg(res.msg);
                        setTimeout(function(){
                            if (_close === undefined) {
                                parent.layui.table.reloadData('lay-tableList');
                                admin.event.closeDialog(that);
                            }
                            if (_parent !== false && parent.window !== top) {
                                parent.location.reload();
                            } else {
                                if (parent.layui.table.cache['lay-tableList']) {
                                    parent.layui.table.reloadData('lay-tableList');
                                }
                            }
                        },2000)
                    }
                    else {
                        show.error(res.msg);
                        console.log(res);
                        var data = res.data.data;
                        var html = "<h3>"+res.msg+"</h3><table class=\"layui-table\" width='100%'><tr><th width='15%'>行数</th><th width='85%'>错误内容</th></tr>";
                        for (let key in data) {
                            html += "<tr><td>第"+key+"行</td><td>"+data[key]+"</td></tr>";
                        }
                        html += "</table>"
                        $("#error_info").html(html);
                    }
                },
                error: function(jqXHR, textStatus) {
                    layer.close(index);
                    if (textStatus === 'timeout') {
                        // 请求超时的回调函数
                        layui.show.error('请求超时');
                    } else {
                        // 其他错误情况的回调函数
                        layui.show.error('请求失败：', textStatus);
                    }
                }
            });
            return false;
        })
    })
</script>