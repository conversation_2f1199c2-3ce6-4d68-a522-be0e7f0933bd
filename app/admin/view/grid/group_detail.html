<include file="/public/header"/>
<style>
    .layui-form-item{ margin-bottom: 5px; margin-right: 10px;}
    .layui-form-item label{height: 24px;}
    .layui-form-item label,.layui-form-item .layui-input-block{border:1px solid #e3e3e3; border-radius: 2px; background: #fafafa;display: flex;}
    .layui-form-item .layui-input-block{margin-left: 111px;}
    .console-link-block {
        font-size: 16px;
        padding: 20px 20px;
        border-radius: 4px;
        background-color: #40D4B0;
        color: #FFFFFF !important;
        box-shadow: 0 2px 3px rgba(0, 0, 0, .05);
        position: relative;
        overflow: hidden;
        display: block;
    }

    .console-link-block .console-link-block-num {
        font-size: 40px;
        margin-bottom: 5px;
        opacity: .9;
    }

    .console-link-block .console-link-block-text {
        opacity: .8;
    }

    .console-link-block .console-link-block-icon {
        position: absolute;
        top: 50%;
        right: 20px;
        width: 50px;
        height: 50px;
        font-size: 50px;
        line-height: 50px;
        margin-top: -25px;
        color: #FFFFFF;
        opacity: .8;
    }

    .console-link-block .console-link-block-band {
        color: #fff;
        width: 100px;
        font-size: 12px;
        padding: 2px 0 3px 0;
        background-color: #E32A16;
        line-height: inherit;
        text-align: center;
        position: absolute;
        top: 8px;
        right: -30px;
        transform-origin: center;
        transform: rotate(45deg) scale(.8);
        opacity: .95;
        z-index: 2;
    }

    /** //统计快捷方式样式 */

    /** 设置每个快捷块的颜色 */
    .layui-col-space15 > div:nth-child(2) .console-link-block {
        background-color: #55A5EA;
    }

    .layui-col-space15 > div:nth-child(3) .console-link-block {
        background-color: #9DAFFF;
    }

    .layui-col-space15 > div:nth-child(4) .console-link-block {
        background-color: #F591A2;
    }

    .layui-col-space15 > div:nth-child(5) .console-link-block {
        background-color: #FEAA4F;
    }

    .layui-col-space15 > div:last-child .console-link-block {
        background-color: #9BC539;
    }
</style>
<php>
    $total = getTotal($data['id'],"grid_group_id");
    $place_merchant_type = getCateTree('place_merchant_type');
    $type = getDictionary(50);
</php>
<link href="__STATICADMIN__css/content.css" rel="stylesheet" type="text/css"/>
<div class="layui-fluid">
    <div class="layui-form layui-card">

        <div class="layui-card-body">
            <div id="layui-tab" id="tab_0" class="layui-tab layui-tab-brief">
                <ul class="layui-tab-title">
                    <li class="layui-this">基本信息</li>
                    <li class="tab-li" data-id="grid">网格</li>
                    <li class="tab-li" data-id="build">实有建筑</li>
                    <li class="tab-li" data-id="house">实有房屋</li>
                    <li  class="tab-li" data-id="place_merchant">个体工商户</li>
                    <li  class="tab-li" data-id="place_enterprise">企业</li>
                    <li  class="tab-li" data-id="place_government">机关及事业单位</li>
                    <li class="tab-li" data-id="person">实有人口</li>
                    <li class="">{$title}简介</li>
                    <li class="">工作职责</li>
                    <li class="tab-li" data-id="history">历史统计</li>
                </ul>
                <div class="layui-tab-content">
                    <div class="layui-tab-item layui-show " data-index="0">
                        <div class="layui-form-item layui-row">
                            <div class="layui-col-md4 layui-grid-0" data-index="0">
                                <div class="layui-form-item">
                                    <div class="layui-input-block" style="margin-left:0">
                                        <div class="layui-imagesbox">
                                            <php>
                                                if (strpos($data['image'], 'http') === 0) {
                                                $data['image'] = [['src'=>$data['image'],'title'=>'图片']];
                                                }else{
                                                $data['image'] = json_decode($data['image'],true);
                                                }
                                            </php>
                                            <!-- // 循环输出代码 -->
                                            <notempty name="$data['image']">
                                                <volist name="$data['image']" id="vo">
                                                    <div class="layui-input-inline layui-uplpad-image">
                                                        <img src="{$vo.src}" lay-image-click>
                                                        <input type="text" name="image[{$key}][src]" class="layui-hide"
                                                               value="{$vo.src}">
                                                        <input type="text" name="image[{$key}][title]"
                                                               class="layui-input"
                                                               value="{$vo.title}" placeholder="图片简介">
                                                    </div>
                                                </volist>
                                            </notempty>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">所在社区</label>
                                    <div class="layui-input-block">
                                        <div class="layui-form-label" style='width: auto; text-align:left;'>
                                            {$data.community_name}
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">{$title}名称</label>
                                    <div class="layui-input-block">
                                        <div class="layui-form-label" style='width: auto; text-align:left;'>
                                        {$data.grid_name}
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">{$title}范围</label>
                                    <div class="layui-input-block">
                                        <div class="layui-form-label" style='width: auto; text-align:left;'>
                                        {$data.address}
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">{$title}东至</label>
                                    <div class="layui-input-block">
                                        <div class="layui-form-label" style='width: auto; text-align:left;'>
                                        {$data.east}
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">{$title}南至</label>
                                    <div class="layui-input-block">
                                        <div class="layui-form-label" style='width: auto; text-align:left;'>
                                        {$data.south}
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">{$title}西至</label>
                                    <div class="layui-input-block">
                                        <div class="layui-form-label" style='width: auto; text-align:left;'>
                                        {$data.west}
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-form-item">
                                    <label class="layui-form-label">{$title}北至</label>
                                    <div class="layui-input-block">
                                        <div class="layui-form-label" style='width: auto; text-align:left;'>
                                            {$data.north}
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="layui-form-item">
                                    <label class="layui-form-label">720云ID</label>
                                    <div class="layui-input-block">
                                        <div class="layui-form-label" style='width: auto; text-align:left;'>
                                            {$data.scene_id}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-col-md8 layui-grid-1" data-index="1">
                                <div class="layui-row layui-col-space15">
                                    <gt name="total.building" value="0">
                                        <div class="layui-col-md4 layui-col-sm4 layui-col-xs6" style="cursor: pointer;">
                                            <div class="console-link-block" sa-event="tabs" data-url="{:url('/building/index',['grid_group_id'=>$data['id']])}" data-title="实有建筑管理">
                                                <div class="console-link-block-num">{$total.building}</div>
                                                <div class="console-link-block-text">实有建筑数量</div>
                                                <i class="console-link-block-icon layui-icon fa-building"></i>
                                                <!--                                <div class="console-link-block-band">待审批</div>-->
                                            </div>
                                        </div>
                                    </gt>
                                    <gt name="total.house" value="0">
                                        <div class="layui-col-md4 layui-col-sm4 layui-col-xs6" style="cursor: pointer;">
                                            <div class="console-link-block" sa-event="tabs" data-url="{:url('/house/index',['grid_group_id'=>$data['id']])}" data-title="实有房屋管理">
                                                <div class="console-link-block-num">{$total.house}</div>
                                                <div class="console-link-block-text">实有房屋数量</div>
                                                <i class="console-link-block-icon layui-icon fa-home"></i>
                                                <!--                                <div class="console-link-block-band">待审批</div>-->
                                            </div>
                                        </div>
                                    </gt>
                                    <gt name="total.person" value="0">
                                        <div class="layui-col-md4 layui-col-sm4 layui-col-xs6" style="cursor: pointer;">
                                            <div class="console-link-block" sa-event="tabs" data-url="{:url('/person/index',['grid_group_id'=>$data['id']])}" data-title="实有人口管理">
                                                <div class="console-link-block-num">{$total.person}</div>
                                                <div class="console-link-block-text">实有人口数量</div>
                                                <i class="console-link-block-icon layui-icon fa-group"></i>
                                                <!--                                <div class="console-link-block-band">待审批</div>-->
                                            </div>
                                        </div>
                                    </gt>
                                    <gt name="total.government" value="0">
                                        <div class="layui-col-md4 layui-col-sm4 layui-col-xs6" style="cursor: pointer;">
                                            <div class="console-link-block" sa-event="tabs" data-url="{:url('/placeGovernment/index',['grid_group_id'=>$data['id']])}" data-title="机关及事业单位">
                                                <div class="console-link-block-num">{$total.government}</div>
                                                <div class="console-link-block-text">机关及事业单位数量</div>
                                                <i class="console-link-block-icon layui-icon fa-briefcase"></i>
                                                <!--                                <div class="console-link-block-band">待审批</div>-->
                                            </div>
                                        </div>
                                    </gt>
                                    <gt name="total.enterprise" value="0">
                                        <div class="layui-col-md4 layui-col-sm4 layui-col-xs6" style="cursor: pointer;">
                                            <div class="console-link-block" sa-event="tabs" data-url="{:url('/placeEnterprise/index',['grid_group_id'=>$data['id']])}" data-title="企业">
                                                <div class="console-link-block-num">{$total.enterprise}</div>
                                                <div class="console-link-block-text">企业数量</div>
                                                <i class="console-link-block-icon layui-icon fa-laptop"></i>
                                                <!--                                <div class="console-link-block-band">待审批</div>-->
                                            </div>
                                        </div>
                                    </gt>
                                    <gt name="total.merchant" value="0">
                                        <div class="layui-col-md4 layui-col-sm4 layui-col-xs6" style="cursor: pointer;">
                                            <div class="console-link-block" sa-event="tabs" data-url="{:url('/placeMerchant/index',['grid_group_id'=>$data['id']])}" data-title="个体工商户">
                                                <div class="console-link-block-num">{$total.merchant}</div>
                                                <div class="console-link-block-text">个体工商户数量</div>
                                                <i class="console-link-block-icon layui-icon fa-coffee"></i>
                                                <!--                                <div class="console-link-block-band">待审批</div>-->
                                            </div>
                                        </div>
                                    </gt>
                                </div>
                                <div class="layui-row layui-col-space15">
                                    <blockquote class="layui-elem-quote layui-quote-nm" style="border: none;background: #ffffff; padding: 5px 0 0 10px; border: 1px solid #e2e2e2;">
                                        <label style="border: none; background:#FFFFFF; display: inline;">{$org_list[0]['name']}</label>
                                        <hr style="margin: 3px auto;">
                                        <foreach name="$org_list[0]['person']" item="vi">
                                            <div class="layui-inline">
                                                      <php>
                                                    if (strpos($vi['avatar'], 'http') === 0) {
                                                    }else{
                                                    $avatar = json_decode($vi['avatar'],true);
                                                    if(count($avatar)){
                                                    $vi['avatar'] = $avatar[0]['src'];
                                                    }
                                                    }
                                                </php>
                                                <img style="float: left;width: 100px;margin: 5px 10px;" src="{$vi.avatar}">
                                                <div class="layui-inline">
                                                    <div class="layui-input-inline" style="width: auto;margin-top: 8px;">
                                                        <input type="text" class="layui-input" style="margin-bottom: 10px;" disabled="" readonly="" value="姓名：{$vi.name}">
                                                        <input type="text" class="layui-input" style="margin-bottom: 10px;" disabled="" readonly="" value="职务：{$vi.post}">
                                                        <input type="text" class="layui-input" disabled="" readonly="" value="电话：{$vi.phone}">
                                                    </div>
                                                </div>
                                            </div>
                                        </foreach>
                                    </blockquote>
                                    <blockquote class="layui-elem-quote layui-quote-nm" style="border: none;background: #ffffff; padding: 5px 0 0 10px; border: 1px solid #e2e2e2;">
                                        <label style="border: none; background:#FFFFFF;">{$org_list[1]['name']}</label>
                                        <hr style="margin: 3px auto;">
                                        <foreach name="$org_list[1]['person']" item="vi">
                                            <div class="layui-inline">
                                                      <php>
                                                    if (strpos($vi['avatar'], 'http') === 0) {
                                                    }else{
                                                    $avatar = json_decode($vi['avatar'],true);
                                                    if(count($avatar)){
                                                    $vi['avatar'] = $avatar[0]['src'];
                                                    }
                                                    }
                                                </php>
                                                <img style="float: left;width: 100px;margin: 5px 10px;" src="{$vi.avatar}">
                                                <div class="layui-inline">
                                                    <div class="layui-input-inline" style="width: auto;margin-top: 8px;">
                                                        <input type="text" class="layui-input" style="margin-bottom: 10px;" disabled="" readonly="" value="姓名：{$vi.name}">
                                                        <input type="text" class="layui-input" style="margin-bottom: 10px;" disabled="" readonly="" value="职务：{$vi.post}">
                                                        <input type="text" class="layui-input" disabled="" readonly="" value="电话：{$vi.phone}">
                                                    </div>
                                                </div>
                                            </div>
                                        </foreach>
                                    </blockquote>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-tab-item " data-index="1">
                        <table id="lay-grid"></table>
                    </div>
                    <div class="layui-tab-item " data-index="2">
                        <table id="lay-build"></table>
                    </div>
                    <div class="layui-tab-item " data-index="3">
                        <table id="lay-house"></table>
                    </div>
                    <div class="layui-tab-item " data-index="4">
                        <table id="lay-place_merchant"></table>

                    </div>
                    <div class="layui-tab-item " data-index="5">
                        <table id="lay-place_enterprise"></table>

                    </div>
                    <div class="layui-tab-item " data-index="6">
                        <table id="lay-place_government"></table>

                    </div>
                    <div class="layui-tab-item " data-index="7">
                        <table id="lay-person"></table>
                    </div>
                    <div class="layui-tab-item " data-index="8">
                        {:htmlspecialchars_decode($data['details'])}
                    </div>
                    <div class="layui-tab-item " data-index="9">
                        {:htmlspecialchars_decode($data['important'])}
                    </div>
                    <div class="layui-tab-item " data-index="10">
                        <table id="lay-history"></table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<include file="/public/static"/>
<include file="/public/footer"/>
<script type="text/html" id="tableBar_grid">
    <button class="layui-btn layui-btn-primary layui-btn-xs" sa-event="tabs"
            data-title="{:__('查看网格信息')}" data-area="100%,100%"
            data-maxmin="true" data-url="{:url('/Grid/detail')}?id={{d.id}}" >
        {:__('查看')}
    </button>
</script>
<script type="text/html" id="tableBar_build">
    <button class="layui-btn layui-btn-primary layui-btn-xs" sa-event="tabs"
            data-title="{:__('查看实有建筑信息')}" data-area="100%,100%"
            data-maxmin="true" data-url="{:url('/Building/detail')}?id={{d.id}}" >
        {:__('查看')}
    </button>
</script>
<script type="text/html" id="tableBar_house">
    <button class="layui-btn layui-btn-primary layui-btn-xs" sa-event="tabs"
            data-title="{:__('查看实有房屋信息')}" data-area="100%,100%"
            data-maxmin="true" data-url="{:url('/House/detail')}?id={{d.id}}" >
        {:__('查看')}
    </button>
</script>
<script type="text/html" id="tableBar_place_merchant">
    <button class="layui-btn layui-btn-primary layui-btn-xs" sa-event="tabs"
            data-title="{:__('查看个体工商户信息')}" data-area="100%,100%"
            data-maxmin="true" data-url="{:url('/PlaceMerchant/detail')}?id={{d.id}}" >
        {:__('查看')}
    </button>
</script>
<script type="text/html" id="tableBar_place_enterprise">
    <button class="layui-btn layui-btn-primary layui-btn-xs" sa-event="tabs"
            data-title="{:__('查看企业信息')}" data-area="100%,100%"
            data-maxmin="true" data-url="{:url('/PlaceEnterprise/detail')}?id={{d.id}}" >
        {:__('查看')}
    </button>
</script>
<script type="text/html" id="tableBar_place_government">
    <button class="layui-btn layui-btn-primary layui-btn-xs" sa-event="tabs"
            data-title="{:__('查看机关及事业单位信息')}" data-area="100%,100%"
            data-maxmin="true" data-url="{:url('/PlaceGovernment/detail')}?id={{d.id}}" >
        {:__('查看')}
    </button>
</script>
<script type="text/html" id="tableBar_person">
    <button class="layui-btn layui-btn-primary layui-btn-xs" sa-event="tabs"
            data-title="{:__('查看实有人口信息')}" data-area="100%,100%"
            data-maxmin="true" data-url="{:url('/Person/detail')}?id={{d.id}}" >
        {:__('查看')}
    </button>
</script>

<script>
    function idCard(userCard, num) {
        //获取出生日期
        if (num == 1) {
            birth = userCard.substring(6, 10) + "-" + userCard.substring(10, 12) + "-" + userCard.substring(12, 14);
            return birth;
        }
        //获取性别
        if (num == 2) {
            if (parseInt(userCard.substr(16, 1)) % 2 == 1) {
                return "男";
            } else {
                return "女";
            }
        }
        //获取年龄
        if (num == 3) {
            var myDate = new Date();
            var month = myDate.getMonth() + 1;
            var day = myDate.getDate();
            var age = myDate.getFullYear() - userCard.substring(6, 10) - 1;
            if (userCard.substring(10, 12) < month || userCard.substring(10, 12) == month && userCard.substring(12, 14) <= day) {
                age++;
            }
            return age;
        }
    }
    layui.use(['layer', 'form', 'admin'], function () {
        var form = layui.form;
        var $ = layui.jquery;
        var table = layui.table;
        var admin = layui.admin;
        var type_json = {:json_encode($place_merchant_type)};
        $(".tab-li").on("click",function(){
            if($(this).attr("data-id")=='grid'){
                table.render({
                    elem: "#lay-grid"
                    ,url: "{:url('/grid/grid',['grid_group'=>$data['id']])}"
                    ,cellMinWidth: 160
                    ,page: true
                    ,limit: 50,limits: [50, 100, 200, 500]
                    ,height: 'full-150' //设置表格高度，减去固定的底部高度
                    ,cols: [[
                        {field: 'community_name',width:120, title:'{:__("所属社区")}'},
                        {field:'group_name', title:'{:__("所属网格组")}'},
                        {field: 'grid_name',width:120,title:'{:__("网格名称")}'},
                        {field: 'image',width: 120,templet:function(d) {
                                var album = [];
                                try {
                                    d.image = JSON.parse(d.image);
                                    for (var i in d.image) {
                                        album[i] = '<a href="javascript:" class="fileslink" ><img class="filesuffix" lay-image-click src="'+admin.toThumb(d.image[i].src)+'"></a>'
                                    }
                                    return album.join('');
                                } catch (e) {
                                    return '<a href="javascript:" class="fileslink" ><img class="filesuffix" lay-image-click src="'+admin.toThumb(d.image)+'"></a>'
                                }
                            },title:'{:__("街景示意图")}'},
                        {align: 'center', toolbar: '#tableBar_grid', width:100, fixed: 'right', title: '{:__("操作")}'},
                    ]]
                })
            }else if($(this).attr("data-id")=='build'){
                table.render({
                    elem: "#lay-build"
                    ,url: "{:url('/Building/index',['grid_group_id'=>$data['id']])}"
                    ,cellMinWidth: 160
                    ,page: true
                    ,limit: 50,limits: [50, 100, 200, 500]
                    ,height: 'full-150' //设置表格高度，减去固定的底部高度
                    ,cols: [[
                        {field:'community_name',title: '{:__("所在社区")}'},
                        {field:'grid_group_name', title: '{:__("所在网格组")}'},
                        {field:'grid_name', title: '{:__("所在网格")}'},
                        {field: 'building_name', title: '{:__("建筑名称")}'},
                        {
                            field: 'image', templet: function (d) {
                                var album = [];
                                try {
                                    d.image = JSON.parse(d.image);
                                    for (var i in d.image) {
                                        album[i] = '<a href="javascript:" class="fileslink" ><img class="filesuffix" lay-image-click src="' + admin.toThumb(d.image[i].src) + '"></a>'
                                    }
                                    return album.join('');
                                } catch (e) {
                                    return '<a href="javascript:" class="fileslink" ><img class="filesuffix" lay-image-click src="' + admin.toThumb(d.image) +  '"></a>'
                                }
                            }, title: '{:__("街景示意图")}'
                        },
                        {
                            field:'type', title: '{:__("建筑类型")}', templet: function (d) {
                                var resData = {:json_encode(getDictionary(13))};
                                return d.type > 0 ?resData[d.type]:''
                            }
                        },
                        {
                            field: 'unit', title: '{:__("单元数-地上层数-地下层数")}', templet: function (d) {
                                return d.unit + "-" + d.floor + "-" + d.floor_under
                            }
                        },
                        {align: 'center', toolbar: '#tableBar_build', width:100, fixed: 'right', title: '{:__("操作")}'},
                    ]]
                })
            }else if($(this).attr("data-id")=='house'){
                table.render({
                    elem: "#lay-house"
                    ,url: "{:url('/House/index',['grid_group_id'=>$data['id']])}"
                    ,cellMinWidth: 160
                    ,page: true
                    ,limit: 50,limits: [50, 100, 200, 500]
                    ,height: 'full-150' //设置表格高度，减去固定的底部高度
                    ,cols: [[
                        {field: 'community_id',width: 320, title: '{:__("所在区域")}',templet:function(d){
                                return d.community_id>0?d.community_name+","+d.grid_group_name+","+d.grid_name:''
                            }},
                        {field: 'building_name',width:160, title: '{:__("所在建筑")}'},
                        {field: 'unit',width:100, title: '{:__("单元楼层")}',templet:function(d){
                                return d.unit+"单元,"+d.floor+"楼"
                            }},
                        {field: 'code',width:100, title: '{:__("门牌号")}', templet: function(d){
                                return d.code;
                            }},
                        {field: 'area',width:80, title: '{:__("房屋面积")}',templet:function(d){
                                return d.area+"㎡"
                            }},
                        {field: 'property_right',width:80, title: '{:__("产权性质")}',templet:function(d){
                                var resData = {:json_encode(getDictionary(349))};
                                return d.property_right > 0 ?resData[d.property_right]:''
                            }},
                        {field: 'usage_category',width:80, title: '{:__("使用类别")}',templet:function(d){
                                var resData = {:json_encode(getDictionary(355))};
                                return d.usage_category > 0 ?resData[d.usage_category]:''
                            }},
                        {field: 'house_type',width:80, title: '{:__("房屋类型")}',templet:function(d){
                                var resData = {:json_encode(getDictionary(362))};
                                return d.house_type > 0 ?resData[d.house_type]:''
                            }},
                        {field: 'residence_nature',width:80, title: '{:__("住所性质")}',templet:function(d){
                                var resData = {:json_encode(getDictionary(367))};
                                return d.residence_nature > 0 ?resData[d.residence_nature]:''
                            }},
                        {field: 'rent',width:120, title: '{:__("是否出租")}',templet:function(d){
                                return d.rent==1?'是':'否'
                            }},
                        {align: 'center', toolbar: '#tableBar_house', width:100, fixed: 'right', title: '{:__("操作")}'},
                    ]]
                })
            }else if($(this).attr("data-id")=='place_merchant'){
                table.render({
                    elem: "#lay-place_merchant"
                    ,url: "{:url('/PlaceMerchant/index',['grid_group_id'=>$data['id']])}"
                    ,cellMinWidth: 60
                    ,page: true
                    ,limit: 50,limits: [50, 100, 200, 500]
                    ,height: 'full-150' //设置表格高度，减去固定的底部高度
                    ,cols: [[
                        {field: 'community_id',width: 320, title: '{:__("所在区域")}',templet:function(d){
                                return d.community_id>0?d.community_name+","+d.grid_group_name+","+d.grid_name:''
                            }},
                        {field: 'building_name',width:160, title: '{:__("所在建筑")}'},
                        {field: 'merchant_name',width:200, title: '{:__("商户名称")}'},
                        {
                    field: 'image',width: 120,width: 120, templet: function (d) {
                        if(d.image==''){
                            return '';
                        }
                        var album = [];
                        try {
                            d.image = JSON.parse(d.image);
                            for (var i in d.image) {
                                album[i] = '<a href="javascript:" class="fileslink" ><img class="filesuffix" lay-image-click src="' + admin.toThumb(d.image[i].src) + '"></a>'
                            }
                            return album.join('');
                        } catch (e) {
                            return '<a href="javascript:" class="fileslink" ><img class="filesuffix" lay-image-click src="' + admin.toThumb(d.image) +  '"></a>'
                        }
                    }, title: '{:__("街景示意图")}'
                },
                        {field: 'type_1_id',width:120, title: '{:__("行业大类")}',templet:function(d){
                                return d.type_1_id>0?type_json[d.type_1_id].name:'';
                            }},
                        {field: 'type_2_id',width:120, title: '{:__("行业类别")}',templet:function(d){
                                return d.type_2_id>0?type_json[d.type_1_id].children[d.type_2_id].name:'';
                            }},
                        {field: 'corporation',width:80, title: '{:__("法人")}'},
                        {field: 'phone',width:120, title: '{:__("联系电话")}'},
                        {field:'address',width: 200, title: '{:__("商户地址")}'},
                        {field: 'unit',width:100, title: '{:__("单元楼层")}',templet:function(d){
                                return d.unit+"单元,"+d.floor+"楼"
                            }},
                        {field: 'code',width:180, title: '{:__("营业执照号")}'},
                        {align: 'center', toolbar: '#tableBar_place_merchant', width:100, fixed: 'right', title: '{:__("操作")}'},
                    ]]
                })
            }else if($(this).attr("data-id")=='place_enterprise'){
                table.render({
                    elem: "#lay-place_enterprise"
                    ,url: "{:url('/PlaceEnterprise/index',['grid_group_id'=>$data['id']])}"
                    ,cellMinWidth: 60
                    ,page: true
                    ,limit: 50,limits: [50, 100, 200, 500]
                    ,height: 'full-150' //设置表格高度，减去固定的底部高度
                    ,cols: [[
                        {field: 'community_id',width: 320, title: '{:__("所在区域")}',templet:function(d){
                                return d.community_id>0?d.community_name+","+d.grid_group_name+","+d.grid_name:''
                            }},
                        {field: 'building_name',width:160, title: '{:__("所在建筑")}'},
                        {field: 'enterprise_name',width:200, title: '{:__("企业名称")}'},
                        {
                    field: 'image',width: 120,width: 120, templet: function (d) {
                        if(d.image==''){
                            return '';
                        }
                        var album = [];
                        try {
                            d.image = JSON.parse(d.image);
                            for (var i in d.image) {
                                album[i] = '<a href="javascript:" class="fileslink" ><img class="filesuffix" lay-image-click src="' + admin.toThumb(d.image[i].src) + '"></a>'
                            }
                            return album.join('');
                        } catch (e) {
                            return '<a href="javascript:" class="fileslink" ><img class="filesuffix" lay-image-click src="' + admin.toThumb(d.image) +  '"></a>'
                        }
                    }, title: '{:__("街景示意图")}'
                },
                        {field: 'type_1_id',width:120, title: '{:__("行业大类")}',templet:function(d){
                                return d.type_1_id>0?type_json[d.type_1_id].name:'';
                            }},
                        {field: 'type_2_id',width:120, title: '{:__("行业类别")}',templet:function(d){
                                return d.type_2_id>0?type_json[d.type_1_id].children[d.type_2_id].name:'';
                            }},
                        {field: 'corporation',width:80, title: '{:__("法人")}'},
                        {field: 'phone',width:120, title: '{:__("联系电话")}'},
                        {field:'address',width: 200, title: '{:__("企业地址")}'},
                        {field: 'unit',width:100, title: '{:__("单元楼层")}',templet:function(d){
                                return d.unit+"单元,"+d.floor+"楼"
                            }},
                        {field: 'code',width:180, title: '{:__("营业执照号")}'},
                        {align: 'center', toolbar: '#tableBar_place_enterprise', width:100, fixed: 'right', title: '{:__("操作")}'},
                    ]]
                })
            }else if($(this).attr("data-id")=='place_government'){
                table.render({
                    elem: "#lay-place_government"
                    ,url: "{:url('/PlaceGovernment/index',['grid_group_id'=>$data['id']])}"
                    ,cellMinWidth: 60
                    ,page: true
                    ,limit: 50,limits: [50, 100, 200, 500]
                    ,height: 'full-150' //设置表格高度，减去固定的底部高度
                    ,cols: [[
                        {field: 'community_id',width: 320, title: '{:__("所在区域")}',templet:function(d){
                                return d.community_id>0?d.community_name+","+d.grid_group_name+","+d.grid_name:''
                            }},
                        {field: 'building_name',width:160, title: '{:__("所在建筑")}'},
                        {field: 'unit_name',width:200, title: '{:__("单位名称")}'},
                        {
                    field: 'image',width: 120,width: 120, templet: function (d) {
                        if(d.image==''){
                            return '';
                        }
                        var album = [];
                        try {
                            d.image = JSON.parse(d.image);
                            for (var i in d.image) {
                                album[i] = '<a href="javascript:" class="fileslink" ><img class="filesuffix" lay-image-click src="' + admin.toThumb(d.image[i].src) + '"></a>'
                            }
                            return album.join('');
                        } catch (e) {
                            return '<a href="javascript:" class="fileslink" ><img class="filesuffix" lay-image-click src="' + admin.toThumb(d.image) +  '"></a>'
                        }
                    }, title: '{:__("街景示意图")}'
                },
                        {field:'address',width: 200, title: '{:__("单位地址")}'},
                        {field:'type',width: 120, title: '{:__("单位分类")}',templet:function(d){
                                var resData = {:json_encode($type)};
                                return d.type > 0 ?resData[d.type]:''
                            }},
                        {field: 'unit',width:100, title: '{:__("单元楼层")}',templet:function(d){
                                return d.unit+"单元,"+d.floor+"楼"
                            }},
                        {field: 'code',width:180, title: '{:__("统一社会信用代码")}'},
                        {field: 'corporation',width:80, title: '{:__("法人")}'},
                        {field: 'phone',width:120, title: '{:__("联系电话")}'},
                        {align: 'center', toolbar: '#tableBar_place_government', width:100, fixed: 'right', title: '{:__("操作")}'},
                    ]]
                })
            }else if($(this).attr("data-id")=='history'){
                table.render({
                    elem: "#lay-history"
                    ,url: "{:url('/index/getStatistics',['column'=>'grid_group_id','id'=>$data['id']])}"
                    ,cellMinWidth: 60
                    ,page: true
                    ,limit: 50,limits: [50, 100, 200, 500]
                    ,height: 'full-150' //设置表格高度，减去固定的底部高度
                    ,cols: [[
                        {field: 'person', title: '{:__("实有人口")}'},
                        {field: 'building', title: '{:__("实有建筑")}'},
                        {field: 'house', title: '{:__("实有房屋")}'},
                        {field: 'merchant', title: '{:__("个体工商户")}'},
                        {field: 'enterprise', title: '{:__("企业")}'},
                        {field: 'government', title: '{:__("机关及事业单位")}'},
                        {field: 'date', title: '{:__("统计时间")}'},
                    ]]
                })
            }else{
                table.render({
                    elem: "#lay-person"
                    ,url: "{:url('/Person/index',['grid_group_id'=>$data['id']])}"
                    ,cellMinWidth: 60
                    ,page: true
                    ,limit: 50,limits: [50, 100, 200, 500]
                    ,height: 'full-150' //设置表格高度，减去固定的底部高度
                    ,cols: [[
                        {field:'name',align: 'left',width:180,title:'{:__("姓名")}', templet: function(d){
                                return d.name;
                            }},

                        {field:'id_code',title:'{:__("年龄")}',width:50,templet:function(d){
                                return d.age
                            }},
                        {field:'sex',title:'{:__("性别")}',width:50,templet:function(d){
                                return d.sex==1?'男':'女'
                            }},
                        {field: 'community_id',width: 320, minWidth: 300, title: '{:__("所在区域")}',templet:function(d){
                                return d.community_id>0?d.community_name+","+d.grid_group_name+","+d.grid_name:''
                            }},
                        {field: 'building_name',width:160, title: '{:__("所在房屋")}'},
                        {field:'address',width:200,title:'{:__("房屋地址")}'},
                        {field:'person_type',title:'{:__("人口类型")}',templet:function(d){
                                var resData = {:json_encode(getDictionary(59))};
                                return d.person_type>0?resData[d.person_type]:''
                            }},
                        {align: 'center', toolbar: '#tableBar_person', width:100, fixed: 'right', title: '{:__("操作")}'},
                    ]]
                })
            }
        });
    });
</script>