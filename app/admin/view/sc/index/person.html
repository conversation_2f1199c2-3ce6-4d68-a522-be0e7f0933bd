<include file="sc/public/header" />
<include file="/person/param" />
<include file="/sc/public/params" />
<style>
	.person li{height: calc(50% - 20px);}
</style>
<!--<iframe src="{:url('sc/index/panorama',$param)}" frameborder="0" class="mapiframe"></iframe>-->
<!-- 头部 begin -->
<include file="sc/public/top" />
<!-- 头部 end -->

<div class="person-main">
	<div class="person">
		<ul class="flex flex-wrap">
			<!-- 循环 begin -->
			<li style="width: 100%">
				<div class="title"><h3>人口分布</h3></div>
				<div class="person-item" id="rkfb"></div>
			</li>
			<li>
				<div class="title"><h3>人口分类</h3></div>
				<div class="person-item" id="rkfl">
					<div class="jdbgk-l-t" style="margin-top: 80px;">
						<canvas id="syrk" class="charts"></canvas>
						<canvas id="czrk" class="charts"></canvas>
						<canvas id="ldrk" class="charts"></canvas>
						<canvas id="zlrk" class="charts"></canvas>
						<span>实有人口</span>
						<span>常住人口</span>
						<span>流动人口</span>
						<span>暂离人口</span>
					</div>
				</div>
			</li>
			<li>
				<div class="title"><h3>户口类型</h3></div>
				<div class="person-item" id="hklx"></div>
			</li>
			<li>
				<div class="title"><h3>教育程度</h3></div>
				<div class="person-item" id="edu"></div>
			</li>
<!--			<li>-->
<!--				<div class="title"><h3>病人</h3></div>-->
<!--				<div class="person-item" id="ill"></div>-->
<!--			</li>-->
<!--			<li>-->
<!--				<div class="title"><h3>劣迹人群</h3></div>-->
<!--				<div class="person-item" id="bad"></div>-->
<!--			</li>-->
<!--			<li>-->
<!--				<div class="title"><h3>其他</h3></div>-->
<!--				<div class="person-item" id="other"></div>-->
<!--			</li>-->
			<!-- 循环 end -->
		</ul>
	</div>
	<div class="newlist-main-1">
		<div class="newlist-filter-bg-1">
			<div class="newlist-filter">
							<form id="form">
				<ul>
									<include file="sc/public/search" />
									<li>
										<div class="newlist-filter-btn">展开</div>
									</li>
								</ul>
				<ul>
					<li>
						<p>{:__('实有建筑')}</p>
						<select id="building_id" name="building_id" lay-search  lay-filter="buildingChange">
							<option value="">请选择</option>
								<foreach name="Buildings" item="item">
															<option value="{$item.id}"
															<eq name="item.id" value="$data.building_id">selected</eq>
															>{$item.building_name}</option>
													</foreach>
						</select>
					</li>
					<li>
						<p>{:__('单元')}</p>
						<select id="unit" name="unit" class="search">
						</select>
					</li>
					<li>
						<p>养老保险</p>
						<select name="endowment_insurance"  class="search">
							<option value="">全部</option>
							<foreach name="endowment_insurance" item="vo" key="key">
								<option value="{$key}">{$vo}</option>
							</foreach>
						</select>
					</li>
					<li>
						<p>户口类型</p>
						<select name="rpr"  class="search">
							<option value="">全部</option>
							<foreach name="rpr" item="vo" key="key">
								<option value="{$key}">{$vo}</option>
							</foreach>
						</select>
					</li>
					<li>
						<p>户口性质</p>
						<select name="rpr_nature"  class="search">
							<option value="">全部</option>
							<foreach name="rpr_nature" item="vo" key="key">
								<option value="{$key}">{$vo}</option>
							</foreach>
						</select>
					</li>
					<li>
						<p>人口类型</p>
						<select name="person_type" id="person_type"  class="search">
							<option value="">全部</option>
							<foreach name="person_type" item="vo" key="key">
								<option value="{$key}"
										<eq name="key" value="$param.person_type">selected</eq>
									>{$vo}</option>
							</foreach>
						</select>
					</li>
					<li>
						<p>兵役状况</p>
						<select name="enlistment"  class="search">
							<option value="">全部</option>
							<foreach name="enlistment" item="vo" key="key">
								<option value="{$key}">{$vo}</option>
							</foreach>
						</select>
					</li>
					<li>
						<p>政治面貌</p>
						<select name="face"  class="search">
							<option value="">全部</option>
							<foreach name="face" item="vo" key="key">
								<option value="{$key}">{$vo}</option>
							</foreach>
						</select>
					</li>
					<li>
						<p>文化程度</p>
						<select name="education"  class="search">
							<option value="">全部</option>
							<foreach name="education" item="vo" key="key">
								<option value="{$key}">{$vo}</option>
							</foreach>
						</select>
					</li>
					<li>
						<p>民族</p>
						<select name="nation"  class="search">
							<option value="">全部</option>
							<foreach name="nation" item="vo" key="key">
								<option value="{$key}">{$vo}</option>
							</foreach>
						</select>
					</li>
					<li>
						<p>性别</p>
						<select name="sex"  class="search">
							<option value="">全部</option>
							<foreach name="sex" item="vo" key="key">
								<option value="{$key}">{$vo}</option>
							</foreach>
						</select>
					</li>
					<li>
						<p>婚姻状况</p>
						<select name="marry"  class="search">
							<option value="">全部</option>
							<foreach name="marry" item="vo" key="key">
								<option value="{$key}">{$vo}</option>
							</foreach>
						</select>
					</li>
					<li>
						<p>独生子女</p>
						<select name="only_child"  class="search">
							<option value="">全部</option>
							<foreach name="only_child" item="vo" key="key">
								<option value="{$key}">{$vo}</option>
							</foreach>
						</select>
					</li>
					<li>
						<p>子女家庭</p>
						<select name="other_child"  class="search">
							<option value="">全部</option>
							<foreach name="other_child" item="vo" key="key">
								<option value="{$key+1}">{$vo}</option>
							</foreach>
						</select>
					</li>
					<li>
						<p>健康状况</p>
						<select name="health"  class="search">
							<option value="">全部</option>
							<foreach name="health" item="vo" key="key">
								<option value="{$key}">{$vo}</option>
							</foreach>
						</select>
					</li>
					<li>
						<p>劳动能力</p>
						<select name="labor_capacity"  class="search">
							<option value="">全部</option>
							<foreach name="labor_capacity" item="vo" key="key">
								<option value="{$key}">{$vo}</option>
							</foreach>
						</select>
					</li>
					<li>
						<p>宗教</p>
						<select name="religion"  class="search">
							<option value="">全部</option>
							<foreach name="religion" item="vo" key="key">
								<option value="{$key}">{$vo}</option>
							</foreach>
						</select>
					</li>
				</ul>
				<ul>
						<li>
						<p>年龄</p>
							<input type="number" name="age_start" autocomplete="off">
						<span>-</span>
						<input type="number" name="age_end" autocomplete="off">
					</li>
				</ul>
				<h3>特殊人群</h3>
							<ul>
						<li>
								<foreach name="insurance"  item="vo" key="key">
								<input type="checkbox"  name="{$key}"  lay-filter="search"
										value="1">
									<label>{$vo}</label>
							</foreach>
						</li>
					<li>
						<foreach name="representative"  item="vo" key="key">
							<php>
								$key = camelCaseToUnderscore($key);
							</php>
							<input type="checkbox"  name="{$key}"  lay-filter="search"
									value="1">
							<label>{$vo}</label>
						</foreach>
					</li>
					<li>
						<foreach name="elderly"  item="vo" key="key">
							<php>
								$key = camelCaseToUnderscore($key);
							</php>
							<input type="checkbox"  name="{$key}" <eq name="$param[$key]" value="1">checked</eq>  lay-filter="search"
									value="1">
							<label>{$vo}</label>
						</foreach>
					</li>
					<li>
						<foreach name="dilemma"  item="vo" key="key">
							<php>
								$key = camelCaseToUnderscore($key);
							</php>
							<input type="checkbox"  name="{$key}" <eq name="$param[$key]" value="1">checked</eq> lay-filter="search"
									value="1">
							<label>{$vo}</label>
						</foreach>
					</li>
					<li>
						<foreach name="dilemma_children"  item="vo" key="key">
							<php>
								$key = camelCaseToUnderscore($key);
							</php>
							<input type="checkbox"  name="{$key}" <eq name="$param[$key]" value="1">checked</eq> lay-filter="search"
									value="1">
							<label>{$vo}</label>
						</foreach>
					</li>
					<li>
						<foreach name="patient"  item="vo" key="key">
							<php>
								$key = camelCaseToUnderscore($key);
							</php>
							<input type="checkbox"  name="{$key}" <eq name="$param[$key]" value="1">checked</eq> lay-filter="search"
									value="1">
							<label>{$vo}</label>
						</foreach>
					</li>
					<li>
						<foreach name="bad"  item="vo" key="key">
							<php>
								$key = camelCaseToUnderscore($key);
							</php>
							<input type="checkbox"  name="{$key}" <eq name="$param[$key]" value="1">checked</eq> lay-filter="search"
									value="1">
							<label>{$vo}</label>
						</foreach>
						</li>
					<li>
						<foreach name="other"  item="vo" key="key">
							<php>
								$key = camelCaseToUnderscore($key);
							</php>
							<input type="checkbox"  name="{$key}" <eq name="$param[$key]" value="1">checked</eq> lay-filter="search"
									value="1">
							<label>{$vo}</label>
						</foreach>
					</li>
					<li>
						<input type="checkbox" name="disability" value="1" lay-filter="search">
						<label>残疾</label>
					</li>
					<li>
						<input type="checkbox" name="dead" value="1" lay-filter="search">
						<label>死亡</label>
					</li>
							</ul>
							</form>
				
				
			</div>
		</div>
	
	
		<div class="newlist mt20">
			<table id="lay-tableList" lay-filter="lay-tableList"></table>
		</div>
	</div>
</div> 


<include file="/public/footer" />
<include file="sc/public/footer" />
<include file="/public/select" />
<script language="JavaScript">
	var uChartsInstance = {};

	function showCharts(id,type,data){
		const canvas = document.getElementById(id);
		const ctx = canvas.getContext("2d");
		canvas.width = canvas.offsetWidth;
		canvas.height = canvas.offsetHeight;
		const opts = getOpts(type,data,canvas,ctx);
		uChartsInstance[id] = new uCharts(opts);
	}

	function showECharts(id,type,data){
		var myCharts = echarts.init(document.getElementById(id), 'dark');
		const opts = getOpts(type,data);
		myCharts.setOption(opts);
		myCharts.on('click', function (params) {
			toDetail(id,params);
		});
	}
</script>
<script>
	layui.use(['admin','table','layer'], function () {
		var table = layui.table;
		var layer = layui.layer;
		var url = "{:url('/Person/index')}"
		$('#form').on('change', 'select, input[type="checkbox"]', function() {
            $("#search").click();
        });
        		
        $("#search").on("click",function(){
            var join = "?"
        	var new_url = url;
            var formData = $('#form').serializeArray();
            var filteredData = formData.filter(function(item) {
                return item.value !== '';
            });
            for(key in filteredData){
                filteredData[key] = filteredData[key]['name']+"="+filteredData[key]['value']
            }
            formData = filteredData.join("&")
            
        	var keyword = $("#keyword").val();
        	if(formData){
        	    if (new_url.indexOf("?") !== -1) {
        			join = "&";
        		}
        		new_url += join + formData
        		console.log(formData)
        	}
        	if(keyword){
        		if (new_url.indexOf("?") !== -1) {
        			join = "&";
        		}
        		new_url += join + "keyword="+keyword
        	}
        	buildTable(table,new_url)
			var loadUrl = "/admin/sc/Person/load?cate=person";
			if(formData){
				if (loadUrl.indexOf("?") !== -1) {
					join = "&";
				}
				loadUrl += join + formData
			}
			$.get(loadUrl,function(res){
				var total = res.data
				var rkfb = {
					categories: total.rkfb.group_name,
					name: "人数",
					data: total.rkfb.group_value
				};
				var syrk = {
					name:"实有人口",
					title: total.rkfl.syrk+"人",
					per:1
				};
				var czrk = {
					name:"常驻人口",
					title: total.rkfl.czrk+"人",
					per:(total.rkfl.czrk/total.rkfl.syrk).toFixed(2)
				};
				var ldrk = {
					name:"流动人口",
					title: total.rkfl.ldrk+"人",
					per:(total.rkfl.ldrk/total.rkfl.syrk).toFixed(2)
				};
				var zlrk = {
					name:"暂离人口",
					title: total.rkfl.zlrk+"人",
					per:(total.rkfl.zlrk/total.rkfl.syrk).toFixed(2)
				};
				var hklx = total['hklx'];
				var edu = {
					name: "人数",
					categories: total.edu.category,
					data: total.edu.value,
				};
				showCharts('czrk', 1, czrk);
				showCharts('ldrk', 1, ldrk);
				showCharts('zlrk', 1, zlrk);
				showCharts('syrk', 1, syrk);
				showECharts('rkfb', 4, rkfb);
				showECharts('hklx', 6, hklx);
				showECharts('edu', 7, edu);
			})
        });
        $("#search").click();
		table.on('row(lay-tableList)', function(obj){
			var data = obj.data; // 获取当前行的数据
			var content = "/admin/sc/person/personInfo?id="+data.id;
			layer.open({
				type: 2, // 页面层
				title: '', // 不带标题栏
				area: ['80%', '80%'], // 窗口宽高
				content: content,
				offset: ['10%', '10%']
			});
		});
		$(".rkxx-l-t").on("click",function(){
			$(this).siblings(".rkxx-l-d").slideToggle(300);
		})
	})

	function nameFormatter(name) {
		let chars = name.split('');
		let wrappedChars = chars.map(char => `<span>${char}</span>`);
		return wrappedChars.join('');
	}

	function buildTable(table,url){
		var isTable = table.render({
			elem: "#lay-tableList"
			,url: url
			,cellMinWidth: 120
			,page: true
			,limit: 50,limits: [50, 100, 200, 500]
			,height: 'full-210' //设置表格高度，减去固定的底部高度
			,cols: [[
				{field:'name',align: 'left',width:180,title:'{:__("姓名")}', templet: function(d){
						let name = '';
						let tag = '';
						if(d.name.indexOf('<')>=0){
							name = d.name.substr(0, d.name.indexOf('<'))
							tag = d.name.substr(d.name.indexOf('<'))
						} else {
							name = d.name;
						}
						console.log('name', name)
						console.log('tag', tag)
						return '<div style="display:flex;justify-content:space-between;align-items:center;"><div style="width: 50%;display:flex;justify-content:space-between;align-items:center;">'+nameFormatter(name)+'</div><div style="width:50%;display:flex;align-items:center;padding-left:3px;padding-top:2px;">'+tag+'</div></div>';
					}},
				{field:'id_code',width: 80,title:'{:__("年龄")}',templet:function(d){
						return d.age
					}},
				{field:'sex',width: 80,title:'{:__("性别")}',templet:function(d){
						return d.sex==1?'男':'女'
					}},
				{field: 'community_id', title: '{:__("所在区域")}',templet:function(d){
						return d.community_id>0?d.community_name+","+d.grid_group_name+","+d.grid_name:''
					}},
				{field: 'building_name',width:200,title: '{:__("所住建筑")}'},
				{field:'address',title:'{:__("房屋地址")}'},
				{field:'nation',width:160,title:'{:__("民族")}',templet:function(d){
						var resData = {:json_encode($nation)};
						return d.nation > 0 ?resData[d.nation]:''
					}},
				{field:'face',width:160,title:'{:__("政治面貌")}',templet:function(d){
						var resData = {:json_encode($face)};
						return d.face > 0 ?resData[d.face]:''
					}},
				{field:'education',width:160,title:'{:__("学历")}',templet:function(d){
						var resData = {:json_encode($education)};
						return d.education > 0 ?resData[d.education]:''
					}},
			]]
		})
	}
</script>