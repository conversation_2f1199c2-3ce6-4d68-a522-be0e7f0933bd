<include file="sc/public/header" />
<!-- 头部 begin -->
<include file="sc/public/top" />
<!-- 头部 end -->

<div class="wjgl-main flex flex-col flex-wrap">
	<div class="wjgl-item">
		<div class="box">
			<div class="box-t"></div>
			<div class="box-m">
				<div class="title-2" style="color:red">一级文件</div>
				<ul>
					<volist name="list[0]" id="vo">
						<li><a href="javascript:void(0);" class="more" data-url="{:url('sc/scFile/edit',['id'=>$vo['id']])}">{$vo.title}</a><span>{:date('Y年-m月-d日',strtotime($vo['add_time']))}</span></li>
					</volist>
				</ul>
				<div class="wjgl-item-d flex flex-col flex-row-c">
					<div class="wjgl-item-d-l"></div>
					<div class="wjgl-item-d-r"><a href="javascript:void(0)" class="more" data-url="{:url('sc/scFile/index',['level'=>1])}">更多>></a></div>
				</div>
			</div>
			<div class="box-d"></div>
		</div>
	</div>
	<div class="wjgl-item">
		<div class="box">
			<div class="box-t"></div>
			<div class="box-m">
				<div class="title-2" style="color:yellow">二级文件</div>
				<ul>
					<volist name="list[1]" id="vo">
						<li><a href="javascript:void(0);" class="more" data-url="{:url('sc/scFile/edit',['id'=>$vo['id']])}">{$vo.title}</a><span>{:date('Y年-m月-d日',strtotime($vo['add_time']))}</span></li>
					</volist>
				</ul>
				<div class="wjgl-item-d flex flex-col flex-row-c">
					<div class="wjgl-item-d-l"></div>
					<div class="wjgl-item-d-r"><a href="javascript:void(0)" class="more" data-url="{:url('sc/scFile/index',['level'=>2])}">更多>></a></div>
				</div>
			</div>
			<div class="box-d"></div>
		</div>
	</div>
	<div class="wjgl-item">
		<div class="box">
			<div class="box-t"></div>
			<div class="box-m">
				<div class="title-2" style="color:white">三级文件</div>
				<ul>
					<volist name="list[2]" id="vo">
						<li><a href="javascript:void(0);" class="more" data-url="{:url('sc/scFile/edit',['id'=>$vo['id']])}">{$vo.title}</a><span>{:date('Y年-m月-d日',strtotime($vo['add_time']))}</span></li>
					</volist>
				</ul>
				<div class="wjgl-item-d flex flex-col flex-row-c">
					<div class="wjgl-item-d-l"></div>
					<div class="wjgl-item-d-r"><a href="javascript:void(0)" class="more" data-url="{:url('sc/scFile/index',['level'=>3])}">更多>></a></div>
				</div>
			</div>
			<div class="box-d"></div>
		</div>
	</div>
	<div class="wjgl-item">
		<div class="box">
			<div class="box-t"></div>
			<div class="box-m">
				<div class="title-2">预警处理</div>
				<ul>
					<volist name="list.day" id="vo">
						<li><a href="javascript:void(0);">{$vo.title}</a>
						<gt name="vo.day" value="0">
							<i>{$vo.day}天</i>
							<else/>
							<i class="red">到期</i>
						</gt>
						</li>
					</volist>
				</ul>
				<div class="wjgl-item-d flex flex-col flex-row-c">
					<!--<div class="wjgl-item-d-l flex flex-col-c">-->
					<!--	<a href="">更新</a>-->
					<!--	<a href="">删除</a>-->
					<!--</div>-->
					<!--<div class="wjgl-item-d-r"><a href="">更多>></a></div>-->
				</div>
			</div>
			<div class="box-d"></div>
		</div>
	</div>
	<div class="wjgl-item">
		<div class="box">
			<div class="box-t"></div>
			<div class="box-m">
				<div class="title-2">下发详情</div>
				<div class="wjgl-item-1-t">
					本月向社区派发：
					<span>{$list['month']['total']}</span>
				</div>
				<div class="wjgl-item-1-d">
					<volist name="list.month.community" id="vo">
					    <dl>
						<dt>{$vo.community_name}</dt>
						<dd>{$vo.num}/{$list['month']['total']}</dd>
					</dl>
					</volist>
					
				</div>

				<div class="wjgl-item-d flex flex-col flex-row-c">
					<div class="wjgl-item-d-l flex flex-col-c">
						<!--<a href="">更新</a>-->
						<!--<a href="">删除</a>-->
					</div>
					<!--<div class="wjgl-item-d-r"><a href="">更多>></a></div>-->
				</div>
			</div>
			<div class="box-d"></div>
		</div>
	</div>
	<div class="wjgl-item">
		<div class="box">
			<div class="box-t"></div>
			<div class="box-m">
				<div class="title-2">完成情况</div>
				<div class="wjgl-item-2">
					<table class="table">
						<thead>
						<tr>
							<th>社区</th>
							<th>已完成派发</th>
							<th>未完成派发</th>
							<th>派发总计数</th>
						</tr>
						</thead>
						<tbody>
						    <foreach name="list.finish.community" item="item">
						        <php>
						            $file = $list['finish']['file'];
						        </php>
						        <tr>
        							<td>{$key}</td>
        							<td>
        							    <foreach name="item" item="vo">
        							        {$file[$vo]}
        							        <php>
            						            unset($file[$vo]);
            						        </php>
        							    </foreach>
        							</td>
        							<td>
        							<foreach name="file" item="vo">
        							        {$vo}
        							    </foreach>
        							</td>
        							<td>{$list['month']['total']}</td>
        						</tr>
						    </foreach>
						</tbody>
					</table>
				</div>

				<div class="wjgl-item-d flex flex-col flex-row-c">
					<!--<div class="wjgl-item-d-l flex flex-col-c">-->
					<!--	<a href="">提醒</a>-->
					<!--</div>-->
					<!--<div class="wjgl-item-d-r"><a href="">更多>></a></div>-->
				</div>
			</div>
			<div class="box-d"></div>
		</div>
	</div>
</div>

<include file="/public/footer" />
<include file="sc/public/footer" />
<script>
    layui.use(['admin','jquery','layer'], function () {
        var $ = layui.jquery;
		var layer = layui.layer;
		$(".more").on("click",function(){
		    var url = $(this).attr("data-url");
		    layer.open({
				type: 2, // 页面层
				title: '', // 不带标题栏
				area: ['80%', '80%'], // 窗口宽高
				content: url,
				offset: ['10%', '10%']
			});
		})
    })
</script>