<include file="sc/public/header" />
<include file="/sc/public/params" />
<!--<iframe src="{:url('sc/index/panorama',$param)}" frameborder="0" class="mapiframe"></iframe>-->
<!-- 头部 begin -->
<include file="sc/public/top" />
<php>
    $ids = [13,349,355,362,367,373];
	$dictionaryGroup = getDictionaryGroup($ids);
	$property_right = $dictionaryGroup[349];
	$usage_category = $dictionaryGroup[355];
	$house_type = $dictionaryGroup[362];
	$residence_nature = $dictionaryGroup[367];
	$build_types = $dictionaryGroup[13];
</php>
<div class="person-main">
	<div class="person">
		<ul class="flex flex-wrap">
			<!-- 循环 begin -->
			<li style="width: 100%">
				<div class="title"><h3>房屋分布</h3></div>
				<div class="person-item" id="fwfb"></div>
			</li>
			<li style="width: calc(50% - 20px)">
				<div class="title"><h3>产权性质</h3></div>
				<div class="person-item" id="property_right"></div>
			</li>
			<li style="width: calc(50% - 20px)">
				<div class="title"><h3>使用类别</h3></div>
				<div class="person-item" id="usage_category"></div>
			</li>
			<li style="width: calc(50% - 20px)">
				<div class="title"><h3>房屋类型</h3></div>
				<div class="person-item" id="house_type"></div>
			</li>
			<li style="width: calc(50% - 20px)">
				<div class="title"><h3>住所性质</h3></div>
				<div class="person-item" id="residence_nature"></div>
			</li>
		</ul>
	</div>
	<div class="newlist-main-1">

		<div class="newlist-filter-bg-1">
			<div class="newlist-filter">
				<form id="form" action="" >
					<ul>
						<include file="sc/public/search" />
						<li>
							<p style="width:111px;text-align:justify;text-align-last: justify;">{:__('实有建筑')}</p>
							<select id="building_id" name="building_id" lay-search  lay-filter="buildingChange" style="margin-right:10px">
								<option value="">请选择</option>
								<foreach name="Buildings" item="item">
									<option value="{$item.id}"
									<eq name="item.id" value="$data.building_id">selected</eq>
									>{$item.building_name}</option>
								</foreach>
							</select>
						</li>
						<li>
							<div class="newlist-filter-btn">展开</div>
						</li>
					</ul>
					<ul>
						<li>
							<p style="width:111px;text-align:justify;text-align-last: justify;">{:__('单元')}</p>
							<select id="unit" name="unit" lay-filter="search" style="margin-right:10px">
							</select>
						</li>
						<li>
							<p style="width:111px;text-align:justify;text-align-last: justify;">{:__('建筑类别')}</p>
							<select name="b__type" lay-search="1" lay-filter="search" style="margin-right:10px">
								<option value="">全部</option>
								<foreach name="build_types" item="vo" key="key">
									<option value="{$key}">{$vo}</option>
								</foreach>
							</select>
						</li>
						<li>
							<p style="width:111px;text-align:justify;text-align-last: justify;">{:__('产权性质')}</p>
							<select name="property_right" lay-search="1" lay-filter="search" style="margin-right:10px">
								<option value="">全部</option>
								<foreach name="property_right" item="vo" key="key">
									<option value="{$key}">{$vo}</option>
								</foreach>
							</select>
						</li>
						<li>
							<p style="width:111px;text-align:justify;text-align-last: justify;">{:__('使用类别')}</p>
							<select name="usage_category" lay-search="1" lay-filter="search" style="margin-right:10px">
								<option value="">全部</option>
								<foreach name="usage_category" item="vo" key="key">
									<option value="{$key}">{$vo}</option>
								</foreach>
							</select>
						</li>
						<li>
							<p style="width:111px;text-align:justify;text-align-last: justify;">{:__('房屋类型')}</p>
							<select name="house_type" lay-search="1" lay-filter="search" style="margin-right:10px">
								<option value="">全部</option>
								<foreach name="house_type" item="vo" key="key">
									<option value="{$key}">{$vo}</option>
								</foreach>
							</select>
						</li>
						<li>
							<p style="width:111px;text-align:justify;text-align-last: justify;">{:__('住所性质')}</p>
							<select name="residence_nature" lay-filter="search" style="margin-right:10px">
								<option value="">全部</option>
								<foreach name="residence_nature" item="vo" key="key">
									<option value="{$key}">{$vo}</option>
								</foreach>
							</select>
						</li>
						<li>
							<p style="width:111px;text-align:justify;text-align-last: justify;">{:__('是否出租')}</p>
							<select name="rent" lay-search="1" lay-filter="search" style="margin-right:10px">
								<option value="">全部</option>
								<option value="2">是</option>
								<option value="1">否</option>
							</select>
						</li>
					</ul>
				</form>
			</div>
			<div class="newlist mt20">
				<table id="lay-tableList" lay-filter="lay-tableList"></table>
			</div>
		</div>

	</div>
</div>

<include file="/public/footer" />
<include file="sc/public/footer" />
<script>
	// 历史记录存储中心 
let historyStore = [];
let historyIndex = -1;
function historyStoreAdd(url) {
	historyStore.push(url)
	historyIndex = historyStore.length - 1
}

function getHistoryIndex() {
	return historyIndex;
}
function goBack() {
	if(historyIndex >=0) {
		historyIndex --;
		window.history.back();
	}
}
</script>
<script language="JavaScript">
	var uChartsInstance = {};

	function showCharts(id,type,data){
		const canvas = document.getElementById(id);
		const ctx = canvas.getContext("2d");
		canvas.width = canvas.offsetWidth;
		canvas.height = canvas.offsetHeight;
		const opts = getOpts(type,data,canvas,ctx);
		uChartsInstance[id] = new uCharts(opts);
	}

	function showECharts(id,type,data){
		var myCharts = echarts.init(document.getElementById(id), 'dark');
		const opts = getOpts(type,data);
		myCharts.setOption(opts);
		myCharts.on('click', function (params) {
			toDetail(id,params);
		});
	}
</script>
<script>
	layui.use(['admin','table','layer'], function () {
		var table = layui.table;
		var layer = layui.layer;
		var url = "{:url('house/index')}"
		$('#form').on('change', 'select, input[type="checkbox"]', function() {
            $("#search").click();
        });
        		
        $("#search").on("click",function(){
            var join = "?"
        	var new_url = url;
            var formData = $('#form').serializeArray();
            var filteredData = formData.filter(function(item) {
                return item.value !== '';
            });
            for(key in filteredData){
                filteredData[key] = filteredData[key]['name']+"="+filteredData[key]['value']
            }
            formData = filteredData.join("&")
            
        	var keyword = $("#keyword").val();
        	if(formData){
        	    if (new_url.indexOf("?") !== -1) {
        			join = "&";
        		}
        		new_url += join + formData
        		console.log(formData)
        	}
        	if(keyword){
        		if (new_url.indexOf("?") !== -1) {
        			join = "&";
        		}
        		new_url += join + "keyword="+keyword
        	}
        	buildTable(table,new_url)
			var loadUrl = "/admin/sc/Person/load?cate=house";
			if(formData){
				if (loadUrl.indexOf("?") !== -1) {
					join = "&";
				}
				loadUrl += join + formData
			}
			$.get(loadUrl,function(res){
				var total = res.data
				var fwfb = {
					categories: total.fwfb.group_name,
					name: "房屋数量",
					data: total.fwfb.group_value
				};
				var property_right = {
					name: "户数",
					categories: total.property_right.group_name,
					data: total.property_right.group_value,
					color:['#4264fb','#783CFA']
				};
				var usage_category = {
					name: "户数",
					categories: total.usage_category.group_name,
					data: total.usage_category.group_value,
				};
				var residence_nature = {
					name: "户数",
					categories: total.residence_nature.group_name,
					data: total.residence_nature.group_value,
					color:['#f3e02b','#f6b51b']
				};
				var house_type = {
					name: "户数",
					categories: total.house_type.group_name,
					data: total.house_type.group_value,
				};
				showECharts('fwfb', 4, fwfb);
				showECharts('usage_category', 7, usage_category);
				showECharts('property_right', 4, property_right);
				showECharts('residence_nature', 4, residence_nature);
				showECharts('house_type', 8, house_type);
			})
        });
		$("#search").click();
		table.on('row(lay-tableList)', function(obj){
			var data = obj.data; // 获取当前行的数据
			var content = "/admin/sc/person/house?id="+data.id;
			layer.open({
				type: 2, // 页面层
				title: '', // 不带标题栏
				area: ['80%', '80%'], // 窗口宽高
				content: content,
				offset: ['10%', '10%']
			});
		});
	})
	function buildTable(table,url){
		var isTable = table.render({
			elem: "#lay-tableList"
			,url: url
			,cellMinWidth: 160
			,page: true
			,limit: 50,limits: [50, 100, 200, 500]
			,height: 'full-210' //设置表格高度，减去固定的底部高度
			,cols: [[
				{field: 'community_id',width: 320, title: '{:__("所在区域")}',templet:function(d){
						return d.community_id>0?d.community_name+","+d.grid_group_name+","+d.grid_name:''
					}},
				{field: 'building_name',width:160, title: '{:__("所在建筑")}'},
				{field: 'unit',width:100, title: '{:__("单元楼层")}',templet:function(d){
						return d.unit+"单元,"+d.floor+"楼"
					}},
				{field: 'code',width:200, title: '{:__("门牌号")}', templet: function(d){
				// 		return d.code;
				        return '<div style="text-align: left;">' + d.code + '</div>';
					}},
				{field: 'area',width:80, title: '{:__("房屋面积")}',templet:function(d){
						return d.area+"㎡"
					}},
				{field: 'property_right',width:80, title: '{:__("产权性质")}',templet:function(d){
						var resData = {:json_encode($property_right)};
						return d.property_right > 0 ?resData[d.property_right]:''
					}},
				{field: 'usage_category',width:80, title: '{:__("使用类别")}',templet:function(d){
						var resData = {:json_encode($usage_category)};
						return d.usage_category > 0 ?resData[d.usage_category]:''
					}},
				{field: 'house_type',width:80, title: '{:__("房屋类型")}',templet:function(d){
						var resData = {:json_encode($house_type)};
						return d.house_type > 0 ?resData[d.house_type]:''
					}},
				{field: 'residence_nature',width:80, title: '{:__("住所性质")}',templet:function(d){
						var resData = {:json_encode($residence_nature)};
						return d.residence_nature > 0 ?resData[d.residence_nature]:''
					}},
				{field: 'rent', title: '{:__("是否出租")}',templet:function(d){
						return d.rent==1?'是':'否'
					}},
			]]
		})
	}
</script>