<include file="sc/public/header" />
<!-- 头部 begin -->
<include file="sc/public/top" />
<!-- 头部 end -->

<div class="wjgl-main flex flex-col flex-wrap">
	<empty name="admin_info.grid_group_id">
		<div class="wjgl-item">
			<div class="box">
				<div class="box-t"></div>
				<div class="box-m">
					<div class="title-2">一级文件</div>
					<ul>
						<volist name="list[0]" id="vo">
							<li><a href="javascript:void(0);" class="more" data-url="{:url('sc/scFile/edit',['id'=>$vo['id']])}">{$vo.title}</a><span>{:date('Y年-m月-d日',strtotime($vo['add_time']))}</span></li>
						</volist>
					</ul>
					<div class="wjgl-item-d flex flex-col flex-row-c">
						<div class="wjgl-item-d-l"></div>
						<div class="wjgl-item-d-r"><a href="javascript:void(0)" class="more" data-url="{:url('sc/scFile/index',['level'=>1])}">更多>></a></div>
					</div>
				</div>
				<div class="box-d"></div>
			</div>
		</div>
	</empty>
	<empty name="admin_info.grid_id">
		<div class="wjgl-item">
			<div class="box">
				<div class="box-t"></div>
				<div class="box-m">
					<div class="title-2">二级文件</div>
					<ul>
						<volist name="list[1]" id="vo">
							<li><a href="javascript:void(0);" class="more" data-url="{:url('sc/scFile/edit',['id'=>$vo['id']])}">{$vo.title}</a><span>{:date('Y年-m月-d日',strtotime($vo['add_time']))}</span></li>
						</volist>
					</ul>
					<div class="wjgl-item-d flex flex-col flex-row-c">
						<div class="wjgl-item-d-l"></div>
						<div class="wjgl-item-d-r"><a href="javascript:void(0)" class="more" data-url="{:url('sc/scFile/index',['level'=>2])}">更多>></a></div>
					</div>
				</div>
				<div class="box-d"></div>
			</div>
		</div>
	</empty>
	<div class="wjgl-item">
		<div class="box">
			<div class="box-t"></div>
			<div class="box-m">
				<div class="title-2">三级文件</div>
				<ul>
					<volist name="list[2]" id="vo">
						<li><a href="javascript:void(0);" class="more" data-url="{:url('sc/scFile/edit',['id'=>$vo['id']])}">{$vo.title}</a><span>{:date('Y年-m月-d日',strtotime($vo['add_time']))}</span></li>
					</volist>
				</ul>
				<div class="wjgl-item-d flex flex-col flex-row-c">
					<div class="wjgl-item-d-l"></div>
					<div class="wjgl-item-d-r"><a href="javascript:void(0)" class="more" data-url="{:url('sc/scFile/index',['level'=>3])}">更多>></a></div>
				</div>
			</div>
			<div class="box-d"></div>
		</div>
	</div>
</div>

<include file="/public/footer" />
<include file="sc/public/footer" />
<script>
    layui.use(['admin','jquery','layer'], function () {
        var $ = layui.jquery;
		var layer = layui.layer;
		$(".more").on("click",function(){
		    var url = $(this).attr("data-url");
		    layer.open({
				type: 2, // 页面层
				title: '', // 不带标题栏
				area: ['80%', '80%'], // 窗口宽高
				content: url,
				offset: ['10%', '10%']
			});
		})
		var title = '{$title}';
		var fontSize = "40px";
		if(title.length<10){
			fontSize = "36px";
		}else if(title.length<12){
			fontSize = "32px";
		}else if(title.length<14){
			fontSize = "26px";
		}else{
			fontSize = "20px";
		}
		$(".header-m-t").css("font-size",fontSize);
    })
</script>