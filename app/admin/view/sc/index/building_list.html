<include file="sc/public/header" />
<style>
	.rkxx-main{height:calc(100% - 226px);  top:176px;}
    .person{height: 480px;}
    .person li{height: calc(100% - 20px);}
</style>

<!--<iframe src="{:url('sc/index/panorama',$param)}" frameborder="0" class="mapiframe"></iframe>-->
<!-- 头部 begin -->
<include file="sc/public/top" />
<!-- 头部 end -->
<include file="/sc/public/params" />

<div class="person-main">
    <div class="person">
        <ul class="flex flex-wrap">
            <!-- 循环 begin -->
            <li style="width: 100%">
                <div class="title"><h3>建筑类别</h3></div>
                <div class="person-item" id="total"></div>
            </li>
        </ul>
    </div>
    <div class="newlist-main-1">

	<div class="newlist-filter-bg-1">
		<div class="newlist-filter">
            <form id="form">
			<ul>
                <include file="sc/public/search" />
                <li>
					<p>{:__('建筑类别')}</p>
					<select name="type" lay-search="1" lay-filter="search">
                        <option value="">全部</option>
                        <foreach name="build_types" item="vo" key="key">
                            <option value="{$key}">{$vo}</option>
                        </foreach>
                    </select>
                    <input id="floor_type" name="floor_type" value="{$floor_type}" type="hidden" />
				</li>
             </ul>
            </form>
		</div>
        <div class="newlist mt20">
            <table id="lay-tableList" lay-filter="lay-tableList"></table>
        </div>
	</div>

</div>
</div>

<include file="/public/footer" />
<include file="sc/public/footer" />
<script language="JavaScript">
    var uChartsInstance = {};

    function showCharts(id,type,data){
        const canvas = document.getElementById(id);
        const ctx = canvas.getContext("2d");
        canvas.width = canvas.offsetWidth;
        canvas.height = canvas.offsetHeight;
        const opts = getOpts(type,data,canvas,ctx);
        uChartsInstance[id] = new uCharts(opts);
    }

    function showECharts(id,type,data){
        var myCharts = echarts.init(document.getElementById(id), 'dark');
        const opts = getOpts(type,data);
        myCharts.setOption(opts);
        myCharts.on('click', function (params) {
            toDetail(id,params);
        });
    }
</script>
<script>
    var admin = null
	layui.use(['admin','table','layer','admin'], function () {
		var table = layui.table;
		var layer = layui.layer;
		admin = layui.admin;
		var url = "{:url('Building/index')}"
		$('#form').on('change', 'select, input[type="checkbox"]', function() {
            $("#search").click();
        });
        $("#search").on("click",function(){
            var join = "?"
        	var new_url = url;
            var formData = $('#form').serializeArray();
            var filteredData = formData.filter(function(item) {
                return item.value !== '';
            });
            for(key in filteredData){
                filteredData[key] = filteredData[key]['name']+"="+filteredData[key]['value']
            }
            formData = filteredData.join("&")
            
        	var keyword = $("#keyword").val();
        	if(formData){
        	    if (new_url.indexOf("?") !== -1) {
        			join = "&";
        		}
        		new_url += join + formData
        		console.log(formData)
        	}
        	if(keyword){
        		if (new_url.indexOf("?") !== -1) {
        			join = "&";
        		}
        		new_url += join + "keyword="+keyword
        	}
        	buildTable(table,new_url)
            var loadUrl = "/admin/sc/Person/load?cate=building";
            if(formData){
                if (loadUrl.indexOf("?") !== -1) {
                    join = "&";
                }
                loadUrl += join + formData
            }
            $.get(loadUrl,function(res){
                var total = {
                    categories: res.data.group_name,
                    name: "栋数",
                    data: res.data.group_value
                };
                showECharts('total', 4, total);
            })
        });
		$("#search").click();
		table.on('row(lay-tableList)', function(obj){
			var data = obj.data; // 获取当前行的数据
			var content = "/admin/sc/person/building?id="+data.id;
			var back = 0;
			layer.open({
				type: 2, // 页面层
				title: '建筑信息', // 不带标题栏
				area: ['100%', '100%'], // 窗口宽高
				full:true,
				content: content
				
			});
		});
	})
	function buildTable(table,url){
		var isTable = table.render({
			elem: "#lay-tableList"
			,url: url
			,cellMinWidth: 160
			,page: true
			,limit: 50,limits: [50, 100, 200, 500]
			,height: 'full-210' //设置表格高度，减去固定的底部高度
			,cols: [[
				{field:'community_name', title: '{:__("所在社区")}'},
				{field:'grid_group_name', title: '{:__("所在网格组")}'},
				{field:'grid_name', title: '{:__("所在网格")}'},
				{field: 'building_name', title: '{:__("建筑名称")}'},
				{
					field:'type',width: 120, title: '{:__("建筑类型")}', templet: function (d) {
						var resData = {:json_encode($build_types)};
						return d.type > 0 ?resData[d.type]:''
					}
				},
				{
					field: 'image',width: 120,width: 120, templet: function (d) {
						if(d.image==''){
							return '';
						}
						var album = [];
						try {
							d.image = JSON.parse(d.image);
							// onerror：如果缩率图不存在则显示原图
							album[0] = '<a href="javascript:" class="fileslink" ><img class="filesuffix" src="' + admin.toThumb(d.image[0].src) + '" onerror="this.onerror=null;this.src=&apos;'+d.image[0].src+'&apos;;"></a>'
				// 			for (var i in d.image) {
				// 				album[i] = '<a href="javascript:" class="fileslink" ><img class="filesuffix" src="' + admin.toThumb(d.image[i].src) + '"></a>'
				// 			}
							return album.join('');
						} catch (e) {
						    if(d.image){
						        // onerror：如果缩率图不存在则显示原图
						        return '<a href="javascript:" class="fileslink" ><img class="filesuffix" src="' + admin.toThumb(d.image) +  '"  onerror="this.onerror=null;this.src=&apos;'+d.image[0].src+'&apos;;"></a>'
						    }else{
						        return '';
						    }
							
						}
					}, title: '{:__("街景示意图")}'
				},
				{
					field: 'unit',  title: '{:__("单元数-地上层数-地下层数")}', templet: function (d) {
						return d.unit + "-" + d.floor + "-" + d.floor_under
					}
				},
				{field:'address', title: '{:__("建筑位置")}'},
				{
					field:'street_name', title: '{:__("街")}', templet: function (d) {
						var street = "";
						if (d.street_name) {
							street += d.street_name + d.street_start + "-" + d.street_end;
						}
						if (d.street_sub) {
							street += " " + d.street_sub;
						}
						return street;
					}
				},
				{
					field:'road_name', templet: function (d) {
						var road = "";
						if (d.road_name) {
							road += d.road_name + d.road_start + "-" + d.road_end;
						}
						if (d.road_sub) {
							road += " " + d.road_sub;
						}
						return road;
					}, title: '{:__("路")}'
				},
			]]
		})
	}
</script>