<include file="sc/public/header" />
<!-- 头部 begin -->
<include file="sc/public/top" />
<!-- 头部 end -->
<php>
	$admin_id = get_admin_id();
	$meeting_host = "https://*************/meeting/#/room"; 
</php>
<div class="sphy-main">
	<div class="sphy-t flex flex-col content-top">
		<div class="sphy-t-l item">
			<div class="box">
				<div class="box-t"></div>
				<div class="box-m">
					<div class="title-2">会议记录</div>
					<ul>
						<foreach name="list" item="item">
							<li class="detail" data-url="{:url('sc/meeting/detail',['id'=>$item['id']])}"><a href="javascript:void(0)">{$item.title}</a><span>{$item.start_date}</span></li>
						</foreach>
					</ul>
					<!-- <div class="sphy-t-l-d flex flex-col flex-row-c">
						<div class="sphy-t-l-d-l">
							<a href="javascript:void(0)"><span class="iconfont icon-yinpinwenjian"></span>音频转换文件</a>
							<a href="javascript:void(0)"><span class="iconfont icon-gongzuorizhi"></span>文件导出</a>
						</div>
						<div class="sphy-t-l-d-r"><a href="javascript:void(0)">更多>></a></div>
					</div> -->
				</div>
				<div class="box-d"></div>
			</div>
		</div>
		<div class="sphy-t-m item">
			<div class="box">
				<div class="box-t"></div>
				<div class="box-m flex flex-column flex-col flex-row-c">
					<div class="image"><img src="/static/sc/images/avatar.jpg" alt=""></div>
					<p id="add" data-url="{:url('/Meeting/add')}">预约会议</p>
				</div>
				<div class="box-d"></div>
			</div>
		</div>
		<!--<if ($wait0) >-->
		<!--	<div class="sphy-t-r item">-->
		<!--		<div class="box" style="position: relative;">-->
		<!--			<div class="box-t"></div>-->
		<!--			<div class="box-m">-->
		<!--				<div class="title-2">会议流程</div>-->
		<!--				<div class="sphy-t-r-d " style="overflow: hidden; border-box;padding: 20px;max-height: 230px;color: #00fcff;font-size: 24px;">-->
		<!--					<div>会议标题：{$wait0.title}</div>-->
		<!--					<div>会议时间：{$wait0.start_date}</div>-->
		<!--					<div class="item-detail">{:htmlspecialchars_decode($wait0['details'])}</div>-->
							
		<!--				</div>-->
		<!--			</div>-->
		<!--			<div class="box-d"></div>-->
		<!--			<div class="look-details" data-url="{:url('sc/meeting/detail',['id'=>$wait0['id']])}"><a href="javascript:void(0)">详情</a></div>-->
		<!--			<eq name="wait0.type" value="1">-->
		<!--				<a class="faqihuiyi" target="_blank" href="{$meeting_host}?roomId={$wait0['room_id']}&userName={$wait0['nickname']}&compereId={$wait0['admin_name']}" >-->
		<!--					发起会议-->
		<!--			<else/>-->
		<!--				<a class="faqihuiyi" target="_blank" href="{$meeting_host}?roomId={$wait0['room_id']}&userName={$wait0['nickname']}&compereId={$wait0['admin_name']}" >-->
		<!--					加入会议-->
		<!--			</eq>-->
		<!--			</a>-->
		<!--		</div>-->
		<!--	</div>-->
		<!--</if>-->
		<if ($wait0) >
			<div class="sphy-t-r item">
				<div class="box" style="position: relative;">
					<div class="box-t"></div>
					<div class="box-m">
						<div class="title-2">会议流程</div>
						<div class="sphy-t-r-d " style="overflow: hidden; border-box;padding: 20px;max-height: 230px;color: #00fcff;font-size: 24px;">
							<div>会议标题：{$wait0.title}</div>
							<div>会议时间：{$wait0.start_date}</div>
							<div class="item-detail">{:htmlspecialchars_decode($wait0['details'])}</div>
							
						</div>
					</div>
					<div class="box-d"></div>
					<div class="look-details" data-url="{:url('sc/meeting/detail',['id'=>$wait0['id']])}"><a href="javascript:void(0)">详情</a></div>
					<!-- <eq name="wait0.type" value="1">
						<a class="faqihuiyi" target="_blank" href="{$meeting_host}?roomId={$wait0['room_id']}&userName={$wait0['unit_name']}&compereId={$wait0['aunit_name']}" >
							发起会议
					<else/>
						<a class="faqihuiyi" target="_blank" href="{$meeting_host}?roomId={$wait0['room_id']}&userName={$wait0['unit_name']}&compereId={$wait0['aunit_name']}" >
							加入会议
					</eq> -->
					<a class="faqihuiyi" target="_blank" href="/admin/sc/meeting/joinRoom?roomId={$wait0['room_id']}" >
						<eq name="wait0.type" value="1">
							发起会议
						<else/>
							加入会议
						</eq>
					</a>
				</div>
			</div>
		</if>
	</div>
	<!--<div class="sphy-bottom">-->
	<!--	<foreach name="wait" item="witem">-->
	<!--		<div class="sphy-t-r sphy-bottom-item">-->
	<!--			<div class="box">-->
	<!--				<div class="box-t"></div>-->
	<!--				<div class="box-m">-->
	<!--					<div class="wtitle-3">会议流程</div>-->
	<!--					<div class="sphy-t-r-d " style="overflow: hidden;box-sizing: border-box;padding: 20px;max-height: 130px;color: #00fcff;font-size: 16px;">-->
	<!--					 	<div>会议标题：{$witem.title}</div>-->
	<!--						<div>会议时间：{$witem.start_date}</div>-->
	<!--						<div class="tem-detail">{:htmlspecialchars_decode($witem['details'])}</div>-->
							
	<!--					</div>-->
	<!--				</div>-->
	<!--				<div class="box-d"></div>-->
	<!--				<div class="look-details" style="top: 10px;" data-url="{:url('sc/meeting/detail',['id'=>$witem['id']])}"><a href="javascript:void(0)">详情</a></div>-->
	<!--				<eq name="witem.type" value="1">-->
	<!--					<a href="{$meeting_host}?roomId={$witem['room_id']}&userName={$witem['nickname']}&compereId={$witem['admin_name']}" -->
	<!--					target="_blank" class="faqihuiyi2">-->
	<!--						发起会议-->
	<!--				<else/>-->
	<!--					<a href="{$meeting_host}?roomId={$witem['room_id']}&userName={$witem['nickname']}&compereId={$witem['admin_name']}" target="_blank" class="faqihuiyi2">-->
	<!--						加入会议-->
	<!--				</eq>-->
	<!--				</a>-->
	<!--			</div>-->
	<!--		</div>-->
	<!--	</foreach>-->
	<!--</div>-->
	<div class="sphy-bottom">
		<foreach name="wait" item="witem">
			<div class="sphy-t-r sphy-bottom-item">
				<div class="box">
					<div class="box-t"></div>
					<div class="box-m">
						<div class="wtitle-3">会议流程</div>
						<div class="sphy-t-r-d " style="overflow: hidden;box-sizing: border-box;padding: 20px;max-height: 130px;color: #00fcff;font-size: 16px;">
						 	<div>会议标题：{$witem.title}</div>
							<div>会议时间：{$witem.start_date}</div>
							<div class="tem-detail">{:htmlspecialchars_decode($witem['details'])}</div>
							
						</div>
					</div>
					<div class="box-d"></div>
					<div class="look-details" style="top: 10px;" data-url="{:url('sc/meeting/detail',['id'=>$witem['id']])}"><a href="javascript:void(0)">详情</a></div>
					<!-- <eq name="witem.type" value="1">
						<a class="faqihuiyi2" target="_blank" href="{$meeting_host}?roomId={$witem['room_id']}&userName={$witem['unit_name']}&compereId={$witem['aunit_name']}" >
							发起会议
					<else/>
						<a class="faqihuiyi2" target="_blank" href="{$meeting_host}?roomId={$witem['room_id']}&userName={$witem['unit_name']}&compereId={$witem['aunit_name']}" >
							加入会议
					</eq> -->
					<!-- </a> -->
					<a class="faqihuiyi2" target="_blank" href="/admin/sc/meeting/joinRoom?roomId={$witem['room_id']}" >
						<eq name="witem.type" value="1">
							发起会议
						<else/>
							加入会议
						</eq>
					</a>
				</div>
			</div>
		</foreach>
	</div>
</div>
</div>

<style>
	/* .title-3{width:150px; height:40px; display:flex; align-items:center; justify-content:center; font-size:24px; color:#00fcff; margin:0 auto; background:url(../images/title-2.png) no-repeat; padding-bottom:6px;} */
	.look-details{
		color: #00fcff;
		/* border: 1px solid #00fcff; */
		position: absolute;
		right: 20px;
		top: 20px;
		font-size: 18px;
		z-index: 100;
	}
	.look-details a{
		color: #00fcff;
	}
	.look-details a:hover{
		color: #00fcff;
	}
html,body{
	overflow: visible;
}
body::-webkit-scrollbar {
	display: none;
}
.content-top{
	display: grid;
	gap: 30px;
	grid-template-columns: repeat(3, 1fr);
}
.content-top .item {
	height: 400px;
	min-width: 200px;
	width: auto;
	max-width: 100%;
	position: relative;
	}
.sphy-bottom{
	margin-top: 30px;
	display: grid;
	gap: 30px;
	grid-template-columns: repeat(6, 1fr);

}
.sphy-bottom-item{
	aspect-ratio: 1 / 0.85;
	width: 100%;
	height: auto;
}
.faqihuiyi:hover,
.faqihuiyi2:hover{
	color: #00fcff;
}
.faqihuiyi{
    text-decoration: none; /* 去除下划线 */
    background-color: transparent; /* 背景透明 */
    font-weight: normal; /* 字体正常 */
    font-size: inherit; /* 继承父元素的字体大小 */
    padding: 0; /* 去除内边距 */
    margin: 0; /* 去除外边距 */
    border: none;
	position: absolute;
	bottom: 40px;
	left: 50%;
	font-size: 38px;
	color: #00fcff;
	display: block;
	width: 360px;
    height: 60px;
	line-height: 60px;
	text-align: center;
	border: 1px solid #00fcff;
	border-radius: 10px;
	transform: translateX(-50%);
	z-index: 99;
}
.faqihuiyi2{
	color: inherit; /* 继承父元素的文字颜色 */
    text-decoration: none; /* 去除下划线 */
    background-color: transparent; /* 背景透明 */
    font-weight: normal; /* 字体正常 */
    font-size: inherit; /* 继承父元素的字体大小 */
    padding: 0; /* 去除内边距 */
    margin: 0; /* 去除外边距 */
    border: none;
	position: absolute;
	bottom: 10px;
	left: 50%;
	font-size: 16px;
	color: #00fcff;
	display: block;
	width: 160px;
	box-sizing: border-box;
    height: 40px;
	line-height: 40px;
	text-align: center;
	border: 1px solid #00fcff;
	border-radius: 10px;
	transform: translateX(-50%);
	z-index: 99;
	
}
.item-detail{
	font-size: 24px;
	color: #00fcff;
}
</style>

<include file="/public/footer" />
<include file="sc/public/footer" />
<script>
	layui.use(['admin','jquery','layer'], function () {
		var $ = layui.jquery;
		var layer = layui.layer;
		$(".detail").on("click",function(){
			layer.open({
				type: 2, // 页面层
				title: '', // 不带标题栏
				area: ['80%', '80%'], // 窗口宽高
				content: $(this).attr("data-url")
			});
		});
		$(".look-details").on("click",function(){
			console.log('look-details')
			layer.open({
				type: 2, // 页面层
				title: '', // 不带标题栏
				area: ['80%', '80%'], // 窗口宽高
				content: $(this).attr("data-url")
			});
		});
		$("#add").on("click",function(){
			var url = $(this).attr("data-url");
			console.log(url);
			layer.open({
				type: 2, // 页面层
				title: '', // 不带标题栏
				area: ['80%', '80%'], // 窗口宽高
				content: url,
				offset: ['10%', '10%']
			});
		})
	})
</script>