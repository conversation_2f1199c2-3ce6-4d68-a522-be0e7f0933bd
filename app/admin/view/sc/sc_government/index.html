<include file="/public/header" />
<php>
    $type = getDictionary(554);
</php>
<!---->

<div class="layui-fluid">

    <div class="layui-card">

        <!-- // 默认操作按钮 -->

        <div class="layui-card-header layadmin-card-header-auto ">

            <div class="layui-form">
                <div class="layui-form-item">
                    
                <include file="/public/search" />
                <div class="layui-inline">

                    <div class="layui-form-label">{:__('栏目')}</div>

                    <div class="layui-input-inline ">
                        <select name="type" lay-filter="search">
                            <option value="">请选择</option>
                            <foreach name="type" item="vo">
                                    <option value="{$vo}">{$vo}</option>
                            </foreach>
                        </select>
                    </div>

                </div>
                <div class="layui-inline">

                    <div class="layui-form-label">{:__('标题')}</div>

                    <div class="layui-input-inline ">

                    <input name="title" class="layui-input" type="text" placeholder="{:__('标题')}"/>
                    </div>

                </div>

                    <div class="layui-inline" >

                        <!-- // 默认搜索 -->

                        <button  id="search" class="layui-btn icon-btn" lay-filter="formSearch" lay-submit><i class="layui-icon layui-icon-search"></i>{:__('搜索')}</button>

                        <!--formBegin-->

                        <button class="layui-btn icon-btn" lay-open="" data-title="{:__('添加')}" data-area="1100px,750px" data-maxmin="true" data-url="{:url('/sc/ScGovernment/add')}" >

                            <i class="layui-icon layui-icon-add-1"></i>{:__('添加')}

                        </button>

                        <!--formEnd-->

                    </div>

                </div>

            </div>   

        </div>

        <!-- // 创建数据实例 -->

        <table id="lay-tableList" lay-filter="lay-tableList"></table>        

    </div>

</div>



<!-- // 列表状态栏 -->

<script type="text/html" id="columnStatus">

    <input type="checkbox" lay-filter="switchStatus" data-url="{:url('/sc/ScGovernment/status')}" value="{{d.id}}" lay-skin="switch" {{d.top==1?'checked':''}}  />
</script>

 

<!-- // 列表工具栏 -->

<script type="text/html" id="tableBar">

    <!--formBegin-->

    <a class="layui-table-text" data-title="{:__('查看')}" data-area="100%,100%" data-maxmin="true"

        data-url="{:url('/sc/ScGovernment/showdetial')}?id={{d.id}}" lay-event="edit" >{:__('查看')}</a>

    <div class="layui-divider layui-divider-vertical"></div>
    <a class="layui-table-text" data-title="{:__('编辑')}" data-area="1100px,750px" data-maxmin="true"

        data-url="{:url('/sc/ScGovernment/edit')}?id={{d.id}}" lay-event="edit" >{:__('编辑')}</a>

    <div class="layui-divider layui-divider-vertical"></div>

    <!--formEnd-->

    <a class="layui-table-text"  data-url="{:url('/sc/ScGovernment/del')}?id={{d.id}}" lay-event="del" >{:__('删除')}</a>

</script>


<script type="text/html" id="columnTop">

    <input type="checkbox" lay-filter="switchStatus" data-url="{:url('/sc/ScGovernment/status',['field'=>'top'])}" value="{{d.id}}" lay-skin="switch" {{d.status==1?'checked':''}}  />

</script>




<script type="text/html" id="tableButton"></script>



<include file="/public/footer" />
<include file="/public/select" />

<script>

    layui.use(['admin','table'], function () {



        var admin = layui.admin;

        var table = layui.table;



        /*

         * 初始化表格

        */

        var isTable = table.render({

            elem: "#lay-tableList"

            ,url: "{:url('/sc/ScGovernment/index')}"

            ,toolbar: '#tableButton'

            ,defaultToolbar: ['filter', 'exports', 'print','search']

            ,cellMinWidth:160
            ,height: 'full-150' //设置表格高度，减去固定的底部高度

            ,page: true

            ,limit: 50,limits: [50, 100, 200, 500]

            ,cols: [[

                {type: 'checkbox', width: 50},

               
                {field: 'type',width:240,title:'{:__("栏目")}'},
                {field: 'community_name',width:120,title:'{:__("所属社区")}'},
                {field: 'grid_group_name',width:120,title:'{:__("所属网格组")}'},
                {field: 'grid_name',width:160,title:'{:__("所属网格")}'},
                {field:'title',width:450,title:'{:__("标题")}',templet:function(d){
                        if(d.top){
                            d.title = d.title+"<span class=\"layui-badge layui-bg-blue\">置顶</span>";
                        }
                        d.status = d.status == 1?"<span class=\"layui-badge layui-bg-green\">已审</span>":'<span class="layui-badge layui-bg-orange" lay-tips="审核通过后才能在前台显示！">未审</span>';
                        return d.title += d.status;
                    }},
                {field:'top',width:80,title:'{:__("置顶")}',templet: '#columnTop'},
                {field:'thumb',templet:function(d) {
                
                    return '<a href="javascript:" target="_blank" ><img class="filesuffix" lay-image-click src="'+d.thumb+'"></a>';
                
                },title:'{:__("封面")}'},
                {field: 'image',width: 120,templet:function(d) {
                
                    if(d.image==''){
                        return '';
                    }
                    var album = [];
                    try {
                        d.image = JSON.parse(d.image);
                        for (var i in d.image) {
                            album[i] = '<a href="javascript:" class="fileslink" ><img class="filesuffix" lay-image-click src="' + admin.toThumb(d.image[i].src) + '"></a>'
                        }
                        return album.join('');
                    } catch (e) {
                        return '<a href="javascript:" class="fileslink" ><img class="filesuffix" lay-image-click src="' + admin.toThumb(d.image) +  '"></a>'
                    }
                
                },title:'{:__("图片/视频")}'},
                {field:'add_time',title:'{:__("发布时间")}'},
                {field: 'create_time',width: 160,title:'{:__("创建时间")}'},
                {field: 'create_by',width: 120,title:'{:__("创建人")}'},
                {field: 'update_time',width: 160,title:'{:__("修改时间")}'},
                {field: 'update_by',width: 120,title:'{:__("修改人")}'},
                

                {align: 'center', toolbar: '#tableBar', width:200, fixed: 'right', title: '{:__("操作")}'},

            ]]

        })



    })

</script>

