<php>
    $target = "_blank";
</php>
<div class="header flex flex-col-c">
  <div class="header-l">
    <div class="header-l-t">
      <ul class="flex flex-col-r">
      <notempty name="url">
        <li><in name="action" value="community,gridGroup,grid,building">
            <a class="active" href="javascript:void(0)" data-url="{:url('sc/index/index')}">首页</a>
            <else/>
            <php>
                $target = "_self";
            </php>
            <a class="topNavBack" href="javascript:void(0)">首页</a>
            </in>
            </li>
        <else/>
        <li><a <eq name="action" value="index">class="active"</eq> href="javascript:void(0)" >首页</a></li>
      </notempty>
      <li><a <eq name="action" value="person">class="active"</eq> href="javascript:void(0)" data-url="{:url('sc/index/person')}" _target="{$target}">人口信息</a></li>
      <li><a <eq name="action" value="buildingList">class="active"</eq> href="javascript:void(0)" data-url="{:url('sc/index/buildingList')}" _target="{$target}">建筑信息</a></li>
      <li><a <eq name="action" value="house">class="active"</eq> href="javascript:void(0)" data-url="{:url('sc/index/house')}" _target="{$target}">房屋信息</a></li>
      </ul>
    </div>
    <div class="header-l-d flex flex-col">
      <div class="weather flex flex-row-d">
        <div class="weather-l flex flex-column flex-row-c">
<!--          <img src="http://openweathermap.org/img/w/01d.png" alt="">-->
<!--          <p>建三江</p>-->
        </div>
        <div class="weather-r">
          <p id = "weather"></p>
          <p class="time" >{:date('Y-m-d h:i:s')}</p>
        </div>
      </div>
      <div class="header-l-d-r">
        <ul class="flex flex-col-r">
        <li><a href="javascript:void(0)" data-url="{:url('sc/personLabel/index')}" _target="{$target}">特殊人群</a></li>
        <li><a href="javascript:void(0)" data-url="{:url('sc/index/map')}" _target="{$target}">地标性建筑</a></li>
        </ul>
      </div>
    </div>
  </div>
  <div class="header-m flex flex-column flex-row-c">
    <div class="header-m-t flex flex-col-c">{$title}</div>
    <div class="header-m-d">
      <input id="keyword" type="text" value="{:input('keyword','')}">
      <button id="search" type="button"></button>
    </div>
  </div>
  <div class="header-r">
    <div class="header-r-t">
      <ul class="flex flex-col-l">

      <li><a <eq name="action" value="place">class="active"</eq> href="javascript:void(0)" data-url="{:url('sc/index/place')}" _target="{$target}">场所信息</a></li>
      <li><a <eq name="action" value="monitor">class="active"</eq> href="javascript:void(0)" data-url="{:url('sc/index/monitor_all')}" _target="{$target}">实时可视</a></li>
      <li class="smenu"><a <eq name="action" value="dynamics">class="active"</eq> href="javascript:void(0)" data-url="{:url('sc/index/dynamics')}" _target="{$target}">要闻动态</a>
        <ul>
          <li><a href="javascript:void(0)" data-url="{:url('sc/index/government')}" _target="{$target}">政务公开</a></li>
        </ul>
      </li>
      <li><a <eq name="action" value="file">class="active"</eq> href="javascript:void(0)" data-url="{:url('sc/index/file')}" _target="{$target}">文件管理</a></li>
      </ul>
    </div>
    <div class="header-r-d flex flex-col flex-row-d">
      <div class="header-r-d-l">
        <ul class="flex flex-col-l">
        <li><a <eq name="action" value="site">class="active"</eq> href="javascript:void(0)" data-url="{:url('sc/index/site')}" _target="{$target}">党建园地</a></li>
        <php>
          $detail_title = "网格";
          if($param['type']=='index'){
            $detail_title = "街道";
          }else if($param['type']=='community'){
            $detail_title = "社区";
          }else if($param['type']=='gridGroup'){
          $detail_title = "网格组";
          }
        </php>
        <!--<li><a class="details" href="javascript:void(0)" detail-url="{:url('sc/index/details')}">{$detail_title}简介</a></li>-->
        <li><a class="details" href="javascript:void(0)" detail-url="{:url('sc/index/details',$param)}">{$detail_title}简介</a></li>
        <li class="smenu"><a <eq name="action" value="workLink">class="active"</eq> href="javascript:void(0)" data-url="{:url('sc/index/workLink')}" _target="{$target}">部门联动</a>
          <ul>
            <li><a <eq name="action" value="meeting">class="active"</eq> href="javascript:void(0)" data-url="{:url('sc/index/meeting')}" _target="{$target}">一键会议</a></li>
          </ul>
        </li>
        </ul>
      </div>
      <!--<a class="header-r-d-r flex flex-column flex-row-c">-->
      <!--  <p class="iconfont icon-tongzhi"></p>-->
      <!--  语音播报-->
      <!--</a>-->
    </div>
  </div>
</div>

<div class="jdbgk-box" style="top:240px; left:50%; margin-left: -425px; display: none;">

</div>
<script>
  var params_type = "{$param['type'] ?? 'index'}"
  var params_id = {$param['id'] ?? 0}
</script>