<include file="sc/public/header" />
<php>
  $ids = [30,42,401];
  $dictionaryGroup = getDictionaryGroup($ids);
  $types = $dictionaryGroup[30];
  $purposes = $dictionaryGroup[42];
  $code_types = $dictionaryGroup[401];
  $params=[];
  if($param['type']!="index"){
    if($param['type']=="gridGroup"){
      $param['type'] = "grid";
    }
    $params[$param['type']."_id"]=$param['id'];
  }
</php>
<style>
    .csxx-r{width: 100%;height: 100%;overflow-y: scroll}
    .box{width:400px; height:240px;}
    .box-m .image{width:100% !important; height:240px !important;}
    .csxx-r-l .map{width:400px;}
    .map .image{width:100% !important; height:240px !important;}
    .csxx-r-r{width:calc(100% - 600px);}
    .csxx-r-r p{margin-top:20px;}
</style>
  <div class="csxx-r flex" style="width: 100%;height: 100%;overflow-y: scroll;">
    <div class="csxx-r-l">
      <php>
        if (strpos($data['path'], 'http') === 0) {
        $data['path'] = [['src'=>$data['path'],'title'=>'图片']];
        }else{
        $data['path'] = json_decode($data['path'],true);
        }
        if (strpos($data['code_path'], 'http') === 0) {
        $data['code_path'] = [['src'=>$data['code_path'],'title'=>'图片']];
        }else{
        $data['code_path'] = json_decode($data['code_path'],true);
        }
      </php>
      <!-- // 循环输出代码 -->
      <notempty name="$data['path']">
      <div class="box" style="width: 400px; height: 240px;">
        <div class="box-t"></div>
        <div class="box-m">
            <div class="image layui-carousel" id="carousel">
              <div carousel-item>
                  <foreach name="$data['path']" item="vo">
                    <div><img src="{$vo.src}" lay-image-click></div>
                  </foreach>
              </div>
            </div>
        </div>
        <div class="box-d"></div>
      </div>
      </notempty>
      <notempty name="$data['code_path']">
      <div class="map" style="width: 400px; height: 240px;">
        <div class="image layui-carousel" id="carousel-2">
          <div carousel-item>
            <foreach name="$data['code_path']" item="vo">
              <div><img src="{$vo.src}" lay-image-click></div>
            </foreach>
          </div>
        </div>
      </div>
      </notempty>
    </div>
    <div class="csxx-r-r">
      <h3>{:hide_str($data['vehicle_code'],0,3,2)}({:hide_str($data['owner'],0,1,2)})</h3>
      <div class="csxx-title">
        <span class="iconfont icon-dianmian"></span>
        车辆信息
      </div>
      <p>车辆品牌： {$data.brand}</p>
      <p>车牌号码： {:hide_str($data['vehicle_code'],0,3,2)}</p>
      <p>车辆种类： {$types[$data['type']]}</p>
      <p>车辆用途： {$purposes[$data['purpose']]}</p>
      <p>车辆牌照： {$code_types[$data['code_type']]}</p>
      <p>车主姓名： {:hide_str($data['owner'],0,1,2)} </p>
      <p>车主身份证号： {:hide_str($data['id_code'],6,8)} </p>
      <p>车主手机号码： {:hide_str($data['phone'],3,4)} </p>
      <div class="csxx-title">
        <span class="iconfont icon-map"></span>
        位置信息
      </div>
      <p>所在社区： {$data.community_name}</p>
      <p>所在网格组：{$data.grid_group_name}</p>
      <p>所在网格： {$data.grid_name}</p>
    </div>
  </div>
<include file="sc/public/footer" />
<include file="/public/footer" />
<script>
  layui.use(['admin','carousel','layphoto'], function(){
    var carousel = layui.carousel;
    // 初始化图片轮播
    carousel.render({
      elem: '#carousel', // 绑定的元素
      width: '700px', // 宽度
      height: '420px', // 高度
      arrow: 'always', // 始终显示箭头
      interval: 3000 //设置轮播时间间隔
    });
    carousel.render({
      elem: '#carousel-2', // 绑定的元素
      width: '700px', // 宽度
      height: '420px', // 高度
      arrow: 'always', // 始终显示箭头
      interval: 3000 //设置轮播时间间隔
    });
  });
</script>