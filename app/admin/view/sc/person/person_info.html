<include file="sc/public/header" />
<php>
  $ids = [404,408,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,
  411,418,522,465,471,478,489,30,42,401];
  $dictionaryGroup = getDictionaryGroup($ids);
  $rpr = $dictionaryGroup[404];
  $rpr_nature = $dictionaryGroup[408];
  $relation = $dictionaryGroup[53];
  $nation = $dictionaryGroup[54];
  $religion = $dictionaryGroup[55];
  $education = $dictionaryGroup[56];
  $enlistment = $dictionaryGroup[57];
  $face = $dictionaryGroup[58];
  $person_type = $dictionaryGroup[59];
  $house_type = $dictionaryGroup[60];
  $health = $dictionaryGroup[61];
  $blood = $dictionaryGroup[62];
  $labor_capacity = $dictionaryGroup[63];
  $marry = $dictionaryGroup[64];
  $only_child = $dictionaryGroup[65];
  $other_child = $dictionaryGroup[66];
  $endowment_insurance = $dictionaryGroup[67];
  $insurance = $dictionaryGroup[68];
  $representative = $dictionaryGroup[69];
  $elderly = $dictionaryGroup[70];
  $dilemma = $dictionaryGroup[71];
  $dilemma_children = $dictionaryGroup[72];
  $patient = $dictionaryGroup[73];
  $bad = $dictionaryGroup[74];
  $other = $dictionaryGroup[75];
  $disabilityType = $dictionaryGroup[411];
  $disabilityLevel = $dictionaryGroup[418];
  $employment = $dictionaryGroup[522];
  $employment_before = $dictionaryGroup[76];
  $employment_form = $dictionaryGroup[77];
  $employment_unit_property = $dictionaryGroup[78];
  $employment_difficult_type = $dictionaryGroup[79];
  $employment_idea = $dictionaryGroup[465];
  $accept_employment_service = $dictionaryGroup[471];
  $unemployment_cause = $dictionaryGroup[478];
  $unemployment_difficult_type = $dictionaryGroup[489];
  $types = $dictionaryGroup[30];
  $purposes = $dictionaryGroup[42];
  $code_types = $dictionaryGroup[401];
  $area = 0;
</php>
<div class="rksj-main">
  <div class="rksj-t">
    <img src="/static/sc/images/rksj-icon-1.png">
    <p>基本信息<a href="javascript:history.go(-1);">[返回]</a></p>
  </div>
  <div class="rksj-d">
    <ul class="flex flex-wrap">
      <li>
        <p>所属社区：</p>
        <span>{$data.community_name}</span>
      </li>
      <li>
        <p>所属网格组： </p>
        <span>{$data.grid_group_name}</span>
      </li>
      <li>
        <p>所属网格：</p>
        <span>{:hide_str($data['grid_name'],2,2,2)}</span>
      </li>
      <li>
        <p>所属建筑： </p>
        <span>{:hide_str($data['building_name'],0,3,2)}</span>
      </li>
      <li>
        <p>所属房屋：</p>
        <span>{:hide_str($data['unit']."单元**楼".$data['code'],0,4,2)} </span>
      </li>
      <li>
        <p>户口一致性标识：</p>
        <span>{$data['consistency'] == 1?'是':'否'}</span>
      </li>
      <li>
        <p>户口类型：</p>
        <span>{$rpr[$data['rpr']]}</span>
      </li>
      <li>
        <p>户口性质：</p>
        <span>{$rpr_nature[$data['rpr_nature']]}</span>
      </li>
      <li>
        <p>户口编号：</p>
        <span>{$data.rpr_code}</span>
      </li>
      <li>
        <p>备注：</p>
        <span>{$data.details}</span>
      </li>
      <li>
        <p>微信号：</p>
        <span>{:hide_str($data['wechat_code'],0,3,2)}</span>
      </li>
      <li></li>
    </ul>
  </div>
  <div class="rksj-d">
    <ul class="flex flex-wrap">
      <li>
        <p>姓    名：</p>
        <span>{:hide_str($data['name'],0,1,2)}</span>
      </li>
      <li>
        <p>性   别：</p>
        <span>{$data['sex']==1?'男':'女'}</span>
        <p style="margin-left:80px;">年   龄：</p>
        <span>{$data.age}周岁</span>
      </li>
      <li>
        <p>民   族：</p>
        <span>{$nation[$data['nation']]}</span>
      </li>
      <li>
        <p>身份证号：</p>
        <span>{:hide_str($data['id_code'],6,8)}</span>
      </li>
      <li>
        <p>籍    贯：</p>
        <span>{$data.hometown}</span>
      </li>
      <li>
        <p>户籍所在地：</p>
        <span>{:hide_str($data['rpr_address'],2,6,2)}</span>
      </li>
      <li>
        <p>宗教信仰：</p>
        <span>{$religion[$data['religion']]}</span>
      </li>
      <li>
        <p>文化程度：</p>
        <span>{$education[$data['education']]}</span>
      </li>
      <li>
        <p>毕业时间：</p>
        <span>{$data.graduation_date}</span>
      </li>
      <li>
        <p>毕业院校：</p>
        <span>{$data.school}</span>
      </li>
      <li>
        <p>所学专业：</p>
        <span>{$data.major}</span>
      </li>
      <li>
        <p>兵役状况：</p>
        <span>{$enlistment[$data['enlistment']]}</span>
      </li>
    </ul>
  </div>
  <div class="rksj-d">
    <ul class="flex flex-wrap">
      <li>
        <p>政治面貌：</p>
        <span>{$face[$data['face']]}</span>
      </li>
      <li>
        <p>家庭住址：</p>
        <span>{:hide_str($data['address'],2,6,2)}</span>
      </li>
      <li>
        <p>人口类型：</p>
        <span>{$person_type[$data['person_type']]}</span>
      </li>
      <li>
        <p>住宅类型：</p>
        <span>{$house_type[$data['house_type']]}</span>
      </li>
      <li>
        <p>住宅面积： </p>
        <span>{$data.house_area}㎡</span>
      </li>
      <li>
        <p>健康状况：</p>
        <span>{$health[$data['health']]}</span>
      </li>
      <li>
        <p>血    型：</p>
        <span>{$blood[$data['blood']]}</span>
      </li>
      <li>
        <p>劳动能力：</p>
        <span>{$labor_capacity[$data['labor_capacity']]}</span>
      </li>
    </ul>
  </div>
  <div class="rksj-d">
    <ul class="flex flex-wrap">
      <li>
        <p>婚姻状况：</p>
        <span>{$marry[$data['marry']]}</span>
      </li>
      <gt name="data.marry" value="1">
        <li>
          <p>初婚日期：</p>
          <span>{$data.marry_date}</span>
        </li>
      </gt>
      <li>
        <p>独生子女家庭：</p>
        <span>{$only_child[$data['only_child']]}</span>
      </li>
      <li>
        <p>其他子女家庭：</p>
        <span>{$other_child[$data['other_child']]}</span>
      </li>
      <li>
        <p>联系方式：</p>
        <span>{:hide_str($data['phone'],3,4)}</span>
      </li>
      <li>
        <p>联系方式1：</p>
        <span>{:hide_str($data['phone_1'],3,4)}</span>
      </li>
      <li>
        <p>联系方式2：</p>
        <span>{:hide_str($data['phone_2'],3,4)}</span>
      </li>
      <li>
        <p>紧急联系手机：</p>
        <span>{:hide_str($data['urgent_phone'],3,4)}</span>
      </li>
    </ul>
  </div>
  <div class="rksj-t">
    <img src="/static/sc/images/rksj-icon-2.png">
    <p>特殊人群</p>
  </div>
  <div class="rksj-d">
    <ul class="flex flex-wrap">
      <li>
        <p>残       疾：</p>
        <span>{$data['disability'] == 1?'是':'否'}</span>
      </li>
      <eq name="data.disability" value="1">
        <li>
          <p>残疾类型：</p>
          <span>{$disabilityType[$data['disability_type']]}</span>
        </li>
        <li>
          <p>残疾等级：</p>
          <span>{$disabilityLevel[$data['disability_level']]}</span>
        </li>
        <li>
          <p>残疾证号码：</p>
          <span>{:hide_str($data['disability_code'],3,4)}</span>
        </li>
      </eq>
      <li>
        <p>是否退休：</p>
        <span>{$data['retire'] == 1?'是':'否'}</span>
      </li>
      <eq name="data.retire" value="1">
        <li>
          <p>退休单位：</p>
          <span>{$data.retire_unit}</span>
        </li>
      </eq>
      <li>
        <p>是否死亡：</p>
        <span>{$data['dead'] == 1?'是':'否'}</span>
      </li>
      <eq name="data.dead" value="1">
        <li>
          <p>死亡日期：</p>
          <span>{$data.dead_date}</span>
        </li>
      </eq>
      <li>
        <p>养老保险：</p>
        <span>{$endowment_insurance[$data['endowment_insurance']]}</span>
      </li>
      <li>
        <p>医      保：</p>
        <span>
          <foreach name="insurance"  item="vo" key="key">
                       <if (isset($data[$key]) && $data[$key] == 1) >{$vo} </if>
          </foreach>
        </span>
      </li>
      <li>
        <p>政务人群：</p>
        <span>
          <foreach name="representative"  item="vo" key="key">
                       <if (isset($data[$key]) && $data[$key] == 1) >{$vo} </if>
          </foreach>
        </span>
      </li>
      <li>
        <p>困境人群：</p>
        <span>
          <foreach name="dilemma"  item="vo" key="key">
                       <if (isset($data[$key]) && $data[$key] == 1) >{$vo} </if>
          </foreach>
        </span>
      </li>
      <li>
        <p>其他人群：</p>
        <span>
          <foreach name="other"  item="vo" key="key">
                       <if (isset($data[$key]) && $data[$key] == 1) >{$vo} </if>
          </foreach>
        </span>
      </li>
    </ul>
  </div>
  <div class="rksj-t">
    <img src="/static/sc/images/rksj-icon-3.png">
    <p>充分就业信息</p>
  </div>
  <div class="rksj-d">
    <ul class="flex flex-wrap">
      <li>
        <p>从业状态：</p>
        <span>{$employment[$emp['type']]|default='未知'}</span>
      </li>
      <eq name="emp.type" value="1">
        <li style="width:500px;">
          <p>就业前属于何种人员：</p>
          <span>{$employment_before[$emp['employment_before']]}</span>
        </li>
        <li>
          <p>就业形式：</p>
          <span>{$employment_form[$emp['employment_form']]}</span>
        </li>
        <li style="width:300px;">
          <p>就业时间：</p>
          <span>{$emp.employment_date}</span>
        </li>
        <li>
          <p>就业单位：</p>
          <span>{$emp.employment_unit}</span>
        </li>
        <li>
          <p>单位性质：</p>
          <span>{$employment_unit_property[$emp['employment_unit_property']]}</span>
        </li>
        <li>
          <p>就业困难再就业：</p>
          <span>{$emp['employment_difficult_reemployment'] == 1?'是':'否'}</span>
        </li>
        <li>
          <p>失业人员再就业：</p>
          <span>{$emp['unemployment_reemployment'] == 1?'是':'否'}</span>
        </li>
        <li>
          <p>就业困难人员：</p>
          <span>{$unemployment_difficult_type[$emp['unemployment_difficult_type']]}</span>
        </li>
        <li>
          <p>技能等级证书名称：</p>
          <span>{$emp.skill_level_certificate}</span>
        </li>
        <li>
          <p>是否有就业失业登记证：</p>
          <span>{$emp['certificate'] == 1?'是':'否'}</span>
        </li>
        <li>
          <p>就业失业登记证编号：</p>
          <span>{$emp.certificate_code}</span>
        </li>
      </eq>
      <eq name="emp.type" value="2">
        <li>
          <p>原工作单位：</p>
          <span>{$emp.unemployment_unit}</span>
        </li>
        <li>
          <p>失业时间：</p>
          <span>{$emp.lost_employment_date}</span>
        </li>
        <li  style="width:50%; border-top:1px solid #56636d;">
          <p>拟求职就业意向：</p>
          <span>{$employment_idea[$emp['employment_idea']]}</span>
        </li>
        <li  style="width:50%">
          <p>拟接受公共就业人才服务内容：</p>
          <span>{$employment_idea[$emp['employment_idea']]}</span>
        </li>
        <li  style="width:50%">
          <p>其他服务内容：</p>
          <span>{$emp.accept_employment_service_other}</span>
        </li>
        <li  style="width:50%">
          <p>失业原因登记：</p>
          <span>{$unemployment_cause[$emp['unemployment_cause']]}</span>
        </li>
        <li  style="width:50%">
          <p>其他失业原因：</p>
          <span>{$emp.unemployment_cause_other}</span>
        </li>
        <li  style="width:50%">
          <p>属于就业困难人员：</p>
          <span>{$emp['unemployment_difficult'] == 1?'是':'否'}</span>
        </li>
        <li  style="width:50%">
          <p>就业困难人员类别(失业)：</p>
          <span>{$unemployment_difficult_type[$emp['unemployment_difficult_type']]}</span>
        </li>
        <li  style="width:50%">
          <p>是否申领失业保险金：</p>
          <span>{$emp['lost_employment_subsidy'] == 1?'是':'否'}</span>
        </li>
      </eq>
    </ul>
  </div>
  <div class="rksj-t">
    <img src="/static/sc/images/rksj-icon-4.png">
    <p>资产收入信息</p>
  </div>
  <div class="rksj-d-1">
    <ul class="flex flex-wrap">
      <li class="bn">
        <p>家庭年收入 : </p>
        <span>{$income.home_year_income} 万元</span>
      </li>
      <li class="bn long">
        <p>家庭经营项目及规模 :</p>
        <span>{$income.home_year_info}</span>
      </li>
      <li>
        <p>房屋登记 : </p>
        <span>{$income['house'] == 1?'是':'否'}</span>
      </li>
      <li>
        <p>房屋数量 : </p>
        <span>{$income.house_number}</span>
      </li>
      <li style="width:989px;">
        <p>房屋资产详细信息 :</p>
        <span>{$income.house_info}</span>
      </li>
      <li>
        <p>耕地资产 : </p>
        <span>{$income['plow_land'] == 1?'是':'否'}</span>
      </li>
      <li class="long">
        <p>耕地资产详细信息 :</p>
        <span>{$income.plow_land_info}</span>
      </li>
      <li>
        <p>林地资产 : </p>
        <span>{$income['forest_land'] == 1?'是':'否'}</span>
      </li>
      <li class="long">
        <p>林地资产详细信息 :</p>
        <span>{$income.forest_land_info}</span>
      </li>
      <li>
        <p>农机资产 : </p>
        <span>{$income['agricultural_machinery'] == 1?'是':'否'}</span>
      </li>
      <li class="long">
        <p>农机资产详细信息 :</p>
        <span>{$income.agricultural_machinery_info}</span>
      </li>
      <li>
        <p>股票资产 : </p>
        <span>{$income['equities'] == 1?'是':'否'}</span>
      </li>
      <li class="long">
        <p>股票资产详细信息 :</p>
        <span>{$income.equities_info}</span>
      </li>
      <li>
        <p>股权资产 :  </p>
        <span>{$income['stock_right'] == 1?'是':'否'}</span>
      </li>
      <li class="long">
        <p>股权资产详细信息 :</p>
        <span>{$income.stock_right_info}</span>
      </li>
      <li>
        <p>其他资产 : </p>
        <span>{$income['other'] == 1?'是':'否'}</span>
      </li>
      <li class="long">
        <p>其他资产详细信息 :</p>
        <span>{$income.other_info}</span>
      </li>
    </ul>
  </div>
  <div class="rksj-t">
    <img src="/static/sc/images/rksj-icon-5.png">
    <p>家庭成员</p>
  </div>
  <div class="rksj-table">
    <table class="table">
      <thead>
      <tr>
        <th>姓名</th>
        <th>性别</th>
        <th>年龄</th>
        <th>身份证号</th>
        <th>联系电话</th>
        <th>民族</th>
        <th>与户主关系</th>
        <th>学历</th>
        <th>政治面貌</th>
      </tr>
      </thead>
      <tbody>
      <volist name="family" id="vo">
        <tr onclick="location.href='/admin/sc/person/personInfo?id={$vo.id}';">
          <td>{$vo.name}</td>
          <td>{$vo.sex==1?'男':'女'}</td>
          <td>{$vo.age}</td>
          <td>{:substr_replace($vo['id_code'], '******', 6, 8);}</td>
          <td>{:substr_replace($vo['phone'], '****', 3, 4);}</td>
          <td>{$nation[$vo['nation']]}</td>
          <td>{$relation[$vo['relation']]}</td>
          <td>{$education[$vo['education']]}</td>
          <td>{$face[$vo['face']]}</td>
        </tr>
      </volist>
      </tbody>
    </table>
  </div>
  <div class="rksj-t">
    <img src="/static/sc/images/rksj-icon-6.png">
    <p>车辆信息</p>
  </div>
  <div class="rksj-d">
    <volist name="vehicle" id="vo">
      <ul class="flex flex-wrap">
        <li>
          <p>车辆品牌 ：</p>
          <span>{$vo.brand}</span>
        </li>
        <li>
          <p>车牌号码 ：</p>
          <span>{$vo.vehicle_code}</span>
        </li>
        <li>
          <p>车辆种类 ：</p>
          <span>{$types[$vo['type']]}</span>
        </li>
        <li>
          <p>车辆用途 ：</p>
          <span>{$purposes[$vo['purpose']]}</span>
        </li>
        <li>
          <p>车辆牌照 ：</p>
          <span>{$code_types[$vo['code_type']]}</span>
        </li>
        <li>
          <p>车主姓名 ：</p>
          <span>{:hide_str($vo['owner'],0,1,2)}</span>
        </li>
        <li>
          <p>车主身份证 ：</p>
          <span>{:hide_str($vo['id_code'],6,8)}</span>
        </li>
        <li>
          <p></p>
          <span></span>
        </li>
        <li>
          <p>车主手机号码 ：</p>
          <span>{:hide_str($vo['phone'],3,4)}</span>
        </li>
        <li>
          <p></p>
          <span></span>
        </li>
        <li>
          <p></p>
          <span></span>
        </li>
        <li>
          <p></p>
          <span></span>
        </li>
        <php>
          $vo['path'] = json_decode($vo['path'],true);
          $vo['code_path'] = json_decode($vo['code_path'],true);
        </php>
        <li class="flex flex-column" style="width:50%">
          <p>车辆照片</p>
          <volist name="$vo['path']" id="vi">
          <div class="image"><img src="{$vi.src}"></div>
          </volist>
        </li>
        <li class="flex flex-column" style="width:50%">
          <p>牌照照片</p>
          <volist name="$vo['code_path']" id="vi">
            <div class="image"><img src="{$vi.src}"></div>
          </volist>
        </li>
      </ul>
    </volist>
  </div>
</div>
<include file="sc/public/footer" />