<include file="sc/public/header" />
<php>
  $ids = [50,30,42,401];
  $dictionaryGroup = getDictionaryGroup($ids);
  $place_merchant_type = getCateTree('place_merchant_type');
  $place_government_type = getDictionary(50);
  $place_enterprise_type = getCateTree('place_enterprise_type');
  $place_communal_facility_type = getCateTree('place_communal_facility_type');
  $type = $dictionaryGroup[30];
  $purpose = $dictionaryGroup[42];
  $code_type = $dictionaryGroup[401];
  $params=[];
  if($param['type']!="index"){
  if($param['type']=="gridGroup"){
  $param['type'] = "grid";
  }
  $params[$param['type']."_id"]=$param['id'];
  }
</php>
<style>
    .csxx-r{width: 100%;height: 100%;overflow-y: scroll}
    .box{width:400px; height:240px;}
    .box-m .image{width:100% !important; height:240px !important;}
    .csxx-r-l .map{width:400px;}
    .csxx-r-r{width:calc(100% - 600px);}
    .csxx-r-r p{margin-top:20px;}
</style>
  <div class="csxx-r flex">
    <div class="csxx-r-l">
      <php>
        if (strpos($data['image'], 'http') === 0) {
        $data['image'] = [['src'=>$data['image'],'title'=>'图片']];
        }else{
        $data['image'] = json_decode($data['image'],true);
        }
      </php>
      <!-- // 循环输出代码 -->
      <notempty name="$data['image']">
      <div class="box" style="width: 400px; height: 240px;">
        <div class="box-t"></div>
        <div class="box-m">
            <div class="image layui-carousel" id="carousel" style="width:100%;">
              <div carousel-item>
                  <foreach name="$data['image']" item="vo">
                    <div><img src="{$vo.src}" lay-image-click></div>
                  </foreach>
              </div>
            </div>
        </div>
        <div class="box-d"></div>
      </div>
      </notempty>
      <div class="map" style="width: 400px;">
        <div class="image"><img src="/static/sc/images/cache/img-5.jpg"></div>
      </div>
    </div>
    <div class="csxx-r-r" style="width:calc(100% - 600px)">
      <h3>{$data.enterprise_name}</h3>
      <div class="csxx-title">
        <span class="iconfont icon-dianmian"></span>
        企业信息
      </div>
      <p>行业大类： {$place_enterprise_type[$data['type_1_id']]['name']}</p>
      <p>行业类别： {$place_enterprise_type[$data['type_1_id']]['children'][$data['type_2_id']]['name']}</p>
      <p>营业执照代码：{:hide_str($data['code'],6,8)} </p>
      <p>法人名称： {:hide_str($data['corporation'],0,1,2)} </p>
      <p>法人身份证号： {:hide_str($data['id_code'],6,8)} </p>
      <p>联系电话： {:hide_str($data['phone'],3,4)} </p>
      <p>工作人员数量： {$data.peoples} </p>
      <div class="csxx-title">
        <span class="iconfont icon-map"></span>
        位置信息
      </div>
      <p>地址： {$data.address} </p>
      <p>所在社区： {$data.community_name}</p>
      <p>所在网格组：{$data.grid_group_name}</p>
      <p>所在网格： {$data.grid_name}</p>
      <p>所属建筑： {$data.building_name}{$data.unit}单元{$data.floor}层</p>
      
      
    </div>
  </div>
<include file="sc/public/footer" />
<include file="/public/footer" />
<script>
  layui.use(['admin','carousel','layphoto'], function(){
    var carousel = layui.carousel;
    // 初始化图片轮播
    carousel.render({
      elem: '#carousel', // 绑定的元素
      width: '400px', // 宽度
      height: '240px', // 高度
      arrow: 'always', // 始终显示箭头
      interval: 3000 //设置轮播时间间隔
    });
  });
</script>