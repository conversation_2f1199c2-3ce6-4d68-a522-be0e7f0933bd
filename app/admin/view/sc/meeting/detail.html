<include file="sc/public/header" />

<style>
    html{width:80%;}
    img{width:600px;}
    body{zoom:1; overflow:scroll;}
    body::-webkit-scrollbar {
      display: none;
    }
    .content-main{margin:0 auto;}
    .content-main-img img{width:658px;}
    .content-main-m{
        padding: 10px 10px!important;
    }
</style>

<div>
    <if class="content-main">
        <div class="content-main-t">
            {$title}
            <p>会议时间：{$start_date} {$start_time}</p>
            <p>
                <if (has_admin_auth(url("/admin/meeting/edit")&&$audio_url))>
                    <a class="layui-btn icon-btn"
                            data-maxmin="true" href="{$audio_url}" target="_blank"><i class="layui-icon fa-video-camera"></i>会议音频</a>
                </if>
            </p>
        </div>
        <div class="content-main-m">
            会议地点：{$address}
        </div>
        <div class="content-main-m">
            召集人：{$convener}
        </div>
        <div class="content-main-m">
            参会要求：<div style="text-indent:1em;">{:htmlspecialchars_decode($meeting_req)}</div>
        </div>
        <div class="content-main-m">
            参会内容：<div style="text-indent:1em;">{:htmlspecialchars_decode($details)}</div>
        </div>
        <if (has_admin_auth(url("/admin/meeting/edit")&&$audio_content))>
        <div class="content-main-m">
            {:htmlspecialchars_decode($audio_content)}
        </div>
    </if>
</div>

<include file="public/footer" />
<include file="sc/public/footer" />
<script src="/static/sc/js/jquery.SuperSlide.2.1.1.js"></script>
