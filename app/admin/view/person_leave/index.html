<include file="/public/header"/>
<php>
    $leave_type = getDictionary(526);
</php>
<div class="layui-fluid">
    <div class="layui-card">
        <!-- // 创建数据实例 -->
        <table id="lay-tableList" lay-filter="lay-tableList"></table>
    </div>
</div>

<include file="/public/footer"/>
<script type="text/html" id="tableBar">

    <a class="layui-table-text" data-url="{:url('/PersonLeave/del')}?id={{d.id}}" lay-event="del">{:__('删除')}</a>
</script>
<script>
    layui.use(['admin', 'table'], function () {

        var admin = layui.admin;
        var table = layui.table;

        /*
         * 初始化表格
        */
        var isTable = table.render({
            elem: "#lay-tableList"
            , url: "{:url('/PersonLeave/index',['person_id'=>$person_id])}"
            , cellMinWidth: 50
            ,page: true
            ,limit: 50,limits: [50, 100, 200, 500]
            ,height: 'full-150' //设置表格高度，减去固定的底部高度
            , cols: [[
                {
                    field: 'leave_type', title: '{:__("类型")}', templet: function (d) {
                        var resData = {:json_encode($leave_type)};
                        return d.leave_type > 0 ?resData[d.leave_type]:''
                    }
                },
                {field: 'details', title: '{:__("原因")}'},
                {field: 'create_date', title: '{:__("日期")}'},
                {field: 'create_by',width:160, title: '{:__("记录人")}'},
                {field: 'create_time',width: 120, title: '{:__("记录时间")}'},
                {align: 'center', toolbar: '#tableBar', width: 200, fixed: 'right', title: '{:__("操作")}'},
            ]]
        })

    })
</script>
