<include file="/public/header"/>
<php>
    $build_types = getDictionary(13);
</php>
<div class="layui-fluid">
    <div class="layui-card">
        <!-- // 默认操作按钮 -->
        <div class="layui-card-header layadmin-card-header-auto ">
            <div class="layui-form">
                <div class="layui-form-item">
                    <include file="/public/search"/>
                    <div class="layui-inline">
                        <div class="layui-input-inline ">
                            <input name="keyword" class="layui-input" type="text" placeholder="{:__('按建筑名称查询')}"/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <!-- // 默认搜索 -->
                        <button id="search" class="layui-btn icon-btn" lay-filter="formSearch" lay-submit><i
                                class="layui-icon layui-icon-search"></i>{:__('搜索')}
                        </button>
                        <button class="layui-btn more">展开</button>
                    </div>
                </div>
                <!-- // 自定义搜索参数 -->
                <div id="laytable-search" class="layui-form-item">

                    <include file="/public/search_road" />
                    <div class="layui-inline">
                        <div class="layui-form-label">{:__('建筑类别')}</div>
                        <div class="layui-input-inline ">
                            <select name="type" lay-search="1" lay-filter="search">
                                <option value="">全部</option>
                                <foreach name="build_types" item="vo" key="key">
                                    <option value="{$key}">{$vo}</option>
                                </foreach>
                            </select>
                        </div>
                    </div>

                </div>
            </div>
        </div>
        <!-- // 创建数据实例 -->
        <table id="lay-tableList" lay-filter="lay-tableList"></table>
    </div>
</div>
<include file="/public/toolBar"/>

<!-- // 列表工具栏 -->
<script type="text/html" id="tableBar">
    <!--formBegin-->
    <a class="layui-table-text" data-title="{:__('查看')}" data-area="100%,100%" data-maxmin="true"
       data-url="{:url('/'.$controller.'/detail')}?id={{d.id}}" lay-event="edit">{:__('查看')}</a>
    <div class="layui-divider layui-divider-vertical"></div>
    <a class="layui-table-text" data-title="{:__('编辑')}" data-area="1100px,750px" data-maxmin="true"
       data-url="{:url('/Building/edit')}?id={{d.id}}" lay-event="edit">{:__('编辑')}</a>
    <div class="layui-divider layui-divider-vertical"></div>
    <!--formEnd-->
    <a class="layui-table-text" data-url="{:url('/Building/del')}?id={{d.id}}" lay-event="del">{:__('删除')}</a>
</script>




<include file="/public/footer"/>
<script>
    layui.use(['admin', 'table'], function () {

        var admin = layui.admin;
        var table = layui.table;

        /*
         * 初始化表格
        */
        var isTable = table.render({
            elem: "#lay-tableList"
            , url: "{:url('/Building/index',$params)}"
            ,toolbar: '#tableButton'
            , defaultToolbar: ['filter', 'exports', 'print', 'search']
            , cellMinWidth: 50
            ,page: true
            ,limit: 50,limits: [50, 100, 200, 500]
            ,height: 'full-150' //设置表格高度，减去固定的底部高度
            , cols: [[
                {type: 'checkbox', width: 50},
                {field:'community_name',width: 120, title: '{:__("所在社区")}'},
                {field:'grid_group_name',width: 120, title: '{:__("所在网格组")}'},
                {field:'grid_name',width: 120, title: '{:__("所在网格")}'},
                {field: 'building_name',width:160, title: '{:__("建筑名称")}'},
                {
                    field:'type',width: 120, title: '{:__("建筑类型")}', templet: function (d) {
                        var resData = {:json_encode(getDictionary(13))};
                        return d.type > 0 ?resData[d.type]:''
                    }
                },
                {
                    field: 'image',width: 120,width: 120, templet: function (d) {
                        if(d.image==''){
                            return '';
                        }
                        var album = [];
                        try {
                            d.image = JSON.parse(d.image);
                            for (var i in d.image) {
                                album[i] = '<a href="javascript:" class="fileslink" ><img class="filesuffix" lay-image-click src="' + admin.toThumb(d.image[i].src) + '"></a>'
                            }
                            return album.join('');
                        } catch (e) {
                            return '<a href="javascript:" class="fileslink" ><img class="filesuffix" lay-image-click src="' + admin.toThumb(d.image) +  '"></a>'
                        }
                    }, title: '{:__("街景示意图")}'
                },
                {
                    field: 'unit', width: 120, title: '{:__("单元数-地上层数-地下层数")}', templet: function (d) {
                        return d.unit + "-" + d.floor + "-" + d.floor_under
                    }
                },
                {field:'address',width: 200, title: '{:__("建筑位置")}'},
                {
                    field:'street_name',width: 120, title: '{:__("街")}', templet: function (d) {
                        var street = "";
                        if (d.street_name) {
                            street += d.street_name + d.street_start + "-" + d.street_end;
                        }
                        if (d.street_sub) {
                            street += " " + d.street_sub;
                        }
                        return street;
                    }
                },
                {
                    field:'road_name',width: 120, templet: function (d) {
                        var road = "";
                        if (d.road_name) {
                            road += d.road_name + d.road_start + "-" + d.road_end;
                        }
                        if (d.road_sub) {
                            road += " " + d.road_sub;
                        }
                        return road;
                    }, title: '{:__("路")}'
                },
                {field: 'create_time',width:160, title: '{:__("创建时间")}'},
                {field: 'create_by',width: 120, title: '{:__("创建人")}'},
                {field: 'update_time',width: 160, title: '{:__("修改时间")}'},
                {field: 'update_by',width: 120, title: '{:__("修改人")}'},
                {align: 'center', toolbar: '#tableBar', width: 200, fixed: 'right', title: '{:__("操作")}'},
            ]]
        })

    })
</script>
<include file="/public/select"/>
