<include file="/public/header" />
<php>
    $level = getDictionary(570);
</php>
<div class="layui-fluid">
    <div class="layui-card">
        <!-- // 默认操作按钮 -->
        <div class="layui-card-header layadmin-card-header-auto ">
            <div class="layui-form">
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <div class="layui-input-inline ">
                            <input name="building_name" class="layui-input" type="text" placeholder="{:__('按标题或内容查询')}"/>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <!-- // 默认搜索 -->
                        <button id="search" class="layui-btn icon-btn" lay-filter="formSearch" lay-submit><i
                                class="layui-icon layui-icon-search"></i>{:__('搜索')}
                        </button>
                    </div>
                </div>
            </div>   
        </div>
        <!-- // 创建数据实例 -->
        <table id="lay-tableList" lay-filter="lay-tableList"></table>        
    </div>
</div>


<include file="/public/scToolBar"/>
<include file="/public/scTableBar"/>
<include file="/public/footer" />
<include file="/public/select"/>
<script>
    layui.use(['admin','table'], function () {

        var admin = layui.admin;
        var table = layui.table;

        /*
         * 初始化表格
        */
        var isTable = table.render({
            elem: "#lay-tableList"
            ,url: "{:url('/ScFile/index')}"
            ,toolbar: '#tableButton'
            ,defaultToolbar: ['filter', 'exports', 'print','search']
            ,cellMinWidth: 60
            ,page: true
            ,limit: 50,limits: [50, 100, 200, 500]
            ,height: 'full-150' //设置表格高度，减去固定的底部高度
            ,cols: [[
                {type: 'checkbox', width: 50},
                {field:'title',title:'{:__("标题")}'},
                {field:'files',templet:function(d) {
                        return d.file==''?'未上传':'已上传';
                    },title:'{:__("文件上传")}'},
                {field:'level',templet:function(d) {
                        var resData = {:json_encode($level)};
                        return d.level > 0 ?resData[d.level]:''
                    },title:'{:__("文件级别")}'},
                {field:'download_limit',title:'{:__("下载截止")}'},
                {field:'add_time',title:'{:__("发派时间")}'},
                {field: 'create_time',width: 160,title:'{:__("创建时间")}'},
                {field: 'create_by',width: 120,title:'{:__("创建人")}'},
                {field: 'update_time',width: 160,title:'{:__("修改时间")}'},
                {field: 'update_by',width: 120,title:'{:__("修改人")}'},
                {align: 'center', toolbar: '#tableBar', width: 200, fixed: 'right', title: '{:__("操作")}'},
            ]]
        })

    })
</script>
