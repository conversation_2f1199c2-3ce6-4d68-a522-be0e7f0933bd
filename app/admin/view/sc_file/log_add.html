<include file="/public/header"/>

<link href="__STATICADMIN__css/content.css" rel="stylesheet" type="text/css"/>
<div class="layui-fluid">
    <form class="layui-form layui-card">

        <div class="layui-card-body">
            
            <input type="text" name="id" value="{$id}" hidden="">
            <div class="layui-form-item">
                <label class="layui-form-label"><font color="red">* </font>网格组</label>
                <div class="layui-input-block">
                    <foreach name="grid_group" item="item">
                        <input type="checkbox" name="grid_id[]" lay-skin="primary" value="{$item.id}" title="{$item.grid_name}" >
                    </foreach>
                </div>
            </div>
            <eq name="data.level" value="3">
            <div class="layui-form-item">
                <label class="layui-form-label"><font color="red">* </font>网格</label>
                <div class="layui-input-block">
                    <foreach name="grid" item="item">
                        <input type="checkbox" name="grid_id[]" lay-skin="primary" value="{$item.id}" title="{$item.grid_name}" >
                    </foreach>
                </div>
            </div>
            </eq>
            <div class="layui-footer layui-form-footer">
                <button class="layui-btn layui-btn-primary" type="button" sa-event="closeDialog">{:__('取消')}</button>
                <button class="layui-btn" type="button" lay-filter="submitIframe" lay-submit>{:__('提交')}</button>
            </div>
    </form>
</div>
<include file="/public/static"/>
<include file="/public/footer"/>
<include file="/public/select"/>
<script>
    layui.use(['layer','form'], function () {
        var form = layui.form;
        var $ = layui.jquery;
    });
</script>