<include file="/public/header"/>
<php>
    $Communities = getCommunity();
    if($Communities[0]['id']==1){
    unset($Communities[0]);
    $Communities = array_values($Communities);
    }
    $community_id = empty($data['community_id'])?$Communities[0]['id']:$data['community_id'];
    $GridGroups = getGridGroup($community_id);
    $grid_group_id = empty($data['grid_group_id'])?$GridGroups[0]['id']:$data['grid_group_id'];
    $Grids = getGrid($grid_group_id);
    $place_communal_facility_type = getCateTree('place_communal_facility_type');
</php>
<!---->
<link href="__STATICADMIN__css/content.css" rel="stylesheet" type="text/css"/>
<div class="layui-fluid">
    <form class="layui-form layui-card">

        <div class="layui-card-body">
            <gt name="$data.id" value="0">
                <input type="text" name="id" value="{$data.id}" hidden="">
                <else/>
                <input type="text" name="id" value="" hidden="">
            </gt>
            <div class="layui-form-item layui-row">
                <div class="layui-col-md6 layui-grid-0" data-index="0">
                    <div class="layui-form-item">
                        <div class="layui-input-block" style="margin-left: 0;">
                            <blockquote class="layui-elem-quote">图片</blockquote>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label layui-hide">图片</label>
                        <div class="layui-input-block" style="margin-left:0">
                            <div class="layui-imagesbox">

                                <php>
                                    if (strpos($data['image'], 'http') === 0) {
                                    $data['image'] = [['src'=>$data['image'],'title'=>'图片']];
                                    }else{
                                    $data['image'] = json_decode($data['image'],true);
                                    }
                                </php>
                                <!-- // 循环输出代码 -->
                                <notempty name="$data['image']">
                                    <foreach name="$data['image']" item="vo">
                                        <div class="layui-input-inline layui-uplpad-image">
                                            <img src="{$vo.src}" lay-image-click>
                                            <input type="text" name="image[{$key}][src]" class="layui-hide"
                                                   value="{$vo.src}">
                                            <input type="text" name="image[{$key}][title]" class="layui-input  layui-image"
                                                   value="{$vo.title}" placeholder="图片简介">
                                            <span class="layui-badge layui-badge-red" data-name="image"
                                                  onclick="layui.admin.resetInput(this,'images');">删除</span>
                                        </div>
                                    </foreach>
                                </notempty>
                                <div class="layui-input-inline layui-uplpad-image">
                                    <div class="layui-upload-drag" lay-upload="image" data-type="multiple" >
                                        <i class="layui-icon layui-icon-upload"></i>
                                        <p>点击上传，或将文件拖拽到此处</p>
                                        <div class="layui-hide"></div>
                                    </div>
                                    
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="width:120px;"><font color="red">* </font>所属社区</label>
                        <div class="layui-input-block" style="margin-left:150px">
                            <select  name="community_id" lay-search lay-filter="communityChange" lay-verify="required">
                                <foreach name="Communities" item="vo">
                                    <option value="{$vo.id}"
                                    <if (isset($data['id']) && $data['id']) >
                                    <in name="$vo.id" value="$data.community_id">selected</in>
                                    </if>
                                    >{$vo.community_name}</option>
                                </foreach>
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="width:120px;"><font color="red">* </font>所属网格组</label>
                        <div class="layui-input-block" style="margin-left:150px">
                            <select id="grid_group" name="grid_group_id" lay-search lay-filter="gridGroupChange" lay-verify="required">
                                <foreach name="GridGroups" item="vo">
                                    <option value="{$vo.id}"
                                    <if (isset($data['id']) && $data['id']) >
                                    <in name="$vo.id" value="$data.grid_group_id">selected</in>
                                    </if>
                                    >{$vo.grid_name}</option>
                                </foreach>
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label" style="width:120px;"><font color="red">* </font>所属网格</label>
                        <div class="layui-input-block" style="margin-left:150px">
                            <select id="grid" name="grid_id" lay-search lay-filter="gridChange" lay-verify="required">
                                <foreach name="Grids" item="vo">
                                    <option value="{$vo.id}"
                                    <if (isset($data['id']) && $data['id']) >
                                    <in name="$vo.id" value="$data.grid_id">selected</in>
                                    </if>
                                    >{$vo.grid_name}</option>
                                </foreach>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="layui-col-md6 layui-grid-1" data-index="1">
                    <div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">* </font>设施名称</label>
                        <div class="layui-input-block"><input class="layui-input" name="name" placeholder="请输入"
                                                              required="1" lay-verify="required" value="{$data.name}"></div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">设施编号</label>
                        <div class="layui-input-block"><input class="layui-input" name="code" placeholder="请输入"
                                                              lay-verify="" value="{$data.code}"></div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">* </font>详细地址</label>
                        <div class="layui-input-block"><input class="layui-input" name="address" placeholder="请输入"
                                                              required="1" lay-verify="required" value="{$data.address}"></div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">* </font>大类</label>
                        <div class="layui-input-block">
                            <select name="type_1_id" lay-verify="required" lay-filter="typeChange">
                                <option value=''>请选择</option>
                                <foreach name="place_communal_facility_type" item="vo" key="key">
                                    <option value="{$vo.id}"
                                    <if (isset($data['id']) && $data['id']) >
                                    <in name="$vo.id" value="$data.type_1_id">selected</in>
                                    </if>
                                    >{$vo.name}</option>
                                </foreach>
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label"><font color="red">* </font>类别</label>
                        <div class="layui-input-block">
                            <select id="type_2_id" name="type_2_id" lay-verify="required">
                                <option value=''>请选择</option>
                                <if (isset($data['type_1_id']) && $data['type_1_id']) >
                                <foreach name="place_communal_facility_type[$data['type_1_id']]['children']" item="vo" key="key">
                                    <option value="{$vo.id}"
                                    <in name="$vo.id" value="$data.type_2_id">selected</in>
                                    >{$vo.name}</option>
                                </foreach>
                                </if>
                            </select>
                        </div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">所属单位</label>
                        <div class="layui-input-block"><input class="layui-input" name="unit" placeholder="请输入"
                                                              lay-verify="" value="{$data.unit}"></div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">负责人姓名</label>
                        <div class="layui-input-block"><input class="layui-input" name="people_name" placeholder="请输入"
                                                              lay-verify="" value="{$data.people_name}"></div>
                    </div>
                    <div class="layui-form-item">
                        <label class="layui-form-label">联系方式</label>
                        <div class="layui-input-block"><input class="layui-input" name="people_phone" placeholder="请输入"
                                                              lay-verify="" value="{$data.people_phone}"></div>
                    </div>
                </div>
            </div>
            <div class="layui-footer layui-form-footer">
                <button class="layui-btn layui-btn-primary" type="button" sa-event="closeDialog">{:__('取消')}</button>
                <button class="layui-btn" type="button" lay-filter="submitIframe" lay-submit>{:__('提交')}</button>
            </div>
    </form>
</div>
<include file="/public/static"/>
<include file="/public/footer"/>
<script>
    layui.use(['layer','form'], function () {
        var form = layui.form;
        var $ = layui.jquery;
        var type_json = {:json_encode($place_communal_facility_type)};

        form.on('select(typeChange)', function(data){
            $('#type_2_id').empty();
            $('#type_2_id').append(new Option("请选择", ""));// 下拉菜单里添加元素
            $.each(type_json[data.value]['children'], function (index, value) {
                $('#type_2_id').append(new Option(value.name, value.id));// 下拉菜单里添加元素
            });
            form.render('select');
        });
    });
</script>
<include file="/public/select"/>