<include file="/public/header"/>
<php>
    $place_communal_facility_type = getCateTree('place_communal_facility_type');
</php>
<div class="layui-fluid">
    <div class="layui-card">
        <!-- // 默认操作按钮 -->
        <div class="layui-card-header layadmin-card-header-auto ">
            <div class="layui-form">
                <div class="layui-form-item">
                    <include file="/public/search"/>
                    <div class="layui-inline">
                        <div class="layui-input-inline ">
                            <select id ="check_type" name="check_type" lay-filter="search">
                                <volist name="checkRule" id="vo">
                                    <option value="{$key}">{$vo.title}</option>
                                </volist>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <!-- // 默认搜索 -->
                        <button id="search" class="layui-btn icon-btn" lay-filter="formSearch" lay-submit><i
                                class="layui-icon layui-icon-search"></i>{:__('搜索')}
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <!-- // 创建数据实例 -->
        <table id="lay-tableList" lay-filter="lay-tableList"></table>
    </div>
</div>

<!-- // 列表工具栏 -->
<script type="text/html" id="tableBar">
    <!--formBegin-->
    <a class="layui-table-text" data-title="{:__('查看')}" data-area="100%,100%" data-maxmin="true"
       data-url="{:url('/'.$controller.'/detail')}?id={{d.id}}" lay-event="edit">{:__('查看')}</a>
    <div class="layui-divider layui-divider-vertical"></div>
    <a class="layui-table-text" data-title="{:__('编辑')}" data-area="1100px,750px" data-maxmin="true"
       data-url="{:url('/'.$controller.'/edit')}?id={{d.id}}" lay-event="edit">{:__('编辑')}</a>
    <div class="layui-divider layui-divider-vertical"></div>
    <!--formEnd-->
    <a class="layui-table-text" data-url="{:url('/'.$controller.'/del')}?id={{d.id}}" lay-event="del">{:__('删除')}</a>
</script>




<include file="/public/footer"/>
<script>
    layui.use(['admin', 'table'], function () {

        var admin = layui.admin;
        var table = layui.table;
        var type_json = {:json_encode($place_communal_facility_type)};
        /*
         * 初始化表格
        */
        var isTable = table.render({
            elem: "#lay-tableList"
            , url: "{:url('/'.$controller.'/proData')}"
            , cellMinWidth: 60
            ,page: true
            ,limit: 50,limits: [50, 100, 200, 500]
            ,height: 'full-150' //设置表格高度，减去固定的底部高度
            , cols: [[
                {field: 'check_name',width: 160, title: '{:__("错误原因")}'},
                {field: 'community_id',width: 320, title: '{:__("所在区域")}', templet: function (d) {
                        return d.community_name + "," + d.grid_group_name + "," + d.grid_name
                    }},
                {field:'name',width:200,title:'{:__("设施名称")}'},
                {field:'address',width:200,title:'{:__("详细地址")}'},
                {field:'code',width:100,title:'{:__("设施编号")}'},
                {field: 'type_1_id',width:120, title: '{:__("大类")}',templet:function(d){
                        return d.type_1_id>0?type_json[d.type_1_id].name:'';
                    }},
                {field: 'type_2_id',width:120, title: '{:__("类别")}',templet:function(d){
                        return d.type_2_id>0?type_json[d.type_1_id].children[d.type_2_id].name:'';
                    }},
                {field: 'people_name',width:80,title:'{:__("负责人姓名")}'},
                {field: 'people_phone',width:120,title:'{:__("联系方式")}'},
                {field: 'create_time',width: 160,title:'{:__("创建时间")}'},
                {field: 'create_by',width: 120,title:'{:__("创建人")}'},
                {field: 'update_time',width: 160,title:'{:__("修改时间")}'},
                {field: 'update_by',width: 120,title:'{:__("修改人")}'},
                {align: 'center', toolbar: '#tableBar', width:200, fixed: 'right', title: '{:__("操作")}'},
            ]],done: function(res, curr, count){
                if(res.url!='/'){
                    layui.jquery("#check_type").val(res.url);
                    layui.form.render("select");
                }
            }
        })

    })
</script>
<include file="/public/select"/>
