<?php
// +----------------------------------------------------------------------
// | swiftAdmin 极速开发框架 [基于WebMan开发]
// +----------------------------------------------------------------------
// | Copyright (c) 2020-2030 http://www.swiftadmin.net
// +----------------------------------------------------------------------
// | swiftAdmin.net High Speed Development Framework
// +----------------------------------------------------------------------
// | Author: meystack <<EMAIL>> Apache 2.0 License
// +----------------------------------------------------------------------
namespace app;

use app\admin\enums\AdminEnum;
use app\admin\model\Person;
use app\admin\service\AuthService;
use app\admin\service\ExcelService;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use support\Log;
use support\Response;
use think\helper\Str;
use think\facade\Db;

class AdminController extends BaseController
{
    /**
     * 数据库实例
     * @var object
     */
    public object $model;

    /**
     * 数据表名称
     * @var string
     */
    public string $tableName;

    /**
     * 操作状态
     * @var mixed
     */
    public mixed $status;

    /**
     * 获取模板
     * @var      string
     */
    public string $template = '';

    /**
     * 权限验证类
     * @var object
     */
    public object $authService;

    /**
     * 当前表字段
     * @var array
     */
    protected array $tableFields = [];

    /**
     * 默认开关
     * @var string
     */
    protected string $keepField = 'sex';

    /**
     * 开启数据限制
     * @var boolean
     */
    protected bool $dataLimit = false;

    /**
     * 是否开启部门限制
     * @var bool
     */
    protected bool $departmentLimit = false;

    /**
     * 数据限制字段
     * @var string
     */
    protected string $dataLimitField = 'admin_id';

    /**
     * 需要排除的字段
     * @var mixed
     */
    protected mixed $ruleOutFields = '';

    /**
     * 查询过滤字段
     * @var array
     */
    protected array $filterWhere = ['page', 'limit'];

    /**
     * 查询转换字段
     * @var array
     */
    protected array $converTime = ['create_time', 'update_time', 'delete_time'];

    /**
     * 定义关联模型
     * @var array
     */
    protected array $relationModel = [];

    /**
     * 跳转URL地址
     * @var string
     */
    protected string $JumpUrl = '/';

    protected array $admin_info = [];

    protected array $columnFormat = [];

    protected string $tpl_name = "";

    protected string $table_name = "";

    protected string $table_source = "";

    protected string $pk= "";

    protected int $row_index = 1;

    protected array $checkRule;

    /**
     * 构造函数
     */
    public function __construct()
    {
        error_reporting(E_ERROR | E_PARSE);
        parent::__construct();
        $this->authService = AuthService::instance();
        $admin_info = get_admin_info();
        if(!empty($admin_info)){
            $this->admin_info = $admin_info;
        }
        plugin_refresh_hooks();
    }

    /**
     * 获取资源列表
     * @return Response
     */
    public function index()
    {
        if (request()->isAjax()) {
            $page = (int)input('page', 1);
            $limit = (int)input('limit', 18);
            $where = $this->buildSelectParams();
            $count = $this->model->where($where)->count();
            //$page = $count <= $limit ? 1 : $page;
            $fieldList = $this->model->getFields();
            $order = !array_key_exists('sort', $fieldList) ? 'id' : 'sort';
            $subQuery = $this->model->field('id')->where($where)->order($order, 'desc')->limit($limit)->page($page)->buildSql();
            $subQuery = '( SELECT object.id FROM ' . $subQuery . ' AS object )';
            $list = $this->model->with($this->relationModel)->where('id in' . $subQuery)->order($order, 'desc')->select()->toArray();
            return $this->success('查询成功', '/', $list, $count);
        }

        return $this->view();
    }

    /**
     * 添加资源
     * @return Response|void
     */
    public function add()
    {
        if (request()->isPost()) {

            $post = $this->preRuleOutFields(\request()->post());
            if ($this->dataLimit) {
                $post[$this->dataLimitField] = get_admin_id();
            }

            $validate = $this->isValidate ? get_class($this->model) : $this->isValidate;
            $post = request_validate_rules($post, $validate, $this->scene);
            if (empty($post) || !is_array($post)) {
                return $this->error($post);
            }
            $post = dataArrayToJson($post);
            if(!empty($this->model->create_by)){
                $post[$this->model->create_by]=$this->admin_info['name'];
            }
            if(!empty($this->model->createTime)){
                $post[$this->model->createTime]=date('Y-m-d H:i:s');
            }
            $this->status = $this->model->create($post);
            return $this->status ? $this->success() : $this->error();
        }

        return $this->view('', ['data' => $this->getTableFields()]);
    }

    /**
     * 编辑资源
     * @return Response|void
     */
    public function edit()
    {
        $id = input('id');
        $data = $this->model->where('id', $id)->findOrEmpty()->toArray();

        // 限制数据调用
        if (!$this->authService->SuperAdmin() && $this->dataLimit
            && in_array($this->dataLimitField, $this->model->getFields())) {
            if ($data[$this->dataLimitField] != get_admin_id()) {
                return $this->error('没有权限');
            }
        }

        if (request()->isPost()) {
            $post = $this->preRuleOutFields(\request()->post());
            $validate = $this->isValidate ? get_class($this->model) : $this->isValidate;
            $post = request_validate_rules($post, $validate, $this->scene);
            if (empty($post) || !is_array($post)) {
                return $this->error($post);
            }
            $post = dataArrayToJson($post);
            if(!empty($this->model->update_by)){
                $post[$this->model->update_by]=$this->admin_info['name'];
            }
            $this->status = $this->model->update($post);
            return $this->status ? $this->success() : $this->error();
        }

        /**
         * 默认共享模板
         */
        $template = str_replace('/_', '/', Str::snake(request()->getController()));
        return $this->view($template . '/add', [
            'data' => $data
        ]);
    }

    public function detail()
    {
        return $this->edit();
    }

    /**
     * 删除资源
     * @return Response
     */
    public function del()
    {
        $id = input('id');
        if (!is_array($id)) {
            $id = [$id];
        }

        try {
            $list = $this->model->whereIn('id', $id)->select();
            foreach ($list as $item) {
                if (!$this->authService->SuperAdmin() && $this->dataLimit
                    && in_array($this->dataLimitField, $this->model->getFields())) {
                    if ($item[$this->dataLimitField] != get_admin_id()) {
                        continue;
                    }
                }
                if (isset($item->isSystem) && $item->isSystem) {
                    throw new \Exception('禁止删除系统级数据');
                }

                $item->delete();
                $this->status = true;
            }
        } catch (\Throwable $th) {
            $this->status = false;
            return $this->error($th->getMessage());
        }

        return $this->status ? $this->success() : $this->error();
    }

    /**
     * 修改资源状态
     * @return Response|void
     */
    public function status()
    {
        if (request()->isAjax()) {

            $where[] = ['id', '=', input('id')];
            if (!$this->authService->SuperAdmin() && $this->dataLimit
                && in_array($this->dataLimitField, $this->model->getFields())) {
                $where[] = [$this->dataLimitField, '=', get_admin_id()];
            }

            try {
                $this->status = $this->model->where($where)->update(['status' => input('status')]);
            } catch (\Throwable $th) {
                return $this->error($th->getMessage());
            }

            if ($this->status) {
                return $this->success();
            }
        }

        return $this->error();
    }

    /**
     * 数据表排序
     * @return Response
     */
    public function sort()
    {
        if (request()->isPost()) {

            if (array_search('sort', $this->model->getTableFields())) {
                try {
                    $ids = request()->post('ids');
                    $list = $this->model->where('id', 'in', $ids)->orderRaw('field(id,' . implode(',', $ids) . ')')->select()->toArray();
                    $newSort = array_column($list, 'sort');
                    rsort($newSort);
                    $array = [];

                    // 循环处理排序字段
                    foreach ($list as $key => $value) {
                        $array[] = [
                            'id'   => $value['id'],
                            'sort' => $newSort[$key],
                        ];
                    }

                    $this->model->saveAll($array);
                } catch (\Throwable $th) {
                    return $this->error($th->getMessage());
                }

            } else {
                return $this->error('数据表未包含排序字段');
            }
        }

        return $this->success();
    }

    /**
     * 自动获取view模板
     * @param string $template
     * @param array $vars
     * @param null $app
     * @return Response
     */
    public function view(string $template = '', array $vars = [], $app = null): Response
    {
        $request = explode('/', \request()->getController());
        if (empty($template)) {
            $parseArr = array_map(function ($item) {
                return Str::snake($item);
            }, $request);
            $template = implode('/', $parseArr) . '/' . Str::snake(\request()->getAction());
        }

        return view($template, $vars, $app);
    }

    /**
     * 排除特定字段
     *
     * @param [type] $params
     * @return array
     */
    protected function preRuleOutFields($params): array
    {
        if (is_array($this->ruleOutFields)) {
            foreach ($this->ruleOutFields as $field) {
                if (key_exists($field, $params)) {
                    unset($params[$field]);
                }
            }
        } else {
            if (key_exists($this->ruleOutFields, $params)) {
                unset($params[$this->ruleOutFields]);
            }
        }

        return $params;
    }
    
    protected function buildSelectParamsOnly($alias="",$allow=true): array
    {
        $where = [];
        $params = request()->all();
        if (!empty($params) && is_array($params)) {

            $this->tableFields = $this->model->getFields();
            $old_alias = $alias;
            $admin_info = get_admin_info();
            // if($allow&&array_key_exists("community_id", $this->tableFields)){
            //     if($admin_info['community_id']>0&&empty($params['community_id'])){
            //         $where[$alias.'community_id'] = $admin_info['community_id'];
            //         if($admin_info['grid_group_id']>0&&empty($params['grid_group_id'])){
            //             $where[$alias.'grid_group_id'] = $admin_info['grid_group_id'];
            //             if($admin_info['grid_id']>0&&empty($params['grid_id'])){
            //                 $where[$alias.'grid_id'] = $admin_info['grid_id'];
            //             }
            //         }
            //     }
            // }
            
            $adminAccess = getAdminAccess($admin_info["id"]);
            $adminGroup = explode(',', $adminAccess['group_id']);
            // 如果是超级管理员、街道管理员、社区管理员
            if($allow){
                if(in_array(1, $adminGroup)
                    ||in_array(2, $adminGroup)
                    ||in_array(3, $adminGroup)){
                    if(array_key_exists("community_id", $this->tableFields)){
                        if($admin_info['community_id']>0&&empty($params['community_id'])){
                            $where[$alias.'community_id'] = $admin_info['community_id'];
                            if($admin_info['grid_group_id']>0&&empty($params['grid_group_id'])){
                                $where[$alias.'grid_group_id'] = $admin_info['grid_group_id'];
                                if($admin_info['grid_id']>0&&empty($params['grid_id'])){
                                    $where[$alias.'grid_id'] = $admin_info['grid_id'];
                                }
                            }
                        }
                    }
                }else if(in_array(5, $adminGroup)){
                    //网格组管理员
                    $where[$alias.'community_id'] = $admin_info['community_id'];
                    $where[$alias.'grid_group_id'] = $admin_info['grid_group_id'];
                    if($admin_info['grid_id']>0&&empty($params['grid_id'])){
                        $where[$alias.'grid_id'] = $admin_info['grid_id'];
                    }
                }else if (in_array(6, $adminGroup)){
                    //网格管理员
                    $where[$alias.'community_id'] = $admin_info['community_id'];
                    $where[$alias.'grid_group_id'] = $admin_info['grid_group_id'];
                    $where[$alias.'grid_id'] = $admin_info['grid_id'];
                }
            }
        }

        return $where;
    }

    /**
     * 获取查询参数
     * @return array
     */
    protected function buildSelectParams($alias="",$allow=true): array
    {
        $where = [];
        $params = request()->all();
        if (!empty($params) && is_array($params)) {

            $this->tableFields = $this->model->getFields();
            $old_alias = $alias;
            $admin_info = get_admin_info();
            // if($allow&&array_key_exists("community_id", $this->tableFields)){
            //     if($admin_info['community_id']>0&&empty($params['community_id'])){
            //         $where[$alias.'community_id'] = $admin_info['community_id'];
            //         if($admin_info['grid_group_id']>0&&empty($params['grid_group_id'])){
            //             $where[$alias.'grid_group_id'] = $admin_info['grid_group_id'];
            //             if($admin_info['grid_id']>0&&empty($params['grid_id'])){
            //                 $where[$alias.'grid_id'] = $admin_info['grid_id'];
            //             }
            //         }
            //     }
            // }
            $adminAccess = getAdminAccess($admin_info["id"]);
            $adminGroup = explode(',', $adminAccess['group_id']);
            // 如果是超级管理员、街道管理员、社区管理员
            if($allow){
                if(in_array(1, $adminGroup)
                    ||in_array(2, $adminGroup)
                    ||in_array(3, $adminGroup)){
                    if(array_key_exists("community_id", $this->tableFields)){
                        if($admin_info['community_id']>0&&empty($params['community_id'])){
                            $where[$alias.'community_id'] = $admin_info['community_id'];
                            if($admin_info['grid_group_id']>0&&empty($params['grid_group_id'])){
                                $where[$alias.'grid_group_id'] = $admin_info['grid_group_id'];
                                if($admin_info['grid_id']>0&&empty($params['grid_id'])){
                                    $where[$alias.'grid_id'] = $admin_info['grid_id'];
                                }
                            }
                        }
                    }
                }else if(in_array(5, $adminGroup)){
                    //网格组管理员
                    $where[$alias.'community_id'] = $admin_info['community_id'];
                    $where[$alias.'grid_group_id'] = $admin_info['grid_group_id'];
                    if($admin_info['grid_id']>0&&empty($params['grid_id'])){
                        $where[$alias.'grid_id'] = $admin_info['grid_id'];
                    }
                }else if (in_array(6, $adminGroup)){
                    //网格管理员
                    $where[$alias.'community_id'] = $admin_info['community_id'];
                    $where[$alias.'grid_group_id'] = $admin_info['grid_group_id'];
                    $where[$alias.'grid_id'] = $admin_info['grid_id'];
                }
            }
            
            
            foreach ($params as $field => $value) {
                if(strpos($field, '__') !== false){
                    $arr = explode('__',$field);
                    $alias = $arr[0].".";
                    $field = $arr[1];
                }else{
                    $alias = $old_alias;
                }
                if(empty($value)){
                    continue;
                }
                if($field=='id'){
                    $where[]=[$alias.'id','in',implode(",",$value)];
                    continue;
                }
                // 过滤字段
                if (in_array($field, $this->filterWhere)) {
                    continue;
                }
                // 非表内字段
                if (!array_key_exists($field, $this->tableFields)) {
                    if($old_alias != $alias){
                        if(strpos($value, ',')!==false) {
                            $where[] = [$alias.$field, 'in', explode(',', $value)];
                        } else {
                            $where[] = [$alias.$field, '=', intval($value )];
                        }
                    }
                    continue;
                }

                // 默认状态字段
                if ($field == $this->keepField && $value) {
                    $where[] = [$alias.$field, '=', intval($value - 1)];
                    continue;
                }

                // 获取类型
                $type = $this->tableFields[$field]['type'];
                $type = explode('(', $type)[0];
                $value = str_replace('/\s+/', '', $value);
                switch ($type) {
                    case 'char':
                    case 'text':
                    case 'varchar':
                    case 'tinytext':
                    case 'longtext':
                        $where[] = [$alias.$field, 'like', '%' . $value . '%'];
                        break;
                    case 'int':
                    case 'bigint':
                    case 'integer':
                    case 'tinyint':
                    case 'smallint':
                    case 'mediumint':
                    case 'float':
                    case 'double':
                    case 'timestamp':
                    case 'year':
                        if(strpos($value,',')!==false) {
                            $where[] = [$alias.$field, 'in', explode(',', $value)];
                        } else {
                            $value = str_replace(',', '-', $value);
                            if (strpos($value, '-')) {
    
                                $arr = explode(' - ', $value);
                                if (empty($arr)) {
                                    continue 2;
                                }
                                if (in_array($field, $this->converTime)) {
                                    if (isset($arr[0])) {
                                        $arr[0] = strtotime($arr[0]);
                                    }
                                    if (isset($arr[1])) {
                                        $arr[1] = strtotime($arr[1]);
                                    }
                                }
                                $exp = 'between';
                                if ($arr[0] === '') {
                                    $exp = '<=';
                                    $arr = $arr[1];
                                } elseif ($arr[1] === '') {
                                    $exp = '>=';
                                    $arr = $arr[0];
                                }
                                $where[] = [$alias.$field, $exp, $arr];
                            } else {
                                $where[] = [$alias.$field, '=', $value];
                            }
                        }
                        break;
                    case 'set';
                        $where[] = [$alias.$field, 'find in set', $value];
                        break;
                    case 'enum';
                        $where[] = [$alias.$field, '=', $value];
                        break;
                    case 'date';
                    case 'time';
                    case 'datetime';
                        $value = str_replace(',', '-', $value);
                        if (strpos($value, '-')) {
                            $arr = explode(' - ', $value);
                            if (!array_filter($arr)) {
                                continue 2;
                            }
                            $exp = 'between';
                            if ($arr[0] === '') {
                                $exp = '<=';
                                $arr = $arr[1];
                            } elseif ($arr[1] === '') {
                                $exp = '>=';
                                $arr = $arr[0];
                            }
                            $where[] = [$alias.$field, $exp, $arr];
                        } else {
                            $where[] = [$alias.$field, '=', $value];
                        }
                        break;
                    case 'blob';
                        break;
                    default:
                        // 默认值
                        break;
                }
            }

            // 限制个人数据权限
            $superAdmin = $this->authService->SuperAdmin();
            if (!$superAdmin && $this->dataLimit) {
                if (in_array($this->dataLimitField, $this->tableFields)) {
                    $where[] = [$this->dataLimitField, '=', get_admin_id()];
                }
            } // 限制部门数据权限
            else if (!$superAdmin && $this->departmentLimit
                && in_array('department_id', $this->tableFields)) {
                $where[] = ['department_id', 'in', get_admin_info('AdminLogin.department_id')];
            }
        }

        return $where;
    }

    /**
     * 递归查询父节点
     * @access public
     * @param int $pid 查询条件
     * @param array $array 返回数组
     * @return array
     */
    public function parentNode(int $pid, array &$array = []): array
    {
        $result = $this->model->where('id', $pid)->find()->toArray();
        if (!empty($result)) {
            /**
             * 多语言字段
             */
            if (isset($result['title'])) {
                $result['title'] = __($result['title']);
            }

            $array[] = $result;
            if ($result['pid'] !== 0) {
                $this->parentNode($result['pid'], $array);
            }
        }

        return $array;
    }

    /**
     * 管理员退出
     * @return Response
     */
    public function logout(): Response
    {
        request()->session()->set(AdminEnum::ADMIN_SESSION, null);
        Db::name("security")->where("admin_id",$this->admin_info['id'])->delete();
        return $this->success('退出成功！', '/');
    }

    /**
     * 错误页面
     * @param int $code
     * @param string $msg
     * @return Response
     */
    public function abortPage(string $msg = '', int $code = 404): Response
    {
        $exception = config('app.exception_template');
        if (isset($exception[$code])) {
            $template = @file_get_contents($exception[$code]);
        } else {
            $template = $msg;
        }

        return \response($template, $code);
    }

    public function dataImport()
    {
        if (request()->isPost()) {
            set_time_limit(0);
            $filePath = input('file_1');
            $msg = "";
            $spreadsheet = IOFactory::load('public'.$filePath);
            //获取活动工作表
            $worksheet = $spreadsheet->getActiveSheet();
            $Dictionary = [];
            foreach ($this->columnFormat as $key => $item) {
                if($item[0]==3){
                    $dataList = getDictionary($item[2]);
                    $Dictionary[$item[2]] = $dataList;
                }
            }

            $admin_id = get_admin_id();
            $columns_arr=[];
            $update_field = [];
            $pk_column = "";
            foreach ($this->columnFormat as $key => $vo){
                if ($vo[0] < 1 || $vo[0] > 4) {
                    continue;
                }
                $columns_arr[$vo[1]]=$vo[1];
                if(array_key_exists('column',$vo)){
                    $vo[1] = $vo['column'];
                }
                $update_field[] = $vo[1];
                if($vo[0]==4&&$vo[1]=='id_code'){
                    $pk_column = $key;
                }
            }
            $create_time=date('Y-m-d H:i:s');
            $create_by = $this->admin_info['name'];
            $columns=implode(",",$columns_arr);
            $columns .= ",sort,admin_id";
            $insert_sql = "INSERT INTO ".$this->table_name."(".$columns.") VALUES ";
            $values = [];
            try{
                Db::query("delete from ".$this->table_name." where admin_id=".$admin_id);
                $error_arr = [];
                foreach ($worksheet->getRowIterator() as $i=>$row) {
                    if ($i < $this->row_index||empty($worksheet->getCell("A".$i)->getValue())) {
                        continue;
                    }
                    $cellIterator = $row->getCellIterator();
                    $cellIterator->setIterateOnlyExistingCells(false); // 遍历所有单元格，包括空单元格
                    $values_arr = [];
                    foreach ($cellIterator as $key => $cell) {
                        $value = $cell->getValue();
                        if(empty($this->columnFormat[$key])){
                            return $this->error("存在未定义的excel列，请检查导入表格是否和模板表格格式相同");
                        }
                        if(array_key_exists('required',$this->columnFormat[$key])&&empty($value)){
                            $error_arr[$i].="第{$key}列必填项为空;";
                        }
                        $vo = $this->columnFormat[$key];
                        if (($vo[0] < 1&&$vo != -3) || $vo[0] > 4) {
                            continue;
                        } else {
                            switch ($vo[0]) {
                                case 1:
                                    $value = empty($value)?'':addslashes((string)$value);
                                    break;
                                case 2:
                                    $value = $value == '是' ? 1 : 0;
                                    break;
                                case 3:
                                    $key = array_search($value, $Dictionary[$vo[2]]);
                                    if ($key !== false) {
                                        $value = $key;
                                    } else {
                                        $value = 0;
                                    }
                                    break;
                            }
                            if(array_key_exists($vo[1], $values_arr)&&empty($value)){
                                continue;
                            }
                            if(strpos($vo[1], '_date')&&ctype_digit($value)){
                                $dateValue = Date::excelToDateTimeObject($value);
                                $value = $dateValue->format('Y-m-d'); // 格式化日期
                            }
                            $values_arr[$vo[1]] = "\"".$value."\"";
                        }
                    }
                    $values_sql = "(".implode(",",$values_arr).",".($i-$this->row_index+1).",".$admin_id.")";
                    $values[] = $values_sql;
                    $values_arr = [];
                    if($i%1000==0){
                        // return $this->error($insert_sql.$values[0]);
                        Db::query($insert_sql.implode(",",$values));
                        $values = [];
                    }
                }

                if(count($values)){
                    Db::query($insert_sql.implode(",",$values));
                }
                Db::query("DELETE FROM ".$this->table_name." WHERE admin_id=$admin_id and id_code IS NULL");
                Db::query("UPDATE ".$this->table_name." i INNER JOIN sa_community c on i.community_name=c.community_name set i.community_id=c.id");
                Db::query("UPDATE ".$this->table_name." i INNER JOIN sa_grid g on i.community_id=g.community_id and i.grid_group_name=g.grid_name set i.grid_group_id=g.id");
                Db::query("UPDATE ".$this->table_name." i INNER JOIN sa_grid g on i.community_id=g.community_id and i.grid_name=g.grid_name set i.grid_id=g.id");
                // 此行代码为2027-05-07由周洪利注释掉
                // Db::query("UPDATE ".$this->table_name." i INNER JOIN sa_person s on i.id_code=s.id_code INNER JOIN ".$this->table_source." p on s.id=p.person_id and i.grid_id=p.grid_id set i.".$this->pk."=p.id");
                Db::query("UPDATE ".$this->table_name." i INNER JOIN sa_person s on i.id_code=s.id_code set i.".$this->pk."=s.id");
                $list = Db::table($this->table_name)->where("admin_id",$admin_id)->where("community_id",0)->field("sort")->select()->toArray();
                foreach ($list as $item){
                    $error_arr[$item['sort']+$this->row_index-1].="找不到对应的社区信息;";
                }
                $list = Db::table($this->table_name)->where("admin_id",$admin_id)->where("grid_group_id",0)->field("sort")->select()->toArray();
                foreach ($list as $item){
                    $error_arr[$item['sort']+$this->row_index-1].="找不到对应的网格组信息;";
                }
                $list = Db::table($this->table_name)->where("admin_id",$admin_id)->where("grid_id",0)->field("sort")->select()->toArray();
                foreach ($list as $item){
                    $error_arr[$item['sort']+$this->row_index-1].="找不到对应的网格信息;";
                }
                $list = Db::table($this->table_name)->alias("i")->join("sa_person p","i.id_code=p.id_code and p.delete_time is null","LEFT")->where("i.admin_id",$admin_id)->where("p.id is null")->field("i.sort")->select()->toArray();
                foreach ($list as $item){
                    $error_arr[$item['sort']+$this->row_index-1].="找不到对应的人口信息;";
                }
                $list = Db::table($this->table_name)->alias("i")
                    ->join("sa_person p","i.id_code=p.id_code and p.delete_time is null")
                    ->join($this->table_source." s","s.person_id=p.id and i.jurisdiction=s.jurisdiction")
                    ->join("sa_community c", "s.community_id=c.id", "LEFT")
                    ->join("sa_grid g", "s.grid_id=g.id", "LEFT")
                    ->join("sa_grid gg", "s.grid_group_id=gg.id", "LEFT")
                    ->field("i.name,i.sort,g.grid_name,gg.grid_name grid_group_name,c.community_name")
                    ->where("admin_id",$admin_id)
                    ->where("(s.community_id<>i.community_id or s.grid_group_id<>i.grid_group_id or s.grid_id <> i.grid_id)")
                    ->field("i.sort")->select()->toArray();
                foreach ($list as $item){
                    $error_arr[$item['sort']+$this->row_index-1].="{$item['name']}(管理类型相同)已存在于{$item['community_name']},{$item['grid_group_name']},{$item['grid_name']};";
                }
                if (strpos($this->table_name, "oldest") !== false||strpos($this->table_name, "elderly") !== false) {
                    $min_age = 60;
                    if(strpos($this->table_name, "oldest") !== false){
                        $min_age = 80;
                    }
                    $list = Db::table($this->table_name)->alias("i")->join("sa_person p","i.id_code=p.id_code","LEFT")->where("i.admin_id",$admin_id)->where("p.age < $min_age")->field("i.sort")->select()->toArray();
                    foreach ($list as $item){
                        $error_arr[$item['sort']+$this->row_index-1].="年龄未满".$min_age."岁;";
                    }
                }else if(strpos($this->table_name, "children") !== false){
                    $list = Db::table($this->table_name)->alias("i")->join("sa_person p","i.id_code=p.id_code","LEFT")->where("i.admin_id",$admin_id)->where("p.age > 18")->field("i.sort")->select()->toArray();
                    foreach ($list as $item){
                        $error_arr[$item['sort']+$this->row_index-1].="年龄超过18岁;";
                    }
                }
                if(count($error_arr)>0){
                    return $this->error("信息校验未通过",'',['data'=>$error_arr]);
                }
                Db::startTrans();
                $sql = $this->buildUpdSql($admin_id);
                Db::query($sql);
                $field=implode(",",$update_field);
                $field.=",sort,person_id";
                $insert_sql = "INSERT INTO ".$this->table_source." ({$field},create_time,create_by) select {$field},'$create_time' create_time,'$create_by' create_by from ".$this->table_name." where admin_id = {$admin_id} ";
                Db::query($insert_sql);
                Db::query("delete from ".$this->table_name." where admin_id=".$admin_id);
//                $Person = new Person();
//                $Person->rbacReverseImp($this->table_source);
            }catch (\Exception $e){
                Db::rollback();
                return $this->error($e->getMessage());
                return $this->error("导入失败，请检查模板");
            }
            Db::commit();
            return $this->success( '导入成功');
        }
        return $this->view('/public/data_import');
    }

    public function excelExport($list = [])
    {
        if($this->table_source=='sa_person_elderly'){
            $type = (int)input('type', 1);
            if($type==1){
                $this->tpl_name = "空巢老人模版";
            }else if($type==2){
                $this->tpl_name = "孤寡老人模版";
            }else if($type==3){
                $this->tpl_name = "独居老人模版";
            }
        }
        // 文件路径
        $filePath = 'public/tpl/'.$this->tpl_name.'.xlsx';

        // 读取Excel文件
        $spreadsheet = IOFactory::load($filePath);

        // 获取活动工作表
        $sheet = $spreadsheet->getActiveSheet();
        $excelService = new ExcelService();
        $integerValidation = $excelService->integerValidation();
        $decimalValidation = $excelService->decimalValidation();
        $idcardValidation = $excelService->idcardValidation();
        $count = 1000;
        if($list){
            $count = count($list);
        }
        $dataList = ['是', '否'];
        $optionValidation = $excelService->optionValidation($dataList);
        $Dictionary = [];
        $id_code_key = "";
        $birth_key = "";
        $age_key = "";
        $sex_key = "";
        $id_code_item = [];
        foreach ($this->columnFormat as $key=>$item) {
            if($item[0]==2||$item[0]==-2){
                $cellRange = $key .$this->row_index. ':' . $key . ($this->row_index+$count);
                $sheet->setDataValidation($cellRange, $optionValidation);
            }
            if($item[0]==3||$item[0]==-3||$item[0]==8){
                $cellRange = $key . $this->row_index. ':' . $key . ($this->row_index+$count);
                $dataList = getDictionary($item[2]);
                $Dictionary[$item[2]] = $dataList;
                $dataList = array_values($dataList);
                if(strpos($item[1], "relation") !== false){
                    $optionListValidation = $excelService->optionRelationValidation($dataList);
                }else if($item[0]==8){
                    $optionListValidation = $idcardValidation;
                }else{
                    $optionListValidation = $excelService->optionValidation($dataList);
                }
                $sheet->setDataValidation($cellRange, $optionListValidation);
            }
            if(array_key_exists('number',$item)){
                $cellRange = $key.$this->row_index. ':' .$key. ($this->row_index+$count);
                $sheet->setDataValidation($cellRange, $integerValidation);
            }
            if($item[1]=='id_code'){
                $id_code_key = $key;
            }else if($item[1]=='birthday'){
                $birth_key = $key;
                $sheet->getStyle("$birth_key".$this->row_index.":$birth_key". ($this->row_index+$count)+10)->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_DATE_YYYYMMDD2);
            }else if($item[1]=='age'){
                $age_key = $key;
                $sheet->getStyle("$age_key".$this->row_index.":$age_key". ($this->row_index+$count)+10)->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_GENERAL);
            }else if($item[1]=='gender'){
                $sex_key = $key;
            }else if(array_key_exists('id_code_type',$item)){
                $id_code_item[$key] =$item;
                if($item['id_code_type']=='birthday'){
                    $sheet->getStyle("$key".$this->row_index.":$key". ($this->row_index+$count)+10)->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_DATE_YYYYMMDD2);
                }else if($item['id_code_type']=='age'){
                    $sheet->getStyle("$key".$this->row_index.":$key". ($this->row_index+$count)+10)->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_GENERAL);
                }
            }
        }

        if (count($list) > 0) {
            $i = $this->row_index;
            foreach ($list as $item) {
                foreach ($this->columnFormat as $column => $value) {
                    switch ($value[0]) {
                        case 0:
                            if($value[1]=='index'){
                                $sheet->setCellValueExplicit($column . $i, $i-$this->row_index+1, DataType::TYPE_STRING);
                            }else{
                                $sheet->setCellValue($column . $i, $value[1]);
                            }
                            break;
                        case 1:
                            $item[$value[1]]=$item[$value[1]]=='0000-00-00'?'':$item[$value[1]];
                            $sheet->setCellValueExplicit($column . $i, trim((string)$item[$value[1]], "\""), DataType::TYPE_STRING);
                            break;
                        case 2:
                        case -2:
                            $sheet->setCellValue($column . $i, $item[$value[1]] == 1 ? '是' : '否');
                            break;
                        case 3:
                        case -3:
                            $index = $item[$value[1]];
                            if ($index>=0) {
                                $sheet->setCellValue($column . $i, ( empty($Dictionary[$value[2]][$index]) && !isset($Dictionary[$value[2]][$index]) ) ? '' : $Dictionary[$value[2]][$index]);
                            }
                            break;
                        case 6:
                            break;
                        case 5:
                            $sheet->setCellValue($column . $i, $item[$value[1]] != $value[2] ? '是' : '否');
                            break;
                        case 4:
                            $sheet->setCellValueExplicit($column . $i, $item[$value[1]], DataType::TYPE_STRING);
                            break;
                        case 8:
                            $index = $item[$value[1]];
                            if ($index) {
                                $title = [];
                                $index = explode(",",$index);
                                foreach ($index as $vo){
                                    if($vo){
                                        $title[] = $Dictionary[$value[2]][$vo];
                                    }
                                }
                                $title = implode(",",$title);
                                $sheet->setCellValue($column . $i, empty($title) ? '' : $title);
                            }
                            break;
                        case 9:
                            if(empty($item[$value[1]])){
                                break;
                            }
                            $sheet->setCellValue($column . $i, "附件");
                            $sheet->getCell($column . $i)->getHyperlink()->setUrl('http://*************'.$item[$value[1]]);
                            $sheet->getCell($column . $i)->getHyperlink()->setTooltip('点击进行下载');
                            break;
                    }
                }
                if($id_code_key){
                    if($birth_key){
                        $sheet->setCellValue($birth_key . $i, "=DATE(MID($id_code_key$i,7,4),MID($id_code_key$i,11,2),MID($id_code_key$i,13,2))");
                    }
                    if($age_key){
                        $sheet->setCellValue($age_key . $i, "=INT((TODAY()-DATE(MID($id_code_key$i,7,4),MID($id_code_key$i,11,2),MID($id_code_key$i,13,2)))/365.25)");
                    }
                    if($sex_key){
                        $sheet->setCellValue($sex_key . $i, "=IF(MOD(MID($id_code_key$i,17,1),2)=0,\"女\",\"男\")");
                    }
                }
                foreach ($id_code_item as $key =>$item){
                    if($item['id_code_type']=='birthday'){
                        $sheet->setCellValue($key . $i, "=DATE(MID(".$item['id_code_key']."$i,7,4),MID(".$item['id_code_key']."$i,11,2),MID(".$item['id_code_key']."$i,13,2))");
                    }else if($item['id_code_type']=='age'){
                        $sheet->setCellValue($key . $i, "=INT((TODAY()-DATE(MID(".$item['id_code_key']."$i,7,4),MID(".$item['id_code_key']."$i,11,2),MID(".$item['id_code_key']."$i,13,2)))/365.25)");
                    }else if($item['id_code_type']=='gender'){
                        $sheet->setCellValue($sex_key . $i, "=IF(MOD(MID(".$item['id_code_key']."$i,17,1),2)=0,\"女\",\"男\")");
                    }
                }
                $i++;
            }
            if (mb_substr($this->tpl_name,0,4)!='充分就业'){
                // for($j=0;$j<10;$j++){
                //     if($id_code_key){
                //         if($birth_key){
                //             $sheet->setCellValue($birth_key . $i, "=DATE(MID($id_code_key$i,7,4),MID($id_code_key$i,11,2),MID($id_code_key$i,13,2))");
                //         }
                //         if($age_key){
                //             $sheet->setCellValue($age_key . $i, "=INT((TODAY()-DATE(MID($id_code_key$i,7,4),MID($id_code_key$i,11,2),MID($id_code_key$i,13,2)))/365.25)");
                //         }
                //         if($sex_key){
                //             $sheet->setCellValue($sex_key . $i, "=IF(MOD(MID($id_code_key$i,17,1),2)=0,\"女\",\"男\")");
                //         }
                //     }
                //     foreach ($id_code_item as $key =>$item){
                //         if($item['id_code_type']=='birthday'){
                //             $sheet->setCellValue($key . $i, "=DATE(MID(".$item['id_code_key']."$i,7,4),MID(".$item['id_code_key']."$i,11,2),MID(".$item['id_code_key']."$i,13,2))");
                //         }else if($item['id_code_type']=='age'){
                //             $sheet->setCellValue($key . $i, "=INT((TODAY()-DATE(MID(".$item['id_code_key']."$i,7,4),MID(".$item['id_code_key']."$i,11,2),MID(".$item['id_code_key']."$i,13,2)))/365.25)");
                //         }else if($item['id_code_type']=='gender'){
                //             $sheet->setCellValue($sex_key . $i, "=IF(MOD(MID(".$item['id_code_key']."$i,17,1),2)=0,\"女\",\"男\")");
                //         }
                //     }
                //     $i++;
                // }
            }
            // 保存Excel文件
            $writer = new Xlsx($spreadsheet);
            $day = date('Ymd');
            $time = date('his');
            $dir = '/download/' . $day;
            if (!file_exists('public' . $dir)) {
                mkdir('public' . $dir, 0777, true);
            }
            $filePath = $dir . '/'.$this->tpl_name.'(' . $day . $time . ').xlsx';
            $writer->save('public' . $filePath);
            return $filePath;
        }
        // 保存Excel文件
        $writer = new Xlsx($spreadsheet);
        $writer->save('public/tpl/'.$this->tpl_name.'.xlsx');
        return '/tpl/'.$this->tpl_name.'.xlsx';
    }

    public function downloadExcelTpl()
    {
        $this->excelExport();
        return $this->redirect('/tpl/'.$this->tpl_name.'.xlsx');
    }

    public function buildUpdSql($admin_id){
        $this->tableFields = $this->model->getFields();
        $fieldColumn="UPDATE ".$this->table_source." p INNER JOIN ".$this->table_name." i on i.".$this->pk."=p.id and i.admin_id=".$admin_id." set ";
        foreach ($this->columnFormat as $item){
            $field = $item[1];

            if (array_key_exists('column',$item)){
                $field = $item['column'];
            }
            if (!array_key_exists($field, $this->tableFields)) {
                continue;
            }
            $fieldColumn.="p.".$field."=i.".$field.",";
        }
        $update_time=date('Y-m-d H:i:s');
        $update_by = $this->admin_info['name'];
        return $fieldColumn."p.sort=i.sort,p.update_time='$update_time',p.update_by='$update_by';";
    }

    public function dataToExcel()
    {
        $alias = "h";
        $where = $this->buildSelectParams($alias.".");
        $keyword = input('keyword','');
        if($keyword){
            $where[] = ["p.name|"."p.id_code", 'like', '%' . $keyword . '%'];
        }
        $fieldList = $this->model->getFields();
        $order = !array_key_exists('sort', $fieldList) ? $alias.'.id' : $alias.'.sort';
        $order = "$alias.community_id,$alias.grid_group_id,$alias.grid_id,".$order;
        $list = $this->model->alias($alias)
            ->join("sa_person p", $alias.".person_id=p.id and p.delete_time is null and dead = 0")
            ->join("sa_community c",$alias.".community_id=c.id","LEFT")
            ->join("sa_grid g",$alias.".grid_id=g.id","LEFT")
            ->join("sa_grid gg",$alias.".grid_group_id=gg.id","LEFT")
            ->field("$alias.*,
            g.grid_name,gg.grid_name grid_group_name,c.community_name,
            p.name,p.id_code,p.birth,p.sex,p.age,p.nation,p.education,p.school,p.major,p.hometown,p.address,p.phone,p.wechat_code,p.health")
            ->where($where)->order($order)->select()->toArray();
        $filePath = $this->excelExport($list);
        if(request()->isPost()){
            return $this->success('',$filePath);
        }
        return $this->redirect($filePath);
    }

    public function dateFormatUpd($admin_id){
        foreach ($this->columnFormat as $item){
            if(array_key_exists("date",$item)){
                $sql = "UPDATE {$this->table_name} set ".$item[1]." = DATE_ADD('1899-12-30', INTERVAL ".$item[1]." DAY) WHERE admin_id=".$admin_id." and ".$item[1]." REGEXP '^[0-9]+$'";
                Db::query($sql);
            }
        }
    }

    public function jurisdictionCheck(){
        $id = (int)input("id",0);
        $where = [];
        $alias = "a";
        if($id){
            $where[] = [$alias.".id","<>",$id];
        }
        $where['p.id_code']=input("id_code",'');
        $where[$alias.'.jurisdiction']=input("jurisdiction",'');
        $data = $this->model->alias($alias)
            ->join("sa_person p",$alias.".person_id=p.id and (p.delete_time is null or (p.delete_flag=1 and p.grid_id <> $alias.grid_id)) and dead = 0")
            ->join("sa_community c",$alias.".community_id=c.id","LEFT")
            ->join("sa_grid g",$alias.".grid_id=g.id","LEFT")
            ->join("sa_grid gg",$alias.".grid_group_id=gg.id","LEFT")
            ->field("g.grid_name,gg.grid_name grid_group_name,c.community_name")
            ->where($where)->find();
        if($data){
            return $this->error("该管理类型已存在于".$data['community_name'].",".$data['grid_group_name'].",".$data['grid_name']);
        }else{
            return $this->success($this->model->getLastSql());
        }
    }
    public function noRepeat(){
//         Db::query("CREATE TEMPORARY TABLE temp_{$this->table_source} AS
// SELECT person_id, min(id) as min_id,grid_id
// FROM {$this->table_source}
// GROUP BY person_id,grid_id;");
//         Db::query("DELETE FROM {$this->table_source}
// WHERE id NOT IN (SELECT min_id FROM temp_{$this->table_source});");
//         Db::query("DROP TEMPORARY TABLE temp_{$this->table_source};");
    }

    public function proData(){
        $this->checkRule=[
            ['type'=>'required','title'=>'必填项未填','columns'=>"community_id,grid_group_id,grid_id,jurisdiction"],
            ['type'=>'pending','title'=>'人口信息被挂起'],
        ];
        if (request()->isAjax()) {
            $page = (int)input('page', 1);
            $limit = (int)input('limit', 18);
            $alias = "e";
            $this->keepField='sex';
            $where = $this->buildSelectParams($alias.".");
            $key = input("check_type",0);
            $check_where = $this->buildCheckWhere("$alias.");
            $count = $this->model->alias($alias)
                ->join("sa_person p",$alias.".person_id=p.id")
                ->where($where)->where($check_where)->count();
            //$page = $count <= $limit ? 1 : $page;
            $fieldList = $this->model->getFields();
            $order = !array_key_exists('sort', $fieldList) ? $alias.'.id' : $alias.'.sort';
            $order = "$alias.community_id,$alias.grid_group_id,$alias.grid_id,".$order;
            $subQuery = $this->model->alias($alias)
                ->join("sa_person p",$alias.".person_id=p.id")
                ->field($alias.'.id')->where($where)->where($check_where)->order($order)->limit($limit)->page($page)->buildSql();
            $subQuery = '( SELECT object.id FROM ' . $subQuery . ' AS object )';
            $list = $this->model->alias($alias)
                ->join("sa_community c",$alias.".community_id=c.id","LEFT")
                ->join("sa_grid g",$alias.".grid_id=g.id","LEFT")
                ->join("sa_grid gg",$alias.".grid_group_id=gg.id","LEFT")
                ->join("sa_person p",$alias.".person_id=p.id and (p.delete_time is null or (p.delete_flag=1 and p.grid_id <> $alias.grid_id)) and dead = 0")
                ->join("sa_building b", "p.building_id=b.id","LEFT")
                ->field("'{$this->checkRule[$key]['title']}' check_name, ".$alias.".*,g.grid_name,gg.grid_name grid_group_name,c.community_name,
                p.name,p.id_code,p.age,p.sex,p.address,p.face,p.nation,p.education,p.school,p.major,p.hometown,p.phone,b.building_name")
                ->where($alias.'.id in' . $subQuery)->order($order)->select()->toArray();
            return $this->success($this->model->getLastSql(), '/', $list, $count);
        }
        return $this->view('/public/pro_data',['checkRule'=>$this->checkRule]);
    }

    public function buildCheckWhere($alias,$key=0){
        $where = "";
        $key = input("check_type",$key);
        if(!array_key_exists($key,$this->checkRule)){
            return $where;
        }
        $rule = $this->checkRule[$key];
        switch ($rule['type']){
            case "required":
                $columns = explode(",",$rule['columns']);
                foreach ($columns as &$item){
                    $item = "($alias$item  IS NULL or $alias$item ='')";
                }
                $where = implode(" OR ",$columns);
                break;

            case "least":
                $columns = explode(",",$rule['columns']);
                foreach ($columns as &$item){
                    $item = "($alias$item  IS NULL)";
                }
                $where = implode(" AND ",$columns);
                break;
            case "association":
                $columns = $rule['columns'];
                foreach ($columns as $key=> &$item){
                    foreach ($item as &$vo){
                        $vo = "$alias$vo  IS NULL";
                    }
                    $item = "($alias$key is not null and (".implode(" OR ",$item)."))";
                }
                $where = implode(" or ",$columns);
                break;
            case "beforeToday":
                $today = date("Y-m-d");
                $columns = explode(",",$rule['columns']);
                foreach ($columns as &$item){
                    $item = "($alias$item  >= '$today')";
                }
                $where = implode(" OR ",$columns);
                break;
            case "afterToday":
                $today = date("Y-m-d");
                $columns = explode(",",$rule['columns']);
                foreach ($columns as &$item){
                    $item = "($alias$item  <= '$today')";
                }
                $where = implode(" OR ",$columns);
                break;
            case "compare":
                $columns = $rule['columns'];
                $where = "$alias{$columns['start']} > $alias{$columns['end']}";
                break;
            case "compareVal":
                $columns = $rule['columns'];
                $where = $columns;
                break;
            case "idCard":
                $columns = explode(",",$rule['columns']);
                foreach ($columns as &$item){
                    $item = "( LENGTH($alias$item)>0 and NOT ($alias$item REGEXP '^[0-9]{17}[0-9Xx]$'))";
                }
                $where = implode(" OR ",$columns);
                break;
            case "phone":
                $columns = explode(",",$rule['columns']);
                foreach ($columns as &$item){
                    $item = "( LENGTH($alias$item)>0 and NOT ($alias$item REGEXP '^1[3-9][0-9]{9}$')) and NOT ($alias$item REGEXP '^[0-9]{3,4}[0-9]{7,8}(-[0-9]{1,4})?$')";
                }
                $where = implode(" OR ",$columns);
                break;
            case "pending":
                $where = "p.delete_flag=1";
        }
        return $where;
    }
}