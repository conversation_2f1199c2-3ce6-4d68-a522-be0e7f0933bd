<?php
declare (strict_types=1);
// +----------------------------------------------------------------------
// | swiftAdmin 极速开发框架 [基于WebMan开发]
// +----------------------------------------------------------------------
// | Copyright (c) 2020-2030 http://www.swiftadmin.net
// +----------------------------------------------------------------------
// | swiftAdmin.NET High Speed Development Framework
// +----------------------------------------------------------------------
// | Author: meystack <<EMAIL>> Apache 2.0 License
// +----------------------------------------------------------------------
namespace app;

use app\common\service\user\UserService;
use support\Response;

/**
 * Api全局控制器基类
 * Class ApiController
 * @package app
 * <AUTHOR> <
 */
class ApiController extends BaseController
{
    /**
     * 当前表字段
     * @var array
     */
    protected array $tableFields = [];

    /**
     * 默认开关
     * @var string
     */
    protected string $keepField = 'status';

    /**
     * 查询过滤字段
     * @var array
     */
    protected array $filterWhere = ['page', 'limit'];

    /**
     * 初始化方法
     */
    public function __construct()
    {
        error_reporting(E_ERROR | E_PARSE);
        parent::__construct();
    }

    /**
     * 操作成功跳转
     * @access protected
     * @param mixed $msg
     * @param mixed $url
     * @param mixed $data
     * @param int $count
     * @param int $code
     * @param int $wait
     * @param array $header
     * @return Response
     */
    protected function success(mixed $msg = '', mixed $url = '', mixed $data = '', int $count = 0, int $code = 200, int $wait = 3, array $header = []): Response
    {
        $msg = !empty($msg) ? __($msg) : __('操作成功！');
        $result = ['code' => $code, 'msg' => $msg, 'data' => $data, 'count' => $count, 'url' => (string)$url, 'wait' => $wait];
        return json($result);
    }

    /**
     * 操作错误跳转的快捷方法
     * @access protected
     * @param mixed $msg 提示信息
     * @param mixed $url 跳转的URL地址
     * @param mixed $data 返回的数据
     * @param int $code
     * @param integer $wait 跳转等待时间
     * @param array $header 发送的Header信息
     * @return Response
     */
    protected function error(mixed $msg = '', mixed $url = '', mixed $data = '', int $code = 101, int $wait = 3, array $header = []): Response
    {
        $msg = !empty($msg) ? __($msg) : __('操作失败！');
        $result = ['code' => $code, 'msg' => $msg, 'data' => $data, 'url' => (string)$url, 'wait' => $wait];
        return json($result);
    }

    /**
     * 退出登录
     * @access public
     */
    public function logOut(): Response
    {
        UserService::logout();
        return $this->success('退出成功', '/');
    }

    protected function buildSelectParams($alias="",$allow=true): array
    {
        $where = [];
        $params = request()->all();
        if (!empty($params) && is_array($params)) {

            $this->tableFields = $this->model->getFields();
            $old_alias = $alias;
            $admin_info = get_admin_info();
            if($allow&&array_key_exists("community_id", $this->tableFields)){
                if($admin_info['community_id']>0&&empty($params['community_id'])){
                    $where[$alias.'community_id'] = $admin_info['community_id'];
                    if($admin_info['grid_group_id']>0&&empty($params['grid_group_id'])){
                        $where[$alias.'grid_group_id'] = $admin_info['grid_group_id'];
                        if($admin_info['grid_id']>0&&empty($params['grid_id'])){
                            $where[$alias.'grid_id'] = $admin_info['grid_id'];
                        }
                    }
                }
            }
            foreach ($params as $field => $value) {
                if(strpos($field, '__') !== false){
                    $arr = explode('__',$field);
                    $alias = $arr[0].".";
                    $field = $arr[1];
                }else{
                    $alias = $old_alias;
                }
                if(empty($value)){
                    continue;
                }
                if($field=='id'){
                    $where[]=[$alias.'id','in',implode(",",$value)];
                    continue;
                }
                // 过滤字段
                if (in_array($field, $this->filterWhere)) {
                    continue;
                }
                // 非表内字段
                if (!array_key_exists($field, $this->tableFields)) {
                    if($old_alias != $alias){
                        $where[] = [$alias.$field, '=', intval($value )];
                    }
                    continue;
                }

                // 默认状态字段
                if ($field == $this->keepField && $value) {
                    $where[] = [$alias.$field, '=', intval($value - 1)];
                    continue;
                }
                // 获取类型
                $type = $this->tableFields[$field]['type'];
                $type = explode('(', $type)[0];
                $value = str_replace('/\s+/', '', (string)$value);
                switch ($type) {
                    case 'char':
                    case 'text':
                    case 'varchar':
                    case 'tinytext':
                    case 'longtext':
                        $where[] = [$alias.$field, 'like', '%' . $value . '%'];
                        break;
                    case 'int':
                    case 'bigint':
                    case 'integer':
                    case 'tinyint':
                    case 'smallint':
                    case 'mediumint':
                    case 'float':
                    case 'double':
                    case 'timestamp':
                    case 'year':
                        $value = str_replace(',', '-', (string)$value);
                        if (strpos($value, '-')) {

                            $arr = explode(' - ', $value);
                            if (empty($arr)) {
                                continue 2;
                            }
                            if (in_array($field, $this->converTime)) {
                                if (isset($arr[0])) {
                                    $arr[0] = strtotime($arr[0]);
                                }
                                if (isset($arr[1])) {
                                    $arr[1] = strtotime($arr[1]);
                                }
                            }
                            $exp = 'between';
                            if ($arr[0] === '') {
                                $exp = '<=';
                                $arr = $arr[1];
                            } elseif ($arr[1] === '') {
                                $exp = '>=';
                                $arr = $arr[0];
                            }
                            $where[] = [$alias.$field, $exp, $arr];
                        } else {
                            $where[] = [$alias.$field, '=', $value];
                        }
                        break;
                    case 'set';
                        $where[] = [$alias.$field, 'find in set', $value];
                        break;
                    case 'enum';
                        $where[] = [$alias.$field, '=', $value];
                        break;
                    case 'date';
                    case 'time';
                    case 'datetime';
                        $value = str_replace(',', '-', (string)$value);
                        if (strpos($value, '-')) {
                            $arr = explode(' - ', $value);
                            if (!array_filter($arr)) {
                                continue 2;
                            }
                            $exp = 'between';
                            if ($arr[0] === '') {
                                $exp = '<=';
                                $arr = $arr[1];
                            } elseif ($arr[1] === '') {
                                $exp = '>=';
                                $arr = $arr[0];
                            }
                            $where[] = [$alias.$field, $exp, $arr];
                        } else {
                            $where[] = [$alias.$field, '=', $value];
                        }
                        break;
                    case 'blob';
                        break;
                    default:
                        // 默认值
                        break;
                }
            }
        }

        return $where;
    }
}
