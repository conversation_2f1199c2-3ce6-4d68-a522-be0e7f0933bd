<?php

namespace app\common\model\sc;

use think\Model;


/**
 * sc_dynamics
 * <!---->
 * 要闻动态
 * Class ScDynamics
 * @package app\common\model\sc
 */
class ScDynamics extends Model
{

    

    // 定义时间戳字段名
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';
    protected $deleteTime = false;
    public $create_by = 'create_by';
    public $update_by = 'update_by';
    

        public function setImageAttr($value)
    {
        if (!empty($value)) {
            $value = serialize($value);
        }

        return $value;
    }

    public function getImageAttr($value)
    {
        if (!empty($value)) {
            $value = unserialize($value);
        }

        return $value;
    }

    public function setContentAttr($value)
    {
        if (!empty($value)) {
            $value = serialize($value);
        }

        return $value;
    }

    public function getContentAttr($value)
    {
        if (!empty($value)) {
            $value = unserialize($value);
        }

        return $value;
    }

}