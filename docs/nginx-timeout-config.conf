# Nginx超时配置模板
# 在宝塔面板 -> 网站 -> 设置 -> 配置文件中添加以下配置

server {
    listen 80;
    server_name your-domain.com;
    
    # 全局超时设置
    client_body_timeout 300s;
    client_header_timeout 300s;
    send_timeout 300s;
    keepalive_timeout 300s;
    
    # 增加请求大小限制
    client_max_body_size 100M;
    client_body_buffer_size 128k;
    
    # API接口专用配置
    location /api/ {
        proxy_pass http://127.0.0.1:8686;
        
        # 关键：大幅增加超时时间
        proxy_connect_timeout 120s;
        proxy_send_timeout 600s;      # 10分钟发送超时
        proxy_read_timeout 600s;      # 10分钟读取超时
        
        # 增加缓冲区大小
        proxy_buffering on;
        proxy_buffer_size 256k;
        proxy_buffers 64 256k;
        proxy_busy_buffers_size 512k;
        proxy_temp_file_write_size 512k;
        
        # 请求头设置
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 禁用缓存，确保实时数据
        proxy_cache off;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }
    
    # 其他location配置...
}

# 如果使用HTTPS，还需要在443端口的server块中添加相同的配置
