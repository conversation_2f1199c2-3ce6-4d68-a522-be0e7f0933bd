# Nginx配置优化建议

## 针对大数据响应的Nginx配置

在宝塔面板中，需要修改以下Nginx配置来支持大数据响应：

### 1. 站点配置文件修改

在宝塔面板 -> 网站 -> 设置 -> 配置文件中添加以下配置：

```nginx
server {
    # ... 其他配置 ...
    
    # 增加缓冲区大小
    proxy_buffering on;
    proxy_buffer_size 128k;
    proxy_buffers 32 128k;
    proxy_busy_buffers_size 256k;
    
    # 增加超时时间
    proxy_connect_timeout 60s;
    proxy_send_timeout 300s;
    proxy_read_timeout 300s;
    
    # 增加客户端请求大小限制
    client_max_body_size 100M;
    client_body_buffer_size 128k;
    
    # 针对API接口的特殊配置
    location /api/ {
        proxy_pass http://127.0.0.1:8686;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 大数据响应专用配置
        proxy_buffering on;
        proxy_buffer_size 256k;
        proxy_buffers 64 256k;
        proxy_busy_buffers_size 512k;
        proxy_temp_file_write_size 512k;
        
        # 超时配置
        proxy_connect_timeout 60s;
        proxy_send_timeout 600s;
        proxy_read_timeout 600s;
        
        # 禁用缓存，确保实时数据
        proxy_cache off;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }
}
```

### 2. 全局Nginx配置优化

在宝塔面板 -> 软件商店 -> Nginx -> 设置 -> 配置修改中：

```nginx
http {
    # ... 其他配置 ...
    
    # 增加工作进程连接数
    worker_connections 2048;
    
    # 优化文件传输
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    
    # 增加哈希表大小
    server_names_hash_bucket_size 128;
    client_header_buffer_size 32k;
    large_client_header_buffers 4 32k;
    
    # 全局超时设置
    keepalive_timeout 65;
    client_body_timeout 60;
    client_header_timeout 60;
    send_timeout 60;
    
    # 压缩配置（与应用层压缩配合）
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml;
}
```

### 3. 系统级优化

#### Windows系统优化

1. **增加TCP连接数限制**：
   - 打开注册表编辑器
   - 导航到 `HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Services\Tcpip\Parameters`
   - 创建或修改 `TcpNumConnections` (DWORD) = 65534

2. **优化内存管理**：
   - 确保系统有足够的虚拟内存
   - 建议设置虚拟内存为物理内存的1.5-2倍

#### PHP配置优化

在宝塔面板 -> 软件商店 -> PHP -> 设置 -> 配置文件中：

```ini
; 内存和时间限制
memory_limit = 2G
max_execution_time = 600
max_input_time = 600

; 输出缓冲
output_buffering = 4096
implicit_flush = Off

; 文件上传
upload_max_filesize = 100M
post_max_size = 100M
max_file_uploads = 20

; 会话配置
session.gc_maxlifetime = 3600
session.cache_expire = 180
```

### 4. 监控和调试

#### 启用Nginx状态监控

```nginx
location /nginx_status {
    stub_status on;
    access_log off;
    allow 127.0.0.1;
    deny all;
}
```

#### 启用详细日志

```nginx
# 在server块中添加
error_log /www/wwwlogs/your_domain_error.log debug;
access_log /www/wwwlogs/your_domain_access.log combined;
```

### 5. 测试验证

#### 测试大数据传输

```bash
# 测试上传大文件
curl -X POST -H "Content-Type: application/octet-stream" \
     --data-binary @large_file.dat \
     http://your-domain.com/api/test

# 测试下载大响应
curl -o response.dat http://your-domain.com/api/large-response
```

#### 监控连接状态

```bash
# 查看Nginx状态
curl http://127.0.0.1/nginx_status

# 监控错误日志
tail -f /www/wwwlogs/your_domain_error.log
```

### 6. 故障排除

#### 常见错误及解决方案

1. **502 Bad Gateway**：
   - 检查Workerman进程是否正常运行
   - 增加proxy_read_timeout时间

2. **504 Gateway Timeout**：
   - 增加proxy_read_timeout和proxy_send_timeout
   - 优化后端处理速度

3. **413 Request Entity Too Large**：
   - 增加client_max_body_size
   - 检查PHP的post_max_size设置

4. **Connection reset by peer**：
   - 检查proxy_buffer相关配置
   - 增加worker_connections数量

通过以上配置优化，应该能够显著改善大数据响应的处理能力。
