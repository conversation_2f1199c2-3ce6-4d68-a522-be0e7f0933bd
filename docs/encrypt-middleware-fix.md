# Webman 加密中间件 POST 参数问题解决方案

## 问题描述

在 Webman 1.5.5 中，自定义的加密中间件在解密 POST 提交的接口参数后，无法在控制器中正确获取这些参数。

## 问题原因

1. **Request 对象内部结构**：Webman 的 Request 类继承自 Workerman 的 Request 类，POST 数据存储在内部的 `_data['post']` 数组中，而不是直接的公共属性。

2. **解析机制**：`post()` 方法会调用 `parsePost()` 方法来解析原始请求体，直接修改对象属性不会影响这个解析过程。

3. **属性访问限制**：`_data` 属性是受保护的，无法直接访问和修改。

## 解决方案

### 方案一：反射设置内部数据（推荐）

修改后的 `EncryptMiddleware.php`：

```php
<?php

namespace app\api\middleware\system;

use Webman\Http\Request;
use Webman\Http\Response;
use Webman\MiddlewareInterface;

class EncryptMiddleware implements MiddlewareInterface
{
    public function process(Request $request, callable $handler): Response
    {
        // 解密请求数据
        if($request->method() != 'GET') {
            $raw = $request->rawBody();
            if($raw) {
                $decrypted = $this->aesDecrypt($raw);
                $decryptedData = json_decode($decrypted, true);
                
                if ($decryptedData !== null && is_array($decryptedData)) {
                    // 尝试使用反射设置POST数据
                    if (!$this->setRequestPostData($request, $decryptedData)) {
                        // 如果反射失败，使用包装类
                        $request = new DecryptedRequest($request, $decryptedData);
                    }
                }
            }
        }

        $response = $handler($request);
        return $response;
    }
    
    /**
     * 设置请求的POST数据
     * @return bool 成功返回true，失败返回false
     */
    private function setRequestPostData(Request $request, array $data): bool
    {
        try {
            // 通过反射访问父类的_data属性
            $reflection = new \ReflectionClass($request);
            while ($reflection) {
                if ($reflection->hasProperty('_data')) {
                    $dataProperty = $reflection->getProperty('_data');
                    $dataProperty->setAccessible(true);
                    
                    $currentData = $dataProperty->getValue($request);
                    if (!is_array($currentData)) {
                        $currentData = [];
                    }
                    
                    $currentData['post'] = $data;
                    $dataProperty->setValue($request, $currentData);
                    return true;
                }
                $reflection = $reflection->getParentClass();
            }
            
            return false;
            
        } catch (\Exception $e) {
            error_log("EncryptMiddleware: Failed to set POST data - " . $e->getMessage());
            return false;
        }
    }
    
    private function aesEncrypt($data)
    {
        $key = get_env('APP_ENCRYPT_KEY');
        $iv = substr($key, 0, 16);
        return openssl_encrypt($data, 'AES-128-CBC', $key, 0, $iv);
    }

    private function aesDecrypt($data)
    {
        $key = get_env('APP_ENCRYPT_KEY');
        $iv = substr($key, 0, 16);
        return openssl_decrypt($data, 'AES-128-CBC', $key, 0, $iv);
    }
}
```

### 方案二：Request 包装类（备用方案）

创建 `DecryptedRequest.php`：

```php
<?php

namespace app\api\middleware\system;

use Webman\Http\Request;

class DecryptedRequest extends Request
{
    private $originalRequest;
    private $decryptedData;
    
    public function __construct(Request $originalRequest, array $decryptedData)
    {
        $this->originalRequest = $originalRequest;
        $this->decryptedData = $decryptedData;
        
        // 复制原始请求的属性
        $this->plugin = $originalRequest->plugin;
        $this->app = $originalRequest->app;
        $this->controller = $originalRequest->controller;
        $this->action = $originalRequest->action;
        $this->route = $originalRequest->route;
    }
    
    public function post($name = null, $default = null)
    {
        if (null === $name) {
            return $this->decryptedData;
        }
        return $this->decryptedData[$name] ?? $default;
    }
    
    public function input(string $name, $default = null)
    {
        if (isset($this->decryptedData[$name])) {
            return $this->decryptedData[$name];
        }
        return $this->originalRequest->input($name, $default);
    }
    
    public function all()
    {
        return $this->decryptedData + $this->originalRequest->get();
    }
    
    public function __call($method, $args)
    {
        return call_user_func_array([$this->originalRequest, $method], $args);
    }
}
```

## 测试方法

1. **生成测试数据**：
   ```
   GET /api/generate-test-data
   ```

2. **测试解密功能**：
   ```
   POST /api/test-encrypt
   Content-Type: text/plain
   Body: [加密后的数据]
   ```

3. **在控制器中获取参数**：
   ```php
   public function yourMethod(Request $request)
   {
       $post = $request->post();           // 获取所有POST参数
       $adminId = $request->post('admin_id'); // 获取特定参数
       $title = $request->input('title');     // 使用input方法
   }
   ```

## 注意事项

1. **环境变量**：确保 `APP_ENCRYPT_KEY` 环境变量已正确设置
2. **错误处理**：中间件包含了错误处理，即使解密失败也不会中断请求流程
3. **兼容性**：解决方案同时支持反射和包装类两种方式，确保在不同环境下都能正常工作
4. **性能**：反射操作有一定的性能开销，建议在生产环境中进行性能测试

## 验证解决方案

修改完成后，您应该能够在控制器中正常使用以下方法获取解密后的参数：

- `$request->post()`
- `$request->post('param_name')`
- `$request->input('param_name')`
- `$request->all()`
