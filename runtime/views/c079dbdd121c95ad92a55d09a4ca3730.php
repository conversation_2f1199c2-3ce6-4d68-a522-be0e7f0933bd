<?php /*a:8:{s:56:"E:\code\php\jsjsq\app/admin/view/sc\index\community.html";i:1751179320;s:54:"E:\code\php\jsjsq\app/admin/view/sc\public\header.html";i:1751179319;s:51:"E:\code\php\jsjsq\app/admin/view/sc\public\top.html";i:1751179319;s:51:"E:\code\php\jsjsq\app/admin/view/sc\public\nav.html";i:1751179319;s:51:"E:\code\php\jsjsq\app/admin/view/sc\public\jdb.html";i:1751179319;s:54:"E:\code\php\jsjsq\app/admin/view/sc\public\footer.html";i:1751179319;s:57:"E:\code\php\jsjsq\app/admin/view/sc\index\right_menu.html";i:1751179319;s:55:"E:\code\php\jsjsq\app/admin/view/sc\public\ucharts.html";i:1751179319;}*/ ?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlentities($title); ?></title>
    <link rel="stylesheet" href="/static/js/layui/css/layui.css">
    <link href="/static/system/layui/css/layphoto.css?v=<?php echo release(); ?>" rel="stylesheet" type="text/css"/>
    <link rel="stylesheet" href="/static/css/style.css?v=<?php echo release(); ?>">
    <link rel="stylesheet" href="/static/sc/css/bootstrap.min.css">
    <link rel="stylesheet" href="/static/sc/css/bootstrap-theme.min.css">
    <link rel="stylesheet" href="/static/sc/css/iconfont.css">
    <link rel="stylesheet" href="/static/sc/css/style.css">
    <script src="/static/js/layui/layui.js"></script>
    <!--[if lt IE 9]>
    <script src="https://cdn.staticfile.org/html5shiv/r29/html5.min.js"></script>
    <script src="https://cdn.staticfile.org/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>
<body style="<?php if($action == 'party'): ?>background-color:#fff;<?php endif; ?>">
<iframe src="<?php echo url('sc/index/panorama',$param); ?>" frameborder="0" id="mapiframe" class="mapiframe"></iframe>
<!-- 头部 begin -->
<?php 
    $target = "_blank";
 ?>
<div class="header flex flex-col-c">
  <div class="header-l">
    <div class="header-l-t">
      <ul class="flex flex-col-r">
      <?php if(!(empty($url) || (($url instanceof \think\Collection || $url instanceof \think\Paginator ) && $url->isEmpty()))): ?>
        <li><?php if(in_array(($action), explode(',',"community,gridGroup,grid,building"))): ?>
            <a class="active" href="javascript:void(0)" data-url="<?php echo url('sc/index/index',$param); ?>">首页</a>
            <?php else: 
                $target = "_self";
             ?>
            <a class="topNavBack" href="javascript:void(0)">首页</a>
            <?php endif; ?>
            </li>
        <?php else: ?>
        <!--<li><a <?php if($action == 'index'): ?>class="active"<?php endif; ?> href="javascript:void(0)" data-url="javascript:void(0);">首页</a></li>-->
        <li><a <?php if($action == 'index'): ?>class="active"<?php endif; ?> href="javascript:void(0)" >首页</a></li>

      <?php endif; ?>
      <li><a <?php if($action == 'person'): ?>class="active"<?php endif; ?> href="javascript:void(0)" data-url="<?php echo url('sc/index/person'); ?>" _target="<?php echo htmlentities($target); ?>">人口信息</a></li>
      <li><a <?php if($action == 'buildingList'): ?>class="active"<?php endif; ?> href="javascript:void(0)" data-url="<?php echo url('sc/index/buildingList'); ?>" _target="<?php echo htmlentities($target); ?>">建筑信息</a></li>
      <li><a <?php if($action == 'house'): ?>class="active"<?php endif; ?> href="javascript:void(0)" data-url="<?php echo url('sc/index/house'); ?>" _target="<?php echo htmlentities($target); ?>">房屋信息</a></li>
      </ul>
    </div>
    <div class="header-l-d flex flex-col">
      <div class="weather flex flex-row-d">
        <div class="weather-l flex flex-column flex-row-c">
          <!--<img src="http://openweathermap.org/img/w/01d.png" alt="">-->
          <!--<p>建三江</p>-->
        </div>
        <div class="weather-r">
          <p id = "weather"></p>
          <p class="time">PM <?php echo date('Y-m-d h:i:s'); ?></p>
        </div>
      </div>
      <div class="header-l-d-r">
        <ul class="flex flex-col-r">
        <li><a href="javascript:void(0)" data-url="<?php echo url('sc/personLabel/index'); ?>" _target="<?php echo htmlentities($target); ?>">特殊人群</a></li>
        <li><a href="javascript:void(0)" data-url="<?php echo url('sc/index/map'); ?>" _target="<?php echo htmlentities($target); ?>">地标性建筑</a></li>
        </ul>
      </div>
    </div>
  </div>
  <div class="header-m flex flex-column flex-row-c">
    <div class="header-m-t flex flex-col-c"><?php echo htmlentities($title); ?></div>
    <div class="header-m-d">
      <input id="keyword" type="text" value="<?php echo input('keyword',''); ?>">
      <button id="search" type="button"></button>
    </div>
  </div>
  <div class="header-r">
    <div class="header-r-t">
      <ul class="flex flex-col-l">

      <li><a <?php if($action == 'place'): ?>class="active"<?php endif; ?> href="javascript:void(0)" data-url="<?php echo url('sc/index/place'); ?>" _target="<?php echo htmlentities($target); ?>">场所信息</a></li>
      <!--<li><a <?php if($action == 'monitor'): ?>class="active"<?php endif; ?> href="javascript:void(0)" data-url="<?php echo url('sc/index/monitor_all'); ?>" _target="<?php echo htmlentities($target); ?>">实时可视</a></li>-->
      <li>
          <a href="javascript:void(0)" data-url="<?php echo url('sc/index/monitor_all'); ?>" _target="<?php echo htmlentities($target); ?>">实时可视</a>
        </li>
      <li class="smenu"><a <?php if($action == 'dynamics'): ?>class="active"<?php endif; ?> href="javascript:void(0)" data-url="<?php echo url('sc/index/dynamics'); ?>" _target="<?php echo htmlentities($target); ?>">要闻动态</a>
        <ul>
          <li><a href="javascript:void(0)" data-url="<?php echo url('sc/index/government'); ?>" _target="<?php echo htmlentities($target); ?>">政务公开</a></li>
        </ul>
      </li>
      <li><a <?php if($action == 'file'): ?>class="active"<?php endif; ?> href="javascript:void(0)" data-url="<?php echo url('sc/index/file'); ?>" _target="<?php echo htmlentities($target); ?>">文件管理</a></li>
      </ul>
    </div>
    <div class="header-r-d flex flex-col flex-row-d">
      <div class="header-r-d-l">
        <ul class="flex flex-col-l">
        <li><a <?php if($action == 'site'): ?>class="active"<?php endif; ?> href="javascript:void(0)" data-url="<?php echo url('sc/index/site'); ?>" _target="<?php echo htmlentities($target); ?>">党建园地</a></li>
        <?php 
          $detail_title = "网格";
          if($param['type']=='index'){
            $detail_title = "街道";
          }else if($param['type']=='community'){
            $detail_title = "社区";
          }else if($param['type']=='gridGroup'){
          $detail_title = "网格组";
          }
         ?>
        <!--<li><a class="details" href="javascript:void(0)" detail-url="<?php echo url('sc/index/details'); ?>"><?php echo htmlentities($detail_title); ?>简介</a></li>-->
        <li><a class="details" href="javascript:void(0)" detail-url="<?php echo url('sc/index/details',$param); ?>"><?php echo htmlentities($detail_title); ?>简介</a></li>
        <li class="smenu"><a <?php if($action == 'workLink'): ?>class="active"<?php endif; ?> href="javascript:void(0)" data-url="<?php echo url('sc/index/workLink'); ?>" _target="<?php echo htmlentities($target); ?>">部门联动</a>
          <ul>
            <li><a <?php if($action == 'meeting'): ?>class="active"<?php endif; ?> href="javascript:void(0)" data-url="<?php echo url('sc/index/meeting'); ?>" _target="<?php echo htmlentities($target); ?>">一键会议</a></li>
          </ul>
        </li>
            <li>
              <!--<a href="https://www.720yun.com/vr/c67j5pek5n3"  _target="<?php echo htmlentities($target); ?>">新全图</a>-->
              <a href="https://www.720yun.com/vr/ec6jtdwOtv7"  _target="<?php echo htmlentities($target); ?>">新全图</a>
            </li>
            <!--<li>-->
            <!--  <a href="https://www.720yun.com/vr/b90jtrtm5k5"  _target="<?php echo htmlentities($target); ?>">大兴</a>-->
            <!--</li>-->
        </ul>
      </div>

      <!--<a class="header-r-d-r flex flex-column flex-row-c">-->
      <!--  <p class="iconfont icon-tongzhi"></p>-->
      <!--  语音播报-->
      <!--</a>-->
    </div>
  </div>
</div>

<div class="jdbgk-box" style="top:240px; left:50%; margin-left: -425px; display: none;">

</div>
<script>
  var params_type = "<?php echo isset($param['type']) ? htmlentities($param['type']) : 'index'; ?>"
  var params_id = <?php echo isset($param['id']) ? htmlentities($param['id']) : 0; ?>
</script>
<!-- 头部 end -->

<div class="jdb-l flex flex-column">
	<a class="on-rkfl" href="javascript:void(0);"style="margin-bottom: 152px;">人口分类</a>
	<a class="on-fwsj" href="javascript:void(0);"style="margin-bottom: 198px;">建筑数据</a>
	<a class="on-jgdw" href="javascript:void(0);">特殊人群</a>
</div>
<div class="jdb-l flex flex-column" style="right:0; left: unset;">
    
	<a class="on-qjqh" href="javascript:void(0);">全景切换</a>
	
	<a class="on-pffb" href="javascript:void(0);">平房分布</a>
	<a class="on-ggss" href="javascript:void(0);" style="margin-top:196px;">公共设施</a>
	<a class="on-tsrq" href="javascript:void(0);" style="margin-top:198px;">机关单位</a>
</div>

<a class="on-jdbgk-m" href="javascript:void(0);" style="background:#012430;position: absolute;bottom: 0;left: 50%;height: 24px;border-radius:0 5px 5px 0;align-items:center;justify-content:center;font-size:18px;color:#00fcff;overflow: hidden;padding:0 10px; margin-left:-45px;">人口分布</a>
<div class="jdbgk-l">
  <div class="bg-2 jdbgk-rkfl">
    <div class="title"><h3>人口分类</h3></div>
    <div class="jdbgk-l-t">
      <canvas id="syrk" class="charts"></canvas>
      <canvas id="czrk" class="charts"></canvas>
      <canvas id="ldrk" class="charts"></canvas>
      <canvas id="zlrk" class="charts"></canvas>
      <span>实有人口</span>
      <span>常住人口</span>
      <span>流动人口</span>
      <span>暂离人口</span>
    </div>
  </div>
  <div class="bg-2 jdbgk-fwsj">
    <div class="title"><h3>建筑数据</h3></div>
    <div class="jdbgk-l-m" id="fw">
        
    </div>
  </div>
  <div class="bg-2 jdbgk-jgdw">
    <div class="title"><h3>特殊人群</h3></div>
    <div class="jdbgk-l-d" id="jgdw">
    </div>
  </div>
</div>

<div class="jdbgk-m-bg bg-2">
  <div class="title"><h3>人口分布</h3></div>
  <div class="jdbgk-m" id="rkfb">
  </div>
</div>

<div class="jdbgk-r">
  <div class="bg-2  jdbgk-pffb">
    <div class="title"><h3>平房分布</h3></div>
    <div class="jdbgk-r-t" id="pffb">
    </div>
  </div>
  <div class="bg-2 jdbgk-ggss">
    <div class="title"><h3>公共设施</h3></div>
    <div class="jdbgk-r-m" id="ggss">
    </div>
  </div>
  <div class="bg-2 jdbgk-tsrq">
    <div class="title"><h3>机关单位</h3></div>
    <div class="jdbgk-r-d" id="tsrq">

    </div>
  </div>
</div>

<script src="/static/sc/js/jquery.min.js"></script>
<script src="/static/sc/js/bootstrap.min.js"></script>
<script src="/static/js/u-charts/u-charts.js"></script>
<script src="/static/system/module/echarts/echarts.js?console"></script>
<script src="/static/system/module/echarts/china.js?console"></script>
<script src="/static/sc/js/script.js"></script>
</body>
</html>
<div class="jdb-r">
	<?php 
	$allow = 1;
	if($data['level']==1&&$admin_info['grid_group_id']){
	$allow = 0;
	}else if($data['level']==2&&$admin_info['grid_id']){
	$allow = 0;
	}
 ?>
<script>
	var syrk = {};
	var czrk = {};
	var ldrk = {};
	var zlrk = {};
	var fw = {};
	var ggss = {};
	var jgdw = {};
	var rkfb = {};
	var pffb = {};
	var tsrq = {};
	var allow = <?php echo htmlentities($allow); ?>;
    $(function(){
        var title = '<?php echo htmlentities($title); ?>';
        var fontSize = "40px";
        $(".header-m-t").text(title+'概况');
        if(title.length<10){
            fontSize = "36px";
        }else if(title.length<12){
            fontSize = "32px";
        }else if(title.length<14){
            fontSize = "26px";
        }else{
            fontSize = "20px";
        }
        $(".header-m-t").css("font-size",fontSize);
		 syrk = {
			name:"实有人口",
			title: "<?php echo isset($total['rkfl']['syrk']) ? htmlentities($total['rkfl']['syrk']) : 0; ?>人",
			per:1
		};
		 czrk = {
			name:"常驻人口",
			title: "<?php echo isset($total['rkfl']['czrk']) ? htmlentities($total['rkfl']['czrk']) : 0; ?>人",
			per:(<?php echo isset($total['rkfl']['czrk']) ? htmlentities($total['rkfl']['czrk']) : 0; ?>/<?php echo isset($total['rkfl']['syrk']) ? htmlentities($total['rkfl']['syrk']) : 1; ?>).toFixed(2)
		};
		 ldrk = {
			name:"流动人口",
			title: "<?php echo isset($total['rkfl']['ldrk']) ? htmlentities($total['rkfl']['ldrk']) : 0; ?>人",
			per:(<?php echo isset($total['rkfl']['ldrk']) ? htmlentities($total['rkfl']['ldrk']) : 0; ?>/<?php echo isset($total['rkfl']['syrk']) ? htmlentities($total['rkfl']['syrk']) : 1; ?>).toFixed(2)
		};
		 zlrk = {
			name:"暂离人口",
			title: "<?php echo isset($total['rkfl']['zlrk']) ? htmlentities($total['rkfl']['zlrk']) : 0; ?>人",
			per:(<?php echo isset($total['rkfl']['zlrk']) ? htmlentities($total['rkfl']['zlrk']) : 0; ?>/<?php echo isset($total['rkfl']['syrk']) ? htmlentities($total['rkfl']['syrk']) : 1; ?>).toFixed(2)
		};
		 fw = {
			categories: ['其他','公共',"平房","楼房","总数"],
			legend:false,
			series: [
				{
					name: "栋数",
					data: [<?php echo htmlentities($total['fwsj'][4]); ?>,<?php echo htmlentities($total['fwsj'][3]); ?>,<?php echo htmlentities($total['fwsj'][2]); ?>,<?php echo htmlentities($total['fwsj'][1]); ?>,<?php echo htmlentities($total['fwsj'][0]); ?>],
					type: 'bar',
					barWidth: '50%',
					barMaxWidth: '20px',
					itemStyle: {
						color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
							{ offset: 0, color: '#f3e02b' },
							{ offset: 1, color: '#f6b51b' }
						])
					},
					label: {
						show: true, // 显示标签
						position: 'right' // 在顶部显示标签
					}
				}
			]
		};
		 ggss = {
			categories: ['警区','加油站',"公厕","停车场","集市","体育场","广场","公园"],
			legend:false,
			series: [
				{
					name: "数量",
					data: [<?php echo htmlentities($total['ggss']['place_jq']); ?>,<?php echo htmlentities($total['ggss']['place_jyz']); ?>,<?php echo htmlentities($total['ggss']['place_gce']); ?>,
						<?php echo htmlentities($total['ggss']['place_tcc']); ?>,<?php echo htmlentities($total['ggss']['place_js']); ?>,<?php echo htmlentities($total['ggss']['place_tyc']); ?>,<?php echo htmlentities($total['ggss']['place_gc']); ?>,<?php echo htmlentities($total['ggss']['place_gy']); ?>],
					type: 'bar',
					barWidth: '50%',
					barMaxWidth: '20px',
					itemStyle: {
						color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
							{ offset: 0, color: '#4264fb' },
							{ offset: 1, color: '#783CFA' }
						])
					},
					label: {
						show: true, // 显示标签
						position: 'right' // 在顶部显示标签
					}
				}
			]
		};
		 jgdw = {
			name: "数量",
			data: [<?php echo htmlentities($total['jgdw']['government_1']); ?>,<?php echo htmlentities($total['jgdw']['government_2']); ?>,<?php echo htmlentities($total['jgdw']['hospital']); ?>,<?php echo htmlentities($total['jgdw']['clinic']); ?>,<?php echo htmlentities($total['jgdw']['school']); ?>,<?php echo htmlentities($total['jgdw']['book']); ?>],
			categories: ["机关单位","事业单位","医院","社区门诊","学校","图书馆"]
		};
		 rkfb = {
			name:'实有人口',
			categories: [<?php echo htmlspecialchars_decode($total['rkfb']['group_name']); ?>],
			data: [<?php echo htmlentities($total['rkfb']['group_value']); ?>]
		};
		 pffb = {
			name: "平房户数",
			categories: [<?php echo htmlspecialchars_decode($total['pffb']['group_name']); ?>],
		data: [<?php echo htmlentities($total['pffb']['group_value']); ?>]
		};
		 tsrq = {
			categories: ["<?php echo htmlentities($total['tsrq'][4]['name']); ?>","<?php echo htmlentities($total['tsrq'][3]['name']); ?>","<?php echo htmlentities($total['tsrq'][2]['name']); ?>","<?php echo htmlentities($total['tsrq'][1]['name']); ?>","<?php echo htmlentities($total['tsrq'][0]['name']); ?>"],
			legend:false,
			series: [
				{
					name: "条数",
					data: ["<?php echo htmlentities($total['tsrq'][4]['value']); ?>","<?php echo htmlentities($total['tsrq'][3]['value']); ?>","<?php echo htmlentities($total['tsrq'][2]['value']); ?>","<?php echo htmlentities($total['tsrq'][1]['value']); ?>","<?php echo htmlentities($total['tsrq'][0]['value']); ?>"],
					type: 'bar',
					barWidth: '50%',
					barMaxWidth: '20px',
					itemStyle: {
						color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
							{ offset: 0, color: '#16e195' },
							{ offset: 1, color: '#35e1e1' }
						])
					},
					label: {
						show: true, // 显示标签
						position: 'right' // 在顶部显示标签
					}
				}
			]
		};
		 if(allow){
			 getServerData();
		 }
    });
</script>
<?php if($data['level'] == '0'): ?>
    <ul class="jdb-r-t flex flex-row-c">
		<li>所有社区<span class="iconfont icon-xiala"></span></li>
		<li>默认排序<span class="iconfont icon-xiala"></span></li>
	</ul>
	<div class="jdb-r-d">
		<ul>
			<?php if(is_array($list) || $list instanceof \think\Collection || $list instanceof \think\Paginator): if( count($list)==0 ) : echo "" ;else: foreach($list as $key=>$vo): ?>
			<li>
				<a href="javascript:void(0);" class="change" data-url="<?php echo url('sc/community',['id'=>$vo['id']]); ?>">
					<dl data-id="<?php echo htmlentities($vo['id']); ?>" data-action="community">
						<dt><i><?php echo htmlentities($key+1); ?></i><?php echo htmlentities($vo['community_name']); ?></dt>
						<dd>负责人：<?php echo htmlentities($vo['person']['name']); ?></dd>
						<dd>联系电话：<?php echo htmlentities($vo['person']['phone']); ?></dd>
					</dl>
					<?php 
						if (strpos($vo['image'], 'http') === 0) {
						$vo['image'] = [['src'=>$vo['image'],'title'=>'图片']];
						}else{
						$vo['image'] = json_decode($vo['image'],true);
						}
					 ?>
					<div class="img">
						<img src="<?php echo getThumb($vo['image'][0]['src']); ?>" alt="">
						<div>点击切换</div>
					</div>
				</a>
			</li>
			<?php endforeach; endif; else: echo "" ;endif; ?>
		</ul>
	</div>
<?php endif; if($data['level'] == '1'): ?>
    <ul class="jdb-r-t flex flex-row-c">
		<?php if(empty($admin_info['community_id']) || (($admin_info['community_id'] instanceof \think\Collection || $admin_info['community_id'] instanceof \think\Paginator ) && $admin_info['community_id']->isEmpty())): ?>
			<li class="navBack" data-action="<?php echo htmlentities($data['action']); ?>" data-id="<?php echo htmlentities($data['id']); ?>">返回上一级<span class="iconfont icon-xiala"></span></li>
			<?php else: ?>
			<li>社区级<span class="iconfont icon-xiala"></span></li>
		<?php endif; ?>
		<li><?php echo htmlentities($title); ?><span class="iconfont icon-xiala"></span></li>
	</ul>
	<div class="jdb-r-d">
		<ul>
			<?php if(is_array($list) || $list instanceof \think\Collection || $list instanceof \think\Paginator): if( count($list)==0 ) : echo "" ;else: foreach($list as $key=>$vo): ?>
			<li>
				<a href="javascript:void(0);">
					<dl data-id="<?php echo htmlentities($vo['id']); ?>" data-action="gridGroup" data-hotspot="<?php echo htmlentities($vo['hotspot']); ?>" data-hotspot_from="<?php echo htmlentities($vo['hotspot_from']); ?>">
						<dt><i><?php echo htmlentities($key+1); ?></i><?php echo htmlentities($vo['grid_name']); ?></dt>
						<dd>负责人：<?php echo htmlentities($vo['person']['name']); ?></dd>
						<dd>联系电话：<?php echo htmlentities($vo['person']['phone']); ?></dd>
					</dl>
					<?php 
						if (strpos($vo['image'], 'http') === 0) {
						$vo['image'] = [['src'=>$vo['image'],'title'=>'图片']];
						}else{
						$vo['image'] = json_decode($vo['image'],true);
						}
					 ?>
					<div class="img" data-url="<?php echo url('sc/gridGroup',['id'=>$vo['id']]); ?>">
						<img src="<?php echo getThumb($vo['image'][0]['src']); ?>" alt="">
						<div>点击切换</div>
					</div>
				</a>
			</li>
			<?php endforeach; endif; else: echo "" ;endif; ?>
		</ul>
	</div>
<?php endif; if($data['level'] == '2'): ?>
    <ul class="jdb-r-t flex flex-row-c">
		<?php if(empty($admin_info['grid_group_id']) || (($admin_info['grid_group_id'] instanceof \think\Collection || $admin_info['grid_group_id'] instanceof \think\Paginator ) && $admin_info['grid_group_id']->isEmpty())): ?>
			<li class="navBack" data-action="<?php echo htmlentities($data['action']); ?>" data-id="<?php echo htmlentities($data['id']); ?>">返回上一级<span class="iconfont icon-xiala"></span></li>
			<?php else: ?>
			<li>网格组级<span class="iconfont icon-xiala"></span></li>
		<?php endif; ?>
		<li><?php echo htmlentities($title); ?><span class="iconfont icon-xiala"></span></li>
	</ul>
	<div class="jdb-r-d">
		<ul>
			<?php if(is_array($list) || $list instanceof \think\Collection || $list instanceof \think\Paginator): if( count($list)==0 ) : echo "" ;else: foreach($list as $key=>$vo): ?>
			<li>
				<a href="javascript:void(0);">
					<dl data-id="<?php echo htmlentities($vo['id']); ?>" data-action="grid" data-hotspot="<?php echo htmlentities($vo['hotspot']); ?>" data-hotspot_from="<?php echo htmlentities($vo['hotspot_from']); ?>">
						<dt><i><?php echo htmlentities($key+1); ?></i><?php echo htmlentities($vo['grid_name']); ?></dt>
						<dd>负责人：<?php echo htmlentities($vo['person']['name']); ?></dd>
						<dd>联系电话：<?php echo htmlentities($vo['person']['phone']); ?></dd>
					</dl>
					<?php 
						if (strpos($vo['image'], 'http') === 0) {
						$vo['image'] = [['src'=>$vo['image'],'title'=>'图片']];
						}else{
						$vo['image'] = json_decode($vo['image'],true);
						}
					 ?>
					<div class="img" data-url="<?php echo url('sc/grid',['id'=>$vo['id']]); ?>">
						<img src="<?php echo getThumb($vo['image'][0]['src']); ?>" alt="">
						<div>点击切换</div>
					</div>
				</a>
			</li>
			<?php endforeach; endif; else: echo "" ;endif; ?>
		</ul>
	</div>
<?php endif; if($data['level'] == '3'): ?>
    <ul class="jdb-r-t flex flex-row-c">
		<?php if(empty($admin_info['grid_id']) || (($admin_info['grid_id'] instanceof \think\Collection || $admin_info['grid_id'] instanceof \think\Paginator ) && $admin_info['grid_id']->isEmpty())): ?>
			<li class="navBack" data-action="<?php echo htmlentities($data['action']); ?>" data-id="<?php echo htmlentities($data['id']); ?>">返回上一级<span class="iconfont icon-xiala"></span></li>
			<?php else: ?>
			<li>网格级<span class="iconfont icon-xiala"></span></li>
		<?php endif; ?>
		<li <?php if(mb_strlen($title)>10){echo "style='font-size:16px;'";}else if(mb_strlen($title)>8){echo "style='font-size:18px;'";} ?>><?php echo htmlentities($title); ?><span class="iconfont icon-xiala"></span></li>
	</ul>
	<div class="jdb-r-d">
		<ul>
			<?php if(is_array($list) || $list instanceof \think\Collection || $list instanceof \think\Paginator): if( count($list)==0 ) : echo "" ;else: foreach($list as $key=>$vo): ?>
			<li>
				<a href="javascript:void(0);">
					<dl data-id="<?php echo htmlentities($vo['id']); ?>" data-action="building">
						<dt><i><?php echo htmlentities($key+1); ?></i><?php echo htmlentities($vo['building_name']); ?></dt>
						<dd>单元数：<?php echo htmlentities($vo['unit']); ?>/楼层数：<?php echo htmlentities($vo['floor']); ?></dd>
						<dd>房屋数：<?php echo htmlentities($vo['house']); ?>/人口数：<?php echo htmlentities($vo['person']); ?></dd>
					</dl>
					<?php 
						if (strpos($vo['image'], 'http') === 0) {
						$vo['image'] = [['src'=>$vo['image'],'title'=>'图片']];
						}else{
						$vo['image'] = json_decode($vo['image'],true);
						}
					 ?>
					<div class="img" onclick="showBuilding(<?php echo htmlentities($vo['id']); ?>)">
						<img src="<?php echo getThumb($vo['image'][0]['src']); ?>" alt="">
						<div>点击切换</div>
					</div>
				</a>
			</li>
			<?php endforeach; endif; else: echo "" ;endif; ?>
		</ul>
	</div>
<?php endif; if($data['level'] == '4'): ?>
    <ul class="jdb-r-t flex flex-row-c">
		<li class="navBack" data-action="<?php echo htmlentities($data['action']); ?>" data-id="<?php echo htmlentities($data['id']); ?>">返回上一级<span class="iconfont icon-xiala"></span></li>
		<li <?php if(mb_strlen($title)>10){echo "style='font-size:16px;'";}else if(mb_strlen($title)>8){echo "style='font-size:18px;'";} ?>><?php echo htmlentities($title); ?><span class="iconfont icon-xiala"></span></li>
	</ul>
	<div class="jdb-r-d">
		<ul>
			<?php 
				if (strpos($building['image'], 'http') === 0) {
				$image = [['src'=>$building['image'],'title'=>'图片']];
				}else{
				$image = json_decode($building['image'],true);
				}
			 if(is_array($list) || $list instanceof \think\Collection || $list instanceof \think\Paginator): if( count($list)==0 ) : echo "" ;else: foreach($list as $key=>$vo): if($vo['unit'] > '0'): ?>
			<li>
				<a href="javascript:void(0);" >
					<dl data-id="<?php echo htmlentities($building['id']); ?>" data-unit="<?php echo htmlentities($vo['unit']); ?>" class="sqbox-on">
						<dt><i><?php echo htmlentities($vo['unit']); ?></i>单元</dt>
						<dd>房屋数量：<?php echo htmlentities($vo['house']); ?></dd>
						<dd>人口数量：<?php echo htmlentities($vo['person']); ?></dd>
					</dl>
					<div class="img">
						<?php 
							$title = $vo['unit']."单元";
						 if(is_array($image) || $image instanceof \think\Collection || $image instanceof \think\Paginator): if( count($image)==0 ) : echo "" ;else: foreach($image as $key=>$v): if($v['title'] == $title): ?>
								<img src="<?php echo getThumb($v['src']); ?>" class="unit-<?php echo htmlentities($vo['unit']); ?>" alt=""  onclick="showUnit('<?php echo htmlentities($v['src']); ?>')">
							<?php endif; ?>
						<?php endforeach; endif; else: echo "" ;endif; ?>
					</div>
				</a>
			</li>
			<?php endif; ?>
			<?php endforeach; endif; else: echo "" ;endif; if($total['merchant'] > '0'): ?>
			<li>
				<a href="javascript:void(0);" data-url="/admin/sc/index/place?id=<?php echo htmlentities($building['id']); ?>&type=building&place=1" class="place">
					<dl>
						<dt><i>商</i>个体工商户</dt>
						<dd>数量：<?php echo htmlentities($total['merchant']); ?></dd>
						<dd></dd>
					</dl>
					<div class="img">
						
					</div>
				</a>
			</li>
			<?php endif; if($total['enterprise'] > '0'): ?>
			<li>
				<a href="javascript:void(0);" data-url="/admin/sc/index/place?id=<?php echo htmlentities($building['id']); ?>&type=building&place=2" class="place">
					<dl>
						<dt><i>企</i>企业</dt>
						<dd>数量：<?php echo htmlentities($total['enterprise']); ?></dd>
						<dd>&nbsp;</dd>
					</dl>
					<div class="img">
						
					</div>
				</a>
			</li>
			<?php endif; if($total['government'] > '0'): ?>
			<li>
				<a href="javascript:void(0);" data-url="/admin/sc/index/place?id=<?php echo htmlentities($building['id']); ?>&type=building&place=3" class="place">
					<dl>
						<dt><i>单</i>机关及事业单位</dt>
						<dd>数量：<?php echo htmlentities($total['government']); ?></dd>
						<dd>&nbsp;</dd>
					</dl>
					<div class="img">
						
					</div>
				</a>
			</li>
			<?php endif; ?>
		</ul>
	</div>
<?php endif; ?>
</div>
<div class="sqbox-bg flex-col-c flex-row-c flex-column">
	<!--<div class="sqbox" id="image-container" style="position: relative;z-index:99">-->
	   <div class="sqbox" id="image-container"> 
	</div>
	<!--<span class="sqbox-x glyphicon glyphicon-remove"></span>-->
	
</div>
<style>
   #draggable-image {
    user-select: none; /* 防止图片在拖动时被选中 */
}
</style>
<script>
	// 历史记录存储中心 
let historyStore = [];
let historyIndex = -1;
function historyStoreAdd(url) {
	historyStore.push(url)
	historyIndex = historyStore.length - 1
}

function getHistoryIndex() {
	return historyIndex;
}
function goBack() {
	if(historyIndex >=0) {
		historyIndex --;
		window.history.back();
	}
}
</script>
<script language="JavaScript">
    let scale = 1
    $(".sqbox")?.[0].addEventListener('wheel', (event) => {
  
   
            // $(".sqbox")[0].on('mouseover',function() {
            //     console.log('鼠标进入div');
            // })
        if($(".sqbox-bg").hasClass('active')){
            if($(".sqbox").length > 0){
                if($(".sqbox-s").length == 0){
                    // event.preventDefault(); // 阻止默认滚动行为
                    const deltaY = event.deltaY; // 获取滚轮方向和速度
                    scale += deltaY * -0.001; // 根据滚轮方向增减缩放比例
                    console.log(scale,'scale')
                    scale = scale<0.3?0.3:scale
                   
                    $(".sqbox")[0].style.transform = `scale(${scale})`; // 应用缩放变换
                }
            }
        }
    }, {
        passive: false 
        
    });
    // script.js 文件内容
    
    // 选择需要观察变动的DOM节点
// const targetNode = document.getElementById('image-container');
 
// 配置观察选项:
// const __config = { attributes: true, childList: true, subtree: true };
 
// 当观察到变动时执行的回调函数
// const callback = function(mutationsList, observer) {
//      $("#image-container").css({'display':'block'});
//     // console.log('callback......');
//     for(const mutation of mutationsList) {
//         if (mutation.type === 'childList') {
//             console.log('A child node has been added or removed.');
//             draggable()
            
//         } else if (mutation.type === 'attributes') {
            
//             console.log(`The ${mutation.attributeName} attribute was modified.`);
//         }
//     }
// };
 
// 创建一个观察器实例并传入回调函数
// const observer = new MutationObserver(callback);
// if(targetNode instanceof Element){
//     console.log(observer)
 
// // 开始观察目标节点
// observer.observe(targetNode,  __config);
// }

 
// 之后，你可以停止观察
// observer.disconnect();
    
    // function draggable () {
    //       var img = document.getElementById('draggable-image');
    // var container = document.getElementById('image-container');
    // var isDragging = false;
    // var offsetX, offsetY, mouseX, mouseY;
    // // img.style.left = '50%';
    // // img.style.top = '50%';
    // console.log(img,'img')
    // if(img){
    //     img.addEventListener('mousedown', function(e) {
    //         console.log(img,'mousedown')
    //         // e.stopPropagation()
    //         isDragging = true;
    //         offsetX = e.clientX - img.offsetLeft;
    //         offsetY = e.clientY - img.offsetTop;
    //         img.style.cursor = 'grabbing'; // 改变光标样式为抓取状态
    //     });
        
    //     document.addEventListener('mousemove', function(e) {
    //         console.log('mousemove')
    //         if (isDragging) {
    //             mouseX = e.clientX;
    //             mouseY = e.clientY;
    //             img.style.left = (mouseX - offsetX) + 'px';
    //             img.style.top = (mouseY - offsetY) + 'px';
    //         }
    //     });
     
    //     document.addEventListener('mouseup', function() {
    //         //   e.stopPropagation()
    //         console.log(img,'mouseup')
    //         if (isDragging) {
    //             isDragging = false;
    //             img.style.cursor = 'grab'; // 恢复光标样式为抓取状态
    //         }
    //     });
    // }
    
 
    
    // $("#image-container").on('click',function(e){
    //     console.log('image-container')
    // })
    // }
    // $(".sqbox-bg").on('click',function () {
    //     $(".sqbox-bg").removeClass("active")
    //     if($("#image-container")){
    //         $("#image-container").css({'display':'none'});
    //     }
         
    // })
    


    var cur_scene_id = 0;
    function sceneChange(scene_id){
        cur_scene_id = scene_id;
        $(".jdb-r").load("/admin/sc/index/changeScene",{scene_id:scene_id});
    }

    function communityChange(community_id){
        window.location.href="/admin/sc/community?id="+community_id;
    }

    function showBuilding(id){
         console.log('id', id)
        $(".sqbox-bg").addClass("active");
        $(".jdb-r").addClass("active");
        $(".sqbox-bg").addClass("building");
        $(".jdb-r").load('/admin/sc/index/building',{id:id,menu:1});
        $(".sqbox").load("/admin/sc/index/panorama",{id:id,type:'building'});
    }

    function showUnit(src){
        $(".sqbox-bg").addClass("active");
        $(".sqbox-bg").addClass("unit");
        $(".sqbox").html("<img src='"+src+"' style='display: block; margin: 0 auto; max-width: 1200px; max-height: 750px;' />");
    }
    
    var uChartsInstance = {};

    function showCharts(id,type,data){
        const canvas = document.getElementById(id);
        const ctx = canvas.getContext("2d");
        canvas.width = canvas.offsetWidth;
        canvas.height = canvas.offsetHeight;
        const opts = getOpts(type,data,canvas,ctx);
        uChartsInstance[id] = new uCharts(opts);
    }

    function showECharts(id,type,data){
        var myCharts = echarts.init(document.getElementById(id), 'dark');
        const opts = getOpts(type,data);
        myCharts.setOption(opts);
        myCharts.on('click', function (params) {
            toDetail(id,params);
        });
    }

    function getServerData() {
        console.log(czrk);
        showCharts('czrk', 1, czrk);
        showCharts('ldrk', 1, ldrk);
        showCharts('zlrk', 1, zlrk);
        showCharts('syrk', 1, syrk);
        showECharts('fw', 5, fw);
        showECharts('ggss', 5, ggss);
        showECharts('tsrq', 4, jgdw);
        showECharts('rkfb', 4, rkfb);
        showECharts('pffb', 4, pffb);
        showECharts('jgdw', 5, tsrq);
        $("#syrk").on("click",function(){
            window.open("<?php echo url('sc/index/person'); ?>?type="+params_type+"&id="+params_id, "_blank");
        })
        $("#czrk").on("click",function(){
            window.open("<?php echo url('sc/index/person'); ?>?type="+params_type+"&id="+params_id+"&person_type=1", "_blank");
        })
        $("#ldrk").on("click",function(){
            window.open("<?php echo url('sc/index/person'); ?>?type="+params_type+"&id="+params_id+"&person_type=2", "_blank");
        })
        $("#zlrk").on("click",function(){
            window.open("<?php echo url('sc/index/person'); ?>?type="+params_type+"&id="+params_id+"&person_type=3", "_blank");
        })
    }

    function toDetail(id,params){
        if(id=='tsrq'){
            var url = "<?php echo url('sc/index/place'); ?>?type="+params_type+"&id="+params_id+"&place=3";
            switch (params.name){
                case '机关单位':
                    url += "&unit_type=1"
                    break
                case '事业单位':
                    url += "&unit_type=2"
                    break
                case '医院':
                    url += "&keyword=医院"
                    break
                case '社区门诊':
                    url += "&keyword=社区门诊"
                    break
                case '图书馆':
                    url += "&keyword=图书馆"
                    break
                case '学校':
                    url += "&keyword=学"
                    break
            }
            window.open(url, "_blank");
        }
        if(id=='jgdw'){
            let group_map = <?php echo json_encode($total['tsrq'],JSON_UNESCAPED_UNICODE); ?>;
            url = "<?php echo url('sc/personLabel/index'); ?>?type="+params_type+"&id="+params_id+"&"+group_map[params.dataIndex]['key']+"=1";
            window.open(url, "_blank");
        }
        if(id=='rkfb'){
            let group_map = [<?php echo $total['rkfb']['group_map']; ?>]
            url = "<?php echo url('sc/index/person'); ?>?type="+params_type+"&id="+params_id+"&"+group_map[params.dataIndex]
            window.open(url, "_blank");
        }
        if(id=='pffb'){
            let group_map = [<?php echo $total['rkfb']['group_map']; ?>]
            url = "<?php echo url('sc/index/buildingList'); ?>?type="+params_type+"&id="+params_id+"&floor_type=2&"+group_map[params.dataIndex]
            window.open(url, "_blank");
        }
        if(id=='fw'){
            let floor_type = 4 - params.dataIndex;
            url = "<?php echo url('sc/index/buildingList'); ?>?type="+params_type+"&id="+params_id+"&floor_type="+ floor_type
            window.open(url, "_blank");
        }
        if(id=='ggss'){
            var condition = "";
            switch (params.dataIndex){
                case 0:
                    condition = "&type_1_id=3&type_2_id=17";
                    break;
                case 1:
                    condition = "&type_1_id=3&type_2_id=16";
                    break;
                case 2:
                    condition = "&type_1_id=3&type_2_id=14";
                    break;
                case 3:
                    condition = "&type_1_id=3&type_2_id=13";
                    break;
                case 4:
                    condition = "&type_1_id=3&type_2_id=12";
                    break;
                case 5:
                    condition = "&type_1_id=1&type_2_id=6";
                    break;
                case 6:
                    condition = "&type_1_id=1&type_2_id=5";
                    break;
                case 7:
                    condition = "&type_1_id=1&type_2_id=4";
                    break;
            }
            window.open("<?php echo url('sc/index/place'); ?>?type="+params_type+"&id="+params_id+'&place=4'+condition, "_blank");
        }
    }


    getServerData();



</script>