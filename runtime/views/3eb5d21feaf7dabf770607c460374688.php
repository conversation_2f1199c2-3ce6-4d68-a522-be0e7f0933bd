<?php /*a:4:{s:56:"E:\code\php\jsjsq\app/admin/view/system\admin\index.html";i:1751179317;s:51:"E:\code\php\jsjsq\app/admin/view/public\header.html";i:1751179315;s:51:"E:\code\php\jsjsq\app/admin/view/public\footer.html";i:1751179315;s:51:"E:\code\php\jsjsq\app/admin/view/public\static.html";i:1751179315;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>SwiftAdmin 后台管理开发框架</title>
    <link href="/favicon.ico" rel="icon">
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link href="/static/system/layui/css/layui.css?v=<?php echo release(); ?>" rel="stylesheet" type="text/css"/>
    <link href="/static/system/css/style.css?v=<?php echo release(); ?>" rel="stylesheet" type="text/css"/>
    <link href="/static/system/layui/css/layphoto.css?v=<?php echo release(); ?>" rel="stylesheet" type="text/css"/>
    <!-- // 加载font-awesome图标 -->
    <link href="/static/system/layui/css/font-awesome.css?v=<?php echo release(); ?>" rel="stylesheet" type="text/css"/>
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>
<script>
    let app_Config = {
        app: "<?php echo htmlentities((isset($app) && ($app !== '')?$app:'/admin')); ?>",
        controller: "<?php echo htmlentities((isset($controller) && ($controller !== '')?$controller:'index')); ?>",
        action: "<?php echo htmlentities((isset($action) && ($action !== '')?$action:'index')); ?>",
        version: "<?php echo config('app.version'); ?>",
        api: "<?php echo config('app.api_url'); ?>",
    };

    let upload_chunkSize = <?php echo saenv('upload_chunk_size'); ?>;
</script>

<body>
<!-- // 重定位Style -->
<style>

    .layui-card-header.layadmin-card-header-auto {
        border-bottom: 0;
        padding-bottom: 0;
    }

    .layui-card-body {
        padding: 0 30px;
    }

    .layui-card-header h5 {
        font-size: 16px;
    }

    .layui-card-header i.layui-icon-list {
        font-size: 22px;
        margin-top: 5px;
        position: relative;
        top: 2px;
    }

</style>
<?php 
    $Communities = getCommunity();
    if($Communities[0]['id']==1){
    unset($Communities[0]);
    $Communities = array_values($Communities);
    }
    $community_id = empty($data['community_id'])?$Communities[0]['id']:$data['community_id'];
    $GridGroups = getGridGroup($community_id);
    $grid_group_id = empty($data['grid_group_id'])?$GridGroups[0]['id']:$data['grid_group_id'];
    $Grids = getGrid($grid_group_id);
 ?>
<div class="layui-col-md3">
    <div class="layui-fluid" >
        <div class="layui-card" style="padding-bottom: 20px">
            <div class="layui-card-header layadmin-card-header-auto">
                <h5> <i class="layui-icon layui-icon-user"></i> <?php echo __('社区结构'); ?></h5>
            </div>
            <div class="layui-card-body">
                <div id="tree"></div>
            </div>
        </div>
    </div>
</div>

<div class="layui-col-md9" >
    <!-- // 展示数据 -->
    <div class="layui-fluid">
        <div class="layui-card">
            <!-- // 默认操作按钮 -->
            <div class="layui-card-header layadmin-card-header-auto ">
            <div class="layui-form">
                <div class="layui-form-item">

                <div class="layui-inline">
                    <div class="layui-input-inline">
                        <select name="group_id">
                            <option value=""><?php echo __('按用户组查询'); ?></option>
                            <?php if(is_array($group) || $group instanceof \think\Collection || $group instanceof \think\Paginator): $i = 0; $__LIST__ = $group;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?><option value="<?php echo htmlentities($vo['id']); ?>" ><?php echo htmlentities($vo['title']); ?></option><?php endforeach; endif; else: echo "" ;endif; ?>
                        </select>
                    </div>
                </div> 

                <div class="layui-inline">
                    <div class="layui-input-inline">
                        <select name="status">
                            <option value=""><?php echo __('按状态查询'); ?></option>
                            <option value="2" ><?php echo __('正常'); ?></option>                                 
                            <option value="1" ><?php echo __('关闭'); ?></option>                             
                        </select>
                    </div>  
                </div>

                <div class="layui-inline"><div class="layui-input-inline ">
                    <input name="name" class="layui-input" type="text" placeholder="<?php echo __('关键字搜索'); ?>"/></div>
                </div>

                <div class="layui-inline" >
                    <!-- // 默认搜索 -->
                    <button class="layui-btn icon-btn" lay-filter="formSearch" lay-submit><i class="layui-icon layui-icon-search"></i><?php echo __('搜索'); ?></button>
                    <!-- // 打开添加页面 -->
                    <button class="layui-btn icon-btn" lay-open="" data-title="<?php echo __('添加'); ?><?php echo __('管理员'); ?>" callback="edits" data-area="690px,500px" data-url="#adminforms" >
                        <i class="layui-icon layui-icon-add-1"></i><?php echo __('添加'); ?>
                    </button>
                </div>
                </div>
            </div>   
            </div>

            <!-- // 创建数据实例 -->
            <table id="lay-tableList" lay-filter="lay-tableList"></table>        
        </div>
    </div>
</div>

<!-- // 添加编辑数据 -->
<script type="text/html" id="adminforms" >
<div class="layui-fluid layui-bg-white">
    <form class="layui-form layui-form-fixed" lay-filter="adminforms" name="adminforms">
    <input type="text" name="id" hidden="">
    <div class="layui-form-item">
        <label class="layui-form-label"><font color="red">* </font><?php echo __('用户头像'); ?></label>
        <div class="layui-input-inline layui-uplpad-image">
            <div class="layui-upload-drag" lay-upload="avatar" data-type="multiple"
                 >
                <i class="layui-icon layui-icon-upload"></i>
                <p>点击上传，或将文件拖拽到此处</p>
                <div class="layui-hide"></div>
            </div>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label"><font color="red">* </font><?php echo __('用户名'); ?></label>
        <div class="layui-input-inline">
            <input name="name" placeholder="<?php echo __('请输入用户名'); ?>" type="text" class="layui-input"  lay-verify="required" />
        </div>
        <label class="layui-form-label"><?php echo __('真实姓名'); ?></label>
        <div class="layui-input-inline">
            <input name="nickname" placeholder="<?php echo __('请输入真实姓名'); ?>" type="text" class="layui-input"  lay-verify="required" />
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label"><?php echo __('密码'); ?></label>
        <div class="layui-input-inline">
            <input name="pwd" placeholder="<?php echo __('无需修改可留空'); ?>" type="password" class="layui-input"  />
        </div>
        <label class="layui-form-label"><?php echo __('手机号'); ?></label>
        <div class="layui-input-inline">
            <input name="mobile" placeholder="<?php echo __('请输入手机号'); ?>" type="text" class="layui-input"  lay-verify="required|phone" />
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label"><?php echo __('性别'); ?></label>
        <div class="layui-input-inline">
            <select name="sex"  lay-verify="required" >
                <option value=""><?php echo __('请选择性别'); ?></option>
                <option value="1" ><?php echo __('男'); ?></option>
                <option value="0" ><?php echo __('女'); ?></option>
            </select>
        </div>
        <label class="layui-form-label"><?php echo __('状态'); ?></label>
        <div class="layui-input-inline">
            <input name="status" type="radio" value="1" title="<?php echo __('正常'); ?>" checked/>
            <input name="status" type="radio" value="0" title="<?php echo __('关闭'); ?>"/>
        </div>
    </div>   

    <div class="layui-form-item">
        <label class="layui-form-label"><?php echo __('角色权限'); ?></label>
        <div class="layui-input-inline">
            <div class="group" name="group_id" ></div>
        </div>
        <label class="layui-form-label"><?php echo __('所在社区'); ?></label>
        <div class="layui-input-inline">
            <select id="community_id" name="community_id" lay-search lay-filter="communityChange">
            <?php if((count($Communities) > 1)): ?>
            <option value="">请选择</option>
            <?php endif; if(is_array($Communities) || $Communities instanceof \think\Collection || $Communities instanceof \think\Paginator): if( count($Communities)==0 ) : echo "" ;else: foreach($Communities as $key=>$item): ?>
                <option value="<?php echo htmlentities($item['id']); ?>"><?php echo htmlentities($item['community_name']); ?></option>
            <?php endforeach; endif; else: echo "" ;endif; ?>
        </select>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label"><?php echo __('所在网格组'); ?></label>
        <div class="layui-input-inline">
            <select id="grid_group" name="grid_group_id" lay-search lay-filter="gridGroupChange">
            <!-- <?php if((count($GridGroups) > 1)): ?>
            <option value="">请选择</option>
            <?php endif; if(is_array($GridGroups) || $GridGroups instanceof \think\Collection || $GridGroups instanceof \think\Paginator): if( count($GridGroups)==0 ) : echo "" ;else: foreach($GridGroups as $key=>$item): ?>
                <option value="<?php echo htmlentities($item['id']); ?>"><?php echo htmlentities($item['grid_name']); ?></option>
            <?php endforeach; endif; else: echo "" ;endif; ?> -->
        </select>
        </div>
        <label class="layui-form-label"><?php echo __('所在网格'); ?></label>
        <div class="layui-input-inline">
            <select id="grid" name="grid_id" lay-search >
            <!-- <?php if((count($Grids) > 1)): ?>
            <option value="">请选择</option>
            <?php endif; if(is_array($Grids) || $Grids instanceof \think\Collection || $Grids instanceof \think\Paginator): if( count($Grids)==0 ) : echo "" ;else: foreach($Grids as $key=>$item): ?>
                <option value="<?php echo htmlentities($item['id']); ?>"><?php echo htmlentities($item['grid_name']); ?></option>
            <?php endforeach; endif; else: echo "" ;endif; ?> -->
        </select>
        </div>
    </div>

    <div class="layui-form-item">
        <label class="layui-form-label"><?php echo __('用户备注'); ?></label>
        <div class="layui-input-block">
            <textarea name="content" id="content" style="min-height: 110px;" 
            placeholder="<?php echo __('请输入用户备注'); ?>" class="layui-textarea"></textarea>
        </div>
    </div>

    <div class="layui-footer layui-form-item layui-center "  >
        <button class="layui-btn layui-btn-primary" type="button" sa-event="closePageDialog" ><?php echo __('取消'); ?></button>
        <button class="layui-btn" lay-add="<?php echo url('/system/admin/add'); ?>" lay-edit="<?php echo url('/system/admin/edit'); ?>" lay-filter="submitPage"
        lay-submit><?php echo __('提交'); ?></button>
    </div>

    </form>
</div>
</script>

<!-- // 编辑权限列表 -->
<script type="text/html" id="authForms">
<div class="layui-fluid layui-bg-white" >
    <form class="layui-form" action="<?php echo url('/system/admin/index'); ?>">
    <div class="layui-authtree"><div id="authTree"></div></div>
    <div class="layui-footer layui-form-item layui-center "  >
        <button class="layui-btn layui-btn-primary" type="button" sa-event="closePageDialog" ><?php echo __('取消'); ?></button>
        <button class="layui-btn" lay-filter="submitPage" lay-submit><?php echo __('提交'); ?></button>
    </div>
    </form>
</div>
</script>

<!-- // 列表编辑框 -->
<script type="text/html" id="tableBar">
    <?php if((has_admin_auth(url("/system/admin/edit")))): ?>
        <a class="layui-table-text" data-title="<?php echo __('编辑'); ?> {{d.name}}" callback="edits" data-url="#adminforms" data-area="690px,500px" lay-event="edit" ><?php echo __('编辑'); ?></a>
        <div class="layui-divider layui-divider-vertical"></div>
    <a class="layui-table-text"  data-url="<?php echo url('/system/admin/reUpwd'); ?>?id={{d.id}}" lay-event="dialog" ><?php echo __('ukey密码重置'); ?></a>
        <div class="layui-divider layui-divider-vertical"></div>
    <?php endif; ?>
    <a class="layui-table-text" data-url="<?php echo url('/system/admin/del'); ?>?id={{d.id}}" lay-event="del" ><?php echo __('删除'); ?></a>
</script>

<!-- // 列表状态栏 -->
<script type="text/html" id="userStatus">
    <input type="checkbox" lay-filter="switchStatus" data-url="<?php echo url('/system/Admin/status'); ?>" value="{{d.id}}" lay-skin="switch"
     {{d.status==1?'checked':''}}  />
</script>

<!-- // 列表分组 -->
<script type="text/html" id="userGroup">
    {{# layui.each(d.group, function(index, item){ }}
        <span class="layui-badge {{ item.color }}" >{{ item.title }}</span>
    {{# }); }}
</script>

<!--// 加载CommonJS-->
<script src="/static/system/layui/layui.js?v=<?php echo release(); ?>"></script>
<script src="/static/system/js/common.js?v=<?php echo release(); ?>"></script>
<script src="/static/system/js/cascadata.js?v=<?php echo release(); ?>"></script>
<!--// 加载Markdown编辑器-->
<link href="/static/js/markdown/cherry-markdown.css?v=<?php echo release(); ?>" rel="stylesheet" type="text/css"/>
<script src="/static/js/markdown/cherry-markdown.core.js?v=<?php echo release(); ?>"></script>

<!-- // 全局加载第三方JS -->
<script src="/static/js/tinymce/tinymce.min.js?v=<?php echo release(); ?>"></script>
<script src="/static/system/module/xmSelect/xmSelect.js?v=<?php echo release(); ?>"></script>
<script>
    layui.use(['admin','layer','table','form','tree','jquery'], function () {

        var admin = layui.admin;
        var layer = layui.layer;
        var table = layui.table;        // 表格
        var tree = layui.tree;          // 权限树
        var form = layui.form;
        var $ = layui.jquery;

        // 定义表格URL
        var tableURL = "<?php echo url('/system/admin/index'); ?>";
        var department = <?php echo $department; ?>;

        // 基础表格
        var isTable = table.render({
          elem: "#lay-tableList"
          ,url: tableURL
          ,page: true
          ,cols: [[
            {type: 'checkbox'},
            {type: 'numbers', align: 'center',sort: true, width: 100,  title: '序号'},
            {field: 'name', align: 'center', width: 120, title: '<?php echo __("用户名"); ?>'},
            {field: 'nickname', align: 'center', width: 120, title: '<?php echo __("真实姓名"); ?>'},
            {field: 'mobile', align: 'center', width: 120, title: '<?php echo __("手机号"); ?>'},
            {field: 'community_name', align: 'center', width: 120, title: '<?php echo __("所在社区"); ?>'},
            {field: 'grid_group_name', align: 'center', width: 120, title: '<?php echo __("所在网格组"); ?>'},
            {field: 'grid_name', align: 'center', width: 120, title: '<?php echo __("所在网格"); ?>'},
            {field: 'status', align: 'center',templet: '#userStatus', width: 100, title: '<?php echo __("状态"); ?>'},
            {field: 'group', align: 'center', templet:'#userGroup', width: 220, title: '<?php echo __("用户组"); ?>'},
            {field: 'login_ip', align: 'center',width: 180, title: '<?php echo __("登录 IP"); ?>'},
            {field: 'create_time', align: 'center', width: 180,title: '<?php echo __("创建时间"); ?>'},
            {align: 'center', toolbar: '#tableBar',width: 280, fixed: 'right', title: '<?php echo __("操作"); ?>'},
          ]]
        })

        tree.render({
          elem: '#tree',
          data: department,
          spread: false,
          click: function (event) {
            var url = tableURL + '?community_id=' + event.data.community_id + '&grid_id=' + event.data.grid_id;
            table.reload('lay-tableList', {
              url: url
            });
          }
        })

        // 添加 / 编辑用户
        admin.callback.edits = function(clickthis,colletction,config) {
            // 获取表格对象
            var tableThis = colletction.tableThis,
                status = typeof(tableThis) === "undefined" ? true : false;

            // 渲染用户组
            xmSelect.render({
                el: '.group',
                name: 'group_id',
                size: 'small',
                prop: {
                    value: 'id',
                    name:'title'
                }
                ,theme: {
                    color: '#0081ff',
                }
                ,data: <?php echo json_encode($group); ?>
                ,initValue: !status ? tableThis.data.group_id.split(',') : []
            })
            
            // var form_community_id = tableThis.data.community_id;
            // var form_grid_group_id = tableThis.data.grid_group_id;
            // var form_grid_id = tableThis.data.grid_id;

            // setTimeout(()=>{
            //   if( form_community_id ) {
            //     $('#community_id').siblings("div.layui-form-select").find('dl dd[lay-value=' + form_community_id + ']').click();
            //   }
            // })
            // 编辑时回显已选择的内容
            if(!status){
                var form_community_id = tableThis.data.community_id;
                var form_grid_group_id = tableThis.data.grid_group_id;
                var form_grid_id = tableThis.data.grid_id;
    
                setTimeout(()=>{
                  if( form_community_id ) {
                    $('#community_id').siblings("div.layui-form-select").find('dl dd[lay-value=' + form_community_id + ']').click();
                  }
                })
            }

            form.on('select(communityChange)', function(data) {
              $('#grid_group').empty();
              $('#grid').empty();
              var community_id = data.value; // roomName 选中的省份名称
              $.ajax({
                url: '<?php echo url("/Ajax/getGridGroupList"); ?>',
                dataType: 'json',
                type: 'post',
                data:{community_id:community_id},
                success: function (resData) {
                  $('#grid_group').append(new Option("请选择", ""));// 下拉菜单里添加元素
                  $.each(resData, function (index, value) {
                    $('#grid_group').append(new Option(value.grid_name, value.id));// 下拉菜单里添加元素
                  });
                  form.render('select');
                  $('#grid_group').val(form_grid_group_id );
                  form.render('select');
                  setTimeout(()=>{
                    if( form_grid_group_id  ) {
                       $('#grid_group').siblings("div.layui-form-select").find('dl dd[lay-value=' + form_grid_group_id  + ']').click();
                    }
                  })
                }
              });
            });

            form.on('select(gridGroupChange)', function(data) {
              $('#grid').empty();
              var grid_id = data.value; // roomName 选中的省份名称
              $.ajax({
                url: '<?php echo url("/Ajax/getGridList"); ?>',
                dataType: 'json',
                type: 'post',
                data:{grid_id:grid_id},
                success: function (resData) {
                  $('#grid').append(new Option("请选择", ""));// 下拉菜单里添加元素
                  $.each(resData, function (index, value) {
                    $('#grid').append(new Option(value.grid_name+value.id, value.id));// 下拉菜单里添加元素
                  });
                  form.render('select');
                  console.log('form_grid_id', form_grid_id)
                  $('#grid').val(form_grid_id);
                  form.render('select');
                }
              });
            });

            // 监听权限提交
            form.on("submit(submitPage)",function(post){
              // 获取用户id
              var pageThat = layui.$(this),
              _pageUrl = !status ? pageThat.attr('lay-edit') : pageThat.attr('lay-add');

              // 开始POST提交数据
              layui.$.post(_pageUrl,
                post.field, function(res){
                  if (res.code === 200) {
                    layer.msg(res.msg);
                    // 关闭当前窗口
                    table.reload('lay-tableList');
                    layer.close(colletction.index);
                    pageThat.attr("disabled",true);
                  }
                  else {
                    layui.show.error(res.msg);
                  }
                }, 'json');
              return false;
            })
        }

        // 编辑用户权限/栏目权限回调函数
        admin.callback.rulecates = function(clickthis,colletction,config) {
            var tableThis = colletction.tableThis,
                event = tableThis.event;

            layui.$.ajax({
                url:'<?php echo url("/system/Admin/getRuleCateTree"); ?>',
                type:'post',
                dataType:'json',
                data:{
                    type : event,
                },
                success(data) {
                    tree.render({
                        id: 'authTree',
                        elem: '#authTree',
                        checkids: tableThis.data[event].length ? tableThis.data[event].map(Number): [],
                        data: data,
                        showCheckbox: true,
                    })

                    // 监听权限提交
                    form.on("submit(submitPage)", function (post) {

                        // 获取用户id
                        post.field.admin_id = tableThis.data.id;
                        // 增加节点数据
                        post.field[event] = tree.getChecked('authTree', true);
                        // 开始POST提交数据
                        layui.$.post("<?php echo url('/system/Admin/edit" + event + "'); ?>",
                            post.field, function (res) {
                                if (res.code === 200) {
                                    // 更新本地规则
                                    tableThis.update({
                                        [event]: post.field[event]
                                    });

                                    // 关闭当前窗口
                                    layer.msg(res.msg);
                                    layer.close(colletction.index);
                                    clickthis.attr("disabled", true);
                                } else {
                                    layui.show.error(res.msg);
                                }
                            }, 'json');

                        return false;
                    })
                }
            })
        }
    });
</script>
