<?php /*a:3:{s:57:"E:\code\jsjsq\app/admin/view/system\admin_notice\add.html";i:1746518922;s:47:"E:\code\jsjsq\app/admin/view/public\header.html";i:1746518922;s:47:"E:\code\jsjsq\app/admin/view/public\footer.html";i:1746518922;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>SwiftAdmin 后台管理开发框架</title>
    <link href="/favicon.ico" rel="icon">
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link href="/static/system/layui/css/layui.css?v=<?php echo release(); ?>" rel="stylesheet" type="text/css"/>
    <link href="/static/system/css/style.css?v=<?php echo release(); ?>" rel="stylesheet" type="text/css"/>
    <link href="/static/system/layui/css/layphoto.css?v=<?php echo release(); ?>" rel="stylesheet" type="text/css"/>
    <!-- // 加载font-awesome图标 -->
    <link href="/static/system/layui/css/font-awesome.css?v=<?php echo release(); ?>" rel="stylesheet" type="text/css"/>
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>
<script>
    let app_Config = {
        app: "<?php echo htmlentities((isset($app) && ($app !== '')?$app:'/admin')); ?>",
        controller: "<?php echo htmlentities((isset($controller) && ($controller !== '')?$controller:'index')); ?>",
        action: "<?php echo htmlentities((isset($action) && ($action !== '')?$action:'index')); ?>",
        version: "<?php echo config('app.version'); ?>",
        api: "<?php echo config('app.api_url'); ?>",
    };

    let upload_chunkSize = <?php echo saenv('upload_chunk_size'); ?>;
</script>

<body>
<div class="layui-fluid layui-bg-white">
  <form action="<?php echo url('/system/AdminNotice/add'); ?>" class="layui-form">
    <div class="layui-form-item">
      <label class="layui-form-label"><span style="color: red; ">* </span><?php echo __('收件人'); ?></label>
      <div class="layui-input-block">
        <select name="admin_id" lay-verify="required" lay-search>
          <option value=""></option>
          <?php if(is_array($adminList) || $adminList instanceof \think\Collection || $adminList instanceof \think\Paginator): $i = 0; $__LIST__ = $adminList;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
          <option value="<?php echo htmlentities($vo['id']); ?>"><?php echo htmlentities($vo['name']); ?></option>
          <?php endforeach; endif; else: echo "" ;endif; ?>
        </select>
      </div>
    </div>
    <div class="layui-form-item">
      <label class="layui-form-label"><span style="color: red; ">* </span><?php echo __('标题'); ?></label>
      <div class="layui-input-block">
        <input type="text" name="title" class="layui-input" lay-verify="required" value="" placeholder="请输入私信标题">
      </div>
    </div>

    <div class="layui-form-item">
      <label class="layui-form-label"><span style="color: red; ">* </span><?php echo __('回复内容'); ?></label>
      <div class="layui-input-block">
        <textarea name="content" class="layui-textarea" placeholder="请输入私信内容"></textarea>
      </div>
    </div>

    <div class="layui-footer layui-form-item layui-center ">
      <button class="layui-btn layui-btn-primary" type="button" sa-event="closePageDialog"><?php echo __('取消'); ?>
      </button>
      <button class="layui-btn" lay-filter="submitIframe" lay-submit><?php echo __('提交'); ?></button>
    </div>
  </form>
</div>
<!--// 加载CommonJS-->
<script src="/static/system/layui/layui.js?v=<?php echo release(); ?>"></script>
<script src="/static/system/js/common.js?v=<?php echo release(); ?>"></script>
<script src="/static/system/js/cascadata.js?v=<?php echo release(); ?>"></script>