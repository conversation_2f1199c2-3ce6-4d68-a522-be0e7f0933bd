<?php /*a:9:{s:46:"E:\code\jsjsq\app/admin/view/person\index.html";i:1746518922;s:47:"E:\code\jsjsq\app/admin/view/public\header.html";i:1746518922;s:46:"E:\code\jsjsq\app/admin/view/person\param.html";i:1746518922;s:47:"E:\code\jsjsq\app/admin/view/public\search.html";i:1746518922;s:52:"E:\code\jsjsq\app/admin/view/public\search_road.html";i:1746518922;s:47:"E:\code\jsjsq\app/admin/view/public\import.html";i:1746518922;s:47:"E:\code\jsjsq\app/admin/view/public\static.html";i:1747036715;s:47:"E:\code\jsjsq\app/admin/view/public\footer.html";i:1746518922;s:47:"E:\code\jsjsq\app/admin/view/public\select.html";i:1747648922;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>SwiftAdmin 后台管理开发框架</title>
    <link href="/favicon.ico" rel="icon">
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link href="/static/system/layui/css/layui.css?v=<?php echo release(); ?>" rel="stylesheet" type="text/css"/>
    <link href="/static/system/css/style.css?v=<?php echo release(); ?>" rel="stylesheet" type="text/css"/>
    <link href="/static/system/layui/css/layphoto.css?v=<?php echo release(); ?>" rel="stylesheet" type="text/css"/>
    <!-- // 加载font-awesome图标 -->
    <link href="/static/system/layui/css/font-awesome.css?v=<?php echo release(); ?>" rel="stylesheet" type="text/css"/>
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>
<script>
    let app_Config = {
        app: "<?php echo htmlentities((isset($app) && ($app !== '')?$app:'/admin')); ?>",
        controller: "<?php echo htmlentities((isset($controller) && ($controller !== '')?$controller:'index')); ?>",
        action: "<?php echo htmlentities((isset($action) && ($action !== '')?$action:'index')); ?>",
        version: "<?php echo config('app.version'); ?>",
        api: "<?php echo config('app.api_url'); ?>",
    };

    let upload_chunkSize = <?php echo saenv('upload_chunk_size'); ?>;
</script>

<body>
<?php 
  $ids = [67,404,408,59,57,54,56,58,64,65,61,63,66,55,68,69,70,71,72,73,74,75];
  $dictionaryGroup = getDictionaryGroup($ids);
  $endowment_insurance = $dictionaryGroup[67];
  $rpr = $dictionaryGroup[404];
  $rpr_nature = $dictionaryGroup[408];
  $person_type = $dictionaryGroup[59];
  $enlistment = $dictionaryGroup[57];
  $nation = $dictionaryGroup[54];
  $education = $dictionaryGroup[56];
  $face = $dictionaryGroup[58];
  $sex = [2=>'男',1=>'女'];
  $marry = $dictionaryGroup[64];
  $only_child = $dictionaryGroup[65];
  $other_child = $dictionaryGroup[66];
  $health = $dictionaryGroup[61];
  $labor_capacity = $dictionaryGroup[63];
  $religion = $dictionaryGroup[55];
  $insurance = $dictionaryGroup[68];
  $representative = $dictionaryGroup[69];
  $elderly = $dictionaryGroup[70];
  $dilemma = $dictionaryGroup[71];
  $dilemma_children = $dictionaryGroup[72];
  $patient = $dictionaryGroup[73];
  $bad = $dictionaryGroup[74];
  $other = $dictionaryGroup[75];
  if(empty($params)){
  $params = [];
  }
 ?>
<div class="layui-fluid">
    <div class="layui-card">
        <!-- // 默认操作按钮 -->
        <div class="layui-card-header layadmin-card-header-auto ">
            <div class="layui-form">
                <div class="layui-form-item">
                    <?php 
    $Communities = getCommunity();
    if($Communities[0]['id']==1){
    unset($Communities[0]);
    $Communities = array_values($Communities);
    }
    if($params){
        $data = getDataByParam($params);
    }
    $community_id = empty($data['community_id'])?$Communities[0]['id']:$data['community_id'];
    $GridGroups = getGridGroup($community_id);
    $grid_group_id = empty($data['grid_group_id'])?$GridGroups[0]['id']:$data['grid_group_id'];
    $Grids = getGrid($grid_group_id);
 ?>
<style>
    /*.short-label{width: auto;}*/
</style>
<div class="layui-inline">
    <div class="layui-form-label short-label"><?php echo __('社区'); ?></div>
    <div class="layui-input-inline ">
        <select name="community_id" lay-search lay-filter="communityChange">
            <?php if((count($Communities) > 1)): ?>
            <option value="">请选择</option>
            <?php endif; if(is_array($Communities) || $Communities instanceof \think\Collection || $Communities instanceof \think\Paginator): if( count($Communities)==0 ) : echo "" ;else: foreach($Communities as $key=>$item): ?>
                <option value="<?php echo htmlentities($item['id']); ?>"
                <?php if((isset($data['community_id']) && $data['community_id'])): if(in_array(($item['id']), is_array($data['community_id'])?$data['community_id']:explode(',',$data['community_id']))): ?>selected<?php endif; ?>
                <?php endif; ?>
                ><?php echo htmlentities($item['community_name']); ?></option>
            <?php endforeach; endif; else: echo "" ;endif; ?>
        </select>
    </div>
</div>

<div class="layui-inline grid-group">
    <div class="layui-form-label short-label"><?php echo __('网格组'); ?></div>
    <div class="layui-input-inline ">
        <select id="grid_group" name="grid_group_id" lay-search lay-filter="gridGroupChange">
            <?php if((count($GridGroups) > 1)): ?>
            <option value="">请选择</option>
            <?php endif; if(is_array($GridGroups) || $GridGroups instanceof \think\Collection || $GridGroups instanceof \think\Paginator): if( count($GridGroups)==0 ) : echo "" ;else: foreach($GridGroups as $key=>$item): ?>
                <option value="<?php echo htmlentities($item['id']); ?>"
                <?php if((isset($data['grid_group_id']) && $data['grid_group_id'])): if(in_array(($item['id']), is_array($data['grid_group_id'])?$data['grid_group_id']:explode(',',$data['grid_group_id']))): ?>selected<?php endif; ?>
                <?php endif; ?>
                ><?php echo htmlentities($item['grid_name']); ?></option>
            <?php endforeach; endif; else: echo "" ;endif; ?>
        </select>
    </div>
</div>

<div class="layui-inline grid">
    <div class="layui-form-label short-label"><?php echo __('网格'); ?></div>
    <div class="layui-input-inline ">
        <select id="grid" name="grid_id" lay-search lay-filter="gridChange">
            <?php if((count($Grids) > 1)): ?>
            <option value="">请选择</option>
            <?php endif; if(is_array($Grids) || $Grids instanceof \think\Collection || $Grids instanceof \think\Paginator): if( count($Grids)==0 ) : echo "" ;else: foreach($Grids as $key=>$item): ?>
                <option value="<?php echo htmlentities($item['id']); ?>"
                <?php if((isset($data['grid_id']) && $data['grid_id'])): if(in_array(($item['id']), is_array($data['grid_id'])?$data['grid_id']:explode(',',$data['grid_id']))): ?>selected<?php endif; ?>
                <?php endif; ?>
                ><?php echo htmlentities($item['grid_name']); ?></option>
            <?php endforeach; endif; else: echo "" ;endif; ?>
        </select>
    </div>
</div>
                    <div class="layui-inline">
                        <div class="layui-input-inline ">
                            <input name="keyword" class="layui-input" type="text" placeholder="<?php echo __('按姓名或身份证号或家庭地址查询'); ?>"/>
                        </div>
                    </div>
                    <div class="layui-inline" >
                        <!-- // 默认搜索 -->
                        <button id="search" class="layui-btn icon-btn" lay-filter="formSearch" lay-submit><i
                                class="layui-icon layui-icon-search"></i><?php echo __('搜索'); ?>
                        </button>
                        <!--formBegin-->
                        <button class="layui-btn more">展开</button>
                        <!--formEnd-->
                    </div>
                </div>
                <!-- // 自定义搜索参数 -->
                <div id="laytable-search" class="layui-form-item">
                    <?php 
  $road_LIST = getStreetTop(1);
  $street_LIST = getStreetTop(2);
 ?>
<div class="layui-form-item">
  <div class="layui-inline">
    <div class="layui-form-label short-label"><?php echo __('路名'); ?></div>
    <div class="layui-input-inline ">
      <select name="road_id" lay-search lay-filter="roadChange">
        <option value="">请选择</option>
        <?php if(is_array($road_LIST) || $road_LIST instanceof \think\Collection || $road_LIST instanceof \think\Paginator): if( count($road_LIST)==0 ) : echo "" ;else: foreach($road_LIST as $key=>$item): ?>
          <option value="<?php echo htmlentities($item['id']); ?>"><?php echo htmlentities($item['name']); ?></option>
        <?php endforeach; endif; else: echo "" ;endif; ?>
      </select>
    </div>
  </div>
  <div class="layui-inline">
    <div class="layui-form-label short-label"><?php echo __('　范围'); ?></div>
    <div class="layui-input-inline" style="width: 80px;">
      <input type="number" name="road_start" id="road_start" autocomplete="off" class="layui-input StreetNo">
    </div>
    <div class="layui-form-mid">-</div>
    <div class="layui-input-inline" style="width: 80px;">
      <input type="number" name="road_end" id="road_end" autocomplete="off" class="layui-input StreetNo">
    </div>
  </div>
</div>
<div class="layui-form-item">
  <div class="layui-inline">
    <div class="layui-form-label short-label"><?php echo __('街名'); ?></div>
    <div class="layui-input-inline ">
      <select name="street_id" lay-search lay-filter="streetChange">
        <option value="">请选择</option>
        <?php if(is_array($street_LIST) || $street_LIST instanceof \think\Collection || $street_LIST instanceof \think\Paginator): if( count($street_LIST)==0 ) : echo "" ;else: foreach($street_LIST as $key=>$item): ?>
          <option value="<?php echo htmlentities($item['id']); ?>"><?php echo htmlentities($item['name']); ?></option>
        <?php endforeach; endif; else: echo "" ;endif; ?>
      </select>
    </div>
  </div>
  <div class="layui-inline">
    <div class="layui-form-label short-label"><?php echo __('　范围'); ?></div>
    <div class="layui-input-inline" style="width: 80px;">
      <input type="number" name="street_start" id="street_start" autocomplete="off" class="layui-input StreetNo">
    </div>
    <div class="layui-form-mid">-</div>
    <div class="layui-input-inline" style="width: 80px;">
      <input type="number" name="street_end" id="street_end" autocomplete="off" class="layui-input StreetNo">
    </div>
  </div>
</div>
                    <div class="layui-inline">
                        <div class="layui-form-label  short-label"><?php echo __('实有建筑'); ?></div>
                        <div class="layui-input-inline">
                            <select id="building" name="building_id" lay-search  lay-filter="buildingChange">
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-form-label  short-label"><?php echo __('单元'); ?></div>
                        <div class="layui-input-inline">
                            <select id="unit" name="unit" lay-filter="search">
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-form-label">养老保险</div>
                        <div class="layui-input-inline">
                            <select name="endowment_insurance"  lay-filter="search">
                                <option value="">全部</option>
                                <?php if(is_array($endowment_insurance) || $endowment_insurance instanceof \think\Collection || $endowment_insurance instanceof \think\Paginator): if( count($endowment_insurance)==0 ) : echo "" ;else: foreach($endowment_insurance as $key=>$vo): ?>
                                    <option value="<?php echo htmlentities($key+1); ?>"><?php echo htmlentities($vo); ?></option>
                                <?php endforeach; endif; else: echo "" ;endif; ?>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-form-label">户口类型</div>
                        <div class="layui-input-inline">
                            <select name="rpr"  lay-filter="search">
                                <option value="">全部</option>
                                <?php if(is_array($rpr) || $rpr instanceof \think\Collection || $rpr instanceof \think\Paginator): if( count($rpr)==0 ) : echo "" ;else: foreach($rpr as $key=>$vo): ?>
                                    <option value="<?php echo htmlentities($key); ?>"><?php echo htmlentities($vo); ?></option>
                                <?php endforeach; endif; else: echo "" ;endif; ?>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-form-label">户口性质</div>
                        <div class="layui-input-inline">
                            <select name="rpr_nature"  lay-filter="search">
                                <option value="">全部</option>
                                <?php if(is_array($rpr_nature) || $rpr_nature instanceof \think\Collection || $rpr_nature instanceof \think\Paginator): if( count($rpr_nature)==0 ) : echo "" ;else: foreach($rpr_nature as $key=>$vo): ?>
                                    <option value="<?php echo htmlentities($key); ?>"><?php echo htmlentities($vo); ?></option>
                                <?php endforeach; endif; else: echo "" ;endif; ?>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-form-label">人口类型</div>
                        <div class="layui-input-inline">
                            <select name="person_type"  lay-filter="search">
                                <option value="">全部</option>
                                <?php if(is_array($person_type) || $person_type instanceof \think\Collection || $person_type instanceof \think\Paginator): if( count($person_type)==0 ) : echo "" ;else: foreach($person_type as $key=>$vo): ?>
                                    <option value="<?php echo htmlentities($key); ?>"><?php echo htmlentities($vo); ?></option>
                                <?php endforeach; endif; else: echo "" ;endif; ?>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-form-label">兵役状况</div>
                        <div class="layui-input-inline">
                            <select name="enlistment"  lay-filter="search">
                                <option value="">全部</option>
                                <?php if(is_array($enlistment) || $enlistment instanceof \think\Collection || $enlistment instanceof \think\Paginator): if( count($enlistment)==0 ) : echo "" ;else: foreach($enlistment as $key=>$vo): ?>
                                    <option value="<?php echo htmlentities($key); ?>"><?php echo htmlentities($vo); ?></option>
                                <?php endforeach; endif; else: echo "" ;endif; ?>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-form-label">政治面貌</div>
                        <div class="layui-input-inline">
                            <select name="face"  lay-filter="search">
                                <option value="">全部</option>
                                <?php if(is_array($face) || $face instanceof \think\Collection || $face instanceof \think\Paginator): if( count($face)==0 ) : echo "" ;else: foreach($face as $key=>$vo): ?>
                                    <option value="<?php echo htmlentities($key); ?>"><?php echo htmlentities($vo); ?></option>
                                <?php endforeach; endif; else: echo "" ;endif; ?>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-form-label">文化程度</div>
                        <div class="layui-input-inline">
                            <select name="education"  lay-filter="search">
                                <option value="">全部</option>
                                <?php if(is_array($education) || $education instanceof \think\Collection || $education instanceof \think\Paginator): if( count($education)==0 ) : echo "" ;else: foreach($education as $key=>$vo): ?>
                                    <option value="<?php echo htmlentities($key); ?>"><?php echo htmlentities($vo); ?></option>
                                <?php endforeach; endif; else: echo "" ;endif; ?>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-form-label">民族</div>
                        <div class="layui-input-inline">
                            <select name="nation"  lay-filter="search">
                                <option value="">全部</option>
                                <?php if(is_array($nation) || $nation instanceof \think\Collection || $nation instanceof \think\Paginator): if( count($nation)==0 ) : echo "" ;else: foreach($nation as $key=>$vo): ?>
                                    <option value="<?php echo htmlentities($key); ?>"><?php echo htmlentities($vo); ?></option>
                                <?php endforeach; endif; else: echo "" ;endif; ?>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-form-label">性别</div>
                        <div class="layui-input-inline">
                            <select name="sex"  lay-filter="search">
                                <option value="">全部</option>
                                <?php if(is_array($sex) || $sex instanceof \think\Collection || $sex instanceof \think\Paginator): if( count($sex)==0 ) : echo "" ;else: foreach($sex as $key=>$vo): ?>
                                    <option value="<?php echo htmlentities($key); ?>"><?php echo htmlentities($vo); ?></option>
                                <?php endforeach; endif; else: echo "" ;endif; ?>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-form-label">婚姻状况</div>
                        <div class="layui-input-inline">
                            <select name="marry"  lay-filter="search">
                                <option value="">全部</option>
                                <?php if(is_array($marry) || $marry instanceof \think\Collection || $marry instanceof \think\Paginator): if( count($marry)==0 ) : echo "" ;else: foreach($marry as $key=>$vo): ?>
                                    <option value="<?php echo htmlentities($key); ?>"><?php echo htmlentities($vo); ?></option>
                                <?php endforeach; endif; else: echo "" ;endif; ?>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-form-label">独生子女</div>
                        <div class="layui-input-inline">
                            <select name="only_child"  lay-filter="search">
                                <option value="">全部</option>
                                <?php if(is_array($only_child) || $only_child instanceof \think\Collection || $only_child instanceof \think\Paginator): if( count($only_child)==0 ) : echo "" ;else: foreach($only_child as $key=>$vo): ?>
                                    <option value="<?php echo htmlentities($key); ?>"><?php echo htmlentities($vo); ?></option>
                                <?php endforeach; endif; else: echo "" ;endif; ?>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-form-label">子女家庭</div>
                        <div class="layui-input-inline">
                            <select name="other_child"  lay-filter="search">
                                <option value="">全部</option>
                                <?php if(is_array($other_child) || $other_child instanceof \think\Collection || $other_child instanceof \think\Paginator): if( count($other_child)==0 ) : echo "" ;else: foreach($other_child as $key=>$vo): ?>
                                    <option value="<?php echo htmlentities($key+1); ?>"><?php echo htmlentities($vo); ?></option>
                                <?php endforeach; endif; else: echo "" ;endif; ?>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-form-label">健康状况</div>
                        <div class="layui-input-inline">
                            <select name="health"  lay-filter="search">
                                <option value="">全部</option>
                                <?php if(is_array($health) || $health instanceof \think\Collection || $health instanceof \think\Paginator): if( count($health)==0 ) : echo "" ;else: foreach($health as $key=>$vo): ?>
                                    <option value="<?php echo htmlentities($key); ?>"><?php echo htmlentities($vo); ?></option>
                                <?php endforeach; endif; else: echo "" ;endif; ?>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-form-label">劳动能力</div>
                        <div class="layui-input-inline">
                            <select name="labor_capacity"  lay-filter="search">
                                <option value="">全部</option>
                                <?php if(is_array($labor_capacity) || $labor_capacity instanceof \think\Collection || $labor_capacity instanceof \think\Paginator): if( count($labor_capacity)==0 ) : echo "" ;else: foreach($labor_capacity as $key=>$vo): ?>
                                    <option value="<?php echo htmlentities($key); ?>"><?php echo htmlentities($vo); ?></option>
                                <?php endforeach; endif; else: echo "" ;endif; ?>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-form-label">宗教</div>
                        <div class="layui-input-inline">
                            <select name="religion"  lay-filter="search">
                                <option value="">全部</option>
                                <?php if(is_array($religion) || $religion instanceof \think\Collection || $religion instanceof \think\Paginator): if( count($religion)==0 ) : echo "" ;else: foreach($religion as $key=>$vo): ?>
                                    <option value="<?php echo htmlentities($key); ?>"><?php echo htmlentities($vo); ?></option>
                                <?php endforeach; endif; else: echo "" ;endif; ?>
                            </select>
                        </div>
                    </div>
                    <div class="clear"></div>
                    <div class="layui-inline">
                        <div class="layui-form-label">出生年月日</div>
                        <div class="layui-input-inline" style="width: 236px;">
                            <div class="layui-input-inline" style="width: 100px;">
                                <input class="layui-input" lay-datetime="" name="birth_start" id="start_date"
                                       data-datetype="date" data-dateformat="yyyy-MM-dd"
                                       placeholder="yyyy-MM-dd">
                            </div>
                            <div class="layui-form-mid">-</div>
                            <div class="layui-input-inline" style="width: 100px;">
                                <input class="layui-input" lay-datetime="" name="birth_end" id="end_date"
                                       data-datetype="date" data-dateformat="yyyy-MM-dd"
                                       placeholder="yyyy-MM-dd">
                            </div>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-form-label">年龄</div>
                        <div class="layui-input-inline" style="width: 80px;">
                            <input type="number" name="age_start" autocomplete="off" class="layui-input">
                        </div>
                        <div class="layui-form-mid">-</div>
                        <div class="layui-input-inline" style="width: 80px;">
                            <input type="number" name="age_end" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-card" style="border: 1px solid #e3e3e3;">
                        <div class="layui-card-header">特殊人群</div>
                        <div class="layui-card-body">
                            <input type="checkbox" name="consistency" value="1" title="人户一致" lay-filter="search">
                            <input type="checkbox" name="retire" value="1" title="退休" lay-filter="search">
                            <?php if(is_array($insurance) || $insurance instanceof \think\Collection || $insurance instanceof \think\Paginator): if( count($insurance)==0 ) : echo "" ;else: foreach($insurance as $key=>$vo): ?>
                                <input type="checkbox"  name="<?php echo htmlentities($key); ?>"  lay-filter="search"
                                       value="1" title="<?php echo htmlentities($vo); ?>">
                            <?php endforeach; endif; else: echo "" ;endif; if(is_array($representative) || $representative instanceof \think\Collection || $representative instanceof \think\Paginator): if( count($representative)==0 ) : echo "" ;else: foreach($representative as $key=>$vo): 
                                    $key = camelCaseToUnderscore($key);
                                 ?>
                                <input type="checkbox"  name="<?php echo htmlentities($key); ?>"  lay-filter="search"
                                       value="1" title="<?php echo htmlentities($vo); ?>">
                            <?php endforeach; endif; else: echo "" ;endif; if(is_array($elderly) || $elderly instanceof \think\Collection || $elderly instanceof \think\Paginator): if( count($elderly)==0 ) : echo "" ;else: foreach($elderly as $key=>$vo): 
                                    $key = camelCaseToUnderscore($key);
                                 ?>
                                <input type="checkbox"  name="<?php echo htmlentities($key); ?>"  lay-filter="search"
                                       value="1" title="<?php echo htmlentities($vo); ?>">
                            <?php endforeach; endif; else: echo "" ;endif; if(is_array($dilemma) || $dilemma instanceof \think\Collection || $dilemma instanceof \think\Paginator): if( count($dilemma)==0 ) : echo "" ;else: foreach($dilemma as $key=>$vo): 
                                    $key = camelCaseToUnderscore($key);
                                 ?>
                                <input type="checkbox"  name="<?php echo htmlentities($key); ?>"  lay-filter="search"
                                       value="1" title="<?php echo htmlentities($vo); ?>">
                            <?php endforeach; endif; else: echo "" ;endif; if(is_array($dilemma_children) || $dilemma_children instanceof \think\Collection || $dilemma_children instanceof \think\Paginator): if( count($dilemma_children)==0 ) : echo "" ;else: foreach($dilemma_children as $key=>$vo): 
                                    $key = camelCaseToUnderscore($key);
                                 ?>
                                <input type="checkbox"  name="<?php echo htmlentities($key); ?>"  lay-filter="search"
                                       value="1" title="<?php echo htmlentities($vo); ?>">
                            <?php endforeach; endif; else: echo "" ;endif; if(is_array($patient) || $patient instanceof \think\Collection || $patient instanceof \think\Paginator): if( count($patient)==0 ) : echo "" ;else: foreach($patient as $key=>$vo): 
                                    $key = camelCaseToUnderscore($key);
                                 ?>
                                <input type="checkbox"  name="<?php echo htmlentities($key); ?>"  lay-filter="search"
                                       value="1" title="<?php echo htmlentities($vo); ?>">
                            <?php endforeach; endif; else: echo "" ;endif; if(is_array($bad) || $bad instanceof \think\Collection || $bad instanceof \think\Paginator): if( count($bad)==0 ) : echo "" ;else: foreach($bad as $key=>$vo): 
                                    $key = camelCaseToUnderscore($key);
                                 ?>
                                <input type="checkbox"  name="<?php echo htmlentities($key); ?>"  lay-filter="search"
                                       value="1" title="<?php echo htmlentities($vo); ?>">
                            <?php endforeach; endif; else: echo "" ;endif; if(is_array($other) || $other instanceof \think\Collection || $other instanceof \think\Paginator): if( count($other)==0 ) : echo "" ;else: foreach($other as $key=>$vo): 
                                    $key = camelCaseToUnderscore($key);
                                 ?>
                                <input type="checkbox"  name="<?php echo htmlentities($key); ?>"  lay-filter="search"
                                       value="1" title="<?php echo htmlentities($vo); ?>">
                            <?php endforeach; endif; else: echo "" ;endif; ?>
                            <input type="checkbox" name="disability" value="1" title="残疾" lay-filter="search">
                            <input type="checkbox" name="dead" value="1" title="死亡" lay-filter="search">

                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- // 创建数据实例 -->
        <table id="lay-tableList" lay-filter="lay-tableList"></table>
    </div>
</div>

<script type="text/html" id="tableButton">
    <div class="layui-btn-container">
        <button class="layui-btn icon-btn" lay-open="" data-title="<?php echo __('添加'); ?>" data-area="100%,100%"
                data-maxmin="true" data-url="<?php echo url('/'.$controller.'/add'); ?>">
            <i class="layui-icon layui-icon-add-1"></i><?php echo __('添加'); ?>
        </button>
        <button class="layui-btn icon-btn" lay-filter="formExport" lay-submit><i class="layui-icon layui-icon-export"></i><?php echo __('导出'); ?></button>
<button id="formExport" class="layui-btn" data-url="<?php echo url($controller.'/dataToExcel'); ?>" lay-event="formExport">批量导出</button>
<button class="layui-btn icon-btn" lay-open="" data-title="<?php echo __('数据导入'); ?>" data-area="750px,500px"
        data-maxmin="true" data-url="/admin/<?php echo htmlentities($controller); ?>/dataImport" >
  <i class="layui-icon layui-icon-import"></i><?php echo __('导入'); ?>
</button>
        <button class="layui-btn layui-btn-danger" lay-event="Pending">挂起</button>
        <button id="delete-btn" class="layui-btn layui-btn-danger" data-url="<?php echo url('Pending/del'); ?>" lay-event="delete">批量删除</button>
        <button class="layui-btn icon-btn" lay-open="" data-title="<?php echo __('问题数据'); ?>" data-area="100%,100%"
                data-maxmin="true" data-url="<?php echo url('/'.$controller.'/proData'); ?>">
            <?php echo __('问题数据'); ?>
        </button>
    </div>
</script>
<!-- // 列表工具栏 -->
<script type="text/html" id="tableBar">
    <!--formBegin-->
    <a class="layui-table-text" data-title="<?php echo __('查看'); ?>" data-area="100%,100%" data-maxmin="true"
       data-url="<?php echo url('/'.$controller.'/detail'); ?>?id={{d.id}}" lay-event="edit" ><?php echo __('查看'); ?></a>
    <div class="layui-divider layui-divider-vertical"></div>
    <a class="layui-table-text" data-title="<?php echo __('编辑'); ?>" data-area="100%,100%" data-maxmin="true"
       data-url="<?php echo url('/Person/edit'); ?>?id={{d.id}}" lay-event="edit" ><?php echo __('编辑'); ?></a>
    <div class="layui-divider layui-divider-vertical"></div>
    <!--formEnd-->
    <a class="layui-table-text"  data-url="<?php echo url('/Person/del'); ?>?id={{d.id}}" lay-event="del" ><?php echo __('删除'); ?></a>
</script>




<!--// 加载Markdown编辑器-->
<link href="/static/js/markdown/cherry-markdown.css?v=<?php echo release(); ?>" rel="stylesheet" type="text/css"/>
<script src="/static/js/markdown/cherry-markdown.core.js?v=<?php echo release(); ?>"></script>

<!-- // 全局加载第三方JS -->
<script src="/static/js/tinymce/tinymce.min.js?v=<?php echo release(); ?>"></script>
<script src="/static/system/module/xmSelect/xmSelect.js?v=<?php echo release(); ?>"></script>
<!--// 加载CommonJS-->
<script src="/static/system/layui/layui.js?v=<?php echo release(); ?>"></script>
<script src="/static/system/js/common.js?v=<?php echo release(); ?>"></script>
<script src="/static/system/js/cascadata.js?v=<?php echo release(); ?>"></script>
<script>
    layui.use(['admin','table','form','jquery'], function () {

        var admin = layui.admin;
        var table = layui.table;
        var form = layui.form;
        var $ = layui.jquery;

        /*
         * 初始化表格
        */
        var isTable = table.render({
            elem: "#lay-tableList"
            ,url: "<?php echo url('/Person/index',$params); ?>"
            ,toolbar: '#tableButton'
            ,defaultToolbar: ['filter', 'exports', 'print','search']
            ,cellMinWidth: 60
            ,page: true
            ,limit: 50,limits: [50, 100, 200, 500]
            ,height: 'full-150' //设置表格高度，减去固定的底部高度
            ,cols: [[
                {type: 'checkbox', width: 50},
                {field:'name',align: 'left',width:180,title:'<?php echo __("姓名"); ?>', templet: function(d){
                    if (d.name && d.name.length === 2) {
                          return d.name.substring(0, 1) + '\u3000' + d.name.substring(1);
                        }
                    if (d.name && d.name[3]=='s'){
                        return d.name.substring(0, 1) + '\u3000' + d.name.substring(1);
                    }  
                        return d.name;
                }},
                {field:'id_code',title:'<?php echo __("年龄"); ?>',width:50,templet:function(d){
                        return d.age
                    }},
                {field:'sex',title:'<?php echo __("性别"); ?>',width:50,templet:function(d){
                        return d.sex==1?'男':'女'
                    }},
                {field: 'community_id',width: 320, minWidth: 300, title: '<?php echo __("所在区域"); ?>',templet:function(d){
                        return d.community_id>0?d.community_name+","+d.grid_group_name+","+d.grid_name:''
                    }},
                {field: 'building_name',width:160, title: '<?php echo __("所在房屋"); ?>'},
                {field:'address',width:200,title:'<?php echo __("房屋地址"); ?>'},
                {field:'id_code',width:180,title:'<?php echo __("身份证号"); ?>'},
                {field:'phone',width:120,title:'<?php echo __("联系方式"); ?>'},
                {field:'rpr_address',width:200,title:'<?php echo __("户籍地址"); ?>'},
                {field:'nation',width:80,title:'<?php echo __("民族"); ?>',templet:function(d){
                        var resData = <?php echo json_encode($nation); ?>;
                        return d.nation > 0 ?resData[d.nation]:''
                    }},
                {field:'face',width:80,title:'<?php echo __("政治面貌"); ?>',templet:function(d){
                        var resData = <?php echo json_encode($face); ?>;
                        return d.face > 0 ?resData[d.face]:''
                    }},
                {field:'education',width:80,title:'<?php echo __("学历"); ?>',templet:function(d){
                        var resData = <?php echo json_encode($education); ?>;
                        return d.education > 0 ?resData[d.education]:''
                    }},
                {field: 'create_by',width: 120,title:'<?php echo __("创建者"); ?>'},
                {field: 'create_time',width: 160,title:'<?php echo __("创建时间"); ?>'},
                {field: 'update_by',width: 120,title:'<?php echo __("更新者"); ?>'},
                {field: 'update_time',width: 160,title:'<?php echo __("更新时间"); ?>'},
                {align: 'center', toolbar: '#tableBar', width:200, fixed: 'right', title: '<?php echo __("操作"); ?>'},
            ]]
        })

    })
</script>
<?php if($action == 'detail'): ?>



    <style>
        .layui-form-item {
            margin-bottom: 5px;
            margin-right: 10px;
        }



        .layui-form-item label {
            min-height: 24px;
        }



        .layui-form-item label,
        .layui-form-item .layui-input-block {
            border: 1px solid #e3e3e3;
            border-radius: 2px;
            background: #fafafa;
            display: flex;
        }



        .layui-form-item .layui-input-block {
            margin-left: 111px;
        }



        #renew {
            display: none;
        }



        .layui-form-mid {
            position: relative;
            left: -25px;
        }
    </style>



<?php endif; ?>



<script>



    function jsonToGetString(json) {



        let params = [];



        for (let key in json) {



            if (json.hasOwnProperty(key) && json[key] !== null && json[key] !== undefined && json[key] !== '') {



                params.push(encodeURIComponent(key) + '=' + encodeURIComponent(json[key]));



            }



        }



        return params.join('&');



    }



    layui.use(['layer', 'form', 'layer', 'table', 'admin', 'laydate'], function () {



        var form = layui.form;



        var $ = layui.jquery;



        var layer = layui.layer;



        var table = layui.table;



        var admin = layui.admin;



        var laydate = layui.laydate;



        setInterval(function () {



            admin.imageClick();



        }, 1000)







        form.on('select(xiafaChange)', function (data) {



            $('#xfcommunity_id').empty();



            $('#xfgrid_group').empty();



            $('#xfgrid').empty();



            search($)



            $.ajax({



                url: '<?php echo url("/Ajax/getCommunityList"); ?>',



                dataType: 'json',



                type: 'post',



                // data:{community_id:community_id},



                success: function (resData) {



                    $('#xfcommunity_id').append(new Option("请选择", ""));// 下拉菜单里添加元素



                    $.each(resData, function (index, value) {



                        $('#xfcommunity_id').append(new Option(value.community_name, value.id));// 下拉菜单里添加元素



                    });



                    form.render('select');



                }



            });



            // form.render('select');



        });







        form.on('select(ssptypeChange)', function (data) {







            $('#xiafa').empty();



            search($)



            var type = data.value; // roomName 选中的省份名称



            $.ajax({



                url: '<?php echo url("/wxssp/ssptype"); ?>',



                dataType: 'json',



                type: 'post',



                data: { type: type },



                success: function (resData) {



                    console.log(resData);



                    $('#xiafa').append(new Option("请选择", ""));// 下拉菜单里添加元素



                    $.each(resData, function (index, value) {



                        $('#xiafa').append(new Option(value.name, value.id));// 下拉菜单里添加元素



                    });



                    form.render('select');



                }



            });



        });



        form.on('select(xfcommunityChange)', function (data) {



            $('#xfgrid_group').empty();



            $('#xfgrid').empty();



            $('#xiafa').empty();



            if ($("#street").is("*")) {



                $('#street').empty();



                $('#road').empty();



            }



            if ($("#building").is("*")) {



                $('#building').empty();



            }



            if ($("#house").is("*")) {



                $('#house').empty();



            }



            if ($("#unit").is("*")) {



                $('#unit').empty();



            }



            search($)



            var community_id = data.value; // roomName 选中的省份名称



            console.log(data.value)



            $.ajax({



                url: '<?php echo url("/Ajax/getGridGroupList"); ?>',



                dataType: 'json',



                type: 'post',



                data: { community_id: community_id },



                success: function (resData) {



                    $('#xfgrid_group').append(new Option("请选择", ""));// 下拉菜单里添加元素



                    $.each(resData, function (index, value) {



                        $('#xfgrid_group').append(new Option(value.grid_name, value.id));// 下拉菜单里添加元素



                    });



                    form.render('select');



                }



            });



        });



        form.on('select(xfgridGroupChange)', function (data) {



            $('#xfgrid').empty();



            if ($("#street").is("*")) {



                $('#street').empty();



                $('#road').empty();



            }



            if ($("#building").is("*")) {



                $('#building').empty();



            }



            if ($("#house").is("*")) {



                $('#house').empty();



            }



            if ($("#unit").is("*")) {



                $('#unit').empty();



            }



            search($)



            var grid_id = data.value; // roomName 选中的省份名称



            $.ajax({



                url: '<?php echo url("/Ajax/getGridList"); ?>',



                dataType: 'json',



                type: 'post',



                data: { grid_id: grid_id },



                success: function (resData) {



                    $('#xfgrid').append(new Option("请选择", ""));// 下拉菜单里添加元素



                    $.each(resData, function (index, value) {



                        $('#xfgrid').append(new Option(value.grid_name, value.id));// 下拉菜单里添加元素



                    });



                    form.render('select');



                }



            });



        });



        form.on('select(xfgridChange)', function (data) {



            search($)



            var grid_id = data.value; // roomName 选中的省份名称



            form.render('select');



            if ($("#street").is("*")) {



                $('#street').empty();



                $('#road').empty();



                $.ajax({



                    url: '<?php echo url("/Ajax/getGridStreetList"); ?>',



                    dataType: 'json',



                    type: 'post',



                    data: { grid_id: grid_id },



                    success: function (resData) {



                        var shtml = "<option value=''>请选择</option>";



                        var rhtml = "<option value=''>请选择</option>";







                        $.each(resData, function (index, value) {



                            var typeNo = value.typeNo === 1 ? '单号' : '双号';



                            if (value.type == 2) {



                                shtml += "<option min='" + value.start + "' max='" + value.end + "' value='" + value.id + "'>" + value.name + "(" + typeNo + ")(" + value.start + "-" + value.end + ")</option>";



                            } else {



                                rhtml += "<option min='" + value.start + "' max='" + value.end + "' value='" + value.id + "'>" + value.name + "(" + typeNo + ")(" + value.start + "-" + value.end + ")</option>";



                            }



                        });



                        $('#street').html(shtml);



                        $('#road').html(rhtml);



                        form.render('select');



                    }



                });



            }



            if ($("#building").is("*")) {



                $('#building').empty();



                $.ajax({



                    url: '<?php echo url("/Ajax/getBuildingList"); ?>',



                    dataType: 'json',



                    type: 'post',



                    data: { grid_id: grid_id },



                    success: function (resData) {



                        var bhtml = "<option value=''>请选择</option>";



                        $.each(resData, function (index, value) {



                            bhtml += "<option unit='" + value.unit + "' value='" + value.id + "'>" + value.building_name + "</option>";



                        });



                        $('#building').html(bhtml);



                        form.render('select');



                    }



                });



            }



            if ($("#house").is("*")) {



                $('#house').empty();



            }



            if ($("#unit").is("*")) {



                $('#unit').empty();



            }







        });



        form.on('select(communityChange)', function (data) {



            $('#grid_group').empty();



            $('#grid').empty();



            if ($("#street").is("*")) {



                $('#street').empty();



                $('#road').empty();



            }



            if ($("#building").is("*")) {



                $('#building').empty();



            }



            if ($("#house").is("*")) {



                $('#house').empty();



            }



            if ($("#unit").is("*")) {



                $('#unit').empty();



            }



            search($)



            var community_id = data.value; // roomName 选中的省份名称



            console.log(data.value)



            $.ajax({



                url: '<?php echo url("/Ajax/getGridGroupList"); ?>',



                dataType: 'json',



                type: 'post',



                data: { community_id: community_id },



                success: function (resData) {



                    $('#grid_group').append(new Option("请选择", ""));// 下拉菜单里添加元素



                    $.each(resData, function (index, value) {



                        $('#grid_group').append(new Option(value.grid_name, value.id));// 下拉菜单里添加元素



                    });



                    form.render('select');



                }



            });



        });



        form.on('select(gridGroupChange)', function (data) {



            $('#grid').empty();



            if ($("#street").is("*")) {



                $('#street').empty();



                $('#road').empty();



            }



            if ($("#building").is("*")) {



                $('#building').empty();



            }



            if ($("#house").is("*")) {



                $('#house').empty();



            }



            if ($("#unit").is("*")) {



                $('#unit').empty();



            }



            search($)



            var grid_id = data.value; // roomName 选中的省份名称



            $.ajax({



                url: '<?php echo url("/Ajax/getGridList"); ?>',



                dataType: 'json',



                type: 'post',



                data: { grid_id: grid_id },



                success: function (resData) {



                    $('#grid').append(new Option("请选择", ""));// 下拉菜单里添加元素



                    $.each(resData, function (index, value) {



                        $('#grid').append(new Option(value.grid_name, value.id));// 下拉菜单里添加元素



                    });



                    form.render('select');



                }



            });



        });



        form.on('select(gridChange)', function (data) {



            search($)



            var grid_id = data.value; // roomName 选中的省份名称



            form.render('select');



            if ($("#street").is("*")) {



                $('#street').empty();



                $('#road').empty();



                $.ajax({



                    url: '<?php echo url("/Ajax/getGridStreetList"); ?>',



                    dataType: 'json',



                    type: 'post',



                    data: { grid_id: grid_id },



                    success: function (resData) {



                        var shtml = "<option value=''>请选择</option>";



                        var rhtml = "<option value=''>请选择</option>";







                        $.each(resData, function (index, value) {



                            var typeNo = value.typeNo === 1 ? '单号' : '双号';



                            if (value.type == 2) {



                                shtml += "<option min='" + value.start + "' max='" + value.end + "' value='" + value.id + "'>" + value.name + "(" + typeNo + ")(" + value.start + "-" + value.end + ")</option>";



                            } else {



                                rhtml += "<option min='" + value.start + "' max='" + value.end + "' value='" + value.id + "'>" + value.name + "(" + typeNo + ")(" + value.start + "-" + value.end + ")</option>";



                            }



                        });



                        $('#street').html(shtml);



                        $('#road').html(rhtml);



                        form.render('select');



                    }



                });



            }



            if ($("#building").is("*")) {



                $('#building').empty();



                $.ajax({



                    url: '<?php echo url("/Ajax/getBuildingList"); ?>',



                    dataType: 'json',



                    type: 'post',



                    data: { grid_id: grid_id },



                    success: function (resData) {



                        var bhtml = "<option value=''>请选择</option>";



                        $.each(resData, function (index, value) {



                            bhtml += "<option unit='" + value.unit + "' value='" + value.id + "'>" + value.building_name + "</option>";



                        });



                        $('#building').html(bhtml);



                        form.render('select');



                    }



                });



            }



            if ($("#house").is("*")) {



                $('#house').empty();



            }



            if ($("#unit").is("*")) {



                $('#unit').empty();



            }



        });







        form.on('select(streetChange)', function () {



            search($)



            var streetObj = $('#street option:selected');



            var startObj = $("#street_start");



            var endObj = $("#street_end");



            startObj.val("");



            endObj.val("");



            var min = streetObj.attr('min');



            var max = streetObj.attr('max');



            startObj.attr('min', min);



            startObj.attr('max', max);



            endObj.attr('min', min);



            endObj.attr('max', max);



            form.render('input');



        });



        form.on('select(roadChange)', function () {

            search($)

            var roadObj = $('#road option:selected');

            var startObj = $("#road_start");

            var endObj = $("#road_end");

            startObj.val("");

            endObj.val("");

            var min = roadObj.attr('min');

            var max = roadObj.attr('max');

            startObj.attr('min', min);

            startObj.attr('max', max);

            endObj.attr('min', min);

            endObj.attr('max', max);

            
            form.render('input');

        });



        <?php if(($action == "add" || $action == "edit")): ?>

            // 监听input输入事件
            $(".StreetNo").on('blur', function (e) {

                search($);

                var obj = $(this);
                console.log("StreetNo obj>>>>",obj)
                var id = $(this).attr("id");

                var min = parseInt(obj.attr('min'));

                var max = parseInt(obj.attr('max'));

                var value = parseInt(obj.val());

                console.log('road>>>>>',value,min);
                if (value && (value % 2) != (min % 2)) {

                    var typeNmae = min % 2 == 0 ? '双号' : '单号';

                    layer.msg('只能填写' + typeNmae + '！', { icon: 5 });

                    $(this).val("");
                }

                if ((id == "road_start" || id == "road_end") && $("#road_fictitious").is(":checked")) {

                    return false;
                }

                if ((id == "street_start" || id == "street_end") && $("#street_fictitious").is(":checked")) {

                    return false;
                }

                if (value && (value < min || value > max)) {

                    layer.msg('超过起止号范围(' + min + '-' + max + ')！', { icon: 5 });

                    $(this).val("");
                }
            });


        <?php endif; ?>



            form.on('select(buildingChange)', function (data) {



                $('#house').empty();



                search($)



                var building_id = data.value; // roomName 选中的省份名称



                $.ajax({



                    url: '<?php echo url("/Ajax/getHouseList"); ?>',



                    dataType: 'json',



                    type: 'post',



                    data: { building_id: building_id },



                    success: function (resData) {



                        var hhtml = "<option value=''>请选择</option>";



                        $.each(resData, function (index, value) {



                            if (value.floor) {



                                value.code = value.floor + "楼" + value.code;



                            }



                            if (value.unit) {



                                value.code = value.unit + "单元" + value.code;



                            }



                            if (value.street) {



                                value.code = value.code + '(' + value.street + ')';



                            }



                            hhtml += "<option value='" + value.id + "' area='" + value.area + "'>" + value.code + "</option>";



                        });



                        $('#house').html(hhtml);



                        form.render('select');



                    }



                });



                if ($("#unit").is("*")) {



                    $('#unit').empty();



                    var buildingObj = $('#building option:selected');



                    var unit = buildingObj.attr('unit');



                    var uhtml = "<option value=''>请选择</option>";



                    for (var i = 1; i <= unit; i++) {



                        uhtml += "<option value='" + i + "'>" + i + "单元</option>";



                    }



                    $('#unit').html(uhtml);



                    form.render('select');



                }



            });







        form.on('select(houseChange)', function () {



            var houseObj = $('#house option:selected');



            if ($("#area").is("*")) {



                var area = houseObj.attr('area');



                $("#area").val(area);



            }



        });



        form.on("submit(formExport)", function () {



            var formData = [];



            $('.layui-form input:not(:checkbox), .layui-form select').each(function () {



                var name = $(this).attr('name');



                var value = $(this).val();



                if (name && value) {



                    formData.push(name + "=" + value);



                }



            });



            $('.layui-form input:checkbox').each(function () {



                if ($(this).is(":checked")) {



                    var name = $(this).attr('name');



                    formData.push(name + "=1");



                }



            });



            var url = "/admin/<?php echo htmlentities($controller); ?>/dataToExcel";



            const data_url = $(this).attr('data-url');



            if (data_url) {



                url = data_url;



            }



            window.open(url + '?' + formData.join("&"));



            return false;



        })



        table.on('toolbar(lay-tableList)', function (obj) {



            var checkStatus = table.checkStatus(obj.config.id);



            switch (obj.event) {



                case 'Pending':



                    //获取选中的数据



                    var checkData = checkStatus.data;



                    if (checkData.length == 0) {



                        layer.msg('请至少勾选一条数据', { icon: 2 });



                    } else {



                        layer.confirm('确定挂起选中的数据吗？', { icon: 3, title: '提示' }, function (index) {



                            //这里可以发送批量删除请求



                            console.log(checkData);



                            var id = [];



                            checkData.forEach(function (item, index) {



                                id.push(item.id)



                            });



                            console.log(id)



                            $.post('/admin/Pending/add',



                                { id: id }, function (res) {



                                    layer.close(index);



                                    if (res.code === 200) {



                                        layer.msg(res.msg);



                                        search($)



                                    }



                                    else {



                                        layui.show.error(res.msg);



                                    }







                                }, 'json');



                        });



                    }



                    break;



                case 'formExport':



                    var url = $("#formExport").attr('data-url')



                    //获取选中的数据



                    var checkData = checkStatus.data;



                    if (checkData.length == 0) {



                        layer.msg('请至少勾选一条数据', { icon: 2 });



                    } else {



                        layer.confirm('确定导出选中的数据吗？', { icon: 3, title: '提示' }, function (index) {



                            //这里可以发送批量删除请求



                            var id = [];



                            checkData.forEach(function (item, index) {



                                id.push(item.id)



                            });



                            $.post(url,



                                { id: id }, function (res) {



                                    layer.close(index);



                                    if (res.code === 200) {



                                        location.href = res.url;



                                    }



                                    else {



                                        layui.show.error(res.msg);



                                    }







                                }, 'json');



                        });



                    }



                    break;



                case 'delete':



                    //获取选中的数据



                    var checkData = checkStatus.data;



                    var url = $("#delete-btn").attr('data-url')



                    if (checkData.length == 0) {



                        layer.msg('请至少勾选一条数据', { icon: 2 });



                    } else {



                        layer.confirm('确定删除选中的数据吗？', { icon: 3, title: '提示' }, function (index) {



                            var id = [];



                            checkData.forEach(function (item, index) {



                                id.push(item.id)



                            });



                            $.post(url,



                                { id: id }, function (res) {



                                    layer.close(index);



                                    if (res.code === 200) {



                                        layer.msg(res.msg);



                                        search($)



                                    }



                                    else {



                                        layui.show.error(res.msg);



                                    }







                                }, 'json');



                        });



                    }



                    break;



            };



        });



        form.on('select(search)', function (data) {



            search($)



            form.render('select');



        });



        form.on('checkbox(search)', function (data) {



            search($)



            form.render('select');



        });



        $(".noNum").on("blur", function () {



            if (/\d/.test($(this).val())) {



                $(this).val("");



                layui.show.error("非法输入");



            }



        });



        $(".noChinese").on("blur", function () {



            if (/[\u4e00-\u9fa5]/.test($(this).val())) {



                $(this).val("");



                layui.show.error("非法输入");



            }



        });



        $('.more').on('click', function () {



            $(this).text() == '展开' ? $(this).text('收起') : $(this).text('展开');



            $("#laytable-search").slideToggle();



        });



        var action = '<?php echo htmlentities($action); ?>';



        if (action == 'detail') {



            convertFormToText($, form);



        }



        getBuilding($, form)



        dateRange(laydate, $)



    });



    function idCard(userCard, num) {



        var birthYear = userCard.substr(6, 4);



        var birthMonth = userCard.substr(10, 2);



        var birthDay = userCard.substr(12, 2);



        var birthday = birthYear + '-' + birthMonth + '-' + birthDay;







        //获取出生日期



        if (num == 1) {



            return birthday;



        }



        //获取性别



        if (num == 2) {



            var genderCode = parseInt(userCard.substr(16, 1));



            var gender = (genderCode % 2 === 0) ? '女' : '男';



            return gender;



        }



        //获取年龄



        if (num == 3) {



            // 计算年龄



            return getAge(birthday);



        }



    }



    function getAge(birthday) {



        var today = new Date();



        var birthDate = new Date(birthday);



        var age = today.getFullYear() - birthDate.getFullYear();



        var m = today.getMonth() - birthDate.getMonth();



        if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {



            age--;



        }



        return age;



    }



    function convertFormToText($, form) {



        $(".layui-form-select,.layui-form-switch,.layui-form-checkbox,.layui-upload-drag,.layui-btn-fluid,span,.layui-footer").remove();



        $('input:not(:checkbox), textarea').each(function () {



            var value = $(this).val();



            if ($(this).attr('type') != 'hidden' && $(this).attr('hidden') != "hidden" && !$(this).hasClass('layui-hide') && !$(this).hasClass('layui-image')) {



                if (value == '' && $(this).attr("name") != 'file') {



                    value = '无'



                }



                var label = "<div class=\"layui-form-label\" style='width: auto; text-align:left;'>" + value + "</div>"



                $(this).after(label);



                $(this).remove();



            }



        });



        $('select').each(function () {



            var value = $(this).find("option:selected").text();



            if (value == '请选择') {



                value = '无'



            }



            var label = "<div class=\"layui-form-label\" style='width: auto; text-align:left;'>" + value + "</div>"



            $(this).after(label);



            $(this).remove();



        });



        $('input:checkbox').each(function () {



            var value = $(this).attr('title');



            var skin = $(this).attr("lay-skin");



            if (skin == "switch") {



                value = $(this).is(":checked") ? '是' : '否';



                var label = "<div class=\"layui-form-label\" style='width: auto; text-align:left;'>" + value + "</div>"



                $(this).after(label);



            } else if ($(this).is(":checked")) {



                var label = "<div class=\"layui-form-label\" style='width: auto; text-align:left;'>" + value + "</div>"



                $(this).after(label);



            }



            $(this).remove();



        });



        form.render();



    }



    function search($) {



        if ($("#search").is("*")) {



            $("#search").click();



        }



    }



    function getBuilding($, form) {



        let grid_id = $('#grid').val();



        if (grid_id > 0 && $("#building").is("*") && $("#building").val() == null) {



            $('#building').empty();



            $.ajax({



                url: '<?php echo url("/Ajax/getBuildingList"); ?>',



                dataType: 'json',



                type: 'post',



                data: { grid_id: grid_id },



                success: function (resData) {



                    var bhtml = "<option value=''>请选择</option>";



                    $.each(resData, function (index, value) {



                        bhtml += "<option unit='" + value.unit + "' value='" + value.id + "'>" + value.building_name + "</option>";



                    });



                    $('#building').html(bhtml);



                    form.render('select');



                }



            });



        }



    }



    function dateRange(laydate, $) {



        laydate.render({



            elem: '#start_date',



            type: 'date',



            max: '<?php echo date("Y-m-d"); ?>',



            done: function (value, date, endDate) {



                var startDate = new Date($("#start_date").val());



                var endDate = new Date($("#end_date").val());



                if (startDate >= endDate) {



                    $("#start_date").val("");



                    layui.show.error('结束日期必须大于开始日期');



                }



            }



        });



        laydate.render({



            elem: '#end_date',



            type: 'date',



            done: function (value, date, endDate) {



                var startDate = new Date($("#start_date").val());



                var endDate = new Date($("#end_date").val());



                if (startDate >= endDate) {



                    $("#end_date").val("");



                    layui.show.error('结束日期必须大于开始日期');



                }



            }



        });



    }



</script>
