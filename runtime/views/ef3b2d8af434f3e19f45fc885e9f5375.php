<?php /*a:3:{s:49:"E:\code\jsjsq\app/admin/view/wx_policy\index.html";i:1748247477;s:47:"E:\code\jsjsq\app/admin/view/public\header.html";i:1746518922;s:47:"E:\code\jsjsq\app/admin/view/public\footer.html";i:1746518922;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>SwiftAdmin 后台管理开发框架</title>
    <link href="/favicon.ico" rel="icon">
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link href="/static/system/layui/css/layui.css?v=<?php echo release(); ?>" rel="stylesheet" type="text/css"/>
    <link href="/static/system/css/style.css?v=<?php echo release(); ?>" rel="stylesheet" type="text/css"/>
    <link href="/static/system/layui/css/layphoto.css?v=<?php echo release(); ?>" rel="stylesheet" type="text/css"/>
    <!-- // 加载font-awesome图标 -->
    <link href="/static/system/layui/css/font-awesome.css?v=<?php echo release(); ?>" rel="stylesheet" type="text/css"/>
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>
<script>
    let app_Config = {
        app: "<?php echo htmlentities((isset($app) && ($app !== '')?$app:'/admin')); ?>",
        controller: "<?php echo htmlentities((isset($controller) && ($controller !== '')?$controller:'index')); ?>",
        action: "<?php echo htmlentities((isset($action) && ($action !== '')?$action:'index')); ?>",
        version: "<?php echo config('app.version'); ?>",
        api: "<?php echo config('app.api_url'); ?>",
    };

    let upload_chunkSize = <?php echo saenv('upload_chunk_size'); ?>;
</script>

<body>
<!---->
<div class="layui-fluid">
    <div class="layui-card">
        <!-- // 默认操作按钮 -->
        <div class="layui-card-header layadmin-card-header-auto ">
            <div class="layui-form">
                <!-- // 自定义搜索参数 -->
                <div id="laytable-search" class="layui-form-item" >
                                    <div class="layui-inline">
                    <div class="layui-form-label"><?php echo __('id'); ?></div>
                    <div class="layui-input-inline ">
                    <input name="id" class="layui-input" type="text" placeholder="<?php echo __('id'); ?>"/>
                    </div>
                </div>

                <div class="layui-inline">
                    <div class="layui-form-label"><?php echo __('法律类型id'); ?></div>
                    <div class="layui-input-inline ">
                    <input name="policytype_id" class="layui-input" type="text" placeholder="<?php echo __('法律类型id'); ?>"/>
                    </div>
                </div>

                <div class="layui-inline">
                    <div class="layui-form-label"><?php echo __('内容'); ?></div>
                    <div class="layui-input-inline ">
                    <input name="policy_text" class="layui-input" type="text" placeholder="<?php echo __('内容'); ?>"/>
                    </div>
                </div>

                <div class="layui-inline">
                    <div class="layui-form-label"><?php echo __('创建时间'); ?></div>
                    <div class="layui-input-inline ">
                    <input name="create_time" lay-datetime data-range="true" data-type="date" data-dateformat="yyyy/MM/dd" class="layui-input" type="text" placeholder="<?php echo __('创建时间'); ?>"/>
                    </div>
                </div>

                <div class="layui-inline">
                    <div class="layui-form-label"><?php echo __('创建人'); ?></div>
                    <div class="layui-input-inline ">
                    <input name="create_by" class="layui-input" type="text" placeholder="<?php echo __('创建人'); ?>"/>
                    </div>
                </div>

                <div class="layui-inline">
                    <div class="layui-form-label"><?php echo __('修改时间'); ?></div>
                    <div class="layui-input-inline ">
                    <input name="update_time" lay-datetime data-range="true" data-type="date" data-dateformat="yyyy/MM/dd" class="layui-input" type="text" placeholder="<?php echo __('修改时间'); ?>"/>
                    </div>
                </div>

                <div class="layui-inline">
                    <div class="layui-form-label"><?php echo __('修改人'); ?></div>
                    <div class="layui-input-inline ">
                    <input name="update_by" class="layui-input" type="text" placeholder="<?php echo __('修改人'); ?>"/>
                    </div>
                </div>

                <div class="layui-inline">
                    <div class="layui-form-label"><?php echo __('简介'); ?></div>
                    <div class="layui-input-inline ">
                    <input name="introduction" class="layui-input" type="text" placeholder="<?php echo __('简介'); ?>"/>
                    </div>
                </div>

                <div class="layui-inline">
                    <div class="layui-form-label"><?php echo __('封面'); ?></div>
                    <div class="layui-input-inline ">
                    <input name="image" class="layui-input" type="text" placeholder="<?php echo __('封面'); ?>"/>
                    </div>
                </div>
                </div>
                <div class="layui-form-item">
                    <!-- <div class="layui-inline">
                        <select name="status">
                            <option value=""><?php echo __('按状态查询'); ?></option>
                            <option value="2" ><?php echo __('正常'); ?></option>                                 
                            <option value="1" ><?php echo __('关闭'); ?></option>      
                        </select>
                    </div> -->

                <div class="layui-inline">
                    <div class="layui-form-label"><?php echo __('名称'); ?></div>
                    <div class="layui-input-inline ">
                    <input name="policy_name" class="layui-input" type="text" placeholder="<?php echo __('名称'); ?>"/>
                    </div>
                </div>
                    <div class="layui-inline" >
                        <!-- // 默认搜索 -->
                        <button class="layui-btn icon-btn" lay-filter="formSearch" lay-submit><i class="layui-icon layui-icon-search"></i><?php echo __('搜索'); ?></button>
                        <!--formBegin-->
                        <button class="layui-btn icon-btn" lay-open="" data-title="<?php echo __('添加'); ?>" data-area="1100px,750px" data-maxmin="true" data-url="<?php echo url('/Wxpolicy/add'); ?>" >
                            <i class="layui-icon layui-icon-add-1"></i><?php echo __('添加'); ?>
                        </button>
                        <!--formEnd-->
                    </div>
                </div>
            </div>   
        </div>
        <!-- // 创建数据实例 -->
        <table id="lay-tableList" lay-filter="lay-tableList"></table>        
    </div>
</div>

<!-- // 列表状态栏 -->
<script type="text/html" id="columnStatus">
    <input type="checkbox" lay-filter="switchStatus" data-url="<?php echo url('/Wxpolicy/status'); ?>" value="{{d.id}}" lay-skin="switch" {{d.status==1?'checked':''}}  />
</script>
 
<!-- // 列表工具栏 -->
<script type="text/html" id="tableBar">
    <!--formBegin-->
    <a class="layui-table-text" data-title="<?php echo __('编辑'); ?>" data-area="1100px,750px" data-maxmin="true"
        data-url="<?php echo url('/Wxpolicy/edit'); ?>?id={{d.id}}" lay-event="edit" ><?php echo __('编辑'); ?></a>
    <div class="layui-divider layui-divider-vertical"></div>
    <!--formEnd-->
    <a class="layui-table-text"  data-url="<?php echo url('/Wxpolicy/del'); ?>?id={{d.id}}" lay-event="del" ><?php echo __('删除'); ?></a>
</script>



<script type="text/html" id="tableButton"></script>

<!--// 加载CommonJS-->
<script src="/static/system/layui/layui.js?v=<?php echo release(); ?>"></script>
<script src="/static/system/js/common.js?v=<?php echo release(); ?>"></script>
<script src="/static/system/js/cascadata.js?v=<?php echo release(); ?>"></script>
<script>
    layui.use(['admin','table'], function () {

        var admin = layui.admin;
        var table = layui.table;

        /*
         * 初始化表格
        */
        var isTable = table.render({
            elem: "#lay-tableList"
            ,url: "<?php echo url('/Wxpolicy/index'); ?>"
            ,toolbar: '#tableButton'
            ,defaultToolbar: ['filter', 'exports', 'print','search']
            ,cellMinWidth: 60
            ,page: true
            ,limit: 18
            ,cols: [[
                {type: 'checkbox', width: 50},
                {field:'policytype_id',title:'<?php echo __("栏目"); ?>'},
                {field:'policy_name',title:'<?php echo __("名称"); ?>'},
                {field:'introduction',title:'<?php echo __("简介"); ?>'},
                {field:'policy_text',title:'<?php echo __("内容"); ?>', templet: function (d) {
                    return d.policy_text;
                }},
                {
                    field: 'image', templet: function (d) {
                        if(d.image==''){
                            return '';
                        }
                        var album = [];
                        try {
                            d.image = JSON.parse(d.image);
                            for (var i in d.image) {
                                album[i] = '<a href="javascript:" class="fileslink" ><img class="filesuffix" lay-image-click src="' + d.image[i].src + '"></a>'
                            }
                            return album.join('');
                        } catch (e) {
                            return '<a href="javascript:" class="fileslink" ><img class="filesuffix" lay-image-click src="' + d.image + '"></a>'
                        }
                    }, title: '<?php echo __("封面"); ?>'
                },
                {field:'status',templet: '#columnStatus',title:'<?php echo __("是否展示"); ?>'},
                {field: 'create_time',width: 160,title:'<?php echo __("创建时间"); ?>'},
                {field: 'create_by',width: 120,title:'<?php echo __("创建人"); ?>'},
                {field: 'update_time',width: 160,title:'<?php echo __("修改时间"); ?>'},
                {field: 'update_by',width: 120,title:'<?php echo __("修改人"); ?>'},

                {align: 'center', toolbar: '#tableBar', width:160, fixed: 'right', title: '<?php echo __("操作"); ?>'},
            ]]
        })

    })
</script>
