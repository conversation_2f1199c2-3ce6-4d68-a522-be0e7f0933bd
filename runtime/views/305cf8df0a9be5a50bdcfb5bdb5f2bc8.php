<?php /*a:4:{s:48:"E:\code\jsjsq\app/admin/view/wx_lawfirm\add.html";i:1746518922;s:47:"E:\code\jsjsq\app/admin/view/public\header.html";i:1746518922;s:47:"E:\code\jsjsq\app/admin/view/public\static.html";i:1747036715;s:47:"E:\code\jsjsq\app/admin/view/public\footer.html";i:1746518922;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>SwiftAdmin 后台管理开发框架</title>
    <link href="/favicon.ico" rel="icon">
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link href="/static/system/layui/css/layui.css?v=<?php echo release(); ?>" rel="stylesheet" type="text/css"/>
    <link href="/static/system/css/style.css?v=<?php echo release(); ?>" rel="stylesheet" type="text/css"/>
    <link href="/static/system/layui/css/layphoto.css?v=<?php echo release(); ?>" rel="stylesheet" type="text/css"/>
    <!-- // 加载font-awesome图标 -->
    <link href="/static/system/layui/css/font-awesome.css?v=<?php echo release(); ?>" rel="stylesheet" type="text/css"/>
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>
<script>
    let app_Config = {
        app: "<?php echo htmlentities((isset($app) && ($app !== '')?$app:'/admin')); ?>",
        controller: "<?php echo htmlentities((isset($controller) && ($controller !== '')?$controller:'index')); ?>",
        action: "<?php echo htmlentities((isset($action) && ($action !== '')?$action:'index')); ?>",
        version: "<?php echo config('app.version'); ?>",
        api: "<?php echo config('app.api_url'); ?>",
    };

    let upload_chunkSize = <?php echo saenv('upload_chunk_size'); ?>;
</script>

<body>
<!-- // 重定位style -->
<!---->
<link href="/static/system/css/content.css" rel="stylesheet" type="text/css" />
<div class="layui-fluid">
    <form class="layui-form layui-card" >

        <div class="layui-card-body">
        <?php if($data['id'] > '0'): ?>
            <input type="text" name="id" value="<?php echo htmlentities($data['id']); ?>" hidden="">
            <?php else: ?>
            <input type="text" name="id" value="" hidden="">
        <?php endif; ?>
        <div class="layui-form-item" >
<label class="layui-form-label"><font color="red">* </font>名称</label>
<div class="layui-input-block"><input class="layui-input"  name="Lawfirm_name" placeholder="请输入" required="1" lay-verify=""value="<?php echo htmlentities($data['Lawfirm_name']); ?>" ></div>
</div>

<div class="layui-form-item" >
<label class="layui-form-label"><font color="red">* </font>地址</label>
<div class="layui-input-block"><input class="layui-input"  name="address" placeholder="请输入" required="1" lay-verify=""value="<?php echo htmlentities($data['address']); ?>" ></div>
</div>

<div class="layui-form-item" >
<label class="layui-form-label"><font color="red">* </font>联系电话</label>
<div class="layui-input-block"><input class="layui-input"  name="mobile" placeholder="请输入" required="1" lay-verify=""value="<?php echo htmlentities($data['mobile']); ?>" ></div>
</div>

<div class="layui-form-item" >
<label class="layui-form-label"><font color="red">* </font>微信</label>
<div class="layui-input-block"><input class="layui-input"  name="wechat" placeholder="请输入" required="1" lay-verify=""value="<?php echo htmlentities($data['wechat']); ?>" ></div>
</div>

<div class="layui-form-item" >
<label class="layui-form-label"><font color="red">* </font>简介</label>
<div class="layui-input-block"><textarea class="layui-textarea" name="introduction" placeholder="请输入" required="1" ><?php echo htmlentities($data['introduction']); ?></textarea></div>
</div>

<div class="layui-form-item" >
<label class="layui-form-label"><font color="red">* </font>封面</label>
<div class="layui-input-block">
            <div class="layui-imagesbox">
                <?php 
                    if (strpos($data['image'], 'http') === 0) {
                    $data['image'] = [['src'=>$data['image'],'title'=>'图片']];
                    }else{
                    $data['image'] = json_decode($data['image'],true);
                    }
                 ?>
                <!-- // 循环输出代码 -->
                <?php if(!(empty($data['image']) || (($data['image'] instanceof \think\Collection || $data['image'] instanceof \think\Paginator ) && $data['image']->isEmpty()))): if(is_array($data['image']) || $data['image'] instanceof \think\Collection || $data['image'] instanceof \think\Paginator): $i = 0; $__LIST__ = $data['image'];if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$vo): $mod = ($i % 2 );++$i;?>
                    <div class="layui-input-inline layui-uplpad-image">
                        <img src="<?php echo htmlentities($vo['src']); ?>" lay-image-hover >
                        <input type="text" name="image[<?php echo htmlentities($key); ?>][src]" class="layui-hide" value="<?php echo htmlentities($vo['src']); ?>" >
                        <input type="text" name="image[<?php echo htmlentities($key); ?>][title]" class="layui-input" value="<?php echo htmlentities($vo['title']); ?>" placeholder="图片简介">
                        <span class="layui-badge layui-badge-red" data-name="image" onclick="layui.admin.resetInput(this,'images');">删除</span>
                    </div>
                <?php endforeach; endif; else: echo "" ;endif; ?>
                <?php endif; ?>
                <div class="layui-input-inline layui-uplpad-image">
                    <div class="layui-upload-drag" lay-upload="image" data-type="multiple" data-accept="file" data-size="102400">
                        <i class="layui-icon layui-icon-upload"></i>
                        <p>点击上传，或将文件拖拽到此处</p>
                        <div class="layui-hide"></div>
                    </div>
                    
                </div>
            </div></div>
</div>

<div class="layui-form-item" >
<label class="layui-form-label">是否展示</label>
<div class="layui-input-block">    <input  type="hidden" type="checkbox" name="status" value="0" />
    <input type="checkbox" name="status" value="1" <?php if($data['status'] == '1'): ?> checked <?php endif; ?> lay-skin="switch" /></div>
</div>

        <div class="layui-footer layui-form-footer">
            <button class="layui-btn layui-btn-primary" type="button" sa-event="closeDialog"><?php echo __('取消'); ?></button>
            <button class="layui-btn" type="button" lay-filter="submitIframe" lay-submit><?php echo __('提交'); ?></button>
        </div>
    </form>
</div>
<!--// 加载Markdown编辑器-->
<link href="/static/js/markdown/cherry-markdown.css?v=<?php echo release(); ?>" rel="stylesheet" type="text/css"/>
<script src="/static/js/markdown/cherry-markdown.core.js?v=<?php echo release(); ?>"></script>

<!-- // 全局加载第三方JS -->
<script src="/static/js/tinymce/tinymce.min.js?v=<?php echo release(); ?>"></script>
<script src="/static/system/module/xmSelect/xmSelect.js?v=<?php echo release(); ?>"></script>
<!--// 加载CommonJS-->
<script src="/static/system/layui/layui.js?v=<?php echo release(); ?>"></script>
<script src="/static/system/js/common.js?v=<?php echo release(); ?>"></script>
<script src="/static/system/js/cascadata.js?v=<?php echo release(); ?>"></script>