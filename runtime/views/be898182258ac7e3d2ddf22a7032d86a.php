<?php /*a:3:{s:51:"E:\code\php\jsjsq\app/admin/view/index\console.html";i:1751179316;s:51:"E:\code\php\jsjsq\app/admin/view/public\header.html";i:1751179315;s:51:"E:\code\php\jsjsq\app/admin/view/public\footer.html";i:1751179315;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>SwiftAdmin 后台管理开发框架</title>
    <link href="/favicon.ico" rel="icon">
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link href="/static/system/layui/css/layui.css?v=<?php echo release(); ?>" rel="stylesheet" type="text/css"/>
    <link href="/static/system/css/style.css?v=<?php echo release(); ?>" rel="stylesheet" type="text/css"/>
    <link href="/static/system/layui/css/layphoto.css?v=<?php echo release(); ?>" rel="stylesheet" type="text/css"/>
    <!-- // 加载font-awesome图标 -->
    <link href="/static/system/layui/css/font-awesome.css?v=<?php echo release(); ?>" rel="stylesheet" type="text/css"/>
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>
<script>
    let app_Config = {
        app: "<?php echo htmlentities((isset($app) && ($app !== '')?$app:'/admin')); ?>",
        controller: "<?php echo htmlentities((isset($controller) && ($controller !== '')?$controller:'index')); ?>",
        action: "<?php echo htmlentities((isset($action) && ($action !== '')?$action:'index')); ?>",
        version: "<?php echo config('app.version'); ?>",
        api: "<?php echo config('app.api_url'); ?>",
    };

    let upload_chunkSize = <?php echo saenv('upload_chunk_size'); ?>;
</script>

<body>
<style>
  
    .layui-form-item {
        margin-bottom: 10px !important;
    }

    .more {
        display: none;
        transition: max-height 0.3s ease-out;
    }

    .right-bottom-panel {
        position: fixed;
        width: 200px;
        right: 2px;
        bottom: 20px;
        border-radius: 2px;
        background: #c1bcbc;
        opacity: 0.6;
    }
    
    .right-bottom-panel:hover{
        opacity: 0.9;
    }

    .right-bottom-panel span {
        display: block;
        line-height: 40px;
        height: 40px;
        padding-left: 10px;
        border-bottom: white 1px solid;
    }

    .layui-carousel img {
        width: auto;
        height: 100%;
    }

    div[carousel-item] {
        text-align: center;
    }

    .layuiadmin-card-header-auto .layui-form-item {
        margin-bottom: 0 !important;
    }
    
  .layui-row{
    position: fixed;
    width:700px;
    right: 20px;
    bottom: 20px;
    opacity: 0.5;}
    .layui-row:hover{
    opacity: 0.9;}
    
  .console-link-block {
    font-size: 16px;
    padding: 20px 20px;
    border-radius: 4px;
    background-color: #40D4B0;
    color: #FFFFFF !important;
    box-shadow: 0 2px 3px rgba(0, 0, 0, .05);
    position: relative;
    overflow: hidden;
    display: block;
  }

  .console-link-block .console-link-block-num {
    font-size: 40px;
    margin-bottom: 5px;
    opacity: .9;
  }

  .console-link-block .console-link-block-text {
    opacity: .8;
  }

  .console-link-block .console-link-block-icon {
    position: absolute;
    top: 50%;
    right: 20px;
    width: 50px;
    height: 50px;
    font-size: 50px;
    line-height: 50px;
    margin-top: -25px;
    color: #FFFFFF;
    opacity: .8;
  }

  .console-link-block .console-link-block-band {
    color: #fff;
    width: 100px;
    font-size: 12px;
    padding: 2px 0 3px 0;
    background-color: #E32A16;
    line-height: inherit;
    text-align: center;
    position: absolute;
    top: 8px;
    right: -30px;
    transform-origin: center;
    transform: rotate(45deg) scale(.8);
    opacity: .95;
    z-index: 2;
  }

  /** //统计快捷方式样式 */

  /** 设置每个快捷块的颜色 */
  .layui-col-space15 > div:nth-child(2) .console-link-block {
    background-color: #55A5EA;
  }

  .layui-col-space15 > div:nth-child(3) .console-link-block {
    background-color: #9DAFFF;
  }

  .layui-col-space15 > div:nth-child(4) .console-link-block {
    background-color: #F591A2;
  }

  .layui-col-space15 > div:nth-child(5) .console-link-block {
    background-color: #FEAA4F;
  }

  .layui-col-space15 > div:last-child .console-link-block {
    background-color: #9BC539;
  }
  
  div[carousel-item]{text-align:left;}
.layui-carousel-arrow { position:fixed}
</style>
<div class="image layui-carousel" id="carousel">
    <div carousel-item>
  <?php if(is_array($list) || $list instanceof \think\Collection || $list instanceof \think\Paginator): if( count($list)==0 ) : echo "" ;else: foreach($list as $key=>$item): 
          $person = $item['person'];
          $total = $item['total'];
          $image = $item['image'];
          if (strpos($image, 'http') === 0) {
          $image = [['src'=>$image,'title'=>'图片']];
          }else{
          $image = json_decode($image,true);
          }
       ?>
      <div>
          <div src="<?php echo htmlentities($image[0]['src']); ?>" lay-image-click style="position: fixed;width:100%;height:100vh; background-image: url('<?php echo htmlentities($image[0]['src']); ?>');
    background-repeat: no-repeat;
    background-position: center center;
    background-size: contain;">
          </div>
          <div class="right-bottom-panel">
              <?php if($person['community_name'] != '街道本级'): if(!(empty($person['community_name']) || (($person['community_name'] instanceof \think\Collection || $person['community_name'] instanceof \think\Paginator ) && $person['community_name']->isEmpty()))): ?>
                  <span><?php echo empty($person['grid_group_name'])?'名称':'所属社区'; ?>:<?php echo htmlentities($person['community_name']); ?></span>
                  <?php if(!(empty($person['grid_group_name']) || (($person['grid_group_name'] instanceof \think\Collection || $person['grid_group_name'] instanceof \think\Paginator ) && $person['grid_group_name']->isEmpty()))): ?><span>所属网格组:<?php echo htmlentities($person['grid_group_name']); ?></span><?php endif; if(!(empty($person['grid_name']) || (($person['grid_name'] instanceof \think\Collection || $person['grid_name'] instanceof \think\Paginator ) && $person['grid_name']->isEmpty()))): ?><span>所属网格:<?php echo htmlentities($person['grid_name']); ?></span><?php endif; ?>
                  <span>负责人:<?php echo htmlentities($person['name']); ?></span>
                  <span>联系方式:<?php echo htmlentities($person['phone']); ?></span>
                <?php endif; else: ?>
                  <span>名称:局直街道办</span>
              <?php endif; ?>
            <span sa-event="tabs" data-url="<?php echo url('/building/index'); ?>" data-title="实有建筑管理">实有建筑量:<?php echo htmlentities($total['building']); ?></span>
            <span sa-event="tabs" data-url="<?php echo url('/house/index'); ?>" data-title="实有房屋管理">实有房屋数量:<?php echo htmlentities($total['house']); ?></span>
            <span sa-event="tabs" data-url="<?php echo url('/person/index'); ?>" data-title="实有人口管理">实有人口数量:<?php echo htmlentities($total['person']); ?></span>
            <span sa-event="tabs" data-url="<?php echo url('/placeGovernment/index'); ?>" data-title="机关及事业单位">机关及事业单位:<?php echo htmlentities($total['government']); ?></span>
            <span sa-event="tabs" data-url="<?php echo url('/placeEnterprise/index'); ?>" data-title="企业">企业数量:<?php echo htmlentities($total['enterprise']); ?></span>
            <span sa-event="tabs" data-url="<?php echo url('/placeMerchant/index'); ?>" data-title="个体工商户">个体工商户数量:<?php echo htmlentities($total['merchant']); ?></span>
        </div>
        </div>
     
  <?php endforeach; endif; else: echo "" ;endif; ?>
  </div>
</div>

<!--// 加载CommonJS-->
<script src="/static/system/layui/layui.js?v=<?php echo release(); ?>"></script>
<script src="/static/system/js/common.js?v=<?php echo release(); ?>"></script>
<script src="/static/system/js/cascadata.js?v=<?php echo release(); ?>"></script>
<script>
  layui.use(['admin','carousel','layphoto'], function(){
    var carousel = layui.carousel;
    // 初始化图片轮播
    carousel.render({
      elem: '#carousel', // 绑定的元素
      width: '100%', // 宽度
      height: '100%', // 高度
      arrow: 'always', // 始终显示箭头
      interval: 5000 //设置轮播时间间隔
    });
  });
</script>