<?php /*a:4:{s:49:"E:\code\jsjsq\app/admin/view/grid\grid_group.html";i:1746518922;s:47:"E:\code\jsjsq\app/admin/view/public\header.html";i:1746518922;s:47:"E:\code\jsjsq\app/admin/view/public\footer.html";i:1746518922;s:47:"E:\code\jsjsq\app/admin/view/public\select.html";i:1747648922;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>SwiftAdmin 后台管理开发框架</title>
    <link href="/favicon.ico" rel="icon">
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link href="/static/system/layui/css/layui.css?v=<?php echo release(); ?>" rel="stylesheet" type="text/css"/>
    <link href="/static/system/css/style.css?v=<?php echo release(); ?>" rel="stylesheet" type="text/css"/>
    <link href="/static/system/layui/css/layphoto.css?v=<?php echo release(); ?>" rel="stylesheet" type="text/css"/>
    <!-- // 加载font-awesome图标 -->
    <link href="/static/system/layui/css/font-awesome.css?v=<?php echo release(); ?>" rel="stylesheet" type="text/css"/>
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>
<script>
    let app_Config = {
        app: "<?php echo htmlentities((isset($app) && ($app !== '')?$app:'/admin')); ?>",
        controller: "<?php echo htmlentities((isset($controller) && ($controller !== '')?$controller:'index')); ?>",
        action: "<?php echo htmlentities((isset($action) && ($action !== '')?$action:'index')); ?>",
        version: "<?php echo config('app.version'); ?>",
        api: "<?php echo config('app.api_url'); ?>",
    };

    let upload_chunkSize = <?php echo saenv('upload_chunk_size'); ?>;
</script>

<body>

<?php 
   $community_LIST = getCommunity();
 ?>
<div class="layui-fluid">
   <div class="layui-card">
      <!-- // 默认操作按钮 -->
      <div class="layui-card-header layadmin-card-header-auto ">
         <div class="layui-form">
            <div class="layui-form-item">
               <div class="layui-inline">
                  <div class="layui-form-label short-label"><?php echo __('社区'); ?></div>
                  <div class="layui-input-inline ">
                     <select name="community_id" lay-search lay-filter="communityChange">
                        <option value="">请选择</option>
                        <?php if(is_array($community_LIST) || $community_LIST instanceof \think\Collection || $community_LIST instanceof \think\Paginator): if( count($community_LIST)==0 ) : echo "" ;else: foreach($community_LIST as $key=>$item): ?>
                           <option value="<?php echo htmlentities($item['id']); ?>"><?php echo htmlentities($item['community_name']); ?></option>
                        <?php endforeach; endif; else: echo "" ;endif; ?>
                     </select>
                  </div>
               </div>
               <div class="layui-inline">
                  <div class="layui-input-inline ">
                     <input name="grid_name" class="layui-input" type="text" placeholder="<?php echo __('按网格组名称查询'); ?>"/>
                  </div>
               </div>
               <div class="layui-inline" >
                  <!-- // 默认搜索 -->
                  <button id="search" class="layui-btn icon-btn" lay-filter="formSearch" lay-submit><i class="layui-icon layui-icon-search"></i><?php echo __('搜索'); ?></button>
                  <!--formBegin-->
                  <button class="layui-btn icon-btn" lay-open="" data-title="<?php echo __('添加'); ?>" data-area="100%,100%" data-maxmin="true" data-url="<?php echo url('/Grid/add'); ?>" >
                     <i class="layui-icon layui-icon-add-1"></i><?php echo __('添加'); ?>
                  </button>
                  <!--formEnd-->
               </div>
            </div>
         </div>
      </div>
      <!-- // 创建数据实例 -->
      <table id="lay-tableList" lay-filter="lay-tableList"></table>
   </div>
</div>

<!-- // 列表工具栏 -->
<script type="text/html" id="tableBar">
   <a class="layui-table-text" data-title="<?php echo __('查看'); ?>" data-area="100%,100%" data-maxmin="true"
      data-url="<?php echo url('/'.$controller.'/detail'); ?>?id={{d.id}}" lay-event="edit" ><?php echo __('查看'); ?></a>
   <div class="layui-divider layui-divider-vertical"></div>
   <a class="layui-table-text" data-title="<?php echo __('街路配置'); ?>" data-area="1100px,750px" data-maxmin="true"
      data-url="<?php echo url('/CommunityStreet/index'); ?>?community_id={{d.community_id}}&grid_group_id={{d.id}}&grid_id=0" lay-event="edit" ><?php echo __('街路配置'); ?></a>
   <div class="layui-divider layui-divider-vertical"></div>
    <a class="layui-table-text" data-title="<?php echo __('编辑'); ?>" data-area="100%,100%" data-maxmin="true"
      data-url="<?php echo url('/Grid/edit'); ?>?id={{d.id}}" lay-event="edit" ><?php echo __('编辑'); ?></a>
   <div class="layui-divider layui-divider-vertical"></div>
   <!--formEnd-->
   <?php if((isSuper())): ?>
   <a class="layui-table-text"  data-url="<?php echo url('/Grid/del'); ?>?id={{d.id}}" lay-event="del" ><?php echo __('删除'); ?></a>
   <?php endif; ?>
</script>





<!--// 加载CommonJS-->
<script src="/static/system/layui/layui.js?v=<?php echo release(); ?>"></script>
<script src="/static/system/js/common.js?v=<?php echo release(); ?>"></script>
<script src="/static/system/js/cascadata.js?v=<?php echo release(); ?>"></script>
<?php if($action == 'detail'): ?>



    <style>
        .layui-form-item {
            margin-bottom: 5px;
            margin-right: 10px;
        }



        .layui-form-item label {
            min-height: 24px;
        }



        .layui-form-item label,
        .layui-form-item .layui-input-block {
            border: 1px solid #e3e3e3;
            border-radius: 2px;
            background: #fafafa;
            display: flex;
        }



        .layui-form-item .layui-input-block {
            margin-left: 111px;
        }



        #renew {
            display: none;
        }



        .layui-form-mid {
            position: relative;
            left: -25px;
        }
    </style>



<?php endif; ?>



<script>



    function jsonToGetString(json) {



        let params = [];



        for (let key in json) {



            if (json.hasOwnProperty(key) && json[key] !== null && json[key] !== undefined && json[key] !== '') {



                params.push(encodeURIComponent(key) + '=' + encodeURIComponent(json[key]));



            }



        }



        return params.join('&');



    }



    layui.use(['layer', 'form', 'layer', 'table', 'admin', 'laydate'], function () {



        var form = layui.form;



        var $ = layui.jquery;



        var layer = layui.layer;



        var table = layui.table;



        var admin = layui.admin;



        var laydate = layui.laydate;



        setInterval(function () {



            admin.imageClick();



        }, 1000)







        form.on('select(xiafaChange)', function (data) {



            $('#xfcommunity_id').empty();



            $('#xfgrid_group').empty();



            $('#xfgrid').empty();



            search($)



            $.ajax({



                url: '<?php echo url("/Ajax/getCommunityList"); ?>',



                dataType: 'json',



                type: 'post',



                // data:{community_id:community_id},



                success: function (resData) {



                    $('#xfcommunity_id').append(new Option("请选择", ""));// 下拉菜单里添加元素



                    $.each(resData, function (index, value) {



                        $('#xfcommunity_id').append(new Option(value.community_name, value.id));// 下拉菜单里添加元素



                    });



                    form.render('select');



                }



            });



            // form.render('select');



        });







        form.on('select(ssptypeChange)', function (data) {







            $('#xiafa').empty();



            search($)



            var type = data.value; // roomName 选中的省份名称



            $.ajax({



                url: '<?php echo url("/wxssp/ssptype"); ?>',



                dataType: 'json',



                type: 'post',



                data: { type: type },



                success: function (resData) {



                    console.log(resData);



                    $('#xiafa').append(new Option("请选择", ""));// 下拉菜单里添加元素



                    $.each(resData, function (index, value) {



                        $('#xiafa').append(new Option(value.name, value.id));// 下拉菜单里添加元素



                    });



                    form.render('select');



                }



            });



        });



        form.on('select(xfcommunityChange)', function (data) {



            $('#xfgrid_group').empty();



            $('#xfgrid').empty();



            $('#xiafa').empty();



            if ($("#street").is("*")) {



                $('#street').empty();



                $('#road').empty();



            }



            if ($("#building").is("*")) {



                $('#building').empty();



            }



            if ($("#house").is("*")) {



                $('#house').empty();



            }



            if ($("#unit").is("*")) {



                $('#unit').empty();



            }



            search($)



            var community_id = data.value; // roomName 选中的省份名称



            console.log(data.value)



            $.ajax({



                url: '<?php echo url("/Ajax/getGridGroupList"); ?>',



                dataType: 'json',



                type: 'post',



                data: { community_id: community_id },



                success: function (resData) {



                    $('#xfgrid_group').append(new Option("请选择", ""));// 下拉菜单里添加元素



                    $.each(resData, function (index, value) {



                        $('#xfgrid_group').append(new Option(value.grid_name, value.id));// 下拉菜单里添加元素



                    });



                    form.render('select');



                }



            });



        });



        form.on('select(xfgridGroupChange)', function (data) {



            $('#xfgrid').empty();



            if ($("#street").is("*")) {



                $('#street').empty();



                $('#road').empty();



            }



            if ($("#building").is("*")) {



                $('#building').empty();



            }



            if ($("#house").is("*")) {



                $('#house').empty();



            }



            if ($("#unit").is("*")) {



                $('#unit').empty();



            }



            search($)



            var grid_id = data.value; // roomName 选中的省份名称



            $.ajax({



                url: '<?php echo url("/Ajax/getGridList"); ?>',



                dataType: 'json',



                type: 'post',



                data: { grid_id: grid_id },



                success: function (resData) {



                    $('#xfgrid').append(new Option("请选择", ""));// 下拉菜单里添加元素



                    $.each(resData, function (index, value) {



                        $('#xfgrid').append(new Option(value.grid_name, value.id));// 下拉菜单里添加元素



                    });



                    form.render('select');



                }



            });



        });



        form.on('select(xfgridChange)', function (data) {



            search($)



            var grid_id = data.value; // roomName 选中的省份名称



            form.render('select');



            if ($("#street").is("*")) {



                $('#street').empty();



                $('#road').empty();



                $.ajax({



                    url: '<?php echo url("/Ajax/getGridStreetList"); ?>',



                    dataType: 'json',



                    type: 'post',



                    data: { grid_id: grid_id },



                    success: function (resData) {



                        var shtml = "<option value=''>请选择</option>";



                        var rhtml = "<option value=''>请选择</option>";







                        $.each(resData, function (index, value) {



                            var typeNo = value.typeNo === 1 ? '单号' : '双号';



                            if (value.type == 2) {



                                shtml += "<option min='" + value.start + "' max='" + value.end + "' value='" + value.id + "'>" + value.name + "(" + typeNo + ")(" + value.start + "-" + value.end + ")</option>";



                            } else {



                                rhtml += "<option min='" + value.start + "' max='" + value.end + "' value='" + value.id + "'>" + value.name + "(" + typeNo + ")(" + value.start + "-" + value.end + ")</option>";



                            }



                        });



                        $('#street').html(shtml);



                        $('#road').html(rhtml);



                        form.render('select');



                    }



                });



            }



            if ($("#building").is("*")) {



                $('#building').empty();



                $.ajax({



                    url: '<?php echo url("/Ajax/getBuildingList"); ?>',



                    dataType: 'json',



                    type: 'post',



                    data: { grid_id: grid_id },



                    success: function (resData) {



                        var bhtml = "<option value=''>请选择</option>";



                        $.each(resData, function (index, value) {



                            bhtml += "<option unit='" + value.unit + "' value='" + value.id + "'>" + value.building_name + "</option>";



                        });



                        $('#building').html(bhtml);



                        form.render('select');



                    }



                });



            }



            if ($("#house").is("*")) {



                $('#house').empty();



            }



            if ($("#unit").is("*")) {



                $('#unit').empty();



            }







        });



        form.on('select(communityChange)', function (data) {



            $('#grid_group').empty();



            $('#grid').empty();



            if ($("#street").is("*")) {



                $('#street').empty();



                $('#road').empty();



            }



            if ($("#building").is("*")) {



                $('#building').empty();



            }



            if ($("#house").is("*")) {



                $('#house').empty();



            }



            if ($("#unit").is("*")) {



                $('#unit').empty();



            }



            search($)



            var community_id = data.value; // roomName 选中的省份名称



            console.log(data.value)



            $.ajax({



                url: '<?php echo url("/Ajax/getGridGroupList"); ?>',



                dataType: 'json',



                type: 'post',



                data: { community_id: community_id },



                success: function (resData) {



                    $('#grid_group').append(new Option("请选择", ""));// 下拉菜单里添加元素



                    $.each(resData, function (index, value) {



                        $('#grid_group').append(new Option(value.grid_name, value.id));// 下拉菜单里添加元素



                    });



                    form.render('select');



                }



            });



        });



        form.on('select(gridGroupChange)', function (data) {



            $('#grid').empty();



            if ($("#street").is("*")) {



                $('#street').empty();



                $('#road').empty();



            }



            if ($("#building").is("*")) {



                $('#building').empty();



            }



            if ($("#house").is("*")) {



                $('#house').empty();



            }



            if ($("#unit").is("*")) {



                $('#unit').empty();



            }



            search($)



            var grid_id = data.value; // roomName 选中的省份名称



            $.ajax({



                url: '<?php echo url("/Ajax/getGridList"); ?>',



                dataType: 'json',



                type: 'post',



                data: { grid_id: grid_id },



                success: function (resData) {



                    $('#grid').append(new Option("请选择", ""));// 下拉菜单里添加元素



                    $.each(resData, function (index, value) {



                        $('#grid').append(new Option(value.grid_name, value.id));// 下拉菜单里添加元素



                    });



                    form.render('select');



                }



            });



        });



        form.on('select(gridChange)', function (data) {



            search($)



            var grid_id = data.value; // roomName 选中的省份名称



            form.render('select');



            if ($("#street").is("*")) {



                $('#street').empty();



                $('#road').empty();



                $.ajax({



                    url: '<?php echo url("/Ajax/getGridStreetList"); ?>',



                    dataType: 'json',



                    type: 'post',



                    data: { grid_id: grid_id },



                    success: function (resData) {



                        var shtml = "<option value=''>请选择</option>";



                        var rhtml = "<option value=''>请选择</option>";







                        $.each(resData, function (index, value) {



                            var typeNo = value.typeNo === 1 ? '单号' : '双号';



                            if (value.type == 2) {



                                shtml += "<option min='" + value.start + "' max='" + value.end + "' value='" + value.id + "'>" + value.name + "(" + typeNo + ")(" + value.start + "-" + value.end + ")</option>";



                            } else {



                                rhtml += "<option min='" + value.start + "' max='" + value.end + "' value='" + value.id + "'>" + value.name + "(" + typeNo + ")(" + value.start + "-" + value.end + ")</option>";



                            }



                        });



                        $('#street').html(shtml);



                        $('#road').html(rhtml);



                        form.render('select');



                    }



                });



            }



            if ($("#building").is("*")) {



                $('#building').empty();



                $.ajax({



                    url: '<?php echo url("/Ajax/getBuildingList"); ?>',



                    dataType: 'json',



                    type: 'post',



                    data: { grid_id: grid_id },



                    success: function (resData) {



                        var bhtml = "<option value=''>请选择</option>";



                        $.each(resData, function (index, value) {



                            bhtml += "<option unit='" + value.unit + "' value='" + value.id + "'>" + value.building_name + "</option>";



                        });



                        $('#building').html(bhtml);



                        form.render('select');



                    }



                });



            }



            if ($("#house").is("*")) {



                $('#house').empty();



            }



            if ($("#unit").is("*")) {



                $('#unit').empty();



            }



        });







        form.on('select(streetChange)', function () {



            search($)



            var streetObj = $('#street option:selected');



            var startObj = $("#street_start");



            var endObj = $("#street_end");



            startObj.val("");



            endObj.val("");



            var min = streetObj.attr('min');



            var max = streetObj.attr('max');



            startObj.attr('min', min);



            startObj.attr('max', max);



            endObj.attr('min', min);



            endObj.attr('max', max);



            form.render('input');



        });



        form.on('select(roadChange)', function () {

            search($)

            var roadObj = $('#road option:selected');

            var startObj = $("#road_start");

            var endObj = $("#road_end");

            startObj.val("");

            endObj.val("");

            var min = roadObj.attr('min');

            var max = roadObj.attr('max');

            startObj.attr('min', min);

            startObj.attr('max', max);

            endObj.attr('min', min);

            endObj.attr('max', max);

            
            form.render('input');

        });



        <?php if(($action == "add" || $action == "edit")): ?>

            // 监听input输入事件
            $(".StreetNo").on('blur', function (e) {

                search($);

                var obj = $(this);
                console.log("StreetNo obj>>>>",obj)
                var id = $(this).attr("id");

                var min = parseInt(obj.attr('min'));

                var max = parseInt(obj.attr('max'));

                var value = parseInt(obj.val());

                console.log('road>>>>>',value,min);
                if (value && (value % 2) != (min % 2)) {

                    var typeNmae = min % 2 == 0 ? '双号' : '单号';

                    layer.msg('只能填写' + typeNmae + '！', { icon: 5 });

                    $(this).val("");
                }

                if ((id == "road_start" || id == "road_end") && $("#road_fictitious").is(":checked")) {

                    return false;
                }

                if ((id == "street_start" || id == "street_end") && $("#street_fictitious").is(":checked")) {

                    return false;
                }

                if (value && (value < min || value > max)) {

                    layer.msg('超过起止号范围(' + min + '-' + max + ')！', { icon: 5 });

                    $(this).val("");
                }
            });


        <?php endif; ?>



            form.on('select(buildingChange)', function (data) {



                $('#house').empty();



                search($)



                var building_id = data.value; // roomName 选中的省份名称



                $.ajax({



                    url: '<?php echo url("/Ajax/getHouseList"); ?>',



                    dataType: 'json',



                    type: 'post',



                    data: { building_id: building_id },



                    success: function (resData) {



                        var hhtml = "<option value=''>请选择</option>";



                        $.each(resData, function (index, value) {



                            if (value.floor) {



                                value.code = value.floor + "楼" + value.code;



                            }



                            if (value.unit) {



                                value.code = value.unit + "单元" + value.code;



                            }



                            if (value.street) {



                                value.code = value.code + '(' + value.street + ')';



                            }



                            hhtml += "<option value='" + value.id + "' area='" + value.area + "'>" + value.code + "</option>";



                        });



                        $('#house').html(hhtml);



                        form.render('select');



                    }



                });



                if ($("#unit").is("*")) {



                    $('#unit').empty();



                    var buildingObj = $('#building option:selected');



                    var unit = buildingObj.attr('unit');



                    var uhtml = "<option value=''>请选择</option>";



                    for (var i = 1; i <= unit; i++) {



                        uhtml += "<option value='" + i + "'>" + i + "单元</option>";



                    }



                    $('#unit').html(uhtml);



                    form.render('select');



                }



            });







        form.on('select(houseChange)', function () {



            var houseObj = $('#house option:selected');



            if ($("#area").is("*")) {



                var area = houseObj.attr('area');



                $("#area").val(area);



            }



        });



        form.on("submit(formExport)", function () {



            var formData = [];



            $('.layui-form input:not(:checkbox), .layui-form select').each(function () {



                var name = $(this).attr('name');



                var value = $(this).val();



                if (name && value) {



                    formData.push(name + "=" + value);



                }



            });



            $('.layui-form input:checkbox').each(function () {



                if ($(this).is(":checked")) {



                    var name = $(this).attr('name');



                    formData.push(name + "=1");



                }



            });



            var url = "/admin/<?php echo htmlentities($controller); ?>/dataToExcel";



            const data_url = $(this).attr('data-url');



            if (data_url) {



                url = data_url;



            }



            window.open(url + '?' + formData.join("&"));



            return false;



        })



        table.on('toolbar(lay-tableList)', function (obj) {



            var checkStatus = table.checkStatus(obj.config.id);



            switch (obj.event) {



                case 'Pending':



                    //获取选中的数据



                    var checkData = checkStatus.data;



                    if (checkData.length == 0) {



                        layer.msg('请至少勾选一条数据', { icon: 2 });



                    } else {



                        layer.confirm('确定挂起选中的数据吗？', { icon: 3, title: '提示' }, function (index) {



                            //这里可以发送批量删除请求



                            console.log(checkData);



                            var id = [];



                            checkData.forEach(function (item, index) {



                                id.push(item.id)



                            });



                            console.log(id)



                            $.post('/admin/Pending/add',



                                { id: id }, function (res) {



                                    layer.close(index);



                                    if (res.code === 200) {



                                        layer.msg(res.msg);



                                        search($)



                                    }



                                    else {



                                        layui.show.error(res.msg);



                                    }







                                }, 'json');



                        });



                    }



                    break;



                case 'formExport':



                    var url = $("#formExport").attr('data-url')



                    //获取选中的数据



                    var checkData = checkStatus.data;



                    if (checkData.length == 0) {



                        layer.msg('请至少勾选一条数据', { icon: 2 });



                    } else {



                        layer.confirm('确定导出选中的数据吗？', { icon: 3, title: '提示' }, function (index) {



                            //这里可以发送批量删除请求



                            var id = [];



                            checkData.forEach(function (item, index) {



                                id.push(item.id)



                            });



                            $.post(url,



                                { id: id }, function (res) {



                                    layer.close(index);



                                    if (res.code === 200) {



                                        location.href = res.url;



                                    }



                                    else {



                                        layui.show.error(res.msg);



                                    }







                                }, 'json');



                        });



                    }



                    break;



                case 'delete':



                    //获取选中的数据



                    var checkData = checkStatus.data;



                    var url = $("#delete-btn").attr('data-url')



                    if (checkData.length == 0) {



                        layer.msg('请至少勾选一条数据', { icon: 2 });



                    } else {



                        layer.confirm('确定删除选中的数据吗？', { icon: 3, title: '提示' }, function (index) {



                            var id = [];



                            checkData.forEach(function (item, index) {



                                id.push(item.id)



                            });



                            $.post(url,



                                { id: id }, function (res) {



                                    layer.close(index);



                                    if (res.code === 200) {



                                        layer.msg(res.msg);



                                        search($)



                                    }



                                    else {



                                        layui.show.error(res.msg);



                                    }







                                }, 'json');



                        });



                    }



                    break;



            };



        });



        form.on('select(search)', function (data) {



            search($)



            form.render('select');



        });



        form.on('checkbox(search)', function (data) {



            search($)



            form.render('select');



        });



        $(".noNum").on("blur", function () {



            if (/\d/.test($(this).val())) {



                $(this).val("");



                layui.show.error("非法输入");



            }



        });



        $(".noChinese").on("blur", function () {



            if (/[\u4e00-\u9fa5]/.test($(this).val())) {



                $(this).val("");



                layui.show.error("非法输入");



            }



        });



        $('.more').on('click', function () {



            $(this).text() == '展开' ? $(this).text('收起') : $(this).text('展开');



            $("#laytable-search").slideToggle();



        });



        var action = '<?php echo htmlentities($action); ?>';



        if (action == 'detail') {



            convertFormToText($, form);



        }



        getBuilding($, form)



        dateRange(laydate, $)



    });



    function idCard(userCard, num) {



        var birthYear = userCard.substr(6, 4);



        var birthMonth = userCard.substr(10, 2);



        var birthDay = userCard.substr(12, 2);



        var birthday = birthYear + '-' + birthMonth + '-' + birthDay;







        //获取出生日期



        if (num == 1) {



            return birthday;



        }



        //获取性别



        if (num == 2) {



            var genderCode = parseInt(userCard.substr(16, 1));



            var gender = (genderCode % 2 === 0) ? '女' : '男';



            return gender;



        }



        //获取年龄



        if (num == 3) {



            // 计算年龄



            return getAge(birthday);



        }



    }



    function getAge(birthday) {



        var today = new Date();



        var birthDate = new Date(birthday);



        var age = today.getFullYear() - birthDate.getFullYear();



        var m = today.getMonth() - birthDate.getMonth();



        if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {



            age--;



        }



        return age;



    }



    function convertFormToText($, form) {



        $(".layui-form-select,.layui-form-switch,.layui-form-checkbox,.layui-upload-drag,.layui-btn-fluid,span,.layui-footer").remove();



        $('input:not(:checkbox), textarea').each(function () {



            var value = $(this).val();



            if ($(this).attr('type') != 'hidden' && $(this).attr('hidden') != "hidden" && !$(this).hasClass('layui-hide') && !$(this).hasClass('layui-image')) {



                if (value == '' && $(this).attr("name") != 'file') {



                    value = '无'



                }



                var label = "<div class=\"layui-form-label\" style='width: auto; text-align:left;'>" + value + "</div>"



                $(this).after(label);



                $(this).remove();



            }



        });



        $('select').each(function () {



            var value = $(this).find("option:selected").text();



            if (value == '请选择') {



                value = '无'



            }



            var label = "<div class=\"layui-form-label\" style='width: auto; text-align:left;'>" + value + "</div>"



            $(this).after(label);



            $(this).remove();



        });



        $('input:checkbox').each(function () {



            var value = $(this).attr('title');



            var skin = $(this).attr("lay-skin");



            if (skin == "switch") {



                value = $(this).is(":checked") ? '是' : '否';



                var label = "<div class=\"layui-form-label\" style='width: auto; text-align:left;'>" + value + "</div>"



                $(this).after(label);



            } else if ($(this).is(":checked")) {



                var label = "<div class=\"layui-form-label\" style='width: auto; text-align:left;'>" + value + "</div>"



                $(this).after(label);



            }



            $(this).remove();



        });



        form.render();



    }



    function search($) {



        if ($("#search").is("*")) {



            $("#search").click();



        }



    }



    function getBuilding($, form) {



        let grid_id = $('#grid').val();



        if (grid_id > 0 && $("#building").is("*") && $("#building").val() == null) {



            $('#building').empty();



            $.ajax({



                url: '<?php echo url("/Ajax/getBuildingList"); ?>',



                dataType: 'json',



                type: 'post',



                data: { grid_id: grid_id },



                success: function (resData) {



                    var bhtml = "<option value=''>请选择</option>";



                    $.each(resData, function (index, value) {



                        bhtml += "<option unit='" + value.unit + "' value='" + value.id + "'>" + value.building_name + "</option>";



                    });



                    $('#building').html(bhtml);



                    form.render('select');



                }



            });



        }



    }



    function dateRange(laydate, $) {



        laydate.render({



            elem: '#start_date',



            type: 'date',



            max: '<?php echo date("Y-m-d"); ?>',



            done: function (value, date, endDate) {



                var startDate = new Date($("#start_date").val());



                var endDate = new Date($("#end_date").val());



                if (startDate >= endDate) {



                    $("#start_date").val("");



                    layui.show.error('结束日期必须大于开始日期');



                }



            }



        });



        laydate.render({



            elem: '#end_date',



            type: 'date',



            done: function (value, date, endDate) {



                var startDate = new Date($("#start_date").val());



                var endDate = new Date($("#end_date").val());



                if (startDate >= endDate) {



                    $("#end_date").val("");



                    layui.show.error('结束日期必须大于开始日期');



                }



            }



        });



    }



</script>
<script>
   layui.use(['admin','table'], function () {

      var admin = layui.admin;
      var table = layui.table;

      /*
       * 初始化表格
      */
      var isTable = table.render({
         elem: "#lay-tableList"
         ,url: "<?php echo url('/Grid/gridGroup'); ?>"
         ,toolbar: '#tableButton'
         ,defaultToolbar: ['filter', 'exports', 'print','search']
         ,cellMinWidth: 60
         ,page: true
         ,limit: 50,limits: [50, 100, 200, 500]
         ,height: 'full-150' //设置表格高度，减去固定的底部高度
         ,cols: [[
            {type: 'checkbox', width: 50},
            {field: 'community_name',width:120, title:'<?php echo __("所属社区"); ?>'},
            {field: 'grid_name',width:120,title:'<?php echo __("网格组名称"); ?>'},
            {field:'address',width:200,title:'<?php echo __("网格组范围"); ?>'},
            {field: 'image',width: 120,templet:function(d) {
                  if(d.image==''){
                     return '';
                  }
                  var album = [];
                  try {
                     d.image = JSON.parse(d.image);
                     for (var i in d.image) {
                        album[i] = '<a href="javascript:" class="fileslink" ><img class="filesuffix" lay-image-click src="'+admin.toThumb(d.image[i].src)+'"></a>'
                     }
                     return album.join('');
                  } catch (e) {
                     return '<a href="javascript:" class="fileslink" ><img class="filesuffix" lay-image-click src="'+admin.toThumb(d.image)+'"></a>'
                  }
               },title:'<?php echo __("街景示意图"); ?>'},
            {field:'east',width: 160,title:'<?php echo __("网格组东至"); ?>'},
            {field:'south',width: 160,title:'<?php echo __("网格组南至"); ?>'},
            {field:'west',width: 160,title:'<?php echo __("网格组西至"); ?>'},
            {field:'north',width: 160,title:'<?php echo __("网格组北至"); ?>'},
            {field: 'create_time',width: 160,title:'<?php echo __("创建时间"); ?>'},
            {field: 'create_by',width: 120,title:'<?php echo __("创建人"); ?>'},
            {field: 'update_time',width: 160,title:'<?php echo __("修改时间"); ?>'},
            {field: 'update_by',width: 120,title:'<?php echo __("修改人"); ?>'},
            {align: 'center', toolbar: '#tableBar', width:280, fixed: 'right', title: '<?php echo __("操作"); ?>'},
         ]]
      })

   })
</script>
