<?php /*a:1:{s:57:"E:\code\php\jsjsq\app/admin/view/sc\index\right_menu.html";i:1751179319;}*/ ?>
<?php 
	$allow = 1;
	if($data['level']==1&&$admin_info['grid_group_id']){
	$allow = 0;
	}else if($data['level']==2&&$admin_info['grid_id']){
	$allow = 0;
	}
 ?>
<script>
	var syrk = {};
	var czrk = {};
	var ldrk = {};
	var zlrk = {};
	var fw = {};
	var ggss = {};
	var jgdw = {};
	var rkfb = {};
	var pffb = {};
	var tsrq = {};
	var allow = <?php echo htmlentities($allow); ?>;
    $(function(){
        var title = '<?php echo htmlentities($title); ?>';
        var fontSize = "40px";
        $(".header-m-t").text(title+'概况');
        if(title.length<10){
            fontSize = "36px";
        }else if(title.length<12){
            fontSize = "32px";
        }else if(title.length<14){
            fontSize = "26px";
        }else{
            fontSize = "20px";
        }
        $(".header-m-t").css("font-size",fontSize);
		 syrk = {
			name:"实有人口",
			title: "<?php echo isset($total['rkfl']['syrk']) ? htmlentities($total['rkfl']['syrk']) : 0; ?>人",
			per:1
		};
		 czrk = {
			name:"常驻人口",
			title: "<?php echo isset($total['rkfl']['czrk']) ? htmlentities($total['rkfl']['czrk']) : 0; ?>人",
			per:(<?php echo isset($total['rkfl']['czrk']) ? htmlentities($total['rkfl']['czrk']) : 0; ?>/<?php echo isset($total['rkfl']['syrk']) ? htmlentities($total['rkfl']['syrk']) : 1; ?>).toFixed(2)
		};
		 ldrk = {
			name:"流动人口",
			title: "<?php echo isset($total['rkfl']['ldrk']) ? htmlentities($total['rkfl']['ldrk']) : 0; ?>人",
			per:(<?php echo isset($total['rkfl']['ldrk']) ? htmlentities($total['rkfl']['ldrk']) : 0; ?>/<?php echo isset($total['rkfl']['syrk']) ? htmlentities($total['rkfl']['syrk']) : 1; ?>).toFixed(2)
		};
		 zlrk = {
			name:"暂离人口",
			title: "<?php echo isset($total['rkfl']['zlrk']) ? htmlentities($total['rkfl']['zlrk']) : 0; ?>人",
			per:(<?php echo isset($total['rkfl']['zlrk']) ? htmlentities($total['rkfl']['zlrk']) : 0; ?>/<?php echo isset($total['rkfl']['syrk']) ? htmlentities($total['rkfl']['syrk']) : 1; ?>).toFixed(2)
		};
		 fw = {
			categories: ['其他','公共',"平房","楼房","总数"],
			legend:false,
			series: [
				{
					name: "栋数",
					data: [<?php echo htmlentities($total['fwsj'][4]); ?>,<?php echo htmlentities($total['fwsj'][3]); ?>,<?php echo htmlentities($total['fwsj'][2]); ?>,<?php echo htmlentities($total['fwsj'][1]); ?>,<?php echo htmlentities($total['fwsj'][0]); ?>],
					type: 'bar',
					barWidth: '50%',
					barMaxWidth: '20px',
					itemStyle: {
						color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
							{ offset: 0, color: '#f3e02b' },
							{ offset: 1, color: '#f6b51b' }
						])
					},
					label: {
						show: true, // 显示标签
						position: 'right' // 在顶部显示标签
					}
				}
			]
		};
		 ggss = {
			categories: ['警区','加油站',"公厕","停车场","集市","体育场","广场","公园"],
			legend:false,
			series: [
				{
					name: "数量",
					data: [<?php echo htmlentities($total['ggss']['place_jq']); ?>,<?php echo htmlentities($total['ggss']['place_jyz']); ?>,<?php echo htmlentities($total['ggss']['place_gce']); ?>,
						<?php echo htmlentities($total['ggss']['place_tcc']); ?>,<?php echo htmlentities($total['ggss']['place_js']); ?>,<?php echo htmlentities($total['ggss']['place_tyc']); ?>,<?php echo htmlentities($total['ggss']['place_gc']); ?>,<?php echo htmlentities($total['ggss']['place_gy']); ?>],
					type: 'bar',
					barWidth: '50%',
					barMaxWidth: '20px',
					itemStyle: {
						color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
							{ offset: 0, color: '#4264fb' },
							{ offset: 1, color: '#783CFA' }
						])
					},
					label: {
						show: true, // 显示标签
						position: 'right' // 在顶部显示标签
					}
				}
			]
		};
		 jgdw = {
			name: "数量",
			data: [<?php echo htmlentities($total['jgdw']['government_1']); ?>,<?php echo htmlentities($total['jgdw']['government_2']); ?>,<?php echo htmlentities($total['jgdw']['hospital']); ?>,<?php echo htmlentities($total['jgdw']['clinic']); ?>,<?php echo htmlentities($total['jgdw']['school']); ?>,<?php echo htmlentities($total['jgdw']['book']); ?>],
			categories: ["机关单位","事业单位","医院","社区门诊","学校","图书馆"]
		};
		 rkfb = {
			name:'实有人口',
			categories: [<?php echo htmlspecialchars_decode($total['rkfb']['group_name']); ?>],
			data: [<?php echo htmlentities($total['rkfb']['group_value']); ?>]
		};
		 pffb = {
			name: "平房户数",
			categories: [<?php echo htmlspecialchars_decode($total['pffb']['group_name']); ?>],
		data: [<?php echo htmlentities($total['pffb']['group_value']); ?>]
		};
		 tsrq = {
			categories: ["<?php echo htmlentities($total['tsrq'][4]['name']); ?>","<?php echo htmlentities($total['tsrq'][3]['name']); ?>","<?php echo htmlentities($total['tsrq'][2]['name']); ?>","<?php echo htmlentities($total['tsrq'][1]['name']); ?>","<?php echo htmlentities($total['tsrq'][0]['name']); ?>"],
			legend:false,
			series: [
				{
					name: "条数",
					data: ["<?php echo htmlentities($total['tsrq'][4]['value']); ?>","<?php echo htmlentities($total['tsrq'][3]['value']); ?>","<?php echo htmlentities($total['tsrq'][2]['value']); ?>","<?php echo htmlentities($total['tsrq'][1]['value']); ?>","<?php echo htmlentities($total['tsrq'][0]['value']); ?>"],
					type: 'bar',
					barWidth: '50%',
					barMaxWidth: '20px',
					itemStyle: {
						color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
							{ offset: 0, color: '#16e195' },
							{ offset: 1, color: '#35e1e1' }
						])
					},
					label: {
						show: true, // 显示标签
						position: 'right' // 在顶部显示标签
					}
				}
			]
		};
		 if(allow){
			 getServerData();
		 }
    });
</script>
<?php if($data['level'] == '0'): ?>
    <ul class="jdb-r-t flex flex-row-c">
		<li>所有社区<span class="iconfont icon-xiala"></span></li>
		<li>默认排序<span class="iconfont icon-xiala"></span></li>
	</ul>
	<div class="jdb-r-d">
		<ul>
			<?php if(is_array($list) || $list instanceof \think\Collection || $list instanceof \think\Paginator): if( count($list)==0 ) : echo "" ;else: foreach($list as $key=>$vo): ?>
			<li>
				<a href="javascript:void(0);" class="change" data-url="<?php echo url('sc/community',['id'=>$vo['id']]); ?>">
					<dl data-id="<?php echo htmlentities($vo['id']); ?>" data-action="community">
						<dt><i><?php echo htmlentities($key+1); ?></i><?php echo htmlentities($vo['community_name']); ?></dt>
						<dd>负责人：<?php echo htmlentities($vo['person']['name']); ?></dd>
						<dd>联系电话：<?php echo htmlentities($vo['person']['phone']); ?></dd>
					</dl>
					<?php 
						if (strpos($vo['image'], 'http') === 0) {
						$vo['image'] = [['src'=>$vo['image'],'title'=>'图片']];
						}else{
						$vo['image'] = json_decode($vo['image'],true);
						}
					 ?>
					<div class="img">
						<img src="<?php echo getThumb($vo['image'][0]['src']); ?>" alt="">
						<div>点击切换</div>
					</div>
				</a>
			</li>
			<?php endforeach; endif; else: echo "" ;endif; ?>
		</ul>
	</div>
<?php endif; if($data['level'] == '1'): ?>
    <ul class="jdb-r-t flex flex-row-c">
		<?php if(empty($admin_info['community_id']) || (($admin_info['community_id'] instanceof \think\Collection || $admin_info['community_id'] instanceof \think\Paginator ) && $admin_info['community_id']->isEmpty())): ?>
			<li class="navBack" data-action="<?php echo htmlentities($data['action']); ?>" data-id="<?php echo htmlentities($data['id']); ?>">返回上一级<span class="iconfont icon-xiala"></span></li>
			<?php else: ?>
			<li>社区级<span class="iconfont icon-xiala"></span></li>
		<?php endif; ?>
		<li><?php echo htmlentities($title); ?><span class="iconfont icon-xiala"></span></li>
	</ul>
	<div class="jdb-r-d">
		<ul>
			<?php if(is_array($list) || $list instanceof \think\Collection || $list instanceof \think\Paginator): if( count($list)==0 ) : echo "" ;else: foreach($list as $key=>$vo): ?>
			<li>
				<a href="javascript:void(0);">
					<dl data-id="<?php echo htmlentities($vo['id']); ?>" data-action="gridGroup" data-hotspot="<?php echo htmlentities($vo['hotspot']); ?>" data-hotspot_from="<?php echo htmlentities($vo['hotspot_from']); ?>">
						<dt><i><?php echo htmlentities($key+1); ?></i><?php echo htmlentities($vo['grid_name']); ?></dt>
						<dd>负责人：<?php echo htmlentities($vo['person']['name']); ?></dd>
						<dd>联系电话：<?php echo htmlentities($vo['person']['phone']); ?></dd>
					</dl>
					<?php 
						if (strpos($vo['image'], 'http') === 0) {
						$vo['image'] = [['src'=>$vo['image'],'title'=>'图片']];
						}else{
						$vo['image'] = json_decode($vo['image'],true);
						}
					 ?>
					<div class="img" data-url="<?php echo url('sc/gridGroup',['id'=>$vo['id']]); ?>">
						<img src="<?php echo getThumb($vo['image'][0]['src']); ?>" alt="">
						<div>点击切换</div>
					</div>
				</a>
			</li>
			<?php endforeach; endif; else: echo "" ;endif; ?>
		</ul>
	</div>
<?php endif; if($data['level'] == '2'): ?>
    <ul class="jdb-r-t flex flex-row-c">
		<?php if(empty($admin_info['grid_group_id']) || (($admin_info['grid_group_id'] instanceof \think\Collection || $admin_info['grid_group_id'] instanceof \think\Paginator ) && $admin_info['grid_group_id']->isEmpty())): ?>
			<li class="navBack" data-action="<?php echo htmlentities($data['action']); ?>" data-id="<?php echo htmlentities($data['id']); ?>">返回上一级<span class="iconfont icon-xiala"></span></li>
			<?php else: ?>
			<li>网格组级<span class="iconfont icon-xiala"></span></li>
		<?php endif; ?>
		<li><?php echo htmlentities($title); ?><span class="iconfont icon-xiala"></span></li>
	</ul>
	<div class="jdb-r-d">
		<ul>
			<?php if(is_array($list) || $list instanceof \think\Collection || $list instanceof \think\Paginator): if( count($list)==0 ) : echo "" ;else: foreach($list as $key=>$vo): ?>
			<li>
				<a href="javascript:void(0);">
					<dl data-id="<?php echo htmlentities($vo['id']); ?>" data-action="grid" data-hotspot="<?php echo htmlentities($vo['hotspot']); ?>" data-hotspot_from="<?php echo htmlentities($vo['hotspot_from']); ?>">
						<dt><i><?php echo htmlentities($key+1); ?></i><?php echo htmlentities($vo['grid_name']); ?></dt>
						<dd>负责人：<?php echo htmlentities($vo['person']['name']); ?></dd>
						<dd>联系电话：<?php echo htmlentities($vo['person']['phone']); ?></dd>
					</dl>
					<?php 
						if (strpos($vo['image'], 'http') === 0) {
						$vo['image'] = [['src'=>$vo['image'],'title'=>'图片']];
						}else{
						$vo['image'] = json_decode($vo['image'],true);
						}
					 ?>
					<div class="img" data-url="<?php echo url('sc/grid',['id'=>$vo['id']]); ?>">
						<img src="<?php echo getThumb($vo['image'][0]['src']); ?>" alt="">
						<div>点击切换</div>
					</div>
				</a>
			</li>
			<?php endforeach; endif; else: echo "" ;endif; ?>
		</ul>
	</div>
<?php endif; if($data['level'] == '3'): ?>
    <ul class="jdb-r-t flex flex-row-c">
		<?php if(empty($admin_info['grid_id']) || (($admin_info['grid_id'] instanceof \think\Collection || $admin_info['grid_id'] instanceof \think\Paginator ) && $admin_info['grid_id']->isEmpty())): ?>
			<li class="navBack" data-action="<?php echo htmlentities($data['action']); ?>" data-id="<?php echo htmlentities($data['id']); ?>">返回上一级<span class="iconfont icon-xiala"></span></li>
			<?php else: ?>
			<li>网格级<span class="iconfont icon-xiala"></span></li>
		<?php endif; ?>
		<li <?php if(mb_strlen($title)>10){echo "style='font-size:16px;'";}else if(mb_strlen($title)>8){echo "style='font-size:18px;'";} ?>><?php echo htmlentities($title); ?><span class="iconfont icon-xiala"></span></li>
	</ul>
	<div class="jdb-r-d">
		<ul>
			<?php if(is_array($list) || $list instanceof \think\Collection || $list instanceof \think\Paginator): if( count($list)==0 ) : echo "" ;else: foreach($list as $key=>$vo): ?>
			<li>
				<a href="javascript:void(0);">
					<dl data-id="<?php echo htmlentities($vo['id']); ?>" data-action="building">
						<dt><i><?php echo htmlentities($key+1); ?></i><?php echo htmlentities($vo['building_name']); ?></dt>
						<dd>单元数：<?php echo htmlentities($vo['unit']); ?>/楼层数：<?php echo htmlentities($vo['floor']); ?></dd>
						<dd>房屋数：<?php echo htmlentities($vo['house']); ?>/人口数：<?php echo htmlentities($vo['person']); ?></dd>
					</dl>
					<?php 
						if (strpos($vo['image'], 'http') === 0) {
						$vo['image'] = [['src'=>$vo['image'],'title'=>'图片']];
						}else{
						$vo['image'] = json_decode($vo['image'],true);
						}
					 ?>
					<div class="img" onclick="showBuilding(<?php echo htmlentities($vo['id']); ?>)">
						<img src="<?php echo getThumb($vo['image'][0]['src']); ?>" alt="">
						<div>点击切换</div>
					</div>
				</a>
			</li>
			<?php endforeach; endif; else: echo "" ;endif; ?>
		</ul>
	</div>
<?php endif; if($data['level'] == '4'): ?>
    <ul class="jdb-r-t flex flex-row-c">
		<li class="navBack" data-action="<?php echo htmlentities($data['action']); ?>" data-id="<?php echo htmlentities($data['id']); ?>">返回上一级<span class="iconfont icon-xiala"></span></li>
		<li <?php if(mb_strlen($title)>10){echo "style='font-size:16px;'";}else if(mb_strlen($title)>8){echo "style='font-size:18px;'";} ?>><?php echo htmlentities($title); ?><span class="iconfont icon-xiala"></span></li>
	</ul>
	<div class="jdb-r-d">
		<ul>
			<?php 
				if (strpos($building['image'], 'http') === 0) {
				$image = [['src'=>$building['image'],'title'=>'图片']];
				}else{
				$image = json_decode($building['image'],true);
				}
			 if(is_array($list) || $list instanceof \think\Collection || $list instanceof \think\Paginator): if( count($list)==0 ) : echo "" ;else: foreach($list as $key=>$vo): if($vo['unit'] > '0'): ?>
			<li>
				<a href="javascript:void(0);" >
					<dl data-id="<?php echo htmlentities($building['id']); ?>" data-unit="<?php echo htmlentities($vo['unit']); ?>" class="sqbox-on">
						<dt><i><?php echo htmlentities($vo['unit']); ?></i>单元</dt>
						<dd>房屋数量：<?php echo htmlentities($vo['house']); ?></dd>
						<dd>人口数量：<?php echo htmlentities($vo['person']); ?></dd>
					</dl>
					<div class="img">
						<?php 
							$title = $vo['unit']."单元";
						 if(is_array($image) || $image instanceof \think\Collection || $image instanceof \think\Paginator): if( count($image)==0 ) : echo "" ;else: foreach($image as $key=>$v): if($v['title'] == $title): ?>
								<img src="<?php echo getThumb($v['src']); ?>" class="unit-<?php echo htmlentities($vo['unit']); ?>" alt=""  onclick="showUnit('<?php echo htmlentities($v['src']); ?>')">
							<?php endif; ?>
						<?php endforeach; endif; else: echo "" ;endif; ?>
					</div>
				</a>
			</li>
			<?php endif; ?>
			<?php endforeach; endif; else: echo "" ;endif; if($total['merchant'] > '0'): ?>
			<li>
				<a href="javascript:void(0);" data-url="/admin/sc/index/place?id=<?php echo htmlentities($building['id']); ?>&type=building&place=1" class="place">
					<dl>
						<dt><i>商</i>个体工商户</dt>
						<dd>数量：<?php echo htmlentities($total['merchant']); ?></dd>
						<dd></dd>
					</dl>
					<div class="img">
						
					</div>
				</a>
			</li>
			<?php endif; if($total['enterprise'] > '0'): ?>
			<li>
				<a href="javascript:void(0);" data-url="/admin/sc/index/place?id=<?php echo htmlentities($building['id']); ?>&type=building&place=2" class="place">
					<dl>
						<dt><i>企</i>企业</dt>
						<dd>数量：<?php echo htmlentities($total['enterprise']); ?></dd>
						<dd>&nbsp;</dd>
					</dl>
					<div class="img">
						
					</div>
				</a>
			</li>
			<?php endif; if($total['government'] > '0'): ?>
			<li>
				<a href="javascript:void(0);" data-url="/admin/sc/index/place?id=<?php echo htmlentities($building['id']); ?>&type=building&place=3" class="place">
					<dl>
						<dt><i>单</i>机关及事业单位</dt>
						<dd>数量：<?php echo htmlentities($total['government']); ?></dd>
						<dd>&nbsp;</dd>
					</dl>
					<div class="img">
						
					</div>
				</a>
			</li>
			<?php endif; ?>
		</ul>
	</div>
<?php endif; ?>