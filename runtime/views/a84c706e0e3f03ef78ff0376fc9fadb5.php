<?php /*a:5:{s:49:"E:\code\php\jsjsq\app/admin/view/index\total.html";i:1751179316;s:51:"E:\code\php\jsjsq\app/admin/view/public\header.html";i:1751179315;s:51:"E:\code\php\jsjsq\app/admin/view/public\search.html";i:1751179315;s:51:"E:\code\php\jsjsq\app/admin/view/public\footer.html";i:1751179315;s:51:"E:\code\php\jsjsq\app/admin/view/public\select.html";i:1751179315;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>SwiftAdmin 后台管理开发框架</title>
    <link href="/favicon.ico" rel="icon">
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link href="/static/system/layui/css/layui.css?v=<?php echo release(); ?>" rel="stylesheet" type="text/css"/>
    <link href="/static/system/css/style.css?v=<?php echo release(); ?>" rel="stylesheet" type="text/css"/>
    <link href="/static/system/layui/css/layphoto.css?v=<?php echo release(); ?>" rel="stylesheet" type="text/css"/>
    <!-- // 加载font-awesome图标 -->
    <link href="/static/system/layui/css/font-awesome.css?v=<?php echo release(); ?>" rel="stylesheet" type="text/css"/>
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>
<script>
    let app_Config = {
        app: "<?php echo htmlentities((isset($app) && ($app !== '')?$app:'/admin')); ?>",
        controller: "<?php echo htmlentities((isset($controller) && ($controller !== '')?$controller:'index')); ?>",
        action: "<?php echo htmlentities((isset($action) && ($action !== '')?$action:'index')); ?>",
        version: "<?php echo config('app.version'); ?>",
        api: "<?php echo config('app.api_url'); ?>",
    };

    let upload_chunkSize = <?php echo saenv('upload_chunk_size'); ?>;
</script>

<body>
<?php 
    $road_LIST = getStreetTop(1);
    $street_LIST = getStreetTop(2);
 ?>
<div class="layui-fluid">
    <div class="layui-card">
        <!-- // 默认操作按钮 -->
        <div class="layui-card-header layadmin-card-header-auto ">
            <div class="layui-form">
                <!-- // 自定义搜索参数 -->
                <div id="laytable-search" class="layui-form-item"></div>
                <div class="layui-form-item">
                    <?php 
    $Communities = getCommunity();
    if($Communities[0]['id']==1){
    unset($Communities[0]);
    $Communities = array_values($Communities);
    }
    if($params){
        $data = getDataByParam($params);
    }
    $community_id = empty($data['community_id'])?$Communities[0]['id']:$data['community_id'];
    $GridGroups = getGridGroup($community_id);
    $grid_group_id = empty($data['grid_group_id'])?$GridGroups[0]['id']:$data['grid_group_id'];
    $Grids = getGrid($grid_group_id);
 ?>
<style>
    /*.short-label{width: auto;}*/
</style>
<div class="layui-inline">
    <div class="layui-form-label short-label"><?php echo __('社区'); ?></div>
    <div class="layui-input-inline ">
        <select name="community_id" lay-search lay-filter="communityChange">
            <?php if((count($Communities) > 1)): ?>
            <option value="">请选择</option>
            <?php endif; if(is_array($Communities) || $Communities instanceof \think\Collection || $Communities instanceof \think\Paginator): if( count($Communities)==0 ) : echo "" ;else: foreach($Communities as $key=>$item): ?>
                <option value="<?php echo htmlentities($item['id']); ?>"
                <?php if((isset($data['community_id']) && $data['community_id'])): if(in_array(($item['id']), is_array($data['community_id'])?$data['community_id']:explode(',',$data['community_id']))): ?>selected<?php endif; ?>
                <?php endif; ?>
                ><?php echo htmlentities($item['community_name']); ?></option>
            <?php endforeach; endif; else: echo "" ;endif; ?>
        </select>
    </div>
</div>

<div class="layui-inline grid-group">
    <div class="layui-form-label short-label"><?php echo __('网格组'); ?></div>
    <div class="layui-input-inline ">
        <select id="grid_group" name="grid_group_id" lay-search lay-filter="gridGroupChange">
            <?php if((count($GridGroups) > 1)): ?>
            <option value="">请选择</option>
            <?php endif; if(is_array($GridGroups) || $GridGroups instanceof \think\Collection || $GridGroups instanceof \think\Paginator): if( count($GridGroups)==0 ) : echo "" ;else: foreach($GridGroups as $key=>$item): ?>
                <option value="<?php echo htmlentities($item['id']); ?>"
                <?php if((isset($data['grid_group_id']) && $data['grid_group_id'])): if(in_array(($item['id']), is_array($data['grid_group_id'])?$data['grid_group_id']:explode(',',$data['grid_group_id']))): ?>selected<?php endif; ?>
                <?php endif; ?>
                ><?php echo htmlentities($item['grid_name']); ?></option>
            <?php endforeach; endif; else: echo "" ;endif; ?>
        </select>
    </div>
</div>

<div class="layui-inline grid">
    <div class="layui-form-label short-label"><?php echo __('网格'); ?></div>
    <div class="layui-input-inline ">
        <select id="grid" name="grid_id" lay-search lay-filter="gridChange">
            <?php if((count($Grids) > 1)): ?>
            <option value="">请选择</option>
            <?php endif; if(is_array($Grids) || $Grids instanceof \think\Collection || $Grids instanceof \think\Paginator): if( count($Grids)==0 ) : echo "" ;else: foreach($Grids as $key=>$item): ?>
                <option value="<?php echo htmlentities($item['id']); ?>"
                <?php if((isset($data['grid_id']) && $data['grid_id'])): if(in_array(($item['id']), is_array($data['grid_id'])?$data['grid_id']:explode(',',$data['grid_id']))): ?>selected<?php endif; ?>
                <?php endif; ?>
                ><?php echo htmlentities($item['grid_name']); ?></option>
            <?php endforeach; endif; else: echo "" ;endif; ?>
        </select>
    </div>
</div>
                    <div class="layui-inline">
                        <div class="layui-form-label"><?php echo __('关键字'); ?></div>
                        <div class="layui-input-inline ">
                            <input name="keyword" class="layui-input" type="text" placeholder="<?php echo __('请输入关键字'); ?>"/>
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <div class="layui-form-label short-label"><?php echo __('路名'); ?></div>
                        <div class="layui-input-inline ">
                            <select name="road_id" lay-search lay-filter="roadChange">
                                <option value="">请选择</option>
                                <?php if(is_array($road_LIST) || $road_LIST instanceof \think\Collection || $road_LIST instanceof \think\Paginator): if( count($road_LIST)==0 ) : echo "" ;else: foreach($road_LIST as $key=>$item): ?>
                                    <option value="<?php echo htmlentities($item['id']); ?>"><?php echo htmlentities($item['name']); ?></option>
                                <?php endforeach; endif; else: echo "" ;endif; ?>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-form-label short-label"><?php echo __('　范围'); ?></div>
                        <div class="layui-input-inline" style="width: 80px;">
                            <input type="number" name="road_start" id="road_start" autocomplete="off" class="layui-input StreetNo">
                        </div>
                        <div class="layui-form-mid">-</div>
                        <div class="layui-input-inline" style="width: 80px;">
                            <input type="number" name="road_end" id="road_end" autocomplete="off" class="layui-input StreetNo">
                        </div>
                    </div>
                </div>
                <div class="layui-form-item">
                    <div class="layui-inline">
                        <div class="layui-form-label short-label"><?php echo __('街名'); ?></div>
                        <div class="layui-input-inline ">
                            <select name="street_id" lay-search lay-filter="streetChange">
                                <option value="">请选择</option>
                                <?php if(is_array($street_LIST) || $street_LIST instanceof \think\Collection || $street_LIST instanceof \think\Paginator): if( count($street_LIST)==0 ) : echo "" ;else: foreach($street_LIST as $key=>$item): ?>
                                    <option value="<?php echo htmlentities($item['id']); ?>"><?php echo htmlentities($item['name']); ?></option>
                                <?php endforeach; endif; else: echo "" ;endif; ?>
                            </select>
                        </div>
                    </div>
                    <div class="layui-inline">
                        <div class="layui-form-label short-label"><?php echo __('　范围'); ?></div>
                        <div class="layui-input-inline" style="width: 80px;">
                            <input type="number" name="street_start" id="street_start" autocomplete="off" class="layui-input StreetNo">
                        </div>
                        <div class="layui-form-mid">-</div>
                        <div class="layui-input-inline" style="width: 80px;">
                            <input type="number" name="street_end" id="street_end" autocomplete="off" class="layui-input StreetNo">
                        </div>
                    </div>
                    <div class="layui-inline" >
                        <!-- // 默认搜索 -->
                        <button id="search" class="layui-btn icon-btn" id="search" lay-filter="allSearch" lay-submit><i class="layui-icon layui-icon-search"></i><?php echo __('搜索'); ?></button>
                    </div>
                </div>
            </div>
        </div>
        <div id="layui-tab" id="tab_0" class="layui-tab layui-tab-brief">
            <ul class="layui-tab-title">
                <li class="layui-this tab-li" data-id="building">建筑信息</li>
                <li class="tab-li" data-id="house">房屋信息</li>
                <li class="tab-li" data-id="merchant">个体工商户</li>
                <li class="tab-li" data-id="enterprise">企业信息</li>
                <li class="tab-li" data-id="government">机关及事业单位</li>
                <li class="tab-li" data-id="person">人口信息</li>
                <li class="tab-li" data-id="vehicle">车辆信息</li>
            </ul>
            <div class="layui-tab-content">
                <div class="layui-tab-item layui-show " data-index="0">
                    <table id="lay-building" ></table>
                </div>
                <div class="layui-tab-item" data-index="1">
                    <table id="lay-house" ></table>
                </div>
                <div class="layui-tab-item" data-index="2">
                    <table id="lay-merchant" ></table>
                </div>
                <div class="layui-tab-item" data-index="3">
                    <table id="lay-enterprise" ></table>
                </div>
                <div class="layui-tab-item" data-index="4">
                    <table id="lay-government" ></table>
                </div>
                <div class="layui-tab-item" data-index="5">
                    <table id="lay-person" ></table>
                </div>
                <div class="layui-tab-item" data-index="6">
                    <table id="lay-vehicle" ></table>
                </div>
    </div>
</div>

<!-- // 列表工具栏 -->
<script type="text/html" id="buildBar">
    <button class="layui-btn layui-btn-primary layui-btn-xs" sa-event="tabs"
            data-title="<?php echo __('建筑信息'); ?>" data-area="100%,100%"
            data-maxmin="true" data-url="<?php echo url('/Building/detail'); ?>?id={{d.id}}" >
        <?php echo __('查看'); ?>
    </button>
</script>
<script type="text/html" id="houseBar">
    <button class="layui-btn layui-btn-primary layui-btn-xs" sa-event="tabs"
            data-title="<?php echo __('房屋信息'); ?>" data-area="100%,100%"
            data-maxmin="true" data-url="<?php echo url('/House/detail'); ?>?id={{d.id}}" >
        <?php echo __('查看'); ?>
    </button>
</script>
<script type="text/html" id="merchantBar">
    <button class="layui-btn layui-btn-primary layui-btn-xs" sa-event="tabs"
            data-title="<?php echo __('个体工商户信息'); ?>" data-area="100%,100%"
            data-maxmin="true" data-url="<?php echo url('/PlaceMerchant/detail'); ?>?id={{d.id}}" >
        <?php echo __('查看'); ?>
    </button>
</script>
<script type="text/html" id="enterpriseBar">
    <button class="layui-btn layui-btn-primary layui-btn-xs" sa-event="tabs"
            data-title="<?php echo __('企业信息'); ?>" data-area="100%,100%"
            data-maxmin="true" data-url="<?php echo url('/PlaceEnterprise/detail'); ?>?id={{d.id}}" >
        <?php echo __('查看'); ?>
    </button>
</script>
<script type="text/html" id="governmentBar">
    <button class="layui-btn layui-btn-primary layui-btn-xs" sa-event="tabs"
            data-title="<?php echo __('机关及事业单位信息'); ?>" data-area="100%,100%"
            data-maxmin="true" data-url="<?php echo url('/PlaceGovernment/detail'); ?>?id={{d.id}}" >
        <?php echo __('查看'); ?>
    </button>
</script>
<script type="text/html" id="personBar">
    <button class="layui-btn layui-btn-primary layui-btn-xs" sa-event="tabs"
            data-title="<?php echo __('人口信息'); ?>" data-area="100%,100%"
            data-maxmin="true" data-url="<?php echo url('/Person/detail'); ?>?id={{d.id}}" >
        <?php echo __('查看'); ?>
    </button>
</script>
<script type="text/html" id="vehicleBar">
    <button class="layui-btn layui-btn-primary layui-btn-xs" sa-event="tabs"
            data-title="<?php echo __('车辆信息'); ?>" data-area="100%,100%"
            data-maxmin="true" data-url="<?php echo url('/PersonVehicle/detail'); ?>?id={{d.id}}" >
        <?php echo __('查看'); ?>
    </button>
</script>






<!--// 加载CommonJS-->
<script src="/static/system/layui/layui.js?v=<?php echo release(); ?>"></script>
<script src="/static/system/js/common.js?v=<?php echo release(); ?>"></script>
<script src="/static/system/js/cascadata.js?v=<?php echo release(); ?>"></script>
<script>
    layui.use(['admin','table','form','jquery'], function () {

        var admin = layui.admin;
        var table = layui.table;
        var form = layui.form;
        var $ = layui.jquery;
        var buildUrl = "<?php echo url('/Building/index'); ?>";
        var houseUrl = "<?php echo url('/House/index'); ?>";
        var merchantUrl = "<?php echo url('/PlaceMerchant/index'); ?>";
        var enterpriseUrl = "<?php echo url('/PlaceEnterprise/index'); ?>";
        var governmentUrl = "<?php echo url('/PlaceGovernment/index'); ?>";
        var personUrl = "<?php echo url('/Person/index'); ?>";
        var vehicleUrl = "<?php echo url('/PersonVehicle/index'); ?>";
        var type_json = <?php echo json_encode(getCateTree('place_merchant_type')); ?>;
        var cur_id = "building";
        var buildTable = table.render({
            elem: "#lay-building"
            ,url: buildUrl
            ,cellMinWidth: 60
            ,page: true
            ,limit: 50,limits: [50, 100, 200, 500]
            ,height: 'full-150' //设置表格高度，减去固定的底部高度
            ,cols: [[
                {field: 'community_id',width: 320, minWidth: 300, title: '<?php echo __("所在区域"); ?>',templet:function(d){
                        return d.community_id>0?d.community_name+","+d.grid_group_name+","+d.grid_name:''
                    }},
                {field:'building_name',width: 160,title:'<?php echo __("建筑名称"); ?>'},
                {field: 'type',width:120,title:'<?php echo __("建筑类型"); ?>',templet:function(d){
                        var resData = <?php echo json_encode(getDictionary(13)); ?>;
                        return d.type > 0 ?resData[d.type]:''
                    }},
                {field: 'image',width: 120,templet:function(d) {
                        if(d.image==''){
                            return '';
                        }
                        var album = [];
                        try {
                            d.image = JSON.parse(d.image);
                            for (var i in d.image) {
                                album[i] = '<a href="javascript:" class="fileslink" ><img class="filesuffix" lay-image-click src="'+admin.toThumb(d.image[i].src)+'"></a>'
                            }
                            return album.join('');
                        } catch (e) {
                            return '<a href="javascript:" class="fileslink" ><img class="filesuffix" lay-image-click src="'+admin.toThumb(d.image)+'"></a>'
                        }
                    },title:'<?php echo __("街景示意图"); ?>'},
                {field:'unit', width: 160, title:'<?php echo __("单元数-地上层数-地下层数"); ?>',templet:function(d){
                        return d.unit+"-"+d.floor+"-"+d.floor_under
                    }},
                {field:'address',width:200,title:'<?php echo __("建筑位置"); ?>'},
                {field: 'street_name',width:120,title:'<?php echo __("街"); ?>',templet:function(d) {
                        var street = "";
                        if(d.street_name){
                            street += d.street_name+d.street_start+"-"+d.street_end;
                        }
                        if(d.street_sub){
                            street += " "+d.street_sub;
                        }
                        return street;
                    }},
                {field:'road_name',width:120,templet:function(d) {
                        var road = "";
                        if(d.road_name){
                            road += d.road_name+d.road_start+"-"+d.road_end;
                        }
                        if(d.road_sub){
                            road += " "+d.road_sub;
                        }
                        return road;
                    },title:'<?php echo __("路"); ?>'},
                {field: 'create_time',width: 160,title:'<?php echo __("创建时间"); ?>'},
                {field: 'create_by',width: 120,title:'<?php echo __("创建人"); ?>'},
                {field: 'update_time',width: 160,title:'<?php echo __("修改时间"); ?>'},
                {field: 'update_by',width: 120,title:'<?php echo __("修改人"); ?>'},
                {align: 'center', toolbar: '#buildBar', width:100, fixed: 'right', title: '<?php echo __("操作"); ?>'},
            ]]
        })
        setTimeout(function(){
            var houseTable = table.render({
                elem: "#lay-house"
                , url: houseUrl
                , cellMinWidth: 60
                ,page: true
                ,limit: 50,limits: [50, 100, 200, 500]
                ,height: 'full-150' //设置表格高度，减去固定的底部高度
                , cols: [[
                    {field: 'community_id',width: 320, minWidth: 300, title: '<?php echo __("所在区域"); ?>',templet:function(d){
                            return d.community_id>0?d.community_name+","+d.grid_group_name+","+d.grid_name:''
                        }},
                    {field: 'building_name',width:160, title: '<?php echo __("所在建筑"); ?>'},
                    {field: 'unit',width:100, title: '<?php echo __("单元楼层"); ?>',templet:function(d){
                            return d.unit+"单元,"+d.floor+"楼"
                        }},
                    {field: 'code',width:100, title: '<?php echo __("门牌号"); ?>'},
                    {field: 'area',width:80, title: '<?php echo __("房屋面积"); ?>',templet:function(d){
                            return d.area+"㎡"
                        }},
                    {field: 'property_right',width:80, title: '<?php echo __("产权性质"); ?>',templet:function(d){
                            var resData = <?php echo json_encode(getDictionary(349)); ?>;
                            return d.property_right > 0 ?resData[d.property_right]:''
                        }},
                    {field: 'usage_category',width:80, title: '<?php echo __("使用类别"); ?>',templet:function(d){
                            var resData = <?php echo json_encode(getDictionary(355)); ?>;
                            return d.usage_category > 0 ?resData[d.usage_category]:''
                        }},
                    {field: 'house_type',width:80, title: '<?php echo __("房屋类型"); ?>',templet:function(d){
                            var resData = <?php echo json_encode(getDictionary(362)); ?>;
                            return d.house_type > 0 ?resData[d.house_type]:''
                        }},
                    {field: 'residence_nature',width:80, title: '<?php echo __("住所性质"); ?>',templet:function(d){
                            var resData = <?php echo json_encode(getDictionary(367)); ?>;
                            return d.residence_nature > 0 ?resData[d.residence_nature]:''
                        }},
                    {field: 'rent',width:120, title: '<?php echo __("是否出租"); ?>',templet:function(d){
                            return d.rent==1?'是':'否'
                        }},
                    {field: 'create_time',width:160, title: '<?php echo __("创建时间"); ?>'},
                    {field: 'create_by',width: 120, title: '<?php echo __("创建人"); ?>'},
                    {field: 'update_time',width: 160, title: '<?php echo __("修改时间"); ?>'},
                    {field: 'update_by',width: 120, title: '<?php echo __("修改人"); ?>'},
                    {align: 'center', toolbar: '#houseBar', width: 100, fixed: 'right', title: '<?php echo __("操作"); ?>'},
                ]]
            })
            var merchantTable = table.render({
                elem: "#lay-merchant"
                ,url: merchantUrl
                ,cellMinWidth: 60
                ,page: true
                ,limit: 50,limits: [50, 100, 200, 500]
                ,height: 'full-150' //设置表格高度，减去固定的底部高度
                , cols: [[
                    {field: 'community_id',width: 320,minWidth: 300, title: '<?php echo __("所在区域"); ?>', templet: function (d) {
                            return d.community_name + "," + d.grid_group_name + "," + d.grid_name
                        }},
                    {field: 'building_name',width:160, title: '<?php echo __("所在建筑"); ?>'},
                    {field: 'merchant_name',width:200, title: '<?php echo __("商户名称"); ?>'},
                    {
                    field: 'image',width: 120,width: 120, templet: function (d) {
                        if(d.image==''){
                            return '';
                        }
                        var album = [];
                        try {
                            d.image = JSON.parse(d.image);
                            for (var i in d.image) {
                                album[i] = '<a href="javascript:" class="fileslink" ><img class="filesuffix" lay-image-click src="' + admin.toThumb(d.image[i].src) + '"></a>'
                            }
                            return album.join('');
                        } catch (e) {
                            return '<a href="javascript:" class="fileslink" ><img class="filesuffix" lay-image-click src="' + admin.toThumb(d.image) +  '"></a>'
                        }
                    }, title: '<?php echo __("街景示意图"); ?>'
                },
                    {field:'address',width: 200, title: '<?php echo __("商户地址"); ?>'},
                    {field: 'unit',width:100, title: '<?php echo __("单元楼层"); ?>',templet:function(d){
                            return d.unit+"单元,"+d.floor+"楼"
                        }},
                    {field: 'code',width:180, title: '<?php echo __("营业执照号"); ?>'},
                    {field: 'corporation',width:80, title: '<?php echo __("法人"); ?>'},
                    {field: 'phone',width:120, title: '<?php echo __("联系电话"); ?>'},
                    {field: 'type_1_id',width:120, title: '<?php echo __("行业大类"); ?>',templet:function(d){
                            return d.type_1_id>0?type_json[d.type_1_id].name:'';
                        }},
                    {field: 'type_2_id',width:120, title: '<?php echo __("行业类别"); ?>',templet:function(d){
                            return d.type_2_id>0?type_json[d.type_1_id].children[d.type_2_id].name:'';
                        }},
                    {field: 'create_time',width:160, title: '<?php echo __("创建时间"); ?>'},
                    {field: 'create_by',width: 120, title: '<?php echo __("创建人"); ?>'},
                    {field: 'update_time',width: 160, title: '<?php echo __("修改时间"); ?>'},
                    {field: 'update_by',width: 120, title: '<?php echo __("修改人"); ?>'},
                    {align: 'center', toolbar: '#merchantBar', width: 100, fixed: 'right', title: '<?php echo __("操作"); ?>'},
                ]]
            })
            var enterpriseTable = table.render({
                elem: "#lay-enterprise"
                , url: enterpriseUrl
                , cellMinWidth: 60
                ,page: true
                ,limit: 50,limits: [50, 100, 200, 500]
                ,height: 'full-150' //设置表格高度，减去固定的底部高度
                , cols: [[
                    {field: 'community_id',width: 320, minWidth: 300, title: '<?php echo __("所在区域"); ?>', templet: function (d) {
                            return d.community_name + "," + d.grid_group_name + "," + d.grid_name
                        }},
                    {field: 'building_name',width:160, title: '<?php echo __("所在建筑"); ?>'},
                    {field: 'enterprise_name',width:200, title: '<?php echo __("企业名称"); ?>'},
                    {
                    field: 'image',width: 120,width: 120, templet: function (d) {
                        if(d.image==''){
                            return '';
                        }
                        var album = [];
                        try {
                            d.image = JSON.parse(d.image);
                            for (var i in d.image) {
                                album[i] = '<a href="javascript:" class="fileslink" ><img class="filesuffix" lay-image-click src="' + admin.toThumb(d.image[i].src) + '"></a>'
                            }
                            return album.join('');
                        } catch (e) {
                            return '<a href="javascript:" class="fileslink" ><img class="filesuffix" lay-image-click src="' + admin.toThumb(d.image) +  '"></a>'
                        }
                    }, title: '<?php echo __("街景示意图"); ?>'
                },
                    {field:'address',width: 200, title: '<?php echo __("企业地址"); ?>'},
                    {field: 'unit',width:100, title: '<?php echo __("单元楼层"); ?>',templet:function(d){
                            return d.unit+"单元,"+d.floor+"楼"
                        }},
                    {field: 'code',width:180, title: '<?php echo __("营业执照号"); ?>'},
                    {field: 'corporation',width:80, title: '<?php echo __("法人"); ?>'},
                    {field: 'phone',width:120, title: '<?php echo __("联系电话"); ?>'},
                    {field: 'type_1_id',width:120, title: '<?php echo __("行业大类"); ?>',templet:function(d){
                            return d.type_1_id>0?type_json[d.type_1_id].name:'';
                        }},
                    {field: 'type_2_id',width:120, title: '<?php echo __("行业类别"); ?>',templet:function(d){
                            return d.type_2_id>0?type_json[d.type_1_id].children[d.type_2_id].name:'';
                        }},
                    {field: 'create_time',width:160, title: '<?php echo __("创建时间"); ?>'},
                    {field: 'create_by',width: 120, title: '<?php echo __("创建人"); ?>'},
                    {field: 'update_time',width: 160, title: '<?php echo __("修改时间"); ?>'},
                    {field: 'update_by',width: 120, title: '<?php echo __("修改人"); ?>'},
                    {align: 'center', toolbar: '#enterpriseBar', width: 200, fixed: 'right', title: '<?php echo __("操作"); ?>'},
                ]]
            })
            var governmentTable = table.render({
                elem: "#lay-government"
                ,url: governmentUrl
                ,cellMinWidth: 60
                ,page: true
                ,limit: 50,limits: [50, 100, 200, 500]
                ,height: 'full-150' //设置表格高度，减去固定的底部高度
                , cols: [[
                    {field: 'community_id',width: 320, minWidth: 300, title: '<?php echo __("所在区域"); ?>', templet: function (d) {
                            return d.community_name + "," + d.grid_group_name + "," + d.grid_name
                        }},
                    {field: 'building_name',width:160, title: '<?php echo __("所在建筑"); ?>'},
                    {field: 'unit_name',width:200, title: '<?php echo __("单位名称"); ?>'},
                    {
                    field: 'image',width: 120,width: 120, templet: function (d) {
                        if(d.image==''){
                            return '';
                        }
                        var album = [];
                        try {
                            d.image = JSON.parse(d.image);
                            for (var i in d.image) {
                                album[i] = '<a href="javascript:" class="fileslink" ><img class="filesuffix" lay-image-click src="' + admin.toThumb(d.image[i].src) + '"></a>'
                            }
                            return album.join('');
                        } catch (e) {
                            return '<a href="javascript:" class="fileslink" ><img class="filesuffix" lay-image-click src="' + admin.toThumb(d.image) +  '"></a>'
                        }
                    }, title: '<?php echo __("街景示意图"); ?>'
                },
                    {field:'address',width: 200, title: '<?php echo __("单位地址"); ?>'},
                    {field:'type',width: 120, title: '<?php echo __("单位分类"); ?>',templet:function(d){
                            var resData = <?php echo json_encode(getDictionary(50)); ?>;
                            return d.type > 0 ?resData[d.type]:''
                        }},
                    {field: 'unit',width:100, title: '<?php echo __("单元楼层"); ?>',templet:function(d){
                            return d.unit+"单元,"+d.floor+"楼"
                        }},
                    {field: 'code',width:180, title: '<?php echo __("统一社会信用代码"); ?>'},
                    {field: 'corporation',width:80, title: '<?php echo __("法人"); ?>'},
                    {field: 'phone',width:120, title: '<?php echo __("联系电话"); ?>'},
                    {field: 'create_time',width:160, title: '<?php echo __("创建时间"); ?>'},
                    {field: 'create_by',width: 120, title: '<?php echo __("创建人"); ?>'},
                    {field: 'update_time',width: 160, title: '<?php echo __("修改时间"); ?>'},
                    {field: 'update_by',width: 120, title: '<?php echo __("修改人"); ?>'},
                    {align: 'center', toolbar: '#governmentBar', width: 200, fixed: 'right', title: '<?php echo __("操作"); ?>'},
                ]]
            })
            var personTable = table.render({
                elem: "#lay-person"
                ,url: personUrl
                ,cellMinWidth: 60
                ,page: true
                ,limit: 50,limits: [50, 100, 200, 500]
                ,height: 'full-150' //设置表格高度，减去固定的底部高度
                ,cols: [[
                    {field:'name',align: 'left',width:180,title:'<?php echo __("姓名"); ?>', templet: function(d){
                            return d.name;
                        }},
                    {field:'id_code',title:'<?php echo __("年龄"); ?>',width:50,templet:function(d){
                            return d.age
                        }},
                    {field:'sex',title:'<?php echo __("性别"); ?>',width:50,templet:function(d){
                            return d.sex==1?'男':'女'
                        }},
                    {field: 'community_id',width: 320, minWidth: 300, title: '<?php echo __("所在区域"); ?>',templet:function(d){
                            return d.community_id>0?d.community_name+","+d.grid_group_name+","+d.grid_name:''
                        }},
                    {field: 'building_name',width:160, title: '<?php echo __("所在房屋"); ?>'},
                    {field:'address',width:200,title:'<?php echo __("房屋地址"); ?>'},
                    {field:'nation',width:80,title:'<?php echo __("民族"); ?>',templet:function(d){
                            var resData = <?php echo json_encode(getDictionary(54)); ?>;
                            return d.nation > 0 ?resData[d.nation]:''
                        }},
                    {field:'face',width:80,title:'<?php echo __("政治面貌"); ?>',templet:function(d){
                            var resData = <?php echo json_encode(getDictionary(58)); ?>;
                            return d.face > 0 ?resData[d.face]:''
                        }},
                    {field:'education',width:80,title:'<?php echo __("学历"); ?>',templet:function(d){
                            var resData = <?php echo json_encode(getDictionary(56)); ?>;
                            return d.education > 0 ?resData[d.education]:''
                        }},
                    {field: 'create_by',width: 120,title:'<?php echo __("创建者"); ?>'},
                    {field: 'create_time',width: 160,title:'<?php echo __("创建时间"); ?>'},
                    {field: 'update_by',width: 120,title:'<?php echo __("更新者"); ?>'},
                    {field: 'update_time',width: 160,title:'<?php echo __("更新时间"); ?>'},
                    {align: 'center', toolbar: '#personBar', width:200, fixed: 'right', title: '<?php echo __("操作"); ?>'},
                ]]
            })
            var vehicleTable = table.render({
                elem: "#lay-vehicle"
                ,url: vehicleUrl
                ,cellMinWidth: 60
                ,page: true
                ,limit: 50,limits: [50, 100, 200, 500]
                ,height: 'full-150' //设置表格高度，减去固定的底部高度
                ,cols: [[
                    {field: 'community_id',width: 320, minWidth: 300, title: '<?php echo __("所在区域"); ?>', templet: function (d) {
                            return d.community_name + "," + d.grid_group_name + "," + d.grid_name
                        }},
                    {field: 'vehicle_code',width:120,title:'<?php echo __("车牌号码"); ?>'},
                    {field:'phone',width:120,title:'<?php echo __("车主手机号码"); ?>'},
                    {field: 'owner',width:80,title:'<?php echo __("车主姓名"); ?>'},
                    {field:'id_code',width:180,title:'<?php echo __("车主身份证号"); ?>'},
                    {field: 'path',width:120,templet:function(d) {
                            var album = [];
                            try {
                                d.path = JSON.parse(d.path);
                                for (var i in d.path) {
                                    album[i] = '<a href="javascript:" class="fileslink" ><img class="filesuffix" lay-image-click src="'+d.path[i].src+'"></a>'
                                }
                                return album.join('');
                            } catch (e) {
                                return '<a href="javascript:" class="fileslink" ><img class="filesuffix" lay-image-click src="'+d.path+'"></a>'
                            }
                        },title:'<?php echo __("车辆照片"); ?>'},
                    {field: 'code_path',width:120,templet:function(d) {
                            var album = [];
                            try {
                                d.code_path = JSON.parse(d.code_path);
                                for (var i in d.code_path) {
                                    album[i] = '<a href="javascript:" class="fileslink" ><img class="filesuffix" lay-image-click src="'+d.code_path[i].src+'"></a>'
                                }
                                return album.join('');
                            } catch (e) {
                                return '<a href="javascript:" class="fileslink" ><img class="filesuffix" lay-image-click src="'+d.code_path+'"></a>'
                            }
                        },title:'<?php echo __("牌照照片"); ?>'},
                    {field: 'brand',width:120,title:'<?php echo __("车辆品牌"); ?>'},
                    {field: 'type',width:120,title:'<?php echo __("车辆种类"); ?>',templet:function(d){
                            var resData = <?php echo json_encode(getDictionary(30)); ?>;
                            return d.type>-1?resData[d.type]:''
                        }},
                    {field: 'purpose',width:120,title:'<?php echo __("车辆用途"); ?>',templet:function(d){
                            var resData = <?php echo json_encode(getDictionary(42)); ?>;
                            return d.purpose>0?resData[d.purpose]:''
                        }},
                    {field: 'code_type',width:120,title:'<?php echo __("车辆牌照"); ?>',templet:function(d){
                            var resData = <?php echo json_encode(getDictionary(401)); ?>;
                            return d.code_type>0?resData[d.code_type]:''
                        }},
                    {field: 'create_time',width: 160,title:'<?php echo __("创建时间"); ?>'},
                    {field: 'create_by',width: 120,title:'<?php echo __("创建人"); ?>'},
                    {field: 'update_time',width: 160,title:'<?php echo __("修改时间"); ?>'},
                    {field: 'update_by',width: 120,title:'<?php echo __("修改人"); ?>'},
                    {align: 'center', toolbar: '#vehicleBar', width:200, fixed: 'right', title: '<?php echo __("操作"); ?>'},
                ]]
            })
        },2000);
        form.on('submit(allSearch)', function(data){
            let field = data.field;
            for (const key in field) {
                if (!field[key]) {
                    delete field[key];
                }
            }
            table.reloadData('lay-'+cur_id, {
                page: {curr: 1},
                where: field
            });
            return false; // 阻止表单跳转。如果需要表单跳转,去掉这段即可。
        });
        $(".tab-li").on('click', function(){
            cur_id = $(this).attr("data-id");
            $("#search").click();
        });
    })
</script>
<?php if($action == 'detail'): ?>

    <style>

        .layui-form-item{ margin-bottom: 5px; margin-right: 10px;}

        .layui-form-item label{min-height: 24px;}

        .layui-form-item label,.layui-form-item .layui-input-block{border:1px solid #e3e3e3; border-radius: 2px; background: #fafafa;display: flex;}

        .layui-form-item .layui-input-block{margin-left: 111px;}

        #renew {display: none;}

        .layui-form-mid{position: relative; left: -25px;}

    </style>

<?php endif; ?>

<script>

    function jsonToGetString(json) {

        let params = [];

        for (let key in json) {

            if (json.hasOwnProperty(key) && json[key] !== null && json[key] !== undefined && json[key] !== '') {

                params.push(encodeURIComponent(key) + '=' + encodeURIComponent(json[key]));

            }

        }

        return params.join('&');

    }

    layui.use(['layer','form','layer','table','admin','laydate'], function () {

        var form = layui.form;

        var $ = layui.jquery;

        var layer = layui.layer;

        var table = layui.table;

        var admin = layui.admin;

        var laydate = layui.laydate;

        setInterval(function(){

            admin.imageClick();

        },1000)

        

        form.on('select(xiafaChange)', function(data) {

          $('#xfcommunity_id').empty();

          $('#xfgrid_group').empty();

          $('#xfgrid').empty();

          search($)

          $.ajax({

                url: '<?php echo url("/Ajax/getCommunityList"); ?>',

                dataType: 'json',

                type: 'post',

                // data:{community_id:community_id},

                success: function (resData) {

                    $('#xfcommunity_id').append(new Option("请选择", ""));// 下拉菜单里添加元素

                    $.each(resData, function (index, value) {

                        $('#xfcommunity_id').append(new Option(value.community_name, value.id));// 下拉菜单里添加元素

                    });

                    form.render('select');

                }

            });

          // form.render('select');

        });



        form.on('select(ssptypeChange)', function(data) {



                  $('#xiafa').empty();

              search($)

              var type = data.value; // roomName 选中的省份名称

              $.ajax({

                url: '<?php echo url("/wxssp/ssptype"); ?>',

                dataType: 'json',

                type: 'post',

                data:{type:type},

                success: function (resData) {

                  console.log(resData);

                  $('#xiafa').append(new Option("请选择", ""));// 下拉菜单里添加元素

                  $.each(resData, function (index, value) {

                    $('#xiafa').append(new Option(value.name, value.id));// 下拉菜单里添加元素

                  });

                  form.render('select');

                }

              });

            });

        form.on('select(xfcommunityChange)', function(data) {

            $('#xfgrid_group').empty();

            $('#xfgrid').empty();

            $('#xiafa').empty();

            if ($("#street").is("*")) {

                $('#street').empty();

                $('#road').empty();

            }

            if($("#building").is("*")){

                $('#building').empty();

            }

            if($("#house").is("*")) {

                $('#house').empty();

            }

            if($("#unit").is("*")) {

                $('#unit').empty();

            }

            search($)

            var community_id = data.value; // roomName 选中的省份名称

            console.log(data.value)

            $.ajax({

                url: '<?php echo url("/Ajax/getGridGroupList"); ?>',

                dataType: 'json',

                type: 'post',

                data:{community_id:community_id},

                success: function (resData) {

                    $('#xfgrid_group').append(new Option("请选择", ""));// 下拉菜单里添加元素

                    $.each(resData, function (index, value) {

                        $('#xfgrid_group').append(new Option(value.grid_name, value.id));// 下拉菜单里添加元素

                    });

                    form.render('select');

                }

            });

        });

        form.on('select(xfgridGroupChange)', function(data) {

            $('#xfgrid').empty();

            if ($("#street").is("*")) {

                $('#street').empty();

                $('#road').empty();

            }

            if($("#building").is("*")){

                $('#building').empty();

            }

            if($("#house").is("*")) {

                $('#house').empty();

            }

            if($("#unit").is("*")) {

                $('#unit').empty();

            }

            search($)

            var grid_id = data.value; // roomName 选中的省份名称

            $.ajax({

                url: '<?php echo url("/Ajax/getGridList"); ?>',

                dataType: 'json',

                type: 'post',

                data:{grid_id:grid_id},

                success: function (resData) {

                    $('#xfgrid').append(new Option("请选择", ""));// 下拉菜单里添加元素

                    $.each(resData, function (index, value) {

                        $('#xfgrid').append(new Option(value.grid_name, value.id));// 下拉菜单里添加元素

                    });

                    form.render('select');

                }

            });

        });

        form.on('select(xfgridChange)', function(data) {

            search($)

            var grid_id = data.value; // roomName 选中的省份名称

            form.render('select');

            if ($("#street").is("*")) {

                $('#street').empty();

                $('#road').empty();

                $.ajax({

                    url: '<?php echo url("/Ajax/getGridStreetList"); ?>',

                    dataType: 'json',

                    type: 'post',

                    data:{grid_id:grid_id},

                    success: function (resData) {

                        var shtml = "<option value=''>请选择</option>";

                        var rhtml = "<option value=''>请选择</option>";



                        $.each(resData, function (index, value) {

                            var typeNo = value.typeNo === 1 ?'单号':'双号';

                            if(value.type== 2){

                                shtml += "<option min='"+value.start+"' max='"+value.end+"' value='"+value.id+"'>"+value.name+"("+typeNo+")("+value.start+"-"+value.end+")</option>";

                            }else{

                                rhtml += "<option min='"+value.start+"' max='"+value.end+"' value='"+value.id+"'>"+value.name+"("+typeNo+")("+value.start+"-"+value.end+")</option>";

                            }

                        });

                        $('#street').html(shtml);

                        $('#road').html(rhtml);

                        form.render('select');

                    }

                });

            }

            if($("#building").is("*")){

                $('#building').empty();

                $.ajax({

                    url: '<?php echo url("/Ajax/getBuildingList"); ?>',

                    dataType: 'json',

                    type: 'post',

                    data:{grid_id:grid_id},

                    success: function (resData) {

                        var bhtml = "<option value=''>请选择</option>";

                        $.each(resData, function (index, value) {

                            bhtml += "<option unit='"+value.unit+"' value='"+value.id+"'>"+value.building_name+"</option>";

                        });

                        $('#building').html(bhtml);

                        form.render('select');

                    }

                });

            }

            if($("#house").is("*")) {

                $('#house').empty();

            }

            if($("#unit").is("*")) {

                $('#unit').empty();

            }

            

        });

        form.on('select(communityChange)', function(data) {
console.log('绑定了')
            $('#grid_group').empty();

            $('#grid').empty();

            if ($("#street").is("*")) {

                $('#street').empty();

                $('#road').empty();

            }

            if($("#building").is("*")){

                $('#building').empty();

            }

            if($("#house").is("*")) {

                $('#house').empty();

            }

            if($("#unit").is("*")) {

                $('#unit').empty();

            }

            search($)

            var community_id = data.value; // roomName 选中的省份名称

            console.log(data.value)

            $.ajax({

                url: '<?php echo url("/Ajax/getGridGroupList"); ?>',

                dataType: 'json',

                type: 'post',

                data:{community_id:community_id},

                success: function (resData) {

                    $('#grid_group').append(new Option("请选择", ""));// 下拉菜单里添加元素

                    $.each(resData, function (index, value) {

                        $('#grid_group').append(new Option(value.grid_name, value.id));// 下拉菜单里添加元素

                    });

                    form.render('select');

                }

            });

        });

        form.on('select(gridGroupChange)', function(data) {

            $('#grid').empty();

            if ($("#street").is("*")) {

                $('#street').empty();

                $('#road').empty();

            }

            if($("#building").is("*")){

                $('#building').empty();

            }

            if($("#house").is("*")) {

                $('#house').empty();

            }

            if($("#unit").is("*")) {

                $('#unit').empty();

            }

            search($)

            var grid_id = data.value; // roomName 选中的省份名称

            $.ajax({

                url: '<?php echo url("/Ajax/getGridList"); ?>',

                dataType: 'json',

                type: 'post',

                data:{grid_id:grid_id},

                success: function (resData) {

                    $('#grid').append(new Option("请选择", ""));// 下拉菜单里添加元素

                    $.each(resData, function (index, value) {

                        $('#grid').append(new Option(value.grid_name, value.id));// 下拉菜单里添加元素

                    });

                    form.render('select');

                }

            });

        });

        form.on('select(gridChange)', function(data) {

            search($)

            var grid_id = data.value; // roomName 选中的省份名称

            form.render('select');

            if ($("#street").is("*")) {

                $('#street').empty();

                $('#road').empty();

                $.ajax({

                    url: '<?php echo url("/Ajax/getGridStreetList"); ?>',

                    dataType: 'json',

                    type: 'post',

                    data:{grid_id:grid_id},

                    success: function (resData) {

                        var shtml = "<option value=''>请选择</option>";

                        var rhtml = "<option value=''>请选择</option>";



                        $.each(resData, function (index, value) {

                            var typeNo = value.typeNo === 1 ?'单号':'双号';

                            if(value.type== 2){

                                shtml += "<option min='"+value.start+"' max='"+value.end+"' value='"+value.id+"-"+value.typeNo+"'>"+value.name+"("+typeNo+")("+value.start+"-"+value.end+")</option>";

                            }else{

                                rhtml += "<option min='"+value.start+"' max='"+value.end+"' value='"+value.id+"-"+value.typeNo+"'>"+value.name+"("+typeNo+")("+value.start+"-"+value.end+")</option>";

                            }

                        });

                        $('#street').html(shtml);

                        $('#road').html(rhtml);

                        form.render('select');

                    }

                });

            }

            if($("#building").is("*")){

                $('#building').empty();

                $.ajax({

                    url: '<?php echo url("/Ajax/getBuildingList"); ?>',

                    dataType: 'json',

                    type: 'post',

                    data:{grid_id:grid_id},

                    success: function (resData) {

                        var bhtml = "<option value=''>请选择</option>";

                        $.each(resData, function (index, value) {

                            bhtml += "<option unit='"+value.unit+"' value='"+value.id+"'>"+value.building_name+"</option>";

                        });

                        $('#building').html(bhtml);

                        form.render('select');

                    }

                });

            }

            if($("#house").is("*")) {

                $('#house').empty();

            }

            if($("#unit").is("*")) {

                $('#unit').empty();

            }

        });



        form.on('select(streetChange)', function() {

            search($)

            var streetObj = $('#street option:selected');

            var startObj = $("#street_start");

            var endObj = $("#street_end");

            startObj.val("");

            endObj.val("");

            var min = streetObj.attr('min');

            var max = streetObj.attr('max');

            startObj.attr('min',min);

            startObj.attr('max',max);

            endObj.attr('min',min);

            endObj.attr('max',max);

            form.render('input');

        });

        form.on('select(roadChange)', function() {

            search($)

            var roadObj = $('#road option:selected');

            var startObj = $("#road_start");

            var endObj = $("#road_end");

            startObj.val("");

            endObj.val("");

            var min = roadObj.attr('min');

            var max = roadObj.attr('max');

            startObj.attr('min',min);

            startObj.attr('max',max);

            endObj.attr('min',min);

            endObj.attr('max',max);

            form.render('input');

        });

        <?php if(($action == "add"|| $action == "edit")): ?>

        // 监听input输入事件

        $(".StreetNo").on('blur',function(e){

            search($);

            var obj =$(this);

            var id = $(this).attr("id");

            var min = parseInt(obj.attr('min'));

            var max = parseInt(obj.attr('max'));

            var value = parseInt(obj.val());

            if((value%2)!=(min%2)){

                var typeNmae = min%2==0?'双号':'单号';

                layer.msg('只能填写'+typeNmae+'！', {icon: 5});

                $(this).val("");

            }

            if((id =="road_start"||id=="road_end")&&$("#road_fictitious").is(":checked")){

                return false;

            }

            if((id =="street_start"||id=="street_end")&&$("#street_fictitious").is(":checked")){

                return false;

            }

            if(value<min||value>max){

                layer.msg('超过起止号范围('+min+'-'+max+')！', {icon: 5});

                $(this).val("");

            }

        });

        <?php endif; ?>

        form.on('select(buildingChange)', function(data) {

            $('#house').empty();

            search($)

            var building_id = data.value; // roomName 选中的省份名称

            $.ajax({

                url: '<?php echo url("/Ajax/getHouseList"); ?>',

                dataType: 'json',

                type: 'post',

                data:{building_id:building_id},

                success: function (resData) {

                    var hhtml = "<option value=''>请选择</option>";

                    $.each(resData, function (index, value) {

                        if(value.floor){

                            value.code = value.floor+"楼"+value.code;

                        }

                        if(value.unit){

                            value.code = value.unit+"单元"+value.code;

                        }

                        if(value.street){

                            value.code = value.code+'('+value.street+')';

                        }

                        hhtml += "<option value='"+value.id+"' area='"+value.area+"'>"+value.code+"</option>";

                    });

                    $('#house').html(hhtml);

                    form.render('select');

                }

            });

            if($("#unit").is("*")){

                $('#unit').empty();

                var buildingObj = $('#building option:selected');

                var unit = buildingObj.attr('unit');

                var uhtml = "<option value=''>请选择</option>";

                for(var i=1;i<=unit;i++){

                    uhtml += "<option value='"+i+"'>"+i+"单元</option>";

                }

                $('#unit').html(uhtml);

                form.render('select');

            }

        });



        form.on('select(houseChange)', function() {

            var houseObj = $('#house option:selected');

            if($("#area").is("*")) {

                var area = houseObj.attr('area');

                $("#area").val(area);

            }

        });

        form.on("submit(formExport)",function(){

            var formData = [];

            $('.layui-form input:not(:checkbox), .layui-form select').each(function() {

                var name = $(this).attr('name');

                var value = $(this).val();

                if(name&&value){

                    formData.push(name+"="+value);

                }

            });

            $('.layui-form input:checkbox').each(function() {

                if($(this).is(":checked")){

                    var name = $(this).attr('name');

                    formData.push(name+"=1");

                }

            });

            var url = "/admin/<?php echo htmlentities($controller); ?>/dataToExcel";

            const data_url = $(this).attr('data-url');

            if(data_url){

                url = data_url;

            }

            window.open(url+'?'+formData.join("&"));

            return false;

        })

        table.on('toolbar(lay-tableList)', function(obj){

            var checkStatus = table.checkStatus(obj.config.id);

            switch(obj.event){

                case 'Pending':

                    //获取选中的数据

                    var checkData = checkStatus.data;

                    if(checkData.length==0){

                        layer.msg('请至少勾选一条数据',{icon: 2});

                    }else{

                        layer.confirm('确定挂起选中的数据吗？', {icon: 3, title:'提示'}, function(index){

                            //这里可以发送批量删除请求

                            console.log(checkData);

                            var id = [];

                            checkData.forEach(function(item, index) {

                                id.push(item.id)

                            });

                            console.log(id)

                            $.post('/admin/Pending/add',

                                {id:id}, function(res){

                                    layer.close(index);

                                    if (res.code === 200) {

                                        layer.msg(res.msg);

                                        search($)

                                    }

                                    else {

                                        layui.show.error(res.msg);

                                    }



                                }, 'json');

                        });

                    }

                    break;

                case 'formExport':

                    var url = $("#formExport").attr('data-url')

                    //获取选中的数据

                    var checkData = checkStatus.data;

                    if(checkData.length==0){

                        layer.msg('请至少勾选一条数据',{icon: 2});

                    }else{

                        layer.confirm('确定导出选中的数据吗？', {icon: 3, title:'提示'}, function(index){

                            //这里可以发送批量删除请求

                            var id = [];

                            checkData.forEach(function(item, index) {

                                id.push(item.id)

                            });

                            $.post(url,

                                {id:id}, function(res){

                                    layer.close(index);

                                    if (res.code === 200) {

                                        location.href=res.url;

                                    }

                                    else {

                                        layui.show.error(res.msg);

                                    }



                                }, 'json');

                        });

                    }

                    break;

                case 'delete':

                    //获取选中的数据

                    var checkData = checkStatus.data;

                    var url = $("#delete-btn").attr('data-url')

                    if(checkData.length==0){

                        layer.msg('请至少勾选一条数据',{icon: 2});

                    }else{

                        layer.confirm('确定删除选中的数据吗？', {icon: 3, title:'提示'}, function(index){

                            var id = [];

                            checkData.forEach(function(item, index) {

                                id.push(item.id)

                            });

                            $.post(url,

                                {id:id}, function(res){

                                    layer.close(index);

                                    if (res.code === 200) {

                                        layer.msg(res.msg);

                                        search($)

                                    }

                                    else {

                                        layui.show.error(res.msg);

                                    }



                                }, 'json');

                        });

                    }

                    break;

            };

        });

        form.on('select(search)', function(data) {

            search($)

            form.render('select');

        });

        form.on('checkbox(search)', function(data) {

            search($)

            form.render('select');

        });

        $(".noNum").on("blur",function(){

            if(/\d/.test($(this).val())){

                $(this).val("");

                layui.show.error("非法输入");

            }

        });

        $(".noChinese").on("blur",function(){

            if(/[\u4e00-\u9fa5]/.test($(this).val())){

                $(this).val("");

                layui.show.error("非法输入");

            }

        });

        $('.more').on('click', function(){

            $(this).text()=='展开'?$(this).text('收起'):$(this).text('展开');

            $("#laytable-search").slideToggle();

        });

        var action = '<?php echo htmlentities($action); ?>';

        if(action=='detail'){

            convertFormToText($,form);

        }

        getBuilding($,form)

        dateRange(laydate,$)

    });

    function idCard(userCard, num) {

        var birthYear = userCard.substr(6, 4);

        var birthMonth = userCard.substr(10, 2);

        var birthDay = userCard.substr(12, 2);

        var birthday = birthYear + '-' + birthMonth + '-' + birthDay;



        //获取出生日期

        if (num == 1) {

            return birthday;

        }

        //获取性别

        if (num == 2) {

            var genderCode = parseInt(userCard.substr(16, 1));

            var gender = (genderCode % 2 === 0) ? '女' : '男';

            return gender;

        }

        //获取年龄

        if (num == 3) {

            // 计算年龄

            return getAge(birthday);

        }

    }

    function getAge(birthday) {

        var today = new Date();

        var birthDate = new Date(birthday);

        var age = today.getFullYear() - birthDate.getFullYear();

        var m = today.getMonth() - birthDate.getMonth();

        if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {

            age--;

        }

        return age;

    }

    function convertFormToText($,form) {

        $(".layui-form-select,.layui-form-switch,.layui-form-checkbox,.layui-upload-drag,.layui-btn-fluid,span,.layui-footer").remove();

        $('input:not(:checkbox), textarea').each(function() {

            var value = $(this).val();

            if($(this).attr('type')!='hidden'&&$(this).attr('hidden')!="hidden"&&!$(this).hasClass('layui-hide')&&!$(this).hasClass('layui-image')){

                if (value==''&&$(this).attr("name")!='file') {

                    value = '无'

                }

                var label = "<div class=\"layui-form-label\" style='width: auto; text-align:left;'>"+value+"</div>"

                $(this).after(label);

                $(this).remove();

            }

        });

        $('select').each(function() {

            var value = $(this).find("option:selected").text();

            if (value=='请选择') {

                value = '无'

            }

            var label = "<div class=\"layui-form-label\" style='width: auto; text-align:left;'>"+value+"</div>"

            $(this).after(label);

            $(this).remove();

        });

        $('input:checkbox').each(function() {

            var value = $(this).attr('title');

            var skin = $(this).attr("lay-skin");

            if(skin=="switch"){

                value = $(this).is(":checked")?'是':'否';

                var label = "<div class=\"layui-form-label\" style='width: auto; text-align:left;'>"+value+"</div>"

                $(this).after(label);

            }else if ($(this).is(":checked")) {

                var label = "<div class=\"layui-form-label\" style='width: auto; text-align:left;'>"+value+"</div>"

                $(this).after(label);

            }

            $(this).remove();

        });

        form.render();

    }

    function search($){

        if($("#search").is("*")){

            $("#search").click();

        }

    }

    function getBuilding($,form){

        let grid_id = $('#grid').val();

        if(grid_id>0&&$("#building").is("*")&&$("#building").val()==null){

            $('#building').empty();

            $.ajax({

                url: '<?php echo url("/Ajax/getBuildingList"); ?>',

                dataType: 'json',

                type: 'post',

                data:{grid_id:grid_id},

                success: function (resData) {

                    var bhtml = "<option value=''>请选择</option>";

                    $.each(resData, function (index, value) {

                        bhtml += "<option unit='"+value.unit+"' value='"+value.id+"'>"+value.building_name+"</option>";

                    });

                    $('#building').html(bhtml);

                    form.render('select');

                }

            });

        }

    }

    function dateRange(laydate,$){

        laydate.render({

            elem: '#start_date',

            type: 'date',

            max:'<?php echo date("Y-m-d"); ?>',

            done: function(value, date, endDate){

                var startDate = new Date($("#start_date").val());

                var endDate = new Date($("#end_date").val());

                if ( startDate>= endDate) {

                    $("#start_date").val("");

                    layui.show.error('结束日期必须大于开始日期');

                }

            }

        });

        laydate.render({

            elem: '#end_date',

            type: 'date',

            done: function(value, date, endDate){

                var startDate = new Date($("#start_date").val());

                var endDate = new Date($("#end_date").val());

                if ( startDate>= endDate) {

                    $("#end_date").val("");

                    layui.show.error('结束日期必须大于开始日期');

                }

            }

        });

    }

</script>