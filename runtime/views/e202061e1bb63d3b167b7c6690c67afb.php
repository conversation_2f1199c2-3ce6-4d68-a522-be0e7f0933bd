<?php /*a:5:{s:43:"E:\code\jsjsq\app/admin/view/house\add.html";i:1746711408;s:47:"E:\code\jsjsq\app/admin/view/public\header.html";i:1746518922;s:47:"E:\code\jsjsq\app/admin/view/public\static.html";i:1747036715;s:47:"E:\code\jsjsq\app/admin/view/public\footer.html";i:1746518922;s:47:"E:\code\jsjsq\app/admin/view/public\select.html";i:1747648922;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>SwiftAdmin 后台管理开发框架</title>
    <link href="/favicon.ico" rel="icon">
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link href="/static/system/layui/css/layui.css?v=<?php echo release(); ?>" rel="stylesheet" type="text/css"/>
    <link href="/static/system/css/style.css?v=<?php echo release(); ?>" rel="stylesheet" type="text/css"/>
    <link href="/static/system/layui/css/layphoto.css?v=<?php echo release(); ?>" rel="stylesheet" type="text/css"/>
    <!-- // 加载font-awesome图标 -->
    <link href="/static/system/layui/css/font-awesome.css?v=<?php echo release(); ?>" rel="stylesheet" type="text/css"/>
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>
<script>
    let app_Config = {
        app: "<?php echo htmlentities((isset($app) && ($app !== '')?$app:'/admin')); ?>",
        controller: "<?php echo htmlentities((isset($controller) && ($controller !== '')?$controller:'index')); ?>",
        action: "<?php echo htmlentities((isset($action) && ($action !== '')?$action:'index')); ?>",
        version: "<?php echo config('app.version'); ?>",
        api: "<?php echo config('app.api_url'); ?>",
    };

    let upload_chunkSize = <?php echo saenv('upload_chunk_size'); ?>;
</script>

<body>
<?php 
    $Communities = getCommunity();
    if($Communities[0]['id']==1){
    unset($Communities[0]);
    $Communities = array_values($Communities);
    }
    $community_id = empty($data['community_id'])?$Communities[0]['id']:$data['community_id'];
    $GridGroups = getGridGroup($community_id);
    $grid_group_id = empty($data['grid_group_id'])?$GridGroups[0]['id']:$data['grid_group_id'];
    $Grids = getGrid($grid_group_id);
    $grid_id = empty($data['grid_id'])?$Grids[0]['id']:$data['grid_id'];
    $Buildings = getBuilding($grid_id);
    $ids = [53,349,355,362,367,373,378];
    $dictionaryGroup = getDictionaryGroup($ids);
    $property_right = $dictionaryGroup[349];
    $usage_category = $dictionaryGroup[355];
    $house_type = $dictionaryGroup[362];
    $residence_nature = $dictionaryGroup[367];
    $rent_used = $dictionaryGroup[373];
    $rent_relation = $dictionaryGroup[378];
    $relation = $dictionaryGroup[53];
    $today = date('Y-m-d');
 ?>
<link href="/static/system/css/content.css" rel="stylesheet" type="text/css"/>
<div class="layui-fluid">
    <?php if($action != 'detail'): ?>
        <form class="layui-form layui-card">
            <?php else: ?>
            <div class="layui-form layui-card">
    <?php endif; ?>

    <div class="layui-card-body">
        <?php if($data['id'] > '0'): ?>
            <input type="text" name="id" value="<?php echo htmlentities($data['id']); ?>" hidden="">
            <?php else: ?>
            <input type="text" name="id" value="" hidden="">
        <?php endif; if($action == 'detail'): ?>
            <div id="layui-tab" id="tab_0" class="layui-tab layui-tab-brief">
                <ul class="layui-tab-title">
                    <li class="layui-this">基本信息</li>
                    <li  class="tab-li" data-id="family">家庭成员</li>
                </ul>
                <div class="layui-tab-content">
                    <div class="layui-tab-item layui-show " data-index="0">
        <?php endif; ?>
        <div class="layui-form-item layui-row">
            <div class="layui-col-md4 layui-grid-0" data-index="0">
                <div class="layui-form-item">
                    <div class="layui-input-block" style="margin-left: 0;">
                        <blockquote class="layui-elem-quote">房屋信息</blockquote>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">所属社区</label>
                    <div class="layui-input-block">
                        <select  name="community_id" lay-search lay-filter="communityChange" lay-verify="required">
                            <?php if(is_array($Communities) || $Communities instanceof \think\Collection || $Communities instanceof \think\Paginator): if( count($Communities)==0 ) : echo "" ;else: foreach($Communities as $key=>$vo): ?>
                                <option value="<?php echo htmlentities($vo['id']); ?>"
                                <?php if((isset($data['id']) && $data['id'])): if(in_array(($vo['id']), is_array($data['community_id'])?$data['community_id']:explode(',',$data['community_id']))): ?>selected<?php endif; ?>
                                <?php endif; ?>
                                ><?php echo htmlentities($vo['community_name']); ?></option>
                            <?php endforeach; endif; else: echo "" ;endif; ?>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><font color="red">*</font>所属网格组</label>
                    <div class="layui-input-block">
                        <select id="grid_group" name="grid_group_id" lay-search lay-filter="gridGroupChange" lay-verify="required">
                            <?php if(is_array($GridGroups) || $GridGroups instanceof \think\Collection || $GridGroups instanceof \think\Paginator): if( count($GridGroups)==0 ) : echo "" ;else: foreach($GridGroups as $key=>$vo): ?>
                                <option value="<?php echo htmlentities($vo['id']); ?>"
                                <?php if((isset($data['id']) && $data['id'])): if(in_array(($vo['id']), is_array($data['grid_group_id'])?$data['grid_group_id']:explode(',',$data['grid_group_id']))): ?>selected<?php endif; ?>
                                <?php endif; ?>
                                ><?php echo htmlentities($vo['grid_name']); ?></option>
                            <?php endforeach; endif; else: echo "" ;endif; ?>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><font color="red">*</font>所属网格</label>
                    <div class="layui-input-block">
                        <select id="grid" name="grid_id" lay-search lay-filter="gridChange" lay-verify="required">
                            <?php if(is_array($Grids) || $Grids instanceof \think\Collection || $Grids instanceof \think\Paginator): if( count($Grids)==0 ) : echo "" ;else: foreach($Grids as $key=>$vo): ?>
                                <option value="<?php echo htmlentities($vo['id']); ?>"
                                <?php if((isset($data['id']) && $data['id'])): if(in_array(($vo['id']), is_array($data['grid_id'])?$data['grid_id']:explode(',',$data['grid_id']))): ?>selected<?php endif; ?>
                                <?php endif; ?>
                                ><?php echo htmlentities($vo['grid_name']); ?></option>
                            <?php endforeach; endif; else: echo "" ;endif; ?>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><font color="red">*</font>所在建筑</label>
                    <div class="layui-input-block">
                        <select id="building" name="building_id" lay-search lay-verify="required">
                            <option value="">请选择</option>
                            <?php if(is_array($Buildings) || $Buildings instanceof \think\Collection || $Buildings instanceof \think\Paginator): if( count($Buildings)==0 ) : echo "" ;else: foreach($Buildings as $key=>$vo): ?>
                                <option value="<?php echo htmlentities($vo['id']); ?>"
                                <?php if((isset($data['id']) && $data['id'])): if(in_array(($vo['id']), is_array($data['building_id'])?$data['building_id']:explode(',',$data['building_id']))): ?>selected<?php endif; ?>
                                <?php endif; ?>
                                ><?php echo htmlentities($vo['building_name']); ?></option>
                            <?php endforeach; endif; else: echo "" ;endif; ?>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">街牌号</label>
                    <div class="layui-input-block"><input class="layui-input" name="street" placeholder="请输入"
                                                          lay-verify="" value="<?php echo htmlentities($data['street']); ?>"></div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><font color="red">*</font>所在单元</label>
                    <div class="layui-input-block">
                        <input class="layui-input" name="unit" placeholder="请输入"
                               lay-verify="number|required" value="<?php echo htmlentities($data['unit']); ?>">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><font color="red">*</font>所在楼层</label>
                    <div class="layui-input-block">
                        <input class="layui-input" name="floor" placeholder="请输入"
                               lay-verify="number|required" value="<?php echo htmlentities($data['floor']); ?>">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><font color="red">*</font>门牌号</label>
                    <div class="layui-input-block"><input class="layui-input" name="code" placeholder="请输入"
                                                          lay-verify="required" value="<?php echo htmlentities($data['code']); ?>"></div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><font color="red">*</font>产权性质</label>
                    <div class="layui-input-block">
                        <select name="property_right" lay-verify="required">

                            <option value="">请选择</option>
                            <?php if(is_array($property_right) || $property_right instanceof \think\Collection || $property_right instanceof \think\Paginator): if( count($property_right)==0 ) : echo "" ;else: foreach($property_right as $key=>$vo): ?>
                                <option value="<?php echo htmlentities($key); ?>"
                                <?php if((isset($data['id']) && $data['id'])): if(in_array(($key), is_array($data['property_right'])?$data['property_right']:explode(',',$data['property_right']))): ?>selected<?php endif; ?>
                                <?php endif; ?>
                                ><?php echo htmlentities($vo); ?></option>
                            <?php endforeach; endif; else: echo "" ;endif; ?>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><font color="red">*</font>使用类别</label>
                    <div class="layui-input-block">
                        <select name="usage_category" lay-verify="required">

                            <option value="">请选择</option>
                            <?php if(is_array($usage_category) || $usage_category instanceof \think\Collection || $usage_category instanceof \think\Paginator): if( count($usage_category)==0 ) : echo "" ;else: foreach($usage_category as $key=>$vo): ?>
                                <option value="<?php echo htmlentities($key); ?>"
                                <?php if((isset($data['id']) && $data['id'])): if(in_array(($key), is_array($data['usage_category'])?$data['usage_category']:explode(',',$data['usage_category']))): ?>selected<?php endif; ?>
                                <?php endif; ?>
                                ><?php echo htmlentities($vo); ?></option>
                            <?php endforeach; endif; else: echo "" ;endif; ?>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><font color="red">*</font>房屋类型</label>
                    <div class="layui-input-block">
                        <select name="house_type" id="house_type" lay-verify="required" lay-filter="checkRent">

                            <option value="">请选择</option>
                            <?php if(is_array($house_type) || $house_type instanceof \think\Collection || $house_type instanceof \think\Paginator): if( count($house_type)==0 ) : echo "" ;else: foreach($house_type as $key=>$vo): ?>
                                <option value="<?php echo htmlentities($key); ?>"
                                <?php if((isset($data['id']) && $data['id'])): if(in_array(($key), is_array($data['house_type'])?$data['house_type']:explode(',',$data['house_type']))): ?>selected<?php endif; ?>
                                <?php endif; ?>
                                
                                ><?php echo htmlentities($vo); ?></option>
                            <?php endforeach; endif; else: echo "" ;endif; ?>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label"><font color="red">*</font>住所性质</label>
                    <div class="layui-input-block">
                        <select name="residence_nature" lay-verify="required">

                            <option value="">请选择</option>
                            <?php if(is_array($residence_nature) || $residence_nature instanceof \think\Collection || $residence_nature instanceof \think\Paginator): if( count($residence_nature)==0 ) : echo "" ;else: foreach($residence_nature as $key=>$vo): ?>
                                <option value="<?php echo htmlentities($key); ?>"
                                <?php if((isset($data['id']) && $data['id'])): if(in_array(($key), is_array($data['residence_nature'])?$data['residence_nature']:explode(',',$data['residence_nature']))): ?>selected<?php endif; ?>
                                <?php endif; ?>
                                ><?php echo htmlentities($vo); ?></option>
                            <?php endforeach; endif; else: echo "" ;endif; ?>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">住房面积</label>
                    <div class="layui-input-block">
                        <input class="layui-input" name="area" placeholder="请输入"
                               lay-verify="number" value="<?php echo htmlentities($data['area']); ?>">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label">备注</label>
                    <div class="layui-input-block">
                        <input class="layui-input" name="details" placeholder="请输入"
                               lay-verify="" value="<?php echo htmlentities($data['details']); ?>">
                    </div>
                </div>
            </div>
            <div class="layui-col-md4 layui-grid-1" data-index="1">
                <div class="layui-form-item">
                    <div class="layui-input-block" style="margin-left: 0;">
                        <blockquote class="layui-elem-quote">产权人信息</blockquote>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label" style="width:106px;"><font color="red">* </font>产权人姓名</label>
                    <div class="layui-input-block" style="margin-left:136px"><input class="layui-input"
                                                                                    name="owner_name"
                                                                                    placeholder="请输入" required="1"
                                                                                    lay-verify="required"
                                                                                    value="<?php echo htmlentities($data['owner_name']); ?>">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label" style="width:106px;">产权人身份证号</label>
                    <div class="layui-input-block" style="margin-left:136px"><input class="layui-input"
                                                                                    name="owner_code"
                                                                                    placeholder="请输入"
                                                                                    lay-verify="identity"
                                                                                    value="<?php echo htmlentities($data['owner_code']); ?>">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label" style="width:106px;">产权人手机号码</label>
                    <div class="layui-input-block" style="margin-left:136px"><input class="layui-input"
                                                                                    name="owner_phone"
                                                                                    placeholder="请输入"
                                                                                    lay-verify="phone"
                                                                                    value="<?php echo htmlentities($data['owner_phone']); ?>">
                    </div>
                </div>
            </div>
            <div class="layui-col-md4 layui-grid-2" data-index="2">
                <div class="layui-form-item">
                    <div class="layui-input-block" style="margin-left: 0;">
                        <blockquote class="layui-elem-quote">承租信息</blockquote>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label" style="width:106px;">是否出租</label>
                    <div class="layui-input-block" id="rent-container" style="margin-left:136px"><input type="hidden" type="checkbox"
                                                                                    name="rent" value="0"/>
                        <input type="checkbox" id="rentButton" name="rent" value="1" title="是" lay-filter="rentChange"
                        <?php if($data['rent'] == '1'): ?> checked<?php endif; ?>
                        lay-skin="switch" />
                    </div>
                </div>
                <div id="rent-div" <?php if($data['rent'] != '1'): ?>style="display: none;"<?php endif; ?> >
                <div class="layui-form-item">
                    <label class="layui-form-label" style="width:106px;">承租开始日期</label>
                    <div class="layui-input-block" style="margin-left:136px">
                        <input class="layui-input datepicker" id="start_date"
                               lay-datetime="" autocomplete="off"
                               name="rent_start"
                               data-datetype="date"
                               data-dateformat="yyyy-MM-dd"
                               placeholder="yyyy-MM-dd"
                               data-maxvalue="<?php echo htmlentities($today); ?>"
                               value="<?php echo $data['rent_start']=='0000-00-00' ? '' : htmlentities($data['rent_start']); ?>">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label" style="width:106px;">承租结束日期</label>
                    <div class="layui-input-block" style="margin-left:136px"><input class="layui-input datepicker" id="end_date"
                                                                                    name="rent_end"
                                                                                    lay-datetime
                                                                                    data-datetype="date"
                                                                                    data-dateformat="yyyy-MM-dd"
                                                                                    placeholder="yyyy-MM-dd"
                                                                                    value="<?php echo $data['rent_end']=='0000-00-00' ? '' : htmlentities($data['rent_end']); ?>"></div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label" style="width:106px;">承租用途</label>
                    <div class="layui-input-block" style="margin-left:136px">
                        <select name="rent_used" id="rent_used" <?php if($data['rent'] == '1'): ?> lay-verify="required"<?php endif; ?>>
                        <?php if(is_array($rent_used) || $rent_used instanceof \think\Collection || $rent_used instanceof \think\Paginator): if( count($rent_used)==0 ) : echo "" ;else: foreach($rent_used as $key=>$vo): ?>
                            <option value="<?php echo htmlentities($key); ?>"
                            <?php if((isset($data['id']) && $data['id'])): if(in_array(($key), is_array($data['rent_used'])?$data['rent_used']:explode(',',$data['rent_used']))): ?>selected<?php endif; ?>
                            <?php endif; ?>
                            ><?php echo htmlentities($vo); ?></option>
                        <?php endforeach; endif; else: echo "" ;endif; ?>
                        </select>
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label" style="width:106px;">承租人姓名</label>
                    <div class="layui-input-block" style="margin-left:136px"><input class="layui-input" id="rent_name"
                                                                                    name="rent_name"
                                                                                    placeholder="请输入"
                        <?php if($data['rent'] == '1'): ?> lay-verify="required"<?php endif; ?>
                        value="<?php echo htmlentities($data['rent_name']); ?>"></div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label" style="width:106px;">承租人身份证号</label>
                    <div class="layui-input-block" style="margin-left:136px"><input class="layui-input"
                                                                                    name="rent_code"
                                                                                    placeholder="请输入"
                                                                                    lay-verify="identity"
                                                                                    value="<?php echo htmlentities($data['rent_code']); ?>"></div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label" style="width:106px;">承租人手机号</label>
                    <div class="layui-input-block" style="margin-left:136px"><input class="layui-input"
                                                                                    name="rent_phone"
                                                                                    placeholder="请输入"
                                                                                    lay-verify="phone"
                                                                                    value="<?php echo htmlentities($data['rent_phone']); ?>">
                    </div>
                </div>
                <div class="layui-form-item">
                    <label class="layui-form-label" style="width:106px;">与产权人关系</label>
                    <div class="layui-input-block" style="margin-left:136px">
                        <select name="relation">
                            <?php if(is_array($rent_relation) || $rent_relation instanceof \think\Collection || $rent_relation instanceof \think\Paginator): if( count($rent_relation)==0 ) : echo "" ;else: foreach($rent_relation as $key=>$vo): ?>
                                <option value="<?php echo htmlentities($key); ?>"
                                <?php if((isset($data['id']) && $data['id'])): if(in_array(($key), is_array($data['relation'])?$data['relation']:explode(',',$data['relation']))): ?>selected<?php endif; ?>
                                <?php endif; ?>
                                ><?php echo htmlentities($vo); ?></option>
                            <?php endforeach; endif; else: echo "" ;endif; ?>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php if($action == 'detail'): ?>
</div>
<div class="layui-tab-item " data-index="1">
    <table id="lay-family"></table>

</div>
</div>
<?php endif; ?>
<div class="layui-footer layui-form-footer">
    <button class="layui-btn layui-btn-primary" type="button" sa-event="closeDialog"><?php echo __('取消'); ?></button>
    <button class="layui-btn" type="button" lay-filter="submitIframe" lay-submit><?php echo __('提交'); ?></button>
</div>
<?php if($action != 'detail'): ?>
    </form>
    <?php else: ?>
    </div>
<?php endif; ?>
</div>

<!--// 加载Markdown编辑器-->
<link href="/static/js/markdown/cherry-markdown.css?v=<?php echo release(); ?>" rel="stylesheet" type="text/css"/>
<script src="/static/js/markdown/cherry-markdown.core.js?v=<?php echo release(); ?>"></script>

<!-- // 全局加载第三方JS -->
<script src="/static/js/tinymce/tinymce.min.js?v=<?php echo release(); ?>"></script>
<script src="/static/system/module/xmSelect/xmSelect.js?v=<?php echo release(); ?>"></script>
<!--// 加载CommonJS-->
<script src="/static/system/layui/layui.js?v=<?php echo release(); ?>"></script>
<script src="/static/system/js/common.js?v=<?php echo release(); ?>"></script>
<script src="/static/system/js/cascadata.js?v=<?php echo release(); ?>"></script>
<?php if($action == 'detail'): ?>



    <style>
        .layui-form-item {
            margin-bottom: 5px;
            margin-right: 10px;
        }



        .layui-form-item label {
            min-height: 24px;
        }



        .layui-form-item label,
        .layui-form-item .layui-input-block {
            border: 1px solid #e3e3e3;
            border-radius: 2px;
            background: #fafafa;
            display: flex;
        }



        .layui-form-item .layui-input-block {
            margin-left: 111px;
        }



        #renew {
            display: none;
        }



        .layui-form-mid {
            position: relative;
            left: -25px;
        }
    </style>



<?php endif; ?>



<script>



    function jsonToGetString(json) {



        let params = [];



        for (let key in json) {



            if (json.hasOwnProperty(key) && json[key] !== null && json[key] !== undefined && json[key] !== '') {



                params.push(encodeURIComponent(key) + '=' + encodeURIComponent(json[key]));



            }



        }



        return params.join('&');



    }



    layui.use(['layer', 'form', 'layer', 'table', 'admin', 'laydate'], function () {



        var form = layui.form;



        var $ = layui.jquery;



        var layer = layui.layer;



        var table = layui.table;



        var admin = layui.admin;



        var laydate = layui.laydate;



        setInterval(function () {



            admin.imageClick();



        }, 1000)







        form.on('select(xiafaChange)', function (data) {



            $('#xfcommunity_id').empty();



            $('#xfgrid_group').empty();



            $('#xfgrid').empty();



            search($)



            $.ajax({



                url: '<?php echo url("/Ajax/getCommunityList"); ?>',



                dataType: 'json',



                type: 'post',



                // data:{community_id:community_id},



                success: function (resData) {



                    $('#xfcommunity_id').append(new Option("请选择", ""));// 下拉菜单里添加元素



                    $.each(resData, function (index, value) {



                        $('#xfcommunity_id').append(new Option(value.community_name, value.id));// 下拉菜单里添加元素



                    });



                    form.render('select');



                }



            });



            // form.render('select');



        });







        form.on('select(ssptypeChange)', function (data) {







            $('#xiafa').empty();



            search($)



            var type = data.value; // roomName 选中的省份名称



            $.ajax({



                url: '<?php echo url("/wxssp/ssptype"); ?>',



                dataType: 'json',



                type: 'post',



                data: { type: type },



                success: function (resData) {



                    console.log(resData);



                    $('#xiafa').append(new Option("请选择", ""));// 下拉菜单里添加元素



                    $.each(resData, function (index, value) {



                        $('#xiafa').append(new Option(value.name, value.id));// 下拉菜单里添加元素



                    });



                    form.render('select');



                }



            });



        });



        form.on('select(xfcommunityChange)', function (data) {



            $('#xfgrid_group').empty();



            $('#xfgrid').empty();



            $('#xiafa').empty();



            if ($("#street").is("*")) {



                $('#street').empty();



                $('#road').empty();



            }



            if ($("#building").is("*")) {



                $('#building').empty();



            }



            if ($("#house").is("*")) {



                $('#house').empty();



            }



            if ($("#unit").is("*")) {



                $('#unit').empty();



            }



            search($)



            var community_id = data.value; // roomName 选中的省份名称



            console.log(data.value)



            $.ajax({



                url: '<?php echo url("/Ajax/getGridGroupList"); ?>',



                dataType: 'json',



                type: 'post',



                data: { community_id: community_id },



                success: function (resData) {



                    $('#xfgrid_group').append(new Option("请选择", ""));// 下拉菜单里添加元素



                    $.each(resData, function (index, value) {



                        $('#xfgrid_group').append(new Option(value.grid_name, value.id));// 下拉菜单里添加元素



                    });



                    form.render('select');



                }



            });



        });



        form.on('select(xfgridGroupChange)', function (data) {



            $('#xfgrid').empty();



            if ($("#street").is("*")) {



                $('#street').empty();



                $('#road').empty();



            }



            if ($("#building").is("*")) {



                $('#building').empty();



            }



            if ($("#house").is("*")) {



                $('#house').empty();



            }



            if ($("#unit").is("*")) {



                $('#unit').empty();



            }



            search($)



            var grid_id = data.value; // roomName 选中的省份名称



            $.ajax({



                url: '<?php echo url("/Ajax/getGridList"); ?>',



                dataType: 'json',



                type: 'post',



                data: { grid_id: grid_id },



                success: function (resData) {



                    $('#xfgrid').append(new Option("请选择", ""));// 下拉菜单里添加元素



                    $.each(resData, function (index, value) {



                        $('#xfgrid').append(new Option(value.grid_name, value.id));// 下拉菜单里添加元素



                    });



                    form.render('select');



                }



            });



        });



        form.on('select(xfgridChange)', function (data) {



            search($)



            var grid_id = data.value; // roomName 选中的省份名称



            form.render('select');



            if ($("#street").is("*")) {



                $('#street').empty();



                $('#road').empty();



                $.ajax({



                    url: '<?php echo url("/Ajax/getGridStreetList"); ?>',



                    dataType: 'json',



                    type: 'post',



                    data: { grid_id: grid_id },



                    success: function (resData) {



                        var shtml = "<option value=''>请选择</option>";



                        var rhtml = "<option value=''>请选择</option>";







                        $.each(resData, function (index, value) {



                            var typeNo = value.typeNo === 1 ? '单号' : '双号';



                            if (value.type == 2) {



                                shtml += "<option min='" + value.start + "' max='" + value.end + "' value='" + value.id + "'>" + value.name + "(" + typeNo + ")(" + value.start + "-" + value.end + ")</option>";



                            } else {



                                rhtml += "<option min='" + value.start + "' max='" + value.end + "' value='" + value.id + "'>" + value.name + "(" + typeNo + ")(" + value.start + "-" + value.end + ")</option>";



                            }



                        });



                        $('#street').html(shtml);



                        $('#road').html(rhtml);



                        form.render('select');



                    }



                });



            }



            if ($("#building").is("*")) {



                $('#building').empty();



                $.ajax({



                    url: '<?php echo url("/Ajax/getBuildingList"); ?>',



                    dataType: 'json',



                    type: 'post',



                    data: { grid_id: grid_id },



                    success: function (resData) {



                        var bhtml = "<option value=''>请选择</option>";



                        $.each(resData, function (index, value) {



                            bhtml += "<option unit='" + value.unit + "' value='" + value.id + "'>" + value.building_name + "</option>";



                        });



                        $('#building').html(bhtml);



                        form.render('select');



                    }



                });



            }



            if ($("#house").is("*")) {



                $('#house').empty();



            }



            if ($("#unit").is("*")) {



                $('#unit').empty();



            }







        });



        form.on('select(communityChange)', function (data) {



            $('#grid_group').empty();



            $('#grid').empty();



            if ($("#street").is("*")) {



                $('#street').empty();



                $('#road').empty();



            }



            if ($("#building").is("*")) {



                $('#building').empty();



            }



            if ($("#house").is("*")) {



                $('#house').empty();



            }



            if ($("#unit").is("*")) {



                $('#unit').empty();



            }



            search($)



            var community_id = data.value; // roomName 选中的省份名称



            console.log(data.value)



            $.ajax({



                url: '<?php echo url("/Ajax/getGridGroupList"); ?>',



                dataType: 'json',



                type: 'post',



                data: { community_id: community_id },



                success: function (resData) {



                    $('#grid_group').append(new Option("请选择", ""));// 下拉菜单里添加元素



                    $.each(resData, function (index, value) {



                        $('#grid_group').append(new Option(value.grid_name, value.id));// 下拉菜单里添加元素



                    });



                    form.render('select');



                }



            });



        });



        form.on('select(gridGroupChange)', function (data) {



            $('#grid').empty();



            if ($("#street").is("*")) {



                $('#street').empty();



                $('#road').empty();



            }



            if ($("#building").is("*")) {



                $('#building').empty();



            }



            if ($("#house").is("*")) {



                $('#house').empty();



            }



            if ($("#unit").is("*")) {



                $('#unit').empty();



            }



            search($)



            var grid_id = data.value; // roomName 选中的省份名称



            $.ajax({



                url: '<?php echo url("/Ajax/getGridList"); ?>',



                dataType: 'json',



                type: 'post',



                data: { grid_id: grid_id },



                success: function (resData) {



                    $('#grid').append(new Option("请选择", ""));// 下拉菜单里添加元素



                    $.each(resData, function (index, value) {



                        $('#grid').append(new Option(value.grid_name, value.id));// 下拉菜单里添加元素



                    });



                    form.render('select');



                }



            });



        });



        form.on('select(gridChange)', function (data) {



            search($)



            var grid_id = data.value; // roomName 选中的省份名称



            form.render('select');



            if ($("#street").is("*")) {



                $('#street').empty();



                $('#road').empty();



                $.ajax({



                    url: '<?php echo url("/Ajax/getGridStreetList"); ?>',



                    dataType: 'json',



                    type: 'post',



                    data: { grid_id: grid_id },



                    success: function (resData) {



                        var shtml = "<option value=''>请选择</option>";



                        var rhtml = "<option value=''>请选择</option>";







                        $.each(resData, function (index, value) {



                            var typeNo = value.typeNo === 1 ? '单号' : '双号';



                            if (value.type == 2) {



                                shtml += "<option min='" + value.start + "' max='" + value.end + "' value='" + value.id + "'>" + value.name + "(" + typeNo + ")(" + value.start + "-" + value.end + ")</option>";



                            } else {



                                rhtml += "<option min='" + value.start + "' max='" + value.end + "' value='" + value.id + "'>" + value.name + "(" + typeNo + ")(" + value.start + "-" + value.end + ")</option>";



                            }



                        });



                        $('#street').html(shtml);



                        $('#road').html(rhtml);



                        form.render('select');



                    }



                });



            }



            if ($("#building").is("*")) {



                $('#building').empty();



                $.ajax({



                    url: '<?php echo url("/Ajax/getBuildingList"); ?>',



                    dataType: 'json',



                    type: 'post',



                    data: { grid_id: grid_id },



                    success: function (resData) {



                        var bhtml = "<option value=''>请选择</option>";



                        $.each(resData, function (index, value) {



                            bhtml += "<option unit='" + value.unit + "' value='" + value.id + "'>" + value.building_name + "</option>";



                        });



                        $('#building').html(bhtml);



                        form.render('select');



                    }



                });



            }



            if ($("#house").is("*")) {



                $('#house').empty();



            }



            if ($("#unit").is("*")) {



                $('#unit').empty();



            }



        });







        form.on('select(streetChange)', function () {



            search($)



            var streetObj = $('#street option:selected');



            var startObj = $("#street_start");



            var endObj = $("#street_end");



            startObj.val("");



            endObj.val("");



            var min = streetObj.attr('min');



            var max = streetObj.attr('max');



            startObj.attr('min', min);



            startObj.attr('max', max);



            endObj.attr('min', min);



            endObj.attr('max', max);



            form.render('input');



        });



        form.on('select(roadChange)', function () {

            search($)

            var roadObj = $('#road option:selected');

            var startObj = $("#road_start");

            var endObj = $("#road_end");

            startObj.val("");

            endObj.val("");

            var min = roadObj.attr('min');

            var max = roadObj.attr('max');

            startObj.attr('min', min);

            startObj.attr('max', max);

            endObj.attr('min', min);

            endObj.attr('max', max);

            
            form.render('input');

        });



        <?php if(($action == "add" || $action == "edit")): ?>

            // 监听input输入事件
            $(".StreetNo").on('blur', function (e) {

                search($);

                var obj = $(this);
                console.log("StreetNo obj>>>>",obj)
                var id = $(this).attr("id");

                var min = parseInt(obj.attr('min'));

                var max = parseInt(obj.attr('max'));

                var value = parseInt(obj.val());

                console.log('road>>>>>',value,min);
                if (value && (value % 2) != (min % 2)) {

                    var typeNmae = min % 2 == 0 ? '双号' : '单号';

                    layer.msg('只能填写' + typeNmae + '！', { icon: 5 });

                    $(this).val("");
                }

                if ((id == "road_start" || id == "road_end") && $("#road_fictitious").is(":checked")) {

                    return false;
                }

                if ((id == "street_start" || id == "street_end") && $("#street_fictitious").is(":checked")) {

                    return false;
                }

                if (value && (value < min || value > max)) {

                    layer.msg('超过起止号范围(' + min + '-' + max + ')！', { icon: 5 });

                    $(this).val("");
                }
            });


        <?php endif; ?>



            form.on('select(buildingChange)', function (data) {



                $('#house').empty();



                search($)



                var building_id = data.value; // roomName 选中的省份名称



                $.ajax({



                    url: '<?php echo url("/Ajax/getHouseList"); ?>',



                    dataType: 'json',



                    type: 'post',



                    data: { building_id: building_id },



                    success: function (resData) {



                        var hhtml = "<option value=''>请选择</option>";



                        $.each(resData, function (index, value) {



                            if (value.floor) {



                                value.code = value.floor + "楼" + value.code;



                            }



                            if (value.unit) {



                                value.code = value.unit + "单元" + value.code;



                            }



                            if (value.street) {



                                value.code = value.code + '(' + value.street + ')';



                            }



                            hhtml += "<option value='" + value.id + "' area='" + value.area + "'>" + value.code + "</option>";



                        });



                        $('#house').html(hhtml);



                        form.render('select');



                    }



                });



                if ($("#unit").is("*")) {



                    $('#unit').empty();



                    var buildingObj = $('#building option:selected');



                    var unit = buildingObj.attr('unit');



                    var uhtml = "<option value=''>请选择</option>";



                    for (var i = 1; i <= unit; i++) {



                        uhtml += "<option value='" + i + "'>" + i + "单元</option>";



                    }



                    $('#unit').html(uhtml);



                    form.render('select');



                }



            });







        form.on('select(houseChange)', function () {



            var houseObj = $('#house option:selected');



            if ($("#area").is("*")) {



                var area = houseObj.attr('area');



                $("#area").val(area);



            }



        });



        form.on("submit(formExport)", function () {



            var formData = [];



            $('.layui-form input:not(:checkbox), .layui-form select').each(function () {



                var name = $(this).attr('name');



                var value = $(this).val();



                if (name && value) {



                    formData.push(name + "=" + value);



                }



            });



            $('.layui-form input:checkbox').each(function () {



                if ($(this).is(":checked")) {



                    var name = $(this).attr('name');



                    formData.push(name + "=1");



                }



            });



            var url = "/admin/<?php echo htmlentities($controller); ?>/dataToExcel";



            const data_url = $(this).attr('data-url');



            if (data_url) {



                url = data_url;



            }



            window.open(url + '?' + formData.join("&"));



            return false;



        })



        table.on('toolbar(lay-tableList)', function (obj) {



            var checkStatus = table.checkStatus(obj.config.id);



            switch (obj.event) {



                case 'Pending':



                    //获取选中的数据



                    var checkData = checkStatus.data;



                    if (checkData.length == 0) {



                        layer.msg('请至少勾选一条数据', { icon: 2 });



                    } else {



                        layer.confirm('确定挂起选中的数据吗？', { icon: 3, title: '提示' }, function (index) {



                            //这里可以发送批量删除请求



                            console.log(checkData);



                            var id = [];



                            checkData.forEach(function (item, index) {



                                id.push(item.id)



                            });



                            console.log(id)



                            $.post('/admin/Pending/add',



                                { id: id }, function (res) {



                                    layer.close(index);



                                    if (res.code === 200) {



                                        layer.msg(res.msg);



                                        search($)



                                    }



                                    else {



                                        layui.show.error(res.msg);



                                    }







                                }, 'json');



                        });



                    }



                    break;



                case 'formExport':



                    var url = $("#formExport").attr('data-url')



                    //获取选中的数据



                    var checkData = checkStatus.data;



                    if (checkData.length == 0) {



                        layer.msg('请至少勾选一条数据', { icon: 2 });



                    } else {



                        layer.confirm('确定导出选中的数据吗？', { icon: 3, title: '提示' }, function (index) {



                            //这里可以发送批量删除请求



                            var id = [];



                            checkData.forEach(function (item, index) {



                                id.push(item.id)



                            });



                            $.post(url,



                                { id: id }, function (res) {



                                    layer.close(index);



                                    if (res.code === 200) {



                                        location.href = res.url;



                                    }



                                    else {



                                        layui.show.error(res.msg);



                                    }







                                }, 'json');



                        });



                    }



                    break;



                case 'delete':



                    //获取选中的数据



                    var checkData = checkStatus.data;



                    var url = $("#delete-btn").attr('data-url')



                    if (checkData.length == 0) {



                        layer.msg('请至少勾选一条数据', { icon: 2 });



                    } else {



                        layer.confirm('确定删除选中的数据吗？', { icon: 3, title: '提示' }, function (index) {



                            var id = [];



                            checkData.forEach(function (item, index) {



                                id.push(item.id)



                            });



                            $.post(url,



                                { id: id }, function (res) {



                                    layer.close(index);



                                    if (res.code === 200) {



                                        layer.msg(res.msg);



                                        search($)



                                    }



                                    else {



                                        layui.show.error(res.msg);



                                    }







                                }, 'json');



                        });



                    }



                    break;



            };



        });



        form.on('select(search)', function (data) {



            search($)



            form.render('select');



        });



        form.on('checkbox(search)', function (data) {



            search($)



            form.render('select');



        });



        $(".noNum").on("blur", function () {



            if (/\d/.test($(this).val())) {



                $(this).val("");



                layui.show.error("非法输入");



            }



        });



        $(".noChinese").on("blur", function () {



            if (/[\u4e00-\u9fa5]/.test($(this).val())) {



                $(this).val("");



                layui.show.error("非法输入");



            }



        });



        $('.more').on('click', function () {



            $(this).text() == '展开' ? $(this).text('收起') : $(this).text('展开');



            $("#laytable-search").slideToggle();



        });



        var action = '<?php echo htmlentities($action); ?>';



        if (action == 'detail') {



            convertFormToText($, form);



        }



        getBuilding($, form)



        dateRange(laydate, $)



    });



    function idCard(userCard, num) {



        var birthYear = userCard.substr(6, 4);



        var birthMonth = userCard.substr(10, 2);



        var birthDay = userCard.substr(12, 2);



        var birthday = birthYear + '-' + birthMonth + '-' + birthDay;







        //获取出生日期



        if (num == 1) {



            return birthday;



        }



        //获取性别



        if (num == 2) {



            var genderCode = parseInt(userCard.substr(16, 1));



            var gender = (genderCode % 2 === 0) ? '女' : '男';



            return gender;



        }



        //获取年龄



        if (num == 3) {



            // 计算年龄



            return getAge(birthday);



        }



    }



    function getAge(birthday) {



        var today = new Date();



        var birthDate = new Date(birthday);



        var age = today.getFullYear() - birthDate.getFullYear();



        var m = today.getMonth() - birthDate.getMonth();



        if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {



            age--;



        }



        return age;



    }



    function convertFormToText($, form) {



        $(".layui-form-select,.layui-form-switch,.layui-form-checkbox,.layui-upload-drag,.layui-btn-fluid,span,.layui-footer").remove();



        $('input:not(:checkbox), textarea').each(function () {



            var value = $(this).val();



            if ($(this).attr('type') != 'hidden' && $(this).attr('hidden') != "hidden" && !$(this).hasClass('layui-hide') && !$(this).hasClass('layui-image')) {



                if (value == '' && $(this).attr("name") != 'file') {



                    value = '无'



                }



                var label = "<div class=\"layui-form-label\" style='width: auto; text-align:left;'>" + value + "</div>"



                $(this).after(label);



                $(this).remove();



            }



        });



        $('select').each(function () {



            var value = $(this).find("option:selected").text();



            if (value == '请选择') {



                value = '无'



            }



            var label = "<div class=\"layui-form-label\" style='width: auto; text-align:left;'>" + value + "</div>"



            $(this).after(label);



            $(this).remove();



        });



        $('input:checkbox').each(function () {



            var value = $(this).attr('title');



            var skin = $(this).attr("lay-skin");



            if (skin == "switch") {



                value = $(this).is(":checked") ? '是' : '否';



                var label = "<div class=\"layui-form-label\" style='width: auto; text-align:left;'>" + value + "</div>"



                $(this).after(label);



            } else if ($(this).is(":checked")) {



                var label = "<div class=\"layui-form-label\" style='width: auto; text-align:left;'>" + value + "</div>"



                $(this).after(label);



            }



            $(this).remove();



        });



        form.render();



    }



    function search($) {



        if ($("#search").is("*")) {



            $("#search").click();



        }



    }



    function getBuilding($, form) {



        let grid_id = $('#grid').val();



        if (grid_id > 0 && $("#building").is("*") && $("#building").val() == null) {



            $('#building').empty();



            $.ajax({



                url: '<?php echo url("/Ajax/getBuildingList"); ?>',



                dataType: 'json',



                type: 'post',



                data: { grid_id: grid_id },



                success: function (resData) {



                    var bhtml = "<option value=''>请选择</option>";



                    $.each(resData, function (index, value) {



                        bhtml += "<option unit='" + value.unit + "' value='" + value.id + "'>" + value.building_name + "</option>";



                    });



                    $('#building').html(bhtml);



                    form.render('select');



                }



            });



        }



    }



    function dateRange(laydate, $) {



        laydate.render({



            elem: '#start_date',



            type: 'date',



            max: '<?php echo date("Y-m-d"); ?>',



            done: function (value, date, endDate) {



                var startDate = new Date($("#start_date").val());



                var endDate = new Date($("#end_date").val());



                if (startDate >= endDate) {



                    $("#start_date").val("");



                    layui.show.error('结束日期必须大于开始日期');



                }



            }



        });



        laydate.render({



            elem: '#end_date',



            type: 'date',



            done: function (value, date, endDate) {



                var startDate = new Date($("#start_date").val());



                var endDate = new Date($("#end_date").val());



                if (startDate >= endDate) {



                    $("#end_date").val("");



                    layui.show.error('结束日期必须大于开始日期');



                }



            }



        });



    }



</script>
<script type="text/html" id="tableBar_family">
    <button class="layui-btn layui-btn-primary layui-btn-xs" sa-event="tabs"
            data-title="<?php echo __('查看家庭成员信息'); ?>" data-area="100%,100%"
            data-maxmin="true" data-url="<?php echo url('/Person/detail'); ?>?id={{d.id}}" >
        <?php echo __('查看'); ?>
    </button>
</script>
<script>
    layui.use(['layer','form','laydate'], function () {
        var form = layui.form;
        var $ = layui.jquery;
        var laydate = layui.laydate;
        var table = layui.table;

        // 增加房屋类型，与是否出租的关联
        var house_type = $('select[name="house_type"]').val();
        var disabledRent = false;
        if(house_type==2) {
            disabledRent = false
        } else {
            disabledRent = true
        }
        $('input[name="rent"]').attr("disabled", disabledRent);

        form.on('select(checkRent)', function(data){
            var house_type = $('select[name="house_type"]').val();
            if(house_type==2) {
                disabledRent = false
            } else {
                disabledRent = true
                $("#rentButton").removeAttr('checked');
                $("#rent-div").hide();
            }

            $('input[name="rent"]').attr("disabled", disabledRent);
            if(disabledRent) {
                $("#rentButton").removeAttr('checked');
                $("#rent-div").hide();
            } else {
                $("#rentButton").removeAttr('disabled');
            }
            form.render();
        })

        $('#rent-container').on("click", function(){
            if($('#rentButton').prop('disabled')) {
                layer.alert('房屋类型不是租赁，无法出租')
            }
        })
        // end


        form.on('switch(rentChange)', function(data){
            if(data.elem.checked){
                $('#rent_used').attr("lay-verify","required");
                $("#rent_name").attr("lay-verify","required");
                $("#rent-div").show();
            }else{
                $('#rent-div input, #rent-div select').each(function() {
                    $(this).val("");
                });
                $('#rent_used').attr("lay-verify","");
                $("#rent_name").attr("lay-verify","");
                $("#rent-div").hide();
            }
            form.render();
        });
        $(".tab-li").on("click",function(){
            table.render({
                elem: "#lay-family"
                ,url: "<?php echo url('/Person/index',['house_id'=>$data['id']]); ?>"
                ,cellMinWidth: 60
                ,page: true
                ,limit: 50,limits: [50, 100, 200, 500]
                ,height: 'full-150' //设置表格高度，减去固定的底部高度
                ,cols: [[
                    {field:'name',align: 'left',width:180,title:'<?php echo __("姓名"); ?>', templet: function(d){
                            return d.name;
                        }},
                    {field:'relation',title:'<?php echo __("与户主关系"); ?>',templet:function(d){
                            var resData = <?php echo json_encode($relation); ?>;
                            return d.relation>0?resData[d.relation]:''
                        }},
                    {field:'id_code',title:'<?php echo __("年龄"); ?>',width:50,templet:function(d){
                            return d.age
                        }},
                    {
                        field: 'sex', title: '<?php echo __("性别"); ?>', width: 50, templet: function (d) {
                            return d.sex == 1 ? '男' : '女'
                        }
                    },
                    {
                        field: 'community_id',width: 320, minWidth: 300, title: '<?php echo __("所在区域"); ?>', templet: function (d) {
                            return d.community_id > 0 ? d.community_name + "," + d.grid_group_name + "," + d.grid_name : ''
                        }
                    },
                    {field: 'building_name',width:160, title: '<?php echo __("所在房屋"); ?>'},
                    {field:'address',width: 200, title: '<?php echo __("房屋地址"); ?>'},
                    {field: 'create_time',width:160, title: '<?php echo __("创建时间"); ?>'},
                    {field: 'create_by',width: 120, title: '<?php echo __("创建人"); ?>'},
                    {field: 'update_time',width: 160, title: '<?php echo __("修改时间"); ?>'},
                    {field: 'update_by',width: 120, title: '<?php echo __("修改人"); ?>'},
                    {align: 'center', toolbar: '#tableBar_family', width: 100, fixed: 'right', title: '<?php echo __("操作"); ?>'},
                ]]
            })
        });
    });
</script>
