<?php /*a:3:{s:59:"E:\code\jsjsq\app/admin/view/system\admin_notice\index.html";i:1746518922;s:47:"E:\code\jsjsq\app/admin/view/public\header.html";i:1746518922;s:47:"E:\code\jsjsq\app/admin/view/public\footer.html";i:1746518922;}*/ ?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>SwiftAdmin 后台管理开发框架</title>
    <link href="/favicon.ico" rel="icon">
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link href="/static/system/layui/css/layui.css?v=<?php echo release(); ?>" rel="stylesheet" type="text/css"/>
    <link href="/static/system/css/style.css?v=<?php echo release(); ?>" rel="stylesheet" type="text/css"/>
    <link href="/static/system/layui/css/layphoto.css?v=<?php echo release(); ?>" rel="stylesheet" type="text/css"/>
    <!-- // 加载font-awesome图标 -->
    <link href="/static/system/layui/css/font-awesome.css?v=<?php echo release(); ?>" rel="stylesheet" type="text/css"/>
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
</head>
<script>
    let app_Config = {
        app: "<?php echo htmlentities((isset($app) && ($app !== '')?$app:'/admin')); ?>",
        controller: "<?php echo htmlentities((isset($controller) && ($controller !== '')?$controller:'index')); ?>",
        action: "<?php echo htmlentities((isset($action) && ($action !== '')?$action:'index')); ?>",
        version: "<?php echo config('app.version'); ?>",
        api: "<?php echo config('app.api_url'); ?>",
    };

    let upload_chunkSize = <?php echo saenv('upload_chunk_size'); ?>;
</script>

<body>
<!--<link href="/static/system/css/message.css" rel="stylesheet" type="text/css" />-->
<style>
    #msgType {
        padding-bottom: 16px;
    }

    #msgType li {
        border-radius: 3px;
        cursor: pointer;
        margin-bottom: 5px;
        padding: 8px 8px 8px 16px;
    }

    #msgType li i {
        margin-right: 10px;
    }

    #msgType li.active, #msgType li:hover {
        color: #1890ff;
        background-color: #e6f7ff;
    }
</style>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md2">

            <div class="layui-card">
                <div class="layui-card-header layadmin-card-header-auto">
                    <h5><?php echo __('我的信箱'); ?></h5>
                </div>
                <div id="msgType" class="layui-card-body">
                    <li class="active" data-event="todo">
                        <i class="layui-icon fa-tags"></i>
                        <span>待办项</span>
                    </li>
                    <li data-event="send">
                        <i class="layui-icon fa-rocket"></i>
                        <span>已发送</span>
                    </li>
                    <li data-event="notice">
                        <i class="layui-icon fa-volume-up"></i>
                        <span>系统通知</span>
                    </li>
                    <li data-event="message">
                        <i class="layui-icon fa-comment"></i>
                        <span>我的私信</span>
                    </li>
                </div>
            </div>
        </div>

        <div class="layui-col-md10">
            <!-- // 展示数据 -->
            <div class="layui-card">
                <!-- // 默认操作按钮 -->
                <div class="layui-card-header layadmin-card-header-auto ">
                    <div class="layui-form">
                        <div class="layui-form-item">

                            <div class="layui-inline">
                                <div class="layui-input-inline ">
                                    <input name="title" class="layui-input" type="text"
                                           placeholder="<?php echo __('请输入消息标题'); ?>"/>
                                </div>
                            </div>

                            <div class="layui-inline">
                                <!-- // 默认搜索 -->
                                <button class="layui-btn icon-btn" lay-filter="formSearch" lay-submit><i
                                        class="layui-icon layui-icon-search"></i><?php echo __('搜索'); ?>
                                </button>
                                <!-- // 打开添加页面 -->
                                <button class="layui-btn icon-btn" lay-open="" data-title="<?php echo __('发送私信'); ?>"
                                        data-area="36%,39%" data-url="<?php echo url('system/AdminNotice/add'); ?>">
                                    <i class="layui-icon layui-icon-add-1"></i><?php echo __('发私信'); ?>
                                </button>
                                <?php if((has_admin_auth(url("/system/AdminNotice/addBat")))): ?>
                                <button class="layui-btn icon-btn" lay-open="" data-title="<?php echo __('群发消息'); ?>"
                                        data-area="50%,69%" data-url="<?php echo url('system/AdminNotice/addBat'); ?>">
                                    <i class="layui-icon layui-icon-add-1"></i><?php echo __('群发消息'); ?>
                                </button>
                                <?php endif; ?>
                                <button class="layui-btn layui-btn-danger" lay-batch data-table="lay-tableList"
                                        data-url="/user/batchMessage?type=read"><?php echo __('全部已读'); ?>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- // 创建数据实例 -->
                <table id="lay-tableList" lay-filter="lay-tableList"></table>
            </div>

        </div>

    </div>
</div>

<!-- // 列表编辑框 -->
<script type="text/html" id="tableBar">
    <a class="layui-table-text" data-title="<?php echo __('查看'); ?>" data-url="<?php echo url('/system/AdminNotice/read'); ?>?id={{d.id}}"
       data-area="39%,46%" lay-event="edit"><?php echo __('查看'); ?></a>
    <div class="layui-divider layui-divider-vertical"></div>
    <a class="layui-table-text" data-url="<?php echo url('/system/AdminNotice/del'); ?>?id={{d.id}}" lay-event="del"><?php echo __('删除'); ?></a>
</script>

<script type="text/html" id="setRowChecked">
    <div class="layui-btn-container ">
        <button class="layui-btn layui-btn-normal layui-btn-xs" lay-event="all"><?php echo __('全部'); ?></button>
        <button class="layui-btn layui-btn-primary layui-btn-xs" lay-event="1"><?php echo __('已读'); ?></button>
        <button class="layui-btn layui-btn-primary layui-btn-xs" lay-event="0"><?php echo __('未读'); ?></button>
    </div>
</script>

<!--// 加载CommonJS-->
<script src="/static/system/layui/layui.js?v=<?php echo release(); ?>"></script>
<script src="/static/system/js/common.js?v=<?php echo release(); ?>"></script>
<script src="/static/system/js/cascadata.js?v=<?php echo release(); ?>"></script>

<script>
    layui.use(['table', 'jquery',], function () {
        let $ = layui.jquery;
        let table = layui.table;        // 表格

        // 定义表格URL
        let tableURL = "<?php echo url('/system/AdminNotice/index'); ?>";

        // 基础表格
        table.render({
            elem: "#lay-tableList"
            , url: tableURL
            , page: true
            , toolbar: '#setRowChecked'
            , cols: [[
                {type: 'checkbox'},
                {
                    field: 'title', align: 'left',  templet: function (e) {
                        let title = '<a class="layui-table-text';
                        title += '" lay-open data-title="查看消息" data-url="<?php echo url('/system/AdminNotice/read'); ?>?id=' + e.id + '" data-area="600px, 390px" >';
                        if (e.status === 0) {
                            title += '<i class="layui-icon fa-envelope"></i> ';
                        } else {
                            title += '<i class="layui-icon fa-envelope-o"></i> ';
                        }
                        title += e.title;
                        title += '</a>';
                        return title;
                    }, title: '<?php echo __("标题"); ?>'
                },
                {field: 'send_ip', align: 'center', width: 180, title: '<?php echo __("发送者 IP"); ?>'},
                {field: 'create_time', align: 'center', width: 180, title: '<?php echo __("创建时间"); ?>'},
                {align: 'center', toolbar: '#tableBar', width: 160, title: '<?php echo __("操作"); ?>'},
            ]]
        })

        $('#msgType li').click(function () {
            let event = $(this).data('event');
            table.reloadData('lay-tableList', {
                where: {type: event}
            }, true);
            $(this).addClass('active').siblings().removeClass('active');
        });

        // 列表双击
        table.on('rowDouble(lay-tableList)', function (obj) {
            let data = obj.data;
        });

        // 头工具栏事件
        table.on('toolbar(lay-tableList)', function (obj) {
            let checkBtn = null;
            switch (obj.event) {
                case 'all':
                case '0':
                case '1':
                    checkBtn = $(this);
                    break;
            }
            table.reloadData('lay-tableList', {
                where: {status: obj.event}
            },true);
            $('.layui-btn-container .layui-btn').removeClass('layui-btn-normal').addClass('layui-btn-primary');
            $(checkBtn).removeClass('layui-btn-primary').addClass('layui-btn-normal');
        });

    });
</script>
