[2025-07-01 08:11:47] default.INFO: 开始获取统计数据 [] []
[2025-07-01 08:11:47] default.INFO: 内存限制设置为: 2G [] []
[2025-07-01 08:11:47] default.INFO: 时间限制设置为: 3000s [] []
[2025-07-01 08:11:48] default.INFO: rkfl完成，内存使用: 6MB [] []
[2025-07-01 08:11:48] default.INFO: fwsj完成，内存使用: 6MB [] []
[2025-07-01 08:16:41] default.INFO: 开始获取统计数据 [] []
[2025-07-01 08:16:41] default.INFO: 内存限制设置为: 2G [] []
[2025-07-01 08:16:41] default.INFO: 时间限制设置为: 3000s [] []
[2025-07-01 08:16:42] default.INFO: rkfl完成，内存使用: 10MB [] []
[2025-07-01 08:16:42] default.INFO: fwsj完成，内存使用: 10MB [] []
[2025-07-01 08:16:42] default.INFO: jgdw完成，内存使用: 10MB [] []
[2025-07-01 08:54:42] default.INFO: 开始获取统计数据 [] []
[2025-07-01 08:54:42] default.INFO: Windows环境 - 内存限制: 1G, 时间限制: 180s [] []
[2025-07-01 08:54:42] default.INFO: 开始内存使用: 6MB [] []
[2025-07-01 09:15:22] default.INFO: testMinimal: 开始执行 [] []
[2025-07-01 09:15:22] default.INFO: testMinimal: 准备返回结果 [] []
[2025-07-01 09:16:32] default.INFO: testDatabase: 开始测试数据库连接 [] []
[2025-07-01 09:16:32] default.INFO: testDatabase: 数据库查询成功，人员总数: 60280 [] []
[2025-07-01 09:18:24] default.INFO: 开始获取统计数据 [] []
[2025-07-01 09:18:24] default.INFO: Windows环境 - 内存限制: 1G, 时间限制: 180s [] []
[2025-07-01 09:18:24] default.INFO: 开始内存使用: 6MB [] []
[2025-07-01 09:18:24] default.INFO: 开始执行 getRkflTotal [] []
[2025-07-01 09:18:25] default.INFO: rkfl完成，内存使用: 6MB [] []
[2025-07-01 09:18:25] default.INFO: 开始执行 getFwTotal [] []
[2025-07-01 09:18:25] default.INFO: fwsj完成，内存使用: 6MB [] []
[2025-07-01 09:18:25] default.INFO: 开始执行 getPlaceTotal [] []
[2025-07-01 09:18:25] default.INFO: jgdw完成，内存使用: 6MB [] []
[2025-07-01 09:18:25] default.INFO: 跳过 rkfb 和 pffb 查询，直接测试 tsrq [] []
[2025-07-01 09:18:25] default.INFO: 开始执行 getTsrqTotal3 [] []
[2025-07-01 09:18:44] default.INFO: 开始获取统计数据 [] []
[2025-07-01 09:18:44] default.INFO: Windows环境 - 内存限制: 1G, 时间限制: 180s [] []
[2025-07-01 09:18:44] default.INFO: 开始内存使用: 8MB [] []
[2025-07-01 09:18:44] default.INFO: 开始执行 getRkflTotal [] []
[2025-07-01 09:18:45] default.INFO: rkfl完成，内存使用: 8MB [] []
[2025-07-01 09:18:45] default.INFO: 开始执行 getFwTotal [] []
[2025-07-01 09:18:45] default.INFO: fwsj完成，内存使用: 8MB [] []
[2025-07-01 09:18:45] default.INFO: 开始执行 getPlaceTotal [] []
[2025-07-01 09:18:45] default.INFO: jgdw完成，内存使用: 8MB [] []
[2025-07-01 09:18:45] default.INFO: 跳过 rkfb 和 pffb 查询，直接测试 tsrq [] []
[2025-07-01 09:18:45] default.INFO: 开始执行 getTsrqTotal3 [] []
[2025-07-01 09:19:18] default.INFO: 开始获取统计数据 [] []
[2025-07-01 09:19:18] default.INFO: Windows环境 - 内存限制: 1G, 时间限制: 180s [] []
[2025-07-01 09:19:18] default.INFO: 开始内存使用: 8MB [] []
[2025-07-01 09:19:18] default.INFO: 开始执行 getRkflTotal [] []
[2025-07-01 09:19:18] default.INFO: rkfl完成，内存使用: 8MB [] []
[2025-07-01 09:19:18] default.INFO: 开始执行 getFwTotal [] []
[2025-07-01 09:19:18] default.INFO: fwsj完成，内存使用: 8MB [] []
[2025-07-01 09:19:18] default.INFO: 开始执行 getPlaceTotal [] []
[2025-07-01 09:19:18] default.INFO: jgdw完成，内存使用: 8MB [] []
[2025-07-01 09:19:18] default.INFO: 跳过 rkfb 和 pffb 查询，直接测试 tsrq [] []
[2025-07-01 09:19:18] default.INFO: 开始执行 getTsrqTotal3 [] []
[2025-07-01 09:19:19] default.INFO: tsrq完成，内存使用: 8MB [] []
[2025-07-01 09:19:19] default.INFO: 所有数据获取完成 [] []
[2025-07-01 09:19:19] default.INFO: 响应数据大小: 0MB [] []
[2025-07-01 09:19:19] default.INFO: 峰值内存使用: 8MB [] []
[2025-07-01 09:19:19] default.INFO: Performance {"execution_time":1.022608995437622,"memory_used":0,"peak_memory":8388608} []
[2025-07-01 09:21:29] default.INFO: 开始获取统计数据 [] []
[2025-07-01 09:21:29] default.INFO: Windows环境 - 内存限制: 1G, 时间限制: 180s [] []
[2025-07-01 09:21:29] default.INFO: 开始内存使用: 8MB [] []
[2025-07-01 09:21:29] default.INFO: 开始执行 getRkflTotal [] []
[2025-07-01 09:21:29] default.INFO: rkfl完成，内存使用: 8MB [] []
[2025-07-01 09:21:29] default.INFO: 开始执行 getFwTotal [] []
[2025-07-01 09:21:29] default.INFO: fwsj完成，内存使用: 8MB [] []
[2025-07-01 09:21:29] default.INFO: 开始执行 getPlaceTotal [] []
[2025-07-01 09:21:29] default.INFO: jgdw完成，内存使用: 8MB [] []
[2025-07-01 09:21:29] default.INFO: 跳过 rkfb 和 pffb 查询，直接测试 tsrq [] []
[2025-07-01 09:21:29] default.INFO: 开始执行 getTsrqTotal3 [] []
[2025-07-01 09:21:30] default.INFO: tsrq完成，内存使用: 8MB [] []
[2025-07-01 09:21:30] default.INFO: 所有数据获取完成 [] []
[2025-07-01 09:21:30] default.INFO: 响应数据大小: 0MB [] []
[2025-07-01 09:21:30] default.INFO: 峰值内存使用: 8MB [] []
[2025-07-01 09:21:30] default.INFO: Performance {"execution_time":0.9109249114990234,"memory_used":0,"peak_memory":8388608} []
[2025-07-01 09:22:01] default.INFO: 开始获取统计数据 [] []
[2025-07-01 09:22:01] default.INFO: Windows环境 - 内存限制: 1G, 时间限制: 180s [] []
[2025-07-01 09:22:01] default.INFO: 开始内存使用: 6MB [] []
[2025-07-01 09:22:01] default.INFO: 开始执行 getRkflTotal [] []
[2025-07-01 09:22:01] default.INFO: rkfl完成，内存使用: 6MB [] []
[2025-07-01 09:22:01] default.INFO: 开始执行 getFwTotal [] []
[2025-07-01 09:22:01] default.INFO: fwsj完成，内存使用: 6MB [] []
[2025-07-01 09:22:01] default.INFO: 开始执行 getPlaceTotal [] []
[2025-07-01 09:22:01] default.INFO: jgdw完成，内存使用: 6MB [] []
[2025-07-01 09:22:01] default.INFO: 跳过 rkfb 和 pffb 查询，直接测试 tsrq [] []
[2025-07-01 09:22:10] default.INFO: 开始获取统计数据 [] []
[2025-07-01 09:22:10] default.INFO: Windows环境 - 内存限制: 1G, 时间限制: 180s [] []
[2025-07-01 09:22:10] default.INFO: 开始内存使用: 8MB [] []
[2025-07-01 09:22:10] default.INFO: 开始执行 getRkflTotal [] []
[2025-07-01 09:22:10] default.INFO: rkfl完成，内存使用: 8MB [] []
[2025-07-01 09:22:10] default.INFO: 开始执行 getFwTotal [] []
[2025-07-01 09:22:11] default.INFO: fwsj完成，内存使用: 8MB [] []
[2025-07-01 09:22:11] default.INFO: 开始执行 getPlaceTotal [] []
[2025-07-01 09:22:11] default.INFO: jgdw完成，内存使用: 8MB [] []
[2025-07-01 09:22:11] default.INFO: 跳过 rkfb 和 pffb 查询，直接测试 tsrq [] []
[2025-07-01 09:22:11] default.INFO: 开始执行 getTsrqTotal3 [] []
[2025-07-01 09:22:11] default.INFO: tsrq完成，内存使用: 8MB [] []
[2025-07-01 09:22:11] default.INFO: 所有数据获取完成 [] []
[2025-07-01 09:22:11] default.INFO: 响应数据大小: 0MB [] []
[2025-07-01 09:22:11] default.INFO: 峰值内存使用: 8MB [] []
[2025-07-01 09:22:11] default.INFO: Performance {"execution_time":1.0863478183746338,"memory_used":0,"peak_memory":8388608} []
[2025-07-01 10:05:40] default.INFO: 开始获取统计数据 [] []
[2025-07-01 10:05:40] default.INFO: Windows环境 - 内存限制: 1G, 时间限制: 180s [] []
[2025-07-01 10:05:40] default.INFO: 开始内存使用: 6MB [] []
[2025-07-01 10:05:40] default.INFO: 开始执行 getRkflTotal [] []
[2025-07-01 10:13:11] default.INFO: 开始获取统计数据 [] []
[2025-07-01 10:13:11] default.INFO: Windows环境 - 内存限制: 1G, 时间限制: 180s [] []
[2025-07-01 10:13:11] default.INFO: 开始内存使用: 6MB [] []
[2025-07-01 10:13:11] default.INFO: 开始执行 getRkflTotal [] []
[2025-07-01 10:21:57] default.INFO: testSimpleEncrypt: 开始执行 [] []
[2025-07-01 10:21:57] default.INFO: testSimpleEncrypt: 准备返回结果 [] []
[2025-07-01 10:22:20] default.INFO: 开始获取统计数据 [] []
[2025-07-01 10:22:20] default.INFO: Windows环境 - 内存限制: 1G, 时间限制: 180s [] []
[2025-07-01 10:22:20] default.INFO: 开始内存使用: 6MB [] []
[2025-07-01 10:22:20] default.INFO: 开始执行简化的 getRkflTotal [] []
[2025-07-01 10:22:32] default.INFO: 开始获取统计数据 [] []
[2025-07-01 10:22:32] default.INFO: Windows环境 - 内存限制: 1G, 时间限制: 180s [] []
[2025-07-01 10:22:32] default.INFO: 开始内存使用: 8MB [] []
[2025-07-01 10:22:32] default.INFO: 开始执行简化的 getRkflTotal [] []
[2025-07-01 10:22:38] default.INFO: 开始获取统计数据 [] []
[2025-07-01 10:22:38] default.INFO: Windows环境 - 内存限制: 1G, 时间限制: 180s [] []
[2025-07-01 10:22:38] default.INFO: 开始内存使用: 8MB [] []
[2025-07-01 10:22:38] default.INFO: 开始执行简化的 getRkflTotal [] []
[2025-07-01 10:26:04] default.INFO: 开始获取统计数据 [] []
[2025-07-01 10:26:04] default.INFO: Windows环境 - 内存限制: 1G, 时间限制: 180s [] []
[2025-07-01 10:26:04] default.INFO: 开始内存使用: 8MB [] []
[2025-07-01 10:26:04] default.INFO: 开始执行简化的 getRkflTotal [] []
[2025-07-01 10:27:39] default.INFO: 开始获取统计数据 [] []
[2025-07-01 10:27:39] default.INFO: Windows环境 - 内存限制: 1G, 时间限制: 180s [] []
[2025-07-01 10:27:39] default.INFO: 开始内存使用: 8MB [] []
[2025-07-01 10:27:39] default.INFO: 开始执行简化的 getRkflTotal [] []
[2025-07-01 10:28:07] default.INFO: 开始获取统计数据 [] []
[2025-07-01 10:28:07] default.INFO: Windows环境 - 内存限制: 1G, 时间限制: 180s [] []
[2025-07-01 10:28:07] default.INFO: 开始内存使用: 6MB [] []
[2025-07-01 10:28:07] default.INFO: 开始执行简化的 getRkflTotal [] []
[2025-07-01 10:28:08] default.INFO: rkfl完成，内存使用: 6MB [] []
[2025-07-01 10:28:08] default.INFO: 开始执行 getFwTotal [] []
[2025-07-01 10:28:08] default.INFO: fwsj完成，内存使用: 6MB [] []
[2025-07-01 10:28:08] default.INFO: 开始执行 getPlaceTotal [] []
[2025-07-01 10:28:37] default.INFO: 开始获取统计数据 [] []
[2025-07-01 10:28:37] default.INFO: Windows环境 - 内存限制: 1G, 时间限制: 180s [] []
[2025-07-01 10:28:37] default.INFO: 开始内存使用: 8MB [] []
[2025-07-01 10:28:37] default.INFO: 开始执行简化的 getRkflTotal [] []
[2025-07-01 10:29:04] default.INFO: 开始获取统计数据 [] []
[2025-07-01 10:29:04] default.INFO: Windows环境 - 内存限制: 1G, 时间限制: 180s [] []
[2025-07-01 10:29:04] default.INFO: 开始内存使用: 8MB [] []
[2025-07-01 10:29:04] default.INFO: 开始执行简化的 getRkflTotal [] []
[2025-07-01 10:29:25] default.INFO: 开始获取统计数据 [] []
[2025-07-01 10:29:25] default.INFO: Windows环境 - 内存限制: 1G, 时间限制: 180s [] []
[2025-07-01 10:29:25] default.INFO: 开始内存使用: 8MB [] []
[2025-07-01 10:29:25] default.INFO: 开始执行简化的 getRkflTotal [] []
