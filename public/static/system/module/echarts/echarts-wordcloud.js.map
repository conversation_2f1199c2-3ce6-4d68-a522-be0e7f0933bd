{"version": 3, "sources": ["webpack://echarts-wordcloud/webpack/universalModuleDefinition", "webpack://echarts-wordcloud/./src/WordCloudSeries.js", "webpack://echarts-wordcloud/./src/WordCloudView.js", "webpack://echarts-wordcloud/./src/layout.js", "webpack://echarts-wordcloud/./src/wordCloud.js", "webpack://echarts-wordcloud/./index.js", "webpack://echarts-wordcloud/external \"echarts\"", "webpack://echarts-wordcloud/webpack/bootstrap", "webpack://echarts-wordcloud/webpack/runtime/make namespace object", "webpack://echarts-wordcloud/webpack/startup"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD,O;;;;;;;;;;;;;;;;ACV+C;;AAE/C,mCAAyB;AACzB;;AAEA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA,GAAG;;AAEH;AACA,qBAAqB,yCAA+B;AACpD;AACA,KAAK;AACL,mBAAmB,sBAAY;AAC/B;AACA;AACA,GAAG;;AAEH;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA,CAAC;;;AC1D8C;;AAE/C,iCAAuB;AACvB;;AAEA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;;AAEA,uBAAuB,8BAAoB;AAC3C,eAAe,wCAA8B;AAC7C;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;;AAEP;;AAEA;;AAEA,6CAA6C,wCAA8B;AAC3E;AACA;AACA;AACA;AACA;AACA,yCAAyC,wCAA8B;AACvE;AACA;AACA;AACA;AACA;;AAEA,MAAM,4CAAkC;AACxC;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;;AAEA;AACA,GAAG;;AAEH;AACA;AACA;AACA,CAAC;;;AC/ED;AACA;AACA;AACA;AACA;AACA;AACA;;AAEa;;AAEb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA,WAAW;AACX;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;;AAEA;AACA;AACA,gCAAgC,GAAG;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA,6BAA6B;;AAE7B;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,yBAAyB;AACzB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,mDAAmD;AACnD;;AAEA,wBAAwB;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA,yCAAyC,2BAA2B;;AAEpE;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wCAAwC,oBAAoB;AAC5D;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,qDAAqD;;AAErD;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA,wBAAwB;AACxB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,qBAAqB,mBAAmB;AACxC;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA,SAAS;AACT;AACA,OAAO;AACP;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,OAAO;;AAEP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0CAA0C,oBAAoB;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA,OAAO;AACP;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;;AAEA;AACA;AACA;AACA,OAAO;AACP;;AAEA;AACA;AACA;AACA,OAAO;AACP;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA,6CAAe,SAAS,EAAC;;;ACjzCsB;;AAEpB;AACF;;AAEoB;;AAE7C,KAAK,kBAAiC;AACtC;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,iBAAiB,2BAA2B;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,iBAAiB,2BAA2B;AAC5C;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA,gCAAsB;AACtB;AACA,mBAAmB,sCAA4B;AAC/C;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA,IAAI,MAAqB;AACzB;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,kCAAwB;AACtC;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;;AAEA;;AAEA;;AAEA;;AAEA;AACA;;AAEA;;AAEA;;AAEA;AACA,KAAK;;AAEL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,GAAG;AACH,CAAC;;AAED,sCAA4B;AAC5B,4BAA4B;AAC5B,GAAG,8BAAoB;;AAEvB;;AAEA,EAAE,2BAAiB;AACnB;AACA;;AAEA;AACA;AACA;AACA,GAAG;;AAEH;AACA;AACA,MAAM,2BAAiB;AACvB;AACA,6BAA6B,qCAA2B;AACxD;AACA,OAAO;AACP;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;;;ACpNyB;;;;;;;;;;;ACAzB,iE;;;;;;UCAA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;WCrBA;WACA;WACA;WACA,sDAAsD,kBAAkB;WACxE;WACA,+CAA+C,cAAc;WAC7D,E;;;;UCNA;UACA;UACA;UACA", "file": "echarts-wordcloud.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"echarts\"));\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([\"echarts\"], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"echarts-wordcloud\"] = factory(require(\"echarts\"));\n\telse\n\t\troot[\"echarts-wordcloud\"] = factory(root[\"echarts\"]);\n})(self, function(__WEBPACK_EXTERNAL_MODULE_echarts_lib_echarts__) {\nreturn ", "import * as echarts from 'echarts/lib/echarts';\n\necharts.extendSeriesModel({\n  type: 'series.wordCloud',\n\n  visualStyleAccessPath: 'textStyle',\n  visualStyleMapper: function (model) {\n    return {\n      fill: model.get('color')\n    };\n  },\n  visualDrawType: 'fill',\n\n  optionUpdated: function () {\n    var option = this.option;\n    option.gridSize = Math.max(Math.floor(option.gridSize), 4);\n  },\n\n  getInitialData: function (option, ecModel) {\n    var dimensions = echarts.helper.createDimensions(option.data, {\n      coordDimensions: ['value']\n    });\n    var list = new echarts.List(dimensions, this);\n    list.initData(option.data);\n    return list;\n  },\n\n  // Most of options are from https://github.com/timdream/wordcloud2.js/blob/gh-pages/API.md\n  defaultOption: {\n    maskImage: null,\n\n    // Shape can be 'circle', 'cardioid', 'diamond', 'triangle-forward', 'triangle', 'pentagon', 'star'\n    shape: 'circle',\n    keepAspect: false,\n\n    left: 'center',\n\n    top: 'center',\n\n    width: '70%',\n\n    height: '80%',\n\n    sizeRange: [12, 60],\n\n    rotationRange: [-90, 90],\n\n    rotationStep: 45,\n\n    gridSize: 8,\n\n    drawOutOfBound: false,\n    shrinkToFit: false,\n\n    textStyle: {\n      fontWeight: 'normal'\n    }\n  }\n});\n", "import * as echarts from 'echarts/lib/echarts';\n\necharts.extendChartView({\n  type: 'wordCloud',\n\n  render: function (seriesModel, ecModel, api) {\n    var group = this.group;\n    group.removeAll();\n\n    var data = seriesModel.getData();\n\n    var gridSize = seriesModel.get('gridSize');\n\n    seriesModel.layoutInstance.ondraw = function (text, size, dataIdx, drawn) {\n      var itemModel = data.getItemModel(dataIdx);\n      var textStyleModel = itemModel.getModel('textStyle');\n\n      var textEl = new echarts.graphic.Text({\n        style: echarts.helper.createTextStyle(textStyleModel),\n        scaleX: 1 / drawn.info.mu,\n        scaleY: 1 / drawn.info.mu,\n        x: (drawn.gx + drawn.info.gw / 2) * gridSize,\n        y: (drawn.gy + drawn.info.gh / 2) * gridSize,\n        rotation: drawn.rot\n      });\n      textEl.setStyle({\n        x: drawn.info.fillTextOffsetX,\n        y: drawn.info.fillTextOffsetY + size * 0.5,\n        text: text,\n        verticalAlign: 'middle',\n        fill: data.getItemVisual(dataIdx, 'style').fill,\n        fontSize: size\n      });\n\n      group.add(textEl);\n\n      data.setItemGraphicEl(dataIdx, textEl);\n\n      textEl.ensureState('emphasis').style = echarts.helper.createTextStyle(\n        itemModel.getModel(['emphasis', 'textStyle']),\n        {\n          state: 'emphasis'\n        }\n      );\n      textEl.ensureState('blur').style = echarts.helper.createTextStyle(\n        itemModel.getModel(['blur', 'textStyle']),\n        {\n          state: 'blur'\n        }\n      );\n\n      echarts.helper.enableHoverEmphasis(\n        textEl,\n        itemModel.get(['emphasis', 'focus']),\n        itemModel.get(['emphasis', 'blurScope'])\n      );\n\n      textEl.stateTransition = {\n        duration: seriesModel.get('animation')\n          ? seriesModel.get(['stateAnimation', 'duration'])\n          : 0,\n        easing: seriesModel.get(['stateAnimation', 'easing'])\n      };\n      // TODO\n      textEl.__highDownDispatcher = true;\n    };\n\n    this._model = seriesModel;\n  },\n\n  remove: function () {\n    this.group.removeAll();\n\n    this._model.layoutInstance.dispose();\n  },\n\n  dispose: function () {\n    this._model.layoutInstance.dispose();\n  }\n});\n", "/*!\n * wordcloud2.js\n * http://timdream.org/wordcloud2.js/\n *\n * Copyright 2011 - 2019 <PERSON> and contributors.\n * Released under the MIT license\n */\n\n'use strict';\n\n// setImmediate\nif (!window.setImmediate) {\n  window.setImmediate = (function setupSetImmediate() {\n    return (\n      window.msSetImmediate ||\n      window.webkitSetImmediate ||\n      window.mozSetImmediate ||\n      window.oSetImmediate ||\n      (function setupSetZeroTimeout() {\n        if (!window.postMessage || !window.addEventListener) {\n          return null;\n        }\n\n        var callbacks = [undefined];\n        var message = 'zero-timeout-message';\n\n        // Like setTimeout, but only takes a function argument.  There's\n        // no time argument (always zero) and no arguments (you have to\n        // use a closure).\n        var setZeroTimeout = function setZeroTimeout(callback) {\n          var id = callbacks.length;\n          callbacks.push(callback);\n          window.postMessage(message + id.toString(36), '*');\n\n          return id;\n        };\n\n        window.addEventListener(\n          'message',\n          function setZeroTimeoutMessage(evt) {\n            // Skipping checking event source, retarded IE confused this window\n            // object with another in the presence of iframe\n            if (\n              typeof evt.data !== 'string' ||\n              evt.data.substr(0, message.length) !== message /* ||\n            evt.source !== window */\n            ) {\n              return;\n            }\n\n            evt.stopImmediatePropagation();\n\n            var id = parseInt(evt.data.substr(message.length), 36);\n            if (!callbacks[id]) {\n              return;\n            }\n\n            callbacks[id]();\n            callbacks[id] = undefined;\n          },\n          true\n        );\n\n        /* specify clearImmediate() here since we need the scope */\n        window.clearImmediate = function clearZeroTimeout(id) {\n          if (!callbacks[id]) {\n            return;\n          }\n\n          callbacks[id] = undefined;\n        };\n\n        return setZeroTimeout;\n      })() ||\n      // fallback\n      function setImmediateFallback(fn) {\n        window.setTimeout(fn, 0);\n      }\n    );\n  })();\n}\n\nif (!window.clearImmediate) {\n  window.clearImmediate = (function setupClearImmediate() {\n    return (\n      window.msClearImmediate ||\n      window.webkitClearImmediate ||\n      window.mozClearImmediate ||\n      window.oClearImmediate ||\n      // \"clearZeroTimeout\" is implement on the previous block ||\n      // fallback\n      function clearImmediateFallback(timer) {\n        window.clearTimeout(timer);\n      }\n    );\n  })();\n}\n\n// Check if WordCloud can run on this browser\nvar isSupported = (function isSupported() {\n  var canvas = document.createElement('canvas');\n  if (!canvas || !canvas.getContext) {\n    return false;\n  }\n\n  var ctx = canvas.getContext('2d');\n  if (!ctx) {\n    return false;\n  }\n  if (!ctx.getImageData) {\n    return false;\n  }\n  if (!ctx.fillText) {\n    return false;\n  }\n\n  if (!Array.prototype.some) {\n    return false;\n  }\n  if (!Array.prototype.push) {\n    return false;\n  }\n\n  return true;\n})();\n\n// Find out if the browser impose minium font size by\n// drawing small texts on a canvas and measure it's width.\nvar minFontSize = (function getMinFontSize() {\n  if (!isSupported) {\n    return;\n  }\n\n  var ctx = document.createElement('canvas').getContext('2d');\n\n  // start from 20\n  var size = 20;\n\n  // two sizes to measure\n  var hanWidth, mWidth;\n\n  while (size) {\n    ctx.font = size.toString(10) + 'px sans-serif';\n    if (\n      ctx.measureText('\\uFF37').width === hanWidth &&\n      ctx.measureText('m').width === mWidth\n    ) {\n      return size + 1;\n    }\n\n    hanWidth = ctx.measureText('\\uFF37').width;\n    mWidth = ctx.measureText('m').width;\n\n    size--;\n  }\n\n  return 0;\n})();\n\nvar getItemExtraData = function (item) {\n  if (Array.isArray(item)) {\n    var itemCopy = item.slice();\n    // remove data we already have (word and weight)\n    itemCopy.splice(0, 2);\n    return itemCopy;\n  } else {\n    return [];\n  }\n};\n\n// Based on http://jsfromhell.com/array/shuffle\nvar shuffleArray = function shuffleArray(arr) {\n  for (var j, x, i = arr.length; i; ) {\n    j = Math.floor(Math.random() * i);\n    x = arr[--i];\n    arr[i] = arr[j];\n    arr[j] = x;\n  }\n  return arr;\n};\n\nvar timer = {};\nvar WordCloud = function WordCloud(elements, options) {\n  if (!isSupported) {\n    return;\n  }\n\n  var timerId = Math.floor(Math.random() * Date.now());\n\n  if (!Array.isArray(elements)) {\n    elements = [elements];\n  }\n\n  elements.forEach(function (el, i) {\n    if (typeof el === 'string') {\n      elements[i] = document.getElementById(el);\n      if (!elements[i]) {\n        throw new Error('The element id specified is not found.');\n      }\n    } else if (!el.tagName && !el.appendChild) {\n      throw new Error(\n        'You must pass valid HTML elements, or ID of the element.'\n      );\n    }\n  });\n\n  /* Default values to be overwritten by options object */\n  var settings = {\n    list: [],\n    fontFamily:\n      '\"Trebuchet MS\", \"Heiti TC\", \"微軟正黑體\", ' +\n      '\"Arial Unicode MS\", \"Droid Fallback Sans\", sans-serif',\n    fontWeight: 'normal',\n    color: 'random-dark',\n    minSize: 0, // 0 to disable\n    weightFactor: 1,\n    clearCanvas: true,\n    backgroundColor: '#fff', // opaque white = rgba(255, 255, 255, 1)\n\n    gridSize: 8,\n    drawOutOfBound: false,\n    shrinkToFit: false,\n    origin: null,\n\n    drawMask: false,\n    maskColor: 'rgba(255,0,0,0.3)',\n    maskGapWidth: 0.3,\n\n    layoutAnimation: true,\n\n    wait: 0,\n    abortThreshold: 0, // disabled\n    abort: function noop() {},\n\n    minRotation: -Math.PI / 2,\n    maxRotation: Math.PI / 2,\n    rotationStep: 0.1,\n\n    shuffle: true,\n    rotateRatio: 0.1,\n\n    shape: 'circle',\n    ellipticity: 0.65,\n\n    classes: null,\n\n    hover: null,\n    click: null\n  };\n\n  if (options) {\n    for (var key in options) {\n      if (key in settings) {\n        settings[key] = options[key];\n      }\n    }\n  }\n\n  /* Convert weightFactor into a function */\n  if (typeof settings.weightFactor !== 'function') {\n    var factor = settings.weightFactor;\n    settings.weightFactor = function weightFactor(pt) {\n      return pt * factor; // in px\n    };\n  }\n\n  /* Convert shape into a function */\n  if (typeof settings.shape !== 'function') {\n    switch (settings.shape) {\n      case 'circle':\n      /* falls through */\n      default:\n        // 'circle' is the default and a shortcut in the code loop.\n        settings.shape = 'circle';\n        break;\n\n      case 'cardioid':\n        settings.shape = function shapeCardioid(theta) {\n          return 1 - Math.sin(theta);\n        };\n        break;\n\n      /*\n        To work out an X-gon, one has to calculate \"m\",\n        where 1/(cos(2*PI/X)+m*sin(2*PI/X)) = 1/(cos(0)+m*sin(0))\n        http://www.wolframalpha.com/input/?i=1%2F%28cos%282*PI%2FX%29%2Bm*sin%28\n        2*PI%2FX%29%29+%3D+1%2F%28cos%280%29%2Bm*sin%280%29%29\n        Copy the solution into polar equation r = 1/(cos(t') + m*sin(t'))\n        where t' equals to mod(t, 2PI/X);\n        */\n\n      case 'diamond':\n        // http://www.wolframalpha.com/input/?i=plot+r+%3D+1%2F%28cos%28mod+\n        // %28t%2C+PI%2F2%29%29%2Bsin%28mod+%28t%2C+PI%2F2%29%29%29%2C+t+%3D\n        // +0+..+2*PI\n        settings.shape = function shapeSquare(theta) {\n          var thetaPrime = theta % ((2 * Math.PI) / 4);\n          return 1 / (Math.cos(thetaPrime) + Math.sin(thetaPrime));\n        };\n        break;\n\n      case 'square':\n        // http://www.wolframalpha.com/input/?i=plot+r+%3D+min(1%2Fabs(cos(t\n        // )),1%2Fabs(sin(t)))),+t+%3D+0+..+2*PI\n        settings.shape = function shapeSquare(theta) {\n          return Math.min(\n            1 / Math.abs(Math.cos(theta)),\n            1 / Math.abs(Math.sin(theta))\n          );\n        };\n        break;\n\n      case 'triangle-forward':\n        // http://www.wolframalpha.com/input/?i=plot+r+%3D+1%2F%28cos%28mod+\n        // %28t%2C+2*PI%2F3%29%29%2Bsqrt%283%29sin%28mod+%28t%2C+2*PI%2F3%29\n        // %29%29%2C+t+%3D+0+..+2*PI\n        settings.shape = function shapeTriangle(theta) {\n          var thetaPrime = theta % ((2 * Math.PI) / 3);\n          return (\n            1 / (Math.cos(thetaPrime) + Math.sqrt(3) * Math.sin(thetaPrime))\n          );\n        };\n        break;\n\n      case 'triangle':\n      case 'triangle-upright':\n        settings.shape = function shapeTriangle(theta) {\n          var thetaPrime = (theta + (Math.PI * 3) / 2) % ((2 * Math.PI) / 3);\n          return (\n            1 / (Math.cos(thetaPrime) + Math.sqrt(3) * Math.sin(thetaPrime))\n          );\n        };\n        break;\n\n      case 'pentagon':\n        settings.shape = function shapePentagon(theta) {\n          var thetaPrime = (theta + 0.955) % ((2 * Math.PI) / 5);\n          return 1 / (Math.cos(thetaPrime) + 0.726543 * Math.sin(thetaPrime));\n        };\n        break;\n\n      case 'star':\n        settings.shape = function shapeStar(theta) {\n          var thetaPrime = (theta + 0.955) % ((2 * Math.PI) / 10);\n          if (\n            ((theta + 0.955) % ((2 * Math.PI) / 5)) - (2 * Math.PI) / 10 >=\n            0\n          ) {\n            return (\n              1 /\n              (Math.cos((2 * Math.PI) / 10 - thetaPrime) +\n                3.07768 * Math.sin((2 * Math.PI) / 10 - thetaPrime))\n            );\n          } else {\n            return 1 / (Math.cos(thetaPrime) + 3.07768 * Math.sin(thetaPrime));\n          }\n        };\n        break;\n    }\n  }\n\n  /* Make sure gridSize is a whole number and is not smaller than 4px */\n  settings.gridSize = Math.max(Math.floor(settings.gridSize), 4);\n\n  /* shorthand */\n  var g = settings.gridSize;\n  var maskRectWidth = g - settings.maskGapWidth;\n\n  /* normalize rotation settings */\n  var rotationRange = Math.abs(settings.maxRotation - settings.minRotation);\n  var minRotation = Math.min(settings.maxRotation, settings.minRotation);\n  var rotationStep = settings.rotationStep;\n\n  /* information/object available to all functions, set when start() */\n  var grid, // 2d array containing filling information\n    ngx,\n    ngy, // width and height of the grid\n    center, // position of the center of the cloud\n    maxRadius;\n\n  /* timestamp for measuring each putWord() action */\n  var escapeTime;\n\n  /* function for getting the color of the text */\n  var getTextColor;\n  function randomHslColor(min, max) {\n    return (\n      'hsl(' +\n      (Math.random() * 360).toFixed() +\n      ',' +\n      (Math.random() * 30 + 70).toFixed() +\n      '%,' +\n      (Math.random() * (max - min) + min).toFixed() +\n      '%)'\n    );\n  }\n  switch (settings.color) {\n    case 'random-dark':\n      getTextColor = function getRandomDarkColor() {\n        return randomHslColor(10, 50);\n      };\n      break;\n\n    case 'random-light':\n      getTextColor = function getRandomLightColor() {\n        return randomHslColor(50, 90);\n      };\n      break;\n\n    default:\n      if (typeof settings.color === 'function') {\n        getTextColor = settings.color;\n      }\n      break;\n  }\n\n  /* function for getting the font-weight of the text */\n  var getTextFontWeight;\n  if (typeof settings.fontWeight === 'function') {\n    getTextFontWeight = settings.fontWeight;\n  }\n\n  /* function for getting the classes of the text */\n  var getTextClasses = null;\n  if (typeof settings.classes === 'function') {\n    getTextClasses = settings.classes;\n  }\n\n  /* Interactive */\n  var interactive = false;\n  var infoGrid = [];\n  var hovered;\n\n  var getInfoGridFromMouseTouchEvent = function getInfoGridFromMouseTouchEvent(\n    evt\n  ) {\n    var canvas = evt.currentTarget;\n    var rect = canvas.getBoundingClientRect();\n    var clientX;\n    var clientY;\n    /** Detect if touches are available */\n    if (evt.touches) {\n      clientX = evt.touches[0].clientX;\n      clientY = evt.touches[0].clientY;\n    } else {\n      clientX = evt.clientX;\n      clientY = evt.clientY;\n    }\n    var eventX = clientX - rect.left;\n    var eventY = clientY - rect.top;\n\n    var x = Math.floor((eventX * (canvas.width / rect.width || 1)) / g);\n    var y = Math.floor((eventY * (canvas.height / rect.height || 1)) / g);\n\n    return infoGrid[x][y];\n  };\n\n  var wordcloudhover = function wordcloudhover(evt) {\n    var info = getInfoGridFromMouseTouchEvent(evt);\n\n    if (hovered === info) {\n      return;\n    }\n\n    hovered = info;\n    if (!info) {\n      settings.hover(undefined, undefined, evt);\n\n      return;\n    }\n\n    settings.hover(info.item, info.dimension, evt);\n  };\n\n  var wordcloudclick = function wordcloudclick(evt) {\n    var info = getInfoGridFromMouseTouchEvent(evt);\n    if (!info) {\n      return;\n    }\n\n    settings.click(info.item, info.dimension, evt);\n    evt.preventDefault();\n  };\n\n  /* Get points on the grid for a given radius away from the center */\n  var pointsAtRadius = [];\n  var getPointsAtRadius = function getPointsAtRadius(radius) {\n    if (pointsAtRadius[radius]) {\n      return pointsAtRadius[radius];\n    }\n\n    // Look for these number of points on each radius\n    var T = radius * 8;\n\n    // Getting all the points at this radius\n    var t = T;\n    var points = [];\n\n    if (radius === 0) {\n      points.push([center[0], center[1], 0]);\n    }\n\n    while (t--) {\n      // distort the radius to put the cloud in shape\n      var rx = 1;\n      if (settings.shape !== 'circle') {\n        rx = settings.shape((t / T) * 2 * Math.PI); // 0 to 1\n      }\n\n      // Push [x, y, t]; t is used solely for getTextColor()\n      points.push([\n        center[0] + radius * rx * Math.cos((-t / T) * 2 * Math.PI),\n        center[1] +\n          radius * rx * Math.sin((-t / T) * 2 * Math.PI) * settings.ellipticity,\n        (t / T) * 2 * Math.PI\n      ]);\n    }\n\n    pointsAtRadius[radius] = points;\n    return points;\n  };\n\n  /* Return true if we had spent too much time */\n  var exceedTime = function exceedTime() {\n    return (\n      settings.abortThreshold > 0 &&\n      new Date().getTime() - escapeTime > settings.abortThreshold\n    );\n  };\n\n  /* Get the deg of rotation according to settings, and luck. */\n  var getRotateDeg = function getRotateDeg() {\n    if (settings.rotateRatio === 0) {\n      return 0;\n    }\n\n    if (Math.random() > settings.rotateRatio) {\n      return 0;\n    }\n\n    if (rotationRange === 0) {\n      return minRotation;\n    }\n\n    return minRotation + Math.round(Math.random() * rotationRange / rotationStep) * rotationStep;\n  };\n\n  var getTextInfo = function getTextInfo(\n    word,\n    weight,\n    rotateDeg,\n    extraDataArray\n  ) {\n    // calculate the acutal font size\n    // fontSize === 0 means weightFactor function wants the text skipped,\n    // and size < minSize means we cannot draw the text.\n    var debug = false;\n    var fontSize = settings.weightFactor(weight);\n    if (fontSize <= settings.minSize) {\n      return false;\n    }\n\n    // Scale factor here is to make sure fillText is not limited by\n    // the minium font size set by browser.\n    // It will always be 1 or 2n.\n    var mu = 1;\n    if (fontSize < minFontSize) {\n      mu = (function calculateScaleFactor() {\n        var mu = 2;\n        while (mu * fontSize < minFontSize) {\n          mu += 2;\n        }\n        return mu;\n      })();\n    }\n\n    // Get fontWeight that will be used to set fctx.font\n    var fontWeight;\n    if (getTextFontWeight) {\n      fontWeight = getTextFontWeight(word, weight, fontSize, extraDataArray);\n    } else {\n      fontWeight = settings.fontWeight;\n    }\n\n    var fcanvas = document.createElement('canvas');\n    var fctx = fcanvas.getContext('2d', { willReadFrequently: true });\n\n    fctx.font =\n      fontWeight +\n      ' ' +\n      (fontSize * mu).toString(10) +\n      'px ' +\n      settings.fontFamily;\n\n    // Estimate the dimension of the text with measureText().\n    var fw = fctx.measureText(word).width / mu;\n    var fh =\n      Math.max(\n        fontSize * mu,\n        fctx.measureText('m').width,\n        fctx.measureText('\\uFF37').width\n      ) / mu;\n\n    // Create a boundary box that is larger than our estimates,\n    // so text don't get cut of (it sill might)\n    var boxWidth = fw + fh * 2;\n    var boxHeight = fh * 3;\n    var fgw = Math.ceil(boxWidth / g);\n    var fgh = Math.ceil(boxHeight / g);\n    boxWidth = fgw * g;\n    boxHeight = fgh * g;\n\n    // Calculate the proper offsets to make the text centered at\n    // the preferred position.\n\n    // This is simply half of the width.\n    var fillTextOffsetX = -fw / 2;\n    // Instead of moving the box to the exact middle of the preferred\n    // position, for Y-offset we move 0.4 instead, so Latin alphabets look\n    // vertical centered.\n    var fillTextOffsetY = -fh * 0.4;\n\n    // Calculate the actual dimension of the canvas, considering the rotation.\n    var cgh = Math.ceil(\n      (boxWidth * Math.abs(Math.sin(rotateDeg)) +\n        boxHeight * Math.abs(Math.cos(rotateDeg))) /\n        g\n    );\n    var cgw = Math.ceil(\n      (boxWidth * Math.abs(Math.cos(rotateDeg)) +\n        boxHeight * Math.abs(Math.sin(rotateDeg))) /\n        g\n    );\n    var width = cgw * g;\n    var height = cgh * g;\n\n    fcanvas.setAttribute('width', width);\n    fcanvas.setAttribute('height', height);\n\n    if (debug) {\n      // Attach fcanvas to the DOM\n      document.body.appendChild(fcanvas);\n      // Save it's state so that we could restore and draw the grid correctly.\n      fctx.save();\n    }\n\n    // Scale the canvas with |mu|.\n    fctx.scale(1 / mu, 1 / mu);\n    fctx.translate((width * mu) / 2, (height * mu) / 2);\n    fctx.rotate(-rotateDeg);\n\n    // Once the width/height is set, ctx info will be reset.\n    // Set it again here.\n    fctx.font =\n      fontWeight +\n      ' ' +\n      (fontSize * mu).toString(10) +\n      'px ' +\n      settings.fontFamily;\n\n    // Fill the text into the fcanvas.\n    // XXX: We cannot because textBaseline = 'top' here because\n    // Firefox and Chrome uses different default line-height for canvas.\n    // Please read https://bugzil.la/737852#c6.\n    // Here, we use textBaseline = 'middle' and draw the text at exactly\n    // 0.5 * fontSize lower.\n    fctx.fillStyle = '#000';\n    fctx.textBaseline = 'middle';\n    fctx.fillText(\n      word,\n      fillTextOffsetX * mu,\n      (fillTextOffsetY + fontSize * 0.5) * mu\n    );\n\n    // Get the pixels of the text\n    var imageData = fctx.getImageData(0, 0, width, height).data;\n\n    if (exceedTime()) {\n      return false;\n    }\n\n    if (debug) {\n      // Draw the box of the original estimation\n      fctx.strokeRect(fillTextOffsetX * mu, fillTextOffsetY, fw * mu, fh * mu);\n      fctx.restore();\n    }\n\n    // Read the pixels and save the information to the occupied array\n    var occupied = [];\n    var gx = cgw;\n    var gy, x, y;\n    var bounds = [cgh / 2, cgw / 2, cgh / 2, cgw / 2];\n    while (gx--) {\n      gy = cgh;\n      while (gy--) {\n        y = g;\n        /* eslint no-labels: ['error', { 'allowLoop': true }] */\n        singleGridLoop: while (y--) {\n          x = g;\n          while (x--) {\n            if (imageData[((gy * g + y) * width + (gx * g + x)) * 4 + 3]) {\n              occupied.push([gx, gy]);\n\n              if (gx < bounds[3]) {\n                bounds[3] = gx;\n              }\n              if (gx > bounds[1]) {\n                bounds[1] = gx;\n              }\n              if (gy < bounds[0]) {\n                bounds[0] = gy;\n              }\n              if (gy > bounds[2]) {\n                bounds[2] = gy;\n              }\n\n              if (debug) {\n                fctx.fillStyle = 'rgba(255, 0, 0, 0.5)';\n                fctx.fillRect(gx * g, gy * g, g - 0.5, g - 0.5);\n              }\n              break singleGridLoop;\n            }\n          }\n        }\n        if (debug) {\n          fctx.fillStyle = 'rgba(0, 0, 255, 0.5)';\n          fctx.fillRect(gx * g, gy * g, g - 0.5, g - 0.5);\n        }\n      }\n    }\n\n    if (debug) {\n      fctx.fillStyle = 'rgba(0, 255, 0, 0.5)';\n      fctx.fillRect(\n        bounds[3] * g,\n        bounds[0] * g,\n        (bounds[1] - bounds[3] + 1) * g,\n        (bounds[2] - bounds[0] + 1) * g\n      );\n    }\n\n    // Return information needed to create the text on the real canvas\n    return {\n      mu: mu,\n      occupied: occupied,\n      bounds: bounds,\n      gw: cgw,\n      gh: cgh,\n      fillTextOffsetX: fillTextOffsetX,\n      fillTextOffsetY: fillTextOffsetY,\n      fillTextWidth: fw,\n      fillTextHeight: fh,\n      fontSize: fontSize\n    };\n  };\n\n  /* Determine if there is room available in the given dimension */\n  var canFitText = function canFitText(gx, gy, gw, gh, occupied) {\n    // Go through the occupied points,\n    // return false if the space is not available.\n    var i = occupied.length;\n    while (i--) {\n      var px = gx + occupied[i][0];\n      var py = gy + occupied[i][1];\n\n      if (px >= ngx || py >= ngy || px < 0 || py < 0) {\n        if (!settings.drawOutOfBound) {\n          return false;\n        }\n        continue;\n      }\n\n      if (!grid[px][py]) {\n        return false;\n      }\n    }\n    return true;\n  };\n\n  /* Actually draw the text on the grid */\n  var drawText = function drawText(\n    gx,\n    gy,\n    info,\n    word,\n    weight,\n    distance,\n    theta,\n    rotateDeg,\n    attributes,\n    extraDataArray\n  ) {\n    var fontSize = info.fontSize;\n    var color;\n    if (getTextColor) {\n      color = getTextColor(\n        word,\n        weight,\n        fontSize,\n        distance,\n        theta,\n        extraDataArray\n      );\n    } else {\n      color = settings.color;\n    }\n\n    // get fontWeight that will be used to set ctx.font and font style rule\n    var fontWeight;\n    if (getTextFontWeight) {\n      fontWeight = getTextFontWeight(word, weight, fontSize, extraDataArray);\n    } else {\n      fontWeight = settings.fontWeight;\n    }\n\n    var classes;\n    if (getTextClasses) {\n      classes = getTextClasses(word, weight, fontSize, extraDataArray);\n    } else {\n      classes = settings.classes;\n    }\n\n    elements.forEach(function (el) {\n      if (el.getContext) {\n        var ctx = el.getContext('2d');\n        var mu = info.mu;\n\n        // Save the current state before messing it\n        ctx.save();\n        ctx.scale(1 / mu, 1 / mu);\n\n        ctx.font =\n          fontWeight +\n          ' ' +\n          (fontSize * mu).toString(10) +\n          'px ' +\n          settings.fontFamily;\n        ctx.fillStyle = color;\n\n        // Translate the canvas position to the origin coordinate of where\n        // the text should be put.\n        ctx.translate((gx + info.gw / 2) * g * mu, (gy + info.gh / 2) * g * mu);\n\n        if (rotateDeg !== 0) {\n          ctx.rotate(-rotateDeg);\n        }\n\n        // Finally, fill the text.\n\n        // XXX: We cannot because textBaseline = 'top' here because\n        // Firefox and Chrome uses different default line-height for canvas.\n        // Please read https://bugzil.la/737852#c6.\n        // Here, we use textBaseline = 'middle' and draw the text at exactly\n        // 0.5 * fontSize lower.\n        ctx.textBaseline = 'middle';\n        ctx.fillText(\n          word,\n          info.fillTextOffsetX * mu,\n          (info.fillTextOffsetY + fontSize * 0.5) * mu\n        );\n\n        // The below box is always matches how <span>s are positioned\n        /* ctx.strokeRect(info.fillTextOffsetX, info.fillTextOffsetY,\n            info.fillTextWidth, info.fillTextHeight); */\n\n        // Restore the state.\n        ctx.restore();\n      } else {\n        // drawText on DIV element\n        var span = document.createElement('span');\n        var transformRule = '';\n        transformRule = 'rotate(' + (-rotateDeg / Math.PI) * 180 + 'deg) ';\n        if (info.mu !== 1) {\n          transformRule +=\n            'translateX(-' +\n            info.fillTextWidth / 4 +\n            'px) ' +\n            'scale(' +\n            1 / info.mu +\n            ')';\n        }\n        var styleRules = {\n          position: 'absolute',\n          display: 'block',\n          font:\n            fontWeight + ' ' + fontSize * info.mu + 'px ' + settings.fontFamily,\n          left: (gx + info.gw / 2) * g + info.fillTextOffsetX + 'px',\n          top: (gy + info.gh / 2) * g + info.fillTextOffsetY + 'px',\n          width: info.fillTextWidth + 'px',\n          height: info.fillTextHeight + 'px',\n          lineHeight: fontSize + 'px',\n          whiteSpace: 'nowrap',\n          transform: transformRule,\n          webkitTransform: transformRule,\n          msTransform: transformRule,\n          transformOrigin: '50% 40%',\n          webkitTransformOrigin: '50% 40%',\n          msTransformOrigin: '50% 40%'\n        };\n        if (color) {\n          styleRules.color = color;\n        }\n        span.textContent = word;\n        for (var cssProp in styleRules) {\n          span.style[cssProp] = styleRules[cssProp];\n        }\n        if (attributes) {\n          for (var attribute in attributes) {\n            span.setAttribute(attribute, attributes[attribute]);\n          }\n        }\n        if (classes) {\n          span.className += classes;\n        }\n        el.appendChild(span);\n      }\n    });\n  };\n\n  /* Help function to updateGrid */\n  var fillGridAt = function fillGridAt(x, y, drawMask, dimension, item) {\n    if (x >= ngx || y >= ngy || x < 0 || y < 0) {\n      return;\n    }\n\n    grid[x][y] = false;\n\n    if (drawMask) {\n      var ctx = elements[0].getContext('2d');\n      ctx.fillRect(x * g, y * g, maskRectWidth, maskRectWidth);\n    }\n\n    if (interactive) {\n      infoGrid[x][y] = { item: item, dimension: dimension };\n    }\n  };\n\n  /* Update the filling information of the given space with occupied points.\n       Draw the mask on the canvas if necessary. */\n  var updateGrid = function updateGrid(gx, gy, gw, gh, info, item) {\n    var occupied = info.occupied;\n    var drawMask = settings.drawMask;\n    var ctx;\n    if (drawMask) {\n      ctx = elements[0].getContext('2d');\n      ctx.save();\n      ctx.fillStyle = settings.maskColor;\n    }\n\n    var dimension;\n    if (interactive) {\n      var bounds = info.bounds;\n      dimension = {\n        x: (gx + bounds[3]) * g,\n        y: (gy + bounds[0]) * g,\n        w: (bounds[1] - bounds[3] + 1) * g,\n        h: (bounds[2] - bounds[0] + 1) * g\n      };\n    }\n\n    var i = occupied.length;\n    while (i--) {\n      var px = gx + occupied[i][0];\n      var py = gy + occupied[i][1];\n\n      if (px >= ngx || py >= ngy || px < 0 || py < 0) {\n        continue;\n      }\n\n      fillGridAt(px, py, drawMask, dimension, item);\n    }\n\n    if (drawMask) {\n      ctx.restore();\n    }\n  };\n\n  /* putWord() processes each item on the list,\n       calculate it's size and determine it's position, and actually\n       put it on the canvas. */\n  var putWord = function putWord(item) {\n    var word, weight, attributes;\n    if (Array.isArray(item)) {\n      word = item[0];\n      weight = item[1];\n    } else {\n      word = item.word;\n      weight = item.weight;\n      attributes = item.attributes;\n    }\n    var rotateDeg = getRotateDeg();\n\n    var extraDataArray = getItemExtraData(item);\n\n    // get info needed to put the text onto the canvas\n    var info = getTextInfo(word, weight, rotateDeg, extraDataArray);\n\n    // not getting the info means we shouldn't be drawing this one.\n    if (!info) {\n      return false;\n    }\n\n    if (exceedTime()) {\n      return false;\n    }\n\n    // If drawOutOfBound is set to false,\n    // skip the loop if we have already know the bounding box of\n    // word is larger than the canvas.\n    if (!settings.drawOutOfBound && !settings.shrinkToFit) {\n      var bounds = info.bounds;\n      if (bounds[1] - bounds[3] + 1 > ngx || bounds[2] - bounds[0] + 1 > ngy) {\n        return false;\n      }\n    }\n\n    // Determine the position to put the text by\n    // start looking for the nearest points\n    var r = maxRadius + 1;\n\n    var tryToPutWordAtPoint = function (gxy) {\n      var gx = Math.floor(gxy[0] - info.gw / 2);\n      var gy = Math.floor(gxy[1] - info.gh / 2);\n      var gw = info.gw;\n      var gh = info.gh;\n\n      // If we cannot fit the text at this position, return false\n      // and go to the next position.\n      if (!canFitText(gx, gy, gw, gh, info.occupied)) {\n        return false;\n      }\n\n      // Actually put the text on the canvas\n      drawText(\n        gx,\n        gy,\n        info,\n        word,\n        weight,\n        maxRadius - r,\n        gxy[2],\n        rotateDeg,\n        attributes,\n        extraDataArray\n      );\n\n      // Mark the spaces on the grid as filled\n      updateGrid(gx, gy, gw, gh, info, item);\n\n      return {\n        gx: gx,\n        gy: gy,\n        rot: rotateDeg,\n        info: info\n      };\n    };\n\n    while (r--) {\n      var points = getPointsAtRadius(maxRadius - r);\n\n      if (settings.shuffle) {\n        points = [].concat(points);\n        shuffleArray(points);\n      }\n\n      // Try to fit the words by looking at each point.\n      // array.some() will stop and return true\n      // when putWordAtPoint() returns true.\n      for (var i = 0; i < points.length; i++) {\n        var res = tryToPutWordAtPoint(points[i]);\n        if (res) {\n          return res;\n        }\n      }\n\n      // var drawn = points.some(tryToPutWordAtPoint);\n      // if (drawn) {\n      //   // leave putWord() and return true\n      //   return true;\n      // }\n\n      if (settings.shrinkToFit) {\n        if (Array.isArray(item)) {\n          item[1] = (item[1] * 3) / 4;\n        } else {\n          item.weight = (item.weight * 3) / 4;\n        }\n        return putWord(item);\n      }\n    }\n    // we tried all distances but text won't fit, return null\n    return null;\n  };\n\n  /* Send DOM event to all elements. Will stop sending event and return\n       if the previous one is canceled (for cancelable events). */\n  var sendEvent = function sendEvent(type, cancelable, details) {\n    if (cancelable) {\n      return !elements.some(function (el) {\n        var event = new CustomEvent(type, {\n          detail: details || {}\n        });\n        return !el.dispatchEvent(event);\n      }, this);\n    } else {\n      elements.forEach(function (el) {\n        var event = new CustomEvent(type, {\n          detail: details || {}\n        });\n        el.dispatchEvent(event);\n      }, this);\n    }\n  };\n\n  /* Start drawing on a canvas */\n  var start = function start() {\n    // For dimensions, clearCanvas etc.,\n    // we only care about the first element.\n    var canvas = elements[0];\n\n    if (canvas.getContext) {\n      ngx = Math.ceil(canvas.width / g);\n      ngy = Math.ceil(canvas.height / g);\n    } else {\n      var rect = canvas.getBoundingClientRect();\n      ngx = Math.ceil(rect.width / g);\n      ngy = Math.ceil(rect.height / g);\n    }\n\n    // Sending a wordcloudstart event which cause the previous loop to stop.\n    // Do nothing if the event is canceled.\n    if (!sendEvent('wordcloudstart', true)) {\n      return;\n    }\n\n    // Determine the center of the word cloud\n    center = settings.origin\n      ? [settings.origin[0] / g, settings.origin[1] / g]\n      : [ngx / 2, ngy / 2];\n\n    // Maxium radius to look for space\n    maxRadius = Math.floor(Math.sqrt(ngx * ngx + ngy * ngy));\n\n    /* Clear the canvas only if the clearCanvas is set,\n         if not, update the grid to the current canvas state */\n    grid = [];\n\n    var gx, gy, i;\n    if (!canvas.getContext || settings.clearCanvas) {\n      elements.forEach(function (el) {\n        if (el.getContext) {\n          var ctx = el.getContext('2d');\n          ctx.fillStyle = settings.backgroundColor;\n          ctx.clearRect(0, 0, ngx * (g + 1), ngy * (g + 1));\n          ctx.fillRect(0, 0, ngx * (g + 1), ngy * (g + 1));\n        } else {\n          el.textContent = '';\n          el.style.backgroundColor = settings.backgroundColor;\n          el.style.position = 'relative';\n        }\n      });\n\n      /* fill the grid with empty state */\n      gx = ngx;\n      while (gx--) {\n        grid[gx] = [];\n        gy = ngy;\n        while (gy--) {\n          grid[gx][gy] = true;\n        }\n      }\n    } else {\n      /* Determine bgPixel by creating\n           another canvas and fill the specified background color. */\n      var bctx = document.createElement('canvas').getContext('2d');\n\n      bctx.fillStyle = settings.backgroundColor;\n      bctx.fillRect(0, 0, 1, 1);\n      var bgPixel = bctx.getImageData(0, 0, 1, 1).data;\n\n      /* Read back the pixels of the canvas we got to tell which part of the\n           canvas is empty.\n           (no clearCanvas only works with a canvas, not divs) */\n      var imageData = canvas\n        .getContext('2d')\n        .getImageData(0, 0, ngx * g, ngy * g).data;\n\n      gx = ngx;\n      var x, y;\n      while (gx--) {\n        grid[gx] = [];\n        gy = ngy;\n        while (gy--) {\n          y = g;\n          /* eslint no-labels: ['error', { 'allowLoop': true }] */\n          singleGridLoop: while (y--) {\n            x = g;\n            while (x--) {\n              i = 4;\n              while (i--) {\n                if (\n                  imageData[((gy * g + y) * ngx * g + (gx * g + x)) * 4 + i] !==\n                  bgPixel[i]\n                ) {\n                  grid[gx][gy] = false;\n                  break singleGridLoop;\n                }\n              }\n            }\n          }\n          if (grid[gx][gy] !== false) {\n            grid[gx][gy] = true;\n          }\n        }\n      }\n\n      imageData = bctx = bgPixel = undefined;\n    }\n\n    // fill the infoGrid with empty state if we need it\n    if (settings.hover || settings.click) {\n      interactive = true;\n\n      /* fill the grid with empty state */\n      gx = ngx + 1;\n      while (gx--) {\n        infoGrid[gx] = [];\n      }\n\n      if (settings.hover) {\n        canvas.addEventListener('mousemove', wordcloudhover);\n      }\n\n      if (settings.click) {\n        canvas.addEventListener('click', wordcloudclick);\n        canvas.addEventListener('touchstart', wordcloudclick);\n        canvas.addEventListener('touchend', function (e) {\n          e.preventDefault();\n        });\n        canvas.style.webkitTapHighlightColor = 'rgba(0, 0, 0, 0)';\n      }\n\n      canvas.addEventListener('wordcloudstart', function stopInteraction() {\n        canvas.removeEventListener('wordcloudstart', stopInteraction);\n\n        canvas.removeEventListener('mousemove', wordcloudhover);\n        canvas.removeEventListener('click', wordcloudclick);\n        hovered = undefined;\n      });\n    }\n\n    i = 0;\n    var loopingFunction, stoppingFunction;\n    var layouting = true;\n    if (!settings.layoutAnimation) {\n      loopingFunction = function (cb) {\n        cb();\n      };\n      stoppingFunction = function () {\n        layouting = false;\n      };\n    } else if (settings.wait !== 0) {\n      loopingFunction = window.setTimeout;\n      stoppingFunction = window.clearTimeout;\n    } else {\n      loopingFunction = window.setImmediate;\n      stoppingFunction = window.clearImmediate;\n    }\n\n    var addEventListener = function addEventListener(type, listener) {\n      elements.forEach(function (el) {\n        el.addEventListener(type, listener);\n      }, this);\n    };\n\n    var removeEventListener = function removeEventListener(type, listener) {\n      elements.forEach(function (el) {\n        el.removeEventListener(type, listener);\n      }, this);\n    };\n\n    var anotherWordCloudStart = function anotherWordCloudStart() {\n      removeEventListener('wordcloudstart', anotherWordCloudStart);\n      stoppingFunction(timer[timerId]);\n    };\n\n    addEventListener('wordcloudstart', anotherWordCloudStart);\n\n    // At least wait the following code before call the first iteration.\n    timer[timerId] = (settings.layoutAnimation ? loopingFunction : setTimeout)(\n      function loop() {\n        if (!layouting) {\n          return;\n        }\n        if (i >= settings.list.length) {\n          stoppingFunction(timer[timerId]);\n          sendEvent('wordcloudstop', false);\n          removeEventListener('wordcloudstart', anotherWordCloudStart);\n          delete timer[timerId];\n          return;\n        }\n        escapeTime = new Date().getTime();\n        var drawn = putWord(settings.list[i]);\n        var canceled = !sendEvent('wordclouddrawn', true, {\n          item: settings.list[i],\n          drawn: drawn\n        });\n        if (exceedTime() || canceled) {\n          stoppingFunction(timer[timerId]);\n          settings.abort();\n          sendEvent('wordcloudabort', false);\n          sendEvent('wordcloudstop', false);\n          removeEventListener('wordcloudstart', anotherWordCloudStart);\n          return;\n        }\n        i++;\n        timer[timerId] = loopingFunction(loop, settings.wait);\n      },\n      settings.wait\n    );\n  };\n\n  // All set, start the drawing\n  start();\n};\n\nWordCloud.isSupported = isSupported;\nWordCloud.minFontSize = minFontSize;\n\nexport default WordCloud;\n", "import * as echarts from 'echarts/lib/echarts';\n\nimport './WordCloudSeries';\nimport './WordCloudView';\n\nimport wordCloudLayoutHelper from './layout';\n\nif (!wordCloudLayoutHelper.isSupported) {\n  throw new Error('Sorry your browser not support wordCloud');\n}\n\n// https://github.com/timdream/wordcloud2.js/blob/c236bee60436e048949f9becc4f0f67bd832dc5c/index.js#L233\nfunction updateCanvasMask(maskCanvas) {\n  var ctx = maskCanvas.getContext('2d');\n  var imageData = ctx.getImageData(0, 0, maskCanvas.width, maskCanvas.height);\n  var newImageData = ctx.createImageData(imageData);\n\n  var toneSum = 0;\n  var toneCnt = 0;\n  for (var i = 0; i < imageData.data.length; i += 4) {\n    var alpha = imageData.data[i + 3];\n    if (alpha > 128) {\n      var tone =\n        imageData.data[i] + imageData.data[i + 1] + imageData.data[i + 2];\n      toneSum += tone;\n      ++toneCnt;\n    }\n  }\n  var threshold = toneSum / toneCnt;\n\n  for (var i = 0; i < imageData.data.length; i += 4) {\n    var tone =\n      imageData.data[i] + imageData.data[i + 1] + imageData.data[i + 2];\n    var alpha = imageData.data[i + 3];\n\n    if (alpha < 128 || tone > threshold) {\n      // Area not to draw\n      newImageData.data[i] = 0;\n      newImageData.data[i + 1] = 0;\n      newImageData.data[i + 2] = 0;\n      newImageData.data[i + 3] = 0;\n    } else {\n      // Area to draw\n      // The color must be same with backgroundColor\n      newImageData.data[i] = 255;\n      newImageData.data[i + 1] = 255;\n      newImageData.data[i + 2] = 255;\n      newImageData.data[i + 3] = 255;\n    }\n  }\n\n  ctx.putImageData(newImageData, 0, 0);\n}\n\necharts.registerLayout(function (ecModel, api) {\n  ecModel.eachSeriesByType('wordCloud', function (seriesModel) {\n    var gridRect = echarts.helper.getLayoutRect(\n      seriesModel.getBoxLayoutParams(),\n      {\n        width: api.getWidth(),\n        height: api.getHeight()\n      }\n    );\n\n    var keepAspect = seriesModel.get('keepAspect');\n    var maskImage = seriesModel.get('maskImage');\n    var ratio = maskImage ? maskImage.width / maskImage.height : 1;\n    keepAspect && adjustRectAspect(gridRect, ratio);\n\n    var data = seriesModel.getData();\n\n    var canvas = document.createElement('canvas');\n    canvas.width = gridRect.width;\n    canvas.height = gridRect.height;\n\n    var ctx = canvas.getContext('2d');\n    if (maskImage) {\n      try {\n        ctx.drawImage(maskImage, 0, 0, canvas.width, canvas.height);\n        updateCanvasMask(canvas);\n      } catch (e) {\n        console.error('Invalid mask image');\n        console.error(e.toString());\n      }\n    }\n\n    var sizeRange = seriesModel.get('sizeRange');\n    var rotationRange = seriesModel.get('rotationRange');\n    var valueExtent = data.getDataExtent('value');\n\n    var DEGREE_TO_RAD = Math.PI / 180;\n    var gridSize = seriesModel.get('gridSize');\n    wordCloudLayoutHelper(canvas, {\n      list: data\n        .mapArray('value', function (value, idx) {\n          var itemModel = data.getItemModel(idx);\n          return [\n            data.getName(idx),\n            itemModel.get('textStyle.fontSize', true) ||\n              echarts.number.linearMap(value, valueExtent, sizeRange),\n            idx\n          ];\n        })\n        .sort(function (a, b) {\n          // Sort from large to small in case there is no more room for more words\n          return b[1] - a[1];\n        }),\n      fontFamily:\n        seriesModel.get('textStyle.fontFamily') ||\n        seriesModel.get('emphasis.textStyle.fontFamily') ||\n        ecModel.get('textStyle.fontFamily'),\n      fontWeight:\n        seriesModel.get('textStyle.fontWeight') ||\n        seriesModel.get('emphasis.textStyle.fontWeight') ||\n        ecModel.get('textStyle.fontWeight'),\n\n      gridSize: gridSize,\n\n      ellipticity: gridRect.height / gridRect.width,\n\n      minRotation: rotationRange[0] * DEGREE_TO_RAD,\n      maxRotation: rotationRange[1] * DEGREE_TO_RAD,\n\n      clearCanvas: !maskImage,\n\n      rotateRatio: 1,\n\n      rotationStep: seriesModel.get('rotationStep') * DEGREE_TO_RAD,\n\n      drawOutOfBound: seriesModel.get('drawOutOfBound'),\n      shrinkToFit: seriesModel.get('shrinkToFit'),\n\n      layoutAnimation: seriesModel.get('layoutAnimation'),\n\n      shuffle: false,\n\n      shape: seriesModel.get('shape')\n    });\n\n    function onWordCloudDrawn(e) {\n      var item = e.detail.item;\n      if (e.detail.drawn && seriesModel.layoutInstance.ondraw) {\n        e.detail.drawn.gx += gridRect.x / gridSize;\n        e.detail.drawn.gy += gridRect.y / gridSize;\n        seriesModel.layoutInstance.ondraw(\n          item[0],\n          item[1],\n          item[2],\n          e.detail.drawn\n        );\n      }\n    }\n\n    canvas.addEventListener('wordclouddrawn', onWordCloudDrawn);\n\n    if (seriesModel.layoutInstance) {\n      // Dispose previous\n      seriesModel.layoutInstance.dispose();\n    }\n\n    seriesModel.layoutInstance = {\n      ondraw: null,\n\n      dispose: function () {\n        canvas.removeEventListener('wordclouddrawn', onWordCloudDrawn);\n        // Abort\n        canvas.addEventListener('wordclouddrawn', function (e) {\n          // Prevent default to cancle the event and stop the loop\n          e.preventDefault();\n        });\n      }\n    };\n  });\n});\n\necharts.registerPreprocessor(function (option) {\n  var series = (option || {}).series;\n  !echarts.util.isArray(series) && (series = series ? [series] : []);\n\n  var compats = ['shadowColor', 'shadowBlur', 'shadowOffsetX', 'shadowOffsetY'];\n\n  echarts.util.each(series, function (seriesItem) {\n    if (seriesItem && seriesItem.type === 'wordCloud') {\n      var textStyle = seriesItem.textStyle || {};\n\n      compatTextStyle(textStyle.normal);\n      compatTextStyle(textStyle.emphasis);\n    }\n  });\n\n  function compatTextStyle(textStyle) {\n    textStyle &&\n      echarts.util.each(compats, function (key) {\n        if (textStyle.hasOwnProperty(key)) {\n          textStyle['text' + echarts.format.capitalFirst(key)] = textStyle[key];\n        }\n      });\n  }\n});\n\nfunction adjustRectAspect(gridRect, aspect) {\n  // var outerWidth = gridRect.width + gridRect.x * 2;\n  // var outerHeight = gridRect.height + gridRect.y * 2;\n  var width = gridRect.width;\n  var height = gridRect.height;\n  if (width > height * aspect) {\n    gridRect.x += (width - height * aspect) / 2;\n    gridRect.width = height * aspect;\n  } else {\n    gridRect.y += (height - width / aspect) / 2;\n    gridRect.height = width / aspect;\n  }\n}\n", "import './src/wordCloud';\n", "module.exports = __WEBPACK_EXTERNAL_MODULE_echarts_lib_echarts__;", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tif(__webpack_module_cache__[moduleId]) {\n\t\treturn __webpack_module_cache__[moduleId].exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "// module exports must be returned from runtime so entry inlining is disabled\n// startup\n// Load entry module and return exports\nreturn __webpack_require__(\"./index.js\");\n"], "sourceRoot": ""}