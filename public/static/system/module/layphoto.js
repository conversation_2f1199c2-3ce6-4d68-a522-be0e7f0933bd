layui.define("jquery",function(exports){

    !function(p) {
        "use strict";
        var m, d, e, n = p.layui && layui.define, u = {
            getPath: (e = document.currentScript ? document.currentScript.src : function() {
                for (var e, t = document.scripts, i = t.length - 1, n = i; 0 < n; n--)
                    if ("interactive" === t[n].readyState) {
                        e = t[n].src;
                        break
                    }
                return e || t[i].src
            }(),
            (p.LAYUI_GLOBAL || {}).layer_dir || e.substring(0, e.lastIndexOf("/") + 1)),
            config: {
                removeFocus: !0
            },
            end: {},
            events: {
                resize: {}
            },
            minStackIndex: 0,
            minStackArr: [],
            btn: ["&#x786E;&#x5B9A;", "&#x53D6;&#x6D88;"],
            type: ["dialog", "page", "iframe", "loading", "tips"],
            getStyle: function(e, t) {
                e = e.currentStyle || p.getComputedStyle(e, null);
                return e[e.getPropertyValue ? "getPropertyValue" : "getAttribute"](t)
            },
            link: function(e, i, t) {
                var n, a, o, s, l, r;
                h.path && (n = document.getElementsByTagName("head")[0],
                    a = document.createElement("link"),
                    o = ((t = "string" == typeof i ? i : t) || e).replace(/\.|\//g, ""),
                    s = "layuicss-" + o,
                    l = "creating",
                    r = 0,
                    a.rel = "stylesheet",
                    a.href = h.path + e,
                    a.id = s,
                document.getElementById(s) || n.appendChild(a),
                "function" == typeof i) && !function c(e) {
                    var t = document.getElementById(s);
                    return 100 < ++r ? p.console && console.error(o + ".css: Invalid") : void (1989 === parseInt(u.getStyle(t, "width")) ? (e === l && t.removeAttribute("lay-status"),
                        t.getAttribute("lay-status") === l ? setTimeout(c, 100) : i()) : (t.setAttribute("lay-status", l),
                        setTimeout(function() {
                            c(l)
                        }, 100)))
                }()
            }
        }, h = {
            v: "3.7.0",
            ie: (e = navigator.userAgent.toLowerCase(),
            !!(p.ActiveXObject || "ActiveXObject"in p) && ((e.match(/msie\s(\d+)/) || [])[1] || "11")),
            index: p.layer && p.layer.v ? 1e5 : 0,
            path: u.getPath,
            config: function(e, t) {
                return h.cache = u.config = m.extend({}, u.config, e = e || {}),
                    h.path = u.config.path || h.path,
                "string" == typeof e.extend && (e.extend = [e.extend]),
                u.config.path && h.ready(),
                e.extend && (n ? layui.addcss("modules/layer/" + e.extend) : u.link("css/" + e.extend)),
                    this
            },
            ready: function(e) {
                // var t = "layer"
                //     , i = (n ? "modules/" : "css/") + "layer.css?v=" + h.v;
                // return n ? layui["layui.all"] ? "function" == typeof e && e() : layui.addcss(i, e, t) : u.link(i, e, t),
                // this
            },
            alert: function(e, t, i) {
                var n = "function" == typeof t;
                return h.open(m.extend({
                    content: e,
                    yes: i = n ? t : i
                }, n ? {} : t))
            },
            confirm: function(e, t, i, n) {
                var a = "function" == typeof t;
                return a && (n = i,
                    i = t),
                    h.open(m.extend({
                        content: e,
                        btn: u.btn,
                        yes: i,
                        btn2: n
                    }, a ? {} : t))
            },
            msg: function(e, t, i) {
                var n = "function" == typeof t
                    , a = u.config.skin
                    , a = (a ? a + " " + a + "-msg" : "") || "layui-layer-msg"
                    , o = y.anim.length - 1;
                return n && (i = t),
                    h.open(m.extend({
                        content: e,
                        time: 3e3,
                        shade: !1,
                        skin: a,
                        title: !1,
                        closeBtn: !1,
                        btn: !1,
                        resize: !1,
                        end: i,
                        removeFocus: !1
                    }, n && !u.config.skin ? {
                        skin: a + " layui-layer-hui",
                        anim: o
                    } : (-1 !== (t = t || {}).icon && (void 0 !== t.icon || u.config.skin) || (t.skin = a + " " + (t.skin || "layui-layer-hui")),
                        t)))
            },
            load: function(e, t) {
                return h.open(m.extend({
                    type: 3,
                    icon: e || 0,
                    resize: !1,
                    shade: .01,
                    removeFocus: !1
                }, t))
            },
            tips: function(e, t, i) {
                return h.open(m.extend({
                    type: 4,
                    content: [e, t],
                    closeBtn: !1,
                    time: 3e3,
                    shade: !1,
                    resize: !1,
                    fixed: !1,
                    maxWidth: 260,
                    removeFocus: !1
                }, i))
            }
        }, t = function(e) {
            var t = this
                , i = function() {
                t.creat()
            };
            t.index = ++h.index,
                t.config.maxWidth = m(d).width() - 30,
                t.config = m.extend({}, t.config, u.config, e),
                document.body ? i() : setTimeout(function() {
                    i()
                }, 30)
        }, y = (t.pt = t.prototype,
            ["layui-layer", ".layui-layer-title", ".layui-layer-main", ".layui-layer-dialog", "layui-layer-iframe", "layui-layer-content", "layui-layer-btn", "layui-layer-close"]), i = (y.anim = {
            0: "layer-anim-00",
            1: "layer-anim-01",
            2: "layer-anim-02",
            3: "layer-anim-03",
            4: "layer-anim-04",
            5: "layer-anim-05",
            6: "layer-anim-06",
            slideDown: "layer-anim-slide-down",
            slideLeft: "layer-anim-slide-left",
            slideUp: "layer-anim-slide-up",
            slideRight: "layer-anim-slide-right"
        },
            y.SHADE = "layui-layer-shade",
            y.MOVE = "layui-layer-move",
            t.pt.config = {
                type: 0,
                shade: .3,
                fixed: !0,
                move: y[1],
                title: "&#x4FE1;&#x606F;",
                offset: "auto",
                area: "auto",
                closeBtn: 1,
                icon: -1,
                time: 0,
                zIndex: 19891014,
                maxWidth: 360,
                anim: 0,
                isOutAnim: !0,
                minStack: !0,
                moveType: 1,
                resize: !0,
                scrollbar: !0,
                tips: 2
            },
            t.pt.vessel = function(e, t) {
                var i, n = this.index, a = this.config, o = a.zIndex + n, s = "object" == typeof a.title, l = a.maxmin && (1 === a.type || 2 === a.type), s = a.title ? '<div class="layui-layer-title" style="' + (s ? a.title[1] : "") + '">' + (s ? a.title[0] : a.title) + "</div>" : "";
                return a.zIndex = o,
                    t([a.shade ? '<div class="' + y.SHADE + '" id="' + y.SHADE + n + '" times="' + n + '" style="z-index:' + (o - 1) + '; "></div>' : "", '<div class="' + y[0] + " layui-layer-" + u.type[a.type] + (0 != a.type && 2 != a.type || a.shade ? "" : " layui-layer-border") + " " + (a.skin || "") + '" id="' + y[0] + n + '" type="' + u.type[a.type] + '" times="' + n + '" showtime="' + a.time + '" conType="' + (e ? "object" : "string") + '" style="z-index: ' + o + "; width:" + a.area[0] + ";height:" + a.area[1] + ";position:" + (a.fixed ? "fixed;" : "absolute;") + '">' + (e && 2 != a.type ? "" : s) + "<div" + (a.id ? ' id="' + a.id + '"' : "") + ' class="layui-layer-content' + (0 == a.type && -1 !== a.icon ? " layui-layer-padding" : "") + (3 == a.type ? " layui-layer-loading" + a.icon : "") + '">' + (n = ["layui-icon-tips", "layui-icon-success", "layui-icon-error", "layui-icon-question", "layui-icon-lock", "layui-icon-face-cry", "layui-icon-face-smile"],
                        o = "layui-anim layui-anim-rotate layui-anim-loop",
                        0 == a.type && -1 !== a.icon ? '<i class="layui-layer-face layui-icon ' + ((i = 16 == a.icon ? "layui-icon layui-icon-loading " + o : i) || n[a.icon] || n[0]) + '"></i>' : 3 == a.type ? (i = ["layui-icon-loading", "layui-icon-loading-1"],
                            2 == a.icon ? '<div class="layui-layer-loading-2 ' + o + '"></div>' : '<i class="layui-layer-loading-icon layui-icon ' + (i[a.icon] || i[0]) + " " + o + '"></i>') : "") + ((1 != a.type || !e) && a.content || "") + '</div><div class="layui-layer-setwin">' + (n = [],
                    l && (n.push('<span class="layui-layer-min"></span>'),
                        n.push('<span class="layui-layer-max"></span>')),
                    a.closeBtn && n.push('<span class="layui-icon layui-icon-close ' + [y[7], y[7] + (a.title ? a.closeBtn : 4 == a.type ? "1" : "2")].join(" ") + '"></span>'),
                        n.join("")) + "</div>" + (a.btn ? function() {
                        var e = "";
                        "string" == typeof a.btn && (a.btn = [a.btn]);
                        for (var t, i = 0, n = a.btn.length; i < n; i++)
                            e += '<a class="' + y[6] + i + '">' + a.btn[i] + "</a>";
                        return '<div class="' + (t = [y[6]],
                        a.btnAlign && t.push(y[6] + "-" + a.btnAlign),
                            t.join(" ")) + '">' + e + "</div>"
                    }() : "") + (a.resize ? '<span class="layui-layer-resize"></span>' : "") + "</div>"], s, m('<div class="' + y.MOVE + '" id="' + y.MOVE + '"></div>')),
                    this
            }
            ,
            t.pt.creat = function() {
                var e, t, i, n, a, o = this, s = o.config, l = o.index, r = "object" == typeof (f = s.content), c = m("body");
                if (s.id && m("." + y[0]).find("#" + s.id)[0])
                    e = m("#" + s.id).closest("." + y[0]),
                        t = e.attr("times"),
                        i = e.data("config"),
                        n = m("#" + y.SHADE + t),
                        "min" === (e.data("maxminStatus") || {}) ? h.restore(t) : i.hideOnClose && (n.show(),
                            e.show());
                else {
                    switch (s.removeFocus && document.activeElement.blur(),
                    "string" == typeof s.area && (s.area = "auto" === s.area ? ["", ""] : [s.area, ""]),
                    s.shift && (s.anim = s.shift),
                    6 == h.ie && (s.fixed = !1),
                        s.type) {
                        case 0:
                            s.btn = "btn"in s ? s.btn : u.btn[0],
                                h.closeAll("dialog");
                            break;
                        case 2:
                            var f = s.content = r ? s.content : [s.content || "", "auto"];
                            s.content = '<iframe scrolling="' + (s.content[1] || "auto") + '" allowtransparency="true" id="' + y[4] + l + '" name="' + y[4] + l + '" οnlοad="this.className=\'\';" class="layui-layer-load" frameborder="0" src="' + s.content[0] + '"></iframe>';
                            break;
                        case 3:
                            delete s.title,
                                delete s.closeBtn,
                            -1 === s.icon && s.icon,
                                h.closeAll("loading");
                            break;
                        case 4:
                            r || (s.content = [s.content, "body"]),
                                s.follow = s.content[1],
                                s.content = s.content[0] + '<i class="layui-layer-TipsG"></i>',
                                delete s.title,
                                s.tips = "object" == typeof s.tips ? s.tips : [s.tips, !0],
                            s.tipsMore || h.closeAll("tips")
                    }
                    o.vessel(r, function(e, t, i) {
                        c.append(e[0]),
                            r ? 2 == s.type || 4 == s.type ? m("body").append(e[1]) : f.parents("." + y[0])[0] || (f.data("display", f.css("display")).show().addClass("layui-layer-wrap").wrap(e[1]),
                                m("#" + y[0] + l).find("." + y[5]).before(t)) : c.append(e[1]),
                        m("#" + y.MOVE)[0] || c.append(u.moveElem = i),
                            o.layero = m("#" + y[0] + l),
                            o.shadeo = m("#" + y.SHADE + l),
                        s.scrollbar || u.setScrollbar(l)
                    }).auto(l),
                        o.shadeo.css({
                            "background-color": s.shade[1] || "#000",
                            opacity: s.shade[0] || s.shade
                        }),
                    2 == s.type && 6 == h.ie && o.layero.find("iframe").attr("src", f[0]),
                        4 == s.type ? o.tips() : (o.offset(),
                        parseInt(u.getStyle(document.getElementById(y.MOVE), "z-index")) || (o.layero.css("visibility", "hidden"),
                            h.ready(function() {
                                o.offset(),
                                    o.layero.css("visibility", "visible")
                            }))),
                    !s.fixed || u.events.resize[o.index] || (u.events.resize[o.index] = function() {
                        o.resize()
                    }
                        ,
                        d.on("resize", u.events.resize[o.index])),
                    s.time <= 0 || setTimeout(function() {
                        h.close(o.index)
                    }, s.time),
                        o.move().callback(),
                    y.anim[s.anim] && (a = "layer-anim " + y.anim[s.anim],
                        o.layero.addClass(a).one("webkitAnimationEnd mozAnimationEnd MSAnimationEnd oanimationend animationend", function() {
                            m(this).removeClass(a)
                        })),
                        o.layero.data("config", s)
                }
            }
            ,
            t.pt.resize = function() {
                var e = this
                    , t = e.config;
                e.offset(),
                (/^\d+%$/.test(t.area[0]) || /^\d+%$/.test(t.area[1])) && e.auto(e.index),
                4 == t.type && e.tips()
            }
            ,
            t.pt.auto = function(e) {
                var t = this.config
                    , i = m("#" + y[0] + e)
                    , n = ("" === t.area[0] && 0 < t.maxWidth && (h.ie && h.ie < 8 && t.btn && i.width(i.innerWidth()),
                i.outerWidth() > t.maxWidth) && i.width(t.maxWidth),
                    [i.innerWidth(), i.innerHeight()])
                    , a = i.find(y[1]).outerHeight() || 0
                    , o = i.find("." + y[6]).outerHeight() || 0
                    , e = function(e) {
                    (e = i.find(e)).height(n[1] - a - o - 2 * (0 | parseFloat(e.css("padding-top"))))
                };
                return 2 === t.type ? e("iframe") : "" === t.area[1] ? 0 < t.maxHeight && i.outerHeight() > t.maxHeight ? (n[1] = t.maxHeight,
                    e("." + y[5])) : t.fixed && n[1] >= d.height() && (n[1] = d.height(),
                    e("." + y[5])) : e("." + y[5]),
                    this
            }
            ,
            t.pt.offset = function() {
                var e = this
                    , t = e.config
                    , i = e.layero
                    , n = [i.outerWidth(), i.outerHeight()]
                    , a = "object" == typeof t.offset;
                e.offsetTop = (d.height() - n[1]) / 2,
                    e.offsetLeft = (d.width() - n[0]) / 2,
                    a ? (e.offsetTop = t.offset[0],
                        e.offsetLeft = t.offset[1] || e.offsetLeft) : "auto" !== t.offset && ("t" === t.offset ? e.offsetTop = 0 : "r" === t.offset ? e.offsetLeft = d.width() - n[0] : "b" === t.offset ? e.offsetTop = d.height() - n[1] : "l" === t.offset ? e.offsetLeft = 0 : "lt" === t.offset ? (e.offsetTop = 0,
                        e.offsetLeft = 0) : "lb" === t.offset ? (e.offsetTop = d.height() - n[1],
                        e.offsetLeft = 0) : "rt" === t.offset ? (e.offsetTop = 0,
                        e.offsetLeft = d.width() - n[0]) : "rb" === t.offset ? (e.offsetTop = d.height() - n[1],
                        e.offsetLeft = d.width() - n[0]) : e.offsetTop = t.offset),
                t.fixed || (e.offsetTop = /%$/.test(e.offsetTop) ? d.height() * parseFloat(e.offsetTop) / 100 : parseFloat(e.offsetTop),
                    e.offsetLeft = /%$/.test(e.offsetLeft) ? d.width() * parseFloat(e.offsetLeft) / 100 : parseFloat(e.offsetLeft),
                    e.offsetTop += d.scrollTop(),
                    e.offsetLeft += d.scrollLeft()),
                "min" === i.data("maxminStatus") && (e.offsetTop = d.height() - (i.find(y[1]).outerHeight() || 0),
                    e.offsetLeft = i.css("left")),
                    i.css({
                        top: e.offsetTop,
                        left: e.offsetLeft
                    })
            }
            ,
            t.pt.tips = function() {
                var e = this.config
                    , t = this.layero
                    , i = [t.outerWidth(), t.outerHeight()]
                    , n = m(e.follow)
                    , a = {
                    width: (n = n[0] ? n : m("body")).outerWidth(),
                    height: n.outerHeight(),
                    top: n.offset().top,
                    left: n.offset().left
                }
                    , o = t.find(".layui-layer-TipsG")
                    , n = e.tips[0];
                e.tips[1] || o.remove(),
                    a.autoLeft = function() {
                        0 < a.left + i[0] - d.width() ? (a.tipLeft = a.left + a.width - i[0],
                            o.css({
                                right: 12,
                                left: "auto"
                            })) : a.tipLeft = a.left
                    }
                    ,
                    a.where = [function() {
                        a.autoLeft(),
                            a.tipTop = a.top - i[1] - 10,
                            o.removeClass("layui-layer-TipsB").addClass("layui-layer-TipsT").css("border-right-color", e.tips[1])
                    }
                        , function() {
                            a.tipLeft = a.left + a.width + 10,
                                a.tipTop = a.top,
                                o.removeClass("layui-layer-TipsL").addClass("layui-layer-TipsR").css("border-bottom-color", e.tips[1])
                        }
                        , function() {
                            a.autoLeft(),
                                a.tipTop = a.top + a.height + 10,
                                o.removeClass("layui-layer-TipsT").addClass("layui-layer-TipsB").css("border-right-color", e.tips[1])
                        }
                        , function() {
                            a.tipLeft = a.left - i[0] - 10,
                                a.tipTop = a.top,
                                o.removeClass("layui-layer-TipsR").addClass("layui-layer-TipsL").css("border-bottom-color", e.tips[1])
                        }
                    ],
                    a.where[n - 1](),
                    1 === n ? a.top - (d.scrollTop() + i[1] + 16) < 0 && a.where[2]() : 2 === n ? 0 < d.width() - (a.left + a.width + i[0] + 16) || a.where[3]() : 3 === n ? 0 < a.top - d.scrollTop() + a.height + i[1] + 16 - d.height() && a.where[0]() : 4 === n && 0 < i[0] + 16 - a.left && a.where[1](),
                    t.find("." + y[5]).css({
                        "background-color": e.tips[1],
                        "padding-right": e.closeBtn ? "30px" : ""
                    }),
                    t.css({
                        left: a.tipLeft - (e.fixed ? d.scrollLeft() : 0),
                        top: a.tipTop - (e.fixed ? d.scrollTop() : 0)
                    })
            }
            ,
            t.pt.move = function() {
                var n = this
                    , a = n.config
                    , e = m(document)
                    , o = n.layero
                    , r = ["LAY_MOVE_DICT", "LAY_RESIZE_DICT"]
                    , t = o.find(a.move)
                    , i = o.find(".layui-layer-resize");
                return a.move && t.css("cursor", "move"),
                    t.on("mousedown", function(e) {
                        var t, i;
                        e.button || (t = m(this),
                            i = {},
                        a.move && (i.layero = o,
                            i.config = a,
                            i.offset = [e.clientX - parseFloat(o.css("left")), e.clientY - parseFloat(o.css("top"))],
                            t.data(r[0], i),
                            u.eventMoveElem = t,
                            u.moveElem.css("cursor", "move").show()),
                            e.preventDefault())
                    }),
                    i.on("mousedown", function(e) {
                        var t = m(this)
                            , i = {};
                        a.resize && (i.layero = o,
                            i.config = a,
                            i.offset = [e.clientX, e.clientY],
                            i.index = n.index,
                            i.area = [o.outerWidth(), o.outerHeight()],
                            t.data(r[1], i),
                            u.eventResizeElem = t,
                            u.moveElem.css("cursor", "se-resize").show()),
                            e.preventDefault()
                    }),
                u.docEvent || (e.on("mousemove", function(e) {
                    var t, i, n, a, o, s, l;
                    u.eventMoveElem && (t = (a = u.eventMoveElem.data(r[0]) || {}).layero,
                        o = a.config,
                        s = e.clientX - a.offset[0],
                        l = e.clientY - a.offset[1],
                        i = "fixed" === t.css("position"),
                        e.preventDefault(),
                        a.stX = i ? 0 : d.scrollLeft(),
                        a.stY = i ? 0 : d.scrollTop(),
                    o.moveOut || (i = d.width() - t.outerWidth() + a.stX,
                        n = d.height() - t.outerHeight() + a.stY,
                    i < (s = s < a.stX ? a.stX : s) && (s = i),
                    n < (l = l < a.stY ? a.stY : l) && (l = n)),
                        t.css({
                            left: s,
                            top: l
                        })),
                    u.eventResizeElem && (o = (a = u.eventResizeElem.data(r[1]) || {}).config,
                        s = e.clientX - a.offset[0],
                        l = e.clientY - a.offset[1],
                        e.preventDefault(),
                        h.style(a.index, {
                            width: a.area[0] + s,
                            height: a.area[1] + l
                        }),
                        o.resizing) && o.resizing(a.layero)
                }).on("mouseup", function(e) {
                    var t, i;
                    u.eventMoveElem && (i = (t = u.eventMoveElem.data(r[0]) || {}).config,
                        u.eventMoveElem.removeData(r[0]),
                        delete u.eventMoveElem,
                        u.moveElem.hide(),
                        i.moveEnd) && i.moveEnd(t.layero),
                    u.eventResizeElem && (u.eventResizeElem.removeData(r[1]),
                        delete u.eventResizeElem,
                        u.moveElem.hide())
                }),
                    u.docEvent = !0),
                    n
            }
            ,
            t.pt.callback = function() {
                var t = this
                    , i = t.layero
                    , n = t.config;
                t.openLayer(),
                n.success && (2 == n.type ? i.find("iframe").on("load", function() {
                    n.success(i, t.index, t)
                }) : n.success(i, t.index, t)),
                6 == h.ie && t.IE6(i),
                    i.find("." + y[6]).children("a").on("click", function() {
                        var e = m(this).index();
                        0 === e ? n.yes ? n.yes(t.index, i, t) : n.btn1 ? n.btn1(t.index, i, t) : h.close(t.index) : !1 !== (n["btn" + (e + 1)] && n["btn" + (e + 1)](t.index, i, t)) && h.close(t.index)
                    }),
                    i.find("." + y[7]).on("click", function() {
                        !1 !== (n.cancel && n.cancel(t.index, i, t)) && h.close(t.index)
                    }),
                n.shadeClose && t.shadeo.on("click", function() {
                    h.close(t.index)
                }),
                    i.find(".layui-layer-min").on("click", function() {
                        !1 !== (n.min && n.min(i, t.index, t)) && h.min(t.index, n)
                    }),
                    i.find(".layui-layer-max").on("click", function() {
                        m(this).hasClass("layui-layer-maxmin") ? (h.restore(t.index),
                        n.restore && n.restore(i, t.index, t)) : (h.full(t.index, n),
                            setTimeout(function() {
                                n.full && n.full(i, t.index, t)
                            }, 100))
                    }),
                n.end && (u.end[t.index] = n.end)
            }
            ,
            u.reselect = function() {
                m.each(m("select"), function(e, t) {
                    var i = m(this);
                    i.parents("." + y[0])[0] || 1 == i.attr("layer") && m("." + y[0]).length < 1 && i.removeAttr("layer").show()
                })
            }
            ,
            t.pt.IE6 = function(e) {
                m("select").each(function(e, t) {
                    var i = m(this);
                    i.parents("." + y[0])[0] || "none" !== i.css("display") && i.attr({
                        layer: "1"
                    }).hide()
                })
            }
            ,
            t.pt.openLayer = function() {
                h.zIndex = this.config.zIndex,
                    h.setTop = function(e) {
                        return h.zIndex = parseInt(e[0].style.zIndex),
                            e.on("mousedown", function() {
                                h.zIndex++,
                                    e.css("z-index", h.zIndex + 1)
                            }),
                            h.zIndex
                    }
            }
            ,
            u.record = function(e) {
                if (!e[0])
                    return p.console && console.error("index error");
                var t = [e[0].style.width || e.width(), e[0].style.height || e.height(), e.position().top, e.position().left + parseFloat(e.css("margin-left"))];
                e.find(".layui-layer-max").addClass("layui-layer-maxmin"),
                    e.attr({
                        area: t
                    })
            }
            ,
            u.setScrollbar = function(e) {
                y.html.css("overflow", "hidden").attr("layer-full", e)
            }
            ,
            u.restScrollbar = function(e) {
                y.html.attr("layer-full") == e && (y.html[0].style[y.html[0].style.removeProperty ? "removeProperty" : "removeAttribute"]("overflow"),
                    y.html.removeAttr("layer-full"))
            }
            ,
            (p.layer = h).getChildFrame = function(e, t) {
                return t = t || m("." + y[4]).attr("times"),
                    m("#" + y[0] + t).find("iframe").contents().find(e)
            }
            ,
            h.getFrameIndex = function(e) {
                return m("#" + e).parents("." + y[4]).attr("times")
            }
            ,
            h.iframeAuto = function(e) {
                var t, i, n;
                e && (t = h.getChildFrame("html", e).outerHeight(),
                    i = (e = m("#" + y[0] + e)).find(y[1]).outerHeight() || 0,
                    n = e.find("." + y[6]).outerHeight() || 0,
                    e.css({
                        height: t + i + n
                    }),
                    e.find("iframe").css({
                        height: t
                    }))
            }
            ,
            h.iframeSrc = function(e, t) {
                m("#" + y[0] + e).find("iframe").attr("src", t)
            }
            ,
            h.style = function(e, t, i) {
                var e = m("#" + y[0] + e)
                    , n = e.find(".layui-layer-content")
                    , a = e.attr("type")
                    , o = e.find(y[1]).outerHeight() || 0
                    , s = e.find("." + y[6]).outerHeight() || 0;
                e.attr("minLeft");
                a !== u.type[3] && a !== u.type[4] && (i || (parseFloat(t.width) <= 260 && (t.width = 260),
                parseFloat(t.height) - o - s <= 64 && (t.height = 64 + o + s)),
                    e.css(t),
                    s = e.find("." + y[6]).outerHeight() || 0,
                    a === u.type[2] ? e.find("iframe").css({
                        height: ("number" == typeof t.height ? t.height : e.height()) - o - s
                    }) : n.css({
                        height: ("number" == typeof t.height ? t.height : e.height()) - o - s - parseFloat(n.css("padding-top")) - parseFloat(n.css("padding-bottom"))
                    }))
            }
            ,
            h.min = function(e, t) {
                var i, n, a, o, s, l, r = m("#" + y[0] + e), c = r.data("maxminStatus");
                "min" !== c && ("max" === c && h.restore(e),
                    r.data("maxminStatus", "min"),
                    t = t || r.data("config") || {},
                    c = m("#" + y.SHADE + e),
                    i = r.find(".layui-layer-min"),
                    n = r.find(y[1]).outerHeight() || 0,
                    o = (a = "string" == typeof (o = r.attr("minLeft"))) ? o : 181 * u.minStackIndex + "px",
                    s = r.css("position"),
                    l = {
                        width: 180,
                        height: n,
                        position: "fixed",
                        overflow: "hidden"
                    },
                    u.record(r),
                0 < u.minStackArr.length && (o = u.minStackArr[0],
                    u.minStackArr.shift()),
                parseFloat(o) + 180 > d.width() && (o = d.width() - 180 - (u.minStackArr.edgeIndex = u.minStackArr.edgeIndex || 0,
                    u.minStackArr.edgeIndex += 3)) < 0 && (o = 0),
                t.minStack && (l.left = o,
                    l.top = d.height() - n,
                a || u.minStackIndex++,
                    r.attr("minLeft", o)),
                    r.attr("position", s),
                    h.style(e, l, !0),
                    i.hide(),
                "page" === r.attr("type") && r.find(y[4]).hide(),
                    u.restScrollbar(e),
                    c.hide())
            }
            ,
            h.restore = function(e) {
                var t = m("#" + y[0] + e)
                    , i = m("#" + y.SHADE + e)
                    , n = t.attr("area").split(",")
                    , a = t.attr("type")
                    , o = t.data("config") || {};
                t.removeData("maxminStatus"),
                    h.style(e, {
                        width: n[0],
                        height: n[1],
                        top: parseFloat(n[2]),
                        left: parseFloat(n[3]),
                        position: t.attr("position"),
                        overflow: "visible"
                    }, !0),
                    t.find(".layui-layer-max").removeClass("layui-layer-maxmin"),
                    t.find(".layui-layer-min").show(),
                "page" === a && t.find(y[4]).show(),
                    o.scrollbar ? u.restScrollbar(e) : u.setScrollbar(e),
                    i.show()
            }
            ,
            h.full = function(t) {
                var i = m("#" + y[0] + t)
                    , e = i.data("maxminStatus");
                "max" !== e && ("min" === e && h.restore(t),
                    i.data("maxminStatus", "max"),
                    u.record(i),
                y.html.attr("layer-full") || u.setScrollbar(t),
                    setTimeout(function() {
                        var e = "fixed" === i.css("position");
                        h.style(t, {
                            top: e ? 0 : d.scrollTop(),
                            left: e ? 0 : d.scrollLeft(),
                            width: "100%",
                            height: "100%"
                        }, !0),
                            i.find(".layui-layer-min").hide()
                    }, 100))
            }
            ,
            h.title = function(e, t) {
                m("#" + y[0] + (t || h.index)).find(y[1]).html(e)
            }
            ,
            h.close = function(o, s) {
                var l, e, r = (t = m("." + y[0]).children("#" + o).closest("." + y[0]))[0] ? (o = t.attr("times"),
                    t) : m("#" + y[0] + o), c = r.attr("type"), t = r.data("config") || {}, f = t.id && t.hideOnClose;
                r[0] && (l = {
                    slideDown: "layer-anim-slide-down-out",
                    slideLeft: "layer-anim-slide-left-out",
                    slideUp: "layer-anim-slide-up-out",
                    slideRight: "layer-anim-slide-right-out"
                }[t.anim] || "layer-anim-close",
                    e = function() {
                        var e = "layui-layer-wrap";
                        if (f)
                            return r.removeClass("layer-anim " + l),
                                r.hide();
                        if (c === u.type[1] && "object" === r.attr("conType")) {
                            r.children(":not(." + y[5] + ")").remove();
                            for (var t = r.find("." + e), i = 0; i < 2; i++)
                                t.unwrap();
                            t.css("display", t.data("display")).removeClass(e)
                        } else {
                            if (c === u.type[2])
                                try {
                                    var n = m("#" + y[4] + o)[0];
                                    n.contentWindow.document.write(""),
                                        n.contentWindow.close(),
                                        r.find("." + y[5])[0].removeChild(n)
                                } catch (a) {}
                            r[0].innerHTML = "",
                                r.remove()
                        }
                        "function" == typeof u.end[o] && u.end[o](),
                            delete u.end[o],
                        "function" == typeof s && s(),
                        u.events.resize[o] && (d.off("resize", u.events.resize[o]),
                            delete u.events.resize[o])
                    }
                    ,
                    m("#" + y.SHADE + o)[f ? "hide" : "remove"](),
                t.isOutAnim && r.addClass("layer-anim " + l),
                6 == h.ie && u.reselect(),
                    u.restScrollbar(o),
                "string" == typeof r.attr("minLeft") && (u.minStackIndex--,
                    u.minStackArr.push(r.attr("minLeft"))),
                    h.ie && h.ie < 10 || !t.isOutAnim ? e() : setTimeout(function() {
                        e()
                    }, 200))
            }
            ,
            h.closeAll = function(n, a) {
                "function" == typeof n && (a = n,
                    n = null);
                var o = m("." + y[0]);
                m.each(o, function(e) {
                    var t = m(this)
                        , i = n ? t.attr("type") === n : 1;
                    i && h.close(t.attr("times"), e === o.length - 1 ? a : null)
                }),
                0 === o.length && "function" == typeof a && a()
            }
            ,
            h.closeLast = function(e) {
                h.close(m(".layui-layer-" + (e = e || "page") + ":last").attr("times"))
            }
            ,
        h.cache || {}), g = function(e) {
            return i.skin ? " " + i.skin + " " + i.skin + "-" + e : ""
        };
        h.prompt = function(i, n) {
            var e = ""
                , t = "";
            "function" == typeof (i = i || {}) && (n = i),
            i.area && (e = 'style="width: ' + (o = i.area)[0] + "; height: " + o[1] + ';"',
                delete i.area),
            i.placeholder && (t = ' placeholder="' + i.placeholder + '"');
            var a, o = 2 == i.formType ? '<textarea class="layui-layer-input"' + e + t + "></textarea>" : '<input type="' + (1 == i.formType ? "password" : "text") + '" class="layui-layer-input"' + t + ">", s = i.success;
            return delete i.success,
                h.open(m.extend({
                    type: 1,
                    btn: ["&#x786E;&#x5B9A;", "&#x53D6;&#x6D88;"],
                    content: o,
                    skin: "layui-layer-prompt" + g("prompt"),
                    maxWidth: d.width(),
                    success: function(e) {
                        (a = e.find(".layui-layer-input")).val(i.value || "").focus(),
                        "function" == typeof s && s(e)
                    },
                    resize: !1,
                    yes: function(e) {
                        var t = a.val();
                        t.length > (i.maxlength || 500) ? h.tips("&#x6700;&#x591A;&#x8F93;&#x5165;" + (i.maxlength || 500) + "&#x4E2A;&#x5B57;&#x6570;", a, {
                            tips: 1
                        }) : n && n(t, e, a)
                    }
                }, i))
        }
            ,
            h.tab = function(n) {
                var a = (n = n || {}).tab || {}
                    , o = "layui-this"
                    , s = n.success;
                return delete n.success,
                    h.open(m.extend({
                        type: 1,
                        skin: "layui-layer-tab" + g("tab"),
                        resize: !1,
                        title: function() {
                            var e = a.length
                                , t = 1
                                , i = "";
                            if (0 < e)
                                for (i = '<span class="' + o + '">' + a[0].title + "</span>"; t < e; t++)
                                    i += "<span>" + a[t].title + "</span>";
                            return i
                        }(),
                        content: '<ul class="layui-layer-tabmain">' + function() {
                            var e = a.length
                                , t = 1
                                , i = "";
                            if (0 < e)
                                for (i = '<li class="layui-layer-tabli ' + o + '">' + (a[0].content || "no content") + "</li>"; t < e; t++)
                                    i += '<li class="layui-layer-tabli">' + (a[t].content || "no  content") + "</li>";
                            return i
                        }() + "</ul>",
                        success: function(e) {
                            var t = e.find(".layui-layer-title").children()
                                , i = e.find(".layui-layer-tabmain").children();
                            t.on("mousedown", function(e) {
                                e.stopPropagation ? e.stopPropagation() : e.cancelBubble = !0;
                                var e = m(this)
                                    , t = e.index();
                                e.addClass(o).siblings().removeClass(o),
                                    i.eq(t).show().siblings().hide(),
                                "function" == typeof n.change && n.change(t)
                            }),
                            "function" == typeof s && s(e)
                        }
                    }, n))
            }
            ,
            h.photos = function(n, e, a) {
                var o = {};
                if ((n = m.extend(!0, {
                    toolbar: !0,
                    footer: !0
                }, n)).photos) {
                    var t = !("string" == typeof n.photos || n.photos instanceof m)
                        , i = t ? n.photos : {}
                        , s = i.data || []
                        , l = i.start || 0
                        , r = n.success;
                    if (o.imgIndex = 1 + (0 | l),
                        n.img = n.img || "img",
                        delete n.success,
                        t) {
                        if (0 === s.length)
                            return h.msg("&#x6CA1;&#x6709;&#x56FE;&#x7247;")
                    } else {
                        var c = m(n.photos)
                            , f = function() {
                            s = [],
                                c.find(n.img).each(function(e) {
                                    var t = m(this);
                                    t.attr("layer-index", e),
                                        s.push({
                                            alt: t.attr("alt"),
                                            pid: t.attr("layer-pid"),
                                            src: t.attr("lay-src") || t.attr("layer-src") || t.attr("src"),
                                            thumb: t.attr("src")
                                        })
                                })
                        };
                        if (f(),
                        0 === s.length)
                            return;
                        if (e || c.on("click", n.img, function() {
                            f();
                            var e = m(this).attr("layer-index");
                            h.photos(m.extend(n, {
                                photos: {
                                    start: e,
                                    data: s,
                                    tab: n.tab
                                },
                                full: n.full
                            }), !0)
                        }),
                            !e)
                            return
                    }
                    o.imgprev = function(e) {
                        o.imgIndex--,
                        o.imgIndex < 1 && (o.imgIndex = s.length),
                            o.tabimg(e)
                    }
                        ,
                        o.imgnext = function(e, t) {
                            o.imgIndex++,
                            o.imgIndex > s.length && (o.imgIndex = 1,
                                t) || o.tabimg(e)
                        }
                        ,
                        o.keyup = function(e) {
                            var t;
                            o.end || (t = e.keyCode,
                                e.preventDefault(),
                                37 === t ? o.imgprev(!0) : 39 === t ? o.imgnext(!0) : 27 === t && h.close(o.index))
                        }
                        ,
                        o.tabimg = function(e) {
                            if (!(s.length <= 1))
                                return i.start = o.imgIndex - 1,
                                    h.close(o.index),
                                    h.photos(n, !0, e)
                        }
                        ,
                        o.isNumber = function(e) {
                            return "number" == typeof e && !isNaN(e)
                        }
                        ,
                        o.image = {},
                        o.getTransform = function(e) {
                            var t = []
                                , i = e.rotate
                                , n = e.scaleX
                                , e = e.scale;
                            return o.isNumber(i) && 0 !== i && t.push("rotate(" + i + "deg)"),
                            o.isNumber(n) && 1 !== n && t.push("scaleX(" + n + ")"),
                            o.isNumber(e) && t.push("scale(" + e + ")"),
                                t.length ? t.join(" ") : "none"
                        }
                        ,
                        o.event = function(e, i, n) {
                            o.main.find(".layui-layer-photos-prev").on("click", function(e) {
                                e.preventDefault(),
                                    o.imgprev(!0)
                            }),
                                o.main.find(".layui-layer-photos-next").on("click", function(e) {
                                    e.preventDefault(),
                                        o.imgnext(!0)
                                }),
                                m(document).on("keyup", o.keyup),
                                e.off("click").on("click", "*[toolbar-event]", function() {
                                    var e = m(this);
                                    switch (e.attr("toolbar-event")) {
                                        case "rotate":
                                            o.image.rotate = ((o.image.rotate || 0) + Number(e.attr("data-option"))) % 360,
                                                o.imgElem.css({
                                                    transform: o.getTransform(o.image)
                                                });
                                            break;
                                        case "scalex":
                                            o.image.scaleX = -1 === o.image.scaleX ? 1 : -1,
                                                o.imgElem.css({
                                                    transform: o.getTransform(o.image)
                                                });
                                            break;
                                        case "zoom":
                                            var t = Number(e.attr("data-option"));
                                            o.image.scale = (o.image.scale || 1) + t,
                                            t < 0 && o.image.scale < 0 - t && (o.image.scale = 0 - t),
                                                o.imgElem.css({
                                                    transform: o.getTransform(o.image)
                                                });
                                            break;
                                        case "reset":
                                            o.image.scaleX = 1,
                                                o.image.scale = 1,
                                                o.image.rotate = 0,
                                                o.imgElem.css({
                                                    transform: "none"
                                                });
                                            break;
                                        case "close":
                                            h.close(i)
                                    }
                                    n.offset(),
                                        n.auto(i)
                                }),
                                o.main.on("mousewheel DOMMouseScroll", function(e) {
                                    var e = e.originalEvent.wheelDelta || -e.originalEvent.detail
                                        , t = o.main.find('[toolbar-event="zoom"]');
                                    (0 < e ? t.eq(0) : t.eq(1)).trigger("click")
                                })
                        }
                        ,
                        o.loadi = h.load(1, {
                            shade: !("shade"in n) && .9,
                            scrollbar: !1
                        });
                    var t = s[l].src
                        , d = function(e) {
                        h.close(o.loadi);
                        var t, i = s[l].alt || "";
                        a && (n.anim = -1),
                            o.index = h.open(m.extend({
                                type: 1,
                                id: "layui-layer-photos",
                                area: (e = [e.width, e.height],
                                    t = [m(p).width() - 100, m(p).height() - 100],
                                !n.full && (t[0] < e[0] || t[1] < e[1]) && ((t = [e[0] / t[0], e[1] / t[1]])[1] < t[0] ? (e[0] = e[0] / t[0],
                                    e[1] = e[1] / t[0]) : t[0] < t[1] && (e[0] = e[0] / t[1],
                                    e[1] = e[1] / t[1])),
                                    [e[0] + "px", e[1] + "px"]),
                                title: !1,
                                shade: .9,
                                shadeClose: !0,
                                closeBtn: !1,
                                move: ".layer-layer-photos-main img",
                                moveType: 1,
                                scrollbar: !1,
                                moveOut: !0,
                                anim: 5,
                                isOutAnim: !1,
                                skin: "layui-layer-photos" + g("photos"),
                                content: '<div class="layer-layer-photos-main"><img src="' + s[l].src + '" alt="' + i + '" layer-pid="' + (s[l].pid || "") + '">' + (t = ['<div class="layui-layer-photos-pointer">'],
                                1 < s.length && t.push(['<div class="layer-layer-photos-page">', '<span class="layui-icon layui-icon-left layui-layer-photos-prev"></span>', '<span class="layui-icon layui-icon-right layui-layer-photos-next"></span>', "</div>"].join("")),
                                n.toolbar && t.push(['<div class="layui-layer-photos-toolbar layui-layer-photos-header">', '<span toolbar-event="rotate" data-option="90" title="\u65cb\u8f6c"><i class="layui-icon layui-icon-refresh"></i></span>', '<span toolbar-event="scalex" title="\u53d8\u6362"><i class="layui-icon layui-icon-slider"></i></span>', '<span toolbar-event="zoom" data-option="0.1" title="\u653e\u5927"><i class="layui-icon layui-icon-add-circle"></i></span>', '<span toolbar-event="zoom" data-option="-0.1" title="\u7f29\u5c0f"><i class="layui-icon layui-icon-reduce-circle"></i></span>', '<span toolbar-event="reset" title="\u8fd8\u539f"><i class="layui-icon layui-icon-refresh-1"></i></span>', '<span toolbar-event="close" title="\u5173\u95ed"><i class="layui-icon layui-icon-close"></i></span>', "</div>"].join("")),
                                    // n.footer && t.push(['<div class="layui-layer-photos-toolbar layui-layer-photos-footer">', "<h3>" + i + "</h3>", "<em>" + o.imgIndex + " / " + s.length + "</em>", '<a href="' + s[l].src + '" target="_blank">\u67e5\u770b\u539f\u56fe</a>', "</div>"].join("")),
                                n.footer && t.push(['<div class="layui-layer-photos-toolbar layui-layer-photos-footer">',
                                    "<h3>" + i + "</h3>", "<em>" + o.imgIndex + " / " + s.length + "</em>", "</div>"].join("")),
                                    t.push("</div>"),
                                    t.join("")) + "</div>",
                                success: function(e, t, i) {
                                    o.main = e.find(".layer-layer-photos-main"),
                                        o.footer = e.find(".layui-layer-photos-footer"),
                                        o.imgElem = o.main.children("img"),
                                        o.event(e, t, i),
                                    n.tab && n.tab(s[l], e),
                                    "function" == typeof r && r(e)
                                },
                                end: function() {
                                    o.end = !0,
                                        m(document).off("keyup", o.keyup)
                                }
                            }, n))
                    }
                        , u = function() {
                        h.close(o.loadi),
                            h.msg("&#x5F53;&#x524D;&#x56FE;&#x7247;&#x5730;&#x5740;&#x5F02;&#x5E38;<br>&#x662F;&#x5426;&#x7EE7;&#x7EED;&#x67E5;&#x770B;&#x4E0B;&#x4E00;&#x5F20;&#xFF1F;", {
                                time: 3e4,
                                btn: ["&#x4E0B;&#x4E00;&#x5F20;", "&#x4E0D;&#x770B;&#x4E86;"],
                                yes: function() {
                                    1 < s.length && o.imgnext(!0, !0)
                                }
                            })
                    }
                        , y = new Image;
                    (y.src = t,
                        y.complete) ? d(y) : (y.onload = function() {
                            y.onload = null,
                                d(y)
                        }
                            ,
                            y.onerror = function(e) {
                                y.onerror = null,
                                    u(e)
                            }
                    )
                }
            }
            ,
            u.run = function(e) {
                d = (m = e)(p);
                var e = navigator.userAgent.toLowerCase()
                    , e = /android|iphone|ipod|ipad|ios/.test(e)
                    , n = m(p);
                e && m.each({
                    Height: "height",
                    Width: "width"
                }, function(e, t) {
                    var i = "inner" + e;
                    d[t] = function() {
                        return i in p ? p[i] : n[t]()
                    }
                }),
                    y.html = m("html"),
                    h.open = function(e) {
                        return new t(e).index
                    }
            }
            ,
            p.layui && layui.define ? (h.ready(),
                layui.define("jquery", function(e) {
                    h.path = layui.cache.dir,
                        u.run(layui.$),
                        e("layer", p.layer = h)
                })) : "function" == typeof define && define.amd ? define(["jquery"], function() {
                return u.run(p.jQuery),
                    h
            }) : (h.ready(),
                u.run(p.jQuery))
    }(window);

    exports('layphoto',{});
});
