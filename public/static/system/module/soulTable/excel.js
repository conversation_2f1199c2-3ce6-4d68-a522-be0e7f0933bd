"undefined"==typeof layui&&"undefined"==typeof jQuery&&console.error("非layui调用请先加载jQuery"),"undefined"!=typeof jQuery&&($=jQuery),LAY_EXCEL={downloadExl:function(e,t,r){this.exportExcel({sheet1:e},t+"."+(r=r||"xlsx"),r,null)},exportExcel:function(e,t,r,n){r=r||"xlsx",t=t||"导出数据."+r;var a=XLSX.utils.book_new(),s={Title:t,Subject:"Export From web browser",Author:"excel.wj2015.com",Manager:"",Company:"",Category:"",Keywords:"",Comments:"",LastAuthor:"",CreatedData:new Date};n&&n.Props&&(s=$.extend(s,n.Props)),a.compression=!n||n.compression,!1!==a.compression&&(a.compression=!0),a.Props=s;var i,o,l,c,f,h,u={"!merges":null,"!margins":null,"!cols":null,"!rows":null,"!protect":null,"!autofilter":null};for(i in u=n&&n.extend?$.extend(u,n.extend):u)u.hasOwnProperty(i)&&(u[i]||delete u[i]);for(o in e=$.isArray(e)?{sheet1:e}:e)e.hasOwnProperty(o)&&(l=e[o],a.SheetNames.push(o),c=!1,(c=l.length&&l[0]&&$.isArray(l[0])?!0:c)?h=XLSX.utils.aoa_to_sheet(l):(c={},l.length&&(c.headers=l.unshift(),c.skipHeader=!0,f=this.splitContent(l)),h=XLSX.utils.json_to_sheet(l,c),u[o]?$.extend(h,u[o]):$.extend(h,u),void 0!==f&&this.mergeCellOpt(h,f.style)),a.Sheets[o]=h);r=XLSX.write(a,{bookType:r,type:"binary",cellStyles:!0,compression:a.compression});saveAs(new Blob([this.s2ab(r)],{type:"application/octet-stream"}),t)},splitContent:function(e){for(var t={},r=0;r<e.length;r++){var n,a,s=e[r],i=0;for(n in s)s.hasOwnProperty(n)&&("object"==typeof(a=s[n])?null!==a?t[this.numToTitle(i+1)+(parseInt(r)+1)]=a:s[n]="":(0===a&&(a={v:"0",s:{alignment:{horizontal:"right"}}}),t[this.numToTitle(i+1)+(parseInt(r)+1)]=a),i++)}return{content:e,style:t}},mergeCellOpt:function(e,t){for(var r in t)if(t.hasOwnProperty(r)){var n=t[r];if(e[r]){for(var a=["t","w","f","r","h","c","z","l","s"],s=0;s<a.length;s++)e[r][a[s]]=e[r][a[s]];$.extend(e[r],n)}}},tableToJson:function(e){e=$(e);var t=[];e.find("thead > tr").each(function(){var e=[];$(this).find("td,th").each(function(){e.push($(this).text())}),t.push(e)});var r=[];return e.find("tbody > tr").each(function(){var e=[];$(this).find("td").each(function(){e.push($(this).text())}),r.push(e)}),{head:t,body:r}},numsTitleCache:{},titleNumsCache:{},numToTitle:function(e){if(this.numsTitleCache[e])return this.numsTitleCache[e];var t="";if(26<e){var r=e%26,t=this.numToTitle((e-r)/26)+this.numToTitle(r||26);return this.numsTitleCache[e]=t,this.titleNumsCache[t]=e,t}return t=String.fromCharCode(64+e),this.numsTitleCache[e]=t,this.titleNumsCache[t]=e,t},titleToNum:function(e){if(this.titleNumsCache[e])return this.titleNumsCache[e];var t,r=e.length,n=0;for(t in e)e.hasOwnProperty(t)&&(n+=(e[t].charCodeAt()-64)*Math.pow(26,r-t-1));return this.numsTitleCache[n]=e,this.titleNumsCache[e]=n},setExportCellStyle:function(e,t,r,n){if("object"!=typeof e||!e.length||!e[0]||!Object.keys(e[0]).length)return[];var a=Object.keys(e[0]),s={c:0,r:0},i={c:e.length-1,r:a.length-1};t&&"string"==typeof t&&((t=t.split(":"))[0].length&&(s=this.splitPosition(t[0])),void 0!==t[1]&&""!==t[1]&&(i=this.splitPosition(t[1]))),s.c>i.c&&console.error("开始列不得大于结束列"),s.r>i.r&&console.error("开始行不得大于结束行");for(var o=s.r;o<=i.r;o++)for(var l=s.c;l<=i.c;l++){if(!(c=e[o])){for(var c={},f=0;f<a.length;f++)c[a[f]]="";e[o]=c}var h=c[a[l]],u=null,u="object"==typeof(h=null==h?"":h)?$.extend(!0,{},h,r):$.extend(!0,{},{v:h},r);"function"==typeof n&&(u=n(h,u,c,r,o,l,a[l])),e[o][a[l]]=u}return e},makeMergeConfig:function(e){for(var t=[],r=0;r<e.length;r++)t.push({s:this.splitPosition(e[r][0]),e:this.splitPosition(e[r][1])});return t},makeColConfig:function(e,t){t=0<t?t:50;var r,n=[],a=0;for(r in e)if(e.hasOwnProperty(r)){var s=e[r];if(r.match&&r.match(/[A-Z]*/)){for(var i=this.titleToNum(r)-1;a<i;)n.push({wpx:t}),a++;a=1+i,n.push({wpx:0<s?s:t})}}return n},makeRowConfig:function(e,t){t=0<t?t:10;var r,n=[],a=0;for(r in e)if(e.hasOwnProperty(r)){var s=e[r];if(r.match&&r.match(/[0-9]*/)){for(var i=parseInt(r)-1;a<i;)n.push({hpx:t}),a++;a=1+i,n.push({hpx:0<s?s:t})}}return n},splitPosition:function(e){e=e.match("^([A-Z]+)([0-9]+)$");return e?{c:this.titleToNum(e[1])-1,r:parseInt(e[2])-1}:{c:0,r:0}},s2ab:function(e){for(var t=new ArrayBuffer(e.length),r=new Uint8Array(t),n=0;n<e.length;n++)r[n]=255&e.charCodeAt(n);return t},filterDataToAoaData:function(e){var a=[];return $.each(e,function(e,t){var r,n=[];for(r in t)t.hasOwnProperty(r)&&n.push(t[r]);a.push(n)}),a},filterExportData:function(e,t){var r=[],n=[];if(Array.isArray(t))for(var a=0;a<t.length;a++)n[t[a]]=t[a];else n=t;for(a=0;a<e.length;a++){var s,i,o,l=e[a];for(s in r[a]={},n)n.hasOwnProperty(s)&&("function"==typeof(o=n[i=s])&&o.apply?r[a][i]=o.apply(window,[i,l,e,a]):void 0!==l[o]?r[a][i]=l[o]:r[a][i]="")}return r},filterImportData:function(e,n){var a=this;return $.each(e,function(e,r){$.each(r,function(e,t){r[e]=a.filterExportData(t,n)})}),e},importExcel:function(s,e,i){var o={header:"A",range:null,fields:null};$.extend(o,e);var l=this;if(s.length<1)throw{code:999,message:"传入文件为空"};var r=["application/vnd.ms-excel","application/msexcel","application/x-msexcel","application/x-ms-excel","application/x-excel","application/x-dos_ms_excel","application/xls","application/x-xls","application/vnd-xls","application/csv","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",""];$.each(s,function(e,t){if(-1===r.indexOf(t.type))throw{code:999,message:t.name+"（"+t.type+"）为不支持的文件类型"}});var c={},f={};$.each(s,function(t,e){var r=new FileReader;if(!r)throw{code:999,message:"不支持FileReader，请更换更新的浏览器"};r.onload=function(e){var n=XLSX.read(e.target.result,{type:"binary"}),a={};$.each(n.Sheets,function(e,t){var r;n.Sheets.hasOwnProperty(e)&&(r={header:o.header,defval:""},o.range&&(r.range=o.range),a[e]=XLSX.utils.sheet_to_json(t,r),o.fields&&(a[e]=l.filterExportData(a[e],o.fields)))}),c[t]=a,f[t]=n,t===s.length-1&&i&&i.apply&&i.apply(window,[c,f])},r.readAsBinaryString(e)})}},"undefined"!=typeof layui&&layui.define(["jquery"],function(e){$=layui.jquery,e("excel",LAY_EXCEL)}),function(){var u="object"==typeof window?window:"object"==typeof self?self:this,n=u.BlobBuilder||u.WebKitBlobBuilder||u.MSBlobBuilder||u.MozBlobBuilder;u.URL=u.URL||u.webkitURL||function(e,t){return(t=document.createElement("a")).href=e,t};var r=u.Blob,d=URL.createObjectURL,p=URL.revokeObjectURL,a=u.Symbol&&u.Symbol.toStringTag,e=!1,t=!1,m=!!u.ArrayBuffer,s=n&&n.prototype.append&&n.prototype.getBlob;try{e=2===new Blob(["ä"]).size,t=2===new Blob([new Uint8Array([1,2])]).size}catch(e){}function i(e){return e.map(function(e){if(e.buffer instanceof ArrayBuffer){var t,r=e.buffer;return e.byteLength!==r.byteLength&&((t=new Uint8Array(e.byteLength)).set(new Uint8Array(r,e.byteOffset,e.byteLength)),r=t.buffer),r}return e})}function o(e,t){t=t||{};var r=new n;return i(e).forEach(function(e){r.append(e)}),t.type?r.getBlob(t.type):r.getBlob()}function l(e,t){return new r(i(e),t||{})}function c(){function i(e){for(var t=[],r=0;r<e.length;r++){var n=e.charCodeAt(r);n<128?t.push(n):n<2048?t.push(192|n>>6,128|63&n):n<55296||57344<=n?t.push(224|n>>12,128|n>>6&63,128|63&n):(r++,n=65536+((1023&n)<<10|1023&e.charCodeAt(r)),t.push(240|n>>18,128|n>>12&63,128|n>>6&63,128|63&n))}return t}function t(e){for(var t,r,n,a="",s=e.length,i=0;i<s;)switch((t=e[i++])>>4){case 0:case 1:case 2:case 3:case 4:case 5:case 6:case 7:a+=String.fromCharCode(t);break;case 12:case 13:r=e[i++],a+=String.fromCharCode((31&t)<<6|63&r);break;case 14:r=e[i++],n=e[i++],a+=String.fromCharCode((15&t)<<12|(63&r)<<6|(63&n)<<0)}return a}function o(e){for(var t=new Array(e.byteLength),r=new Uint8Array(e),n=t.length;n--;)t[n]=r[n];return t}function r(e){for(var t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",r=[],n=0;n<e.length;n+=3){var a=e[n],s=n+1<e.length,i=s?e[n+1]:0,o=n+2<e.length,l=o?e[n+2]:0,c=(15&i)<<2|l>>6,l=63&l;o||(l=64,s||(c=64)),r.push(t[a>>2],t[(3&a)<<4|i>>4],t[c],t[l])}return r.join("")}var n,l,e=Object.create||function(e){function t(){}return t.prototype=e,new t};function c(e,t){for(var r,n=0,a=(e=e||[]).length;n<a;n++){var s=e[n];s instanceof c?e[n]=s._buffer:"string"==typeof s?e[n]=i(s):m&&(ArrayBuffer.prototype.isPrototypeOf(s)||l(s))?e[n]=o(s):m&&((r=s)&&DataView.prototype.isPrototypeOf(r))?e[n]=o(s.buffer):e[n]=i(String(s))}this._buffer=[].concat.apply([],e),this.size=this._buffer.length,this.type=t&&t.type||""}function a(e,t,r){e=c.call(this,e,r=r||{})||this;return e.name=t,e.lastModifiedDate=r.lastModified?new Date(r.lastModified):new Date,e.lastModified=+e.lastModifiedDate,e}if(m&&(n=["[object Int8Array]","[object Uint8Array]","[object Uint8ClampedArray]","[object Int16Array]","[object Uint16Array]","[object Int32Array]","[object Uint32Array]","[object Float32Array]","[object Float64Array]"],l=ArrayBuffer.isView||function(e){return e&&-1<n.indexOf(Object.prototype.toString.call(e))}),c.prototype.slice=function(e,t,r){return new c([this._buffer.slice(e||0,t||this._buffer.length)],{type:r})},c.prototype.toString=function(){return"[object Blob]"},(a.prototype=e(c.prototype)).constructor=a,Object.setPrototypeOf)Object.setPrototypeOf(a,c);else try{a.__proto__=c}catch(e){}function s(){if(!(this instanceof s))throw new TypeError("Failed to construct 'FileReader': Please use the 'new' operator, this DOM object constructor cannot be called as a function.");var r=document.createDocumentFragment();this.addEventListener=r.addEventListener,this.dispatchEvent=function(e){var t=this["on"+e.type];"function"==typeof t&&t(e),r.dispatchEvent(e)},this.removeEventListener=r.removeEventListener}function f(e,t,r){if(!(t instanceof c))throw new TypeError("Failed to execute '"+r+"' on 'FileReader': parameter 1 is not of type 'Blob'.");e.result="",setTimeout(function(){this.readyState=s.LOADING,e.dispatchEvent(new Event("load")),e.dispatchEvent(new Event("loadend"))})}a.prototype.toString=function(){return"[object File]"},s.EMPTY=0,s.LOADING=1,s.DONE=2,s.prototype.error=null,s.prototype.onabort=null,s.prototype.onerror=null,s.prototype.onload=null,s.prototype.onloadend=null,s.prototype.onloadstart=null,s.prototype.onprogress=null,s.prototype.readAsDataURL=function(e){f(this,e,"readAsDataURL"),this.result="data:"+e.type+";base64,"+r(e._buffer)},s.prototype.readAsText=function(e){f(this,e,"readAsText"),this.result=t(e._buffer)},s.prototype.readAsArrayBuffer=function(e){f(this,e,"readAsText"),this.result=e._buffer.slice()},s.prototype.abort=function(){},URL.createObjectURL=function(e){return e instanceof c?"data:"+e.type+";base64,"+r(e._buffer):d.call(URL,e)},URL.revokeObjectURL=function(e){p&&p.call(URL,e)};var h=u.XMLHttpRequest&&u.XMLHttpRequest.prototype.send;h&&(XMLHttpRequest.prototype.send=function(e){e instanceof c?(this.setRequestHeader("Content-Type",e.type),h.call(this,t(e._buffer))):h.call(this,e)}),u.FileReader=s,u.File=a,u.Blob=c}function f(){var e=!!u.ActiveXObject||"-ms-scroll-limit"in document.documentElement.style&&"-ms-ime-align"in document.documentElement.style,t=u.XMLHttpRequest&&u.XMLHttpRequest.prototype.send;e&&t&&(XMLHttpRequest.prototype.send=function(e){e instanceof Blob&&this.setRequestHeader("Content-Type",e.type),t.call(this,e)});try{new File([],"")}catch(e){try{var r=new Function('class File extends Blob {constructor(chunks, name, opts) {opts = opts || {};super(chunks, opts || {});this.name = name;this.lastModifiedDate = opts.lastModified ? new Date(opts.lastModified) : new Date;this.lastModified = +this.lastModifiedDate;}};return new File([], ""), File')();u.File=r}catch(e){r=function(e,t,r){e=new Blob(e,r),r=r&&void 0!==r.lastModified?new Date(r.lastModified):new Date;return e.name=t,e.lastModifiedDate=r,e.lastModified=+r,e.toString=function(){return"[object File]"},a&&(e[a]="File"),e};u.File=r}}}u.Blob&&(o.prototype=Blob.prototype,l.prototype=Blob.prototype),a&&(File.prototype[a]="File",Blob.prototype[a]="Blob",FileReader.prototype[a]="FileReader"),e?(f(),u.Blob=t?u.Blob:l):s?(f(),u.Blob=o):c()}(),function(e,t){"function"==typeof define&&define.amd?define([],t):"undefined"!=typeof exports?t():(t(),e.FileSaver={})}(this,function(){"use strict";function l(e,t,r){var n=new XMLHttpRequest;n.open("GET",e),n.responseType="blob",n.onload=function(){a(n.response,t,r)},n.onerror=function(){console.error("could not download file")},n.send()}function i(e){var t=new XMLHttpRequest;return t.open("HEAD",e,!1),t.send(),200<=t.status&&t.status<=299}function o(t){try{t.dispatchEvent(new MouseEvent("click"))}catch(e){var r=document.createEvent("MouseEvents");r.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),t.dispatchEvent(r)}}var c="object"==typeof window&&window.window===window?window:"object"==typeof self&&self.self===self?self:"object"==typeof global&&global.global===global?global:void 0,a=c.saveAs||"object"!=typeof window||window!==c?function(){}:"download"in HTMLAnchorElement.prototype?function(e,t,r){var n=c.URL||c.webkitURL,a=document.createElement("a");t=t||e.name||"download",a.download=t,a.rel="noopener","string"==typeof e?(a.href=e,a.origin===location.origin?o(a):i(a.href)?l(e,t,r):o(a,a.target="_blank")):(a.href=n.createObjectURL(e),setTimeout(function(){n.revokeObjectURL(a.href)},4e4),setTimeout(function(){o(a)},0))}:"msSaveOrOpenBlob"in navigator?function(e,t,r){var n,a,s;t=t||e.name||"download","string"!=typeof e?navigator.msSaveOrOpenBlob((a=e,void 0===(s=r)?s={autoBom:!1}:"object"!=typeof s&&(console.warn("Depricated: Expected third argument to be a object"),s={autoBom:!s}),s.autoBom&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(a.type)?new Blob(["\ufeff",a],{type:a.type}):a),t):i(e)?l(e,t,r):((n=document.createElement("a")).href=e,n.target="_blank",setTimeout(function(){o(n)}))}:function(e,t,r,n){if((n=n||open("","_blank"))&&(n.document.title=n.document.body.innerText="downloading..."),"string"==typeof e)return l(e,t,r);var a,s,i,t="application/octet-stream"===e.type,r=/constructor/i.test(c.HTMLElement)||c.safari,o=/CriOS\/[\d]+/.test(navigator.userAgent);(o||t&&r)&&"object"==typeof FileReader?((a=new FileReader).onloadend=function(){var e=a.result,e=o?e:e.replace(/^data:[^;]*;/,"data:attachment/file;");n?n.location.href=e:location=e,n=null},a.readAsDataURL(e)):(s=c.URL||c.webkitURL,i=s.createObjectURL(e),n?n.location=i:location.href=i,n=null,setTimeout(function(){s.revokeObjectURL(i)},4e4))};c.saveAs=a.saveAs=a,"undefined"!=typeof module&&(module.exports=a)}),function(e){var t;"object"==typeof exports&&"undefined"!=typeof module&&"undefined"==typeof DO_NOT_EXPORT_JSZIP?module.exports=e():"function"==typeof define&&define.amd&&"undefined"==typeof DO_NOT_EXPORT_JSZIP?(JSZipSync=e(),define([],e)):("undefined"!=typeof window?t=window:"undefined"!=typeof global?t=global:"undefined"!=typeof $&&$.global?t=$.global:"undefined"!=typeof self&&(t=self),t.JSZipSync=e())}(function(){return function n(a,s,i){function o(r,e){if(!s[r]){if(!a[r]){var t="function"==typeof require&&require;if(!e&&t)return t(r,!0);if(l)return l(r,!0);throw new Error("Cannot find module '"+r+"'")}t=s[r]={exports:{}};a[r][0].call(t.exports,function(e){var t=a[r][1][e];return o(t||e)},t,t.exports,n,a,s,i)}return s[r].exports}for(var l="function"==typeof require&&require,e=0;e<i.length;e++)o(i[e]);return o}({1:[function(e,t,r){"use strict";var f="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";r.encode=function(e,t){for(var r,n,a,s,i,o,l="",c=0;c<e.length;)a=(o=e.charCodeAt(c++))>>2,s=(3&o)<<4|(r=e.charCodeAt(c++))>>4,i=(15&r)<<2|(n=e.charCodeAt(c++))>>6,o=63&n,isNaN(r)?i=o=64:isNaN(n)&&(o=64),l=l+f.charAt(a)+f.charAt(s)+f.charAt(i)+f.charAt(o);return l},r.decode=function(e,t){var r,n,a,s,i,o,l="",c=0;for(e=e.replace(/[^A-Za-z0-9\+\/\=]/g,"");c<e.length;)a=f.indexOf(e.charAt(c++)),r=(15&(s=f.indexOf(e.charAt(c++))))<<4|(i=f.indexOf(e.charAt(c++)))>>2,n=(3&i)<<6|(o=f.indexOf(e.charAt(c++))),l+=String.fromCharCode(a<<2|s>>4),64!=i&&(l+=String.fromCharCode(r)),64!=o&&(l+=String.fromCharCode(n));return l}},{}],2:[function(e,t,r){"use strict";function n(){this.compressedSize=0,this.uncompressedSize=0,this.crc32=0,this.compressionMethod=null,this.compressedContent=null}n.prototype={getContent:function(){return null},getCompressedContent:function(){return null}},t.exports=n},{}],3:[function(e,t,r){"use strict";r.STORE={magic:"\0\0",compress:function(e){return e},uncompress:function(e){return e},compressInputType:null,uncompressInputType:null},r.DEFLATE=e("./flate")},{"./flate":8}],4:[function(e,t,r){"use strict";var i=e("./utils"),o=[0,1996959894,3993919788,2567524794,124634137,1886057615,3915621685,2657392035,249268274,2044508324,3772115230,2547177864,162941995,2125561021,3887607047,2428444049,498536548,1789927666,4089016648,2227061214,450548861,1843258603,4107580753,2211677639,325883990,1684777152,4251122042,2321926636,335633487,1661365465,4195302755,2366115317,997073096,1281953886,3579855332,2724688242,1006888145,1258607687,3524101629,2768942443,901097722,1119000684,3686517206,2898065728,853044451,1172266101,3705015759,2882616665,651767980,1373503546,3369554304,3218104598,565507253,1454621731,3485111705,3099436303,671266974,1594198024,3322730930,2970347812,795835527,1483230225,3244367275,3060149565,1994146192,31158534,2563907772,4023717930,1907459465,112637215,2680153253,3904427059,2013776290,251722036,2517215374,3775830040,2137656763,141376813,2439277719,3865271297,1802195444,476864866,2238001368,4066508878,1812370925,453092731,2181625025,4111451223,1706088902,314042704,2344532202,4240017532,1658658271,366619977,2362670323,4224994405,1303535960,984961486,2747007092,3569037538,1256170817,1037604311,2765210733,3554079995,1131014506,879679996,2909243462,3663771856,1141124467,855842277,2852801631,3708648649,1342533948,654459306,3188396048,3373015174,1466479909,544179635,3110523913,3462522015,1591671054,702138776,2966460450,3352799412,1504918807,783551873,3082640443,3233442989,3988292384,2596254646,62317068,1957810842,3939845945,2647816111,81470997,1943803523,3814918930,2489596804,225274430,2053790376,3826175755,2466906013,167816743,2097651377,4027552580,2265490386,503444072,1762050814,4150417245,2154129355,426522225,1852507879,4275313526,2312317920,282753626,1742555852,4189708143,2394877945,397917763,1622183637,3604390888,2714866558,953729732,1340076626,3518719985,2797360999,1068828381,1219638859,3624741850,2936675148,906185462,1090812512,3747672003,2825379669,829329135,1181335161,3412177804,3160834842,628085408,1382605366,3423369109,3138078467,570562233,1426400815,3317316542,2998733608,733239954,1555261956,3268935591,3050360625,752459403,1541320221,2607071920,3965973030,1969922972,40735498,2617837225,3943577151,1913087877,83908371,2512341634,3803740692,2075208622,213261112,2463272603,3855990285,2094854071,198958881,2262029012,4057260610,1759359992,534414190,2176718541,4139329115,1873836001,414664567,2282248934,4279200368,1711684554,285281116,2405801727,4167216745,1634467795,376229701,2685067896,3608007406,1308918612,956543938,2808555105,3495958263,1231636301,1047427035,2932959818,3654703836,1088359270,936918e3,2847714899,3736837829,1202900863,817233897,3183342108,3401237130,1404277552,615818150,3134207493,3453421203,1423857449,601450431,3009837614,3294710456,1567103746,711928724,3020668471,3272380065,1510334235,755167117];t.exports=function(e,t){if(void 0===e||!e.length)return 0;var r="string"!==i.getTypeOf(e);void 0===t&&(t=0);var n;t^=-1;for(var a=0,s=e.length;a<s;a++)n=r?e[a]:e.charCodeAt(a),t=t>>>8^o[255&(t^n)];return-1^t}},{"./utils":21}],5:[function(e,t,r){"use strict";var n=e("./utils");function a(e){this.data=null,this.length=0,this.index=0}a.prototype={checkOffset:function(e){this.checkIndex(this.index+e)},checkIndex:function(e){if(this.length<e||e<0)throw new Error("End of data reached (data length = "+this.length+", asked index = "+e+"). Corrupted zip ?")},setIndex:function(e){this.checkIndex(e),this.index=e},skip:function(e){this.setIndex(this.index+e)},byteAt:function(e){},readInt:function(e){var t,r=0;for(this.checkOffset(e),t=this.index+e-1;t>=this.index;t--)r=(r<<8)+this.byteAt(t);return this.index+=e,r},readString:function(e){return n.transformTo("string",this.readData(e))},readData:function(e){},lastIndexOfSignature:function(e){},readDate:function(){var e=this.readInt(4);return new Date(1980+(e>>25&127),(e>>21&15)-1,e>>16&31,e>>11&31,e>>5&63,(31&e)<<1)}},t.exports=a},{"./utils":21}],6:[function(e,t,r){"use strict";r.base64=!1,r.binary=!1,r.dir=!1,r.createFolders=!1,r.date=null,r.compression=null,r.comment=null},{}],7:[function(e,t,r){"use strict";var n=e("./utils");r.string2binary=function(e){return n.string2binary(e)},r.string2Uint8Array=function(e){return n.transformTo("uint8array",e)},r.uint8Array2String=function(e){return n.transformTo("string",e)},r.string2Blob=function(e){e=n.transformTo("arraybuffer",e);return n.arrayBuffer2Blob(e)},r.arrayBuffer2Blob=function(e){return n.arrayBuffer2Blob(e)},r.transformTo=function(e,t){return n.transformTo(e,t)},r.getTypeOf=function(e){return n.getTypeOf(e)},r.checkSupport=function(e){return n.checkSupport(e)},r.MAX_VALUE_16BITS=n.MAX_VALUE_16BITS,r.MAX_VALUE_32BITS=n.MAX_VALUE_32BITS,r.pretty=function(e){return n.pretty(e)},r.findCompression=function(e){return n.findCompression(e)},r.isRegExp=function(e){return n.isRegExp(e)}},{"./utils":21}],8:[function(e,t,r){"use strict";var n="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Uint32Array,a=e("pako");r.uncompressInputType=n?"uint8array":"array",r.compressInputType=n?"uint8array":"array",r.magic="\b\0",r.compress=function(e){return a.deflateRaw(e)},r.uncompress=function(e){return a.inflateRaw(e)}},{pako:24}],9:[function(e,t,r){"use strict";var n=e("./base64");function a(e,t){if(!(this instanceof a))return new a(e,t);this.files={},this.comment=null,this.root="",e&&this.load(e,t),this.clone=function(){var e,t=new a;for(e in this)"function"!=typeof this[e]&&(t[e]=this[e]);return t}}(a.prototype=e("./object")).load=e("./load"),a.support=e("./support"),a.defaults=e("./defaults"),a.utils=e("./deprecatedPublicUtils"),a.base64={encode:function(e){return n.encode(e)},decode:function(e){return n.decode(e)}},a.compressions=e("./compressions"),t.exports=a},{"./base64":1,"./compressions":3,"./defaults":6,"./deprecatedPublicUtils":7,"./load":10,"./object":13,"./support":17}],10:[function(e,t,r){"use strict";var s=e("./base64"),i=e("./zipEntries");t.exports=function(e,t){var r,n,a;for((t=t||{}).base64&&(e=s.decode(e)),r=(e=new i(e,t)).files,n=0;n<r.length;n++)a=r[n],this.file(a.fileName,a.decompressed,{binary:!0,optimizedBinaryString:!0,date:a.date,dir:a.dir,comment:a.fileComment.length?a.fileComment:null,createFolders:t.createFolders});return e.zipComment.length&&(this.comment=e.zipComment),this}},{"./base64":1,"./zipEntries":22}],11:[function(e,a,t){!function(r){"use strict";var n=function(){};if(void 0!==r){var t=!r.from;if(!t)try{r.from("foo","utf8")}catch(e){t=!0}n=t?function(e,t){return t?new r(e,t):new r(e)}:r.from.bind(r),r.alloc||(r.alloc=function(e){return new r(e)})}a.exports=function(e,t){return"number"==typeof e?r.alloc(e):n(e,t)},a.exports.test=function(e){return r.isBuffer(e)}}.call(this,"undefined"!=typeof Buffer?Buffer:void 0)},{}],12:[function(e,t,r){"use strict";e=e("./uint8ArrayReader");function n(e){this.data=e,this.length=this.data.length,this.index=0}(n.prototype=new e).readData=function(e){this.checkOffset(e);var t=this.data.slice(this.index,this.index+e);return this.index+=e,t},t.exports=n},{"./uint8ArrayReader":18}],13:[function(e,t,r){"use strict";function n(e){var t;return e._data instanceof c&&(e._data=e._data.getContent(),e.options.binary=!0,e.options.base64=!1,"uint8array"===d.getTypeOf(e._data)&&(t=e._data,e._data=new Uint8Array(t.length),0!==t.length&&e._data.set(t,0))),e._data}function a(e){var t=n(e);return"string"===d.getTypeOf(t)?!e.options.binary&&o.nodebuffer?f(t,"utf-8"):e.asBinary():t}function s(e){var t=n(this);return null==t?"":(this.options.base64&&(t=g.decode(t)),t=e&&this.options.binary?B.utf8decode(t):d.transformTo("string",t),e||this.options.binary?t:d.transformTo("string",B.utf8encode(t)))}function i(e,t,r){this.name=e,this.dir=r.dir,this.date=r.date,this.comment=r.comment,this._data=t,this.options=r,this._initialMetadata={dir:r.dir,date:r.date}}var o=e("./support"),d=e("./utils"),p=e("./crc32"),m=e("./signature"),l=e("./defaults"),g=e("./base64"),b=e("./compressions"),c=e("./compressedObject"),f=e("./nodeBuffer"),v=e("./utf8"),E=e("./stringWriter"),w=e("./uint8ArrayWriter");i.prototype={asText:function(){return s.call(this,!0)},asBinary:function(){return s.call(this,!1)},asNodeBuffer:function(){var e=a(this);return d.transformTo("nodebuffer",e)},asUint8Array:function(){var e=a(this);return d.transformTo("uint8array",e)},asArrayBuffer:function(){return this.asUint8Array().buffer}};function S(e,t){for(var r="",n=0;n<t;n++)r+=String.fromCharCode(255&e),e>>>=8;return r}function h(e,t){return"/"!=e.slice(-1)&&(e+="/"),t=void 0!==t&&t,this.files[e]||u.call(this,e,null,{dir:!0,createFolders:t}),this.files[e]}function _(e,t){var r,n=new c;return e._data instanceof c?(n.uncompressedSize=e._data.uncompressedSize,n.crc32=e._data.crc32,0===n.uncompressedSize||e.dir?(t=b.STORE,n.compressedContent="",n.crc32=0):e._data.compressionMethod===t.magic?n.compressedContent=e._data.getCompressedContent():(r=e._data.getContent(),n.compressedContent=t.compress(d.transformTo(t.compressInputType,r)))):((r=a(e))&&0!==r.length&&!e.dir||(t=b.STORE,r=""),n.uncompressedSize=r.length,n.crc32=p(r),n.compressedContent=t.compress(d.transformTo(t.compressInputType,r))),n.compressedSize=n.compressedContent.length,n.compressionMethod=t.magic,n}function y(e,t,r,n){r.compressedContent;var a=d.transformTo("string",v.utf8encode(t.name)),s=t.comment||"",i=d.transformTo("string",v.utf8encode(s)),o=a.length!==t.name.length,l=i.length!==s.length,c=t.options,f="",h=(t._initialMetadata.dir!==t.dir?t:c).dir,t=(s=(t._initialMetadata.date!==t.date?t:c).date).getHours();t<<=6,t|=s.getMinutes(),t<<=5,t|=s.getSeconds()/2,c=s.getFullYear()-1980,c<<=4,c|=s.getMonth()+1,c<<=5,c|=s.getDate(),o&&(s=S(1,1)+S(p(a),4)+a,f+="up"+S(s.length,2)+s),l&&(u=S(1,1)+S(this.crc32(i),4)+i,f+="uc"+S(u.length,2)+u);var u="";return u+="\n\0",u+=o||l?"\0\b":"\0\0",u+=r.compressionMethod,u+=S(t,2),u+=S(c,2),u+=S(r.crc32,4),u+=S(r.compressedSize,4),u+=S(r.uncompressedSize,4),u+=S(a.length,2),u+=S(f.length,2),{fileRecord:m.LOCAL_FILE_HEADER+u+a+f,dirRecord:m.CENTRAL_FILE_HEADER+"\0"+u+S(i.length,2)+"\0\0\0\0"+(!0===h?"\0\0\0":"\0\0\0\0")+S(n,4)+a+f+i,compressedObject:r}}var C=function(){for(var e,t={},r=0;r<arguments.length;r++)for(e in arguments[r])arguments[r].hasOwnProperty(e)&&void 0===t[e]&&(t[e]=arguments[r][e]);return t},u=function(e,t,r){var n,a,s=d.getTypeOf(t);if(!0!==(a=(a=r)||{}).base64||null!==a.binary&&void 0!==a.binary||(a.binary=!0),(a=C(a,l)).date=a.date||new Date,null!==a.compression&&(a.compression=a.compression.toUpperCase()),(r=a).createFolders&&(n=function(e){if(e.slice(-1)=="/")e=e.substring(0,e.length-1);var t=e.lastIndexOf("/");return t>0?e.substring(0,t):""}(e))&&h.call(this,n,!0),r.dir||null==t)r.base64=!1,r.binary=!1,t=null;else if("string"===s)r.binary&&!r.base64&&!0!==r.optimizedBinaryString&&(t=d.string2binary(t));else{if(r.base64=!1,r.binary=!0,!(s||t instanceof c))throw new Error("The data of '"+e+"' is in an unsupported format !");"arraybuffer"===s&&(t=d.transformTo("uint8array",t))}r=new i(e,t,r);return this.files[e]=r},B={load:function(e,t){throw new Error("Load method is not defined. Is the file jszip-load.js included ?")},filter:function(e){var t,r,n,a=[];for(t in this.files)this.files.hasOwnProperty(t)&&(r=this.files[t],n=new i(r.name,r._data,C(r.options)),r=t.slice(this.root.length,t.length),t.slice(0,this.root.length)===this.root&&e(r,n)&&a.push(n));return a},file:function(r,e,t){if(1!==arguments.length)return r=this.root+r,u.call(this,r,e,t),this;if(d.isRegExp(r)){var n=r;return this.filter(function(e,t){return!t.dir&&n.test(e)})}return this.filter(function(e,t){return!t.dir&&e===r})[0]||null},folder:function(r){if(!r)return this;if(d.isRegExp(r))return this.filter(function(e,t){return t.dir&&r.test(e)});var e=this.root+r,t=h.call(this,e),e=this.clone();return e.root=t.name,e},remove:function(r){r=this.root+r;var e=this.files[r];if(e||("/"!=r.slice(-1)&&(r+="/"),e=this.files[r]),e&&!e.dir)delete this.files[r];else for(var t=this.filter(function(e,t){return t.name.slice(0,r.length)===r}),n=0;n<t.length;n++)delete this.files[t[n].name];return this},generate:function(e){e=C(e||{},{base64:!0,compression:"STORE",type:"base64",comment:null}),d.checkSupport(e.type);var t,r=[],n=0,a=0,s=d.transformTo("string",this.utf8encode(e.comment||this.comment||""));for(t in this.files)if(this.files.hasOwnProperty(t)){var i=this.files[t],o=i.options.compression||e.compression.toUpperCase(),l=b[o];if(!l)throw new Error(o+" is not a valid compression method !");l=_.call(this,i,l),i=y.call(this,t,i,l,n);n+=i.fileRecord.length+l.compressedSize,a+=i.dirRecord.length,r.push(i)}for(var c=m.CENTRAL_DIRECTORY_END+"\0\0\0\0"+S(r.length,2)+S(r.length,2)+S(a,4)+S(n,4)+S(s.length,2)+s,s=e.type.toLowerCase(),f=new("uint8array"===s||"arraybuffer"===s||"blob"===s||"nodebuffer"===s?w:E)(n+a+c.length),h=0;h<r.length;h++)f.append(r[h].fileRecord),f.append(r[h].compressedObject.compressedContent);for(h=0;h<r.length;h++)f.append(r[h].dirRecord);f.append(c);var u=f.finalize();switch(e.type.toLowerCase()){case"uint8array":case"arraybuffer":case"nodebuffer":return d.transformTo(e.type.toLowerCase(),u);case"blob":return d.arrayBuffer2Blob(d.transformTo("arraybuffer",u));case"base64":return e.base64?g.encode(u):u;default:return u}},crc32:function(e,t){return p(e,t)},utf8encode:function(e){return d.transformTo("string",v.utf8encode(e))},utf8decode:function(e){return v.utf8decode(e)}};t.exports=B},{"./base64":1,"./compressedObject":2,"./compressions":3,"./crc32":4,"./defaults":6,"./nodeBuffer":11,"./signature":14,"./stringWriter":16,"./support":17,"./uint8ArrayWriter":19,"./utf8":20,"./utils":21}],14:[function(e,t,r){"use strict";r.LOCAL_FILE_HEADER="PK",r.CENTRAL_FILE_HEADER="PK",r.CENTRAL_DIRECTORY_END="PK",r.ZIP64_CENTRAL_DIRECTORY_LOCATOR="PK",r.ZIP64_CENTRAL_DIRECTORY_END="PK",r.DATA_DESCRIPTOR="PK\b"},{}],15:[function(e,t,r){"use strict";var n=e("./dataReader"),a=e("./utils");function s(e,t){this.data=e,t||(this.data=a.string2binary(this.data)),this.length=this.data.length,this.index=0}(s.prototype=new n).byteAt=function(e){return this.data.charCodeAt(e)},s.prototype.lastIndexOfSignature=function(e){return this.data.lastIndexOf(e)},s.prototype.readData=function(e){this.checkOffset(e);var t=this.data.slice(this.index,this.index+e);return this.index+=e,t},t.exports=s},{"./dataReader":5,"./utils":21}],16:[function(e,t,r){"use strict";var n=e("./utils"),e=function(){this.data=[]};e.prototype={append:function(e){e=n.transformTo("string",e),this.data.push(e)},finalize:function(){return this.data.join("")}},t.exports=e},{"./utils":21}],17:[function(e,t,n){!function(t){"use strict";if(n.base64=!0,n.array=!0,n.string=!0,n.arraybuffer="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof Uint8Array,n.nodebuffer=void 0!==t,n.uint8array="undefined"!=typeof Uint8Array,"undefined"==typeof ArrayBuffer)n.blob=!1;else{t=new ArrayBuffer(0);try{n.blob=0===new Blob([t],{type:"application/zip"}).size}catch(e){try{var r=new(window.BlobBuilder||window.WebKitBlobBuilder||window.MozBlobBuilder||window.MSBlobBuilder);r.append(t),n.blob=0===r.getBlob("application/zip").size}catch(e){n.blob=!1}}}}.call(this,"undefined"!=typeof Buffer?Buffer:void 0)},{}],18:[function(e,t,r){"use strict";e=e("./dataReader");function n(e){e&&(this.data=e,this.length=this.data.length,this.index=0)}(n.prototype=new e).byteAt=function(e){return this.data[e]},n.prototype.lastIndexOfSignature=function(e){for(var t=e.charCodeAt(0),r=e.charCodeAt(1),n=e.charCodeAt(2),a=e.charCodeAt(3),s=this.length-4;0<=s;--s)if(this.data[s]===t&&this.data[s+1]===r&&this.data[s+2]===n&&this.data[s+3]===a)return s;return-1},n.prototype.readData=function(e){if(this.checkOffset(e),0===e)return new Uint8Array(0);var t=this.data.subarray(this.index,this.index+e);return this.index+=e,t},t.exports=n},{"./dataReader":5}],19:[function(e,t,r){"use strict";var n=e("./utils"),e=function(e){this.data=new Uint8Array(e),this.index=0};e.prototype={append:function(e){0!==e.length&&(e=n.transformTo("uint8array",e),this.data.set(e,this.index),this.index+=e.length)},finalize:function(){return this.data}},t.exports=e},{"./utils":21}],20:[function(e,t,r){"use strict";for(var o=e("./utils"),l=e("./support"),n=e("./nodeBuffer"),c=new Array(256),a=0;a<256;a++)c[a]=252<=a?6:248<=a?5:240<=a?4:224<=a?3:192<=a?2:1;c[254]=c[254]=1;function s(e){for(var t,r,n=e.length,a=new Array(2*n),s=0,i=0;i<n;)if((t=e[i++])<128)a[s++]=t;else if(4<(r=c[t]))a[s++]=65533,i+=r-1;else{for(t&=2===r?31:3===r?15:7;1<r&&i<n;)t=t<<6|63&e[i++],r--;1<r?a[s++]=65533:t<65536?a[s++]=t:(t-=65536,a[s++]=55296|t>>10&1023,a[s++]=56320|1023&t)}return a.length!==s&&(a.subarray?a=a.subarray(0,s):a.length=s),o.applyFromCharCode(a)}r.utf8encode=function(e){return l.nodebuffer?n(e,"utf-8"):function(e){for(var t,r,n,a,s=e.length,i=0,o=0;o<s;o++)55296==(64512&(r=e.charCodeAt(o)))&&o+1<s&&56320==(64512&(n=e.charCodeAt(o+1)))&&(r=65536+(r-55296<<10)+(n-56320),o++),i+=r<128?1:r<2048?2:r<65536?3:4;for(t=new(l.uint8array?Uint8Array:Array)(i),o=a=0;a<i;o++)55296==(64512&(r=e.charCodeAt(o)))&&o+1<s&&56320==(64512&(n=e.charCodeAt(o+1)))&&(r=65536+(r-55296<<10)+(n-56320),o++),r<128?t[a++]=r:(r<2048?t[a++]=192|r>>>6:(r<65536?t[a++]=224|r>>>12:(t[a++]=240|r>>>18,t[a++]=128|r>>>12&63),t[a++]=128|r>>>6&63),t[a++]=128|63&r);return t}(e)},r.utf8decode=function(e){if(l.nodebuffer)return o.transformTo("nodebuffer",e).toString("utf-8");for(var t=[],r=0,n=(e=o.transformTo(l.uint8array?"uint8array":"array",e)).length;r<n;){var a=function(e,t){for(var r=(t=(t=t||e.length)>e.length?e.length:t)-1;0<=r&&128==(192&e[r]);)r--;return!(r<0)&&0!==r&&r+c[e[r]]>t?r:t}(e,Math.min(r+65536,n));l.uint8array?t.push(s(e.subarray(r,a))):t.push(s(e.slice(r,a))),r=a}return t.join("")}},{"./nodeBuffer":11,"./support":17,"./utils":21}],21:[function(e,t,c){"use strict";var r=e("./support"),n=e("./compressions"),f=e("./nodeBuffer");function a(e){return e}function s(e,t){for(var r=0;r<e.length;++r)t[r]=255&e.charCodeAt(r);return t}function i(e){var t=65536,r=[],n=e.length,a=c.getTypeOf(e),s=0,i=!0;try{switch(a){case"uint8array":String.fromCharCode.apply(null,new Uint8Array(0));break;case"nodebuffer":String.fromCharCode.apply(null,f(0))}}catch(e){i=!1}if(!i){for(var o="",l=0;l<e.length;l++)o+=String.fromCharCode(e[l]);return o}for(;s<n&&1<t;)try{"array"===a||"nodebuffer"===a?r.push(String.fromCharCode.apply(null,e.slice(s,Math.min(s+t,n)))):r.push(String.fromCharCode.apply(null,e.subarray(s,Math.min(s+t,n)))),s+=t}catch(e){t=Math.floor(t/2)}return r.join("")}function o(e,t){for(var r=0;r<e.length;r++)t[r]=e[r];return t}c.string2binary=function(e){for(var t="",r=0;r<e.length;r++)t+=String.fromCharCode(255&e.charCodeAt(r));return t},c.arrayBuffer2Blob=function(t){c.checkSupport("blob");try{return new Blob([t],{type:"application/zip"})}catch(e){try{var r=new(window.BlobBuilder||window.WebKitBlobBuilder||window.MozBlobBuilder||window.MSBlobBuilder);return r.append(t),r.getBlob("application/zip")}catch(e){throw new Error("Bug : can't construct the Blob.")}}},c.applyFromCharCode=i;var l={};l.string={string:a,array:function(e){return s(e,new Array(e.length))},arraybuffer:function(e){return l.string.uint8array(e).buffer},uint8array:function(e){return s(e,new Uint8Array(e.length))},nodebuffer:function(e){return s(e,f(e.length))}},l.array={string:i,array:a,arraybuffer:function(e){return new Uint8Array(e).buffer},uint8array:function(e){return new Uint8Array(e)},nodebuffer:function(e){return f(e)}},l.arraybuffer={string:function(e){return i(new Uint8Array(e))},array:function(e){return o(new Uint8Array(e),new Array(e.byteLength))},arraybuffer:a,uint8array:function(e){return new Uint8Array(e)},nodebuffer:function(e){return f(new Uint8Array(e))}},l.uint8array={string:i,array:function(e){return o(e,new Array(e.length))},arraybuffer:function(e){return e.buffer},uint8array:a,nodebuffer:function(e){return f(e)}},l.nodebuffer={string:i,array:function(e){return o(e,new Array(e.length))},arraybuffer:function(e){return l.nodebuffer.uint8array(e).buffer},uint8array:function(e){return o(e,new Uint8Array(e.length))},nodebuffer:a},c.transformTo=function(e,t){if(t=t||"",!e)return t;c.checkSupport(e);var r=c.getTypeOf(t);return l[r][e](t)},c.getTypeOf=function(e){return"string"==typeof e?"string":"[object Array]"===Object.prototype.toString.call(e)?"array":r.nodebuffer&&f.test(e)?"nodebuffer":r.uint8array&&e instanceof Uint8Array?"uint8array":r.arraybuffer&&e instanceof ArrayBuffer?"arraybuffer":void 0},c.checkSupport=function(e){if(!r[e.toLowerCase()])throw new Error(e+" is not supported by this browser")},c.MAX_VALUE_16BITS=65535,c.MAX_VALUE_32BITS=-1,c.pretty=function(e){for(var t,r="",n=0;n<(e||"").length;n++)r+="\\x"+((t=e.charCodeAt(n))<16?"0":"")+t.toString(16).toUpperCase();return r},c.findCompression=function(e){for(var t in n)if(n.hasOwnProperty(t)&&n[t].magic===e)return n[t];return null},c.isRegExp=function(e){return"[object RegExp]"===Object.prototype.toString.call(e)}},{"./compressions":3,"./nodeBuffer":11,"./support":17}],22:[function(e,t,r){"use strict";var n=e("./stringReader"),a=e("./nodeBufferReader"),s=e("./uint8ArrayReader"),i=e("./utils"),o=e("./signature"),l=e("./zipEntry"),c=e("./support"),f=e("./object");function h(e,t){this.files=[],this.loadOptions=t,e&&this.load(e)}h.prototype={checkSignature:function(e){var t=this.reader.readString(4);if(t!==e)throw new Error("Corrupted zip or bug : unexpected signature ("+i.pretty(t)+", expected "+i.pretty(e)+")")},readBlockEndOfCentral:function(){this.diskNumber=this.reader.readInt(2),this.diskWithCentralDirStart=this.reader.readInt(2),this.centralDirRecordsOnThisDisk=this.reader.readInt(2),this.centralDirRecords=this.reader.readInt(2),this.centralDirSize=this.reader.readInt(4),this.centralDirOffset=this.reader.readInt(4),this.zipCommentLength=this.reader.readInt(2),this.zipComment=this.reader.readString(this.zipCommentLength),this.zipComment=f.utf8decode(this.zipComment)},readBlockZip64EndOfCentral:function(){this.zip64EndOfCentralSize=this.reader.readInt(8),this.versionMadeBy=this.reader.readString(2),this.versionNeeded=this.reader.readInt(2),this.diskNumber=this.reader.readInt(4),this.diskWithCentralDirStart=this.reader.readInt(4),this.centralDirRecordsOnThisDisk=this.reader.readInt(8),this.centralDirRecords=this.reader.readInt(8),this.centralDirSize=this.reader.readInt(8),this.centralDirOffset=this.reader.readInt(8),this.zip64ExtensibleData={};for(var e,t,r,n=this.zip64EndOfCentralSize-44;0<n;)e=this.reader.readInt(2),t=this.reader.readInt(4),r=this.reader.readString(t),this.zip64ExtensibleData[e]={id:e,length:t,value:r}},readBlockZip64EndOfCentralLocator:function(){if(this.diskWithZip64CentralDirStart=this.reader.readInt(4),this.relativeOffsetEndOfZip64CentralDir=this.reader.readInt(8),this.disksCount=this.reader.readInt(4),1<this.disksCount)throw new Error("Multi-volumes zip are not supported")},readLocalFiles:function(){for(var e,t=0;t<this.files.length;t++)e=this.files[t],this.reader.setIndex(e.localHeaderOffset),this.checkSignature(o.LOCAL_FILE_HEADER),e.readLocalPart(this.reader),e.handleUTF8()},readCentralDir:function(){var e;for(this.reader.setIndex(this.centralDirOffset);this.reader.readString(4)===o.CENTRAL_FILE_HEADER;)(e=new l({zip64:this.zip64},this.loadOptions)).readCentralPart(this.reader),this.files.push(e)},readEndOfCentral:function(){var e=this.reader.lastIndexOfSignature(o.CENTRAL_DIRECTORY_END);if(-1===e)throw new Error("Corrupted zip : can't find end of central directory");if(this.reader.setIndex(e),this.checkSignature(o.CENTRAL_DIRECTORY_END),this.readBlockEndOfCentral(),this.diskNumber===i.MAX_VALUE_16BITS||this.diskWithCentralDirStart===i.MAX_VALUE_16BITS||this.centralDirRecordsOnThisDisk===i.MAX_VALUE_16BITS||this.centralDirRecords===i.MAX_VALUE_16BITS||this.centralDirSize===i.MAX_VALUE_32BITS||this.centralDirOffset===i.MAX_VALUE_32BITS){if(this.zip64=!0,-1===(e=this.reader.lastIndexOfSignature(o.ZIP64_CENTRAL_DIRECTORY_LOCATOR)))throw new Error("Corrupted zip : can't find the ZIP64 end of central directory locator");this.reader.setIndex(e),this.checkSignature(o.ZIP64_CENTRAL_DIRECTORY_LOCATOR),this.readBlockZip64EndOfCentralLocator(),this.reader.setIndex(this.relativeOffsetEndOfZip64CentralDir),this.checkSignature(o.ZIP64_CENTRAL_DIRECTORY_END),this.readBlockZip64EndOfCentral()}},prepareReader:function(e){var t=i.getTypeOf(e);"string"!==t||c.uint8array?this.reader="nodebuffer"===t?new a(e):new s(i.transformTo("uint8array",e)):this.reader=new n(e,this.loadOptions.optimizedBinaryString)},load:function(e){this.prepareReader(e),this.readEndOfCentral(),this.readCentralDir(),this.readLocalFiles()}},t.exports=h},{"./nodeBufferReader":12,"./object":13,"./signature":14,"./stringReader":15,"./support":17,"./uint8ArrayReader":18,"./utils":21,"./zipEntry":23}],23:[function(e,t,r){"use strict";var n=e("./stringReader"),s=e("./utils"),a=e("./compressedObject"),i=e("./object");function o(e,t){this.options=e,this.loadOptions=t}o.prototype={isEncrypted:function(){return 1==(1&this.bitFlag)},useUTF8:function(){return 2048==(2048&this.bitFlag)},prepareCompressedContent:function(r,n,a){return function(){var e=r.index;r.setIndex(n);var t=r.readData(a);return r.setIndex(e),t}},prepareContent:function(e,t,r,n,a){return function(){var e=s.transformTo(n.uncompressInputType,this.getCompressedContent()),e=n.uncompress(e);if(e.length!==a)throw new Error("Bug : uncompressed data size mismatch");return e}},readLocalPart:function(e){var t;if(e.skip(22),this.fileNameLength=e.readInt(2),t=e.readInt(2),this.fileName=e.readString(this.fileNameLength),e.skip(t),-1==this.compressedSize||-1==this.uncompressedSize)throw new Error("Bug or corrupted zip : didn't get enough informations from the central directory (compressedSize == -1 || uncompressedSize == -1)");if(null===(t=s.findCompression(this.compressionMethod)))throw new Error("Corrupted zip : compression "+s.pretty(this.compressionMethod)+" unknown (inner file : "+this.fileName+")");if(this.decompressed=new a,this.decompressed.compressedSize=this.compressedSize,this.decompressed.uncompressedSize=this.uncompressedSize,this.decompressed.crc32=this.crc32,this.decompressed.compressionMethod=this.compressionMethod,this.decompressed.getCompressedContent=this.prepareCompressedContent(e,e.index,this.compressedSize,t),this.decompressed.getContent=this.prepareContent(e,e.index,this.compressedSize,t,this.uncompressedSize),this.loadOptions.checkCRC32&&(this.decompressed=s.transformTo("string",this.decompressed.getContent()),i.crc32(this.decompressed)!==this.crc32))throw new Error("Corrupted zip : CRC32 mismatch")},readCentralPart:function(e){if(this.versionMadeBy=e.readString(2),this.versionNeeded=e.readInt(2),this.bitFlag=e.readInt(2),this.compressionMethod=e.readString(2),this.date=e.readDate(),this.crc32=e.readInt(4),this.compressedSize=e.readInt(4),this.uncompressedSize=e.readInt(4),this.fileNameLength=e.readInt(2),this.extraFieldsLength=e.readInt(2),this.fileCommentLength=e.readInt(2),this.diskNumberStart=e.readInt(2),this.internalFileAttributes=e.readInt(2),this.externalFileAttributes=e.readInt(4),this.localHeaderOffset=e.readInt(4),this.isEncrypted())throw new Error("Encrypted zip are not supported");this.fileName=e.readString(this.fileNameLength),this.readExtraFields(e),this.parseZIP64ExtraField(e),this.fileComment=e.readString(this.fileCommentLength),this.dir=!!(16&this.externalFileAttributes)},parseZIP64ExtraField:function(e){var t;this.extraFields[1]&&(t=new n(this.extraFields[1].value),this.uncompressedSize===s.MAX_VALUE_32BITS&&(this.uncompressedSize=t.readInt(8)),this.compressedSize===s.MAX_VALUE_32BITS&&(this.compressedSize=t.readInt(8)),this.localHeaderOffset===s.MAX_VALUE_32BITS&&(this.localHeaderOffset=t.readInt(8)),this.diskNumberStart===s.MAX_VALUE_32BITS&&(this.diskNumberStart=t.readInt(4)))},readExtraFields:function(e){var t,r,n,a=e.index;for(this.extraFields=this.extraFields||{};e.index<a+this.extraFieldsLength;)t=e.readInt(2),r=e.readInt(2),n=e.readString(r),this.extraFields[t]={id:t,length:r,value:n}},handleUTF8:function(){var e;this.useUTF8()?(this.fileName=i.utf8decode(this.fileName),this.fileComment=i.utf8decode(this.fileComment)):(null!==(e=this.findExtraFieldUnicodePath())&&(this.fileName=e),null!==(e=this.findExtraFieldUnicodeComment())&&(this.fileComment=e))},findExtraFieldUnicodePath:function(){var e=this.extraFields[28789];if(e){var t=new n(e.value);return 1!==t.readInt(1)?null:i.crc32(this.fileName)!==t.readInt(4)?null:i.utf8decode(t.readString(e.length-5))}return null},findExtraFieldUnicodeComment:function(){var e=this.extraFields[25461];if(e){var t=new n(e.value);return 1!==t.readInt(1)?null:i.crc32(this.fileComment)!==t.readInt(4)?null:i.utf8decode(t.readString(e.length-5))}return null}},t.exports=o},{"./compressedObject":2,"./object":13,"./stringReader":15,"./utils":21}],24:[function(e,t,r){"use strict";var n={};(0,e("./lib/utils/common").assign)(n,e("./lib/deflate"),e("./lib/inflate"),e("./lib/zlib/constants")),t.exports=n},{"./lib/deflate":25,"./lib/inflate":26,"./lib/utils/common":27,"./lib/zlib/constants":30}],25:[function(e,t,r){"use strict";function n(e){this.options=o.assign({level:-1,method:8,chunkSize:16384,windowBits:15,memLevel:8,strategy:0,to:""},e||{});var t=this.options;if(t.raw&&0<t.windowBits?t.windowBits=-t.windowBits:t.gzip&&0<t.windowBits&&t.windowBits<16&&(t.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new s,(this.strm.avail_out=0)!==(e=i.deflateInit2(this.strm,t.level,t.method,t.windowBits,t.memLevel,t.strategy)))throw new Error(a[e]);t.header&&i.deflateSetHeader(this.strm,t.header)}var i=e("./zlib/deflate.js"),o=e("./utils/common"),l=e("./utils/strings"),a=e("./zlib/messages"),s=e("./zlib/zstream");function c(e,t){t=new n(t);if(t.push(e,!0),t.err)throw t.msg;return t.result}n.prototype.push=function(e,t){var r,n,a=this.strm,s=this.options.chunkSize;if(this.ended)return!1;n=t===~~t?t:!0===t?4:0,a.input="string"==typeof e?l.string2buf(e):e,a.next_in=0,a.avail_in=a.input.length;do{if(0===a.avail_out&&(a.output=new o.Buf8(s),a.next_out=0,a.avail_out=s),1!==(r=i.deflate(a,n))&&0!==r)return this.onEnd(r),!(this.ended=!0)}while((0===a.avail_out||0===a.avail_in&&4===n)&&("string"===this.options.to?this.onData(l.buf2binstring(o.shrinkBuf(a.output,a.next_out))):this.onData(o.shrinkBuf(a.output,a.next_out))),(0<a.avail_in||0===a.avail_out)&&1!==r);return 4!==n||(r=i.deflateEnd(this.strm),this.onEnd(r),this.ended=!0,0===r)},n.prototype.onData=function(e){this.chunks.push(e)},n.prototype.onEnd=function(e){0===e&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=o.flattenChunks(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg},r.Deflate=n,r.deflate=c,r.deflateRaw=function(e,t){return(t=t||{}).raw=!0,c(e,t)},r.gzip=function(e,t){return(t=t||{}).gzip=!0,c(e,t)}},{"./utils/common":27,"./utils/strings":28,"./zlib/deflate.js":32,"./zlib/messages":37,"./zlib/zstream":39}],26:[function(e,t,r){"use strict";function n(e){this.options=f.assign({chunkSize:16384,windowBits:0,to:""},e||{});var t=this.options;if(t.raw&&0<=t.windowBits&&t.windowBits<16&&(t.windowBits=-t.windowBits,0===t.windowBits&&(t.windowBits=-15)),!(0<=t.windowBits&&t.windowBits<16)||e&&e.windowBits||(t.windowBits+=32),15<t.windowBits&&t.windowBits<48&&0==(15&t.windowBits)&&(t.windowBits|=15),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new s,this.strm.avail_out=0,(t=c.inflateInit2(this.strm,t.windowBits))!==u.Z_OK)throw new Error(a[t]);this.header=new i,c.inflateGetHeader(this.strm,this.header)}var c=e("./zlib/inflate.js"),f=e("./utils/common"),h=e("./utils/strings"),u=e("./zlib/constants"),a=e("./zlib/messages"),s=e("./zlib/zstream"),i=e("./zlib/gzheader");function o(e,t){t=new n(t);if(t.push(e,!0),t.err)throw t.msg;return t.result}n.prototype.push=function(e,t){var r,n,a,s,i,o=this.strm,l=this.options.chunkSize;if(this.ended)return!1;n=t===~~t?t:!0===t?u.Z_FINISH:u.Z_NO_FLUSH,o.input="string"==typeof e?h.binstring2buf(e):e,o.next_in=0,o.avail_in=o.input.length;do{if(0===o.avail_out&&(o.output=new f.Buf8(l),o.next_out=0,o.avail_out=l),(r=c.inflate(o,u.Z_NO_FLUSH))!==u.Z_STREAM_END&&r!==u.Z_OK)return this.onEnd(r),!(this.ended=!0)}while(o.next_out&&(0===o.avail_out||r===u.Z_STREAM_END||0===o.avail_in&&n===u.Z_FINISH)&&("string"===this.options.to?(a=h.utf8border(o.output,o.next_out),s=o.next_out-a,i=h.buf2string(o.output,a),o.next_out=s,o.avail_out=l-s,s&&f.arraySet(o.output,o.output,a,s,0),this.onData(i)):this.onData(f.shrinkBuf(o.output,o.next_out))),0<o.avail_in&&r!==u.Z_STREAM_END);return(n=r===u.Z_STREAM_END?u.Z_FINISH:n)!==u.Z_FINISH||(r=c.inflateEnd(this.strm),this.onEnd(r),this.ended=!0,r===u.Z_OK)},n.prototype.onData=function(e){this.chunks.push(e)},n.prototype.onEnd=function(e){e===u.Z_OK&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=f.flattenChunks(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg},r.Inflate=n,r.inflate=o,r.inflateRaw=function(e,t){return(t=t||{}).raw=!0,o(e,t)},r.ungzip=o},{"./utils/common":27,"./utils/strings":28,"./zlib/constants":30,"./zlib/gzheader":33,"./zlib/inflate.js":35,"./zlib/messages":37,"./zlib/zstream":39}],27:[function(e,t,r){"use strict";var n="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Int32Array;r.assign=function(e){for(var t=Array.prototype.slice.call(arguments,1);t.length;){var r=t.shift();if(r){if("object"!=typeof r)throw new TypeError(r+"must be non-object");for(var n in r)r.hasOwnProperty(n)&&(e[n]=r[n])}}return e},r.shrinkBuf=function(e,t){return e.length===t?e:e.subarray?e.subarray(0,t):(e.length=t,e)};var a={arraySet:function(e,t,r,n,a){if(t.subarray&&e.subarray)e.set(t.subarray(r,r+n),a);else for(var s=0;s<n;s++)e[a+s]=t[r+s]},flattenChunks:function(e){for(var t,r,n,a=0,s=0,i=e.length;s<i;s++)a+=e[s].length;for(n=new Uint8Array(a),s=t=0,i=e.length;s<i;s++)r=e[s],n.set(r,t),t+=r.length;return n}},s={arraySet:function(e,t,r,n,a){for(var s=0;s<n;s++)e[a+s]=t[r+s]},flattenChunks:function(e){return[].concat.apply([],e)}};r.setTyped=function(e){e?(r.Buf8=Uint8Array,r.Buf16=Uint16Array,r.Buf32=Int32Array,r.assign(r,a)):(r.Buf8=Array,r.Buf16=Array,r.Buf32=Array,r.assign(r,s))},r.setTyped(n)},{}],28:[function(e,t,r){"use strict";var l=e("./common"),a=!0,s=!0;try{String.fromCharCode.apply(null,[0])}catch(e){a=!1}try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(e){s=!1}for(var c=new l.Buf8(256),n=0;n<256;n++)c[n]=252<=n?6:248<=n?5:240<=n?4:224<=n?3:192<=n?2:1;function f(e,t){if(t<65537&&(e.subarray&&s||!e.subarray&&a))return String.fromCharCode.apply(null,l.shrinkBuf(e,t));for(var r="",n=0;n<t;n++)r+=String.fromCharCode(e[n]);return r}c[254]=c[254]=1,r.string2buf=function(e){for(var t,r,n,a,s=e.length,i=0,o=0;o<s;o++)55296==(64512&(r=e.charCodeAt(o)))&&o+1<s&&56320==(64512&(n=e.charCodeAt(o+1)))&&(r=65536+(r-55296<<10)+(n-56320),o++),i+=r<128?1:r<2048?2:r<65536?3:4;for(t=new l.Buf8(i),o=a=0;a<i;o++)55296==(64512&(r=e.charCodeAt(o)))&&o+1<s&&56320==(64512&(n=e.charCodeAt(o+1)))&&(r=65536+(r-55296<<10)+(n-56320),o++),r<128?t[a++]=r:(r<2048?t[a++]=192|r>>>6:(r<65536?t[a++]=224|r>>>12:(t[a++]=240|r>>>18,t[a++]=128|r>>>12&63),t[a++]=128|r>>>6&63),t[a++]=128|63&r);return t},r.buf2binstring=function(e){return f(e,e.length)},r.binstring2buf=function(e){for(var t=new l.Buf8(e.length),r=0,n=t.length;r<n;r++)t[r]=e.charCodeAt(r);return t},r.buf2string=function(e,t){for(var r,n,a=t||e.length,s=new Array(2*a),i=0,o=0;o<a;)if((r=e[o++])<128)s[i++]=r;else if(4<(n=c[r]))s[i++]=65533,o+=n-1;else{for(r&=2===n?31:3===n?15:7;1<n&&o<a;)r=r<<6|63&e[o++],n--;1<n?s[i++]=65533:r<65536?s[i++]=r:(r-=65536,s[i++]=55296|r>>10&1023,s[i++]=56320|1023&r)}return f(s,i)},r.utf8border=function(e,t){for(var r=(t=(t=t||e.length)>e.length?e.length:t)-1;0<=r&&128==(192&e[r]);)r--;return!(r<0)&&0!==r&&r+c[e[r]]>t?r:t}},{"./common":27}],29:[function(e,t,r){"use strict";t.exports=function(e,t,r,n){for(var a=65535&e|0,s=e>>>16&65535|0,i=0;0!==r;){for(r-=i=2e3<r?2e3:r;s=s+(a=a+t[n++]|0)|0,--i;);a%=65521,s%=65521}return a|s<<16|0}},{}],30:[function(e,t,r){t.exports={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8}},{}],31:[function(e,t,r){"use strict";var o=function(){for(var e=[],t=0;t<256;t++){for(var r=t,n=0;n<8;n++)r=1&r?3988292384^r>>>1:r>>>1;e[t]=r}return e}();t.exports=function(e,t,r,n){var a=o,s=n+r;e^=-1;for(var i=n;i<s;i++)e=e>>>8^a[255&(e^t[i])];return-1^e}},{}],32:[function(e,t,r){"use strict";var h=e("../utils/common"),o=e("./trees"),u=e("./adler32"),d=e("./crc32"),n=e("./messages"),l=0,c=4,f=0,p=-2,m=-1,g=4,a=2,b=8,v=9,s=286,i=30,E=19,w=2*s+1,S=15,_=3,y=258,C=y+_+1,B=42,T=113,k=1,x=2,A=3,I=4;function R(e,t){return e.msg=n[t],t}function O(e){return(e<<1)-(4<e?9:0)}function F(e){for(var t=e.length;0<=--t;)e[t]=0}function D(e){var t=e.state,r=t.pending;0!==(r=r>e.avail_out?e.avail_out:r)&&(h.arraySet(e.output,t.pending_buf,t.pending_out,r,e.next_out),e.next_out+=r,t.pending_out+=r,e.total_out+=r,e.avail_out-=r,t.pending-=r,0===t.pending&&(t.pending_out=0))}function P(e,t){o._tr_flush_block(e,0<=e.block_start?e.block_start:-1,e.strstart-e.block_start,t),e.block_start=e.strstart,D(e.strm)}function N(e,t){e.pending_buf[e.pending++]=t}function L(e,t){e.pending_buf[e.pending++]=t>>>8&255,e.pending_buf[e.pending++]=255&t}function M(e,t){var r,n,a=e.max_chain_length,s=e.strstart,i=e.prev_length,o=e.nice_match,l=e.strstart>e.w_size-C?e.strstart-(e.w_size-C):0,c=e.window,f=e.w_mask,h=e.prev,u=e.strstart+y,d=c[s+i-1],p=c[s+i];e.prev_length>=e.good_match&&(a>>=2),o>e.lookahead&&(o=e.lookahead);do{if(c[(r=t)+i]===p&&c[r+i-1]===d&&c[r]===c[s]&&c[++r]===c[s+1]){for(s+=2,r++;c[++s]===c[++r]&&c[++s]===c[++r]&&c[++s]===c[++r]&&c[++s]===c[++r]&&c[++s]===c[++r]&&c[++s]===c[++r]&&c[++s]===c[++r]&&c[++s]===c[++r]&&s<u;);if(n=y-(u-s),s=u-y,i<n){if(e.match_start=t,o<=(i=n))break;d=c[s+i-1],p=c[s+i]}}}while((t=h[t&f])>l&&0!=--a);return i<=e.lookahead?i:e.lookahead}function U(e){var t,r,n,a,s,i,o,l,c,f=e.w_size;do{if(c=e.window_size-e.lookahead-e.strstart,e.strstart>=f+(f-C)){for(h.arraySet(e.window,e.window,f,f,0),e.match_start-=f,e.strstart-=f,e.block_start-=f,t=r=e.hash_size;n=e.head[--t],e.head[t]=f<=n?n-f:0,--r;);for(t=r=f;n=e.prev[--t],e.prev[t]=f<=n?n-f:0,--r;);c+=f}if(0===e.strm.avail_in)break;if(s=e.strm,i=e.window,o=e.strstart+e.lookahead,l=c,c=void 0,c=s.avail_in,r=0===(c=l<c?l:c)?0:(s.avail_in-=c,h.arraySet(i,s.input,s.next_in,c,o),1===s.state.wrap?s.adler=u(s.adler,i,c,o):2===s.state.wrap&&(s.adler=d(s.adler,i,c,o)),s.next_in+=c,s.total_in+=c,c),e.lookahead+=r,e.lookahead+e.insert>=_)for(a=e.strstart-e.insert,e.ins_h=e.window[a],e.ins_h=(e.ins_h<<e.hash_shift^e.window[a+1])&e.hash_mask;e.insert&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[a+_-1])&e.hash_mask,e.prev[a&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=a,a++,e.insert--,!(e.lookahead+e.insert<_)););}while(e.lookahead<C&&0!==e.strm.avail_in)}function H(e,t){for(var r,n;;){if(e.lookahead<C){if(U(e),e.lookahead<C&&t===l)return k;if(0===e.lookahead)break}if(r=0,e.lookahead>=_&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+_-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),0!==r&&e.strstart-r<=e.w_size-C&&(e.match_length=M(e,r)),e.match_length>=_)if(n=o._tr_tally(e,e.strstart-e.match_start,e.match_length-_),e.lookahead-=e.match_length,e.match_length<=e.max_lazy_match&&e.lookahead>=_){for(e.match_length--;e.strstart++,e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+_-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart,0!=--e.match_length;);e.strstart++}else e.strstart+=e.match_length,e.match_length=0,e.ins_h=e.window[e.strstart],e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+1])&e.hash_mask;else n=o._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++;if(n&&(P(e,!1),0===e.strm.avail_out))return k}return e.insert=e.strstart<_-1?e.strstart:_-1,t===c?(P(e,!0),0===e.strm.avail_out?A:I):e.last_lit&&(P(e,!1),0===e.strm.avail_out)?k:x}function z(e,t){for(var r,n,a;;){if(e.lookahead<C){if(U(e),e.lookahead<C&&t===l)return k;if(0===e.lookahead)break}if(r=0,e.lookahead>=_&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+_-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),e.prev_length=e.match_length,e.prev_match=e.match_start,e.match_length=_-1,0!==r&&e.prev_length<e.max_lazy_match&&e.strstart-r<=e.w_size-C&&(e.match_length=M(e,r),e.match_length<=5&&(1===e.strategy||e.match_length===_&&4096<e.strstart-e.match_start)&&(e.match_length=_-1)),e.prev_length>=_&&e.match_length<=e.prev_length){for(a=e.strstart+e.lookahead-_,n=o._tr_tally(e,e.strstart-1-e.prev_match,e.prev_length-_),e.lookahead-=e.prev_length-1,e.prev_length-=2;++e.strstart<=a&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+_-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),0!=--e.prev_length;);if(e.match_available=0,e.match_length=_-1,e.strstart++,n&&(P(e,!1),0===e.strm.avail_out))return k}else if(e.match_available){if((n=o._tr_tally(e,0,e.window[e.strstart-1]))&&P(e,!1),e.strstart++,e.lookahead--,0===e.strm.avail_out)return k}else e.match_available=1,e.strstart++,e.lookahead--}return e.match_available&&(n=o._tr_tally(e,0,e.window[e.strstart-1]),e.match_available=0),e.insert=e.strstart<_-1?e.strstart:_-1,t===c?(P(e,!0),0===e.strm.avail_out?A:I):e.last_lit&&(P(e,!1),0===e.strm.avail_out)?k:x}var V,e=function(e,t,r,n,a){this.good_length=e,this.max_lazy=t,this.nice_length=r,this.max_chain=n,this.func=a};function W(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=b,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new h.Buf16(2*w),this.dyn_dtree=new h.Buf16(2*(2*i+1)),this.bl_tree=new h.Buf16(2*(2*E+1)),F(this.dyn_ltree),F(this.dyn_dtree),F(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new h.Buf16(S+1),this.heap=new h.Buf16(2*s+1),F(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new h.Buf16(2*s+1),F(this.depth),this.l_buf=0,this.lit_bufsize=0,this.last_lit=0,this.d_buf=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}function X(e){var t;return e&&e.state?(e.total_in=e.total_out=0,e.data_type=a,(t=e.state).pending=0,t.pending_out=0,t.wrap<0&&(t.wrap=-t.wrap),t.status=t.wrap?B:T,e.adler=2===t.wrap?0:1,t.last_flush=l,o._tr_init(t),f):R(e,p)}function j(e){var t=X(e);return t===f&&((e=e.state).window_size=2*e.w_size,F(e.head),e.max_lazy_match=V[e.level].max_lazy,e.good_match=V[e.level].good_length,e.nice_match=V[e.level].nice_length,e.max_chain_length=V[e.level].max_chain,e.strstart=0,e.block_start=0,e.lookahead=0,e.insert=0,e.match_length=e.prev_length=_-1,e.match_available=0,e.ins_h=0),t}function G(e,t,r,n,a,s){if(!e)return p;var i=1;if(t===m&&(t=6),n<0?(i=0,n=-n):15<n&&(i=2,n-=16),a<1||v<a||r!==b||n<8||15<n||t<0||9<t||s<0||g<s)return R(e,p);8===n&&(n=9);var o=new W;return(e.state=o).strm=e,o.wrap=i,o.gzhead=null,o.w_bits=n,o.w_size=1<<o.w_bits,o.w_mask=o.w_size-1,o.hash_bits=a+7,o.hash_size=1<<o.hash_bits,o.hash_mask=o.hash_size-1,o.hash_shift=~~((o.hash_bits+_-1)/_),o.window=new h.Buf8(2*o.w_size),o.head=new h.Buf16(o.hash_size),o.prev=new h.Buf16(o.w_size),o.lit_bufsize=1<<a+6,o.pending_buf_size=4*o.lit_bufsize,o.pending_buf=new h.Buf8(o.pending_buf_size),o.d_buf=o.lit_bufsize>>1,o.l_buf=3*o.lit_bufsize,o.level=t,o.strategy=s,o.method=r,j(e)}V=[new e(0,0,0,0,function(e,t){var r=65535;for(r>e.pending_buf_size-5&&(r=e.pending_buf_size-5);;){if(e.lookahead<=1){if(U(e),0===e.lookahead&&t===l)return k;if(0===e.lookahead)break}e.strstart+=e.lookahead,e.lookahead=0;var n=e.block_start+r;if((0===e.strstart||e.strstart>=n)&&(e.lookahead=e.strstart-n,e.strstart=n,P(e,!1),0===e.strm.avail_out))return k;if(e.strstart-e.block_start>=e.w_size-C&&(P(e,!1),0===e.strm.avail_out))return k}return e.insert=0,t===c?(P(e,!0),0===e.strm.avail_out?A:I):(e.strstart>e.block_start&&(P(e,!1),e.strm.avail_out),k)}),new e(4,4,8,4,H),new e(4,5,16,8,H),new e(4,6,32,32,H),new e(4,4,16,16,z),new e(8,16,32,32,z),new e(8,16,128,128,z),new e(8,32,128,256,z),new e(32,128,258,1024,z),new e(32,258,258,4096,z)],r.deflateInit=function(e,t){return G(e,t,b,15,8,0)},r.deflateInit2=G,r.deflateReset=j,r.deflateResetKeep=X,r.deflateSetHeader=function(e,t){return!e||!e.state||2!==e.state.wrap?p:(e.state.gzhead=t,f)},r.deflate=function(e,t){var r,n,a,s;if(!e||!e.state||5<t||t<0)return e?R(e,p):p;if(r=e.state,!e.output||!e.input&&0!==e.avail_in||666===r.status&&t!==c)return R(e,0===e.avail_out?-5:p);if(r.strm=e,i=r.last_flush,r.last_flush=t,r.status===B&&(2===r.wrap?(e.adler=0,N(r,31),N(r,139),N(r,8),r.gzhead?(N(r,(r.gzhead.text?1:0)+(r.gzhead.hcrc?2:0)+(r.gzhead.extra?4:0)+(r.gzhead.name?8:0)+(r.gzhead.comment?16:0)),N(r,255&r.gzhead.time),N(r,r.gzhead.time>>8&255),N(r,r.gzhead.time>>16&255),N(r,r.gzhead.time>>24&255),N(r,9===r.level?2:2<=r.strategy||r.level<2?4:0),N(r,255&r.gzhead.os),r.gzhead.extra&&r.gzhead.extra.length&&(N(r,255&r.gzhead.extra.length),N(r,r.gzhead.extra.length>>8&255)),r.gzhead.hcrc&&(e.adler=d(e.adler,r.pending_buf,r.pending,0)),r.gzindex=0,r.status=69):(N(r,0),N(r,0),N(r,0),N(r,0),N(r,0),N(r,9===r.level?2:2<=r.strategy||r.level<2?4:0),N(r,3),r.status=T)):(s=b+(r.w_bits-8<<4)<<8,s|=(2<=r.strategy||r.level<2?0:r.level<6?1:6===r.level?2:3)<<6,0!==r.strstart&&(s|=32),s+=31-s%31,r.status=T,L(r,s),0!==r.strstart&&(L(r,e.adler>>>16),L(r,65535&e.adler)),e.adler=1)),69===r.status)if(r.gzhead.extra){for(n=r.pending;r.gzindex<(65535&r.gzhead.extra.length)&&(r.pending!==r.pending_buf_size||(r.gzhead.hcrc&&r.pending>n&&(e.adler=d(e.adler,r.pending_buf,r.pending-n,n)),D(e),n=r.pending,r.pending!==r.pending_buf_size));)N(r,255&r.gzhead.extra[r.gzindex]),r.gzindex++;r.gzhead.hcrc&&r.pending>n&&(e.adler=d(e.adler,r.pending_buf,r.pending-n,n)),r.gzindex===r.gzhead.extra.length&&(r.gzindex=0,r.status=73)}else r.status=73;if(73===r.status)if(r.gzhead.name){n=r.pending;do{if(r.pending===r.pending_buf_size&&(r.gzhead.hcrc&&r.pending>n&&(e.adler=d(e.adler,r.pending_buf,r.pending-n,n)),D(e),n=r.pending,r.pending===r.pending_buf_size)){a=1;break}}while(a=r.gzindex<r.gzhead.name.length?255&r.gzhead.name.charCodeAt(r.gzindex++):0,N(r,a),0!==a);r.gzhead.hcrc&&r.pending>n&&(e.adler=d(e.adler,r.pending_buf,r.pending-n,n)),0===a&&(r.gzindex=0,r.status=91)}else r.status=91;if(91===r.status)if(r.gzhead.comment){n=r.pending;do{if(r.pending===r.pending_buf_size&&(r.gzhead.hcrc&&r.pending>n&&(e.adler=d(e.adler,r.pending_buf,r.pending-n,n)),D(e),n=r.pending,r.pending===r.pending_buf_size)){a=1;break}}while(a=r.gzindex<r.gzhead.comment.length?255&r.gzhead.comment.charCodeAt(r.gzindex++):0,N(r,a),0!==a);r.gzhead.hcrc&&r.pending>n&&(e.adler=d(e.adler,r.pending_buf,r.pending-n,n)),0===a&&(r.status=103)}else r.status=103;if(103===r.status&&(r.gzhead.hcrc?(r.pending+2>r.pending_buf_size&&D(e),r.pending+2<=r.pending_buf_size&&(N(r,255&e.adler),N(r,e.adler>>8&255),e.adler=0,r.status=T)):r.status=T),0!==r.pending){if(D(e),0===e.avail_out)return r.last_flush=-1,f}else if(0===e.avail_in&&O(t)<=O(i)&&t!==c)return R(e,-5);if(666===r.status&&0!==e.avail_in)return R(e,-5);if(0!==e.avail_in||0!==r.lookahead||t!==l&&666!==r.status){var i=2===r.strategy?function(e,t){for(var r;;){if(0===e.lookahead&&(U(e),0===e.lookahead)){if(t===l)return k;break}if(e.match_length=0,r=o._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++,r&&(P(e,!1),0===e.strm.avail_out))return k}return e.insert=0,t===c?(P(e,!0),0===e.strm.avail_out?A:I):e.last_lit&&(P(e,!1),0===e.strm.avail_out)?k:x}(r,t):3===r.strategy?function(e,t){for(var r,n,a,s,i=e.window;;){if(e.lookahead<=y){if(U(e),e.lookahead<=y&&t===l)return k;if(0===e.lookahead)break}if(e.match_length=0,e.lookahead>=_&&0<e.strstart&&(n=i[a=e.strstart-1])===i[++a]&&n===i[++a]&&n===i[++a]){for(s=e.strstart+y;n===i[++a]&&n===i[++a]&&n===i[++a]&&n===i[++a]&&n===i[++a]&&n===i[++a]&&n===i[++a]&&n===i[++a]&&a<s;);e.match_length=y-(s-a),e.match_length>e.lookahead&&(e.match_length=e.lookahead)}if(e.match_length>=_?(r=o._tr_tally(e,1,e.match_length-_),e.lookahead-=e.match_length,e.strstart+=e.match_length,e.match_length=0):(r=o._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++),r&&(P(e,!1),0===e.strm.avail_out))return k}return e.insert=0,t===c?(P(e,!0),0===e.strm.avail_out?A:I):e.last_lit&&(P(e,!1),0===e.strm.avail_out)?k:x}(r,t):V[r.level].func(r,t);if(i!==A&&i!==I||(r.status=666),i===k||i===A)return 0===e.avail_out&&(r.last_flush=-1),f;if(i===x&&(1===t?o._tr_align(r):5!==t&&(o._tr_stored_block(r,0,0,!1),3===t&&(F(r.head),0===r.lookahead&&(r.strstart=0,r.block_start=0,r.insert=0))),D(e),0===e.avail_out))return r.last_flush=-1,f}return t!==c?f:r.wrap<=0?1:(2===r.wrap?(N(r,255&e.adler),N(r,e.adler>>8&255),N(r,e.adler>>16&255),N(r,e.adler>>24&255),N(r,255&e.total_in),N(r,e.total_in>>8&255),N(r,e.total_in>>16&255),N(r,e.total_in>>24&255)):(L(r,e.adler>>>16),L(r,65535&e.adler)),D(e),0<r.wrap&&(r.wrap=-r.wrap),0!==r.pending?f:1)},r.deflateEnd=function(e){var t;return e&&e.state?(t=e.state.status)!==B&&69!==t&&73!==t&&91!==t&&103!==t&&t!==T&&666!==t?R(e,p):(e.state=null,t===T?R(e,-3):f):p},r.deflateInfo="pako deflate (from Nodeca project)"},{"../utils/common":27,"./adler32":29,"./crc32":31,"./messages":37,"./trees":38}],33:[function(e,t,r){"use strict";t.exports=function(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1}},{}],34:[function(e,t,r){"use strict";t.exports=function(e,t){var r,n,a,s,i,o,l=e.state,c=e.next_in,f=e.input,h=c+(e.avail_in-5),u=e.next_out,d=e.output,p=u-(t-e.avail_out),m=u+(e.avail_out-257),g=l.dmax,b=l.wsize,v=l.whave,E=l.wnext,w=l.window,S=l.hold,_=l.bits,y=l.lencode,C=l.distcode,B=(1<<l.lenbits)-1,T=(1<<l.distbits)-1;e:do{_<15&&(S+=f[c++]<<_,_+=8,S+=f[c++]<<_,_+=8),r=y[S&B];t:for(;;){if(S>>>=n=r>>>24,_-=n,0===(n=r>>>16&255))d[u++]=65535&r;else{if(!(16&n)){if(0==(64&n)){r=y[(65535&r)+(S&(1<<n)-1)];continue t}if(32&n){l.mode=12;break e}e.msg="invalid literal/length code",l.mode=30;break e}a=65535&r,(n&=15)&&(_<n&&(S+=f[c++]<<_,_+=8),a+=S&(1<<n)-1,S>>>=n,_-=n),_<15&&(S+=f[c++]<<_,_+=8,S+=f[c++]<<_,_+=8),r=C[S&T];r:for(;;){if(S>>>=n=r>>>24,_-=n,!(16&(n=r>>>16&255))){if(0==(64&n)){r=C[(65535&r)+(S&(1<<n)-1)];continue r}e.msg="invalid distance code",l.mode=30;break e}if(s=65535&r,_<(n&=15)&&(S+=f[c++]<<_,(_+=8)<n&&(S+=f[c++]<<_,_+=8)),g<(s+=S&(1<<n)-1)){e.msg="invalid distance too far back",l.mode=30;break e}if(S>>>=n,_-=n,(n=u-p)<s){if(v<(n=s-n)&&l.sane){e.msg="invalid distance too far back",l.mode=30;break e}if(o=w,(i=0)===E){if(i+=b-n,n<a){for(a-=n;d[u++]=w[i++],--n;);i=u-s,o=d}}else if(E<n){if(i+=b+E-n,(n-=E)<a){for(a-=n;d[u++]=w[i++],--n;);if(i=0,E<a){for(a-=n=E;d[u++]=w[i++],--n;);i=u-s,o=d}}}else if(i+=E-n,n<a){for(a-=n;d[u++]=w[i++],--n;);i=u-s,o=d}for(;2<a;)d[u++]=o[i++],d[u++]=o[i++],d[u++]=o[i++],a-=3;a&&(d[u++]=o[i++],1<a&&(d[u++]=o[i++]))}else{for(i=u-s;d[u++]=d[i++],d[u++]=d[i++],d[u++]=d[i++],2<(a-=3););a&&(d[u++]=d[i++],1<a&&(d[u++]=d[i++]))}break}}break}}while(c<h&&u<m);S&=(1<<(_-=(a=_>>3)<<3))-1,e.next_in=c-=a,e.next_out=u,e.avail_in=c<h?h-c+5:5-(c-h),e.avail_out=u<m?m-u+257:257-(u-m),l.hold=S,l.bits=_}},{}],35:[function(e,t,r){"use strict";var D=e("../utils/common"),P=e("./adler32"),N=e("./crc32"),L=e("./inffast"),M=e("./inftrees"),U=1,H=2,z=0,V=-2,W=1,n=852,a=592;function X(e){return(e>>>24&255)+(e>>>8&65280)+((65280&e)<<8)+((255&e)<<24)}function s(){this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new D.Buf16(320),this.work=new D.Buf16(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}function i(e){var t;return e&&e.state?(t=e.state,e.total_in=e.total_out=t.total=0,e.msg="",t.wrap&&(e.adler=1&t.wrap),t.mode=W,t.last=0,t.havedict=0,t.dmax=32768,t.head=null,t.hold=0,t.bits=0,t.lencode=t.lendyn=new D.Buf32(n),t.distcode=t.distdyn=new D.Buf32(a),t.sane=1,t.back=-1,z):V}function o(e){var t;return e&&e.state?((t=e.state).wsize=0,t.whave=0,t.wnext=0,i(e)):V}function l(e,t){var r,n;return e&&e.state?(n=e.state,t<0?(r=0,t=-t):(r=1+(t>>4),t<48&&(t&=15)),t&&(t<8||15<t)?V:(null!==n.window&&n.wbits!==t&&(n.window=null),n.wrap=r,n.wbits=t,o(e))):V}function c(e,t){var r;return e?(r=new s,(e.state=r).window=null,(t=l(e,t))!==z&&(e.state=null),t):V}var j,G,$=!0;r.inflateReset=o,r.inflateReset2=l,r.inflateResetKeep=i,r.inflateInit=function(e){return c(e,15)},r.inflateInit2=c,r.inflate=function(e,t){var r,n,a,s,i,o,l,c,f,h,u,d,p,m,g,b,v,E,w,S,_,y,C,B,T,k,x,A,I,R=0,O=new D.Buf8(4),F=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];if(!e||!e.state||!e.output||!e.input&&0!==e.avail_in)return V;12===(r=e.state).mode&&(r.mode=13),i=e.next_out,a=e.output,l=e.avail_out,s=e.next_in,n=e.input,o=e.avail_in,c=r.hold,f=r.bits,h=o,u=l,y=z;e:for(;;)switch(r.mode){case W:if(0===r.wrap){r.mode=13;break}for(;f<16;){if(0===o)break e;o--,c+=n[s++]<<f,f+=8}if(2&r.wrap&&35615===c){O[r.check=0]=255&c,O[1]=c>>>8&255,r.check=N(r.check,O,2,0),f=c=0,r.mode=2;break}if(r.flags=0,r.head&&(r.head.done=!1),!(1&r.wrap)||(((255&c)<<8)+(c>>8))%31){e.msg="incorrect header check",r.mode=30;break}if(8!=(15&c)){e.msg="unknown compression method",r.mode=30;break}if(f-=4,_=8+(15&(c>>>=4)),0===r.wbits)r.wbits=_;else if(_>r.wbits){e.msg="invalid window size",r.mode=30;break}r.dmax=1<<_,e.adler=r.check=1,r.mode=512&c?10:12,f=c=0;break;case 2:for(;f<16;){if(0===o)break e;o--,c+=n[s++]<<f,f+=8}if(r.flags=c,8!=(255&r.flags)){e.msg="unknown compression method",r.mode=30;break}if(57344&r.flags){e.msg="unknown header flags set",r.mode=30;break}r.head&&(r.head.text=c>>8&1),512&r.flags&&(O[0]=255&c,O[1]=c>>>8&255,r.check=N(r.check,O,2,0)),f=c=0,r.mode=3;case 3:for(;f<32;){if(0===o)break e;o--,c+=n[s++]<<f,f+=8}r.head&&(r.head.time=c),512&r.flags&&(O[0]=255&c,O[1]=c>>>8&255,O[2]=c>>>16&255,O[3]=c>>>24&255,r.check=N(r.check,O,4,0)),f=c=0,r.mode=4;case 4:for(;f<16;){if(0===o)break e;o--,c+=n[s++]<<f,f+=8}r.head&&(r.head.xflags=255&c,r.head.os=c>>8),512&r.flags&&(O[0]=255&c,O[1]=c>>>8&255,r.check=N(r.check,O,2,0)),f=c=0,r.mode=5;case 5:if(1024&r.flags){for(;f<16;){if(0===o)break e;o--,c+=n[s++]<<f,f+=8}r.length=c,r.head&&(r.head.extra_len=c),512&r.flags&&(O[0]=255&c,O[1]=c>>>8&255,r.check=N(r.check,O,2,0)),f=c=0}else r.head&&(r.head.extra=null);r.mode=6;case 6:if(1024&r.flags&&((d=o<(d=r.length)?o:d)&&(r.head&&(_=r.head.extra_len-r.length,r.head.extra||(r.head.extra=new Array(r.head.extra_len)),D.arraySet(r.head.extra,n,s,d,_)),512&r.flags&&(r.check=N(r.check,n,d,s)),o-=d,s+=d,r.length-=d),r.length))break e;r.length=0,r.mode=7;case 7:if(2048&r.flags){if(0===o)break e;for(d=0;_=n[s+d++],r.head&&_&&r.length<65536&&(r.head.name+=String.fromCharCode(_)),_&&d<o;);if(512&r.flags&&(r.check=N(r.check,n,d,s)),o-=d,s+=d,_)break e}else r.head&&(r.head.name=null);r.length=0,r.mode=8;case 8:if(4096&r.flags){if(0===o)break e;for(d=0;_=n[s+d++],r.head&&_&&r.length<65536&&(r.head.comment+=String.fromCharCode(_)),_&&d<o;);if(512&r.flags&&(r.check=N(r.check,n,d,s)),o-=d,s+=d,_)break e}else r.head&&(r.head.comment=null);r.mode=9;case 9:if(512&r.flags){for(;f<16;){if(0===o)break e;o--,c+=n[s++]<<f,f+=8}if(c!==(65535&r.check)){e.msg="header crc mismatch",r.mode=30;break}f=c=0}r.head&&(r.head.hcrc=r.flags>>9&1,r.head.done=!0),e.adler=r.check=0,r.mode=12;break;case 10:for(;f<32;){if(0===o)break e;o--,c+=n[s++]<<f,f+=8}e.adler=r.check=X(c),f=c=0,r.mode=11;case 11:if(0===r.havedict)return e.next_out=i,e.avail_out=l,e.next_in=s,e.avail_in=o,r.hold=c,r.bits=f,2;e.adler=r.check=1,r.mode=12;case 12:if(5===t||6===t)break e;case 13:if(r.last){c>>>=7&f,f-=7&f,r.mode=27;break}for(;f<3;){if(0===o)break e;o--,c+=n[s++]<<f,f+=8}switch(r.last=1&c,--f,3&(c>>>=1)){case 0:r.mode=14;break;case 1:if(!function(e){if($){var t;for(j=new D.Buf32(512),G=new D.Buf32(32),t=0;t<144;)e.lens[t++]=8;for(;t<256;)e.lens[t++]=9;for(;t<280;)e.lens[t++]=7;for(;t<288;)e.lens[t++]=8;for(M(U,e.lens,0,288,j,0,e.work,{bits:9}),t=0;t<32;)e.lens[t++]=5;M(H,e.lens,0,32,G,0,e.work,{bits:5}),$=!1}e.lencode=j,e.lenbits=9,e.distcode=G,e.distbits=5}(r),r.mode=20,6!==t)break;c>>>=2,f-=2;break e;case 2:r.mode=17;break;case 3:e.msg="invalid block type",r.mode=30}c>>>=2,f-=2;break;case 14:for(c>>>=7&f,f-=7&f;f<32;){if(0===o)break e;o--,c+=n[s++]<<f,f+=8}if((65535&c)!=(c>>>16^65535)){e.msg="invalid stored block lengths",r.mode=30;break}if(r.length=65535&c,f=c=0,r.mode=15,6===t)break e;case 15:r.mode=16;case 16:if(d=r.length){if(0===(d=l<(d=o<d?o:d)?l:d))break e;D.arraySet(a,n,s,d,i),o-=d,s+=d,l-=d,i+=d,r.length-=d;break}r.mode=12;break;case 17:for(;f<14;){if(0===o)break e;o--,c+=n[s++]<<f,f+=8}if(r.nlen=257+(31&c),c>>>=5,f-=5,r.ndist=1+(31&c),c>>>=5,f-=5,r.ncode=4+(15&c),c>>>=4,f-=4,286<r.nlen||30<r.ndist){e.msg="too many length or distance symbols",r.mode=30;break}r.have=0,r.mode=18;case 18:for(;r.have<r.ncode;){for(;f<3;){if(0===o)break e;o--,c+=n[s++]<<f,f+=8}r.lens[F[r.have++]]=7&c,c>>>=3,f-=3}for(;r.have<19;)r.lens[F[r.have++]]=0;if(r.lencode=r.lendyn,r.lenbits=7,C={bits:r.lenbits},y=M(0,r.lens,0,19,r.lencode,0,r.work,C),r.lenbits=C.bits,y){e.msg="invalid code lengths set",r.mode=30;break}r.have=0,r.mode=19;case 19:for(;r.have<r.nlen+r.ndist;){for(;b=(R=r.lencode[c&(1<<r.lenbits)-1])>>>16&255,v=65535&R,!((g=R>>>24)<=f);){if(0===o)break e;o--,c+=n[s++]<<f,f+=8}if(v<16)c>>>=g,f-=g,r.lens[r.have++]=v;else{if(16===v){for(B=g+2;f<B;){if(0===o)break e;o--,c+=n[s++]<<f,f+=8}if(c>>>=g,f-=g,0===r.have){e.msg="invalid bit length repeat",r.mode=30;break}_=r.lens[r.have-1],d=3+(3&c),c>>>=2,f-=2}else if(17===v){for(B=g+3;f<B;){if(0===o)break e;o--,c+=n[s++]<<f,f+=8}f-=g,_=0,d=3+(7&(c>>>=g)),c>>>=3,f-=3}else{for(B=g+7;f<B;){if(0===o)break e;o--,c+=n[s++]<<f,f+=8}f-=g,_=0,d=11+(127&(c>>>=g)),c>>>=7,f-=7}if(r.have+d>r.nlen+r.ndist){e.msg="invalid bit length repeat",r.mode=30;break}for(;d--;)r.lens[r.have++]=_}}if(30===r.mode)break;if(0===r.lens[256]){e.msg="invalid code -- missing end-of-block",r.mode=30;break}if(r.lenbits=9,C={bits:r.lenbits},y=M(U,r.lens,0,r.nlen,r.lencode,0,r.work,C),r.lenbits=C.bits,y){e.msg="invalid literal/lengths set",r.mode=30;break}if(r.distbits=6,r.distcode=r.distdyn,C={bits:r.distbits},y=M(H,r.lens,r.nlen,r.ndist,r.distcode,0,r.work,C),r.distbits=C.bits,y){e.msg="invalid distances set",r.mode=30;break}if(r.mode=20,6===t)break e;case 20:r.mode=21;case 21:if(6<=o&&258<=l){e.next_out=i,e.avail_out=l,e.next_in=s,e.avail_in=o,r.hold=c,r.bits=f,L(e,u),i=e.next_out,a=e.output,l=e.avail_out,s=e.next_in,n=e.input,o=e.avail_in,c=r.hold,f=r.bits,12===r.mode&&(r.back=-1);break}for(r.back=0;b=(R=r.lencode[c&(1<<r.lenbits)-1])>>>16&255,v=65535&R,!((g=R>>>24)<=f);){if(0===o)break e;o--,c+=n[s++]<<f,f+=8}if(b&&0==(240&b)){for(E=g,w=b,S=v;b=(R=r.lencode[S+((c&(1<<E+w)-1)>>E)])>>>16&255,v=65535&R,!(E+(g=R>>>24)<=f);){if(0===o)break e;o--,c+=n[s++]<<f,f+=8}c>>>=E,f-=E,r.back+=E}if(c>>>=g,f-=g,r.back+=g,r.length=v,0===b){r.mode=26;break}if(32&b){r.back=-1,r.mode=12;break}if(64&b){e.msg="invalid literal/length code",r.mode=30;break}r.extra=15&b,r.mode=22;case 22:if(r.extra){for(B=r.extra;f<B;){if(0===o)break e;o--,c+=n[s++]<<f,f+=8}r.length+=c&(1<<r.extra)-1,c>>>=r.extra,f-=r.extra,r.back+=r.extra}r.was=r.length,r.mode=23;case 23:for(;b=(R=r.distcode[c&(1<<r.distbits)-1])>>>16&255,v=65535&R,!((g=R>>>24)<=f);){if(0===o)break e;o--,c+=n[s++]<<f,f+=8}if(0==(240&b)){for(E=g,w=b,S=v;b=(R=r.distcode[S+((c&(1<<E+w)-1)>>E)])>>>16&255,v=65535&R,!(E+(g=R>>>24)<=f);){if(0===o)break e;o--,c+=n[s++]<<f,f+=8}c>>>=E,f-=E,r.back+=E}if(c>>>=g,f-=g,r.back+=g,64&b){e.msg="invalid distance code",r.mode=30;break}r.offset=v,r.extra=15&b,r.mode=24;case 24:if(r.extra){for(B=r.extra;f<B;){if(0===o)break e;o--,c+=n[s++]<<f,f+=8}r.offset+=c&(1<<r.extra)-1,c>>>=r.extra,f-=r.extra,r.back+=r.extra}if(r.offset>r.dmax){e.msg="invalid distance too far back",r.mode=30;break}r.mode=25;case 25:if(0===l)break e;if(r.offset>(d=u-l)){if((d=r.offset-d)>r.whave&&r.sane){e.msg="invalid distance too far back",r.mode=30;break}p=d>r.wnext?(d-=r.wnext,r.wsize-d):r.wnext-d,d>r.length&&(d=r.length),m=r.window}else m=a,p=i-r.offset,d=r.length;for(l-=d=l<d?l:d,r.length-=d;a[i++]=m[p++],--d;);0===r.length&&(r.mode=21);break;case 26:if(0===l)break e;a[i++]=r.length,l--,r.mode=21;break;case 27:if(r.wrap){for(;f<32;){if(0===o)break e;o--,c|=n[s++]<<f,f+=8}if(u-=l,e.total_out+=u,r.total+=u,u&&(e.adler=r.check=(r.flags?N:P)(r.check,a,u,i-u)),u=l,(r.flags?c:X(c))!==r.check){e.msg="incorrect data check",r.mode=30;break}f=c=0}r.mode=28;case 28:if(r.wrap&&r.flags){for(;f<32;){if(0===o)break e;o--,c+=n[s++]<<f,f+=8}if(c!==(4294967295&r.total)){e.msg="incorrect length check",r.mode=30;break}f=c=0}r.mode=29;case 29:y=1;break e;case 30:y=-3;break e;case 31:return-4;default:return V}return e.next_out=i,e.avail_out=l,e.next_in=s,e.avail_in=o,r.hold=c,r.bits=f,(r.wsize||u!==e.avail_out&&r.mode<30&&(r.mode<27||4!==t))&&(k=(T=e).output,x=e.next_out,A=u-e.avail_out,null===(I=T.state).window&&(I.wsize=1<<I.wbits,I.wnext=0,I.whave=0,I.window=new D.Buf8(I.wsize)),A>=I.wsize?(D.arraySet(I.window,k,x-I.wsize,I.wsize,0),I.wnext=0,I.whave=I.wsize):(A<(T=I.wsize-I.wnext)&&(T=A),D.arraySet(I.window,k,x-A,T,I.wnext),(A-=T)?(D.arraySet(I.window,k,x-A,A,0),I.wnext=A,I.whave=I.wsize):(I.wnext+=T,I.wnext===I.wsize&&(I.wnext=0),I.whave<I.wsize&&(I.whave+=T)))),h-=e.avail_in,u-=e.avail_out,e.total_in+=h,e.total_out+=u,r.total+=u,r.wrap&&u&&(e.adler=r.check=(r.flags?N:P)(r.check,a,u,e.next_out-u)),e.data_type=r.bits+(r.last?64:0)+(12===r.mode?128:0)+(20===r.mode||15===r.mode?256:0),y=(0==h&&0===u||4===t)&&y===z?-5:y},r.inflateEnd=function(e){if(!e||!e.state)return V;var t=e.state;return t.window&&(t.window=null),e.state=null,z},r.inflateGetHeader=function(e,t){return!e||!e.state||0==(2&(e=e.state).wrap)?V:((e.head=t).done=!1,z)},r.inflateInfo="pako inflate (from Nodeca project)"},{"../utils/common":27,"./adler32":29,"./crc32":31,"./inffast":34,"./inftrees":36}],36:[function(e,t,r){"use strict";var D=e("../utils/common"),P=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],N=[16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78],L=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0],M=[16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64];t.exports=function(e,t,r,n,a,s,i,o){for(var l,c,f,h,u,d,p,m,g,b=o.bits,v=0,E=0,w=0,S=0,_=0,y=0,C=0,B=0,T=0,k=0,x=null,A=0,I=new D.Buf16(16),R=new D.Buf16(16),O=null,F=0,v=0;v<=15;v++)I[v]=0;for(E=0;E<n;E++)I[t[r+E]]++;for(_=b,S=15;1<=S&&0===I[S];S--);if(S<_&&(_=S),0===S)return a[s++]=20971520,a[s++]=20971520,o.bits=1,0;for(w=1;w<S&&0===I[w];w++);for(_<w&&(_=w),v=B=1;v<=15;v++)if(B<<=1,(B-=I[v])<0)return-1;if(0<B&&(0===e||1!==S))return-1;for(R[1]=0,v=1;v<15;v++)R[v+1]=R[v]+I[v];for(E=0;E<n;E++)0!==t[r+E]&&(i[R[t[r+E]]++]=E);if(d=0===e?(x=O=i,19):1===e?(x=P,A-=257,O=N,F-=257,256):(x=L,O=M,-1),v=w,u=s,f=-1,h=(T=1<<(y=_))-1,1===e&&852<T||2===e&&592<T)return 1;for(C=E=k=0;;){for(g=i[E]<d?(m=0,i[E]):i[E]>d?(m=O[F+i[E]],x[A+i[E]]):(m=96,0),l=1<<(p=v-C),w=c=1<<y;a[u+(k>>C)+(c-=l)]=p<<24|m<<16|g|0,0!==c;);for(l=1<<v-1;k&l;)l>>=1;if(0!==l?(k&=l-1,k+=l):k=0,E++,0==--I[v]){if(v===S)break;v=t[r+i[E]]}if(_<v&&(k&h)!==f){for(u+=w,B=1<<(y=v-(C=0===C?_:C));y+C<S&&!((B-=I[y+C])<=0);)y++,B<<=1;if(T+=1<<y,1===e&&852<T||2===e&&592<T)return 1;a[f=k&h]=_<<24|y<<16|u-s|0}}return 0!==k&&(a[u+k]=v-C<<24|64<<16|0),o.bits=_,0}},{"../utils/common":27}],37:[function(e,t,r){"use strict";t.exports={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"}},{}],38:[function(e,t,r){"use strict";var a=e("../utils/common"),o=0,l=1;function n(e){for(var t=e.length;0<=--t;)e[t]=0}var i=29,c=256,f=c+1+i,h=30,u=19,g=2*f+1,b=15,s=16,d=7,p=256,m=16,v=17,E=18,w=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],S=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],_=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],y=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],C=new Array(2*(f+2));n(C);var B=new Array(2*h);n(B);var T=new Array(512);n(T);var k=new Array(256);n(k);var x=new Array(i);n(x);var A=new Array(h);n(A);function I(e,t){this.dyn_tree=e,this.max_code=0,this.stat_desc=t}var R,O,F,D=function(e,t,r,n,a){this.static_tree=e,this.extra_bits=t,this.extra_base=r,this.elems=n,this.max_length=a,this.has_stree=e&&e.length};function P(e){return e<256?T[e]:T[256+(e>>>7)]}function N(e,t){e.pending_buf[e.pending++]=255&t,e.pending_buf[e.pending++]=t>>>8&255}function L(e,t,r){e.bi_valid>s-r?(e.bi_buf|=t<<e.bi_valid&65535,N(e,e.bi_buf),e.bi_buf=t>>s-e.bi_valid,e.bi_valid+=r-s):(e.bi_buf|=t<<e.bi_valid&65535,e.bi_valid+=r)}function M(e,t,r){L(e,r[2*t],r[2*t+1])}function U(e,t){for(var r=0;r|=1&e,e>>>=1,r<<=1,0<--t;);return r>>>1}function H(e,t,r){for(var n,a=new Array(b+1),s=0,i=1;i<=b;i++)a[i]=s=s+r[i-1]<<1;for(n=0;n<=t;n++){var o=e[2*n+1];0!==o&&(e[2*n]=U(a[o]++,o))}}function z(e){for(var t=0;t<f;t++)e.dyn_ltree[2*t]=0;for(t=0;t<h;t++)e.dyn_dtree[2*t]=0;for(t=0;t<u;t++)e.bl_tree[2*t]=0;e.dyn_ltree[2*p]=1,e.opt_len=e.static_len=0,e.last_lit=e.matches=0}function V(e){8<e.bi_valid?N(e,e.bi_buf):0<e.bi_valid&&(e.pending_buf[e.pending++]=e.bi_buf),e.bi_buf=0,e.bi_valid=0}function W(e,t,r,n){var a=2*t,s=2*r;return e[a]<e[s]||e[a]===e[s]&&n[t]<=n[r]}function X(e,t,r){for(var n=e.heap[r],a=r<<1;a<=e.heap_len&&(a<e.heap_len&&W(t,e.heap[a+1],e.heap[a],e.depth)&&a++,!W(t,n,e.heap[a],e.depth));)e.heap[r]=e.heap[a],r=a,a<<=1;e.heap[r]=n}function j(e,t,r){var n,a,s,i,o=0;if(0!==e.last_lit)for(;n=e.pending_buf[e.d_buf+2*o]<<8|e.pending_buf[e.d_buf+2*o+1],a=e.pending_buf[e.l_buf+o],o++,0==n?M(e,a,t):(M(e,(s=k[a])+c+1,t),0!==(i=w[s])&&L(e,a-=x[s],i),M(e,s=P(--n),r),0!==(i=S[s])&&L(e,n-=A[s],i)),o<e.last_lit;);M(e,p,t)}function G(e,t){var r,n,a,s=t.dyn_tree,i=t.stat_desc.static_tree,o=t.stat_desc.has_stree,l=t.stat_desc.elems,c=-1;for(e.heap_len=0,e.heap_max=g,r=0;r<l;r++)0!==s[2*r]?(e.heap[++e.heap_len]=c=r,e.depth[r]=0):s[2*r+1]=0;for(;e.heap_len<2;)s[2*(a=e.heap[++e.heap_len]=c<2?++c:0)]=1,e.depth[a]=0,e.opt_len--,o&&(e.static_len-=i[2*a+1]);for(t.max_code=c,r=e.heap_len>>1;1<=r;r--)X(e,s,r);for(a=l;r=e.heap[1],e.heap[1]=e.heap[e.heap_len--],X(e,s,1),n=e.heap[1],e.heap[--e.heap_max]=r,e.heap[--e.heap_max]=n,s[2*a]=s[2*r]+s[2*n],e.depth[a]=(e.depth[r]>=e.depth[n]?e.depth[r]:e.depth[n])+1,s[2*r+1]=s[2*n+1]=a,e.heap[1]=a++,X(e,s,1),2<=e.heap_len;);e.heap[--e.heap_max]=e.heap[1],function(e,t){for(var r,n,a,s,i,o=t.dyn_tree,l=t.max_code,c=t.stat_desc.static_tree,f=t.stat_desc.has_stree,h=t.stat_desc.extra_bits,u=t.stat_desc.extra_base,d=t.stat_desc.max_length,p=0,m=0;m<=b;m++)e.bl_count[m]=0;for(o[2*e.heap[e.heap_max]+1]=0,r=e.heap_max+1;r<g;r++)d<(m=o[2*o[2*(n=e.heap[r])+1]+1]+1)&&(m=d,p++),o[2*n+1]=m,l<n||(e.bl_count[m]++,s=0,u<=n&&(s=h[n-u]),i=o[2*n],e.opt_len+=i*(m+s),f&&(e.static_len+=i*(c[2*n+1]+s)));if(0!==p){do{for(m=d-1;0===e.bl_count[m];)m--}while(e.bl_count[m]--,e.bl_count[m+1]+=2,e.bl_count[d]--,0<(p-=2));for(m=d;0!==m;m--)for(n=e.bl_count[m];0!==n;)l<(a=e.heap[--r])||(o[2*a+1]!==m&&(e.opt_len+=(m-o[2*a+1])*o[2*a],o[2*a+1]=m),n--)}}(e,t),H(s,c,e.bl_count)}function $(e,t,r){var n,a,s=-1,i=t[1],o=0,l=7,c=4;for(0===i&&(l=138,c=3),t[2*(r+1)+1]=65535,n=0;n<=r;n++)a=i,i=t[2*(n+1)+1],++o<l&&a===i||(o<c?e.bl_tree[2*a]+=o:0!==a?(a!==s&&e.bl_tree[2*a]++,e.bl_tree[2*m]++):o<=10?e.bl_tree[2*v]++:e.bl_tree[2*E]++,s=a,c=(o=0)===i?(l=138,3):a===i?(l=6,3):(l=7,4))}function Y(e,t,r){var n,a,s=-1,i=t[1],o=0,l=7,c=4;for(0===i&&(l=138,c=3),n=0;n<=r;n++)if(a=i,i=t[2*(n+1)+1],!(++o<l&&a===i)){if(o<c)for(;M(e,a,e.bl_tree),0!=--o;);else 0!==a?(a!==s&&(M(e,a,e.bl_tree),o--),M(e,m,e.bl_tree),L(e,o-3,2)):o<=10?(M(e,v,e.bl_tree),L(e,o-3,3)):(M(e,E,e.bl_tree),L(e,o-11,7));s=a,c=(o=0)===i?(l=138,3):a===i?(l=6,3):(l=7,4)}}var K=!1;function Z(e,t,r,n){L(e,0+(n?1:0),3),n=t,t=r,r=!0,V(e=e),r&&(N(e,t),N(e,~t)),a.arraySet(e.pending_buf,e.window,n,t,e.pending),e.pending+=t}r._tr_init=function(e){K||(function(){for(var e,t,r,n=new Array(b+1),a=0,s=0;s<i-1;s++)for(x[s]=a,e=0;e<1<<w[s];e++)k[a++]=s;for(k[a-1]=s,s=r=0;s<16;s++)for(A[s]=r,e=0;e<1<<S[s];e++)T[r++]=s;for(r>>=7;s<h;s++)for(A[s]=r<<7,e=0;e<1<<S[s]-7;e++)T[256+r++]=s;for(t=0;t<=b;t++)n[t]=0;for(e=0;e<=143;)C[2*e+1]=8,e++,n[8]++;for(;e<=255;)C[2*e+1]=9,e++,n[9]++;for(;e<=279;)C[2*e+1]=7,e++,n[7]++;for(;e<=287;)C[2*e+1]=8,e++,n[8]++;for(H(C,f+1,n),e=0;e<h;e++)B[2*e+1]=5,B[2*e]=U(e,5);R=new D(C,w,c+1,f,b),O=new D(B,S,0,h,b),F=new D(new Array(0),_,0,u,d)}(),K=!0),e.l_desc=new I(e.dyn_ltree,R),e.d_desc=new I(e.dyn_dtree,O),e.bl_desc=new I(e.bl_tree,F),e.bi_buf=0,e.bi_valid=0,z(e)},r._tr_stored_block=Z,r._tr_flush_block=function(e,t,r,n){var a,s,i=0;0<e.level?(2===e.strm.data_type&&(e.strm.data_type=function(e){for(var t=4093624447,r=0;r<=31;r++,t>>>=1)if(1&t&&0!==e.dyn_ltree[2*r])return o;if(0!==e.dyn_ltree[18]||0!==e.dyn_ltree[20]||0!==e.dyn_ltree[26])return l;for(r=32;r<c;r++)if(0!==e.dyn_ltree[2*r])return l;return o}(e)),G(e,e.l_desc),G(e,e.d_desc),i=function(e){var t;for($(e,e.dyn_ltree,e.l_desc.max_code),$(e,e.dyn_dtree,e.d_desc.max_code),G(e,e.bl_desc),t=u-1;3<=t&&0===e.bl_tree[2*y[t]+1];t--);return e.opt_len+=3*(t+1)+5+5+4,t}(e),a=e.opt_len+3+7>>>3,(s=e.static_len+3+7>>>3)<=a&&(a=s)):a=s=r+5,r+4<=a&&-1!==t?Z(e,t,r,n):4===e.strategy||s===a?(L(e,2+(n?1:0),3),j(e,C,B)):(L(e,4+(n?1:0),3),function(e,t,r,n){var a;for(L(e,t-257,5),L(e,r-1,5),L(e,n-4,4),a=0;a<n;a++)L(e,e.bl_tree[2*y[a]+1],3);Y(e,e.dyn_ltree,t-1),Y(e,e.dyn_dtree,r-1)}(e,e.l_desc.max_code+1,e.d_desc.max_code+1,i+1),j(e,e.dyn_ltree,e.dyn_dtree)),z(e),n&&V(e)},r._tr_tally=function(e,t,r){return e.pending_buf[e.d_buf+2*e.last_lit]=t>>>8&255,e.pending_buf[e.d_buf+2*e.last_lit+1]=255&t,e.pending_buf[e.l_buf+e.last_lit]=255&r,e.last_lit++,0===t?e.dyn_ltree[2*r]++:(e.matches++,t--,e.dyn_ltree[2*(k[r]+c+1)]++,e.dyn_dtree[2*P(t)]++),e.last_lit===e.lit_bufsize-1},r._tr_align=function(e){L(e,2,3),M(e,p,C),16===(e=e).bi_valid?(N(e,e.bi_buf),e.bi_buf=0,e.bi_valid=0):8<=e.bi_valid&&(e.pending_buf[e.pending++]=255&e.bi_buf,e.bi_buf>>=8,e.bi_valid-=8)}},{"../utils/common":27}],39:[function(e,t,r){"use strict";t.exports=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0}},{}]},{},[9])(9)});var XLSX={};function make_xlsx_lib(n){n.version="0.14.3";var f=1200,a=1252;"undefined"!=typeof module&&"undefined"!=typeof require&&"undefined"==typeof cptable&&("undefined"!=typeof global?global.cptable=void 0:"undefined"!=typeof window&&(window.cptable=void 0));for(var t=[874,932,936,949,950],e=0;e<=8;++e)t.push(1250+e);function l(e){-1!=t.indexOf(e)&&(a=h[0]=e)}var h={0:1252,1:65001,2:65001,77:1e4,128:932,129:949,130:1361,134:936,136:950,161:1253,162:1254,163:1258,177:1255,178:1256,186:1257,204:1251,222:874,238:1250,255:1252,69:6969};var ce=function(e){l(f=e)};function c(){ce(1200),l(1252)}function ee(e){for(var t=[],r=0,n=e.length;r<n;++r)t[r]=e.charCodeAt(r);return t}var te=function(e){var t=e.charCodeAt(0),r=e.charCodeAt(1);return 255==t&&254==r?function(e){for(var t=[],r=0;r<e.length>>1;++r)t[r]=String.fromCharCode(e.charCodeAt(2*r)+(e.charCodeAt(2*r+1)<<8));return t.join("")}(e.slice(2)):254==t&&255==r?function(e){for(var t=[],r=0;r<e.length>>1;++r)t[r]=String.fromCharCode(e.charCodeAt(2*r+1)+(e.charCodeAt(2*r)<<8));return t.join("")}(e.slice(2)):65279==t?e.slice(1):e},u=function(e){return String.fromCharCode(e)};"undefined"!=typeof cptable&&(ce=function(e){f=e},te=function(e){return 255===e.charCodeAt(0)&&254===e.charCodeAt(1)?cptable.utils.decode(1200,ee(e.slice(2))):e},u=function(e){return 1200===f?String.fromCharCode(e):cptable.utils.decode(f,[255&e,e>>8])[0]});var d,fe=null,Y=(d="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",{encode:function(e){for(var t,r,n,a,s,i="",o=0,l=0,c=0;c<e.length;)a=(t=e.charCodeAt(c++))>>2,s=(3&t)<<4|(r=e.charCodeAt(c++))>>4,o=(15&r)<<2|(n=e.charCodeAt(c++))>>6,l=63&n,isNaN(r)?o=l=64:isNaN(n)&&(l=64),i+=d.charAt(a)+d.charAt(s)+d.charAt(o)+d.charAt(l);return i},decode:function(e){var t,r,n,a,s="";e=e.replace(/[^\w\+\/\=]/g,"");for(var i=0;i<e.length;)t=d.indexOf(e.charAt(i++)),r=d.indexOf(e.charAt(i++)),s+=String.fromCharCode(t<<2|r>>4),64!==(n=d.indexOf(e.charAt(i++)))&&(s+=String.fromCharCode((15&r)<<4|n>>2)),64!==(a=d.indexOf(e.charAt(i++)))&&(s+=String.fromCharCode((3&n)<<6|a));return s}}),K="undefined"!=typeof Buffer&&"undefined"!=typeof process&&void 0!==process.versions&&!!process.versions.node,s=function(){};if("undefined"!=typeof Buffer){var r=!Buffer.from;if(!r)try{Buffer.from("foo","utf8")}catch(e){r=!0}s=r?function(e,t){return t?new Buffer(e,t):new Buffer(e)}:Buffer.from.bind(Buffer),Buffer.alloc||(Buffer.alloc=function(e){return new Buffer(e)}),Buffer.allocUnsafe||(Buffer.allocUnsafe=function(e){return new Buffer(e)})}function Z(e){return K?Buffer.alloc(e):new Array(e)}function Q(e){return K?Buffer.allocUnsafe(e):new Array(e)}var J=function(e){return K?s(e,"binary"):e.split("").map(function(e){return 255&e.charCodeAt(0)})};function i(e){if("undefined"==typeof ArrayBuffer)return J(e);for(var t=new ArrayBuffer(e.length),r=new Uint8Array(t),n=0;n!=e.length;++n)r[n]=255&e.charCodeAt(n);return t}function o(e){if(Array.isArray(e))return e.map(ql).join("");for(var t=[],r=0;r<e.length;++r)t[r]=ql(e[r]);return t.join("")}function p(e){if("undefined"==typeof ArrayBuffer)throw new Error("Unsupported");if(e instanceof ArrayBuffer)return p(new Uint8Array(e));for(var t=new Array(e.length),r=0;r<e.length;++r)t[r]=e[r];return t}var he=function(e){return[].concat.apply([],e)},q=/\u0000/g,re=/[\u0001-\u0006]/g,ue={},ne=function(e){function b(e){for(var t="",r=e.length-1;0<=r;)t+=e.charAt(r--);return t}function v(e,t){for(var r="";r.length<t;)r+=e;return r}function B(e,t){e=""+e;return t<=e.length?e:v("0",t-e.length)+e}function E(e,t){e=""+e;return t<=e.length?e:v(" ",t-e.length)+e}function w(e,t){e=""+e;return t<=e.length?e:e+v(" ",t-e.length)}e.version="0.10.2";var a=Math.pow(2,32);function S(e,t){if(a<e||e<-a)return r=e,n=t,r=""+Math.round(r),n<=r.length?r:v("0",n-r.length)+r;var r,n,e=Math.round(e);return(t=t)<=(e=""+(e=e)).length?e:v("0",t-e.length)+e}function T(e,t){return e.length>=7+(t=t||0)&&103==(32|e.charCodeAt(t))&&101==(32|e.charCodeAt(t+1))&&110==(32|e.charCodeAt(t+2))&&101==(32|e.charCodeAt(t+3))&&114==(32|e.charCodeAt(t+4))&&97==(32|e.charCodeAt(t+5))&&108==(32|e.charCodeAt(t+6))}var k=[["Sun","Sunday"],["Mon","Monday"],["Tue","Tuesday"],["Wed","Wednesday"],["Thu","Thursday"],["Fri","Friday"],["Sat","Saturday"]],x=[["J","Jan","January"],["F","Feb","February"],["M","Mar","March"],["A","Apr","April"],["M","May","May"],["J","Jun","June"],["J","Jul","July"],["A","Aug","August"],["S","Sep","September"],["O","Oct","October"],["N","Nov","November"],["D","Dec","December"]];function t(e){e[0]="General",e[1]="0",e[2]="0.00",e[3]="#,##0",e[4]="#,##0.00",e[9]="0%",e[10]="0.00%",e[11]="0.00E+00",e[12]="# ?/?",e[13]="# ??/??",e[14]="m/d/yy",e[15]="d-mmm-yy",e[16]="d-mmm",e[17]="mmm-yy",e[18]="h:mm AM/PM",e[19]="h:mm:ss AM/PM",e[20]="h:mm",e[21]="h:mm:ss",e[22]="m/d/yy h:mm",e[37]="#,##0 ;(#,##0)",e[38]="#,##0 ;[Red](#,##0)",e[39]="#,##0.00;(#,##0.00)",e[40]="#,##0.00;[Red](#,##0.00)",e[45]="mm:ss",e[46]="[h]:mm:ss",e[47]="mmss.0",e[48]="##0.0E+0",e[49]="@",e[56]='"上午/下午 "hh"時"mm"分"ss"秒 "',e[65535]="General"}var s={};function _(e,t,r){var n,a=e<0?-1:1,s=e*a,i=0,o=1,l=0,c=1,f=0,h=0;for(Math.floor(s);f<t&&(l=(n=Math.floor(s))*o+i,h=n*f+c,!(s-n<5e-8));)s=1/(s-n),i=o,o=l,c=f,f=h;if(t<h&&(l=t<f?(h=c,i):(h=f,o)),!r)return[0,a*l,h];r=Math.floor(a*l/h);return[r,a*l-r*h,h]}function A(e,t,r){if(2958465<e||e<0)return null;var n=0|e,a=Math.floor(86400*(e-n)),s=0,i=[],e={D:n,T:a,u:86400*(e-n)-a,y:0,m:0,d:0,H:0,M:0,S:0,q:0};return Math.abs(e.u)<1e-6&&(e.u=0),t&&t.date1904&&(n+=1462),.9999<e.u&&(e.u=0,86400==++a&&(e.T=a=0,++n,++e.D)),60===n?(i=r?[1317,10,29]:[1900,2,29],s=3):0===n?(i=r?[1317,8,29]:[1900,1,0],s=6):(60<n&&--n,(t=new Date(1900,0,1)).setDate(t.getDate()+n-1),i=[t.getFullYear(),t.getMonth()+1,t.getDate()],s=t.getDay(),n<60&&(s=(s+6)%7),r&&(s=0)),e.y=i[0],e.m=i[1],e.d=i[2],e.S=a%60,a=Math.floor(a/60),e.M=a%60,a=Math.floor(a/60),e.H=a,e.q=s,e}t(s),e.parse_date_code=A;var n=new Date(1899,11,31,0,0,0),i=n.getTime(),o=new Date(1900,2,1,0,0,0);function l(e,t){var r=e.getTime();return t?r-=1262304e5:o<=e&&(r+=864e5),(r-(i+6e4*(e.getTimezoneOffset()-n.getTimezoneOffset())))/864e5}function r(e){return e.toString(10)}e._general_int=r;var c,f,h,u,d,p=(c=/\.(\d*[1-9])0+$/,f=/\.0*$/,h=/\.(\d*[1-9])0+/,u=/\.0*[Ee]/,d=/(E[+-])(\d)$/,function(e){var t,r,n,a=Math.floor(Math.log(Math.abs(e))*Math.LOG10E),s=-4<=a&&a<=-1?e.toPrecision(10+a):Math.abs(a)<=9?(r=(t=e)<0?12:11,(n=m(t.toFixed(12))).length<=r||(n=t.toPrecision(10)).length<=r?n:t.toExponential(5)):10===a?e.toFixed(10).substr(0,12):s=(s=(e=e).toFixed(11).replace(c,".$1")).length>(e<0?12:11)?e.toPrecision(6):s;return m(function(e){for(var t=0;t!=e.length;++t)if(101==(32|e.charCodeAt(t)))return e.replace(h,".$1").replace(u,"E").replace("e","E").replace(d,"$10$2");return e}(s))});function m(e){return-1<e.indexOf(".")?e.replace(f,"").replace(c,".$1"):e}function I(e,t){switch(typeof e){case"string":return e;case"boolean":return e?"TRUE":"FALSE";case"number":return((0|e)===e?r:p)(e);case"undefined":return"";case"object":if(null==e)return"";if(e instanceof Date)return $(14,l(e,t&&t.date1904),t)}throw new Error("unsupported value in General format: "+e)}function y(e){if(e.length<=3)return e;for(var t=e.length%3,r=e.substr(0,t);t!=e.length;t+=3)r+=(0<r.length?",":"")+e.substr(t,3);return r}e._general_num=p,e._general=I;var C,R,O,F,D,P=(C=/%/g,R=/# (\?+)( ?)\/( ?)(\d+)/,O=/^#*0*\.([0#]+)/,F=/\).*[0#]/,D=/\(###\) ###\\?-####/,function(e,t,r){return((0|r)===r?g:U)(e,t,r)});function N(e){for(var t,r="",n=0;n!=e.length;++n)switch(t=e.charCodeAt(n)){case 35:break;case 63:r+=" ";break;case 48:r+="0";break;default:r+=String.fromCharCode(t)}return r}function L(e,t){t=Math.pow(10,t);return""+Math.round(e*t)/t}function M(e,t){return t<(""+Math.round((e-Math.floor(e))*Math.pow(10,t))).length?0:Math.round((e-Math.floor(e))*Math.pow(10,t))}function U(e,t,r){if(40===e.charCodeAt(0)&&!t.match(F)){var n=t.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return 0<=r?U("n",n,r):"("+U("n",n,-r)+")"}if(44===t.charCodeAt(t.length-1))return function(e,t,r){for(var n=t.length-1;44===t.charCodeAt(n-1);)--n;return P(e,t.substr(0,n),r/Math.pow(10,3*(t.length-n)))}(e,t,r);if(-1!==t.indexOf("%"))return s=e,o=r,i=(l=t).replace(C,""),l=l.length-i.length,P(s,i,o*Math.pow(10,2*l))+v("%",l);if(-1!==t.indexOf("E"))return function e(t,r){var n,a=t.indexOf("E")-t.indexOf(".")-1;if(t.match(/^#+0.0E\+0$/)){if(0==r)return"0.0E+0";if(r<0)return"-"+e(t,-r);var s=t.indexOf(".");-1===s&&(s=t.indexOf("E"));var i=Math.floor(Math.log(r)*Math.LOG10E)%s;if(i<0&&(i+=s),-1===(n=(r/Math.pow(10,i)).toPrecision(1+a+(s+i)%s)).indexOf("e")){var o=Math.floor(Math.log(r)*Math.LOG10E);for(-1===n.indexOf(".")?n=n.charAt(0)+"."+n.substr(1)+"E+"+(o-n.length+i):n+="E+"+(o-i);"0."===n.substr(0,2);)n=(n=n.charAt(0)+n.substr(2,s)+"."+n.substr(2+s)).replace(/^0+([1-9])/,"$1").replace(/^0+\./,"0.");n=n.replace(/\+-/,"-")}n=n.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(e,t,r,n){return t+r+n.substr(0,(s+i)%s)+"."+n.substr(i)+"E"})}else n=r.toExponential(a);return t.match(/E\+00$/)&&n.match(/e[+-]\d$/)&&(n=n.substr(0,n.length-1)+"0"+n.charAt(n.length-1)),(n=t.match(/E\-/)&&n.match(/e\+/)?n.replace(/e\+/,"e"):n).replace("e","E")}(t,r);if(36===t.charCodeAt(0))return"$"+U(e,t.substr(" "==t.charAt(1)?2:1),r);var a,s,i,o,l,c=Math.abs(r),f=r<0?"-":"";if(t.match(/^00+$/))return f+S(c,t.length);if(t.match(/^[#?]+$/))return(u="0"===(u=S(r,0))?"":u).length>t.length?u:N(t.substr(0,t.length-u.length))+u;if(a=t.match(R))return n=a,s=c,i=f,o=parseInt(n[4],10),l=Math.round(s*o),s=Math.floor(l/o),o,i+(0===s?"":""+s)+" "+(0==(l-=s*o)?v(" ",n[1].length+1+n[4].length):E(l,n[1].length)+n[2]+"/"+n[3]+B(o,n[4].length));if(t.match(/^#+0+$/))return f+S(c,t.length-t.indexOf("0"));if(a=t.match(O))return u=L(r,a[1].length).replace(/^([^\.]+)$/,"$1."+N(a[1])).replace(/\.$/,"."+N(a[1])).replace(/\.(\d*)$/,function(e,t){return"."+t+v("0",N(a[1]).length-t.length)}),-1!==t.indexOf("0.")?u:u.replace(/^0\./,".");if(t=t.replace(/^#+([0.])/,"$1"),a=t.match(/^(0*)\.(#*)$/))return f+L(c,a[2].length).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,a[1].length?"0.":".");if(a=t.match(/^#{1,3},##0(\.?)$/))return f+y(S(c,0));if(a=t.match(/^#,##0\.([#0]*0)$/))return r<0?"-"+U(e,t,-r):y(""+(Math.floor(r)+((h=a[1].length)<(""+Math.round((r-Math.floor(r))*Math.pow(10,h))).length?1:0)))+"."+B(M(r,a[1].length),a[1].length);if(a=t.match(/^#,#*,#0/))return U(e,t.replace(/^#,#*,/,""),r);if(a=t.match(/^([0#]+)(\\?-([0#]+))+$/))return u=b(U(e,t.replace(/[\\-]/g,""),r)),d=0,b(b(t.replace(/\\/g,"")).replace(/[0#]/g,function(e){return d<u.length?u.charAt(d++):"0"===e?"0":""}));if(t.match(D))return"("+(u=U(e,"##########",r)).substr(0,3)+") "+u.substr(3,3)+"-"+u.substr(6);var h="";if(a=t.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return d=Math.min(a[4].length,7),p=_(c,Math.pow(10,d)-1,!1),u=f," "==(h=P("n",a[1],p[1])).charAt(h.length-1)&&(h=h.substr(0,h.length-1)+"0"),u+=h+a[2]+"/"+a[3],(h=w(p[2],d)).length<a[4].length&&(h=N(a[4].substr(a[4].length-h.length))+h),u+=h;if(a=t.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return d=Math.min(Math.max(a[1].length,a[4].length),7),f+((p=_(c,Math.pow(10,d)-1,!0))[0]||(p[1]?"":"0"))+" "+(p[1]?E(p[1],d)+a[2]+"/"+a[3]+w(p[2],d):v(" ",2*d+1+a[2].length+a[3].length));if(a=t.match(/^[#0?]+$/))return u=S(r,0),t.length<=u.length?u:N(t.substr(0,t.length-u.length))+u;if(a=t.match(/^([#0?]+)\.([#0]+)$/)){var u,d=(u=""+r.toFixed(Math.min(a[2].length,10)).replace(/([^0])0+$/,"$1")).indexOf("."),p=t.indexOf(".")-d,m=t.length-u.length-p;return N(t.substr(0,p)+u+t.substr(t.length-m))}if(a=t.match(/^00,000\.([#0]*0)$/))return d=M(r,a[1].length),r<0?"-"+U(e,t,-r):y((m=r)<2147483647&&-2147483648<m?""+(0<=m?0|m:m-1|0):""+Math.floor(m)).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(e){return"00,"+(e.length<3?B(0,3-e.length):"")+e})+"."+B(d,a[1].length);switch(t){case"###,##0.00":return U(e,"#,##0.00",r);case"###,###":case"##,###":case"#,###":var g=y(S(c,0));return"0"!==g?f+g:"";case"###,###.00":return U(e,"###,##0.00",r).replace(/^0\./,".");case"#,###.00":return U(e,"#,##0.00",r).replace(/^0\./,".")}throw new Error("unsupported format |"+t+"|")}function g(e,t,r){if(40===e.charCodeAt(0)&&!t.match(F)){var n=t.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return 0<=r?g("n",n,r):"("+g("n",n,-r)+")"}if(44===t.charCodeAt(t.length-1))return function(e,t,r){for(var n=t.length-1;44===t.charCodeAt(n-1);)--n;return P(e,t.substr(0,n),r/Math.pow(10,3*(t.length-n)))}(e,t,r);if(-1!==t.indexOf("%"))return a=e,i=r,n=(s=t).replace(C,""),s=s.length-n.length,P(a,n,i*Math.pow(10,2*s))+v("%",s);var a,s,i;if(-1!==t.indexOf("E"))return function e(t,r){var n,a=t.indexOf("E")-t.indexOf(".")-1;if(t.match(/^#+0.0E\+0$/)){if(0==r)return"0.0E+0";if(r<0)return"-"+e(t,-r);var s=t.indexOf(".");-1===s&&(s=t.indexOf("E"));var i,o=Math.floor(Math.log(r)*Math.LOG10E)%s;o<0&&(o+=s),(n=(r/Math.pow(10,o)).toPrecision(1+a+(s+o)%s)).match(/[Ee]/)||(i=Math.floor(Math.log(r)*Math.LOG10E),-1===n.indexOf(".")?n=n.charAt(0)+"."+n.substr(1)+"E+"+(i-n.length+o):n+="E+"+(i-o),n=n.replace(/\+-/,"-")),n=n.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,function(e,t,r,n){return t+r+n.substr(0,(s+o)%s)+"."+n.substr(o)+"E"})}else n=r.toExponential(a);return t.match(/E\+00$/)&&n.match(/e[+-]\d$/)&&(n=n.substr(0,n.length-1)+"0"+n.charAt(n.length-1)),(n=t.match(/E\-/)&&n.match(/e\+/)?n.replace(/e\+/,"e"):n).replace("e","E")}(t,r);if(36===t.charCodeAt(0))return"$"+g(e,t.substr(" "==t.charAt(1)?2:1),r);var o,l=Math.abs(r),c=r<0?"-":"";if(t.match(/^00+$/))return c+B(l,t.length);if(t.match(/^[#?]+$/))return(h=0===r?"":""+r).length>t.length?h:N(t.substr(0,t.length-h.length))+h;if(o=t.match(R))return c+(0===(f=l)?"":""+f)+v(" ",(f=o)[1].length+2+f[4].length);if(t.match(/^#+0+$/))return c+B(l,t.length-t.indexOf("0"));if(o=t.match(O))return h=(h=(""+r).replace(/^([^\.]+)$/,"$1."+N(o[1])).replace(/\.$/,"."+N(o[1]))).replace(/\.(\d*)$/,function(e,t){return"."+t+v("0",N(o[1]).length-t.length)}),-1!==t.indexOf("0.")?h:h.replace(/^0\./,".");if(t=t.replace(/^#+([0.])/,"$1"),o=t.match(/^(0*)\.(#*)$/))return c+(""+l).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,o[1].length?"0.":".");if(o=t.match(/^#{1,3},##0(\.?)$/))return c+y(""+l);if(o=t.match(/^#,##0\.([#0]*0)$/))return r<0?"-"+g(e,t,-r):y(""+r)+"."+v("0",o[1].length);if(o=t.match(/^#,#*,#0/))return g(e,t.replace(/^#,#*,/,""),r);if(o=t.match(/^([0#]+)(\\?-([0#]+))+$/))return h=b(g(e,t.replace(/[\\-]/g,""),r)),u=0,b(b(t.replace(/\\/g,"")).replace(/[0#]/g,function(e){return u<h.length?h.charAt(u++):"0"===e?"0":""}));if(t.match(D))return"("+(h=g(e,"##########",r)).substr(0,3)+") "+h.substr(3,3)+"-"+h.substr(6);var f="";if(o=t.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return u=Math.min(o[4].length,7),d=_(l,Math.pow(10,u)-1,!1),h=c," "==(f=P("n",o[1],d[1])).charAt(f.length-1)&&(f=f.substr(0,f.length-1)+"0"),h+=f+o[2]+"/"+o[3],(f=w(d[2],u)).length<o[4].length&&(f=N(o[4].substr(o[4].length-f.length))+f),h+=f;if(o=t.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return u=Math.min(Math.max(o[1].length,o[4].length),7),c+((d=_(l,Math.pow(10,u)-1,!0))[0]||(d[1]?"":"0"))+" "+(d[1]?E(d[1],u)+o[2]+"/"+o[3]+w(d[2],u):v(" ",2*u+1+o[2].length+o[3].length));if(o=t.match(/^[#0?]+$/))return h=""+r,t.length<=h.length?h:N(t.substr(0,t.length-h.length))+h;if(o=t.match(/^([#0]+)\.([#0]+)$/)){var h,u=(h=""+r.toFixed(Math.min(o[2].length,10)).replace(/([^0])0+$/,"$1")).indexOf("."),f=t.indexOf(".")-u,d=t.length-h.length-f;return N(t.substr(0,f)+h+t.substr(t.length-d))}if(o=t.match(/^00,000\.([#0]*0)$/))return r<0?"-"+g(e,t,-r):y(""+r).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,function(e){return"00,"+(e.length<3?B(0,3-e.length):"")+e})+"."+B(0,o[1].length);switch(t){case"###,###":case"##,###":case"#,###":var p=y(""+l);return"0"!==p?c+p:"";default:if(t.match(/\.[0#?]*$/))return g(e,t.slice(0,t.lastIndexOf(".")),r)+N(t.slice(t.lastIndexOf(".")))}throw new Error("unsupported format |"+t+"|")}function H(e){for(var t=[],r=!1,n=0,a=0;n<e.length;++n)switch(e.charCodeAt(n)){case 34:r=!r;break;case 95:case 42:case 92:++n;break;case 59:t[t.length]=e.substr(a,n-a),a=n+1}if(t[t.length]=e.substr(a),!0===r)throw new Error("Format |"+e+"| unterminated string ");return t}e._split=H;var z=/\[[HhMmSs]*\]/;function V(e){for(var t=0,r="",n="";t<e.length;)switch(r=e.charAt(t)){case"G":T(e,t)&&(t+=6),t++;break;case'"':for(;34!==e.charCodeAt(++t)&&t<e.length;)++t;++t;break;case"\\":case"_":t+=2;break;case"@":++t;break;case"B":case"b":if("1"===e.charAt(t+1)||"2"===e.charAt(t+1))return!0;case"M":case"D":case"Y":case"H":case"S":case"E":case"m":case"d":case"y":case"h":case"s":case"e":case"g":return!0;case"A":case"a":if("A/P"===e.substr(t,3).toUpperCase())return!0;if("AM/PM"===e.substr(t,5).toUpperCase())return!0;++t;break;case"[":for(n=r;"]"!==e.charAt(t++)&&t<e.length;)n+=e.charAt(t);if(n.match(z))return!0;break;case".":case"0":case"#":for(;t<e.length&&(-1<"0#?.,E+-%".indexOf(r=e.charAt(++t))||"\\"==r&&"-"==e.charAt(t+1)&&-1<"0#".indexOf(e.charAt(t+2))););break;case"?":for(;e.charAt(++t)===r;);break;case"*":++t," "!=e.charAt(t)&&"*"!=e.charAt(t)||++t;break;case"(":case")":++t;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(;t<e.length&&-1<"0123456789".indexOf(e.charAt(++t)););break;default:++t}return!1}function W(e,t,r,n){for(var a,s,i,o=[],l="",c=0,f="",h="t",u="H";c<e.length;)switch(f=e.charAt(c)){case"G":if(!T(e,c))throw new Error("unrecognized character "+f+" in "+e);o[o.length]={t:"G",v:"General"},c+=7;break;case'"':for(l="";34!==(i=e.charCodeAt(++c))&&c<e.length;)l+=String.fromCharCode(i);o[o.length]={t:"t",v:l},++c;break;case"\\":var d=e.charAt(++c);o[o.length]={t:"("===d||")"===d?d:"t",v:d},++c;break;case"_":o[o.length]={t:"t",v:" "},c+=2;break;case"@":o[o.length]={t:"T",v:t},++c;break;case"B":case"b":if("1"===e.charAt(c+1)||"2"===e.charAt(c+1)){if(null==a&&null==(a=A(t,r,"2"===e.charAt(c+1))))return"";o[o.length]={t:"X",v:e.substr(c,2)},h=f,c+=2;break}case"M":case"D":case"Y":case"H":case"S":case"E":f=f.toLowerCase();case"m":case"d":case"y":case"h":case"s":case"e":case"g":if(t<0)return"";if(null==a&&null==(a=A(t,r)))return"";for(l=f;++c<e.length&&e.charAt(c).toLowerCase()===f;)l+=f;"h"===(f="m"===f&&"h"===h.toLowerCase()?"M":f)&&(f=u),o[o.length]={t:f,v:l},h=f;break;case"A":case"a":d={t:f,v:f};if(null==a&&(a=A(t,r)),"A/P"===e.substr(c,3).toUpperCase()?(null!=a&&(d.v=12<=a.H?"P":"A"),d.t="T",u="h",c+=3):"AM/PM"===e.substr(c,5).toUpperCase()?(null!=a&&(d.v=12<=a.H?"PM":"AM"),d.t="T",c+=5,u="h"):(d.t="t",++c),null==a&&"T"===d.t)return"";o[o.length]=d,h=f;break;case"[":for(l=f;"]"!==e.charAt(c++)&&c<e.length;)l+=e.charAt(c);if("]"!==l.slice(-1))throw'unterminated "[" block: |'+l+"|";if(l.match(z)){if(null==a&&null==(a=A(t,r)))return"";o[o.length]={t:"Z",v:l.toLowerCase()},h=l.charAt(1)}else-1<l.indexOf("$")&&(l=(l.match(/\$([^-\[\]]*)/)||[])[1]||"$",V(e)||(o[o.length]={t:"t",v:l}));break;case".":if(null!=a){for(l=f;++c<e.length&&"0"===(f=e.charAt(c));)l+=f;o[o.length]={t:"s",v:l};break}case"0":case"#":for(l=f;++c<e.length&&-1<"0#?.,E+-%".indexOf(f=e.charAt(c))||"\\"==f&&"-"==e.charAt(c+1)&&c<e.length-2&&-1<"0#".indexOf(e.charAt(c+2));)l+=f;o[o.length]={t:"n",v:l};break;case"?":for(l=f;e.charAt(++c)===f;)l+=f;o[o.length]={t:f,v:l},h=f;break;case"*":++c," "!=e.charAt(c)&&"*"!=e.charAt(c)||++c;break;case"(":case")":o[o.length]={t:1===n?"t":f,v:f},++c;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(l=f;c<e.length&&-1<"0123456789".indexOf(e.charAt(++c));)l+=e.charAt(c);o[o.length]={t:"D",v:l};break;case" ":o[o.length]={t:f,v:f},++c;break;default:if(-1===",$-+/():!^&'~{}<>=€acfijklopqrtuvwxzP".indexOf(f))throw new Error("unrecognized character "+f+" in "+e);o[o.length]={t:"t",v:f},++c}var p,m=0,g=0;for(c=o.length-1,h="t";0<=c;--c)switch(o[c].t){case"h":case"H":o[c].t=u,h="h",m<1&&(m=1);break;case"s":(p=o[c].v.match(/\.0+$/))&&(g=Math.max(g,p[0].length-1)),m<3&&(m=3);case"d":case"y":case"M":case"e":h=o[c].t;break;case"m":"s"===h&&(o[c].t="M",m<2&&(m=2));break;case"X":break;case"Z":(m=(m=m<1&&o[c].v.match(/[Hh]/)?1:m)<2&&o[c].v.match(/[Mm]/)?2:m)<3&&o[c].v.match(/[Ss]/)&&(m=3)}switch(m){case 0:break;case 1:.5<=a.u&&(a.u=0,++a.S),60<=a.S&&(a.S=0,++a.M),60<=a.M&&(a.M=0,++a.H);break;case 2:.5<=a.u&&(a.u=0,++a.S),60<=a.S&&(a.S=0,++a.M)}var b="";for(c=0;c<o.length;++c)switch(o[c].t){case"t":case"T":case" ":case"D":break;case"X":o[c].v="",o[c].t=";";break;case"d":case"m":case"y":case"h":case"H":case"M":case"s":case"e":case"b":case"Z":o[c].v=function(e,t,r,n){var a,s="",i=0,o=0,l=r.y,c=0;switch(e){case 98:l=r.y+543;case 121:switch(t.length){case 1:case 2:a=l%100,c=2;break;default:a=l%1e4,c=4}break;case 109:switch(t.length){case 1:case 2:a=r.m,c=t.length;break;case 3:return x[r.m-1][1];case 5:return x[r.m-1][0];default:return x[r.m-1][2]}break;case 100:switch(t.length){case 1:case 2:a=r.d,c=t.length;break;case 3:return k[r.q][0];default:return k[r.q][1]}break;case 104:switch(t.length){case 1:case 2:a=1+(r.H+11)%12,c=t.length;break;default:throw"bad hour format: "+t}break;case 72:switch(t.length){case 1:case 2:a=r.H,c=t.length;break;default:throw"bad hour format: "+t}break;case 77:switch(t.length){case 1:case 2:a=r.M,c=t.length;break;default:throw"bad minute format: "+t}break;case 115:if("s"!=t&&"ss"!=t&&".0"!=t&&".00"!=t&&".000"!=t)throw"bad second format: "+t;return 0!==r.u||"s"!=t&&"ss"!=t?(60*(o=2<=n?3===n?1e3:100:1===n?10:1)<=(i=Math.round(o*(r.S+r.u)))&&(i=0),"s"===t?0===i?"0":""+i/o:(s=B(i,2+n),"ss"===t?s.substr(0,2):"."+s.substr(2,t.length-1))):B(r.S,t.length);case 90:switch(t){case"[h]":case"[hh]":a=24*r.D+r.H;break;case"[m]":case"[mm]":a=60*(24*r.D+r.H)+r.M;break;case"[s]":case"[ss]":a=60*(60*(24*r.D+r.H)+r.M)+Math.round(r.S+r.u);break;default:throw"bad abstime format: "+t}c=3===t.length?1:2;break;case 101:a=l,c=1}return 0<c?B(a,c):""}(o[c].t.charCodeAt(0),o[c].v,a,g),o[c].t="t";break;case"n":case"(":case"?":for(S=c+1;null!=o[S]&&("?"===(f=o[S].t)||"D"===f||(" "===f||"t"===f)&&null!=o[S+1]&&("?"===o[S+1].t||"t"===o[S+1].t&&"/"===o[S+1].v)||"("===o[c].t&&(" "===f||"n"===f||")"===f)||"t"===f&&("/"===o[S].v||" "===o[S].v&&null!=o[S+1]&&"?"==o[S+1].t));)o[c].v+=o[S].v,o[S]={v:"",t:";"},++S;b+=o[c].v,c=S-1;break;case"G":o[c].t="t",o[c].v=I(t,r)}var v,E,w="";if(0<b.length){40==b.charCodeAt(0)?(v=t<0&&45===b.charCodeAt(0)?-t:t,E=P("(",b,v)):(E=P("n",b,v=t<0&&1<n?-t:t),v<0&&o[0]&&"t"==o[0].t&&(E=E.substr(1),o[0].v="-"+o[0].v));for(var S=E.length-1,_=o.length,c=0;c<o.length;++c)if(null!=o[c]&&"t"!=o[c].t&&-1<o[c].v.indexOf(".")){_=c;break}var y=o.length;if(_===o.length&&-1===E.indexOf("E")){for(c=o.length-1;0<=c;--c)null!=o[c]&&-1!=="n?(".indexOf(o[c].t)&&(S>=o[c].v.length-1?(S-=o[c].v.length,o[c].v=E.substr(S+1,o[c].v.length)):S<0?o[c].v="":(o[c].v=E.substr(0,S+1),S=-1),o[c].t="t",y=c);0<=S&&y<o.length&&(o[y].v=E.substr(0,S+1)+o[y].v)}else if(_!==o.length&&-1===E.indexOf("E")){for(S=E.indexOf(".")-1,c=_;0<=c;--c)if(null!=o[c]&&-1!=="n?(".indexOf(o[c].t)){for(s=-1<o[c].v.indexOf(".")&&c===_?o[c].v.indexOf(".")-1:o[c].v.length-1,w=o[c].v.substr(s+1);0<=s;--s)0<=S&&("0"===o[c].v.charAt(s)||"#"===o[c].v.charAt(s))&&(w=E.charAt(S--)+w);o[c].v=w,o[c].t="t",y=c}for(0<=S&&y<o.length&&(o[y].v=E.substr(0,S+1)+o[y].v),S=E.indexOf(".")+1,c=_;c<o.length;++c)if(null!=o[c]&&(-1!=="n?(".indexOf(o[c].t)||c===_)){for(s=-1<o[c].v.indexOf(".")&&c===_?o[c].v.indexOf(".")+1:0,w=o[c].v.substr(0,s);s<o[c].v.length;++s)S<E.length&&(w+=E.charAt(S++));o[c].v=w,o[c].t="t",y=c}}}for(c=0;c<o.length;++c)null!=o[c]&&-1<"n(?".indexOf(o[c].t)&&(v=1<n&&t<0&&0<c&&"-"===o[c-1].v?-t:t,o[c].v=P(o[c].t,o[c].v,v),o[c].t="t");var C="";for(c=0;c!==o.length;++c)null!=o[c]&&(C+=o[c].v);return C}e.is_date=V,e._eval=W;var X=/\[[=<>]/,j=/\[(=|>[=]?|<[>=]?)(-?\d+(?:\.\d*)?)\]/;function G(e,t){if(null!=t){var r=parseFloat(t[2]);switch(t[1]){case"=":if(e==r)return 1;break;case">":if(r<e)return 1;break;case"<":if(e<r)return 1;break;case"<>":if(e!=r)return 1;break;case">=":if(r<=e)return 1;break;case"<=":if(e<=r)return 1}}}function $(e,t,r){null==r&&(r={});var n="";switch(typeof e){case"string":n="m/d/yy"==e&&r.dateNF?r.dateNF:e;break;case"number":n=14==e&&r.dateNF?r.dateNF:(null!=r.table?r.table:s)[e]}if(T(n,0))return I(t,r);var a=function(e,t){var r=H(e),n=r.length,a=r[n-1].indexOf("@");if(n<4&&-1<a&&--n,4<r.length)throw new Error("cannot find right format for |"+r.join("|")+"|");if("number"!=typeof t)return[4,4===r.length||-1<a?r[r.length-1]:"@"];switch(r.length){case 1:r=-1<a?["General","General","General",r[0]]:[r[0],r[0],r[0],"@"];break;case 2:r=-1<a?[r[0],r[0],r[0],r[1]]:[r[0],r[1],r[0],"@"];break;case 3:r=-1<a?[r[0],r[1],r[0],r[2]]:[r[0],r[1],r[2],"@"]}var s=0<t?r[0]:t<0?r[1]:r[2];return-1===r[0].indexOf("[")&&-1===r[1].indexOf("[")||null==r[0].match(X)&&null==r[1].match(X)?[n,s]:(e=r[0].match(j),s=r[1].match(j),G(t,e)?[n,r[0]]:G(t,s)?[n,r[1]]:[n,r[null!=e&&null!=s?2:1]])}(n,t=t instanceof Date?l(t,r.date1904):t);if(T(a[1]))return I(t,r);if(!0===t)t="TRUE";else if(!1===t)t="FALSE";else if(""===t||null==t)return"";return W(a[1],t,r,a[0])}function Y(e,t){if("number"!=typeof t){t=+t||-1;for(var r=0;r<392;++r)if(null!=s[r]){if(s[r]==e){t=r;break}}else t<0&&(t=r);t<0&&(t=391)}return s[t]=e,t}e.load=Y,e._table=s,e.get_table=function(){return s},e.load_table=function(e){for(var t=0;392!=t;++t)void 0!==e[t]&&Y(e[t],t)},e.init_table=t,e.format=$};ne(ue);var ae={"General Number":"General","General Date":ue._table[22],"Long Date":"dddd, mmmm dd, yyyy","Medium Date":ue._table[15],"Short Date":ue._table[14],"Long Time":ue._table[19],"Medium Time":ue._table[18],"Short Time":ue._table[20],Currency:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',Fixed:ue._table[2],Standard:ue._table[4],Percent:ue._table[10],Scientific:ue._table[11],"Yes/No":'"Yes";"Yes";"No";@',"True/False":'"True";"True";"False";@',"On/Off":'"Yes";"Yes";"No";@'},m={5:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',23:"General",24:"General",25:"General",26:"General",27:"m/d/yy",28:"m/d/yy",29:"m/d/yy",30:"m/d/yy",31:"m/d/yy",32:"h:mm:ss",33:"h:mm:ss",34:"h:mm:ss",35:"h:mm:ss",36:"m/d/yy",41:'_(* #,##0_);_(* (#,##0);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* (#,##0);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* (#,##0.00);_("$"* "-"??_);_(@_)',50:"m/d/yy",51:"m/d/yy",52:"m/d/yy",53:"m/d/yy",54:"m/d/yy",55:"m/d/yy",56:"m/d/yy",57:"m/d/yy",58:"m/d/yy",59:"0",60:"0.00",61:"#,##0",62:"#,##0.00",63:'"$"#,##0_);\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',67:"0%",68:"0.00%",69:"# ?/?",70:"# ??/??",71:"m/d/yy",72:"m/d/yy",73:"d-mmm-yy",74:"d-mmm",75:"mmm-yy",76:"h:mm",77:"h:mm:ss",78:"m/d/yy h:mm",79:"mm:ss",80:"[h]:mm:ss",81:"mmss.0"},g=/[dD]+|[mM]+|[yYeE]+|[Hh]+|[Ss]+/g;var se;!function(e){function t(){var e=0,t=new Array(256);for(var r=0;r!=256;++r){e=r;e=e&1?-306674912^e>>>1:e>>>1;e=e&1?-306674912^e>>>1:e>>>1;e=e&1?-306674912^e>>>1:e>>>1;e=e&1?-306674912^e>>>1:e>>>1;e=e&1?-306674912^e>>>1:e>>>1;e=e&1?-306674912^e>>>1:e>>>1;e=e&1?-306674912^e>>>1:e>>>1;e=e&1?-306674912^e>>>1:e>>>1;t[r]=e}return typeof Int32Array!=="undefined"?new Int32Array(t):t}e.version="1.2.0";var o=t();function r(e,t){var r=t^-1,n=e.length-1;for(var a=0;a<n;){r=r>>>8^o[(r^e.charCodeAt(a++))&255];r=r>>>8^o[(r^e.charCodeAt(a++))&255]}if(a===n)r=r>>>8^o[(r^e.charCodeAt(a))&255];return r^-1}function n(e,t){if(e.length>1e4)return s(e,t);var r=t^-1,n=e.length-3;for(var a=0;a<n;){r=r>>>8^o[(r^e[a++])&255];r=r>>>8^o[(r^e[a++])&255];r=r>>>8^o[(r^e[a++])&255];r=r>>>8^o[(r^e[a++])&255]}while(a<n+3)r=r>>>8^o[(r^e[a++])&255];return r^-1}function s(e,t){var r=t^-1,n=e.length-7;for(var a=0;a<n;){r=r>>>8^o[(r^e[a++])&255];r=r>>>8^o[(r^e[a++])&255];r=r>>>8^o[(r^e[a++])&255];r=r>>>8^o[(r^e[a++])&255];r=r>>>8^o[(r^e[a++])&255];r=r>>>8^o[(r^e[a++])&255];r=r>>>8^o[(r^e[a++])&255];r=r>>>8^o[(r^e[a++])&255]}while(a<n+7)r=r>>>8^o[(r^e[a++])&255];return r^-1}function a(e,t){var r=t^-1;for(var n=0,a=e.length,s,i;n<a;){s=e.charCodeAt(n++);if(s<128)r=r>>>8^o[(r^s)&255];else if(s<2048){r=r>>>8^o[(r^(192|s>>6&31))&255];r=r>>>8^o[(r^(128|s&63))&255]}else if(s>=55296&&s<57344){s=(s&1023)+64;i=e.charCodeAt(n++)&1023;r=r>>>8^o[(r^(240|s>>8&7))&255];r=r>>>8^o[(r^(128|s>>2&63))&255];r=r>>>8^o[(r^(128|i>>6&15|(s&3)<<4))&255];r=r>>>8^o[(r^(128|i&63))&255]}else{r=r>>>8^o[(r^(224|s>>12&15))&255];r=r>>>8^o[(r^(128|s>>6&63))&255];r=r>>>8^o[(r^(128|s&63))&255]}}return r^-1}e.table=o,e.bstr=r,e.buf=n,e.str=a}(se={});var b,ie=function(){var a,e={};function u(e){if("/"==e.charAt(e.length-1))return-1===e.slice(0,-1).indexOf("/")?e:u(e.slice(0,-1));var t=e.lastIndexOf("/");return-1===t?e:e.slice(0,t+1)}function d(e){if("/"==e.charAt(e.length-1))return d(e.slice(0,-1));var t=e.lastIndexOf("/");return-1===t?e:e.slice(t+1)}function b(e){At(e,0);for(var t,r={};e.l<=e.length-4;){var n=e.read_shift(2),a=e.read_shift(2),s=e.l+a,i={};21589===n&&(1&(t=e.read_shift(1))&&(i.mtime=e.read_shift(4)),5<a&&(2&t&&(i.atime=e.read_shift(4)),4&t&&(i.ctime=e.read_shift(4))),i.mtime&&(i.mt=new Date(1e3*i.mtime))),e.l=s,r[n]=i}return r}function s(){return a=a||require("fs")}function i(e,t){if(80==e[0]&&75==e[1])return G(e,t);if(e.length<512)throw new Error("CFB file size "+e.length+" < 512");var r,n=3,a=512,s=0,i=[],o=e.slice(0,512);At(o,0);var l=function(e){if(80==e[e.l]&&75==e[e.l+1])return[0,0];e.chk(S,"Header Signature: "),e.chk(_,"CLSID: ");var t=e.read_shift(2,"u");return[e.read_shift(2,"u"),t]}(o);switch(n=l[0]){case 3:a=512;break;case 4:a=4096;break;case 0:if(0==l[1])return G(e,t);default:throw new Error("Major Version: Expected 3 or 4 saw "+n)}512!==a&&At(o=e.slice(0,a),28);var c=e.slice(0,a);!function(e,t){var r=9;switch(e.l+=2,r=e.read_shift(2)){case 9:if(3!=t)throw new Error("Sector Shift: Expected 9 saw "+r);break;case 12:if(4!=t)throw new Error("Sector Shift: Expected 12 saw "+r);break;default:throw new Error("Sector Shift: Expected 9 or 12 saw "+r)}e.chk("0600","Mini Sector Shift: "),e.chk("000000000000","Reserved: ")}(o,n);var f=o.read_shift(4,"i");if(3===n&&0!==f)throw new Error("# Directory Sectors: Expected 0 saw "+f);o.l+=4,r=o.read_shift(4,"i"),o.l+=4,o.chk("00100000","Mini Stream Cutoff Size: ");for(var h,u=o.read_shift(4,"i"),d=o.read_shift(4,"i"),p=o.read_shift(4,"i"),s=o.read_shift(4,"i"),m=0;m<109&&!((h=o.read_shift(4,"i"))<0);++m)i[m]=h;var g=function(e,t){for(var r=Math.ceil(e.length/t)-1,n=[],a=1;a<r;++a)n[a-1]=e.slice(a*t,(a+1)*t);return n[r-1]=e.slice(r*t),n}(e,a);!function e(t,r,n,a,s){var i=w;if(t===w){if(0!==r)throw new Error("DIFAT chain shorter than expected")}else if(-1!==t){var o=n[t],l=(a>>>2)-1;if(o){for(var c=0;c<l&&(i=St(o,4*c))!==w;++c)s.push(i);e(St(o,a-4),r-1,n,a,s)}}}(p,s,g,a,i);var b=function(e,t,r,n){var a=e.length,s=[],i=[],o=[],l=[],c=n-1,f=0,h=0,u=0,d=0;for(f=0;f<a;++f)if(o=[],a<=(u=f+t)&&(u-=a),!i[u]){for(l=[],h=u;0<=h;){i[h]=!0,o[o.length]=h,l.push(e[h]);var p=r[Math.floor(4*h/n)];if(n<4+(d=4*h&c))throw new Error("FAT boundary crossed: "+h+" 4 "+n);if(!e[p])break;h=St(e[p],d)}s[u]={nodes:o,data:Je([l])}}return s}(g,r,i,a);b[r].name="!Directory",0<d&&u!==w&&(b[u].name="!MiniFAT"),b[i[0]].name="!FAT",b.fat_addrs=i,b.ssz=a;f=[],p=[],s=[];!function(e,t,r,n,a,s,i,o){for(var l,c=0,f=n.length?2:0,h=t[e].data,u=0,d=0;u<h.length;u+=128){var p=h.slice(u,u+128);At(p,64),d=p.read_shift(2),l=et(p,0,d-f),n.push(l);var m={name:l,type:p.read_shift(1),color:p.read_shift(1),L:p.read_shift(4,"i"),R:p.read_shift(4,"i"),C:p.read_shift(4,"i"),clsid:p.read_shift(16),state:p.read_shift(4,"i"),start:0,size:0};0!==p.read_shift(2)+p.read_shift(2)+p.read_shift(2)+p.read_shift(2)&&(m.ct=v(p,p.l-8)),0!==p.read_shift(2)+p.read_shift(2)+p.read_shift(2)+p.read_shift(2)&&(m.mt=v(p,p.l-8)),m.start=p.read_shift(4,"i"),m.size=p.read_shift(4,"i"),m.size<0&&m.start<0&&(m.size=m.type=0,m.start=w,m.name=""),5===m.type?(c=m.start,0<a&&c!==w&&(t[c].name="!StreamData")):4096<=m.size?(m.storage="fat",void 0===t[m.start]&&(t[m.start]=function(e,t,r,n,a){var s=[],i=[];a=a||[];var o=n-1,l=0,c=0;for(l=t;0<=l;){a[l]=!0,s[s.length]=l,i.push(e[l]);var f=r[Math.floor(4*l/n)];if(n<4+(c=4*l&o))throw new Error("FAT boundary crossed: "+l+" 4 "+n);if(!e[f])break;l=St(e[f],c)}return{nodes:s,data:Je([i])}}(r,m.start,t.fat_addrs,t.ssz)),t[m.start].name=m.name,m.content=t[m.start].data.slice(0,m.size)):(m.storage="minifat",m.size<0?m.size=0:c!==w&&m.start!==w&&t[c]&&(m.content=function(e,t,r){var n=e.start,a=e.size,s=[],i=n;for(;r&&0<a&&0<=i;)s.push(t.slice(64*i,64*i+64)),a-=64,i=St(r,4*i);return 0===s.length?Rt(0):he(s).slice(0,e.size)}(m,t[c].data,(t[o]||{}).data))),m.content&&At(m.content,0),s[l]=m,i.push(m)}}(r,b,g,f,d,{},p,u),function(e,t,r){for(var n=0,a=0,s=0,i=0,o=0,l=r.length,c=[],f=[];n<l;++n)c[n]=f[n]=n,t[n]=r[n];for(;o<f.length;++o)n=f[o],a=e[n].L,s=e[n].R,i=e[n].C,c[n]===n&&(-1!==a&&c[a]!==a&&(c[n]=c[a]),-1!==s&&c[s]!==s&&(c[n]=c[s])),-1!==i&&(c[i]=n),-1!==a&&(c[a]=c[n],f.lastIndexOf(a)<o&&f.push(a)),-1!==s&&(c[s]=c[n],f.lastIndexOf(s)<o&&f.push(s));for(n=1;n<l;++n)c[n]===n&&(-1!==s&&c[s]!==s?c[n]=c[s]:-1!==a&&c[a]!==a&&(c[n]=c[a]));for(n=1;n<l;++n)if(0!==e[n].type){if(0===(o=c[n]))t[n]=t[0]+"/"+t[n];else for(;0!==o&&o!==c[o];)t[n]=t[o]+"/"+t[n],o=c[o];c[n]=0}for(t[0]+="/",n=1;n<l;++n)2!==e[n].type&&(t[n]+="/")}(p,s,f),f.shift();s={FileIndex:p,FullPaths:s};return t&&t.raw&&(s.raw={header:c,sectors:g}),s}function v(e,t){return new Date(1e3*(wt(e,t+4)/1e7*Math.pow(2,32)+wt(e,t)/1e7-11644473600))}function p(e,t){var r=t||{},t=r.root||"Root Entry";if(e.FullPaths||(e.FullPaths=[]),e.FileIndex||(e.FileIndex=[]),e.FullPaths.length!==e.FileIndex.length)throw new Error("inconsistent CFB structure");0===e.FullPaths.length&&(e.FullPaths[0]=t+"/",e.FileIndex[0]={name:t,type:5}),r.CLSID&&(e.FileIndex[0].clsid=r.CLSID),t=e,r="Sh33tJ5",ie.find(t,"/"+r)||((e=Rt(4))[0]=55,e[1]=e[3]=50,e[2]=54,t.FileIndex.push({name:r,type:2,content:e,size:4,L:69,R:69,C:69}),t.FullPaths.push(t.FullPaths[0]+r),m(t))}function m(e,t){p(e);for(var r=!1,n=!1,a=e.FullPaths.length-1;0<=a;--a){var s=e.FileIndex[a];switch(s.type){case 0:n?r=!0:(e.FileIndex.pop(),e.FullPaths.pop());break;case 1:case 2:case 5:n=!0,isNaN(s.R*s.L*s.C)&&(r=!0),-1<s.R&&-1<s.L&&s.R==s.L&&(r=!0);break;default:r=!0}}if(r||t){for(var i=new Date(1987,1,19),o=0,l=[],a=0;a<e.FullPaths.length;++a)0!==e.FileIndex[a].type&&l.push([e.FullPaths[a],e.FileIndex[a]]);for(a=0;a<l.length;++a){for(var c=u(l[a][0]),n=!1,o=0;o<l.length;++o)l[o][0]===c&&(n=!0);n||l.push([c,{name:d(c).replace("/",""),type:1,clsid:_,ct:i,mt:i,content:null}])}for(l.sort(function(e,t){return function(e,t){for(var r,n=e.split("/"),a=t.split("/"),s=0,i=Math.min(n.length,a.length);s<i;++s){if(r=n[s].length-a[s].length)return r;if(n[s]!=a[s])return n[s]<a[s]?-1:1}return n.length-a.length}(e[0],t[0])}),e.FullPaths=[],e.FileIndex=[],a=0;a<l.length;++a)e.FullPaths[a]=l[a][0],e.FileIndex[a]=l[a][1];for(a=0;a<l.length;++a){var f=e.FileIndex[a],h=e.FullPaths[a];if(f.name=d(h).replace("/",""),f.L=f.R=f.C=-(f.color=1),f.size=f.content?f.content.length:0,f.start=0,f.clsid=f.clsid||_,0===a)f.C=1<l.length?1:-1,f.size=0,f.type=5;else if("/"==h.slice(-1)){for(o=a+1;o<l.length&&u(e.FullPaths[o])!=h;++o);for(f.C=o>=l.length?-1:o,o=a+1;o<l.length&&u(e.FullPaths[o])!=u(h);++o);f.R=o>=l.length?-1:o,f.type=1}else u(e.FullPaths[a+1]||"")==u(h)&&(f.R=a+1),f.type=2}}}function n(e,t){var r=t||{};if(m(e),"zip"==r.fileType)return function(e,t){var t=t||{},r=[],n=[],a=Rt(1),s=t.compression?8:0,i=0;0;var o=0,l=0,c=0,f=0,h=e.FullPaths[0],u=h,d=e.FileIndex[0],p=[],m=0;for(o=1;o<e.FullPaths.length;++o)if(u=e.FullPaths[o].slice(h.length),(d=e.FileIndex[o]).size&&d.content&&"Sh33tJ5"!=u){var g=c,b=Rt(u.length);for(l=0;l<u.length;++l)b.write_shift(1,127&u.charCodeAt(l));b=b.slice(0,b.l),p[f]=se.buf(d.content,0);var v=d.content;8==s&&(v=function(e){return E?E.deflateRawSync(e):P(e)}(v)),(a=Rt(30)).write_shift(4,67324752),a.write_shift(2,20),a.write_shift(2,i),a.write_shift(2,s),d.mt?function(e,t){var r;r=(r=(r=(t="string"==typeof t?new Date(t):t).getHours())<<6|t.getMinutes())<<5|t.getSeconds()>>>1,e.write_shift(2,r),r=(r=(r=t.getFullYear()-1980)<<4|t.getMonth()+1)<<5|t.getDate(),e.write_shift(2,r)}(a,d.mt):a.write_shift(4,0),a.write_shift(-4,8&i?0:p[f]),a.write_shift(4,8&i?0:v.length),a.write_shift(4,8&i?0:d.content.length),a.write_shift(2,b.length),a.write_shift(2,0),c+=a.length,r.push(a),c+=b.length,r.push(b),c+=v.length,r.push(v),8&i&&((a=Rt(12)).write_shift(-4,p[f]),a.write_shift(4,v.length),a.write_shift(4,d.content.length),c+=a.l,r.push(a)),(a=Rt(46)).write_shift(4,33639248),a.write_shift(2,0),a.write_shift(2,20),a.write_shift(2,i),a.write_shift(2,s),a.write_shift(4,0),a.write_shift(-4,p[f]),a.write_shift(4,v.length),a.write_shift(4,d.content.length),a.write_shift(2,b.length),a.write_shift(2,0),a.write_shift(2,0),a.write_shift(2,0),a.write_shift(2,0),a.write_shift(4,0),a.write_shift(4,g),m+=a.l,n.push(a),m+=b.length,n.push(b),++f}return(a=Rt(22)).write_shift(4,101010256),a.write_shift(2,0),a.write_shift(2,0),a.write_shift(2,f),a.write_shift(2,f),a.write_shift(4,m),a.write_shift(4,c),a.write_shift(2,0),he([he(r),he(n),a])}(e,r);for(var n=function(e){for(var t=0,r=0,n=0;n<e.FileIndex.length;++n){var a=e.FileIndex[n];a.content&&(0<(a=a.content.length)&&(a<4096?t+=a+63>>6:r+=a+511>>9))}for(var s=e.FullPaths.length+3>>2,i=t+127>>7,o=(t+7>>3)+r+s+i,l=o+127>>7,c=l<=109?0:Math.ceil((l-109)/127);l<o+l+c+127>>7;)c=++l<=109?0:Math.ceil((l-109)/127);s=[1,c,l,i,s,r,t,0];return e.FileIndex[0].size=t<<6,s[7]=(e.FileIndex[0].start=s[0]+s[1]+s[2]+s[3]+s[4]+s[5])+(s[6]+7>>3),s}(e),a=Rt(n[7]<<9),s=0,i=0,s=0;s<8;++s)a.write_shift(1,g[s]);for(s=0;s<8;++s)a.write_shift(2,0);for(a.write_shift(2,62),a.write_shift(2,3),a.write_shift(2,65534),a.write_shift(2,9),a.write_shift(2,6),s=0;s<3;++s)a.write_shift(2,0);for(a.write_shift(4,0),a.write_shift(4,n[2]),a.write_shift(4,n[0]+n[1]+n[2]+n[3]-1),a.write_shift(4,0),a.write_shift(4,4096),a.write_shift(4,n[3]?n[0]+n[1]+n[2]-1:w),a.write_shift(4,n[3]),a.write_shift(-4,n[1]?n[0]-1:w),a.write_shift(4,n[1]),s=0;s<109;++s)a.write_shift(-4,s<n[2]?n[1]+s:-1);if(n[1])for(i=0;i<n[1];++i){for(;s<236+127*i;++s)a.write_shift(-4,s<n[2]?n[1]+s:-1);a.write_shift(-4,i===n[1]-1?w:i+1)}function o(e){for(i+=e;s<i-1;++s)a.write_shift(-4,s+1);e&&(++s,a.write_shift(-4,w))}i=s=0;for(i+=n[1];s<i;++s)a.write_shift(-4,y.DIFSECT);for(i+=n[2];s<i;++s)a.write_shift(-4,y.FATSECT);o(n[3]),o(n[4]);for(var l=0,c=0,f=e.FileIndex[0];l<e.FileIndex.length;++l)(f=e.FileIndex[l]).content&&((c=f.content.length)<4096||(f.start=i,o(c+511>>9)));for(o(n[6]+7>>3);511&a.l;)a.write_shift(-4,y.ENDOFCHAIN);for(l=i=s=0;l<e.FileIndex.length;++l)(f=e.FileIndex[l]).content&&(!(c=f.content.length)||4096<=c||(f.start=i,o(c+63>>6)));for(;511&a.l;)a.write_shift(-4,y.ENDOFCHAIN);for(s=0;s<n[4]<<2;++s){var h=e.FullPaths[s];if(h&&0!==h.length){f=e.FileIndex[s],0===s&&(f.start=f.size?f.start-1:w);h=0===s&&r.root||f.name,c=2*(h.length+1);if(a.write_shift(64,h,"utf16le"),a.write_shift(2,c),a.write_shift(1,f.type),a.write_shift(1,f.color),a.write_shift(-4,f.L),a.write_shift(-4,f.R),a.write_shift(-4,f.C),f.clsid)a.write_shift(16,f.clsid,"hex");else for(l=0;l<4;++l)a.write_shift(4,0);a.write_shift(4,f.state||0),a.write_shift(4,0),a.write_shift(4,0),a.write_shift(4,0),a.write_shift(4,0),a.write_shift(4,f.start),a.write_shift(4,f.size),a.write_shift(4,0)}else{for(l=0;l<17;++l)a.write_shift(4,0);for(l=0;l<3;++l)a.write_shift(4,-1);for(l=0;l<12;++l)a.write_shift(4,0)}}for(s=1;s<e.FileIndex.length;++s)if(4096<=(f=e.FileIndex[s]).size){for(a.l=f.start+1<<9,l=0;l<f.size;++l)a.write_shift(1,f.content[l]);for(;511&l;++l)a.write_shift(1,0)}for(s=1;s<e.FileIndex.length;++s)if(0<(f=e.FileIndex[s]).size&&f.size<4096){for(l=0;l<f.size;++l)a.write_shift(1,f.content[l]);for(;63&l;++l)a.write_shift(1,0)}for(;a.l<a.length;)a.write_shift(1,0);return a}e.version="1.1.0";var E,w=-2,S="d0cf11e0a1b11ae1",g=[208,207,17,224,161,177,26,225],_="00000000000000000000000000000000",y={MAXREGSECT:-6,DIFSECT:-4,FATSECT:-3,ENDOFCHAIN:w,FREESECT:-1,HEADER_SIGNATURE:S,HEADER_MINOR_VERSION:"3e00",MAXREGSID:-6,NOSTREAM:-1,HEADER_CLSID:_,EntryTypes:["unknown","storage","stream","lockbytes","property","root"]};function o(e){for(var t=new Array(e.length),r=0;r<e.length;++r)t[r]=String.fromCharCode(e[r]);return t.join("")}var B=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],C=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258],T=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577];for(var t,k="undefined"!=typeof Uint8Array,x=k?new Uint8Array(256):[],r=0;r<256;++r)x[r]=255&((t=139536&((t=r)<<1|t<<11)|558144&(t<<5|t<<15))>>16|t>>8|t);function A(e,t){var r=7&t,t=t>>>3;return(e[t]|(r<=5?0:e[1+t]<<8))>>>r&7}function I(e,t){var r=7&t,t=t>>>3;return(e[t]|(r<=3?0:e[1+t]<<8))>>>r&31}function R(e,t){var r=7&t,t=t>>>3;return(e[t]|(r<=1?0:e[1+t]<<8))>>>r&127}function O(e,t,r){var n=7&t,a=t>>>3,s=(1<<r)-1,t=e[a]>>>n;return r<8-n?t&s:(t|=e[1+a]<<8-n,r<16-n?t&s:(t|=e[2+a]<<16-n,r<24-n?t&s:(t|=e[3+a]<<24-n)&s))}function F(e,t){var r=e.length,n=t<2*r?2*r:t+5,a=0;if(t<=r)return e;if(K){var s=Q(n);if(e.copy)e.copy(s);else for(;a<e.length;++a)s[a]=e[a];return s}if(k){var i=new Uint8Array(n);if(i.set)i.set(e);else for(;a<e.length;++a)i[a]=e[a];return i}return e.length=n,e}function D(e){for(var t=new Array(e),r=0;r<e;++r)t[r]=0;return t}var l,P=(l=function(e,t){for(var r=0;r<e.length;){var n=Math.min(65535,e.length-r),a=r+n==e.length;for(t.write_shift(1,+a),t.write_shift(2,n),t.write_shift(2,65535&~n);0<n--;)t[t.l++]=e[r++]}return t.l},function(e){var t=Rt(50+Math.floor(1.1*e.length)),e=l(e,t);return t.slice(0,e)});function N(e,t,r){for(var n,a=1,s=0,i=0,o=0,l=e.length,c=k?new Uint16Array(32):D(32),s=0;s<32;++s)c[s]=0;for(s=l;s<r;++s)e[s]=0;var l=e.length,f=k?new Uint16Array(l):D(l);for(s=0;s<l;++s)c[n=e[s]]++,a<n&&(a=n),f[s]=0;for(c[0]=0,s=1;s<=a;++s)c[s+16]=o=o+c[s-1]<<1;for(s=0;s<l;++s)0!=(o=e[s])&&(f[s]=c[o+16]++);var h,u,d,p;for(s=0;s<l;++s)if(0!=(h=e[s]))for(u=f[s],d=a,p=void 0,p=x[255&u],o=(d<=8?p>>>8-d:(p=p<<8|x[u>>8&255],d<=16?p>>>16-d:(p=p<<8|x[u>>16&255])>>>24-d))>>a-h,i=(1<<a+4-h)-1;0<=i;--i)t[o|i<<h]=15&h|s<<4;return a}var L=k?new Uint16Array(512):D(512),M=k?new Uint16Array(32):D(32);if(!k){for(var c=0;c<512;++c)L[c]=0;for(c=0;c<32;++c)M[c]=0}!function(){for(var e=[],t=0;t<32;t++)e.push(5);N(e,M,32);for(var r=[],t=0;t<=143;t++)r.push(8);for(;t<=255;t++)r.push(9);for(;t<=279;t++)r.push(7);for(;t<=287;t++)r.push(8);N(r,L,288)}();var U=k?new Uint16Array(32768):D(32768),H=k?new Uint16Array(32768):D(32768),z=k?new Uint16Array(128):D(128),V=1,W=1;function f(e,t){if(3==e[0]&&!(3&e[1]))return[Z(t),2];for(var r=0,n=0,a=Q(t||1<<18),s=0,i=a.length>>>0,o=0,l=0;0==(1&n);)if(n=A(e,r),r+=3,n>>>1!=0)for(l=n>>>1==1?(o=9,5):(r=function(e,t){var r,n,a,s=I(e,t)+257,i=I(e,t+=5)+1,o=(n=t+=5,a=7&t,4+(((r=e)[n=t>>>3]|(a<=4?0:r[1+n]<<8))>>>a&15));t+=4;for(var l=0,c=k?new Uint8Array(19):D(19),f=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],h=1,u=k?new Uint8Array(8):D(8),d=k?new Uint8Array(8):D(8),p=c.length,m=0;m<o;++m)c[B[m]]=l=A(e,t),h<l&&(h=l),u[l]++,t+=3;var g=0;for(u[0]=0,m=1;m<=h;++m)d[m]=g=g+u[m-1]<<1;for(m=0;m<p;++m)0!=(g=c[m])&&(f[m]=d[g]++);for(var b,m=0;m<p;++m)if(0!=(b=c[m]))for(var g=x[f[m]]>>8-b,v=(1<<7-b)-1;0<=v;--v)z[g|v<<b]=7&b|m<<3;for(var E,w,S,_=[],h=1;_.length<s+i;)switch(g=z[R(e,t)],t+=7&g,g>>>=3){case 16:for(l=3+(S=void 0,S=7&(w=t),((E=e)[w=t>>>3]|(S<=6?0:E[1+w]<<8))>>>S&3),t+=2,g=_[_.length-1];0<l--;)_.push(g);break;case 17:for(l=3+A(e,t),t+=3;0<l--;)_.push(0);break;case 18:for(l=11+R(e,t),t+=7;0<l--;)_.push(0);break;default:_.push(g),h<g&&(h=g)}var y=_.slice(0,s),C=_.slice(s);for(m=s;m<286;++m)y[m]=0;for(m=i;m<30;++m)C[m]=0;return V=N(y,U,286),W=N(C,H,30),t}(e,r),o=V,W),!t&&i<s+32767&&(i=(a=F(a,s+32767)).length);;){var c=O(e,r,o),f=(n>>>1==1?L:U)[c];if(r+=15&f,0==((f>>>=4)>>>8&255))a[s++]=f;else{if(256==f)break;var h=(f-=257)<8?0:f-4>>2;5<h&&(h=0);var u=s+C[f];0<h&&(u+=O(e,r,h),r+=h),c=O(e,r,l),r+=15&(f=(n>>>1==1?M:H)[c]);var c=(f>>>=4)<4?0:f-2>>1,d=T[f];for(0<c&&(d+=O(e,r,c),r+=c),!t&&i<u&&(i=(a=F(a,u)).length);s<u;)a[s]=a[s-d],++s}}else{7&r&&(r+=8-(7&r));var p=e[r>>>3]|e[1+(r>>>3)]<<8;if(r+=32,!t&&i<s+p&&(i=(a=F(a,s+p)).length),"function"==typeof e.copy)e.copy(a,s,r>>>3,(r>>>3)+p),s+=p,r+=8*p;else for(;0<p--;)a[s++]=e[r>>>3],r+=8}return[t?a:a.slice(0,s),r+7>>>3]}function X(e,t){t=f(e.slice(e.l||0),t);return e.l+=t[1],t[0]}function j(e,t){if(!e)throw new Error(t);"undefined"!=typeof console&&console.error(t)}function G(e,t){var r=e;At(r,0);var n={FileIndex:[],FullPaths:[]};p(n,{root:t.root});for(var a=r.length-4;(80!=r[a]||75!=r[a+1]||5!=r[a+2]||6!=r[a+3])&&0<=a;)--a;r.l=a+4,r.l+=4;var s=r.read_shift(2);r.l+=6;t=r.read_shift(4);for(r.l=t,a=0;a<s;++a){r.l+=20;var i=r.read_shift(4),o=r.read_shift(4),l=r.read_shift(2),c=r.read_shift(2),f=r.read_shift(2);r.l+=8;var h=r.read_shift(4),u=b(r.slice(r.l+l,r.l+l+c));r.l+=l+c+f;f=r.l;r.l=h+4,function(e,t,r,n,a){e.l+=2;var s=e.read_shift(2),i=e.read_shift(2),o=function(e){var t=65535&e.read_shift(2),r=65535&e.read_shift(2),n=new Date,a=31&r,e=15&(r>>>=5);return r>>>=4,n.setMilliseconds(0),n.setFullYear(1980+r),n.setMonth(e-1),n.setDate(a),e=31&t,a=63&(t>>>=5),n.setHours(t>>>=6),n.setMinutes(a),n.setSeconds(e<<1),n}(e);if(8257&s)throw new Error("Unsupported ZIP encryption");for(var l=e.read_shift(4),c=e.read_shift(4),f=e.read_shift(4),h=e.read_shift(2),u=e.read_shift(2),d="",p=0;p<h;++p)d+=String.fromCharCode(e[e.l++]);{var m;u&&(((m=b(e.slice(e.l,e.l+u)))[21589]||{}).mt&&(o=m[21589].mt),((a||{})[21589]||{}).mt&&(o=a[21589].mt))}e.l+=u;var g=e.slice(e.l,e.l+c);switch(i){case 8:g=function(e,t){if(!E)return X(e,t);var r=new E.InflateRaw,t=r._processChunk(e.slice(e.l),r._finishFlushFlag);return e.l+=r.bytesRead,t}(e,f);break;case 0:break;default:throw new Error("Unsupported ZIP Compression method "+i)}u=!1;8&s&&(134695760==(l=e.read_shift(4))&&(l=e.read_shift(4),u=!0),c=e.read_shift(4),f=e.read_shift(4));c!=t&&j(u,"Bad compressed size: "+t+" != "+c);f!=r&&j(u,"Bad uncompressed size: "+r+" != "+f);r=se.buf(g,0);l!=r&&j(u,"Bad CRC32 checksum: "+l+" != "+r);$(n,d,g,{unsafe:!0,mt:o})}(r,i,o,n,u),r.l=f}return n}function $(e,t,r,n){var a=n&&n.unsafe;a||p(e);var s,i=!a&&ie.find(e,t);return i||(s=e.FullPaths[0],s=t.slice(0,s.length)==s?t:("/"!=s.slice(-1)&&(s+="/"),(s+t).replace("//","/")),i={name:d(t),type:2},e.FileIndex.push(i),e.FullPaths.push(s),a||ie.utils.cfb_gc(e)),i.content=r,i.size=r?r.length:0,n&&(n.CLSID&&(i.clsid=n.CLSID),n.mt&&(i.mt=n.mt),n.ct&&(i.ct=n.ct)),i}return e.find=function(e,t){var r=e.FullPaths.map(function(e){return e.toUpperCase()}),n=r.map(function(e){var t=e.split("/");return t[t.length-("/"==e.slice(-1)?2:1)]}),a=!1;47===t.charCodeAt(0)?(a=!0,t=r[0].slice(0,-1)+t):a=-1!==t.indexOf("/");var s=t.toUpperCase(),i=(!0===a?r:n).indexOf(s);if(-1!==i)return e.FileIndex[i];var o=!s.match(re),s=s.replace(q,"");for(o&&(s=s.replace(re,"!")),i=0;i<r.length;++i){if((o?r[i].replace(re,"!"):r[i]).replace(q,"")==s)return e.FileIndex[i];if((o?n[i].replace(re,"!"):n[i]).replace(q,"")==s)return e.FileIndex[i]}return null},e.read=function(e,t){switch(t&&t.type||"base64"){case"file":return r=e,n=t,s(),i(a.readFileSync(r),n);case"base64":return i(J(Y.decode(e)),t);case"binary":return i(J(e),t)}var r,n;return i(e,t)},e.parse=i,e.write=function(e,t){var r=n(e,t);switch(t&&t.type){case"file":return s(),a.writeFileSync(t.filename,r),r;case"binary":return o(r);case"base64":return Y.encode(o(r))}return r},e.writeFile=function(e,t,r){s(),r=n(e,r),a.writeFileSync(t,r)},e.utils={cfb_new:function(e){var t={};return p(t,e),t},cfb_add:$,cfb_del:function(e,t){p(e);var r=ie.find(e,t);if(r)for(var n=0;n<e.FileIndex.length;++n)if(e.FileIndex[n]==r)return e.FileIndex.splice(n,1),e.FullPaths.splice(n,1),!0;return!1},cfb_mov:function(e,t,r){p(e);var n=ie.find(e,t);if(n)for(var a=0;a<e.FileIndex.length;++a)if(e.FileIndex[a]==n)return e.FileIndex[a].name=d(r),e.FullPaths[a]=r,!0;return!1},cfb_gc:function(e){m(e,!0)},ReadShift:yt,CheckField:xt,prep_blob:At,bconcat:he,use_zlib:function(e){try{var t=new e.InflateRaw;if(t._processChunk(new Uint8Array([3,0]),t._finishFlushFlag),!t.bytesRead)throw new Error("zlib does not expose bytesRead");E=e}catch(e){console.error("cannot use native zlib: "+(e.message||e))}},_deflateRaw:P,_inflateRaw:X,consts:y},e}();if(0,"undefined"!=typeof require)try{b=require("fs")}catch(e){}function v(e){return"string"==typeof e?i(e):Array.isArray(e)?function(e){if("undefined"==typeof Uint8Array)throw new Error("Unsupported");return new Uint8Array(e)}(e):e}function E(e,t,r){if(void 0!==b&&b.writeFileSync)return r?b.writeFileSync(e,t,r):b.writeFileSync(e,t);r="utf8"==r?Fe(t):t;if("undefined"!=typeof IE_SaveFile)return IE_SaveFile(r,e);if("undefined"!=typeof Blob){r=new Blob([v(r)],{type:"application/octet-stream"});if("undefined"!=typeof navigator&&navigator.msSaveBlob)return navigator.msSaveBlob(r,e);if("undefined"!=typeof saveAs)return saveAs(r,e);if("undefined"!=typeof URL&&"undefined"!=typeof document&&document.createElement&&URL.createObjectURL){var n=URL.createObjectURL(r);if("object"==typeof chrome&&"function"==typeof(chrome.downloads||{}).download)return URL.revokeObjectURL&&"undefined"!=typeof setTimeout&&setTimeout(function(){URL.revokeObjectURL(n)},6e4),chrome.downloads.download({url:n,filename:e,saveAs:!0});r=document.createElement("a");if(null!=r.download)return r.download=e,r.href=n,document.body.appendChild(r),r.click(),document.body.removeChild(r),URL.revokeObjectURL&&"undefined"!=typeof setTimeout&&setTimeout(function(){URL.revokeObjectURL(n)},6e4),n}}if("undefined"!=typeof $&&"undefined"!=typeof File&&"undefined"!=typeof Folder)try{var a=File(e);return a.open("w"),a.encoding="binary",Array.isArray(t)&&(t=o(t)),a.write(t),a.close(),t}catch(e){if(!e.message||!e.message.match(/onstruct/))throw e}throw new Error("cannot save file "+e)}function de(e){for(var t=Object.keys(e),r=[],n=0;n<t.length;++n)e.hasOwnProperty(t[n])&&r.push(t[n]);return r}function w(e,t){for(var r=[],n=de(e),a=0;a!==n.length;++a)null==r[e[n[a]][t]]&&(r[e[n[a]][t]]=n[a]);return r}function S(e){for(var t=[],r=de(e),n=0;n!==r.length;++n)t[e[r[n]]]=r[n];return t}function _(e){for(var t=[],r=de(e),n=0;n!==r.length;++n)t[e[r[n]]]=parseInt(r[n],10);return t}var y=new Date(1899,11,30,0,0,0),C=y.getTime()+6e4*((new Date).getTimezoneOffset()-y.getTimezoneOffset());function G(e,t){e=e.getTime();return t&&(e-=1263168e5),(e-C)/864e5}function N(e){var t=new Date;return t.setTime(24*e*60*60*1e3+C),t}var B=new Date("2017-02-19T19:06:09.000Z"),T=2017==(B=isNaN(B.getFullYear())?new Date("2/19/17"):B).getFullYear();function oe(e,t){var r=new Date(e);if(T)return 0<t?r.setTime(r.getTime()+60*r.getTimezoneOffset()*1e3):t<0&&r.setTime(r.getTime()-60*r.getTimezoneOffset()*1e3),r;if(e instanceof Date)return e;if(1917==B.getFullYear()&&!isNaN(r.getFullYear())){t=r.getFullYear();return-1<e.indexOf(""+t)?r:(r.setFullYear(r.getFullYear()+100),r)}r=e.match(/\d+/g)||["2017","2","19","0","0","0"],r=new Date(+r[0],+r[1]-1,+r[2],+r[3]||0,+r[4]||0,+r[5]||0);return r=-1<e.indexOf("Z")?new Date(r.getTime()-60*r.getTimezoneOffset()*1e3):r}function k(e){for(var t="",r=0;r!=e.length;++r)t+=String.fromCharCode(e[r]);return t}function le(e){if("undefined"!=typeof JSON&&!Array.isArray(e))return JSON.parse(JSON.stringify(e));if("object"!=typeof e||null==e)return e;if(e instanceof Date)return new Date(e.getTime());var t,r={};for(t in e)e.hasOwnProperty(t)&&(r[t]=le(e[t]));return r}function R(e,t){for(var r="";r.length<t;)r+=e;return r}function x(e){var t=Number(e);if(!isNaN(t))return t;var r=1,e=e.replace(/([\d]),([\d])/g,"$1$2").replace(/[$]/g,"").replace(/[%]/g,function(){return r*=100,""});return isNaN(t=Number(e))?(e=e.replace(/[(](.*)[)]/,function(e,t){return r=-r,t}),isNaN(t=Number(e))?t:t/r):t/r}function A(e){var t=new Date(e),r=new Date(NaN),n=t.getYear(),a=t.getMonth(),s=t.getDate();return isNaN(s)||n<0||8099<n||(!(0<a||1<s)||101==n)&&!e.toLowerCase().match(/jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec/)&&e.match(/[^-0-9:,\/\\]/)?r:t}var I,O=5=="abacaba".split(/(:?b)/i).length;function F(e){return e?e.data?te(e.data):e.asNodeBuffer&&K?te(e.asNodeBuffer().toString("binary")):e.asBinary?te(e.asBinary()):e._data&&e._data.getContent?te(k(Array.prototype.slice.call(e._data.getContent(),0))):null:null}function D(e){return(e&&".bin"===e.name.slice(-4)?function(e){if(!e)return null;if(e.data)return ee(e.data);if(e.asNodeBuffer&&K)return e.asNodeBuffer();if(e._data&&e._data.getContent){e=e._data.getContent();return"string"==typeof e?ee(e):Array.prototype.slice.call(e)}return null}:F)(e)}function P(e,t){for(var r=de(e.files),n=t.toLowerCase(),a=n.replace(/\//g,"\\"),s=0;s<r.length;++s){var i=r[s].toLowerCase();if(n==i||a==i)return e.files[r[s]]}return null}function L(e,t){e=P(e,t);if(null==e)throw new Error("Cannot find file "+t+" in zip");return e}function M(e,t,r){if(!r)return D(L(e,t));if(!t)return null;try{return M(e,t)}catch(e){return null}}function U(e,t,r){if(!r)return F(L(e,t));if(!t)return null;try{return U(e,t)}catch(e){return null}}function H(e,t){var r=t.split("/");"/"!=t.slice(-1)&&r.pop();for(var n=e.split("/");0!==n.length;){var a=n.shift();".."===a?r.pop():"."!==a&&r.push(a)}return r.join("/")}"undefined"!=typeof JSZipSync&&(I=JSZipSync),"undefined"!=typeof exports&&"undefined"!=typeof module&&module.exports&&void 0===I&&(I=void 0);var z='<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\r\n',V=/([^"\s?>\/]+)\s*=\s*((?:")([^"]*)(?:")|(?:')([^']*)(?:')|([^'">\s]+))/g,W=/<[\/\?]?[a-zA-Z0-9:]+(?:\s+[^"\s?>\/]+\s*=\s*(?:"[^"]*"|'[^']*'|[^'">\s=]+))*\s?[\/\?]?>/g;z.match(W)||(W=/<[^>]*>/g);var X=/<\w*:/,j=/<(\/?)\w+:/;function pe(e,t){for(var r={},n=0,a=0;n!==e.length&&(32!==(a=e.charCodeAt(n))&&10!==a&&13!==a);++n);if(t||(r[0]=e.slice(0,n)),n===e.length)return r;var s,i,o,l=e.match(V),c=0,f=0,h="",u="";if(l)for(f=0;f!=l.length;++f){for(u=l[f],a=0;a!=u.length&&61!==u.charCodeAt(a);++a);for(h=u.slice(0,a).trim();32==u.charCodeAt(a+1);)++a;for(i=34==(n=u.charCodeAt(a+1))||39==n?1:0,s=u.slice(a+1+i,u.length-i),c=0;c!=h.length&&58!==h.charCodeAt(c);++c);c===h.length?(r[h=0<h.indexOf("_")?h.slice(0,h.indexOf("_")):h]=s,r[h.toLowerCase()]=s):r[o=(5===c&&"xmlns"===h.slice(0,5)?"xmlns":"")+h.slice(c+1)]&&"ext"==h.slice(c-3,c)||(r[o]=s,r[o.toLowerCase()]=s)}return r}function me(e){return e.replace(j,"<$1")}var ge,be,ve={"&quot;":'"',"&apos;":"'","&gt;":">","&lt;":"<","&amp;":"&"},Ee=S(ve),we=(ge=/&(?:quot|apos|gt|lt|amp|#x?([\da-fA-F]+));/g,be=/_x([\da-fA-F]{4})_/g,function e(t){var r=t+"",n=r.indexOf("<![CDATA[");if(-1==n)return r.replace(ge,function(e,t){return ve[e]||String.fromCharCode(parseInt(t,-1<e.indexOf("x")?16:10))||e}).replace(be,function(e,t){return String.fromCharCode(parseInt(t,16))});t=r.indexOf("]]>");return e(r.slice(0,n))+r.slice(n+9,t)+e(r.slice(t+3))}),Se=/[&<>'"]/g,_e=/[\u0000-\u0008\u000b-\u001f]/g;function ye(e){return(e+"").replace(Se,function(e){return Ee[e]}).replace(_e,function(e){return"_x"+("000"+e.charCodeAt(0).toString(16)).slice(-4)+"_"})}function Ce(e){return ye(e).replace(/ /g,"_x0020_")}var Be=/[\u0000-\u001f]/g;function Te(e){return(e+"").replace(Se,function(e){return Ee[e]}).replace(/\n/g,"<br/>").replace(Be,function(e){return"&#x"+("000"+e.charCodeAt(0).toString(16)).slice(-4)+";"})}var ke,xe=(ke=/&#(\d+);/g,function(e){return e.replace(ke,Ae)});function Ae(e,t){return String.fromCharCode(parseInt(t,10))}var Ie=function(e){return e.replace(/(\r\n|[\r\n])/g,"&#10;")};function Re(e){switch(e){case 1:case!0:case"1":case"true":case"TRUE":return!0;default:return!1}}var Oe=function(e){for(var t,r,n,a,s="",i=0,o=0;i<e.length;)(t=e.charCodeAt(i++))<128?s+=String.fromCharCode(t):(r=e.charCodeAt(i++),191<t&&t<224?(o=(31&t)<<6,o|=63&r,s+=String.fromCharCode(o)):(n=e.charCodeAt(i++),t<240?s+=String.fromCharCode((15&t)<<12|(63&r)<<6|63&n):(a=((7&t)<<18|(63&r)<<12|(63&n)<<6|63&(o=e.charCodeAt(i++)))-65536,s+=String.fromCharCode(55296+(a>>>10&1023)),s+=String.fromCharCode(56320+(1023&a)))));return s},Fe=function(e){for(var t,r=[],n=0,a=0;n<e.length;)switch(!0){case(a=e.charCodeAt(n++))<128:r.push(String.fromCharCode(a));break;case a<2048:r.push(String.fromCharCode(192+(a>>6))),r.push(String.fromCharCode(128+(63&a)));break;case 55296<=a&&a<57344:a-=55296,t=e.charCodeAt(n++)-56320+(a<<10),r.push(String.fromCharCode(240+(t>>18&7))),r.push(String.fromCharCode(144+(t>>12&63))),r.push(String.fromCharCode(128+(t>>6&63))),r.push(String.fromCharCode(128+(63&t)));break;default:r.push(String.fromCharCode(224+(a>>12))),r.push(String.fromCharCode(128+(a>>6&63))),r.push(String.fromCharCode(128+(63&a)))}return r.join("")};K&&(ho=function(e){for(var t,r,n=Buffer.alloc(2*e.length),a=1,s=0,i=0,o=0;o<e.length;o+=a)a=1,(r=e.charCodeAt(o))<128?t=r:r<224?(t=64*(31&r)+(63&e.charCodeAt(o+1)),a=2):r<240?(t=4096*(15&r)+64*(63&e.charCodeAt(o+1))+(63&e.charCodeAt(o+2)),a=3):(a=4,t=262144*(7&r)+4096*(63&e.charCodeAt(o+1))+64*(63&e.charCodeAt(o+2))+(63&e.charCodeAt(o+3)),i=55296+((t-=65536)>>>10&1023),t=56320+(1023&t)),0!==i&&(n[s++]=255&i,n[s++]=i>>>8,i=0),n[s++]=t%256,n[s++]=t>>>8;return n.slice(0,s).toString("ucs2")},po=function(e){return s(e,"binary").toString("utf8")},(Oe=Oe(uo="foo bar bazâð£")==ho(uo)?ho:Oe)(uo)==po(uo)&&(Oe=po),Fe=function(e){return s(e,"utf8").toString("binary")});var De,Pe,Ne,Le=(De={},function(e,t){var r=e+"|"+(t||"");return De[r]||(De[r]=new RegExp("<(?:\\w+:)?"+e+'(?: xml:space="preserve")?(?:[^>]*)>([\\s\\S]*?)</(?:\\w+:)?'+e+">",t||""))}),Me=(Pe=[["nbsp"," "],["middot","·"],["quot",'"'],["apos","'"],["gt",">"],["lt","<"],["amp","&"]].map(function(e){return[new RegExp("&"+e[0]+";","g"),e[1]]}),function(e){for(var t=e.replace(/^[\t\n\r ]+/,"").replace(/[\t\n\r ]+$/,"").replace(/[\t\n\r ]+/g," ").replace(/<\s*[bB][rR]\s*\/?>/g,"\n").replace(/<[^>]*>/g,""),r=0;r<Pe.length;++r)t=t.replace(Pe[r][0],Pe[r][1]);return t}),Ue=(Ne={},function(e){return void 0!==Ne[e]?Ne[e]:Ne[e]=new RegExp("<(?:vt:)?"+e+">([\\s\\S]*?)</(?:vt:)?"+e+">","g")}),He=/<\/?(?:vt:)?variant>/g,ze=/<(?:vt:)([^>]*)>([\s\S]*)</;function Ve(e,t){var r=pe(e),e=e.match(Ue(r.baseType))||[],n=[];if(e.length==r.size)return e.forEach(function(e){e=e.replace(He,"").match(ze);e&&n.push({v:Oe(e[2]),t:e[1]})}),n;if(t.WTF)throw new Error("unexpected vector length "+e.length+" != "+r.size);return n}var We=/(^\s|\s$|\n)/;function Xe(e,t){return"<"+e+(t.match(We)?' xml:space="preserve"':"")+">"+t+"</"+e+">"}function je(t){return de(t).map(function(e){return" "+e+'="'+t[e]+'"'}).join("")}function Ge(e,t,r){return"<"+e+(null!=r?je(r):"")+(null!=t?(t.match(We)?' xml:space="preserve"':"")+">"+t+"</"+e:"/")+">"}function $e(e,t){try{return e.toISOString().replace(/\.\d*/,"")}catch(e){if(t)throw e}return""}var Ye={dc:"http://purl.org/dc/elements/1.1/",dcterms:"http://purl.org/dc/terms/",dcmitype:"http://purl.org/dc/dcmitype/",mx:"http://schemas.microsoft.com/office/mac/excel/2008/main",r:"http://schemas.openxmlformats.org/officeDocument/2006/relationships",sjs:"http://schemas.openxmlformats.org/package/2006/sheetjs/core-properties",vt:"http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes",xsi:"http://www.w3.org/2001/XMLSchema-instance",xsd:"http://www.w3.org/2001/XMLSchema",main:["http://schemas.openxmlformats.org/spreadsheetml/2006/main","http://purl.oclc.org/ooxml/spreadsheetml/main","http://schemas.microsoft.com/office/excel/2006/main","http://schemas.microsoft.com/office/excel/2006/2"]},Ke={o:"urn:schemas-microsoft-com:office:office",x:"urn:schemas-microsoft-com:office:excel",ss:"urn:schemas-microsoft-com:office:spreadsheet",dt:"uuid:C2F41010-65B3-11d1-A29F-00AA00C14882",mv:"http://macVmlSchemaUri",v:"urn:schemas-microsoft-com:vml",html:"http://www.w3.org/TR/REC-html40"};var Ze,Qe,Je=function(e){for(var t=[],r=0;r<e[0].length;++r)if(e[0][r])for(var n=0,a=e[0][r].length;n<a;n+=10240)t.push.apply(t,e[0][r].slice(n,n+10240));return t},qe=Je,et=function(e,t,r){for(var n=[],a=t;a<r;a+=2)n.push(String.fromCharCode(vt(e,a)));return n.join("").replace(q,"")},tt=et,rt=function(e,t,r){for(var n=[],a=t;a<t+r;++a)n.push(("0"+e[a].toString(16)).slice(-2));return n.join("")},nt=rt,at=function(e,t,r){for(var n=[],a=t;a<r;a++)n.push(String.fromCharCode(bt(e,a)));return n.join("")},st=at,it=function(e,t){var r=wt(e,t);return 0<r?at(e,t+4,t+4+r-1):""},ot=it,lt=function(e,t){var r=wt(e,t);return 0<r?at(e,t+4,t+4+r-1):""},ct=lt,ft=function(e,t){var r=2*wt(e,t);return 0<r?at(e,t+4,t+4+r-1):""},ht=ft,ut=Ze=function(e,t){var r=wt(e,t);return 0<r?et(e,t+4,t+4+r):""},dt=function(e,t){var r=wt(e,t);return 0<r?at(e,t+4,t+4+r):""},pt=dt,mt=Qe=function(e,t){for(var r=1-2*(e[t+7]>>>7),n=((127&e[t+7])<<4)+(e[t+6]>>>4&15),a=15&e[t+6],s=5;0<=s;--s)a=256*a+e[t+s];return 2047==n?0==a?1/0*r:NaN:(0==n?n=-1022:(n-=1023,a+=Math.pow(2,52)),r*Math.pow(2,n-52)*a)},gt=function(e){return Array.isArray(e)};K&&(et=function(e,t,r){return Buffer.isBuffer(e)?e.toString("utf16le",t,r).replace(q,""):tt(e,t,r)},rt=function(e,t,r){return Buffer.isBuffer(e)?e.toString("hex",t,t+r):nt(e,t,r)},it=function(e,t){if(!Buffer.isBuffer(e))return ot(e,t);var r=e.readUInt32LE(t);return 0<r?e.toString("utf8",t+4,t+4+r-1):""},lt=function(e,t){if(!Buffer.isBuffer(e))return ct(e,t);var r=e.readUInt32LE(t);return 0<r?e.toString("utf8",t+4,t+4+r-1):""},ft=function(e,t){if(!Buffer.isBuffer(e))return ht(e,t);var r=2*e.readUInt32LE(t);return e.toString("utf16le",t+4,t+4+r-1)},ut=function(e,t){if(!Buffer.isBuffer(e))return Ze(e,t);var r=e.readUInt32LE(t);return e.toString("utf16le",t+4,t+4+r)},dt=function(e,t){if(!Buffer.isBuffer(e))return pt(e,t);var r=e.readUInt32LE(t);return e.toString("utf8",t+4,t+4+r)},at=function(e,t,r){return Buffer.isBuffer(e)?e.toString("utf8",t,r):st(e,t,r)},Je=function(e){return 0<e[0].length&&Buffer.isBuffer(e[0][0])?Buffer.concat(e[0]):qe(e)},he=function(e){return Buffer.isBuffer(e[0])?Buffer.concat(e):[].concat.apply([],e)},mt=function(e,t){return Buffer.isBuffer(e)?e.readDoubleLE(t):Qe(e,t)},gt=function(e){return Buffer.isBuffer(e)||Array.isArray(e)}),"undefined"!=typeof cptable&&(et=function(e,t,r){return cptable.utils.decode(1200,e.slice(t,r)).replace(q,"")},at=function(e,t,r){return cptable.utils.decode(65001,e.slice(t,r))},it=function(e,t){var r=wt(e,t);return 0<r?cptable.utils.decode(a,e.slice(t+4,t+4+r-1)):""},lt=function(e,t){var r=wt(e,t);return 0<r?cptable.utils.decode(f,e.slice(t+4,t+4+r-1)):""},ft=function(e,t){var r=2*wt(e,t);return 0<r?cptable.utils.decode(1200,e.slice(t+4,t+4+r-1)):""},ut=function(e,t){var r=wt(e,t);return 0<r?cptable.utils.decode(1200,e.slice(t+4,t+4+r)):""},dt=function(e,t){var r=wt(e,t);return 0<r?cptable.utils.decode(65001,e.slice(t+4,t+4+r)):""});var bt=function(e,t){return e[t]},vt=function(e,t){return 256*e[t+1]+e[t]},Et=function(e,t){t=256*e[t+1]+e[t];return t<32768?t:-1*(65535-t+1)},wt=function(e,t){return e[t+3]*(1<<24)+(e[t+2]<<16)+(e[t+1]<<8)+e[t]},St=function(e,t){return e[t+3]<<24|e[t+2]<<16|e[t+1]<<8|e[t]},_t=function(e,t){return e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3]};function yt(e,t){var r,n,a,s,i,o,l="",c=[];switch(t){case"dbcs":if(o=this.l,K&&Buffer.isBuffer(this))l=this.slice(this.l,this.l+2*e).toString("utf16le");else for(i=0;i<e;++i)l+=String.fromCharCode(vt(this,o)),o+=2;e*=2;break;case"utf8":l=at(this,this.l,this.l+e);break;case"utf16le":l=et(this,this.l,this.l+(e*=2));break;case"wstr":if("undefined"==typeof cptable)return yt.call(this,e,"dbcs");l=cptable.utils.decode(f,this.slice(this.l,this.l+2*e)),e*=2;break;case"lpstr-ansi":l=it(this,this.l),e=4+wt(this,this.l);break;case"lpstr-cp":l=lt(this,this.l),e=4+wt(this,this.l);break;case"lpwstr":l=ft(this,this.l),e=4+2*wt(this,this.l);break;case"lpp4":e=4+wt(this,this.l),l=ut(this,this.l),2&e&&(e+=2);break;case"8lpp4":e=4+wt(this,this.l),l=dt(this,this.l),3&e&&(e+=4-(3&e));break;case"cstr":for(e=0,l="";0!==(a=bt(this,this.l+e++));)c.push(u(a));l=c.join("");break;case"_wstr":for(e=0,l="";0!==(a=vt(this,this.l+e));)c.push(u(a)),e+=2;e+=2,l=c.join("");break;case"dbcs-cont":for(l="",o=this.l,i=0;i<e;++i){if(this.lens&&-1!==this.lens.indexOf(o))return a=bt(this,o),this.l=o+1,s=yt.call(this,e-i,a?"dbcs-cont":"sbcs-cont"),c.join("")+s;c.push(u(vt(this,o))),o+=2}l=c.join(""),e*=2;break;case"cpstr":if("undefined"!=typeof cptable){l=cptable.utils.decode(f,this.slice(this.l,this.l+e));break}case"sbcs-cont":for(l="",o=this.l,i=0;i!=e;++i){if(this.lens&&-1!==this.lens.indexOf(o))return a=bt(this,o),this.l=o+1,s=yt.call(this,e-i,a?"dbcs-cont":"sbcs-cont"),c.join("")+s;c.push(u(bt(this,o))),o+=1}l=c.join("");break;default:switch(e){case 1:return r=bt(this,this.l),this.l++,r;case 2:return r=("i"===t?Et:vt)(this,this.l),this.l+=2,r;case 4:case-4:return"i"===t||0==(128&this[this.l+3])?(r=(0<e?St:_t)(this,this.l),this.l+=4,r):(n=wt(this,this.l),this.l+=4,n);case 8:case-8:if("f"===t)return n=8==e?mt(this,this.l):mt([this[this.l+7],this[this.l+6],this[this.l+5],this[this.l+4],this[this.l+3],this[this.l+2],this[this.l+1],this[this.l+0]],0),this.l+=8,n;e=8;case 16:l=rt(this,this.l,e)}}return this.l+=e,l}function Ct(e,t,r){e[r]=255&t,e[r+1]=t>>>8&255,e[r+2]=t>>>16&255,e[r+3]=t>>>24&255}function Bt(e,t,r){e[r]=255&t,e[r+1]=t>>8&255,e[r+2]=t>>16&255,e[r+3]=t>>24&255}function Tt(e,t,r){e[r]=255&t,e[r+1]=t>>>8&255}function kt(e,t,r){var n=0,a=0;if("dbcs"===r){for(a=0;a!=t.length;++a)Tt(this,t.charCodeAt(a),this.l+2*a);n=2*t.length}else if("sbcs"===r){for(t=t.replace(/[^\x00-\x7F]/g,"_"),a=0;a!=t.length;++a)this[this.l+a]=255&t.charCodeAt(a);n=t.length}else{if("hex"===r){for(;a<e;++a)this[this.l++]=parseInt(t.slice(2*a,2*a+2),16)||0;return this}if("utf16le"===r){for(var s=Math.min(this.l+e,this.length),a=0;a<Math.min(t.length,e);++a){var i=t.charCodeAt(a);this[this.l++]=255&i,this[this.l++]=i>>8}for(;this.l<s;)this[this.l++]=0;return this}switch(e){case 1:n=1,this[this.l]=255&t;break;case 2:n=2,this[this.l]=255&t,this[this.l+1]=255&(t>>>=8);break;case 3:n=3,this[this.l]=255&t,this[this.l+1]=255&(t>>>=8),this[this.l+2]=255&(t>>>=8);break;case 4:n=4,Ct(this,t,this.l);break;case 8:if(n=8,"f"===r){!function(e,t,r){var n=(t<0||1/t==-1/0?1:0)<<7,a=0,s=0,i=n?-t:t;isFinite(i)?0==i?a=s=0:(a=Math.floor(Math.log(i)/Math.LN2),s=i*Math.pow(2,52-a),a<=-1023&&(!isFinite(s)||s<Math.pow(2,52))?a=-1022:(s-=Math.pow(2,52),a+=1023)):(a=2047,s=isNaN(t)?26985:0);for(var o=0;o<=5;++o,s/=256)e[r+o]=255&s;e[r+6]=(15&a)<<4|15&s,e[r+7]=a>>4|n}(this,t,this.l);break}case 16:break;case-4:n=4,Bt(this,t,this.l)}}return this.l+=n,this}function xt(e,t){var r=rt(this,this.l,e.length>>1);if(r!==e)throw new Error(t+"Expected "+e+" saw "+r);this.l+=e.length>>1}function At(e,t){e.l=t,e.read_shift=yt,e.chk=xt,e.write_shift=kt}function It(e,t){e.l+=t}function Rt(e){e=Z(e);return At(e,0),e}function Ot(e,t,r){if(e){At(e,e.l||0);for(var n=e.length,a=0;e.l<n;){128&(a=e.read_shift(1))&&(a=(127&a)+((127&e.read_shift(1))<<7));for(var s,i=Ec[a]||Ec[65535],o=127&(s=e.read_shift(1)),l=1;l<4&&128&s;++l)o+=(127&(s=e.read_shift(1)))<<7*l;var c=e.l+o,f=(i.f||It)(e,o,r);if(e.l=c,t(f,i.n,a))return}}}function Ft(){function t(e){return At(e=Rt(e),0),e}function r(){s&&(s.length>s.l&&((s=s.slice(0,s.l)).l=s.length),0<s.length&&e.push(s),s=null)}function n(e){return s&&e<s.length-s.l?s:(r(),s=t(Math.max(e+1,a)))}var e=[],a=K?256:2048,s=t(a);return{next:n,push:function(e){r(),null==(s=e).l&&(s.l=s.length),n(a)},end:function(){return r(),Je([e])},_bufs:e}}function Dt(e,t,r,n){var a=+wc[t];if(!isNaN(a)){t=1+(128<=a?1:0)+1,128<=(n=n||(Ec[a].p||(r||[]).length||0))&&++t,16384<=n&&++t,2097152<=n&&++t;var s=e.next(t);a<=127?s.write_shift(1,a):(s.write_shift(1,128+(127&a)),s.write_shift(1,a>>7));for(var i=0;4!=i;++i){if(!(128<=n)){s.write_shift(1,n);break}s.write_shift(1,128+(127&n)),n>>=7}0<n&&gt(r)&&e.push(r)}}function Pt(e,t,r){var n=le(e);if(t.s?(n.cRel&&(n.c+=t.s.c),n.rRel&&(n.r+=t.s.r)):(n.cRel&&(n.c+=t.c),n.rRel&&(n.r+=t.r)),!r||r.biff<12){for(;256<=n.c;)n.c-=256;for(;65536<=n.r;)n.r-=65536}return n}function Nt(e,t,r){e=le(e);return e.s=Pt(e.s,t.s,r),e.e=Pt(e.e,t.s,r),e}function Lt(e,t){e.cRel&&e.c<0&&((e=le(e)).c+=8<t?16384:256),e.rRel&&e.r<0&&((e=le(e)).r+=8<t?1048576:5<t?65536:16384);t=jt(e);return 0===e.cRel&&(t=t.replace(/^([A-Z])/,"$$$1")),t=0===e.rRel?t.replace(/([A-Z]|^)(\d+)$/,"$1$$$2"):t}function Mt(e,t){return 0!=e.s.r||e.s.rRel||e.e.r!=(12<=t.biff?1048575:8<=t.biff?65536:16384)||e.e.rRel?0!=e.s.c||e.s.cRel||e.e.c!=(12<=t.biff?65535:255)||e.e.cRel?Lt(e.s,t.biff)+":"+Lt(e.e,t.biff):(e.s.rRel?"":"$")+Ht(e.s.r)+":"+(e.e.rRel?"":"$")+Ht(e.e.r):(e.s.cRel?"":"$")+Vt(e.s.c)+":"+(e.e.cRel?"":"$")+Vt(e.e.c)}function Ut(e){return parseInt(e.replace(/\$(\d+)$/,"$1"),10)-1}function Ht(e){return""+(e+1)}function zt(e){for(var t=e.replace(/^\$([A-Z])/,"$1"),r=0,n=0;n!==t.length;++n)r=26*r+t.charCodeAt(n)-64;return r-1}function Vt(e){var t="";for(++e;e;e=Math.floor((e-1)/26))t=String.fromCharCode((e-1)%26+65)+t;return t}function Wt(e){return e.replace(/(\$?[A-Z]*)(\$?\d*)/,"$1,$2").split(",")}function Xt(e){e=Wt(e);return{c:zt(e[0]),r:Ut(e[1])}}function jt(e){return Vt(e.c)+Ht(e.r)}function Gt(e){e=e.split(":").map(Xt);return{s:e[0],e:e[e.length-1]}}function $t(e,t){return void 0===t||"number"==typeof t?$t(e.s,e.e):(e="string"!=typeof e?jt(e):e)==(t="string"!=typeof t?jt(t):t)?e:e+":"+t}function Yt(e){for(var t={s:{c:0,r:0},e:{c:0,r:0}},r=0,n=0,a=0,s=e.length,r=0;n<s&&!((a=e.charCodeAt(n)-64)<1||26<a);++n)r=26*r+a;for(t.s.c=--r,r=0;n<s&&!((a=e.charCodeAt(n)-48)<0||9<a);++n)r=10*r+a;if(t.s.r=--r,n===s||58===e.charCodeAt(++n))return t.e.c=t.s.c,t.e.r=t.s.r,t;for(r=0;n!=s&&!((a=e.charCodeAt(n)-64)<1||26<a);++n)r=26*r+a;for(t.e.c=--r,r=0;n!=s&&!((a=e.charCodeAt(n)-48)<0||9<a);++n)r=10*r+a;return t.e.r=--r,t}function Kt(e,t){var r="d"==e.t&&t instanceof Date;if(null!=e.z)try{return e.w=ue.format(e.z,r?G(t):t)}catch(e){}try{return e.w=ue.format((e.XF||{}).numFmtId||(r?14:0),r?G(t):t)}catch(e){return""+t}}function Zt(e,t,r){return null==e||null==e.t||"z"==e.t?"":void 0!==e.w?e.w:("d"==e.t&&!e.z&&r&&r.dateNF&&(e.z=r.dateNF),Kt(e,null==t?e.v:t))}function Qt(e,t){var r=t&&t.sheet?t.sheet:"Sheet1",t={};return t[r]=e,{SheetNames:[r],Sheets:t}}function Jt(e,t,r){var n=r||{},a=e?Array.isArray(e):n.dense;null!=fe&&null==a&&(a=fe);var s=e||(a?[]:{}),i=0,o=0;s&&null!=n.origin&&("number"==typeof n.origin?i=n.origin:(i=(l="string"==typeof n.origin?Xt(n.origin):n.origin).r,o=l.c));var l,c={s:{c:1e7,r:1e7},e:{c:0,r:0}};s["!ref"]&&(l=Yt(s["!ref"]),c.s.c=l.s.c,c.s.r=l.s.r,c.e.c=Math.max(c.e.c,l.e.c),c.e.r=Math.max(c.e.r,l.e.r),-1==i&&(c.e.r=i=l.e.r+1));for(var f=0;f!=t.length;++f)if(t[f]){if(!Array.isArray(t[f]))throw new Error("aoa_to_sheet expects an array of arrays");for(var h=0;h!=t[f].length;++h)if(void 0!==t[f][h]){var u={v:t[f][h]},d=i+f,p=o+h;if(c.s.r>d&&(c.s.r=d),c.s.c>p&&(c.s.c=p),c.e.r<d&&(c.e.r=d),c.e.c<p&&(c.e.c=p),!t[f][h]||"object"!=typeof t[f][h]||Array.isArray(t[f][h])||t[f][h]instanceof Date)if(Array.isArray(u.v)&&(u.f=t[f][h][1],u.v=u.v[0]),null===u.v)if(u.f)u.t="n";else{if(!n.sheetStubs)continue;u.t="z"}else"number"==typeof u.v?u.t="n":"boolean"==typeof u.v?u.t="b":u.v instanceof Date?(u.z=n.dateNF||ue._table[14],n.cellDates?(u.t="d",u.w=ue.format(u.z,G(u.v))):(u.t="n",u.v=G(u.v),u.w=ue.format(u.z,u.v))):u.t="s";else u=t[f][h];a?(s[d]||(s[d]=[]),s[d][p]=u):s[jt({c:p,r:d})]=u}}return c.s.c<1e7&&(s["!ref"]=$t(c)),s}function qt(e,t){return Jt(null,e,t)}function er(e,t){return(t=t||Rt(4)).write_shift(4,e),t}function tr(e){var t=e.read_shift(4);return 0===t?"":e.read_shift(t,"dbcs")}function rr(e,t){var r=!1;return null==t&&(r=!0,t=Rt(4+2*e.length)),t.write_shift(4,e.length),0<e.length&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}function nr(e,t){var r,n=e.l,a=e.read_shift(1),s=tr(e),i=[],s={t:s,h:s};if(0!=(1&a)){for(var o=e.read_shift(4),l=0;l!=o;++l)i.push({ich:(r=e).read_shift(2),ifnt:r.read_shift(2)});s.r=i}else s.r=[{ich:0,ifnt:0}];return e.l=n+t,s}!function(e,t){var r;if(void 0!==t)r=t;else if("undefined"!=typeof require)try{r=void 0}catch(e){r=null}e.rc4=function(e,t){for(var r=new Array(256),n=0,a=0,s=0,i=0,a=0;256!=a;++a)r[a]=a;for(a=0;256!=a;++a)s=s+r[a]+e[a%e.length].charCodeAt(0)&255,i=r[a],r[a]=r[s],r[s]=i;a=s=0;for(var o=Buffer(t.length),n=0;n!=t.length;++n)s=(s+r[a=a+1&255])%256,i=r[a],r[a]=r[s],r[s]=i,o[n]=t[n]^r[r[a]+r[s]&255];return o},e.md5=function(e){if(!r)throw new Error("Unsupported crypto");return r.createHash("md5").update(e).digest("hex")}}({},"undefined"!=typeof crypto?crypto:void 0);var ar=nr;function sr(e,t){var r,n=!1;return null==t&&(n=!0,t=Rt(23+4*e.t.length)),t.write_shift(1,1),rr(e.t,t),t.write_shift(4,1),r={ich:0,ifnt:0},(e=(e=t)||Rt(4)).write_shift(2,r.ich||0),e.write_shift(2,r.ifnt||0),n?t.slice(0,t.l):t}function ir(e){var t=e.read_shift(4),r=e.read_shift(2);return r+=e.read_shift(1)<<16,e.l++,{c:t,iStyleRef:r}}function or(e,t){return(t=null==t?Rt(8):t).write_shift(-4,e.c),t.write_shift(3,e.iStyleRef||e.s),t.write_shift(1,0),t}var lr=tr,cr=rr;function fr(e){var t=e.read_shift(4);return 0===t||4294967295===t?"":e.read_shift(t,"dbcs")}function hr(e,t){var r=!1;return null==t&&(r=!0,t=Rt(127)),t.write_shift(4,0<e.length?e.length:4294967295),0<e.length&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}var ur=tr,dr=fr,pr=hr;function mr(e){var t=e.slice(e.l,e.l+4),r=1&t[0],n=2&t[0];e.l+=4,t[0]&=252;t=0==n?mt([0,0,0,0,t[0],t[1],t[2],t[3]],0):St(t,0)>>2;return r?t/100:t}function gr(e){var t={s:{},e:{}};return t.s.r=e.read_shift(4),t.e.r=e.read_shift(4),t.s.c=e.read_shift(4),t.e.c=e.read_shift(4),t}var br=gr,vr=function(e,t){return(t=t||Rt(16)).write_shift(4,e.s.r),t.write_shift(4,e.e.r),t.write_shift(4,e.s.c),t.write_shift(4,e.e.c),t};function Er(e){return e.read_shift(8,"f")}function wr(e,t){return(t||Rt(8)).write_shift(8,e,"f")}var Sr={0:"#NULL!",7:"#DIV/0!",15:"#VALUE!",23:"#REF!",29:"#NAME?",36:"#NUM!",42:"#N/A",43:"#GETTING_DATA",255:"#WTF?"},_r=_(Sr);function yr(e,t){if(t=t||Rt(8),!e||e.auto)return t.write_shift(4,0),t.write_shift(4,0),t;e.index?(t.write_shift(1,2),t.write_shift(1,e.index)):e.theme?(t.write_shift(1,6),t.write_shift(1,e.theme)):(t.write_shift(1,5),t.write_shift(1,0));var r=e.tint||0;return 0<r?r*=32767:r<0&&(r*=32768),t.write_shift(2,r),e.rgb?(e=e.rgb||"FFFFFF",t.write_shift(1,parseInt(e.slice(0,2),16)),t.write_shift(1,parseInt(e.slice(2,4),16)),t.write_shift(1,parseInt(e.slice(4,6),16)),t.write_shift(1,255)):(t.write_shift(2,0),t.write_shift(1,0),t.write_shift(1,0)),t}function Cr(e,t){var r=e.read_shift(4);switch(r){case 0:return"";case 4294967295:case 4294967294:return{2:"BITMAP",3:"METAFILEPICT",8:"DIB",14:"ENHMETAFILE"}[e.read_shift(4)]||""}if(400<r)throw new Error("Unsupported Clipboard: "+r.toString(16));return e.l-=4,e.read_shift(0,1==t?"lpstr":"lpwstr")}var Br=12,Tr=[80,81],kr={1:{n:"CodePage",t:2},2:{n:"Category",t:80},3:{n:"PresentationFormat",t:80},4:{n:"ByteCount",t:3},5:{n:"LineCount",t:3},6:{n:"ParagraphCount",t:3},7:{n:"SlideCount",t:3},8:{n:"NoteCount",t:3},9:{n:"HiddenCount",t:3},10:{n:"MultimediaClipCount",t:3},11:{n:"ScaleCrop",t:11},12:{n:"HeadingPairs",t:4096|Br},13:{n:"TitlesOfParts",t:4126},14:{n:"Manager",t:80},15:{n:"Company",t:80},16:{n:"LinksUpToDate",t:11},17:{n:"CharacterCount",t:3},19:{n:"SharedDoc",t:11},22:{n:"HyperlinksChanged",t:11},23:{n:"AppVersion",t:3,p:"version"},24:{n:"DigSig",t:65},26:{n:"ContentType",t:80},27:{n:"ContentStatus",t:80},28:{n:"Language",t:80},29:{n:"Version",t:80},255:{}},xr={1:{n:"CodePage",t:2},2:{n:"Title",t:80},3:{n:"Subject",t:80},4:{n:"Author",t:80},5:{n:"Keywords",t:80},6:{n:"Comments",t:80},7:{n:"Template",t:80},8:{n:"LastAuthor",t:80},9:{n:"RevNumber",t:80},10:{n:"EditTime",t:64},11:{n:"LastPrinted",t:64},12:{n:"CreatedDate",t:64},13:{n:"ModifiedDate",t:64},14:{n:"PageCount",t:3},15:{n:"WordCount",t:3},16:{n:"CharCount",t:3},17:{n:"Thumbnail",t:71},18:{n:"Application",t:80},19:{n:"DocSecurity",t:3},255:{}},Ar={2147483648:{n:"Locale",t:19},2147483651:{n:"Behavior",t:19},1919054434:{}};!function(){for(var e in Ar)Ar.hasOwnProperty(e)&&(kr[e]=xr[e]=Ar[e])}();var Ir=w(kr,"n"),Rr=w(xr,"n"),Or={1:"US",2:"CA",3:"",7:"RU",20:"EG",30:"GR",31:"NL",32:"BE",33:"FR",34:"ES",36:"HU",39:"IT",41:"CH",43:"AT",44:"GB",45:"DK",46:"SE",47:"NO",48:"PL",49:"DE",52:"MX",55:"BR",61:"AU",64:"NZ",66:"TH",81:"JP",82:"KR",84:"VN",86:"CN",90:"TR",105:"JS",213:"DZ",216:"MA",218:"LY",351:"PT",354:"IS",358:"FI",420:"CZ",886:"TW",961:"LB",962:"JO",963:"SY",964:"IQ",965:"KW",966:"SA",971:"AE",972:"IL",974:"QA",981:"IR",65535:"US"},Fr=[null,"solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"];var Dr,Pr=[0,16777215,16711680,65280,255,16776960,16711935,65535,0,16777215,16711680,65280,255,16776960,16711935,65535,8388608,32768,128,8421376,8388736,32896,12632256,8421504,10066431,10040166,16777164,13434879,6684774,16744576,26316,13421823,128,16711935,16776960,65535,8388736,8388608,32896,255,52479,13434879,13434828,16777113,10079487,16751052,13408767,16764057,3368703,3394764,10079232,16763904,16750848,16737792,6710937,9868950,13158,3381606,13056,3355392,10040064,10040166,3355545,3355443,16777215,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0].map(function(e){return[e>>16&255,e>>8&255,255&e]}),Nr={"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":"workbooks","application/vnd.ms-excel.binIndexWs":"TODO","application/vnd.ms-excel.intlmacrosheet":"TODO","application/vnd.ms-excel.binIndexMs":"TODO","application/vnd.openxmlformats-package.core-properties+xml":"coreprops","application/vnd.openxmlformats-officedocument.custom-properties+xml":"custprops","application/vnd.openxmlformats-officedocument.extended-properties+xml":"extprops","application/vnd.openxmlformats-officedocument.customXmlProperties+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.customProperty":"TODO","application/vnd.ms-excel.pivotTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotTable+xml":"TODO","application/vnd.ms-office.chartcolorstyle+xml":"TODO","application/vnd.ms-office.chartstyle+xml":"TODO","application/vnd.ms-excel.calcChain":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.calcChain+xml":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.printerSettings":"TODO","application/vnd.ms-office.activeX":"TODO","application/vnd.ms-office.activeX+xml":"TODO","application/vnd.ms-excel.attachedToolbars":"TODO","application/vnd.ms-excel.connections":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":"TODO","application/vnd.ms-excel.externalLink":"links","application/vnd.openxmlformats-officedocument.spreadsheetml.externalLink+xml":"links","application/vnd.ms-excel.sheetMetadata":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml":"TODO","application/vnd.ms-excel.pivotCacheDefinition":"TODO","application/vnd.ms-excel.pivotCacheRecords":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheDefinition+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheRecords+xml":"TODO","application/vnd.ms-excel.queryTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.queryTable+xml":"TODO","application/vnd.ms-excel.userNames":"TODO","application/vnd.ms-excel.revisionHeaders":"TODO","application/vnd.ms-excel.revisionLog":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionHeaders+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionLog+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.userNames+xml":"TODO","application/vnd.ms-excel.tableSingleCells":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.tableSingleCells+xml":"TODO","application/vnd.ms-excel.slicer":"TODO","application/vnd.ms-excel.slicerCache":"TODO","application/vnd.ms-excel.slicer+xml":"TODO","application/vnd.ms-excel.slicerCache+xml":"TODO","application/vnd.ms-excel.wsSortMap":"TODO","application/vnd.ms-excel.table":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":"TODO","application/vnd.openxmlformats-officedocument.theme+xml":"themes","application/vnd.openxmlformats-officedocument.themeOverride+xml":"TODO","application/vnd.ms-excel.Timeline+xml":"TODO","application/vnd.ms-excel.TimelineCache+xml":"TODO","application/vnd.ms-office.vbaProject":"vba","application/vnd.ms-office.vbaProjectSignature":"vba","application/vnd.ms-office.volatileDependencies":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.volatileDependencies+xml":"TODO","application/vnd.ms-excel.controlproperties+xml":"TODO","application/vnd.openxmlformats-officedocument.model+data":"TODO","application/vnd.ms-excel.Survey+xml":"TODO","application/vnd.openxmlformats-officedocument.drawing+xml":"drawings","application/vnd.openxmlformats-officedocument.drawingml.chart+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramColors+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramData+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramLayout+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramStyle+xml":"TODO","application/vnd.openxmlformats-officedocument.vmlDrawing":"TODO","application/vnd.openxmlformats-package.relationships+xml":"rels","application/vnd.openxmlformats-officedocument.oleObject":"TODO","image/png":"TODO",sheet:"js"},Lr=(de(Dr={workbooks:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml",xlsm:"application/vnd.ms-excel.sheet.macroEnabled.main+xml",xlsb:"application/vnd.ms-excel.sheet.binary.macroEnabled.main",xlam:"application/vnd.ms-excel.addin.macroEnabled.main+xml",xltx:"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml"},strs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml",xlsb:"application/vnd.ms-excel.sharedStrings"},comments:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml",xlsb:"application/vnd.ms-excel.comments"},sheets:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml",xlsb:"application/vnd.ms-excel.worksheet"},charts:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml",xlsb:"application/vnd.ms-excel.chartsheet"},dialogs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml",xlsb:"application/vnd.ms-excel.dialogsheet"},macros:{xlsx:"application/vnd.ms-excel.macrosheet+xml",xlsb:"application/vnd.ms-excel.macrosheet"},styles:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml",xlsb:"application/vnd.ms-excel.styles"}}).forEach(function(t){["xlsm","xlam"].forEach(function(e){Dr[t][e]||(Dr[t][e]=Dr[t].xlsx)})}),de(Dr).forEach(function(t){de(Dr[t]).forEach(function(e){Nr[Dr[t][e]]=t})}),Dr),Mr=function(e){for(var t=[],r=de(e),n=0;n!==r.length;++n)null==t[e[r[n]]]&&(t[e[r[n]]]=[]),t[e[r[n]]].push(r[n]);return t}(Nr);function Ur(){return{workbooks:[],sheets:[],charts:[],dialogs:[],macros:[],rels:[],strs:[],comments:[],links:[],coreprops:[],extprops:[],custprops:[],themes:[],styles:[],calcchains:[],vba:[],drawings:[],TODO:[],xmlns:""}}Ye.CT="http://schemas.openxmlformats.org/package/2006/content-types";var Hr=Ge("Types",null,{xmlns:Ye.CT,"xmlns:xsd":Ye.xsd,"xmlns:xsi":Ye.xsi}),zr=[["xml","application/xml"],["bin","application/vnd.ms-excel.sheet.binary.macroEnabled.main"],["vml","application/vnd.openxmlformats-officedocument.vmlDrawing"],["bmp","image/bmp"],["png","image/png"],["gif","image/gif"],["emf","image/x-emf"],["wmf","image/x-wmf"],["jpg","image/jpeg"],["jpeg","image/jpeg"],["tif","image/tiff"],["tiff","image/tiff"],["pdf","application/pdf"],["rels",Mr.rels[0]]].map(function(e){return Ge("Default",null,{Extension:e[0],ContentType:e[1]})});function Vr(r,n){var t;(i=[])[i.length]=z,i[i.length]=Hr;function e(e){r[e]&&0<r[e].length&&(t=r[e][0],i[i.length]=Ge("Override",null,{PartName:("/"==t[0]?"":"/")+t,ContentType:Lr[e][n.bookType||"xlsx"]}))}function a(t){(r[t]||[]).forEach(function(e){i[i.length]=Ge("Override",null,{PartName:("/"==e[0]?"":"/")+e,ContentType:Lr[t][n.bookType||"xlsx"]})})}function s(t){(r[t]||[]).forEach(function(e){i[i.length]=Ge("Override",null,{PartName:("/"==e[0]?"":"/")+e,ContentType:Mr[t][0]})})}var i=i.concat(zr);return e("workbooks"),a("sheets"),a("charts"),s("themes"),["strs","styles"].forEach(e),["coreprops","extprops","custprops"].forEach(s),s("vba"),s("comments"),s("drawings"),2<i.length&&(i[i.length]="</Types>",i[1]=i[1].replace("/>",">")),i.join("")}var Wr={WB:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument",SHEET:"http://sheetjs.openxmlformats.org/officeDocument/2006/relationships/officeDocument",HLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink",VML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/vmlDrawing",VBA:"http://schemas.microsoft.com/office/2006/relationships/vbaProject"};function Xr(e){var t=e.lastIndexOf("/");return e.slice(0,t+1)+"_rels/"+e.slice(t+1)+".rels"}function jr(e,n){if(!e)return e;"/"!==n.charAt(0)&&(n="/"+n);var a={},s={};return(e.match(W)||[]).forEach(function(e){var t,r=pe(e);"<Relationship"===r[0]&&((t={}).Type=r.Type,t.Target=r.Target,t.Id=r.Id,t.TargetMode=r.TargetMode,e="External"===r.TargetMode?r.Target:H(r.Target,n),a[e]=t,s[r.Id]=t)}),a["!id"]=s,a}Ye.RELS="http://schemas.openxmlformats.org/package/2006/relationships";var Gr=Ge("Relationships",null,{xmlns:Ye.RELS});function $r(t){var r=[z,Gr];return de(t["!id"]).forEach(function(e){r[r.length]=Ge("Relationship",null,t["!id"][e])}),2<r.length&&(r[r.length]="</Relationships>",r[1]=r[1].replace("/>",">")),r.join("")}function Yr(e,t,r,n,a){if(a=a||{},e["!id"]||(e["!id"]={}),t<0)for(t=1;e["!id"]["rId"+t];++t);if(a.Id="rId"+t,a.Type=n,a.Target=r,a.Type==Wr.HLINK&&(a.TargetMode="External"),e["!id"][a.Id])throw new Error("Cannot rewrite rId "+t);return e["!id"][a.Id]=a,e[("/"+a.Target).replace("//","/")]=a,t}var Kr="application/vnd.oasis.opendocument.spreadsheet";function Zr(e,t,r){return['  <rdf:Description rdf:about="'+e+'">\n','    <rdf:type rdf:resource="http://docs.oasis-open.org/ns/office/1.2/meta/'+(r||"odf")+"#"+t+'"/>\n',"  </rdf:Description>\n"].join("")}var Qr,Jr=(Qr='<?xml version="1.0" encoding="UTF-8" standalone="yes"?><office:document-meta xmlns:office="urn:oasis:names:tc:opendocument:xmlns:office:1.0" xmlns:meta="urn:oasis:names:tc:opendocument:xmlns:meta:1.0" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:xlink="http://www.w3.org/1999/xlink" office:version="1.2"><office:meta><meta:generator>SheetJS '+n.version+"</meta:generator></office:meta></office:document-meta>",function(){return Qr}),qr=[["cp:category","Category"],["cp:contentStatus","ContentStatus"],["cp:keywords","Keywords"],["cp:lastModifiedBy","LastAuthor"],["cp:lastPrinted","LastPrinted"],["cp:revision","RevNumber"],["cp:version","Version"],["dc:creator","Author"],["dc:description","Comments"],["dc:identifier","Identifier"],["dc:language","Language"],["dc:subject","Subject"],["dc:title","Title"],["dcterms:created","CreatedDate","date"],["dcterms:modified","ModifiedDate","date"]];Ye.CORE_PROPS="http://schemas.openxmlformats.org/package/2006/metadata/core-properties",Wr.CORE_PROPS="http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties";var en=function(){for(var e=new Array(qr.length),t=0;t<qr.length;++t){var r=qr[t],r="(?:"+r[0].slice(0,r[0].indexOf(":"))+":)"+r[0].slice(r[0].indexOf(":")+1);e[t]=new RegExp("<"+r+"[^>]*>([\\s\\S]*?)</"+r+">")}return e}();function tn(e){var t={};e=Oe(e);for(var r=0;r<qr.length;++r){var n=qr[r],a=e.match(en[r]);null!=a&&0<a.length&&(t[n[1]]=a[1]),"date"===n[2]&&t[n[1]]&&(t[n[1]]=oe(t[n[1]]))}return t}var rn=Ge("cp:coreProperties",null,{"xmlns:cp":Ye.CORE_PROPS,"xmlns:dc":Ye.dc,"xmlns:dcterms":Ye.dcterms,"xmlns:dcmitype":Ye.dcmitype,"xmlns:xsi":Ye.xsi});function nn(e,t,r,n,a){null==a[e]&&null!=t&&""!==t&&(a[e]=t,n[n.length]=r?Ge(e,t,r):Xe(e,t))}var an=[["Application","Application","string"],["AppVersion","AppVersion","string"],["Company","Company","string"],["DocSecurity","DocSecurity","string"],["Manager","Manager","string"],["HyperlinksChanged","HyperlinksChanged","bool"],["SharedDoc","SharedDoc","bool"],["LinksUpToDate","LinksUpToDate","bool"],["ScaleCrop","ScaleCrop","bool"],["HeadingPairs","HeadingPairs","raw"],["TitlesOfParts","TitlesOfParts","raw"]];Ye.EXT_PROPS="http://schemas.openxmlformats.org/officeDocument/2006/extended-properties",Wr.EXT_PROPS="http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties";function sn(e,t,r,n){var a=[];if("string"==typeof e)a=Ve(e,n);else for(var s=0;s<e.length;++s)a=a.concat(e[s].map(function(e){return{v:e}}));var i,o="string"==typeof t?Ve(t,n).map(function(e){return e.v}):t,l=0;if(0<o.length)for(var c=0;c!==a.length;c+=2){switch(i=+a[c+1].v,a[c].v){case"Worksheets":case"工作表":case"Листы":case"أوراق العمل":case"ワークシート":case"גליונות עבודה":case"Arbeitsblätter":case"Çalışma Sayfaları":case"Feuilles de calcul":case"Fogli di lavoro":case"Folhas de cálculo":case"Planilhas":case"Regneark":case"Werkbladen":r.Worksheets=i,r.SheetNames=o.slice(l,l+i);break;case"Named Ranges":case"名前付き一覧":case"Benannte Bereiche":case"Navngivne områder":r.NamedRanges=i,r.DefinedNames=o.slice(l,l+i);break;case"Charts":case"Diagramme":r.Chartsheets=i,r.ChartNames=o.slice(l,l+i)}l+=i}}var on=Ge("Properties",null,{xmlns:Ye.EXT_PROPS,"xmlns:vt":Ye.vt});Ye.CUST_PROPS="http://schemas.openxmlformats.org/officeDocument/2006/custom-properties",Wr.CUST_PROPS="http://schemas.openxmlformats.org/officeDocument/2006/relationships/custom-properties";var ln=/<[^>]+>[^<]*/g;var cn=Ge("Properties",null,{xmlns:Ye.CUST_PROPS,"xmlns:vt":Ye.vt});function fn(t){var r=[z,cn];if(!t)return r.join("");var n=1;return de(t).forEach(function(e){++n,r[r.length]=Ge("property",function(e){switch(typeof e){case"string":return Ge("vt:lpwstr",e);case"number":return Ge((0|e)==e?"vt:i4":"vt:r8",String(e));case"boolean":return Ge("vt:bool",e?"true":"false")}if(e instanceof Date)return Ge("vt:filetime",$e(e));throw new Error("Unable to serialize "+e)}(t[e]),{fmtid:"{D5CDD505-2E9C-101B-9397-08002B2CF9AE}",pid:n,name:e})}),2<r.length&&(r[r.length]="</Properties>",r[1]=r[1].replace("/>",">")),r.join("")}var hn={Title:"Title",Subject:"Subject",Author:"Author",Keywords:"Keywords",Comments:"Description",LastAuthor:"LastAuthor",RevNumber:"Revision",Application:"AppName",LastPrinted:"LastPrinted",CreatedDate:"Created",ModifiedDate:"LastSaved",Category:"Category",Manager:"Manager",Company:"Company",AppVersion:"Version",ContentStatus:"ContentStatus",Identifier:"Identifier",Language:"Language"},un=S(hn);function dn(e){var t=e.read_shift(4),e=e.read_shift(4);return new Date(1e3*(e/1e7*Math.pow(2,32)+t/1e7-11644473600)).toISOString().replace(/\.000/,"")}function pn(e,t,r){var n=e.l,a=e.read_shift(0,"lpstr-cp");if(r)for(;e.l-n&3;)++e.l;return a}function mn(e,t,r){var n=e.read_shift(0,"lpwstr");return r&&(e.l+=4-(n.length+1&3)&3),n}function gn(e,t,r){return 31===t?mn(e):pn(e,0,r)}function bn(e,t,r){return gn(e,t,!1===r?0:4)}function vn(e){return function(e){for(var t=e.read_shift(4),r=[],n=0;n!=t;++n)r[n]=e.read_shift(0,"lpstr-cp").replace(q,"");return r}(e)}function En(e){for(var t,r=e.read_shift(4),n=[],a=0;a!=r/2;++a)n.push([_n(t=e,81),_n(t,3)]);return n}function wn(e,t){for(var r=e.read_shift(4),n={},a=0;a!=r;++a){var s=e.read_shift(4),i=e.read_shift(4);n[s]=e.read_shift(i,1200===t?"utf16le":"utf8").replace(q,"").replace(re,"!"),1200===t&&i%2&&(e.l+=2)}return 3&e.l&&(e.l=e.l>>3<<2),n}function Sn(e){var t=e.read_shift(4),r=e.slice(e.l,e.l+t);return e.l+=t,0<(3&t)&&(e.l+=4-(3&t)&3),r}function _n(e,t,r){var n,a,s=e.read_shift(2),i=r||{};if(e.l+=2,t!==Br&&s!==t&&-1===Tr.indexOf(t))throw new Error("Expected type "+t+" saw "+s);switch(t===Br?s:t){case 2:return n=e.read_shift(2,"i"),i.raw||(e.l+=2),n;case 3:return n=e.read_shift(4,"i");case 11:return 0!==e.read_shift(4);case 19:return n=e.read_shift(4);case 30:return pn(e,0,4).replace(q,"");case 31:return mn(e);case 64:return dn(e);case 65:return Sn(e);case 71:return(a={}).Size=(n=e).read_shift(4),n.l+=a.Size+3-(a.Size-1)%4,a;case 80:return bn(e,s,!i.raw).replace(q,"");case 81:return function(e,t){if(!t)throw new Error("VtUnalignedString must have positive length");return gn(e,t,0)}(e,s).replace(q,"");case 4108:return En(e);case 4126:return vn(e);default:throw new Error("TypedPropertyValue unrecognized type "+t+" "+s)}}function yn(e,t){var r,n,a,s=Rt(4),i=Rt(4);switch(s.write_shift(4,80==e?31:e),e){case 3:i.write_shift(-4,t);break;case 5:(i=Rt(8)).write_shift(8,t,"f");break;case 11:i.write_shift(4,t?1:0);break;case 64:n=("string"==typeof(r=t)?new Date(Date.parse(r)):r).getTime()/1e3+11644473600,a=n%Math.pow(2,32),r=(n-a)/Math.pow(2,32),r*=1e7,0<(n=(a*=1e7)/Math.pow(2,32)|0)&&(a%=Math.pow(2,32),r+=n),(n=Rt(8)).write_shift(4,a),n.write_shift(4,r),i=n;break;case 31:case 80:for((i=Rt(4+2*(t.length+1)+(t.length%2?0:2))).write_shift(4,t.length+1),i.write_shift(0,t,"dbcs");i.l!=i.length;)i.write_shift(1,0);break;default:throw new Error("TypedPropertyValue unrecognized type "+e+" "+t)}return he([s,i])}function Cn(e,t){for(var r=e.l,n=e.read_shift(4),a=e.read_shift(4),s=[],i=0,o=0,l=-1,c={},i=0;i!=a;++i){var f=e.read_shift(4),h=e.read_shift(4);s[i]=[f,h+r]}s.sort(function(e,t){return e[1]-t[1]});var u={};for(i=0;i!=a;++i){if(e.l!==s[i][1]){var d=!0;if(0<i&&t)switch(t[s[i-1][0]].t){case 2:e.l+2===s[i][1]&&(e.l+=2,d=!1);break;case 80:case 4108:e.l<=s[i][1]&&(e.l=s[i][1],d=!1)}if((!t||0==i)&&e.l<=s[i][1]&&(d=!1,e.l=s[i][1]),d)throw new Error("Read Error: Expected address "+s[i][1]+" at "+e.l+" :"+i)}if(t){var p=t[s[i][0]];if(u[p.n]=_n(e,p.t,{raw:!0}),"version"===p.p&&(u[p.n]=String(u[p.n]>>16)+"."+("0000"+String(65535&u[p.n])).slice(-4)),"CodePage"==p.n)switch(u[p.n]){case 0:u[p.n]=1252;case 874:case 932:case 936:case 949:case 950:case 1250:case 1251:case 1253:case 1254:case 1255:case 1256:case 1257:case 1258:case 1e4:case 1200:case 1201:case 1252:case 65e3:case-536:case 65001:case-535:ce(o=u[p.n]>>>0&65535);break;default:throw new Error("Unsupported CodePage: "+u[p.n])}}else if(1===s[i][0]){o=u.CodePage=_n(e,2);ce(o),-1!==l&&(g=e.l,e.l=s[l][1],c=wn(e,o),e.l=g)}else if(0===s[i][0])0!==o?c=wn(e,o):(l=i,e.l=s[i+1][1]);else{var m,g=c[s[i][0]];switch(e[e.l]){case 65:e.l+=4,m=Sn(e);break;case 30:case 31:e.l+=4,m=bn(e,e[e.l-4]).replace(/\u0000+$/,"");break;case 3:e.l+=4,m=e.read_shift(4,"i");break;case 19:e.l+=4,m=e.read_shift(4);break;case 5:e.l+=4,m=e.read_shift(8,"f");break;case 11:e.l+=4,m=In(e,4);break;case 64:e.l+=4,m=oe(dn(e));break;default:throw new Error("unparsed value: "+e[e.l])}u[g]=m}}return e.l=r+n,u}var Bn=["CodePage","Thumbnail","_PID_LINKBASE","_PID_HLINKS","SystemIdentifier","FMTID"].concat(["Worksheets","SheetNames","NamedRanges","DefinedNames","Chartsheets","ChartNames"]);function Tn(e,t,r){var n,a,s,i,o=Rt(8),l=[],c=[],f=8,h=0,u=Rt(8),d=Rt(8);if(u.write_shift(4,2),u.write_shift(4,1200),d.write_shift(4,1),c.push(u),l.push(d),f+=8+u.length,!t){(d=Rt(8)).write_shift(4,0),l.unshift(d);var p=[Rt(4)];for(p[0].write_shift(4,e.length),h=0;h<e.length;++h){var m=e[h][0];for((u=Rt(8+2*(m.length+1)+(m.length%2?0:2))).write_shift(4,h+2),u.write_shift(4,m.length+1),u.write_shift(0,m,"dbcs");u.l!=u.length;)u.write_shift(1,0);p.push(u)}u=he(p),c.unshift(u),f+=8+u.length}for(h=0;h<e.length;++h)t&&!t[e[h][0]]||-1<Bn.indexOf(e[h][0])||null!=e[h][1]&&(s=e[h][1],n=0,u=t?("version"==(i=r[n=+t[e[h][0]]]).p&&"string"==typeof s&&(s=(+(a=s.split("."))[0]<<16)+(+a[1]||0)),yn(i.t,s)):(-1==(i=function(e){switch(typeof e){case"boolean":return 11;case"number":return(0|e)==e?3:5;case"string":return 31;case"object":if(e instanceof Date)return 64}return-1}(s))&&(i=31,s=String(s)),yn(i,s)),c.push(u),(d=Rt(8)).write_shift(4,t?n:2+h),l.push(d),f+=8+u.length);for(var g=8*(c.length+1),h=0;h<c.length;++h)l[h].write_shift(4,g),g+=c[h].length;return o.write_shift(4,f),o.write_shift(4,c.length),he([o].concat(l).concat(c))}function kn(e,t,r){var n=e.content;if(!n)return{};At(n,0);var a,s=0;n.chk("feff","Byte Order: "),n.read_shift(2);var i=n.read_shift(4),o=n.read_shift(16);if(o!==ie.utils.consts.HEADER_CLSID&&o!==r)throw new Error("Bad PropertySet CLSID "+o);if(1!==(e=n.read_shift(4))&&2!==e)throw new Error("Unrecognized #Sets: "+e);if(r=n.read_shift(16),o=n.read_shift(4),1===e&&o!==n.l)throw new Error("Length mismatch: "+o+" !== "+n.l);2===e&&(a=n.read_shift(16),s=n.read_shift(4));var l,c,f=Cn(n,t),h={SystemIdentifier:i};for(l in f)h[l]=f[l];if(h.FMTID=r,1===e)return h;if(s-n.l==2&&(n.l+=2),n.l!==s)throw new Error("Length mismatch 2: "+n.l+" !== "+s);try{c=Cn(n,null)}catch(e){}for(l in c)h[l]=c[l];return h.FMTID=[r,a],h}function xn(e,t,r,n,a,s){var i=Rt(a?68:48),o=[i];i.write_shift(2,65534),i.write_shift(2,0),i.write_shift(4,842412599),i.write_shift(16,ie.utils.consts.HEADER_CLSID,"hex"),i.write_shift(4,a?2:1),i.write_shift(16,t,"hex"),i.write_shift(4,a?68:48);n=Tn(e,r,n);return o.push(n),a&&(a=Tn(a,null,null),i.write_shift(16,s,"hex"),i.write_shift(4,68+n.length),o.push(a)),he(o)}function An(e,t){return e.read_shift(t),null}function In(e,t){return 1===e.read_shift(t)}function Rn(e,t){return(t=t||Rt(2)).write_shift(2,+!!e),t}function On(e){return e.read_shift(2,"u")}function Fn(e,t){return(t=t||Rt(2)).write_shift(2,e),t}function Dn(e,t){return function(e,t,r){for(var n=[],a=e.l+t;e.l<a;)n.push(r(e,a-e.l));if(a!==e.l)throw new Error("Slurp error");return n}(e,t,On)}function Pn(e,t,r){var n=e.read_shift(r&&12<=r.biff?2:1),a="sbcs-cont",s=f;r&&8<=r.biff&&(f=1200),r&&8!=r.biff?12==r.biff&&(a="wstr"):e.read_shift(1)&&(a="dbcs-cont"),2<=r.biff&&r.biff<=5&&(a="cpstr");a=n?e.read_shift(n,a):"";return f=s,a}function Nn(e,t,r){if(r){if(2<=r.biff&&r.biff<=5)return e.read_shift(t,"cpstr");if(12<=r.biff)return e.read_shift(t,"dbcs-cont")}return 0===e.read_shift(1)?e.read_shift(t,"sbcs-cont"):e.read_shift(t,"dbcs-cont")}function Ln(e,t,r){var n=e.read_shift(r&&2==r.biff?1:2);return 0===n?(e.l++,""):Nn(e,n,r)}function Mn(e,t,r){if(5<r.biff)return Ln(e,0,r);var n=e.read_shift(1);return 0===n?(e.l++,""):e.read_shift(n,r.biff<=4||!e.lens?"cpstr":"sbcs-cont")}function Un(e,t,r){return(r=r||Rt(3+2*e.length)).write_shift(2,e.length),r.write_shift(1,1),r.write_shift(31,e,"utf16le"),r}function Hn(e){var t,r,n,a,s=e.read_shift(16);switch(s){case"e0c9ea79f9bace118c8200aa004ba90b":return r=(t=e).read_shift(4),n=t.l,a=!1,24<r&&(t.l+=r-24,"795881f43b1d7f48af2c825dc4852763"===t.read_shift(16)&&(a=!0),t.l=n),r=t.read_shift((a?r-24:r)>>1,"utf16le").replace(q,""),a&&(t.l+=24),r;case"0303000000000000c000000000000046":return function(e){e.l+=2;var t=e.read_shift(0,"lpstr-ansi");if(e.l+=2,57005!=e.read_shift(2))throw new Error("Bad FileMoniker");if(0===e.read_shift(4))return t.replace(/\\/g,"/");if(t=e.read_shift(4),3!=e.read_shift(2))throw new Error("Bad FileMoniker");return e.read_shift(t>>1,"utf16le").replace(q,"")}(e);default:throw new Error("Unsupported Moniker "+s)}}function zn(e){var t=e.read_shift(4);return 0<t?e.read_shift(t,"utf16le").replace(q,""):""}function Vn(e){return[e.read_shift(1),e.read_shift(1),e.read_shift(1),e.read_shift(1)]}function Wn(e){e=Vn(e);return e[3]=0,e}function Xn(e){return{r:e.read_shift(2),c:e.read_shift(2),ixfe:e.read_shift(2)}}function jn(e,t,r,n){return(n=n||Rt(6)).write_shift(2,e),n.write_shift(2,t),n.write_shift(2,r||0),n}function Gn(e){return[e.read_shift(2),mr(e)]}function $n(e){var t=e.read_shift(2),r=e.read_shift(2);return{s:{c:e.read_shift(2),r:t},e:{c:e.read_shift(2),r:r}}}function Yn(e,t){return(t=t||Rt(8)).write_shift(2,e.s.r),t.write_shift(2,e.e.r),t.write_shift(2,e.s.c),t.write_shift(2,e.e.c),t}function Kn(e){var t=e.read_shift(2),r=e.read_shift(2);return{s:{c:e.read_shift(1),r:t},e:{c:e.read_shift(1),r:r}}}var Zn=Kn;function Qn(e){e.l+=4;var t=e.read_shift(2),r=e.read_shift(2),n=e.read_shift(2);return e.l+=12,[r,t,n]}function Jn(e){e.l+=2,e.l+=e.read_shift(2)}var qn={0:Jn,4:Jn,5:Jn,6:Jn,7:function(e){return e.l+=4,e.cf=e.read_shift(2),{}},8:Jn,9:Jn,10:Jn,11:Jn,12:Jn,13:function(e){var t={};return e.l+=4,e.l+=16,t.fSharedNote=e.read_shift(2),e.l+=4,t},14:Jn,15:Jn,16:Jn,17:Jn,18:Jn,19:Jn,20:Jn,21:Qn};function ea(e,t){var r={BIFFVer:0,dt:0};switch(r.BIFFVer=e.read_shift(2),2<=(t-=2)&&(r.dt=e.read_shift(2),e.l-=2),r.BIFFVer){case 1536:case 1280:case 1024:case 768:case 512:case 2:case 7:break;default:if(6<t)throw new Error("Unexpected BIFF Ver "+r.BIFFVer)}return e.read_shift(t),r}function ta(e,t,r){var n=1536,a=16;switch(r.bookType){case"biff8":break;case"biff5":n=1280,a=8;break;case"biff4":n=4,a=6;break;case"biff3":n=3,a=6;break;case"biff2":n=2,a=4;break;case"xla":break;default:throw new Error("unsupported BIFF version")}r=Rt(a);return r.write_shift(2,n),r.write_shift(2,t),4<a&&r.write_shift(2,29282),6<a&&r.write_shift(2,1997),8<a&&(r.write_shift(2,49161),r.write_shift(2,1),r.write_shift(2,1798),r.write_shift(2,0)),r}function ra(e,t,r){var n=0;r&&2==r.biff||(n=e.read_shift(2));e=e.read_shift(2);return r&&2==r.biff&&(n=1-(e>>15),e&=32767),[{Unsynced:1&n,DyZero:(2&n)>>1,ExAsc:(4&n)>>2,ExDsc:(8&n)>>3},e]}var na=Mn;function aa(e,t,r){var n=e.l+t,a=8!=r.biff&&r.biff?2:4,s=e.read_shift(a),t=e.read_shift(a),r=e.read_shift(2),a=e.read_shift(2);return e.l=n,{s:{r:s,c:r},e:{r:t,c:a}}}function sa(e,t,r,n){r=r&&5==r.biff;return(n=n||Rt(r?16:20)).write_shift(2,0),e.style?(n.write_shift(2,e.numFmtId||0),n.write_shift(2,65524)):(n.write_shift(2,e.numFmtId||0),n.write_shift(2,t<<4)),n.write_shift(4,0),n.write_shift(4,0),r||n.write_shift(4,0),n.write_shift(2,0),n}function ia(e,t,r){var n=Xn(e);2==r.biff&&++e.l;e=(r=e).read_shift(1),e=1===r.read_shift(1)?e:1===e;return n.val=e,n.t=!0===e||!1===e?"b":"e",n}function oa(e,t,r,n,a,s){var i=Rt(8);return jn(e,t,n,i),n=r,r=s,(s=(s=i)||Rt(2)).write_shift(1,+n),s.write_shift(1,"e"==r?1:0),i}function la(e,t,r){return 0===t?"":Mn(e,0,r)}function ca(e,t,r){var n,a=e.read_shift(2),a={fBuiltIn:1&a,fWantAdvise:a>>>1&1,fWantPict:a>>>2&1,fOle:a>>>3&1,fOleLink:a>>>4&1,cf:a>>>5&1023,fIcon:a>>>15&1};return 14849===r.sbcch&&(n=function(e,t,r){e.l+=4;var n=e.l+(t-=4),t=Pn(e,0,r);if((r=e.read_shift(2))!==(n-=e.l))throw new Error("Malformed AddinUdf: padding = "+n+" != "+r);return e.l+=r,t}(e,t-2,r)),a.body=n||e.read_shift(t-2),"string"==typeof n&&(a.Name=n),a}var fa=["_xlnm.Consolidate_Area","_xlnm.Auto_Open","_xlnm.Auto_Close","_xlnm.Extract","_xlnm.Database","_xlnm.Criteria","_xlnm.Print_Area","_xlnm.Print_Titles","_xlnm.Recorder","_xlnm.Data_Form","_xlnm.Auto_Activate","_xlnm.Auto_Deactivate","_xlnm.Sheet_Title","_xlnm._FilterDatabase"];function ha(e,t,r){var n=e.l+t,a=e.read_shift(2),s=e.read_shift(1),i=e.read_shift(1),o=e.read_shift(r&&2==r.biff?1:2),t=0;(!r||5<=r.biff)&&(5!=r.biff&&(e.l+=2),t=e.read_shift(2),5==r.biff&&(e.l+=2),e.l+=4);i=Nn(e,i,r);32&a&&(i=fa[i.charCodeAt(0)]);a=n-e.l;return r&&2==r.biff&&--a,{chKey:s,Name:i,itab:t,rgce:n==e.l||0===o?[]:function(e,t,r,n){var a,t=e.l+t,n=So(e,n,r);t!==e.l&&(a=wo(e,t-e.l,n,r));return[n,a]}(e,a,r,o)}}function ua(e,t,r){if(r.biff<8)return function(e,t){3==e[e.l+1]&&e[e.l]++;t=Pn(e,0,t);return 3==t.charCodeAt(0)?t.slice(1):t}(e,r);for(var n,a,s=[],t=e.l+t,i=e.read_shift(8<r.biff?4:2);0!=i--;)s.push((n=e,r.biff,a=8<(a=r).biff?4:2,[n.read_shift(a),n.read_shift(a,"i"),n.read_shift(a,"i")]));if(e.l!=t)throw new Error("Bad ExternSheet: "+e.l+" != "+t);return s}function da(e,t,r){var n=Zn(e,6);switch(r.biff){case 2:e.l++,t-=7;break;case 3:case 4:e.l+=2,t-=8;break;default:e.l+=6,t-=12}return[n,function(e,t,r){var n,a=e.l+t,s=2==r.biff?1:2,i=e.read_shift(s);if(65535==i)return[[],It(e,t-2)];var o=So(e,i,r);t!==i+s&&(n=wo(e,t-i-s,o,r));return e.l=a,[o,n]}(e,t,r)]}var pa=[];function ma(e){var t=Rt(24),r=Xt(e[0]);t.write_shift(2,r.r),t.write_shift(2,r.r),t.write_shift(2,r.c),t.write_shift(2,r.c);for(var n="d0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" "),a=0;a<16;++a)t.write_shift(1,parseInt(n[a],16));return he([t,function(e){var t=Rt(512),r=0,n=e.Target,a=-1<n.indexOf("#")?31:23;switch(n.charAt(0)){case"#":a=28;break;case".":a&=-3}t.write_shift(4,2),t.write_shift(4,a);for(var s=[8,6815827,6619237,4849780,83],r=0;r<s.length;++r)t.write_shift(4,s[r]);if(28==a){for(n=n.slice(1),t.write_shift(4,n.length+1),r=0;r<n.length;++r)t.write_shift(2,n.charCodeAt(r));t.write_shift(2,0)}else if(2&a){for(s="e0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" "),r=0;r<s.length;++r)t.write_shift(1,parseInt(s[r],16));for(t.write_shift(4,2*(n.length+1)),r=0;r<n.length;++r)t.write_shift(2,n.charCodeAt(r));t.write_shift(2,0)}else{for(s="03 03 00 00 00 00 00 00 c0 00 00 00 00 00 00 46".split(" "),r=0;r<s.length;++r)t.write_shift(1,parseInt(s[r],16));for(var i=0;"../"==n.slice(3*i,3*i+3)||"..\\"==n.slice(3*i,3*i+3);)++i;for(t.write_shift(2,i),t.write_shift(4,n.length+1),r=0;r<n.length;++r)t.write_shift(1,255&n.charCodeAt(r));for(t.write_shift(1,0),t.write_shift(2,65535),t.write_shift(2,57005),r=0;r<6;++r)t.write_shift(4,0)}return t.slice(0,t.l)}(e[1])])}function ga(e,t,r){if(!r.cellStyles)return It(e,t);var n=r&&12<=r.biff?4:2,a=e.read_shift(n),s=e.read_shift(n),i=e.read_shift(n),t=e.read_shift(n),r=e.read_shift(2);return 2==n&&(e.l+=2),{s:a,e:s,w:i,ixfe:t,flags:r}}pa[8]=function(e,t){var r=e.l+t;e.l+=10;var n=e.read_shift(2);e.l+=4,e.l+=2,e.l+=2,e.l+=2,e.l+=4;t=e.read_shift(1);return e.l+=t,e.l=r,{fmt:n}};var ba=Xn,va=Dn,Ea=Ln;var wa,Sa,_a=(wa={1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127,8:865,9:437,10:850,11:437,13:437,14:850,15:437,16:850,17:437,18:850,19:932,20:850,21:437,22:850,23:865,24:437,25:437,26:850,27:437,28:863,29:850,31:852,34:852,35:852,36:860,37:850,38:866,55:850,64:852,77:936,78:949,79:950,80:874,87:1252,88:1252,89:1252,255:16969},Sa={B:8,C:250,L:1,D:8,"?":0,"":0},{to_workbook:function(e,t){try{return Qt(ya(e,t),t)}catch(e){if(t&&t.WTF)throw e}return{SheetNames:[],Sheets:{}}},to_sheet:ya,from_sheet:function(e,t){if("string"==(t||{}).type)throw new Error("Cannot write DBF to JS string");for(var r=Ft(),n=(t=Rf(e,{header:1,cellDates:!0}))[0],a=t.slice(1),s=0,i=0,o=0,l=1,s=0;s<n.length;++s)if(null!=s){if(++o,"number"==typeof n[s]&&(n[s]=n[s].toString(10)),"string"!=typeof n[s])throw new Error("DBF Invalid column name "+n[s]+" |"+typeof n[s]+"|");if(n.indexOf(n[s])!==s)for(i=0;i<1024;++i)if(-1==n.indexOf(n[s]+"_"+i)){n[s]+="_"+i;break}}var c=Yt(e["!ref"]),f=[];for(s=0;s<=c.e.c-c.s.c;++s){var h=[];for(i=0;i<a.length;++i)null!=a[i][s]&&h.push(a[i][s]);if(0!=h.length&&null!=n[s]){for(var u="",d="",i=0;i<h.length;++i){switch(typeof h[i]){case"number":d="B";break;case"string":d="C";break;case"boolean":d="L";break;case"object":d=h[i]instanceof Date?"D":"C";break;default:d="C"}if("C"==(u=u&&u!=d?"C":d))break}l+=Sa[u]||0,f[s]=u}else f[s]="?"}var p,m,g=r.next(32);for(g.write_shift(4,318902576),g.write_shift(4,a.length),g.write_shift(2,296+32*o),g.write_shift(2,l),s=0;s<4;++s)g.write_shift(4,0);for(g.write_shift(4,768),i=s=0;s<n.length;++s)null!=n[s]&&(p=r.next(32),m=(n[s].slice(-10)+"\0\0\0\0\0\0\0\0\0\0\0").slice(0,11),p.write_shift(1,m,"sbcs"),p.write_shift(1,"?"==f[s]?"C":f[s],"sbcs"),p.write_shift(4,i),p.write_shift(1,Sa[f[s]]||0),p.write_shift(1,0),p.write_shift(1,2),p.write_shift(4,0),p.write_shift(1,0),p.write_shift(4,0),p.write_shift(4,0),i+=Sa[f[s]]||0);var b=r.next(264);for(b.write_shift(4,13),s=0;s<65;++s)b.write_shift(4,0);for(s=0;s<a.length;++s){var v=r.next(l);for(v.write_shift(1,0),i=0;i<n.length;++i)if(null!=n[i])switch(f[i]){case"L":v.write_shift(1,null==a[s][i]?63:a[s][i]?84:70);break;case"B":v.write_shift(8,a[s][i]||0,"f");break;case"D":a[s][i]?(v.write_shift(4,("0000"+a[s][i].getFullYear()).slice(-4),"sbcs"),v.write_shift(2,("00"+(a[s][i].getMonth()+1)).slice(-2),"sbcs"),v.write_shift(2,("00"+a[s][i].getDate()).slice(-2),"sbcs")):v.write_shift(8,"00000000","sbcs");break;case"C":var E=String(a[s][i]||"");for(v.write_shift(1,E,"sbcs"),o=0;o<250-E.length;++o)v.write_shift(1,32)}}return r.next(1).write_shift(1,26),r.end()}});function ya(e,t){t=t||{};return t.dateNF||(t.dateNF="yyyymmdd"),qt(function(e,t){var r=[],n=Z(1);switch(t.type){case"base64":n=J(Y.decode(e));break;case"binary":n=J(e);break;case"buffer":case"array":n=e}At(n,0);var a=n.read_shift(1),s=!1,i=!1,o=!1;switch(a){case 2:case 3:break;case 48:s=i=!0;break;case 49:i=!0;break;case 131:case 139:s=!0;break;case 140:o=s=!0;break;case 245:s=!0;break;default:throw new Error("DBF Unsupported Version: "+a.toString(16))}var l=0,c=0;2==a&&(l=n.read_shift(2)),n.l+=3,2!=a&&(l=n.read_shift(4)),2!=a&&(c=n.read_shift(2));var f=n.read_shift(2),h=1252;2!=a&&(n.l+=16,n.read_shift(1),0!==n[n.l]&&(h=wa[n[n.l]]),n.l+=1,n.l+=2),o&&(n.l+=36);for(var u=[],d={},p=c-10-(i?264:0),m=o?32:11;2==a?n.l<n.length&&13!=n[n.l]:n.l<p;)switch((d={}).name=cptable.utils.decode(h,n.slice(n.l,n.l+m)).replace(/[\u0000\r\n].*$/g,""),n.l+=m,d.type=String.fromCharCode(n.read_shift(1)),2==a||o||(d.offset=n.read_shift(4)),d.len=n.read_shift(1),2==a&&(d.offset=n.read_shift(2)),d.dec=n.read_shift(1),d.name.length&&u.push(d),2!=a&&(n.l+=o?13:14),d.type){case"B":i&&8==d.len||!t.WTF||console.log("Skipping "+d.name+":"+d.type);break;case"G":case"P":t.WTF&&console.log("Skipping "+d.name+":"+d.type);break;case"C":case"D":case"F":case"I":case"L":case"M":case"N":case"O":case"T":case"Y":case"0":case"@":case"+":break;default:throw new Error("Unknown Field Type: "+d.type)}if(13!==n[n.l]?n.l=c-1:2==a&&(n.l=521),2!=a){if(13!==n.read_shift(1))throw new Error("DBF Terminator not found "+n.l+" "+n[n.l]);n.l=c}var g=0,b=0;for(r[0]=[],b=0;b!=u.length;++b)r[0][b]=u[b].name;for(;0<l--;)if(42!==n[n.l])for(++n.l,r[++g]=[],b=b=0;b!=u.length;++b){var v=n.slice(n.l,n.l+u[b].len);n.l+=u[b].len,At(v,0);var E=cptable.utils.decode(h,v);switch(u[b].type){case"C":r[g][b]=cptable.utils.decode(h,v),r[g][b]=r[g][b].trim();break;case"D":8===E.length?r[g][b]=new Date(+E.slice(0,4),+E.slice(4,6)-1,+E.slice(6,8)):r[g][b]=E;break;case"F":r[g][b]=parseFloat(E.trim());break;case"+":case"I":r[g][b]=o?2147483648^v.read_shift(-4,"i"):v.read_shift(4,"i");break;case"L":switch(E.toUpperCase()){case"Y":case"T":r[g][b]=!0;break;case"N":case"F":r[g][b]=!1;break;case" ":case"?":r[g][b]=!1;break;default:throw new Error("DBF Unrecognized L:|"+E+"|")}break;case"M":if(!s)throw new Error("DBF Unexpected MEMO for type "+a.toString(16));r[g][b]="##MEMO##"+(o?parseInt(E.trim(),10):v.read_shift(4));break;case"N":r[g][b]=+E.replace(/\u0000/g,"").trim();break;case"@":r[g][b]=new Date(v.read_shift(-8,"f")-621356832e5);break;case"T":r[g][b]=new Date(864e5*(v.read_shift(4)-2440588)+v.read_shift(4));break;case"Y":r[g][b]=v.read_shift(4,"i")/1e4;break;case"O":r[g][b]=-v.read_shift(-8,"f");break;case"B":if(i&&8==u[b].len){r[g][b]=v.read_shift(8,"f");break}case"G":case"P":v.l+=u[b].len;break;case"0":if("_NullFlags"===u[b].name)break;default:throw new Error("DBF Unsupported data type "+u[b].type)}}else n.l+=f;if(2!=a&&n.l<n.length&&26!=n[n.l++])throw new Error("DBF EOF Marker missing "+(n.l-1)+" of "+n.length+" "+n[n.l-1].toString(16));return r=t&&t.sheetRows?r.slice(0,t.sheetRows):r}(e,t),t)}var Ca={to_workbook:function(e,t){return Qt(Ta(e,t),t)},to_sheet:Ta,from_sheet:function(e,t){var r,n,a=["ID;PWXL;N;E"],s=[],i=Yt(e["!ref"]),o=Array.isArray(e),l="\r\n";a.push("P;PGeneral"),a.push("F;P0;DG0G8;M255"),e["!cols"]&&(r=a,e["!cols"].forEach(function(e,t){t="F;W"+(t+1)+" "+(t+1)+" ";e.hidden?t+="0":("number"==typeof e.width&&(e.wpx=js(e.width)),"number"==typeof e.wpx&&(e.wch=Gs(e.wpx)),"number"==typeof e.wch&&(t+=Math.round(e.wch)))," "!=t.charAt(t.length-1)&&r.push(t)})),e["!rows"]&&(n=a,e["!rows"].forEach(function(e,t){var r="F;";e.hidden?r+="M0;":e.hpt?r+="M"+20*e.hpt+";":e.hpx&&(r+="M"+20*Js(e.hpx)+";"),2<r.length&&n.push(r+"R"+(t+1))})),a.push("B;Y"+(i.e.r-i.s.r+1)+";X"+(i.e.c-i.s.c+1)+";D"+[i.s.c,i.s.r,i.e.c,i.e.r].join(" "));for(var c=i.s.r;c<=i.e.r;++c)for(var f=i.s.c;f<=i.e.c;++f){var h=jt({r:c,c:f});(h=o?(e[c]||[])[f]:e[h])&&(null!=h.v||h.f&&!h.F)&&s.push(function(e,t,r){var n="C;Y"+(t+1)+";X"+(r+1)+";K";switch(e.t){case"n":n+=e.v||0,e.f&&!e.F&&(n+=";E"+$i(e.f,{r:t,c:r}));break;case"b":n+=e.v?"TRUE":"FALSE";break;case"e":n+=e.w||e.v;break;case"d":n+='"'+(e.w||e.v)+'"';break;case"s":n+='"'+e.v.replace(/"/g,"")+'"'}return n}(h,c,f))}return a.join(l)+l+s.join(l)+l+"E"+l}};function Ba(e,t){for(var r,n,a=e.split(/[\n\r]+/),s=-1,i=-1,o=0,l=0,c=[],f=[],h=null,e={},u=[],d=[],p=0;o!==a.length;++o){var m,p=0,g=a[o].trim(),b=g.replace(/;;/g,"").split(";").map(function(e){return e.replace(/\u0001/g,";")}),v=b[0];if(0<g.length)switch(v){case"ID":case"E":case"B":case"O":break;case"P":"P"==b[1].charAt(0)&&f.push(g.slice(3).replace(/;;/g,";"));break;case"C":for(var E=!1,w=!1,l=1;l<b.length;++l)switch(b[l].charAt(0)){case"X":i=parseInt(b[l].slice(1))-1,w=!0;break;case"Y":for(s=parseInt(b[l].slice(1))-1,w||(i=0),n=c.length;n<=s;++n)c[n]=[];break;case"K":'"'===(m=b[l].slice(1)).charAt(0)?m=m.slice(1,m.length-1):"TRUE"===m?m=!0:"FALSE"===m?m=!1:isNaN(x(m))?isNaN(A(m).getDate())||(m=oe(m)):(m=x(m),null!==h&&ue.is_date(h)&&(m=N(m))),"undefined"!=typeof cptable&&"string"==typeof m&&"string"!=(t||{}).type&&(t||{}).codepage&&(m=cptable.utils.decode(t.codepage,m)),E=!0;break;case"E":var S=Xi(b[l].slice(1),{r:s,c:i});c[s][i]=[c[s][i],S];break;default:if(t&&t.WTF)throw new Error("SYLK bad record "+g)}E&&(c[s][i]=m,h=null);break;case"F":var _=0;for(l=1;l<b.length;++l)switch(b[l].charAt(0)){case"X":i=parseInt(b[l].slice(1))-1,++_;break;case"Y":for(s=parseInt(b[l].slice(1))-1,n=c.length;n<=s;++n)c[n]=[];break;case"M":p=parseInt(b[l].slice(1))/20;break;case"F":case"G":break;case"P":h=f[parseInt(b[l].slice(1))];break;case"S":case"D":case"N":break;case"W":for(r=b[l].slice(1).split(" "),n=parseInt(r[0],10);n<=parseInt(r[1],10);++n)p=parseInt(r[2],10),d[n-1]=0===p?{hidden:!0}:{wch:p},Zs(d[n-1]);break;case"C":d[i=parseInt(b[l].slice(1))-1]||(d[i]={});break;case"R":u[s=parseInt(b[l].slice(1))-1]||(u[s]={}),0<p?(u[s].hpt=p,u[s].hpx=qs(p)):0===p&&(u[s].hidden=!0);break;default:if(t&&t.WTF)throw new Error("SYLK bad record "+g)}_<1&&(h=null);break;default:if(t&&t.WTF)throw new Error("SYLK bad record "+g)}}return 0<u.length&&(e["!rows"]=u),0<d.length&&(e["!cols"]=d),[c=t&&t.sheetRows?c.slice(0,t.sheetRows):c,e]}function Ta(e,t){var r=function(e,t){switch(t.type){case"base64":return Ba(Y.decode(e),t);case"binary":return Ba(e,t);case"buffer":return Ba(e.toString("binary"),t);case"array":return Ba(k(e),t)}throw new Error("Unrecognized type "+t.type)}(e,t),e=r[0],n=r[1],a=qt(e,t);return de(n).forEach(function(e){a[e]=n[e]}),a}var ka={to_workbook:function(e,t){return Qt(Aa(e,t),t)},to_sheet:Aa,from_sheet:function(e){var t=[],r=Yt(e["!ref"]),n=Array.isArray(e);Ia(t,"TABLE",0,1,"sheetjs"),Ia(t,"VECTORS",0,r.e.r-r.s.r+1,""),Ia(t,"TUPLES",0,r.e.c-r.s.c+1,""),Ia(t,"DATA",0,0,"");for(var a=r.s.r;a<=r.e.r;++a){Ra(t,-1,0,"BOT");for(var s=r.s.c;s<=r.e.c;++s){var i,o=jt({r:a,c:s});if(i=n?(e[a]||[])[s]:e[o])switch(i.t){case"n":var l=i.w;null==(l=!l&&null!=i.v?i.v:l)?i.f&&!i.F?Ra(t,1,0,"="+i.f):Ra(t,1,0,""):Ra(t,0,l,"V");break;case"b":Ra(t,0,i.v?1:0,i.v?"TRUE":"FALSE");break;case"s":Ra(t,1,0,isNaN(i.v)?i.v:'="'+i.v+'"');break;case"d":i.w||(i.w=ue.format(i.z||ue._table[14],G(oe(i.v)))),Ra(t,0,i.w,"V");break;default:Ra(t,1,0,"")}else Ra(t,1,0,"")}}Ra(t,-1,0,"EOD");return t.join("\r\n")}};function xa(e,t){for(var r=e.split("\n"),n=-1,a=-1,s=0,i=[];s!==r.length;++s)if("BOT"!==r[s].trim()){if(!(n<0)){var o=r[s].trim().split(","),l=o[0],c=o[1],f=r[++s].trim();switch(+l){case-1:if("BOT"===f){i[++n]=[],a=0;continue}if("EOD"!==f)throw new Error("Unrecognized DIF special command "+f);break;case 0:"TRUE"===f?i[n][a]=!0:"FALSE"===f?i[n][a]=!1:isNaN(x(c))?isNaN(A(c).getDate())?i[n][a]=c:i[n][a]=oe(c):i[n][a]=x(c),++a;break;case 1:f=f.slice(1,f.length-1),i[n][a++]=""!==f?f:null}if("EOD"===f)break}}else i[++n]=[],a=0;return i=t&&t.sheetRows?i.slice(0,t.sheetRows):i}function Aa(e,t){return qt(function(e,t){switch(t.type){case"base64":return xa(Y.decode(e),t);case"binary":return xa(e,t);case"buffer":return xa(e.toString("binary"),t);case"array":return xa(k(e),t)}throw new Error("Unrecognized type "+t.type)}(e,t),t)}function Ia(e,t,r,n,a){e.push(t),e.push(r+","+n),e.push('"'+a.replace(/"/g,'""')+'"')}function Ra(e,t,r,n){e.push(t+","+r),e.push(1==t?'"'+n.replace(/"/g,'""')+'"':n)}var Oa,Fa,Da,Pa,Na=(Oa=["socialcalc:version:1.5","MIME-Version: 1.0","Content-Type: multipart/mixed; boundary=SocialCalcSpreadsheetControlSave"].join("\n"),Fa=["--SocialCalcSpreadsheetControlSave","Content-type: text/plain; charset=UTF-8"].join("\n")+"\n",Da=["# SocialCalc Spreadsheet Control Save","part:sheet"].join("\n"),Pa="--SocialCalcSpreadsheetControlSave--",{to_workbook:function(e,t){return Qt(Ma(e,t),t)},to_sheet:Ma,from_sheet:function(e){return[Oa,Fa,Da,Fa,function(e){if(!e||!e["!ref"])return"";for(var t,r,n=[],a=[],s=Gt(e["!ref"]),i=Array.isArray(e),o=s.s.r;o<=s.e.r;++o)for(var l=s.s.c;l<=s.e.c;++l)if(r=jt({r:o,c:l}),(t=i?(e[o]||[])[l]:e[r])&&null!=t.v&&"z"!==t.t){switch(a=["cell",r,"t"],t.t){case"s":case"str":a.push(La(t.v));break;case"n":t.f?(a[2]="vtf",a[3]="n",a[4]=t.v,a[5]=La(t.f)):(a[2]="v",a[3]=t.v);break;case"b":a[2]="vt"+(t.f?"f":"c"),a[3]="nl",a[4]=t.v?"1":"0",a[5]=La(t.f||(t.v?"TRUE":"FALSE"));break;case"d":var c=G(oe(t.v));a[2]="vtc",a[3]="nd",a[4]=""+c,a[5]=t.w||ue.format(t.z||ue._table[14],c);break;case"e":continue}n.push(a.join(":"))}return n.push("sheet:c:"+(s.e.c-s.s.c+1)+":r:"+(s.e.r-s.s.r+1)+":tvf:1"),n.push("valueformat:1:text-wiki"),n.join("\n")}(e),Pa].join("\n")}});function La(e){return e.replace(/\\/g,"\\b").replace(/:/g,"\\c").replace(/\n/g,"\\n")}function Ma(e,t){return qt(function(e,t){for(var r,n=e.split("\n"),a=-1,s=0,i=[];s!==n.length;++s){var o=n[s].trim().split(":");if("cell"===o[0]){var l=Xt(o[1]);if(i.length<=l.r)for(a=i.length;a<=l.r;++a)i[a]||(i[a]=[]);switch(a=l.r,r=l.c,o[2]){case"t":i[a][r]=o[3].replace(/\\b/g,"\\").replace(/\\c/g,":").replace(/\\n/g,"\n");break;case"v":i[a][r]=+o[3];break;case"vtf":var c=o[o.length-1];case"vtc":"nl"===o[3]?i[a][r]=!!+o[4]:i[a][r]=+o[4],"vtf"==o[2]&&(i[a][r]=[i[a][r],c])}}}return i=t&&t.sheetRows?i.slice(0,t.sheetRows):i}(e,t),t)}var Ua,Ha,za=(Ua={44:",",9:"\t",59:";"},Ha={44:3,9:2,59:1},{to_workbook:function(e,t){return Qt(ja(e,t),t)},to_sheet:ja,from_sheet:function(e){for(var t=[],r=Yt(e["!ref"]),n=Array.isArray(e),a=r.s.r;a<=r.e.r;++a){for(var s=[],i=r.s.c;i<=r.e.c;++i){var o=jt({r:a,c:i});if((o=n?(e[a]||[])[i]:e[o])&&null!=o.v){for(var l=(o.w||(Zt(o),o.w)||"").slice(0,10);l.length<10;)l+=" ";s.push(l+(0===i?" ":""))}else s.push("          ")}t.push(s.join(""))}return t.join("\n")}});function Va(e,t,r,n,a){a.raw?t[r][n]=e:"TRUE"===e?t[r][n]=!0:"FALSE"===e?t[r][n]=!1:""===e||(isNaN(x(e))?isNaN(A(e).getDate())?t[r][n]=e:t[r][n]=oe(e):t[r][n]=x(e))}function Wa(n,e){var a=e||{},e="";null!=fe&&null==a.dense&&(a.dense=fe);var s=a.dense?[]:{},i={s:{c:0,r:0},e:{c:0,r:0}};"sep="==n.slice(0,4)&&10==n.charCodeAt(5)?(e=n.charAt(4),n=n.slice(6)):e=function(e){for(var t={},r=!1,n=0,a=0;n<e.length;++n)34==(a=e.charCodeAt(n))?r=!r:!r&&a in Ua&&(t[a]=(t[a]||0)+1);for(n in a=[],t)t.hasOwnProperty(n)&&a.push([t[n],n]);if(!a.length)for(n in t=Ha)t.hasOwnProperty(n)&&a.push([t[n],n]);return a.sort(function(e,t){return e[0]-t[0]||Ha[e[1]]-Ha[t[1]]}),Ua[a.pop()[1]]}(n.slice(0,1024));var o=0,l=0,c=0,f=0,h=0,u=e.charCodeAt(0),t=!1,d=0;n=n.replace(/\r\n/gm,"\n");var p=null!=a.dateNF?(e=(e="number"==typeof(e=a.dateNF)?ue._table[e]:e).replace(g,"(\\d+)"),new RegExp("^"+e+"$")):null;function r(){var e,t=n.slice(f,h),r={};if(0===(t='"'==t.charAt(0)&&'"'==t.charAt(t.length-1)?t.slice(1,-1).replace(/""/g,'"'):t).length?r.t="z":a.raw||0===t.trim().length?(r.t="s",r.v=t):61==t.charCodeAt(0)?34==t.charCodeAt(1)&&34==t.charCodeAt(t.length-1)?(r.t="s",r.v=t.slice(2,-1).replace(/""/g,'"')):1!=t.length?(r.t="n",r.f=t.slice(1)):(r.t="s",r.v=t):"TRUE"==t?(r.t="b",r.v=!0):"FALSE"==t?(r.t="b",r.v=!1):isNaN(c=x(t))?!isNaN(A(t).getDate())||p&&t.match(p)?(r.z=a.dateNF||ue._table[14],e=0,p&&t.match(p)&&(t=function(e,n){var a=-1,s=-1,i=-1,o=-1,l=-1,c=-1;(e.match(g)||[]).forEach(function(e,t){var r=parseInt(n[t+1],10);switch(e.toLowerCase().charAt(0)){case"y":a=r;break;case"d":i=r;break;case"h":o=r;break;case"s":c=r;break;case"m":0<=o?l=r:s=r}}),0<=c&&-1==l&&0<=s&&(l=s,s=-1);var t=(""+(0<=a?a:(new Date).getFullYear())).slice(-4)+"-"+("00"+(1<=s?s:1)).slice(-2)+"-"+("00"+(1<=i?i:1)).slice(-2);return 8==(t=7==t.length?"0"+t:t).length&&(t="20"+t),e=("00"+(0<=o?o:0)).slice(-2)+":"+("00"+(0<=l?l:0)).slice(-2)+":"+("00"+(0<=c?c:0)).slice(-2),-1==o&&-1==l&&-1==c?t:-1==a&&-1==s&&-1==i?e:t+"T"+e}(a.dateNF,t.match(p)||[]),e=1),a.cellDates?(r.t="d",r.v=oe(t,e)):(r.t="n",r.v=G(oe(t,e))),!1!==a.cellText&&(r.w=ue.format(r.z,r.v instanceof Date?G(r.v):r.v)),a.cellNF||delete r.z):(r.t="s",r.v=t):(!(r.t="n")!==a.cellText&&(r.w=t),r.v=c),"z"==r.t||(a.dense?(s[o]||(s[o]=[]),s[o][l]=r):s[jt({c:l,r:o})]=r),f=h+1,i.e.c<l&&(i.e.c=l),i.e.r<o&&(i.e.r=o),d!=u)return l=0,++o,!!(a.sheetRows&&a.sheetRows<=o);++l}e:for(;h<n.length;++h)switch(d=n.charCodeAt(h)){case 34:t=!t;break;case u:case 10:case 13:if(!t&&r())break e}return 0<h-f&&r(),s["!ref"]=$t(i),s}function Xa(e,t){return"sep="==e.slice(0,4)||0<=e.indexOf("\t")||0<=e.indexOf(",")||0<=e.indexOf(";")?Wa(e,t):qt(function(e,t){var r=t||{},n=[];if(!e||0===e.length)return n;for(var a=e.split(/[\r\n]/),s=a.length-1;0<=s&&0===a[s].length;)--s;for(var i=10,o=0,l=0;l<=s;++l)-1==(o=a[l].indexOf(" "))?o=a[l].length:o++,i=Math.max(i,o);for(l=0;l<=s;++l){n[l]=[];var c=0;for(Va(a[l].slice(0,i).trim(),n,l,c,r),c=1;c<=(a[l].length-i)/10+1;++c)Va(a[l].slice(i+10*(c-1),i+10*c).trim(),n,l,c,r)}return n=r.sheetRows?n.slice(0,r.sheetRows):n}(e,t),t)}function ja(e,t){var r="",n="string"==t.type?[0,0,0,0]:Ef(e,t);switch(t.type){case"base64":r=Y.decode(e);break;case"binary":r=e;break;case"buffer":r=65001==t.codepage?e.toString("utf8"):t.codepage&&"undefined"!=typeof cptable?cptable.utils.decode(t.codepage,e):e.toString("binary");break;case"array":r=k(e);break;case"string":r=e;break;default:throw new Error("Unrecognized type "+t.type)}return 239==n[0]&&187==n[1]&&191==n[2]?r=Oe(r.slice(3)):"binary"==t.type&&"undefined"!=typeof cptable&&t.codepage&&(r=cptable.utils.decode(t.codepage,cptable.utils.encode(1252,r))),"socialcalc:version:"==r.slice(0,19)?Na.to_sheet("string"==t.type?r:Oe(r),t):Xa(r,t)}var Ga,$a,Ya=(Ga={0:{n:"BOF",f:On},1:{n:"EOF"},2:{n:"CALCMODE"},3:{n:"CALCORDER"},4:{n:"SPLIT"},5:{n:"SYNC"},6:{n:"RANGE",f:function(e){var t={s:{c:0,r:0},e:{c:0,r:0}};return t.s.c=e.read_shift(2),t.s.r=e.read_shift(2),t.e.c=e.read_shift(2),t.e.r=e.read_shift(2),65535==t.s.c&&(t.s.c=t.e.c=t.s.r=t.e.r=0),t}},7:{n:"WINDOW1"},8:{n:"COLW1"},9:{n:"WINTWO"},10:{n:"COLW2"},11:{n:"NAME"},12:{n:"BLANK"},13:{n:"INTEGER",f:function(e,t,r){return(r=Za(e,0,r))[1].v=e.read_shift(2,"i"),r}},14:{n:"NUMBER",f:function(e,t,r){return(r=Za(e,0,r))[1].v=e.read_shift(8,"f"),r}},15:{n:"LABEL",f:Qa},16:{n:"FORMULA",f:function(e,t,r){var n=e.l+t;return(t=Za(e,0,r))[1].v=e.read_shift(8,"f"),r.qpro?e.l=n:(n=e.read_shift(2),e.l+=n),t}},24:{n:"TABLE"},25:{n:"ORANGE"},26:{n:"PRANGE"},27:{n:"SRANGE"},28:{n:"FRANGE"},29:{n:"KRANGE1"},32:{n:"HRANGE"},35:{n:"KRANGE2"},36:{n:"PROTEC"},37:{n:"FOOTER"},38:{n:"HEADER"},39:{n:"SETUP"},40:{n:"MARGINS"},41:{n:"LABELFMT"},42:{n:"TITLES"},43:{n:"SHEETJS"},45:{n:"GRAPH"},46:{n:"NGRAPH"},47:{n:"CALCCOUNT"},48:{n:"UNFORMATTED"},49:{n:"CURSORW12"},50:{n:"WINDOW"},51:{n:"STRING",f:Qa},55:{n:"PASSWORD"},56:{n:"LOCKED"},60:{n:"QUERY"},61:{n:"QUERYNAME"},62:{n:"PRINT"},63:{n:"PRINTNAME"},64:{n:"GRAPH2"},65:{n:"GRAPHNAME"},66:{n:"ZOOM"},67:{n:"SYMSPLIT"},68:{n:"NSROWS"},69:{n:"NSCOLS"},70:{n:"RULER"},71:{n:"NNAME"},72:{n:"ACOMM"},73:{n:"AMACRO"},74:{n:"PARSE"},255:{n:"",f:It}},$a={0:{n:"BOF"},1:{n:"EOF"},3:{n:"??"},4:{n:"??"},5:{n:"??"},6:{n:"??"},7:{n:"??"},9:{n:"??"},10:{n:"??"},11:{n:"??"},12:{n:"??"},14:{n:"??"},15:{n:"??"},16:{n:"??"},17:{n:"??"},18:{n:"??"},19:{n:"??"},21:{n:"??"},22:{n:"LABEL16",f:function(e,t){var r=Ja(e);return r[1].t="s",r[1].v=e.read_shift(t-4,"cstr"),r}},23:{n:"NUMBER17",f:qa},24:{n:"NUMBER18",f:function(e,t){var r=Ja(e);r[1].v=e.read_shift(2);var n=r[1].v>>1;if(1&r[1].v)switch(7&n){case 1:n=500*(n>>3);break;case 2:n=(n>>3)/20;break;case 4:n=(n>>3)/2e3;break;case 6:n=(n>>3)/16;break;case 7:n=(n>>3)/64;break;default:throw"unknown NUMBER_18 encoding "+(7&n)}return r[1].v=n,r}},25:{n:"FORMULA19",f:function(e,t){var r=qa(e);return e.l+=t-14,r}},26:{n:"??"},27:{n:"??"},28:{n:"??"},29:{n:"??"},30:{n:"??"},31:{n:"??"},33:{n:"??"},37:{n:"NUMBER25",f:function(e,t){var r=Ja(e),e=e.read_shift(4);return r[1].v=e>>6,r}},39:{n:"NUMBER27",f:es},40:{n:"FORMULA28",f:function(e,t){var r=es(e);return e.l+=t-10,r}},255:{n:"",f:It}},{to_workbook:function(e,t){switch(t.type){case"base64":return Ka(J(Y.decode(e)),t);case"binary":return Ka(J(e),t);case"buffer":case"array":return Ka(e,t)}throw"Unsupported type "+t.type}});function Ka(n,e){if(!n)return n;var a=e||{};null!=fe&&null==a.dense&&(a.dense=fe);var s=a.dense?[]:{},i="Sheet1",o=0,l={},c=[i],f={s:{r:0,c:0},e:{r:0,c:0}},h=a.sheetRows||0;if(2==n[2])a.Enum=Ga;else if(26==n[2])a.Enum=$a;else{if(14!=n[2])throw new Error("Unrecognized LOTUS BOF "+n[2]);a.Enum=$a,a.qpro=!0,n.l=0}return function(e,t,r){if(e){At(e,e.l||0);for(var n=r.Enum||Ga;e.l<e.length;){var a=e.read_shift(2),s=n[a]||n[255],i=e.read_shift(2),o=e.l+i,i=(s.f||It)(e,i,r);if(e.l=o,t(i,s.n,a))return}}}(n,function(e,t,r){if(2==n[2])switch(r){case 0:4096<=(a.vers=e)&&(a.qpro=!0);break;case 6:f=e;break;case 15:a.qpro||(e[1].v=e[1].v.slice(1));case 13:case 14:case 16:case 51:14==r&&112==(112&e[2])&&1<(15&e[2])&&(15&e[2])<15&&(e[1].z=a.dateNF||ue._table[14],a.cellDates&&(e[1].t="d",e[1].v=N(e[1].v))),a.dense?(s[e[0].r]||(s[e[0].r]=[]),s[e[0].r][e[0].c]=e[1]):s[jt(e[0])]=e[1]}else switch(r){case 22:e[1].v=e[1].v.slice(1);case 23:case 24:case 25:case 37:case 39:case 40:if(e[3]>o&&(s["!ref"]=$t(f),l[i]=s,s=a.dense?[]:{},f={s:{r:0,c:0},e:{r:0,c:0}},o=e[3],i="Sheet"+(o+1),c.push(i)),0<h&&e[0].r>=h)break;a.dense?(s[e[0].r]||(s[e[0].r]=[]),s[e[0].r][e[0].c]=e[1]):s[jt(e[0])]=e[1],f.e.c<e[0].c&&(f.e.c=e[0].c),f.e.r<e[0].r&&(f.e.r=e[0].r)}},a),s["!ref"]=$t(f),l[i]=s,{SheetNames:c,Sheets:l}}function Za(e,t,r){var n=[{c:0,r:0},{t:"n",v:0},0];return r.qpro&&20768!=r.vers?(n[0].c=e.read_shift(1),e.l++,n[0].r=e.read_shift(2),e.l+=2):(n[2]=e.read_shift(1),n[0].c=e.read_shift(2),n[0].r=e.read_shift(2)),n}function Qa(e,t,r){var n=e.l+t,t=Za(e,0,r);if(t[1].t="s",20768!=r.vers)return r.qpro&&e.l++,t[1].v=e.read_shift(n-e.l,"cstr"),t;e.l++;n=e.read_shift(1);return t[1].v=e.read_shift(n,"utf8"),t}function Ja(e){var t=[{c:0,r:0},{t:"n",v:0},0];return t[0].r=e.read_shift(2),t[3]=e[e.l++],t[0].c=e[e.l++],t}function qa(e,t){var r=Ja(e),n=e.read_shift(4),a=e.read_shift(4),e=e.read_shift(2);return r[1].v=65535==e?0:(2*(32768&e)-1)*((0<(e=(32767&e)-16446)?a<<e:a>>>-e)+(-32<e?n<<32+e:n>>>-(32+e))),r}function es(e,t){var r=Ja(e),e=e.read_shift(8,"f");return r[1].v=e,r}var ts,rs,ns,as,ss,is,os=(ts=Le("t"),rs=Le("rPr"),ns=/<(?:\w+:)?r>/g,as=/<\/(?:\w+:)?r>/,ss=/\r\n/g,is=function(e,t,r){var n={},a=65001,s="",i=!1,o=e.match(W),l=0;if(o)for(;l!=o.length;++l){var c=pe(o[l]);switch(c[0].replace(/\w*:/g,"")){case"<condense":case"<extend":break;case"<shadow":if(!c.val)break;case"<shadow>":case"<shadow/>":n.shadow=1;break;case"</shadow>":break;case"<charset":if("1"==c.val)break;a=h[parseInt(c.val,10)];break;case"<outline":if(!c.val)break;case"<outline>":case"<outline/>":n.outline=1;break;case"</outline>":break;case"<rFont":n.name=c.val;break;case"<sz":n.sz=c.val;break;case"<strike":if(!c.val)break;case"<strike>":case"<strike/>":n.strike=1;break;case"</strike>":break;case"<u":if(!c.val)break;switch(c.val){case"double":n.uval="double";break;case"singleAccounting":n.uval="single-accounting";break;case"doubleAccounting":n.uval="double-accounting"}case"<u>":case"<u/>":n.u=1;break;case"</u>":break;case"<b":if("0"==c.val)break;case"<b>":case"<b/>":n.b=1;break;case"</b>":break;case"<i":if("0"==c.val)break;case"<i>":case"<i/>":n.i=1;break;case"</i>":break;case"<color":c.rgb&&(n.color=c.rgb.slice(2,8));break;case"<family":n.family=c.val;break;case"<vertAlign":s=c.val;break;case"<scheme":break;case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":i=!0;break;case"</ext>":i=!1;break;default:if(47!==c[0].charCodeAt(1)&&!i)throw new Error("Unrecognized rich format "+c[0])}}e=[];return n.u&&e.push("text-decoration: underline;"),n.uval&&e.push("text-underline-style:"+n.uval+";"),n.sz&&e.push("font-size:"+n.sz+"pt;"),n.outline&&e.push("text-effect: outline;"),n.shadow&&e.push("text-shadow: auto;"),t.push('<span style="'+e.join("")+'">'),n.b&&(t.push("<b>"),r.push("</b>")),n.i&&(t.push("<i>"),r.push("</i>")),n.strike&&(t.push("<s>"),r.push("</s>")),"superscript"==s?s="sup":"subscript"==s&&(s="sub"),""!=s&&(t.push("<"+s+">"),r.push("</"+s+">")),r.push("</span>"),a},function(e){return e.replace(ns,"").split(as).map(ls).join("")});function ls(e){var t=[[],"",[]],r=e.match(ts);if(!r)return"";t[1]=r[1];e=e.match(rs);return e&&is(e[1],t[0],t[2]),t[0].join("")+t[1].replace(ss,"<br/>")+t[2].join("")}var cs=/<(?:\w+:)?t[^>]*>([^<]*)<\/(?:\w+:)?t>/g,fs=/<(?:\w+:)?r>/,hs=/<(?:\w+:)?rPh.*?>([\s\S]*?)<\/(?:\w+:)?rPh>/g;function us(e,t){var r=!t||t.cellHTML,t={};return e?(e.match(/^\s*<(?:\w+:)?t[^>]*>/)?(t.t=we(Oe(e.slice(e.indexOf(">")+1).split(/<\/(?:\w+:)?t>/)[0]||"")),t.r=Oe(e),r&&(t.h=Te(t.t))):e.match(fs)&&(t.r=Oe(e),t.t=we(Oe((e.replace(hs,"").match(cs)||[]).join("").replace(W,""))),r&&(t.h=os(t.r))),t):null}var ds=/<(?:\w+:)?sst([^>]*)>([\s\S]*)<\/(?:\w+:)?sst>/,ps=/<(?:\w+:)?(?:si|sstItem)>/g,ms=/<\/(?:\w+:)?(?:si|sstItem)>/;Wr.SST="http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings";var gs=/^\s|\s$|[\t\n\r]/;function bs(e,t){if(!t.bookSST)return"";var r=[z];r[r.length]=Ge("sst",null,{xmlns:Ye.main[0],count:e.Count,uniqueCount:e.Unique});for(var n,a,s=0;s!=e.length;++s)null!=e[s]&&(a="<si>",(n=e[s]).r?a+=n.r:(a+="<t",n.t||(n.t=""),n.t.match(gs)&&(a+=' xml:space="preserve"'),a+=">"+ye(n.t)+"</t>"),a+="</si>",r[r.length]=a);return 2<r.length&&(r[r.length]="</sst>",r[1]=r[1].replace("/>",">")),r.join("")}var vs=function(e,t){var r=!1;return null==t&&(r=!0,t=Rt(15+4*e.t.length)),t.write_shift(1,0),rr(e.t,t),r?t.slice(0,t.l):t};function Es(e){var t,r,n=Ft();Dt(n,"BrtBeginSst",(t=e,(r=r||Rt(8)).write_shift(4,t.Count),r.write_shift(4,t.Unique),r));for(var a=0;a<e.length;++a)Dt(n,"BrtSSTItem",vs(e[a]));return Dt(n,"BrtEndSst"),n.end()}function ws(e){if("undefined"!=typeof cptable)return cptable.utils.encode(a,e);for(var t=[],r=e.split(""),n=0;n<r.length;++n)t[n]=r[n].charCodeAt(0);return t}function Ss(e,t){var r={};return r.Major=e.read_shift(2),r.Minor=e.read_shift(2),4<=t&&(e.l+=t-4),r}function _s(e){var t=[];e.l+=4;for(var r=e.read_shift(4);0<r--;)t.push(function(e){for(var t=e.read_shift(4),r=e.l+t-4,t={},n=e.read_shift(4),a=[];0<n--;)a.push({t:e.read_shift(4),v:e.read_shift(0,"lpp4")});if(t.name=e.read_shift(0,"lpp4"),t.comps=a,e.l!=r)throw new Error("Bad DataSpaceMapEntry: "+e.l+" != "+r);return t}(e));return t}function ys(e){var t,r,r=(r={},(t=e).read_shift(4),t.l+=4,r.id=t.read_shift(0,"lpp4"),r.name=t.read_shift(0,"lpp4"),r.R=Ss(t,4),r.U=Ss(t,4),r.W=Ss(t,4),r);if(r.ename=e.read_shift(0,"8lpp4"),r.blksz=e.read_shift(4),r.cmode=e.read_shift(4),4!=e.read_shift(4))throw new Error("Bad !Primary record");return r}function Cs(e,t){var t=e.l+t,r={};r.Flags=63&e.read_shift(4),e.l+=4,r.AlgID=e.read_shift(4);var n=!1;switch(r.AlgID){case 26126:case 26127:case 26128:n=36==r.Flags;break;case 26625:n=4==r.Flags;break;case 0:n=16==r.Flags||4==r.Flags||36==r.Flags;break;default:throw"Unrecognized encryption algorithm: "+r.AlgID}if(!n)throw new Error("Encryption Flags/AlgID mismatch");return r.AlgIDHash=e.read_shift(4),r.KeySize=e.read_shift(4),r.ProviderType=e.read_shift(4),e.l+=8,r.CSPName=e.read_shift(t-e.l>>1,"utf16le"),e.l=t,r}function Bs(e,t){var r={},t=e.l+t;return e.l+=4,r.Salt=e.slice(e.l,e.l+16),e.l+=16,r.Verifier=e.slice(e.l,e.l+16),e.l+=16,e.read_shift(4),r.VerifierHash=e.slice(e.l,t),e.l=t,r}function Ts(e){var t=Ss(e);switch(t.Minor){case 2:return[t.Minor,function(e){if(36!=(63&e.read_shift(4)))throw new Error("EncryptionInfo mismatch");var t=e.read_shift(4),t=Cs(e,t),e=Bs(e,e.length-e.l);return{t:"Std",h:t,v:e}}(e)];case 3:return[t.Minor,function(){throw new Error("File is password-protected: ECMA-376 Extensible")}()];case 4:return[t.Minor,function(e){var r=["saltSize","blockSize","keyBits","hashSize","cipherAlgorithm","cipherChaining","hashAlgorithm","saltValue"];e.l+=4;var e=e.read_shift(e.length-e.l,"utf8"),n={};return e.replace(W,function(e){var t=pe(e);switch(me(t[0])){case"<?xml":break;case"<encryption":case"</encryption>":break;case"<keyData":r.forEach(function(e){n[e]=t[e]});break;case"<dataIntegrity":n.encryptedHmacKey=t.encryptedHmacKey,n.encryptedHmacValue=t.encryptedHmacValue;break;case"<keyEncryptors>":case"<keyEncryptors":n.encs=[];break;case"</keyEncryptors>":break;case"<keyEncryptor":n.uri=t.uri;break;case"</keyEncryptor>":break;case"<encryptedKey":n.encs.push(t);break;default:throw t[0]}}),n}(e)]}throw new Error("ECMA-376 Encrypted file unrecognized Version: "+t.Minor)}function ks(e){var t,r=0,n=ws(e),a=n.length+1,s=Z(a);for(s[0]=n.length,t=1;t!=a;++t)s[t]=n[t-1];for(t=a-1;0<=t;--t)r=((0==(16384&r)?0:1)|r<<1&32767)^s[t];return 52811^r}var xs,As,Is,Rs,Os=(xs=[187,255,255,186,255,255,185,128,0,190,15,0,191,15,0],As=[57840,7439,52380,33984,4364,3600,61902,12606,6258,57657,54287,34041,10252,43370,20163],Is=[44796,19929,39858,10053,20106,40212,10761,31585,63170,64933,60267,50935,40399,11199,17763,35526,1453,2906,5812,11624,23248,885,1770,3540,7080,14160,28320,56640,55369,41139,20807,41614,21821,43642,17621,28485,56970,44341,19019,38038,14605,29210,60195,50791,40175,10751,21502,43004,24537,18387,36774,3949,7898,15796,31592,63184,47201,24803,49606,37805,14203,28406,56812,17824,35648,1697,3394,6788,13576,27152,43601,17539,35078,557,1114,2228,4456,30388,60776,51953,34243,7079,14158,28316,14128,28256,56512,43425,17251,34502,7597,13105,26210,52420,35241,883,1766,3532,4129,8258,16516,33032,4657,9314,18628],Rs=function(e){return 255&(e/2|128*e)},function(e){for(var t,r=ws(e),n=function(e){for(var t=As[e.length-1],r=104,n=e.length-1;0<=n;--n)for(var a=e[n],s=0;7!=s;++s)64&a&&(t^=Is[r]),a*=2,--r;return t}(r),a=r.length,s=Z(16),i=0;16!=i;++i)s[i]=0;for(1==(1&a)&&(s[a]=Fs(xs[0],n>>8),e=r[r.length-1],s[--a]=Fs(e,255&n));0<a;)s[--a]=Fs(r[a],n>>8),s[--a]=Fs(r[a],255&n);for(t=(a=15)-r.length;0<t;)s[a]=Fs(xs[t],n>>8),--t,s[--a]=Fs(r[a],255&n),--a,--t;return s});function Fs(e,t){return Rs(e^t)}var Ds=function(e){var t=0,r=Os(e);return function(e){e=function(e,t,r,n,a){var s,i;for(a=a||t,n=n||Os(e),s=0;s!=t.length;++s)i=t[s],i^=n[r],a[s]=255&(i>>5|i<<3),++r;return[a,r,n]}("",e,t,r);return t=e[1],e[0]}};function Ps(e,t,r){r=r||{};return r.Info=e.read_shift(2),e.l-=2,1===r.Info?r.Data=function(e){var t={},r=t.EncryptionVersionInfo=Ss(e,4);if(1!=r.Major||1!=r.Minor)throw"unrecognized version code "+r.Major+" : "+r.Minor;return t.Salt=e.read_shift(16),t.EncryptedVerifier=e.read_shift(16),t.EncryptedVerifierHash=e.read_shift(16),t}(e):r.Data=function(e,t){var r={},n=r.EncryptionVersionInfo=Ss(e,4);if(t-=4,2!=n.Minor)throw new Error("unrecognized minor version code: "+n.Minor);if(4<n.Major||n.Major<2)throw new Error("unrecognized major version code: "+n.Major);return r.Flags=e.read_shift(4),t-=4,n=e.read_shift(4),t-=4,r.EncryptionHeader=Cs(e,n),r.EncryptionVerifier=Bs(e,t-=n),r}(e,t),r}var Ns={to_workbook:function(e,t){return Qt(Ls(e,t),t)},to_sheet:Ls,from_sheet:function(e){for(var t=["{\\rtf1\\ansi"],r=Yt(e["!ref"]),n=Array.isArray(e),a=r.s.r;a<=r.e.r;++a){t.push("\\trowd\\trautofit1");for(var s=r.s.c;s<=r.e.c;++s)t.push("\\cellx"+(s+1));for(t.push("\\pard\\intbl"),s=r.s.c;s<=r.e.c;++s){var i=jt({r:a,c:s});(i=n?(e[a]||[])[s]:e[i])&&(null!=i.v||i.f&&!i.F)&&(t.push(" "+(i.w||(Zt(i),i.w))),t.push("\\cell"))}t.push("\\pard\\intbl\\row")}return t.join("")+"}"}};function Ls(e,t){switch(t.type){case"base64":return Ms(Y.decode(e),t);case"binary":return Ms(e,t);case"buffer":return Ms(e.toString("binary"),t);case"array":return Ms(k(e),t)}throw new Error("Unrecognized type "+t.type)}function Ms(e,t){t=(t||{}).dense?[]:{};if(!e.match(/\\trowd/))throw new Error("RTF missing table");return t["!ref"]=$t({s:{c:0,r:0},e:{c:0,r:0}}),t}function Us(e){for(var t=0,r=1;3!=t;++t)r=256*r+(255<e[t]?255:e[t]<0?0:e[t]);return r.toString(16).toUpperCase().slice(1)}function Hs(e,t){if(0===t)return e;e=function(e){var t=e[0]/255,r=e[1]/255,n=e[2]/255,a=Math.max(t,r,n),s=Math.min(t,r,n),i=a-s;if(0==i)return[0,0,t];var o=0,e=0,e=i/(1<(s=a+s)?2-s:s);switch(a){case t:o=((r-n)/i+6)%6;break;case r:o=(n-t)/i+2;break;case n:o=(t-r)/i+4}return[o/6,e,s/2]}((e=(e=e).slice("#"===e[0]?1:0).slice(0,6),[parseInt(e.slice(0,2),16),parseInt(e.slice(2,4),16),parseInt(e.slice(4,6),16)]));return e[2]=t<0?e[2]*(1+t):1-(1-e[2])*(1-t),Us(function(e){var t,r=e[0],n=e[1],e=e[2],a=2*n*(e<.5?e:1-e),s=[e=e-a/2,e,e],i=6*r;if(0!==n)switch(0|i){case 0:case 6:t=a*i,s[0]+=a,s[1]+=t;break;case 1:s[0]+=t=a*(2-i),s[1]+=a;break;case 2:t=a*(i-2),s[1]+=a,s[2]+=t;break;case 3:s[1]+=t=a*(4-i),s[2]+=a;break;case 4:t=a*(i-4),s[2]+=a,s[0]+=t;break;case 5:s[2]+=t=a*(6-i),s[0]+=a}for(var o=0;3!=o;++o)s[o]=Math.round(255*s[o]);return s}(e))}var zs=6,Vs=15,Ws=1,Xs=zs;function js(e){return Math.floor((e+Math.round(128/Xs)/256)*Xs)}function Gs(e){return Math.floor((e-5)/Xs*100+.5)/100}function $s(e){return Math.round((e*Xs+5)/Xs*256)/256}function Ys(e){return $s(Gs(js(e)))}function Ks(e){var t=Math.abs(e-Ys(e)),r=Xs;if(.005<t)for(Xs=Ws;Xs<Vs;++Xs)Math.abs(e-Ys(e))<=t&&(t=Math.abs(e-Ys(e)),r=Xs);Xs=r}function Zs(e){e.width?(e.wpx=js(e.width),e.wch=Gs(e.wpx),e.MDW=Xs):e.wpx?(e.wch=Gs(e.wpx),e.width=$s(e.wch),e.MDW=Xs):"number"==typeof e.wch&&(e.width=$s(e.wch),e.wpx=js(e.width),e.MDW=Xs),e.customWidth&&delete e.customWidth}var Qs=96;function Js(e){return 96*e/Qs}function qs(e){return e*Qs/96}var ei={None:"none",Solid:"solid",Gray50:"mediumGray",Gray75:"darkGray",Gray25:"lightGray",HorzStripe:"darkHorizontal",VertStripe:"darkVertical",ReverseDiagStripe:"darkDown",DiagStripe:"darkUp",DiagCross:"darkGrid",ThickDiagCross:"darkTrellis",ThinHorzStripe:"lightHorizontal",ThinVertStripe:"lightVertical",ThinReverseDiagStripe:"lightDown",ThinHorzCross:"lightGrid"};var ti=["numFmtId","fillId","fontId","borderId","xfId"],ri=["applyAlignment","applyBorder","applyFill","applyFont","applyNumberFormat","applyProtection","pivotButton","quotePrefix"];var ni,ai,si,ii,oi,li=(ni=/<(?:\w+:)?numFmts([^>]*)>[\S\s]*?<\/(?:\w+:)?numFmts>/,ai=/<(?:\w+:)?cellXfs([^>]*)>[\S\s]*?<\/(?:\w+:)?cellXfs>/,si=/<(?:\w+:)?fills([^>]*)>[\S\s]*?<\/(?:\w+:)?fills>/,ii=/<(?:\w+:)?fonts([^>]*)>[\S\s]*?<\/(?:\w+:)?fonts>/,oi=/<(?:\w+:)?borders([^>]*)>[\S\s]*?<\/(?:\w+:)?borders>/,function(e,t,r){var n,a,s,i,o,l={};return e&&((n=(e=e.replace(/<!--([\s\S]*?)-->/gm,"").replace(/<!DOCTYPE[^\[]*\[[^\]]*\]>/gm,"")).match(ni))&&function(e,t,r){t.NumberFmt=[];for(var n=de(ue._table),a=0;a<n.length;++a)t.NumberFmt[n[a]]=ue._table[n[a]];var s=e[0].match(W);if(s)for(a=0;a<s.length;++a){var i=pe(s[a]);switch(me(i[0])){case"<numFmts":case"</numFmts>":case"<numFmts/>":case"<numFmts>":break;case"<numFmt":var o=we(Oe(i.formatCode)),l=parseInt(i.numFmtId,10);if(t.NumberFmt[l]=o,0<l){if(392<l){for(l=392;60<l&&null!=t.NumberFmt[l];--l);t.NumberFmt[l]=o}ue.load(o,l)}break;case"</numFmt>":break;default:if(r.WTF)throw new Error("unrecognized "+i[0]+" in numFmts")}}}(n,l,r),(n=e.match(ii))&&function(e,n,a,s){n.Fonts=[];var i={},o=!1;e[0].match(W).forEach(function(e){var t=pe(e);switch(me(t[0])){case"<fonts":case"<fonts>":case"</fonts>":break;case"<font":case"<font>":break;case"</font>":case"<font/>":n.Fonts.push(i),i={};break;case"<name":t.val&&(i.name=t.val);break;case"<name/>":case"</name>":break;case"<b":i.bold=t.val?Re(t.val):1;break;case"<b/>":i.bold=1;break;case"<i":i.italic=t.val?Re(t.val):1;break;case"<i/>":i.italic=1;break;case"<u":switch(t.val){case"none":i.underline=0;break;case"single":i.underline=1;break;case"double":i.underline=2;break;case"singleAccounting":i.underline=33;break;case"doubleAccounting":i.underline=34}break;case"<u/>":i.underline=1;break;case"<strike":i.strike=t.val?Re(t.val):1;break;case"<strike/>":i.strike=1;break;case"<outline":i.outline=t.val?Re(t.val):1;break;case"<outline/>":i.outline=1;break;case"<shadow":i.shadow=t.val?Re(t.val):1;break;case"<shadow/>":i.shadow=1;break;case"<condense":i.condense=t.val?Re(t.val):1;break;case"<condense/>":i.condense=1;break;case"<extend":i.extend=t.val?Re(t.val):1;break;case"<extend/>":i.extend=1;break;case"<sz":t.val&&(i.sz=+t.val);break;case"<sz/>":case"</sz>":break;case"<vertAlign":t.val&&(i.vertAlign=t.val);break;case"<vertAlign/>":case"</vertAlign>":break;case"<family":t.val&&(i.family=parseInt(t.val,10));break;case"<family/>":case"</family>":break;case"<scheme":t.val&&(i.scheme=t.val);break;case"<scheme/>":case"</scheme>":break;case"<charset":if("1"==t.val)break;t.codepage=h[parseInt(t.val,10)];break;case"<color":if(i.color||(i.color={}),t.auto&&(i.color.auto=Re(t.auto)),t.rgb)i.color.rgb=t.rgb.slice(-6);else if(t.indexed){i.color.index=parseInt(t.indexed,10);var r=Pr[i.color.index];if(!(r=81==i.color.index?Pr[1]:r))throw new Error(e);i.color.rgb=r[0].toString(16)+r[1].toString(16)+r[2].toString(16)}else t.theme&&(i.color.theme=parseInt(t.theme,10),t.tint&&(i.color.tint=parseFloat(t.tint)),t.theme&&a.themeElements&&a.themeElements.clrScheme&&(i.color.rgb=Hs(a.themeElements.clrScheme[i.color.theme].rgb,i.color.tint||0)));break;case"<color/>":case"</color>":break;case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":o=!0;break;case"</ext>":o=!1;break;default:if(s&&s.WTF&&!o)throw new Error("unrecognized "+t[0]+" in fonts")}})}(n,l,t,r),(n=e.match(si))&&function(e,r,n){r.Fills=[];var a={},s=!1;e[0].match(W).forEach(function(e){var t=pe(e);switch(me(t[0])){case"<fills":case"<fills>":case"</fills>":break;case"<fill>":case"<fill":case"<fill/>":a={},r.Fills.push(a);break;case"</fill>":case"<gradientFill>":break;case"<gradientFill":case"</gradientFill>":r.Fills.push(a),a={};break;case"<patternFill":case"<patternFill>":t.patternType&&(a.patternType=t.patternType);break;case"<patternFill/>":case"</patternFill>":break;case"<bgColor":a.bgColor||(a.bgColor={}),t.indexed&&(a.bgColor.indexed=parseInt(t.indexed,10)),t.theme&&(a.bgColor.theme=parseInt(t.theme,10)),t.tint&&(a.bgColor.tint=parseFloat(t.tint)),t.rgb&&(a.bgColor.rgb=t.rgb.slice(-6));break;case"<bgColor/>":case"</bgColor>":break;case"<fgColor":a.fgColor||(a.fgColor={}),t.theme&&(a.fgColor.theme=parseInt(t.theme,10)),t.tint&&(a.fgColor.tint=parseFloat(t.tint)),t.rgb&&(a.fgColor.rgb=t.rgb.slice(-6));break;case"<fgColor/>":case"</fgColor>":break;case"<stop":case"<stop/>":case"</stop>":break;case"<color":case"<color/>":case"</color>":break;case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":s=!0;break;case"</ext>":s=!1;break;default:if(n&&n.WTF&&!s)throw new Error("unrecognized "+t[0]+" in fills")}})}(n,l,r),(n=e.match(oi))&&function(e,r,n){r.Borders=[];var a={},s=!1;e[0].match(W).forEach(function(e){var t=pe(e);switch(me(t[0])){case"<borders":case"<borders>":case"</borders>":break;case"<border":case"<border>":case"<border/>":a={},t.diagonalUp&&(a.diagonalUp=t.diagonalUp),t.diagonalDown&&(a.diagonalDown=t.diagonalDown),r.Borders.push(a);break;case"</border>":case"<left/>":break;case"<left":case"<left>":case"</left>":case"<right/>":break;case"<right":case"<right>":case"</right>":case"<top/>":break;case"<top":case"<top>":case"</top>":case"<bottom/>":break;case"<bottom":case"<bottom>":case"</bottom>":break;case"<diagonal":case"<diagonal>":case"<diagonal/>":case"</diagonal>":break;case"<horizontal":case"<horizontal>":case"<horizontal/>":case"</horizontal>":break;case"<vertical":case"<vertical>":case"<vertical/>":case"</vertical>":break;case"<start":case"<start>":case"<start/>":case"</start>":break;case"<end":case"<end>":case"<end/>":case"</end>":break;case"<color":case"<color>":break;case"<color/>":case"</color>":break;case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":s=!0;break;case"</ext>":s=!1;break;default:if(n&&n.WTF&&!s)throw new Error("unrecognized "+t[0]+" in borders")}})}(n,l,r),(n=e.match(ai))&&(n=n,s=r,o=!((a=l).CellXf=[]),n[0].match(W).forEach(function(e){var t=pe(e),r=0;switch(me(t[0])){case"<cellXfs":case"<cellXfs>":case"<cellXfs/>":case"</cellXfs>":break;case"<xf":case"<xf/>":for(delete(i=t)[0],r=0;r<ti.length;++r)i[ti[r]]&&(i[ti[r]]=parseInt(i[ti[r]],10));for(r=0;r<ri.length;++r)i[ri[r]]&&(i[ri[r]]=Re(i[ri[r]]));if(392<i.numFmtId)for(r=392;60<r;--r)if(a.NumberFmt[i.numFmtId]==a.NumberFmt[r]){i.numFmtId=r;break}a.CellXf.push(i);break;case"</xf>":break;case"<alignment":case"<alignment/>":var n={};t.vertical&&(n.vertical=t.vertical),t.horizontal&&(n.horizontal=t.horizontal),null!=t.textRotation&&(n.textRotation=t.textRotation),t.indent&&(n.indent=t.indent),t.wrapText&&(n.wrapText=t.wrapText),i.alignment=n;break;case"</alignment>":break;case"<protection":case"</protection>":case"<protection/>":break;case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":o=!0;break;case"</ext>":o=!1;break;default:if(s&&s.WTF&&!o)throw new Error("unrecognized "+t[0]+" in cellXfs")}}))),l}),ci=Ge("styleSheet",null,{xmlns:Ye.main[0],"xmlns:vt":Ye.vt});function fi(e,t){if("undefined"!=typeof style_builder)return style_builder.toXml();var r,n,a,s,i=[z,ci];return e.SSF&&null!=(n=e.SSF,a=["<numFmts>"],[[5,8],[23,26],[41,44],[50,392]].forEach(function(e){for(var t=e[0];t<=e[1];++t)null!=n[t]&&(a[a.length]=Ge("numFmt",null,{numFmtId:t,formatCode:ye(n[t])}))}),r=1===a.length?"":(a[a.length]="</numFmts>",a[0]=Ge("numFmts",null,{count:a.length-2}).replace("/>",">"),a.join("")))&&(i[i.length]=r),i[i.length]='<fonts count="1"><font><sz val="12"/><color theme="1"/><name val="Calibri"/><family val="2"/><scheme val="minor"/></font></fonts>',i[i.length]='<fills count="2"><fill><patternFill patternType="none"/></fill><fill><patternFill patternType="gray125"/></fill></fills>',i[i.length]='<borders count="1"><border><left/><right/><top/><bottom/><diagonal/></border></borders>',i[i.length]='<cellStyleXfs count="1"><xf numFmtId="0" fontId="0" fillId="0" borderId="0"/></cellStyleXfs>',t=t.cellXfs,(s=[])[s.length]="<cellXfs/>",t.forEach(function(e){s[s.length]=Ge("xf",null,e)}),s[s.length]="</cellXfs>",(r=2===s.length?"":(s[0]=Ge("cellXfs",null,{count:s.length-2}).replace("/>",">"),s.join("")))&&(i[i.length]=r),i[i.length]='<cellStyles count="1"><cellStyle name="Normal" xfId="0" builtinId="0"/></cellStyles>',i[i.length]='<dxfs count="0"/>',i[i.length]='<tableStyles count="0" defaultTableStyle="TableStyleMedium9" defaultPivotStyle="PivotStyleMedium4"/>',2<i.length&&(i[i.length]="</styleSheet>",i[1]=i[1].replace("/>",">")),i.join("")}function hi(e,t){var r;(t=t||Rt(153)).write_shift(2,20*e.sz),r=e,n=(n=t)||Rt(2),r=(r.italic?2:0)|(r.strike?8:0)|(r.outline?16:0)|(r.shadow?32:0)|(r.condense?64:0)|(r.extend?128:0),n.write_shift(1,r),n.write_shift(1,0),t.write_shift(2,e.bold?700:400);var n=0;"superscript"==e.vertAlign?n=1:"subscript"==e.vertAlign&&(n=2),t.write_shift(2,n),t.write_shift(1,e.underline||0),t.write_shift(1,e.family||0),t.write_shift(1,e.charset||0),t.write_shift(1,0),yr(e.color,t);n=0;return"major"==e.scheme&&(n=1),"minor"==e.scheme&&(n=2),t.write_shift(1,n),rr(e.name,t),t.length>t.l?t.slice(0,t.l):t}Wr.STY="http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles";var ui=S(["none","solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"]),di=It;function pi(e,t){t=t||Rt(84);e=ui[e.patternType];t.write_shift(4,e=null==e?40:e);var r=0;if(40!=e)for(yr({auto:1},t),yr({auto:1},t);r<12;++r)t.write_shift(4,0);else{for(;r<4;++r)t.write_shift(4,0);for(;r<12;++r)t.write_shift(4,0)}return t.length>t.l?t.slice(0,t.l):t}function mi(e,t,r){return(r=r||Rt(16)).write_shift(2,t||0),r.write_shift(2,e.numFmtId||0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r}function gi(e,t){return(t=t||Rt(10)).write_shift(1,0),t.write_shift(1,0),t.write_shift(4,0),t.write_shift(4,0),t}var bi=It;function vi(s,i){var r;i&&(r=0,[[5,8],[23,26],[41,44],[50,392]].forEach(function(e){for(var t=e[0];t<=e[1];++t)null!=i[t]&&++r}),0!=r&&(Dt(s,"BrtBeginFmts",er(r)),[[5,8],[23,26],[41,44],[50,392]].forEach(function(e){for(var t,r,n,a=e[0];a<=e[1];++a)null!=i[a]&&Dt(s,"BrtFmt",(r=i[t=a],(n=(n=void 0)||Rt(6+4*r.length)).write_shift(2,t),rr(r,n),r=n.length>n.l?n.slice(0,n.l):n,null==n.l&&(n.l=n.length),r))}),Dt(s,"BrtEndFmts")))}function Ei(e){var t;Dt(e,"BrtBeginBorders",er(1)),Dt(e,"BrtBorder",((t=t||Rt(51)).write_shift(1,0),gi(0,t),gi(0,t),gi(0,t),gi(0,t),gi(0,t),t.length>t.l?t.slice(0,t.l):t)),Dt(e,"BrtEndBorders")}function wi(e){var t,r;Dt(e,"BrtBeginStyles",er(1)),Dt(e,"BrtStyle",(t={xfId:0,builtinId:0,name:"Normal"},(r=r||Rt(52)).write_shift(4,t.xfId),r.write_shift(2,1),r.write_shift(1,+t.builtinId),r.write_shift(1,0),hr(t.name||"",r),r.length>r.l?r.slice(0,r.l):r)),Dt(e,"BrtEndStyles")}function Si(e){var t,r,n,a;Dt(e,"BrtBeginTableStyles",(t=0,r="TableStyleMedium9",n="PivotStyleMedium4",(a=Rt(2052)).write_shift(4,t),hr(r,a),hr(n,a),a.length>a.l?a.slice(0,a.l):a)),Dt(e,"BrtEndTableStyles")}function _i(e,t){var r,n=Ft();return Dt(n,"BrtBeginStyleSheet"),vi(n,e.SSF),Dt(e=n,"BrtBeginFonts",er(1)),Dt(e,"BrtFont",hi({sz:12,color:{theme:1},name:"Calibri",family:2,scheme:"minor"})),Dt(e,"BrtEndFonts"),Dt(e=n,"BrtBeginFills",er(2)),Dt(e,"BrtFill",pi({patternType:"none"})),Dt(e,"BrtFill",pi({patternType:"gray125"})),Dt(e,"BrtEndFills"),Ei(n),Dt(e=n,"BrtBeginCellStyleXFs",er(1)),Dt(e,"BrtXF",mi({numFmtId:0,fontId:0,fillId:0,borderId:0},65535)),Dt(e,"BrtEndCellStyleXFs"),r=n,t=t.cellXfs,Dt(r,"BrtBeginCellXFs",er(t.length)),t.forEach(function(e){Dt(r,"BrtXF",mi(e,0))}),Dt(r,"BrtEndCellXFs"),wi(n),Dt(t=n,"BrtBeginDXFs",er(0)),Dt(t,"BrtEndDXFs"),Si(n),Dt(n,"BrtEndStyleSheet"),n.end()}function yi(e,r,n){r.themeElements.clrScheme=[];var a={};(e[0].match(W)||[]).forEach(function(e){var t=pe(e);switch(t[0]){case"<a:clrScheme":case"</a:clrScheme>":break;case"<a:srgbClr":a.rgb=t.val;break;case"<a:sysClr":a.rgb=t.lastClr;break;case"<a:dk1>":case"</a:dk1>":case"<a:lt1>":case"</a:lt1>":case"<a:dk2>":case"</a:dk2>":case"<a:lt2>":case"</a:lt2>":case"<a:accent1>":case"</a:accent1>":case"<a:accent2>":case"</a:accent2>":case"<a:accent3>":case"</a:accent3>":case"<a:accent4>":case"</a:accent4>":case"<a:accent5>":case"</a:accent5>":case"<a:accent6>":case"</a:accent6>":case"<a:hlink>":case"</a:hlink>":case"<a:folHlink>":case"</a:folHlink>":"/"===t[0].charAt(1)?(r.themeElements.clrScheme.push(a),a={}):a.name=t[0].slice(3,t[0].length-1);break;default:if(n&&n.WTF)throw new Error("Unrecognized "+t[0]+" in clrScheme")}})}function Ci(){}function Bi(){}Wr.THEME="http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme";var Ti=/<a:clrScheme([^>]*)>[\s\S]*<\/a:clrScheme>/,ki=/<a:fontScheme([^>]*)>[\s\S]*<\/a:fontScheme>/,xi=/<a:fmtScheme([^>]*)>[\s\S]*<\/a:fmtScheme>/;var Ai=/<a:themeElements([^>]*)>[\s\S]*<\/a:themeElements>/;function Ii(e,t){if(!e||0===e.length)return Ii(Ri());var r,n,a,s,i={};if(!(e=e.match(Ai)))throw new Error("themeElements not found in theme");return r=e[0],a=t,(n=i).themeElements={},[["clrScheme",Ti,yi],["fontScheme",ki,Ci],["fmtScheme",xi,Bi]].forEach(function(e){if(!(s=r.match(e[1])))throw new Error(e[0]+" not found in themeElements");e[2](s,n,a)}),i}function Ri(e,t){if(t&&t.themeXLSX)return t.themeXLSX;t=[z];return t[t.length]='<a:theme xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" name="Office Theme">',t[t.length]="<a:themeElements>",t[t.length]='<a:clrScheme name="Office">',t[t.length]='<a:dk1><a:sysClr val="windowText" lastClr="000000"/></a:dk1>',t[t.length]='<a:lt1><a:sysClr val="window" lastClr="FFFFFF"/></a:lt1>',t[t.length]='<a:dk2><a:srgbClr val="1F497D"/></a:dk2>',t[t.length]='<a:lt2><a:srgbClr val="EEECE1"/></a:lt2>',t[t.length]='<a:accent1><a:srgbClr val="4F81BD"/></a:accent1>',t[t.length]='<a:accent2><a:srgbClr val="C0504D"/></a:accent2>',t[t.length]='<a:accent3><a:srgbClr val="9BBB59"/></a:accent3>',t[t.length]='<a:accent4><a:srgbClr val="8064A2"/></a:accent4>',t[t.length]='<a:accent5><a:srgbClr val="4BACC6"/></a:accent5>',t[t.length]='<a:accent6><a:srgbClr val="F79646"/></a:accent6>',t[t.length]='<a:hlink><a:srgbClr val="0000FF"/></a:hlink>',t[t.length]='<a:folHlink><a:srgbClr val="800080"/></a:folHlink>',t[t.length]="</a:clrScheme>",t[t.length]='<a:fontScheme name="Office">',t[t.length]="<a:majorFont>",t[t.length]='<a:latin typeface="Cambria"/>',t[t.length]='<a:ea typeface=""/>',t[t.length]='<a:cs typeface=""/>',t[t.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',t[t.length]='<a:font script="Hang" typeface="맑은 고딕"/>',t[t.length]='<a:font script="Hans" typeface="宋体"/>',t[t.length]='<a:font script="Hant" typeface="新細明體"/>',t[t.length]='<a:font script="Arab" typeface="Times New Roman"/>',t[t.length]='<a:font script="Hebr" typeface="Times New Roman"/>',t[t.length]='<a:font script="Thai" typeface="Tahoma"/>',t[t.length]='<a:font script="Ethi" typeface="Nyala"/>',t[t.length]='<a:font script="Beng" typeface="Vrinda"/>',t[t.length]='<a:font script="Gujr" typeface="Shruti"/>',t[t.length]='<a:font script="Khmr" typeface="MoolBoran"/>',t[t.length]='<a:font script="Knda" typeface="Tunga"/>',t[t.length]='<a:font script="Guru" typeface="Raavi"/>',t[t.length]='<a:font script="Cans" typeface="Euphemia"/>',t[t.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',t[t.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',t[t.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',t[t.length]='<a:font script="Thaa" typeface="MV Boli"/>',t[t.length]='<a:font script="Deva" typeface="Mangal"/>',t[t.length]='<a:font script="Telu" typeface="Gautami"/>',t[t.length]='<a:font script="Taml" typeface="Latha"/>',t[t.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',t[t.length]='<a:font script="Orya" typeface="Kalinga"/>',t[t.length]='<a:font script="Mlym" typeface="Kartika"/>',t[t.length]='<a:font script="Laoo" typeface="DokChampa"/>',t[t.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',t[t.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',t[t.length]='<a:font script="Viet" typeface="Times New Roman"/>',t[t.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',t[t.length]='<a:font script="Geor" typeface="Sylfaen"/>',t[t.length]="</a:majorFont>",t[t.length]="<a:minorFont>",t[t.length]='<a:latin typeface="Calibri"/>',t[t.length]='<a:ea typeface=""/>',t[t.length]='<a:cs typeface=""/>',t[t.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',t[t.length]='<a:font script="Hang" typeface="맑은 고딕"/>',t[t.length]='<a:font script="Hans" typeface="宋体"/>',t[t.length]='<a:font script="Hant" typeface="新細明體"/>',t[t.length]='<a:font script="Arab" typeface="Arial"/>',t[t.length]='<a:font script="Hebr" typeface="Arial"/>',t[t.length]='<a:font script="Thai" typeface="Tahoma"/>',t[t.length]='<a:font script="Ethi" typeface="Nyala"/>',t[t.length]='<a:font script="Beng" typeface="Vrinda"/>',t[t.length]='<a:font script="Gujr" typeface="Shruti"/>',t[t.length]='<a:font script="Khmr" typeface="DaunPenh"/>',t[t.length]='<a:font script="Knda" typeface="Tunga"/>',t[t.length]='<a:font script="Guru" typeface="Raavi"/>',t[t.length]='<a:font script="Cans" typeface="Euphemia"/>',t[t.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',t[t.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',t[t.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',t[t.length]='<a:font script="Thaa" typeface="MV Boli"/>',t[t.length]='<a:font script="Deva" typeface="Mangal"/>',t[t.length]='<a:font script="Telu" typeface="Gautami"/>',t[t.length]='<a:font script="Taml" typeface="Latha"/>',t[t.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',t[t.length]='<a:font script="Orya" typeface="Kalinga"/>',t[t.length]='<a:font script="Mlym" typeface="Kartika"/>',t[t.length]='<a:font script="Laoo" typeface="DokChampa"/>',t[t.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',t[t.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',t[t.length]='<a:font script="Viet" typeface="Arial"/>',t[t.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',t[t.length]='<a:font script="Geor" typeface="Sylfaen"/>',t[t.length]="</a:minorFont>",t[t.length]="</a:fontScheme>",t[t.length]='<a:fmtScheme name="Office">',t[t.length]="<a:fillStyleLst>",t[t.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',t[t.length]='<a:gradFill rotWithShape="1">',t[t.length]="<a:gsLst>",t[t.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="50000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',t[t.length]='<a:gs pos="35000"><a:schemeClr val="phClr"><a:tint val="37000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',t[t.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="15000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',t[t.length]="</a:gsLst>",t[t.length]='<a:lin ang="16200000" scaled="1"/>',t[t.length]="</a:gradFill>",t[t.length]='<a:gradFill rotWithShape="1">',t[t.length]="<a:gsLst>",t[t.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="100000"/><a:shade val="100000"/><a:satMod val="130000"/></a:schemeClr></a:gs>',t[t.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="50000"/><a:shade val="100000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',t[t.length]="</a:gsLst>",t[t.length]='<a:lin ang="16200000" scaled="0"/>',t[t.length]="</a:gradFill>",t[t.length]="</a:fillStyleLst>",t[t.length]="<a:lnStyleLst>",t[t.length]='<a:ln w="9525" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"><a:shade val="95000"/><a:satMod val="105000"/></a:schemeClr></a:solidFill><a:prstDash val="solid"/></a:ln>',t[t.length]='<a:ln w="25400" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',t[t.length]='<a:ln w="38100" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',t[t.length]="</a:lnStyleLst>",t[t.length]="<a:effectStyleLst>",t[t.length]="<a:effectStyle>",t[t.length]="<a:effectLst>",t[t.length]='<a:outerShdw blurRad="40000" dist="20000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="38000"/></a:srgbClr></a:outerShdw>',t[t.length]="</a:effectLst>",t[t.length]="</a:effectStyle>",t[t.length]="<a:effectStyle>",t[t.length]="<a:effectLst>",t[t.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',t[t.length]="</a:effectLst>",t[t.length]="</a:effectStyle>",t[t.length]="<a:effectStyle>",t[t.length]="<a:effectLst>",t[t.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',t[t.length]="</a:effectLst>",t[t.length]='<a:scene3d><a:camera prst="orthographicFront"><a:rot lat="0" lon="0" rev="0"/></a:camera><a:lightRig rig="threePt" dir="t"><a:rot lat="0" lon="0" rev="1200000"/></a:lightRig></a:scene3d>',t[t.length]='<a:sp3d><a:bevelT w="63500" h="25400"/></a:sp3d>',t[t.length]="</a:effectStyle>",t[t.length]="</a:effectStyleLst>",t[t.length]="<a:bgFillStyleLst>",t[t.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',t[t.length]='<a:gradFill rotWithShape="1">',t[t.length]="<a:gsLst>",t[t.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="40000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',t[t.length]='<a:gs pos="40000"><a:schemeClr val="phClr"><a:tint val="45000"/><a:shade val="99000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',t[t.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="20000"/><a:satMod val="255000"/></a:schemeClr></a:gs>',t[t.length]="</a:gsLst>",t[t.length]='<a:path path="circle"><a:fillToRect l="50000" t="-80000" r="50000" b="180000"/></a:path>',t[t.length]="</a:gradFill>",t[t.length]='<a:gradFill rotWithShape="1">',t[t.length]="<a:gsLst>",t[t.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="80000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',t[t.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="30000"/><a:satMod val="200000"/></a:schemeClr></a:gs>',t[t.length]="</a:gsLst>",t[t.length]='<a:path path="circle"><a:fillToRect l="50000" t="50000" r="50000" b="50000"/></a:path>',t[t.length]="</a:gradFill>",t[t.length]="</a:bgFillStyleLst>",t[t.length]="</a:fmtScheme>",t[t.length]="</a:themeElements>",t[t.length]="<a:objectDefaults>",t[t.length]="<a:spDef>",t[t.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="1"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="3"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="2"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="lt1"/></a:fontRef></a:style>',t[t.length]="</a:spDef>",t[t.length]="<a:lnDef>",t[t.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="2"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="0"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="1"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="tx1"/></a:fontRef></a:style>',t[t.length]="</a:lnDef>",t[t.length]="</a:objectDefaults>",t[t.length]="<a:extraClrSchemeLst/>",t[t.length]="</a:theme>",t.join("")}function Oi(e){var t={};switch(t.xclrType=e.read_shift(2),t.nTintShade=e.read_shift(2),t.xclrType){case 0:e.l+=4;break;case 1:t.xclrValue=It(e,4);break;case 2:t.xclrValue=Vn(e);break;case 3:t.xclrValue=e.read_shift(4);break;case 4:e.l+=4}return e.l+=8,t}function Fi(e){var t=e.read_shift(2),r=e.read_shift(2)-4,n=[t];switch(t){case 4:case 5:case 7:case 8:case 9:case 10:case 11:case 13:n[1]=Oi(e);break;case 6:n[1]=It(e,r);break;case 14:case 15:n[1]=e.read_shift(1==r?1:2);break;default:throw new Error("Unrecognized ExtProp type: "+t+" "+r)}return n}Wr.IMG="http://schemas.openxmlformats.org/officeDocument/2006/relationships/image",Wr.DRAW="http://schemas.openxmlformats.org/officeDocument/2006/relationships/drawing";var Di=1024;function Pi(e,t,r,n,a){for(var s,i,o=0;o!=t.length;++o){var l=t[o],c=(s=M(e,l.replace(/^\//,""),!0),i=a,(".bin"===l.slice(-4)?function(e,n){var a=[],s=[],i={},o=!1;return Ot(e,function(e,t,r){switch(r){case 632:s.push(e);break;case 635:i=e;break;case 637:i.t=e.t,i.h=e.h,i.r=e.r;break;case 636:if(i.author=s[i.iauthor],delete i.iauthor,n.sheetRows&&n.sheetRows<=i.rfx.r)break;i.t||(i.t=""),delete i.rfx,a.push(i);break;case 3072:break;case 35:o=!0;break;case 36:o=!1;break;case 37:case 38:break;default:if(!(0<(t||"").indexOf("Begin"))&&!(0<(t||"").indexOf("End"))&&(!o||n.WTF))throw new Error("Unexpected record "+r+" "+t)}}),a}:function(e,n){if(e.match(/<(?:\w+:)?comments *\/>/))return[];var a=[],s=[],t=e.match(/<(?:\w+:)?authors>([\s\S]*)<\/(?:\w+:)?authors>/);t&&t[1]&&t[1].split(/<\/\w*:?author>/).forEach(function(e){""===e||""===e.trim()||(e=e.match(/<(?:\w+:)?author[^>]*>(.*)/))&&a.push(e[1])});e=e.match(/<(?:\w+:)?commentList>([\s\S]*)<\/(?:\w+:)?commentList>/);e&&e[1]&&e[1].split(/<\/\w*:?comment>/).forEach(function(e){var t,r;""===e||""===e.trim()||(t=e.match(/<(?:\w+:)?comment[^>]*>/))&&(t={author:(r=pe(t[0])).authorId&&a[r.authorId]||"sheetjsghost",ref:r.ref,guid:r.guid},r=Xt(r.ref),n.sheetRows&&n.sheetRows<=r.r||(e=!!(e=e.match(/<(?:\w+:)?text>([\s\S]*)<\/(?:\w+:)?text>/))&&!!e[1]&&us(e[1])||{r:"",t:"",h:""},t.r=e.r,"<t></t>"==e.r&&(e.t=e.h=""),t.t=e.t.replace(/\r\n/g,"\n").replace(/\r/g,"\n"),n.cellHTML&&(t.h=e.h),s.push(t)))});return s})(s,i));if(c&&c.length)for(var f=de(r),h=0;h!=f.length;++h){var u=f[h],d=n[u];d&&d[l]&&function(n,e){var a,s=Array.isArray(n);e.forEach(function(e){var t=Xt(e.ref);(a=s?(n[t.r]||(n[t.r]=[]),n[t.r][t.c]):n[e.ref])||(a={},s?n[t.r][t.c]=a:n[e.ref]=a,(r=Yt(n["!ref"]||"BDWGO1000001:A1")).s.r>t.r&&(r.s.r=t.r),r.e.r<t.r&&(r.e.r=t.r),r.s.c>t.c&&(r.s.c=t.c),r.e.c<t.c&&(r.e.c=t.c),(r=$t(r))!==n["!ref"]&&(n["!ref"]=r)),a.c||(a.c=[]);var r={a:e.author,t:e.t,r:e.r};e.h&&(r.h=e.h),a.c.push(r)})}(r[u],c)}}}Wr.CMNT="http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments";var Ni=Ge("comments",null,{xmlns:Ye.main[0]});function Li(e){var r=[z,Ni],n=[];return r.push("<authors>"),e.forEach(function(e){e[1].forEach(function(e){e=ye(e.a);-1<n.indexOf(e)||(n.push(e),r.push("<author>"+e+"</author>"))})}),r.push("</authors>"),r.push("<commentList>"),e.forEach(function(t){t[1].forEach(function(e){r.push('<comment ref="'+t[0]+'" authorId="'+n.indexOf(ye(e.a))+'"><text>'),r.push(Xe("t",null==e.t?"":ye(e.t))),r.push("</text></comment>")})}),r.push("</commentList>"),2<r.length&&(r[r.length]="</comments>",r[1]=r[1].replace("/>",">")),r.join("")}var Mi=tr;function Ui(e){var a=Ft(),s=[];return Dt(a,"BrtBeginComments"),Dt(a,"BrtBeginCommentAuthors"),e.forEach(function(e){e[1].forEach(function(e){-1<s.indexOf(e.a)||(s.push(e.a.slice(0,54)),Dt(a,"BrtCommentAuthor",rr(e.a.slice(0,54))))})}),Dt(a,"BrtEndCommentAuthors"),Dt(a,"BrtBeginCommentList"),e.forEach(function(n){n[1].forEach(function(e){e.iauthor=s.indexOf(e.a);var t,r={s:Xt(n[0]),e:Xt(n[0])};Dt(a,"BrtBeginComment",(r=[r,e],(t=null==t?Rt(36):t).write_shift(4,r[1].iauthor),vr(r[0],t),t.write_shift(4,0),t.write_shift(4,0),t.write_shift(4,0),t.write_shift(4,0),t)),e.t&&0<e.t.length&&Dt(a,"BrtCommentText",sr(e)),Dt(a,"BrtEndComment"),delete e.iauthor})}),Dt(a,"BrtEndCommentList"),Dt(a,"BrtEndComments"),a.end()}var Hi="application/vnd.ms-office.vbaProject";var zi=["xlsb","xlsm","xlam","biff8","xla"];Wr.DS="http://schemas.openxmlformats.org/officeDocument/2006/relationships/dialogsheet",Wr.MS="http://schemas.microsoft.com/office/2006/relationships/xlMacrosheet";var Vi,Wi,Xi=(Vi=/(^|[^A-Za-z])R(\[?)(-?\d+|)\]?C(\[?)(-?\d+|)\]?/g,Wi={r:0,c:0},function(e,t){return Wi=t,e.replace(Vi,ji)});function ji(e,t,r,n,a,s){var i=0<n.length?0|parseInt(n,10):0,o=0<s.length?0|parseInt(s,10):0;o<0&&0===a.length&&(o=0);var l=!1,c=!1;return(l=0<a.length||0==s.length?!0:l)?o+=Wi.c:--o,(c=0<r.length||0==n.length?!0:c)?i+=Wi.r:--i,t+(l?"":"$")+Vt(o)+(c?"":"$")+Ht(i)}var Gi=/(^|[^._A-Z0-9])([$]?)([A-Z]{1,2}|[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D])([$]?)([1-9]\d{0,5}|10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6])(?![_.\(A-Za-z0-9])/g,$i=function(e,i){return e.replace(Gi,function(e,t,r,n,a,s){n=zt(n)-(r?0:i.c),s=Ut(s)-(a?0:i.r);return t+"R"+(0==s?"":a?1+s:"["+s+"]")+"C"+(0==n?"":r?1+n:"["+n+"]")})};function Yi(e,t,r){var i,t=Gt(t).s,r=Xt(r),t={r:r.r-t.r,c:r.c-t.c};return i=t,e.replace(Gi,function(e,t,r,n,a,s){return t+("$"==r?r+n:Vt(zt(n)+i.c))+("$"==a?a+s:Ht(Ut(s)+i.r))})}function Ki(e){return e.replace(/_xlfn\./g,"")}function Zi(e){e.l+=1}function Qi(e,t){t=e.read_shift(1==t?1:2);return[16383&t,t>>14&1,t>>15&1]}function Ji(e,t,r){var n=2;if(r){if(2<=r.biff&&r.biff<=5)return qi(e);12==r.biff&&(n=4)}var a=e.read_shift(n),r=e.read_shift(n),n=Qi(e,2),e=Qi(e,2);return{s:{r:a,c:n[0],cRel:n[1],rRel:n[2]},e:{r:r,c:e[0],cRel:e[1],rRel:e[2]}}}function qi(e){var t=Qi(e,2),r=Qi(e,2),n=e.read_shift(1),e=e.read_shift(1);return{s:{r:t[0],c:n,cRel:t[1],rRel:t[2]},e:{r:r[0],c:e,cRel:r[1],rRel:r[2]}}}function eo(e,t,r){if(r&&2<=r.biff&&r.biff<=5)return a=Qi(n=e,2),n=n.read_shift(1),{r:a[0],c:n,cRel:a[1],rRel:a[2]};var n,a,r=e.read_shift(r&&12==r.biff?4:2),e=Qi(e,2);return{r:r,c:e[0],cRel:e[1],rRel:e[2]}}function to(e,t,r){r=r&&r.biff?r.biff:8;if(2<=r&&r<=5)return function(e){var t=e.read_shift(2),r=e.read_shift(1),n=(32768&t)>>15,e=(16384&t)>>14;t&=16383,1==n&&8192<=t&&(t-=16384);1==e&&128<=r&&(r-=256);return{r:t,c:r,cRel:e,rRel:n}}(e);var n=e.read_shift(12<=r?4:2),a=e.read_shift(2),r=(16384&a)>>14,e=(32768&a)>>15;if(a&=16383,1==e)for(;524287<n;)n-=1048576;if(1==r)for(;8191<a;)a-=16384;return{r:n,c:a,cRel:r,rRel:e}}function ro(e){return[e.read_shift(1),e.read_shift(1)]}function no(e,t,r){var n=0,a=0;12==r.biff?(n=e.read_shift(4),a=e.read_shift(4)):(a=1+e.read_shift(1),n=1+e.read_shift(2)),2<=r.biff&&r.biff<8&&(--n,0==--a&&(a=256));for(var s=0,i=[];s!=n&&(i[s]=[]);++s)for(var o=0;o!=a;++o)i[s][o]=function(e,t){var r=[e.read_shift(1)];if(12==t)switch(r[0]){case 2:r[0]=4;break;case 4:r[0]=16;break;case 0:r[0]=1;break;case 1:r[0]=2}switch(r[0]){case 4:r[1]=In(e,1)?"TRUE":"FALSE",12!=t&&(e.l+=7);break;case 37:case 16:r[1]=Sr[e[e.l]],e.l+=12==t?4:8;break;case 0:e.l+=8;break;case 1:r[1]=Er(e);break;case 2:r[1]=Mn(e,0,{biff:0<t&&t<8?2:t});break;default:throw new Error("Bad SerAr: "+r[0])}return r}(e,r.biff);return i}function ao(e,t,r){return e.l+=2,[(e=(n=e).read_shift(2),n=n.read_shift(2),{r:e,c:255&n,fQuoted:!!(16384&n),cRel:n>>15,rRel:n>>15})];var n}function so(e){return e.l+=6,[]}var io=ao,oo=so,lo=so,co=ao;function fo(e){return e.l+=2,[On(e),1&e.read_shift(2)]}var r=ao,y=fo,ho=so,uo=ao,po=ao,mo=["Data","All","Headers","??","?Data2","??","?DataHeaders","??","Totals","??","??","??","?DataTotals","??","??","??","?Current"];var go={1:{n:"PtgExp",f:function(e,t,r){return e.l++,r&&12==r.biff?[e.read_shift(4,"i"),0]:[e.read_shift(2),e.read_shift(r&&2==r.biff?1:2)]}},2:{n:"PtgTbl",f:It},3:{n:"PtgAdd",f:Zi},4:{n:"PtgSub",f:Zi},5:{n:"PtgMul",f:Zi},6:{n:"PtgDiv",f:Zi},7:{n:"PtgPower",f:Zi},8:{n:"PtgConcat",f:Zi},9:{n:"PtgLt",f:Zi},10:{n:"PtgLe",f:Zi},11:{n:"PtgEq",f:Zi},12:{n:"PtgGe",f:Zi},13:{n:"PtgGt",f:Zi},14:{n:"PtgNe",f:Zi},15:{n:"PtgIsect",f:Zi},16:{n:"PtgUnion",f:Zi},17:{n:"PtgRange",f:Zi},18:{n:"PtgUplus",f:Zi},19:{n:"PtgUminus",f:Zi},20:{n:"PtgPercent",f:Zi},21:{n:"PtgParen",f:Zi},22:{n:"PtgMissArg",f:Zi},23:{n:"PtgStr",f:function(e,t,r){return e.l++,Pn(e,0,r)}},26:{n:"PtgSheet",f:function(e,t,r){return e.l+=5,e.l+=2,e.l+=2==r.biff?1:4,["PTGSHEET"]}},27:{n:"PtgEndSheet",f:function(e,t,r){return e.l+=2==r.biff?4:5,["PTGENDSHEET"]}},28:{n:"PtgErr",f:function(e){return e.l++,Sr[e.read_shift(1)]}},29:{n:"PtgBool",f:function(e){return e.l++,0!==e.read_shift(1)}},30:{n:"PtgInt",f:function(e){return e.l++,e.read_shift(2)}},31:{n:"PtgNum",f:function(e){return e.l++,Er(e)}},32:{n:"PtgArray",f:function(e,t,r){var n=(96&e[e.l++])>>5;return e.l+=2==r.biff?6:12==r.biff?14:7,[n]}},33:{n:"PtgFunc",f:function(e,t,r){var n=(96&e[e.l])>>5;return e.l+=1,r=e.read_shift(r&&r.biff<=3?1:2),[Fo[r],Oo[r],n]}},34:{n:"PtgFuncVar",f:function(e,t,r){var n=e[e.l++],a=e.read_shift(1),e=r&&r.biff<=3?[88==n?-1:0,e.read_shift(1)]:[e[e.l+1]>>7,32767&e.read_shift(2)];return[a,(0===e[0]?Oo:Ro)[e[1]]]}},35:{n:"PtgName",f:function(e,t,r){var n=e.read_shift(1)>>>5&3,a=!r||8<=r.biff?4:2,a=e.read_shift(a);switch(r.biff){case 2:e.l+=5;break;case 3:case 4:e.l+=8;break;case 5:e.l+=12}return[n,0,a]}},36:{n:"PtgRef",f:function(e,t,r){var n=(96&e[e.l])>>5;return e.l+=1,[n,eo(e,0,r)]}},37:{n:"PtgArea",f:function(e,t,r){return[(96&e[e.l++])>>5,Ji(e,2<=r.biff&&r.biff,r)]}},38:{n:"PtgMemArea",f:function(e,t,r){var n=e.read_shift(1)>>>5&3;return e.l+=r&&2==r.biff?3:4,[n,e.read_shift(r&&2==r.biff?1:2)]}},39:{n:"PtgMemErr",f:It},40:{n:"PtgMemNoMem",f:It},41:{n:"PtgMemFunc",f:function(e,t,r){return[e.read_shift(1)>>>5&3,e.read_shift(r&&2==r.biff?1:2)]}},42:{n:"PtgRefErr",f:function(e,t,r){var n=e.read_shift(1)>>>5&3;return e.l+=4,r.biff<8&&e.l--,12==r.biff&&(e.l+=2),[n]}},43:{n:"PtgAreaErr",f:function(e,t,r){var n=(96&e[e.l++])>>5;return e.l+=r&&8<r.biff?12:r.biff<8?6:8,[n]}},44:{n:"PtgRefN",f:function(e,t,r){var n=(96&e[e.l])>>5;return e.l+=1,[n,to(e,0,r)]}},45:{n:"PtgAreaN",f:function(e,t,r){return[(96&e[e.l++])>>5,function(e,t){if(t.biff<8)return qi(e);var r=e.read_shift(12==t.biff?4:2),n=e.read_shift(12==t.biff?4:2),t=Qi(e,2),e=Qi(e,2);return{s:{r:r,c:t[0],cRel:t[1],rRel:t[2]},e:{r:n,c:e[0],cRel:e[1],rRel:e[2]}}}(e,r)]}},46:{n:"PtgMemAreaN",f:function(e){return[e.read_shift(1)>>>5&3,e.read_shift(2)]}},47:{n:"PtgMemNoMemN",f:function(e){return[e.read_shift(1)>>>5&3,e.read_shift(2)]}},57:{n:"PtgNameX",f:function(e,t,r){return 5==r.biff?function(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2,"i");e.l+=8;var n=e.read_shift(2);return e.l+=12,[t,r,n]}(e):[e.read_shift(1)>>>5&3,e.read_shift(2),e.read_shift(4)]}},58:{n:"PtgRef3d",f:function(e,t,r){var n=(96&e[e.l])>>5;e.l+=1;var a=e.read_shift(2);return r&&5==r.biff&&(e.l+=12),[n,a,eo(e,0,r)]}},59:{n:"PtgArea3d",f:function(e,t,r){var n=(96&e[e.l++])>>5,a=e.read_shift(2,"i");if(r)switch(r.biff){case 5:e.l+=12,0;break;case 12:0}return[n,a,Ji(e,0,r)]}},60:{n:"PtgRefErr3d",f:function(e,t,r){var n=(96&e[e.l++])>>5,a=e.read_shift(2),s=4;if(r)switch(r.biff){case 5:s=15;break;case 12:s=6}return e.l+=s,[n,a]}},61:{n:"PtgAreaErr3d",f:function(e,t,r){var n=(96&e[e.l++])>>5,a=e.read_shift(2),s=8;if(r)switch(r.biff){case 5:e.l+=12,s=6;break;case 12:s=12}return e.l+=s,[n,a]}},255:{}},bo={64:32,96:32,65:33,97:33,66:34,98:34,67:35,99:35,68:36,100:36,69:37,101:37,70:38,102:38,71:39,103:39,72:40,104:40,73:41,105:41,74:42,106:42,75:43,107:43,76:44,108:44,77:45,109:45,78:46,110:46,79:47,111:47,88:34,120:34,89:57,121:57,90:58,122:58,91:59,123:59,92:60,124:60,93:61,125:61};!function(){for(var e in bo)go[e]=go[bo[e]]}();var vo={1:{n:"PtgElfLel",f:fo},2:{n:"PtgElfRw",f:uo},3:{n:"PtgElfCol",f:io},6:{n:"PtgElfRwV",f:po},7:{n:"PtgElfColV",f:co},10:{n:"PtgElfRadical",f:r},11:{n:"PtgElfRadicalS",f:ho},13:{n:"PtgElfColS",f:oo},15:{n:"PtgElfColSV",f:lo},16:{n:"PtgElfRadicalLel",f:y},25:{n:"PtgList",f:function(e){e.l+=2;var t=e.read_shift(2),r=e.read_shift(2),n=e.read_shift(4),a=e.read_shift(2),e=e.read_shift(2);return{ixti:t,coltype:3&r,rt:mo[r>>2&31],idx:n,c:a,C:e}}},29:{n:"PtgSxName",f:function(e){return e.l+=2,[e.read_shift(4)]}},255:{}},Eo={0:{n:"PtgAttrNoop",f:function(e){return e.l+=4,[0,0]}},1:{n:"PtgAttrSemi",f:function(e,t,r){var n=255&e[e.l+1]?1:0;return e.l+=r&&2==r.biff?3:4,[n]}},2:{n:"PtgAttrIf",f:function(e,t,r){var n=255&e[e.l+1]?1:0;return e.l+=2,[n,e.read_shift(r&&2==r.biff?1:2)]}},4:{n:"PtgAttrChoose",f:function(e,t,r){e.l+=2;for(var n=e.read_shift(r&&2==r.biff?1:2),a=[],s=0;s<=n;++s)a.push(e.read_shift(r&&2==r.biff?1:2));return a}},8:{n:"PtgAttrGoto",f:function(e,t,r){var n=255&e[e.l+1]?1:0;return e.l+=2,[n,e.read_shift(r&&2==r.biff?1:2)]}},16:{n:"PtgAttrSum",f:function(e,t,r){e.l+=r&&2==r.biff?3:4}},32:{n:"PtgAttrBaxcel",f:function(e){var t=1&e[e.l+1];return e.l+=4,[t,1]}},64:{n:"PtgAttrSpace",f:function(e){return e.read_shift(2),ro(e)}},65:{n:"PtgAttrSpaceSemi",f:function(e){return e.read_shift(2),ro(e)}},128:{n:"PtgAttrIfError",f:function(e){var t=255&e[e.l+1]?1:0;return e.l+=2,[t,e.read_shift(2)]}},255:{}};function wo(e,t,r,n){if(n.biff<8)return It(e,t);for(var a=e.l+t,s=[],i=0;i!==r.length;++i)switch(r[i][0]){case"PtgArray":r[i][1]=no(e,0,n),s.push(r[i][1]);break;case"PtgMemArea":r[i][2]=function(e,t){for(var r=e.read_shift(12==t.biff?4:2),n=[],a=0;a!=r;++a)n.push((12==t.biff?br:$n)(e,8));return n}(e,(r[i][1],n)),s.push(r[i][2]);break;case"PtgExp":n&&12==n.biff&&(r[i][1][1]=e.read_shift(4),s.push(r[i][1]));break;case"PtgList":case"PtgElfRadicalS":case"PtgElfColS":case"PtgElfColSV":throw"Unsupported "+r[i][0]}return 0!==(t=a-e.l)&&s.push(It(e,t)),s}function So(e,t,r){for(var n,a,s=e.l+t,i=[];s!=e.l;)t=s-e.l,a=e[e.l],n=go[a],(n=24===a||25===a?(24===a?vo:Eo)[e[e.l+1]]:n)&&n.f?i.push([n.n,n.f(e,t,r)]):It(e,t);return i}Eo[33]=Eo[32];var _o={PtgAdd:"+",PtgConcat:"&",PtgDiv:"/",PtgEq:"=",PtgGe:">=",PtgGt:">",PtgLe:"<=",PtgLt:"<",PtgMul:"*",PtgNe:"<>",PtgPower:"^",PtgSub:"-"};function yo(e,t,r){return function(e,t){if(!(e||t&&t.biff<=5&&2<=t.biff))throw new Error("empty sheet name");return-1<e.indexOf(" ")?"'"+e+"'":e}(function(e,t,r){if(!e)return"SH33TJSERR0";if(8<r.biff&&(!e.XTI||!e.XTI[t]))return e.SheetNames[t];if(!e.XTI)return"SH33TJSERR6";var n=e.XTI[t];if(r.biff<8)return 1e4<t&&(t-=65536),0==(t=t<0?-t:t)?"":e.XTI[t-1];if(!n)return"SH33TJSERR1";var a="";if(8<r.biff)switch(e[n[0]][0]){case 357:return a=-1==n[1]?"#REF":e.SheetNames[n[1]],n[1]==n[2]?a:a+":"+e.SheetNames[n[2]];case 358:return null!=r.SID?e.SheetNames[r.SID]:"SH33TJSSAME"+e[n[0]][0];default:return"SH33TJSSRC"+e[n[0]][0]}switch(e[n[0]][0][0]){case 1025:return a=-1==n[1]?"#REF":e.SheetNames[n[1]]||"SH33TJSERR3",n[1]==n[2]?a:a+":"+e.SheetNames[n[2]];case 14849:return"SH33TJSERR8";default:return e[n[0]][0][3]?(a=-1==n[1]?"#REF":e[n[0]][0][3][n[1]]||"SH33TJSERR4",n[1]==n[2]?a:a+":"+e[n[0]][0][3][n[2]]):"SH33TJSERR2"}}(e,t,r),r)}function Co(e,t,r,n,a){var s,i,o=a&&a.biff||8,l={s:{c:0,r:0},e:{c:0,r:0}},c=[],f=0,h=0,u="";if(!e[0]||!e[0][0])return"";for(var d=-1,p="",m=0,g=e[0].length;m<g;++m){switch((A=e[0][m])[0]){case"PtgUminus":c.push("-"+c.pop());break;case"PtgUplus":c.push("+"+c.pop());break;case"PtgPercent":c.push(c.pop()+"%");break;case"PtgAdd":case"PtgConcat":case"PtgDiv":case"PtgEq":case"PtgGe":case"PtgGt":case"PtgLe":case"PtgLt":case"PtgMul":case"PtgNe":case"PtgPower":case"PtgSub":if(x=c.pop(),s=c.pop(),0<=d){switch(e[0][d][1][0]){case 0:p=R(" ",e[0][d][1][1]);break;case 1:p=R("\r",e[0][d][1][1]);break;default:if(p="",a.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][d][1][0])}s+=p,d=-1}c.push(s+_o[A[0]]+x);break;case"PtgIsect":x=c.pop(),s=c.pop(),c.push(s+" "+x);break;case"PtgUnion":x=c.pop(),s=c.pop(),c.push(s+","+x);break;case"PtgRange":x=c.pop(),s=c.pop(),c.push(s+":"+x);break;case"PtgAttrChoose":case"PtgAttrGoto":case"PtgAttrIf":case"PtgAttrIfError":break;case"PtgRef":b=Pt(A[1][1],l,a),c.push(Lt(b,o));break;case"PtgRefN":b=r?Pt(A[1][1],r,a):A[1][1],c.push(Lt(b,o));break;case"PtgRef3d":var f=A[1][1],b=Pt(A[1][2],l,a);u=yo(n,f,a);c.push(u+"!"+Lt(b,o));break;case"PtgFunc":case"PtgFuncVar":var v=A[1][0],E=A[1][1],v=v||0,w=0==(v&=127)?[]:c.slice(-v);c.length-=v,"User"===E&&(E=w.shift()),c.push(E+"("+w.join(",")+")");break;case"PtgBool":c.push(A[1]?"TRUE":"FALSE");break;case"PtgInt":c.push(A[1]);break;case"PtgNum":c.push(String(A[1]));break;case"PtgStr":c.push('"'+A[1]+'"');break;case"PtgErr":c.push(A[1]);break;case"PtgAreaN":i=Nt(A[1][1],r?{s:r}:l,a),c.push(Mt(i,a));break;case"PtgArea":i=Nt(A[1][1],l,a),c.push(Mt(i,a));break;case"PtgArea3d":f=A[1][1],i=A[1][2],u=yo(n,f,a),c.push(u+"!"+Mt(i,a));break;case"PtgAttrSum":c.push("SUM("+c.pop()+")");break;case"PtgAttrBaxcel":case"PtgAttrSemi":break;case"PtgName":var h=A[1][2],S=(n.names||[])[h-1]||(n[0]||[])[h],S=S?S.Name:"SH33TJSNAME"+String(h);S in Do&&(S=Do[S]),c.push(S);break;case"PtgNameX":var _,y=A[1][1];if(h=A[1][2],!(a.biff<=5)){S="";14849==((n[y]||[])[0]||[])[0]||(1025==((n[y]||[])[0]||[])[0]?n[y][h]&&0<n[y][h].itab&&(S=n.SheetNames[n[y][h].itab-1]+"!"):S=n.SheetNames[h-1]+"!"),n[y]&&n[y][h]?S+=n[y][h].Name:n[0]&&n[0][h]?S+=n[0][h].Name:S+="SH33TJSERRX",c.push(S);break}n[y=y<0?-y:y]&&(_=n[y][h]),c.push((_=_||{Name:"SH33TJSERRY"}).Name);break;case"PtgParen":var C="(",B=")";if(0<=d){switch(p="",e[0][d][1][0]){case 2:C=R(" ",e[0][d][1][1])+C;break;case 3:C=R("\r",e[0][d][1][1])+C;break;case 4:B=R(" ",e[0][d][1][1])+B;break;case 5:B=R("\r",e[0][d][1][1])+B;break;default:if(a.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][d][1][0])}d=-1}c.push(C+c.pop()+B);break;case"PtgRefErr":case"PtgRefErr3d":c.push("#REF!");break;case"PtgExp":b={c:A[1][1],r:A[1][0]};var T={c:r.c,r:r.r};if(n.sharedf[jt(b)]){y=n.sharedf[jt(b)];c.push(Co(y,0,T,n,a))}else{for(var k=!1,x=0;x!=n.arrayf.length;++x)if(s=n.arrayf[x],!(b.c<s[0].s.c||b.c>s[0].e.c||b.r<s[0].s.r||b.r>s[0].e.r)){c.push(Co(s[1],0,T,n,a)),k=!0;break}k||c.push(A[1])}break;case"PtgArray":c.push("{"+function(e){for(var t=[],r=0;r<e.length;++r){for(var n=e[r],a=[],s=0;s<n.length;++s){var i=n[s];i?2===i[0]?a.push('"'+i[1].replace(/"/g,'""')+'"'):a.push(i[1]):a.push("")}t.push(a.join(","))}return t.join(";")}(A[1])+"}");break;case"PtgMemArea":break;case"PtgAttrSpace":case"PtgAttrSpaceSemi":d=m;break;case"PtgTbl":case"PtgMemErr":break;case"PtgMissArg":c.push("");break;case"PtgAreaErr":case"PtgAreaErr3d":c.push("#REF!");break;case"PtgList":c.push("Table"+A[1].idx+"[#"+A[1].rt+"]");break;case"PtgMemAreaN":case"PtgMemNoMemN":case"PtgAttrNoop":case"PtgSheet":case"PtgEndSheet":case"PtgMemFunc":case"PtgMemNoMem":break;case"PtgElfCol":case"PtgElfColS":case"PtgElfColSV":case"PtgElfColV":case"PtgElfLel":case"PtgElfRadical":case"PtgElfRadicalLel":case"PtgElfRadicalS":case"PtgElfRw":case"PtgElfRwV":throw new Error("Unsupported ELFs");default:throw new Error("Unrecognized Formula Token: "+String(A))}if(3!=a.biff&&0<=d&&-1==["PtgAttrSpace","PtgAttrSpaceSemi","PtgAttrGoto"].indexOf(e[0][m][0])){var A,I=!0;switch((A=e[0][d])[1][0]){case 4:I=!1;case 0:p=R(" ",A[1][1]);break;case 5:I=!1;case 1:p=R("\r",A[1][1]);break;default:if(p="",a.WTF)throw new Error("Unexpected PtgAttrSpaceType "+A[1][0])}c.push((I?p:"")+c.pop()+(I?"":p)),d=-1}}if(1<c.length&&a.WTF)throw new Error("bad formula stack");return c[0]}function Bo(e,t,r){var n=e.l+t,a=Xn(e);2==r.biff&&++e.l;var s=function(e){var t;if(65535!==vt(e,e.l+6))return[Er(e),"n"];switch(e[e.l]){case 0:return e.l+=8,["String","s"];case 1:return t=1===e[e.l+2],e.l+=8,[t,"b"];case 2:return t=e[e.l+2],e.l+=8,[t,"e"];case 3:return e.l+=8,["","s"]}return[]}(e),t=e.read_shift(1);2!=r.biff&&(e.read_shift(1),5<=r.biff&&e.read_shift(4));r=function(e,t,r){var n,a=e.l+t,s=2==r.biff?1:2,i=e.read_shift(s);if(65535==i)return[[],It(e,t-2)];var o=So(e,i,r);return t!==i+s&&(n=wo(e,t-i-s,o,r)),e.l=a,[o,n]}(e,n-e.l,r);return{cell:a,val:s[0],formula:r,shared:t>>3&1,tt:s[1]}}function To(e,t,r){var n=e.read_shift(4),a=So(e,n,r),n=e.read_shift(4);return[a,0<n?wo(e,n,a,r):null]}var ko=To,xo=To,Ao=To,Io=To,Ro={0:"BEEP",1:"OPEN",2:"OPEN.LINKS",3:"CLOSE.ALL",4:"SAVE",5:"SAVE.AS",6:"FILE.DELETE",7:"PAGE.SETUP",8:"PRINT",9:"PRINTER.SETUP",10:"QUIT",11:"NEW.WINDOW",12:"ARRANGE.ALL",13:"WINDOW.SIZE",14:"WINDOW.MOVE",15:"FULL",16:"CLOSE",17:"RUN",22:"SET.PRINT.AREA",23:"SET.PRINT.TITLES",24:"SET.PAGE.BREAK",25:"REMOVE.PAGE.BREAK",26:"FONT",27:"DISPLAY",28:"PROTECT.DOCUMENT",29:"PRECISION",30:"A1.R1C1",31:"CALCULATE.NOW",32:"CALCULATION",34:"DATA.FIND",35:"EXTRACT",36:"DATA.DELETE",37:"SET.DATABASE",38:"SET.CRITERIA",39:"SORT",40:"DATA.SERIES",41:"TABLE",42:"FORMAT.NUMBER",43:"ALIGNMENT",44:"STYLE",45:"BORDER",46:"CELL.PROTECTION",47:"COLUMN.WIDTH",48:"UNDO",49:"CUT",50:"COPY",51:"PASTE",52:"CLEAR",53:"PASTE.SPECIAL",54:"EDIT.DELETE",55:"INSERT",56:"FILL.RIGHT",57:"FILL.DOWN",61:"DEFINE.NAME",62:"CREATE.NAMES",63:"FORMULA.GOTO",64:"FORMULA.FIND",65:"SELECT.LAST.CELL",66:"SHOW.ACTIVE.CELL",67:"GALLERY.AREA",68:"GALLERY.BAR",69:"GALLERY.COLUMN",70:"GALLERY.LINE",71:"GALLERY.PIE",72:"GALLERY.SCATTER",73:"COMBINATION",74:"PREFERRED",75:"ADD.OVERLAY",76:"GRIDLINES",77:"SET.PREFERRED",78:"AXES",79:"LEGEND",80:"ATTACH.TEXT",81:"ADD.ARROW",82:"SELECT.CHART",83:"SELECT.PLOT.AREA",84:"PATTERNS",85:"MAIN.CHART",86:"OVERLAY",87:"SCALE",88:"FORMAT.LEGEND",89:"FORMAT.TEXT",90:"EDIT.REPEAT",91:"PARSE",92:"JUSTIFY",93:"HIDE",94:"UNHIDE",95:"WORKSPACE",96:"FORMULA",97:"FORMULA.FILL",98:"FORMULA.ARRAY",99:"DATA.FIND.NEXT",100:"DATA.FIND.PREV",101:"FORMULA.FIND.NEXT",102:"FORMULA.FIND.PREV",103:"ACTIVATE",104:"ACTIVATE.NEXT",105:"ACTIVATE.PREV",106:"UNLOCKED.NEXT",107:"UNLOCKED.PREV",108:"COPY.PICTURE",109:"SELECT",110:"DELETE.NAME",111:"DELETE.FORMAT",112:"VLINE",113:"HLINE",114:"VPAGE",115:"HPAGE",116:"VSCROLL",117:"HSCROLL",118:"ALERT",119:"NEW",120:"CANCEL.COPY",121:"SHOW.CLIPBOARD",122:"MESSAGE",124:"PASTE.LINK",125:"APP.ACTIVATE",126:"DELETE.ARROW",127:"ROW.HEIGHT",128:"FORMAT.MOVE",129:"FORMAT.SIZE",130:"FORMULA.REPLACE",131:"SEND.KEYS",132:"SELECT.SPECIAL",133:"APPLY.NAMES",134:"REPLACE.FONT",135:"FREEZE.PANES",136:"SHOW.INFO",137:"SPLIT",138:"ON.WINDOW",139:"ON.DATA",140:"DISABLE.INPUT",142:"OUTLINE",143:"LIST.NAMES",144:"FILE.CLOSE",145:"SAVE.WORKBOOK",146:"DATA.FORM",147:"COPY.CHART",148:"ON.TIME",149:"WAIT",150:"FORMAT.FONT",151:"FILL.UP",152:"FILL.LEFT",153:"DELETE.OVERLAY",155:"SHORT.MENUS",159:"SET.UPDATE.STATUS",161:"COLOR.PALETTE",162:"DELETE.STYLE",163:"WINDOW.RESTORE",164:"WINDOW.MAXIMIZE",166:"CHANGE.LINK",167:"CALCULATE.DOCUMENT",168:"ON.KEY",169:"APP.RESTORE",170:"APP.MOVE",171:"APP.SIZE",172:"APP.MINIMIZE",173:"APP.MAXIMIZE",174:"BRING.TO.FRONT",175:"SEND.TO.BACK",185:"MAIN.CHART.TYPE",186:"OVERLAY.CHART.TYPE",187:"SELECT.END",188:"OPEN.MAIL",189:"SEND.MAIL",190:"STANDARD.FONT",191:"CONSOLIDATE",192:"SORT.SPECIAL",193:"GALLERY.3D.AREA",194:"GALLERY.3D.COLUMN",195:"GALLERY.3D.LINE",196:"GALLERY.3D.PIE",197:"VIEW.3D",198:"GOAL.SEEK",199:"WORKGROUP",200:"FILL.GROUP",201:"UPDATE.LINK",202:"PROMOTE",203:"DEMOTE",204:"SHOW.DETAIL",206:"UNGROUP",207:"OBJECT.PROPERTIES",208:"SAVE.NEW.OBJECT",209:"SHARE",210:"SHARE.NAME",211:"DUPLICATE",212:"APPLY.STYLE",213:"ASSIGN.TO.OBJECT",214:"OBJECT.PROTECTION",215:"HIDE.OBJECT",216:"SET.EXTRACT",217:"CREATE.PUBLISHER",218:"SUBSCRIBE.TO",219:"ATTRIBUTES",220:"SHOW.TOOLBAR",222:"PRINT.PREVIEW",223:"EDIT.COLOR",224:"SHOW.LEVELS",225:"FORMAT.MAIN",226:"FORMAT.OVERLAY",227:"ON.RECALC",228:"EDIT.SERIES",229:"DEFINE.STYLE",240:"LINE.PRINT",243:"ENTER.DATA",249:"GALLERY.RADAR",250:"MERGE.STYLES",251:"EDITION.OPTIONS",252:"PASTE.PICTURE",253:"PASTE.PICTURE.LINK",254:"SPELLING",256:"ZOOM",259:"INSERT.OBJECT",260:"WINDOW.MINIMIZE",265:"SOUND.NOTE",266:"SOUND.PLAY",267:"FORMAT.SHAPE",268:"EXTEND.POLYGON",269:"FORMAT.AUTO",272:"GALLERY.3D.BAR",273:"GALLERY.3D.SURFACE",274:"FILL.AUTO",276:"CUSTOMIZE.TOOLBAR",277:"ADD.TOOL",278:"EDIT.OBJECT",279:"ON.DOUBLECLICK",280:"ON.ENTRY",281:"WORKBOOK.ADD",282:"WORKBOOK.MOVE",283:"WORKBOOK.COPY",284:"WORKBOOK.OPTIONS",285:"SAVE.WORKSPACE",288:"CHART.WIZARD",289:"DELETE.TOOL",290:"MOVE.TOOL",291:"WORKBOOK.SELECT",292:"WORKBOOK.ACTIVATE",293:"ASSIGN.TO.TOOL",295:"COPY.TOOL",296:"RESET.TOOL",297:"CONSTRAIN.NUMERIC",298:"PASTE.TOOL",302:"WORKBOOK.NEW",305:"SCENARIO.CELLS",306:"SCENARIO.DELETE",307:"SCENARIO.ADD",308:"SCENARIO.EDIT",309:"SCENARIO.SHOW",310:"SCENARIO.SHOW.NEXT",311:"SCENARIO.SUMMARY",312:"PIVOT.TABLE.WIZARD",313:"PIVOT.FIELD.PROPERTIES",314:"PIVOT.FIELD",315:"PIVOT.ITEM",316:"PIVOT.ADD.FIELDS",318:"OPTIONS.CALCULATION",319:"OPTIONS.EDIT",320:"OPTIONS.VIEW",321:"ADDIN.MANAGER",322:"MENU.EDITOR",323:"ATTACH.TOOLBARS",324:"VBAActivate",325:"OPTIONS.CHART",328:"VBA.INSERT.FILE",330:"VBA.PROCEDURE.DEFINITION",336:"ROUTING.SLIP",338:"ROUTE.DOCUMENT",339:"MAIL.LOGON",342:"INSERT.PICTURE",343:"EDIT.TOOL",344:"GALLERY.DOUGHNUT",350:"CHART.TREND",352:"PIVOT.ITEM.PROPERTIES",354:"WORKBOOK.INSERT",355:"OPTIONS.TRANSITION",356:"OPTIONS.GENERAL",370:"FILTER.ADVANCED",373:"MAIL.ADD.MAILER",374:"MAIL.DELETE.MAILER",375:"MAIL.REPLY",376:"MAIL.REPLY.ALL",377:"MAIL.FORWARD",378:"MAIL.NEXT.LETTER",379:"DATA.LABEL",380:"INSERT.TITLE",381:"FONT.PROPERTIES",382:"MACRO.OPTIONS",383:"WORKBOOK.HIDE",384:"WORKBOOK.UNHIDE",385:"WORKBOOK.DELETE",386:"WORKBOOK.NAME",388:"GALLERY.CUSTOM",390:"ADD.CHART.AUTOFORMAT",391:"DELETE.CHART.AUTOFORMAT",392:"CHART.ADD.DATA",393:"AUTO.OUTLINE",394:"TAB.ORDER",395:"SHOW.DIALOG",396:"SELECT.ALL",397:"UNGROUP.SHEETS",398:"SUBTOTAL.CREATE",399:"SUBTOTAL.REMOVE",400:"RENAME.OBJECT",412:"WORKBOOK.SCROLL",413:"WORKBOOK.NEXT",414:"WORKBOOK.PREV",415:"WORKBOOK.TAB.SPLIT",416:"FULL.SCREEN",417:"WORKBOOK.PROTECT",420:"SCROLLBAR.PROPERTIES",421:"PIVOT.SHOW.PAGES",422:"TEXT.TO.COLUMNS",423:"FORMAT.CHARTTYPE",424:"LINK.FORMAT",425:"TRACER.DISPLAY",430:"TRACER.NAVIGATE",431:"TRACER.CLEAR",432:"TRACER.ERROR",433:"PIVOT.FIELD.GROUP",434:"PIVOT.FIELD.UNGROUP",435:"CHECKBOX.PROPERTIES",436:"LABEL.PROPERTIES",437:"LISTBOX.PROPERTIES",438:"EDITBOX.PROPERTIES",439:"PIVOT.REFRESH",440:"LINK.COMBO",441:"OPEN.TEXT",442:"HIDE.DIALOG",443:"SET.DIALOG.FOCUS",444:"ENABLE.OBJECT",445:"PUSHBUTTON.PROPERTIES",446:"SET.DIALOG.DEFAULT",447:"FILTER",448:"FILTER.SHOW.ALL",449:"CLEAR.OUTLINE",450:"FUNCTION.WIZARD",451:"ADD.LIST.ITEM",452:"SET.LIST.ITEM",453:"REMOVE.LIST.ITEM",454:"SELECT.LIST.ITEM",455:"SET.CONTROL.VALUE",456:"SAVE.COPY.AS",458:"OPTIONS.LISTS.ADD",459:"OPTIONS.LISTS.DELETE",460:"SERIES.AXES",461:"SERIES.X",462:"SERIES.Y",463:"ERRORBAR.X",464:"ERRORBAR.Y",465:"FORMAT.CHART",466:"SERIES.ORDER",467:"MAIL.LOGOFF",468:"CLEAR.ROUTING.SLIP",469:"APP.ACTIVATE.MICROSOFT",470:"MAIL.EDIT.MAILER",471:"ON.SHEET",472:"STANDARD.WIDTH",473:"SCENARIO.MERGE",474:"SUMMARY.INFO",475:"FIND.FILE",476:"ACTIVE.CELL.FONT",477:"ENABLE.TIPWIZARD",478:"VBA.MAKE.ADDIN",480:"INSERTDATATABLE",481:"WORKGROUP.OPTIONS",482:"MAIL.SEND.MAILER",485:"AUTOCORRECT",489:"POST.DOCUMENT",491:"PICKLIST",493:"VIEW.SHOW",494:"VIEW.DEFINE",495:"VIEW.DELETE",509:"SHEET.BACKGROUND",510:"INSERT.MAP.OBJECT",511:"OPTIONS.MENONO",517:"MSOCHECKS",518:"NORMAL",519:"LAYOUT",520:"RM.PRINT.AREA",521:"CLEAR.PRINT.AREA",522:"ADD.PRINT.AREA",523:"MOVE.BRK",545:"HIDECURR.NOTE",546:"HIDEALL.NOTES",547:"DELETE.NOTE",548:"TRAVERSE.NOTES",549:"ACTIVATE.NOTES",620:"PROTECT.REVISIONS",621:"UNPROTECT.REVISIONS",647:"OPTIONS.ME",653:"WEB.PUBLISH",667:"NEWWEBQUERY",673:"PIVOT.TABLE.CHART",753:"OPTIONS.SAVE",755:"OPTIONS.SPELL",808:"HIDEALL.INKANNOTS"},Oo={0:"COUNT",1:"IF",2:"ISNA",3:"ISERROR",4:"SUM",5:"AVERAGE",6:"MIN",7:"MAX",8:"ROW",9:"COLUMN",10:"NA",11:"NPV",12:"STDEV",13:"DOLLAR",14:"FIXED",15:"SIN",16:"COS",17:"TAN",18:"ATAN",19:"PI",20:"SQRT",21:"EXP",22:"LN",23:"LOG10",24:"ABS",25:"INT",26:"SIGN",27:"ROUND",28:"LOOKUP",29:"INDEX",30:"REPT",31:"MID",32:"LEN",33:"VALUE",34:"TRUE",35:"FALSE",36:"AND",37:"OR",38:"NOT",39:"MOD",40:"DCOUNT",41:"DSUM",42:"DAVERAGE",43:"DMIN",44:"DMAX",45:"DSTDEV",46:"VAR",47:"DVAR",48:"TEXT",49:"LINEST",50:"TREND",51:"LOGEST",52:"GROWTH",53:"GOTO",54:"HALT",55:"RETURN",56:"PV",57:"FV",58:"NPER",59:"PMT",60:"RATE",61:"MIRR",62:"IRR",63:"RAND",64:"MATCH",65:"DATE",66:"TIME",67:"DAY",68:"MONTH",69:"YEAR",70:"WEEKDAY",71:"HOUR",72:"MINUTE",73:"SECOND",74:"NOW",75:"AREAS",76:"ROWS",77:"COLUMNS",78:"OFFSET",79:"ABSREF",80:"RELREF",81:"ARGUMENT",82:"SEARCH",83:"TRANSPOSE",84:"ERROR",85:"STEP",86:"TYPE",87:"ECHO",88:"SET.NAME",89:"CALLER",90:"DEREF",91:"WINDOWS",92:"SERIES",93:"DOCUMENTS",94:"ACTIVE.CELL",95:"SELECTION",96:"RESULT",97:"ATAN2",98:"ASIN",99:"ACOS",100:"CHOOSE",101:"HLOOKUP",102:"VLOOKUP",103:"LINKS",104:"INPUT",105:"ISREF",106:"GET.FORMULA",107:"GET.NAME",108:"SET.VALUE",109:"LOG",110:"EXEC",111:"CHAR",112:"LOWER",113:"UPPER",114:"PROPER",115:"LEFT",116:"RIGHT",117:"EXACT",118:"TRIM",119:"REPLACE",120:"SUBSTITUTE",121:"CODE",122:"NAMES",123:"DIRECTORY",124:"FIND",125:"CELL",126:"ISERR",127:"ISTEXT",128:"ISNUMBER",129:"ISBLANK",130:"T",131:"N",132:"FOPEN",133:"FCLOSE",134:"FSIZE",135:"FREADLN",136:"FREAD",137:"FWRITELN",138:"FWRITE",139:"FPOS",140:"DATEVALUE",141:"TIMEVALUE",142:"SLN",143:"SYD",144:"DDB",145:"GET.DEF",146:"REFTEXT",147:"TEXTREF",148:"INDIRECT",149:"REGISTER",150:"CALL",151:"ADD.BAR",152:"ADD.MENU",153:"ADD.COMMAND",154:"ENABLE.COMMAND",155:"CHECK.COMMAND",156:"RENAME.COMMAND",157:"SHOW.BAR",158:"DELETE.MENU",159:"DELETE.COMMAND",160:"GET.CHART.ITEM",161:"DIALOG.BOX",162:"CLEAN",163:"MDETERM",164:"MINVERSE",165:"MMULT",166:"FILES",167:"IPMT",168:"PPMT",169:"COUNTA",170:"CANCEL.KEY",171:"FOR",172:"WHILE",173:"BREAK",174:"NEXT",175:"INITIATE",176:"REQUEST",177:"POKE",178:"EXECUTE",179:"TERMINATE",180:"RESTART",181:"HELP",182:"GET.BAR",183:"PRODUCT",184:"FACT",185:"GET.CELL",186:"GET.WORKSPACE",187:"GET.WINDOW",188:"GET.DOCUMENT",189:"DPRODUCT",190:"ISNONTEXT",191:"GET.NOTE",192:"NOTE",193:"STDEVP",194:"VARP",195:"DSTDEVP",196:"DVARP",197:"TRUNC",198:"ISLOGICAL",199:"DCOUNTA",200:"DELETE.BAR",201:"UNREGISTER",204:"USDOLLAR",205:"FINDB",206:"SEARCHB",207:"REPLACEB",208:"LEFTB",209:"RIGHTB",210:"MIDB",211:"LENB",212:"ROUNDUP",213:"ROUNDDOWN",214:"ASC",215:"DBCS",216:"RANK",219:"ADDRESS",220:"DAYS360",221:"TODAY",222:"VDB",223:"ELSE",224:"ELSE.IF",225:"END.IF",226:"FOR.CELL",227:"MEDIAN",228:"SUMPRODUCT",229:"SINH",230:"COSH",231:"TANH",232:"ASINH",233:"ACOSH",234:"ATANH",235:"DGET",236:"CREATE.OBJECT",237:"VOLATILE",238:"LAST.ERROR",239:"CUSTOM.UNDO",240:"CUSTOM.REPEAT",241:"FORMULA.CONVERT",242:"GET.LINK.INFO",243:"TEXT.BOX",244:"INFO",245:"GROUP",246:"GET.OBJECT",247:"DB",248:"PAUSE",251:"RESUME",252:"FREQUENCY",253:"ADD.TOOLBAR",254:"DELETE.TOOLBAR",255:"User",256:"RESET.TOOLBAR",257:"EVALUATE",258:"GET.TOOLBAR",259:"GET.TOOL",260:"SPELLING.CHECK",261:"ERROR.TYPE",262:"APP.TITLE",263:"WINDOW.TITLE",264:"SAVE.TOOLBAR",265:"ENABLE.TOOL",266:"PRESS.TOOL",267:"REGISTER.ID",268:"GET.WORKBOOK",269:"AVEDEV",270:"BETADIST",271:"GAMMALN",272:"BETAINV",273:"BINOMDIST",274:"CHIDIST",275:"CHIINV",276:"COMBIN",277:"CONFIDENCE",278:"CRITBINOM",279:"EVEN",280:"EXPONDIST",281:"FDIST",282:"FINV",283:"FISHER",284:"FISHERINV",285:"FLOOR",286:"GAMMADIST",287:"GAMMAINV",288:"CEILING",289:"HYPGEOMDIST",290:"LOGNORMDIST",291:"LOGINV",292:"NEGBINOMDIST",293:"NORMDIST",294:"NORMSDIST",295:"NORMINV",296:"NORMSINV",297:"STANDARDIZE",298:"ODD",299:"PERMUT",300:"POISSON",301:"TDIST",302:"WEIBULL",303:"SUMXMY2",304:"SUMX2MY2",305:"SUMX2PY2",306:"CHITEST",307:"CORREL",308:"COVAR",309:"FORECAST",310:"FTEST",311:"INTERCEPT",312:"PEARSON",313:"RSQ",314:"STEYX",315:"SLOPE",316:"TTEST",317:"PROB",318:"DEVSQ",319:"GEOMEAN",320:"HARMEAN",321:"SUMSQ",322:"KURT",323:"SKEW",324:"ZTEST",325:"LARGE",326:"SMALL",327:"QUARTILE",328:"PERCENTILE",329:"PERCENTRANK",330:"MODE",331:"TRIMMEAN",332:"TINV",334:"MOVIE.COMMAND",335:"GET.MOVIE",336:"CONCATENATE",337:"POWER",338:"PIVOT.ADD.DATA",339:"GET.PIVOT.TABLE",340:"GET.PIVOT.FIELD",341:"GET.PIVOT.ITEM",342:"RADIANS",343:"DEGREES",344:"SUBTOTAL",345:"SUMIF",346:"COUNTIF",347:"COUNTBLANK",348:"SCENARIO.GET",349:"OPTIONS.LISTS.GET",350:"ISPMT",351:"DATEDIF",352:"DATESTRING",353:"NUMBERSTRING",354:"ROMAN",355:"OPEN.DIALOG",356:"SAVE.DIALOG",357:"VIEW.GET",358:"GETPIVOTDATA",359:"HYPERLINK",360:"PHONETIC",361:"AVERAGEA",362:"MAXA",363:"MINA",364:"STDEVPA",365:"VARPA",366:"STDEVA",367:"VARA",368:"BAHTTEXT",369:"THAIDAYOFWEEK",370:"THAIDIGIT",371:"THAIMONTHOFYEAR",372:"THAINUMSOUND",373:"THAINUMSTRING",374:"THAISTRINGLENGTH",375:"ISTHAIDIGIT",376:"ROUNDBAHTDOWN",377:"ROUNDBAHTUP",378:"THAIYEAR",379:"RTD",380:"CUBEVALUE",381:"CUBEMEMBER",382:"CUBEMEMBERPROPERTY",383:"CUBERANKEDMEMBER",384:"HEX2BIN",385:"HEX2DEC",386:"HEX2OCT",387:"DEC2BIN",388:"DEC2HEX",389:"DEC2OCT",390:"OCT2BIN",391:"OCT2HEX",392:"OCT2DEC",393:"BIN2DEC",394:"BIN2OCT",395:"BIN2HEX",396:"IMSUB",397:"IMDIV",398:"IMPOWER",399:"IMABS",400:"IMSQRT",401:"IMLN",402:"IMLOG2",403:"IMLOG10",404:"IMSIN",405:"IMCOS",406:"IMEXP",407:"IMARGUMENT",408:"IMCONJUGATE",409:"IMAGINARY",410:"IMREAL",411:"COMPLEX",412:"IMSUM",413:"IMPRODUCT",414:"SERIESSUM",415:"FACTDOUBLE",416:"SQRTPI",417:"QUOTIENT",418:"DELTA",419:"GESTEP",420:"ISEVEN",421:"ISODD",422:"MROUND",423:"ERF",424:"ERFC",425:"BESSELJ",426:"BESSELK",427:"BESSELY",428:"BESSELI",429:"XIRR",430:"XNPV",431:"PRICEMAT",432:"YIELDMAT",433:"INTRATE",434:"RECEIVED",435:"DISC",436:"PRICEDISC",437:"YIELDDISC",438:"TBILLEQ",439:"TBILLPRICE",440:"TBILLYIELD",441:"PRICE",442:"YIELD",443:"DOLLARDE",444:"DOLLARFR",445:"NOMINAL",446:"EFFECT",447:"CUMPRINC",448:"CUMIPMT",449:"EDATE",450:"EOMONTH",451:"YEARFRAC",452:"COUPDAYBS",453:"COUPDAYS",454:"COUPDAYSNC",455:"COUPNCD",456:"COUPNUM",457:"COUPPCD",458:"DURATION",459:"MDURATION",460:"ODDLPRICE",461:"ODDLYIELD",462:"ODDFPRICE",463:"ODDFYIELD",464:"RANDBETWEEN",465:"WEEKNUM",466:"AMORDEGRC",467:"AMORLINC",468:"CONVERT",724:"SHEETJS",469:"ACCRINT",470:"ACCRINTM",471:"WORKDAY",472:"NETWORKDAYS",473:"GCD",474:"MULTINOMIAL",475:"LCM",476:"FVSCHEDULE",477:"CUBEKPIMEMBER",478:"CUBESET",479:"CUBESETCOUNT",480:"IFERROR",481:"COUNTIFS",482:"SUMIFS",483:"AVERAGEIF",484:"AVERAGEIFS"},Fo={2:1,3:1,10:0,15:1,16:1,17:1,18:1,19:0,20:1,21:1,22:1,23:1,24:1,25:1,26:1,27:2,30:2,31:3,32:1,33:1,34:0,35:0,38:1,39:2,40:3,41:3,42:3,43:3,44:3,45:3,47:3,48:2,53:1,61:3,63:0,65:3,66:3,67:1,68:1,69:1,70:1,71:1,72:1,73:1,74:0,75:1,76:1,77:1,79:2,80:2,83:1,85:0,86:1,89:0,90:1,94:0,95:0,97:2,98:1,99:1,101:3,102:3,105:1,106:1,108:2,111:1,112:1,113:1,114:1,117:2,118:1,119:4,121:1,126:1,127:1,128:1,129:1,130:1,131:1,133:1,134:1,135:1,136:2,137:2,138:2,140:1,141:1,142:3,143:4,144:4,161:1,162:1,163:1,164:1,165:2,172:1,175:2,176:2,177:3,178:2,179:1,184:1,186:1,189:3,190:1,195:3,196:3,197:1,198:1,199:3,201:1,207:4,210:3,211:1,212:2,213:2,214:1,215:1,225:0,229:1,230:1,231:1,232:1,233:1,234:1,235:3,244:1,247:4,252:2,257:1,261:1,271:1,273:4,274:2,275:2,276:2,277:3,278:3,279:1,280:3,281:3,282:3,283:1,284:1,285:2,286:4,287:3,288:2,289:4,290:3,291:3,292:3,293:4,294:1,295:3,296:1,297:3,298:1,299:2,300:3,301:3,302:4,303:2,304:2,305:2,306:2,307:2,308:2,309:3,310:2,311:2,312:2,313:2,314:2,315:2,316:4,325:2,326:2,327:2,328:2,331:2,332:2,337:2,342:1,343:1,346:2,347:1,350:4,351:3,352:1,353:2,360:1,368:1,369:1,370:1,371:1,372:1,373:1,374:1,375:1,376:1,377:1,378:1,382:3,385:1,392:1,393:1,396:2,397:2,398:2,399:1,400:1,401:1,402:1,403:1,404:1,405:1,406:1,407:1,408:1,409:1,410:1,414:4,415:1,416:1,417:2,420:1,421:1,422:2,424:1,425:2,426:2,427:2,428:2,430:3,438:3,439:3,440:3,443:2,444:2,445:2,446:2,447:6,448:6,449:2,450:2,464:2,468:3,476:2,479:1,480:2,65535:0},Do={"_xlfn.ACOT":"ACOT","_xlfn.ACOTH":"ACOTH","_xlfn.AGGREGATE":"AGGREGATE","_xlfn.ARABIC":"ARABIC","_xlfn.AVERAGEIF":"AVERAGEIF","_xlfn.AVERAGEIFS":"AVERAGEIFS","_xlfn.BASE":"BASE","_xlfn.BETA.DIST":"BETA.DIST","_xlfn.BETA.INV":"BETA.INV","_xlfn.BINOM.DIST":"BINOM.DIST","_xlfn.BINOM.DIST.RANGE":"BINOM.DIST.RANGE","_xlfn.BINOM.INV":"BINOM.INV","_xlfn.BITAND":"BITAND","_xlfn.BITLSHIFT":"BITLSHIFT","_xlfn.BITOR":"BITOR","_xlfn.BITRSHIFT":"BITRSHIFT","_xlfn.BITXOR":"BITXOR","_xlfn.CEILING.MATH":"CEILING.MATH","_xlfn.CEILING.PRECISE":"CEILING.PRECISE","_xlfn.CHISQ.DIST":"CHISQ.DIST","_xlfn.CHISQ.DIST.RT":"CHISQ.DIST.RT","_xlfn.CHISQ.INV":"CHISQ.INV","_xlfn.CHISQ.INV.RT":"CHISQ.INV.RT","_xlfn.CHISQ.TEST":"CHISQ.TEST","_xlfn.COMBINA":"COMBINA","_xlfn.CONCAT":"CONCAT","_xlfn.CONFIDENCE.NORM":"CONFIDENCE.NORM","_xlfn.CONFIDENCE.T":"CONFIDENCE.T","_xlfn.COT":"COT","_xlfn.COTH":"COTH","_xlfn.COUNTIFS":"COUNTIFS","_xlfn.COVARIANCE.P":"COVARIANCE.P","_xlfn.COVARIANCE.S":"COVARIANCE.S","_xlfn.CSC":"CSC","_xlfn.CSCH":"CSCH","_xlfn.DAYS":"DAYS","_xlfn.DECIMAL":"DECIMAL","_xlfn.ECMA.CEILING":"ECMA.CEILING","_xlfn.ERF.PRECISE":"ERF.PRECISE","_xlfn.ERFC.PRECISE":"ERFC.PRECISE","_xlfn.EXPON.DIST":"EXPON.DIST","_xlfn.F.DIST":"F.DIST","_xlfn.F.DIST.RT":"F.DIST.RT","_xlfn.F.INV":"F.INV","_xlfn.F.INV.RT":"F.INV.RT","_xlfn.F.TEST":"F.TEST","_xlfn.FILTERXML":"FILTERXML","_xlfn.FLOOR.MATH":"FLOOR.MATH","_xlfn.FLOOR.PRECISE":"FLOOR.PRECISE","_xlfn.FORECAST.ETS":"FORECAST.ETS","_xlfn.FORECAST.ETS.CONFINT":"FORECAST.ETS.CONFINT","_xlfn.FORECAST.ETS.SEASONALITY":"FORECAST.ETS.SEASONALITY","_xlfn.FORECAST.ETS.STAT":"FORECAST.ETS.STAT","_xlfn.FORECAST.LINEAR":"FORECAST.LINEAR","_xlfn.FORMULATEXT":"FORMULATEXT","_xlfn.GAMMA":"GAMMA","_xlfn.GAMMA.DIST":"GAMMA.DIST","_xlfn.GAMMA.INV":"GAMMA.INV","_xlfn.GAMMALN.PRECISE":"GAMMALN.PRECISE","_xlfn.GAUSS":"GAUSS","_xlfn.HYPGEOM.DIST":"HYPGEOM.DIST","_xlfn.IFERROR":"IFERROR","_xlfn.IFNA":"IFNA","_xlfn.IFS":"IFS","_xlfn.IMCOSH":"IMCOSH","_xlfn.IMCOT":"IMCOT","_xlfn.IMCSC":"IMCSC","_xlfn.IMCSCH":"IMCSCH","_xlfn.IMSEC":"IMSEC","_xlfn.IMSECH":"IMSECH","_xlfn.IMSINH":"IMSINH","_xlfn.IMTAN":"IMTAN","_xlfn.ISFORMULA":"ISFORMULA","_xlfn.ISO.CEILING":"ISO.CEILING","_xlfn.ISOWEEKNUM":"ISOWEEKNUM","_xlfn.LOGNORM.DIST":"LOGNORM.DIST","_xlfn.LOGNORM.INV":"LOGNORM.INV","_xlfn.MAXIFS":"MAXIFS","_xlfn.MINIFS":"MINIFS","_xlfn.MODE.MULT":"MODE.MULT","_xlfn.MODE.SNGL":"MODE.SNGL","_xlfn.MUNIT":"MUNIT","_xlfn.NEGBINOM.DIST":"NEGBINOM.DIST","_xlfn.NETWORKDAYS.INTL":"NETWORKDAYS.INTL","_xlfn.NIGBINOM":"NIGBINOM","_xlfn.NORM.DIST":"NORM.DIST","_xlfn.NORM.INV":"NORM.INV","_xlfn.NORM.S.DIST":"NORM.S.DIST","_xlfn.NORM.S.INV":"NORM.S.INV","_xlfn.NUMBERVALUE":"NUMBERVALUE","_xlfn.PDURATION":"PDURATION","_xlfn.PERCENTILE.EXC":"PERCENTILE.EXC","_xlfn.PERCENTILE.INC":"PERCENTILE.INC","_xlfn.PERCENTRANK.EXC":"PERCENTRANK.EXC","_xlfn.PERCENTRANK.INC":"PERCENTRANK.INC","_xlfn.PERMUTATIONA":"PERMUTATIONA","_xlfn.PHI":"PHI","_xlfn.POISSON.DIST":"POISSON.DIST","_xlfn.QUARTILE.EXC":"QUARTILE.EXC","_xlfn.QUARTILE.INC":"QUARTILE.INC","_xlfn.QUERYSTRING":"QUERYSTRING","_xlfn.RANK.AVG":"RANK.AVG","_xlfn.RANK.EQ":"RANK.EQ","_xlfn.RRI":"RRI","_xlfn.SEC":"SEC","_xlfn.SECH":"SECH","_xlfn.SHEET":"SHEET","_xlfn.SHEETS":"SHEETS","_xlfn.SKEW.P":"SKEW.P","_xlfn.STDEV.P":"STDEV.P","_xlfn.STDEV.S":"STDEV.S","_xlfn.SUMIFS":"SUMIFS","_xlfn.SWITCH":"SWITCH","_xlfn.T.DIST":"T.DIST","_xlfn.T.DIST.2T":"T.DIST.2T","_xlfn.T.DIST.RT":"T.DIST.RT","_xlfn.T.INV":"T.INV","_xlfn.T.INV.2T":"T.INV.2T","_xlfn.T.TEST":"T.TEST","_xlfn.TEXTJOIN":"TEXTJOIN","_xlfn.UNICHAR":"UNICHAR","_xlfn.UNICODE":"UNICODE","_xlfn.VAR.P":"VAR.P","_xlfn.VAR.S":"VAR.S","_xlfn.WEBSERVICE":"WEBSERVICE","_xlfn.WEIBULL.DIST":"WEIBULL.DIST","_xlfn.WORKDAY.INTL":"WORKDAY.INTL","_xlfn.XOR":"XOR","_xlfn.Z.TEST":"Z.TEST"};function Po(e){return(e=(e=(e=(e=61==(e="of:"==e.slice(0,3)?e.slice(3):e).charCodeAt(0)&&61==(e=e.slice(1)).charCodeAt(0)?e.slice(1):e).replace(/COM\.MICROSOFT\./g,"")).replace(/\[((?:\.[A-Z]+[0-9]+)(?::\.[A-Z]+[0-9]+)?)\]/g,function(e,t){return t.replace(/\./g,"")})).replace(/\[.(#[A-Z]*[?!])\]/g,"$1")).replace(/[;~]/g,",").replace(/\|/g,";")}function No(e){e=e.split(":");return[e[0].split(".")[0],e[0].split(".")[1]+(1<e.length?":"+(e[1].split(".")[1]||e[1].split(".")[0]):"")]}var Lo={},Mo={};Wr.WS=["http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet","http://purl.oclc.org/ooxml/officeDocument/relationships/worksheet"];var Uo="undefined"!=typeof Map;function Ho(e,t,r){var n=0,a=e.length;if(r){if(Uo?r.has(t):r.hasOwnProperty(t))for(var s=Uo?r.get(t):r[t];n<s.length;++n)if(e[s[n]].t===t)return e.Count++,s[n]}else for(;n<a;++n)if(e[n].t===t)return e.Count++,n;return e[a]={t:t},e.Count++,e.Unique++,r&&(Uo?(r.has(t)||r.set(t,[]),r.get(t).push(a)):(r.hasOwnProperty(t)||(r[t]=[]),r[t].push(a))),a}function zo(e,t){var r={min:e+1,max:e+1},e=-1;return t.MDW&&(Xs=t.MDW),null!=t.width?r.customWidth=1:null!=t.wpx?e=Gs(t.wpx):null!=t.wch&&(e=t.wch),-1<e?(r.width=$s(e),r.customWidth=1):null!=t.width&&(r.width=t.width),t.hidden&&(r.hidden=!0),r}function Vo(e,t){e&&(t="xlml"==t?[1,1,1,1,.5,.5]:[.7,.7,.75,.75,.3,.3],null==e.left&&(e.left=t[0]),null==e.right&&(e.right=t[1]),null==e.top&&(e.top=t[2]),null==e.bottom&&(e.bottom=t[3]),null==e.header&&(e.header=t[4]),null==e.footer&&(e.footer=t[5]))}function Wo(e,t,r){if("undefined"!=typeof style_builder){if(/^\d+$/.exec(t.s))return t.s;if(t.s&&t.s==+t.s)return t.s;var n=t.s||{};return t.z&&(n.numFmt=t.z),style_builder.addStyle(n)}var a=r.revssf[null!=t.z?t.z:"General"],s=60,i=e.length;if(null==a&&r.ssf)for(;s<392;++s)if(null==r.ssf[s]){ue.load(t.z,s),r.ssf[s]=t.z,r.revssf[t.z]=a=s;break}for(s=0;s!=i;++s)if(e[s].numFmtId===a)return s;return e[i]={numFmtId:a,fontId:0,fillId:0,borderId:0,xfId:0,applyNumberFormat:1},i}function Xo(e,t,r,n,a,s){if("z"!==e.t){"d"===e.t&&"string"==typeof e.v&&(e.v=oe(e.v));try{n.cellNF&&(e.z=ue._table[t])}catch(e){if(n.WTF)throw e}if(!n||!1!==n.cellText)try{if(null==ue._table[t]&&ue.load(m[t]||"General",t),"e"===e.t)e.w=e.w||Sr[e.v];else if(0===t)if("n"===e.t)(0|e.v)===e.v?e.w=ue._general_int(e.v):e.w=ue._general_num(e.v);else if("d"===e.t){var i=G(e.v);e.w=(0|i)===i?ue._general_int(i):ue._general_num(i)}else{if(void 0===e.v)return;e.w=ue._general(e.v,Mo)}else"d"===e.t?e.w=ue.format(t,G(e.v),Mo):e.w=ue.format(t,e.v,Mo)}catch(e){if(n.WTF)throw e}if(n.cellStyles&&null!=r)try{e.s=s.Fills[r],e.s.fgColor&&e.s.fgColor.theme&&!e.s.fgColor.rgb&&(e.s.fgColor.rgb=Hs(a.themeElements.clrScheme[e.s.fgColor.theme].rgb,e.s.fgColor.tint||0),n.WTF&&(e.s.fgColor.raw_rgb=a.themeElements.clrScheme[e.s.fgColor.theme].rgb)),e.s.bgColor&&e.s.bgColor.theme&&(e.s.bgColor.rgb=Hs(a.themeElements.clrScheme[e.s.bgColor.theme].rgb,e.s.bgColor.tint||0),n.WTF&&(e.s.bgColor.raw_rgb=a.themeElements.clrScheme[e.s.bgColor.theme].rgb))}catch(e){if(n.WTF&&s.Fills)throw e}}}var jo=/<(?:\w:)?mergeCell ref="[A-Z0-9:]+"\s*[\/]?>/g,Go=/<(?:\w+:)?sheetData>([\s\S]*)<\/(?:\w+:)?sheetData>/,$o=/<(?:\w:)?hyperlink [^>]*>/gm,Yo=/"(\w*:\w*)"/,Ko=/<(?:\w:)?col\b[^>]*[\/]?>/g,Zo=/<(?:\w:)?autoFilter[^>]*([\/]|>([\s\S]*)<\/(?:\w:)?autoFilter)>/g,Qo=/<(?:\w:)?pageMargins[^>]*\/>/g,Jo=/<(?:\w:)?sheetPr\b(?:[^>a-z][^>]*)?\/>/,qo=/<(?:\w:)?sheetViews[^>]*(?:[\/]|>([\s\S]*)<\/(?:\w:)?sheetViews)>/;function el(e,t,r,n,a,s,i){if(!e)return e;null!=fe&&null==t.dense&&(t.dense=fe);var o=t.dense?[]:{},l={s:{r:2e6,c:2e6},e:{r:0,c:0}},c="",f="",h=e.match(Go);h?(c=e.slice(0,h.index),f=e.slice(h.index+h[0].length)):c=f=e;e=c.match(Jo);e&&tl(e[0],0,a,r);var u=(c.match(/<(?:\w*:)?dimension/)||{index:-1}).index;0<u&&((e=c.slice(u,u+50).match(Yo))&&(r=o,(p=Yt(p=e[1])).s.r<=p.e.r&&p.s.c<=p.e.c&&0<=p.s.r&&0<=p.s.c&&(r["!ref"]=$t(p))));var d,p=c.match(qo);p&&p[1]&&(g=p[1],d=a,(g.match(rl)||[]).forEach(function(e){Re(pe(e).rightToLeft)&&(d.Views||(d.Views=[{}]),d.Views[0]||(d.Views[0]={}),d.Views[0].RTL=!0)}));var m,g=[];t.cellStyles&&(m=c.match(Ko))&&function(e,t){for(var r=!1,n=0;n!=t.length;++n){var a=pe(t[n],!0);a.hidden&&(a.hidden=Re(a.hidden));var s=parseInt(a.min,10)-1,i=parseInt(a.max,10)-1;for(delete a.min,delete a.max,a.width=+a.width,!r&&a.width&&(r=!0,Ks(a.width)),Zs(a);s<=i;)e[s++]=le(a)}}(g,m),h&&fl(h[1],o,t,l,s,i);i=f.match(Zo);i&&(o["!autofilter"]={ref:(i[0].match(/ref="([^"]*)"/)||[])[1]});var b=[],v=f.match(jo);if(v)for(u=0;u!=v.length;++u)b[u]=Yt(v[u].slice(v[u].indexOf('"')+1));i=f.match($o);i&&function(e,t,r){for(var n=Array.isArray(e),a=0;a!=t.length;++a){var s=pe(Oe(t[a]),!0);if(!s.ref)return;var i=((r||{})["!id"]||[])[s.id];i?(s.Target=i.Target,s.location&&(s.Target+="#"+s.location)):(s.Target="#"+s.location,i={Target:s.Target,TargetMode:"Internal"}),s.Rel=i,s.tooltip&&(s.Tooltip=s.tooltip,delete s.tooltip);for(var o=Yt(s.ref),l=o.s.r;l<=o.e.r;++l)for(var c=o.s.c;c<=o.e.c;++c){var f=jt({c:c,r:l});n?(e[l]||(e[l]=[]),e[l][c]||(e[l][c]={t:"z",v:void 0}),e[l][c].l=s):(e[f]||(e[f]={t:"z",v:void 0}),e[f].l=s)}}}(o,i,n);var E,w,f=f.match(Qo);return f&&(o["!margins"]=(E=pe(f[0]),w={},["left","right","top","bottom","header","footer"].forEach(function(e){E[e]&&(w[e]=parseFloat(E[e]))}),w)),!o["!ref"]&&l.e.c>=l.s.c&&l.e.r>=l.s.r&&(o["!ref"]=$t(l)),0<t.sheetRows&&o["!ref"]&&(f=Yt(o["!ref"]),t.sheetRows<=+f.e.r&&(f.e.r=t.sheetRows-1,f.e.r>l.e.r&&(f.e.r=l.e.r),f.e.r<f.s.r&&(f.s.r=f.e.r),f.e.c>l.e.c&&(f.e.c=l.e.c),f.e.c<f.s.c&&(f.s.c=f.e.c),o["!fullref"]=o["!ref"],o["!ref"]=$t(f))),0<g.length&&(o["!cols"]=g),0<b.length&&(o["!merges"]=b),o}function tl(e,t,r,n){e=pe(e);r.Sheets[n]||(r.Sheets[n]={}),e.codeName&&(r.Sheets[n].CodeName=e.codeName)}var rl=/<(?:\w:)?sheetView(?:[^>a-z][^>]*)?\/>/;var nl,al,sl,il,ol,ll,cl,fl=(nl=/<(?:\w+:)?c[ >]/,al=/<\/(?:\w+:)?row>/,sl=/r=["']([^"']*)["']/,il=/<(?:\w+:)?is>([\S\s]*?)<\/(?:\w+:)?is>/,ol=/ref=["']([^"']*)["']/,ll=Le("v"),cl=Le("f"),function(e,t,r,n,a,s){for(var i,o,l,c,f,h,u=0,d="",p=[],m=[],g=0,b=0,v="",E=0,w=0,S=0,_=0,y=Array.isArray(s.CellXf),C=[],B=[],T=Array.isArray(t),k=[],x={},A=!1,I=e.split(al),R=0,O=I.length;R!=O;++R){var F=(d=I[R].trim()).length;if(0!==F){for(u=0;u<F&&62!==d.charCodeAt(u);++u);if(++u,E=null!=(l=pe(d.slice(0,u),!0)).r?parseInt(l.r,10):E+1,w=-1,!(r.sheetRows&&r.sheetRows<E))for(n.s.r>E-1&&(n.s.r=E-1),n.e.r<E-1&&(n.e.r=E-1),r&&r.cellStyles&&(A=!(x={}),l.ht&&(A=!0,x.hpt=parseFloat(l.ht),x.hpx=qs(x.hpt)),"1"==l.hidden&&(x.hidden=A=!0),null!=l.outlineLevel&&(A=!0,x.level=+l.outlineLevel),A&&(k[E-1]=x)),p=d.slice(u).split(nl),u=0;u!=p.length;++u)if(0!==(d=p[u].trim()).length){if(m=d.match(sl),g=u,d="<c "+("<"==d.slice(b=0,1)?">":"")+d,null!=m&&2===m.length){for(v=m[1],b=g=0;b!=v.length&&!((i=v.charCodeAt(b)-64)<1||26<i);++b)g=26*g+i;w=--g}else++w;for(b=0;b!=d.length&&62!==d.charCodeAt(b);++b);if(++b,(l=pe(d.slice(0,b),!0)).r||(l.r=jt({r:E-1,c:w})),o={t:""},null!=(m=(v=d.slice(b)).match(ll))&&""!==m[1]&&(o.v=we(m[1])),r.cellFormula){null!=(m=v.match(cl))&&""!==m[1]?(o.f=Ki(we(Oe(m[1]))),-1<m[0].indexOf('t="array"')?(o.F=(v.match(ol)||[])[1],-1<o.F.indexOf(":")&&C.push([Yt(o.F),o.F])):-1<m[0].indexOf('t="shared"')&&(f=pe(m[0]),B[parseInt(f.si,10)]=[f,Ki(we(Oe(m[1]))),l.r])):(m=v.match(/<f[^>]*\/>/))&&B[(f=pe(m[0])).si]&&(o.f=Yi(B[f.si][1],B[f.si][2],l.r));for(var D=Xt(l.r),b=0;b<C.length;++b)D.r>=C[b][0].s.r&&D.r<=C[b][0].e.r&&D.c>=C[b][0].s.c&&D.c<=C[b][0].e.c&&(o.F=C[b][1])}if(null==l.t&&void 0===o.v)if(o.f||o.F)o.v=0,o.t="n";else{if(!r.sheetStubs)continue;o.t="z"}else o.t=l.t||"n";switch(n.s.c>w&&(n.s.c=w),n.e.c<w&&(n.e.c=w),o.t){case"n":if(""==o.v||null==o.v){if(!r.sheetStubs)continue;o.t="z"}else o.v=parseFloat(o.v);break;case"s":if(void 0===o.v){if(!r.sheetStubs)continue;o.t="z"}else c=Lo[parseInt(o.v,10)],o.v=c.t,o.r=c.r,r.cellHTML&&(o.h=c.h);break;case"str":o.t="s",o.v=null!=o.v?Oe(o.v):"",r.cellHTML&&(o.h=Te(o.v));break;case"inlineStr":m=v.match(il),o.t="s",null!=m&&(c=us(m[1]))?o.v=c.t:o.v="";break;case"b":o.v=Re(o.v);break;case"d":r.cellDates?o.v=oe(o.v,1):(o.v=G(oe(o.v,1)),o.t="n");break;case"e":r&&!1===r.cellText||(o.w=o.v),o.v=_r[o.v]}var P,S=_=0;y&&void 0!==l.s&&null!=(h=s.CellXf[l.s])&&(null!=h.numFmtId&&(S=h.numFmtId),r.cellStyles&&null!=h.fillId&&(_=h.fillId)),Xo(o,S,_,r,a,s),r.cellDates&&y&&"n"==o.t&&ue.is_date(ue._table[S])&&(o.t="d",o.v=N(o.v)),T?(t[(P=Xt(l.r)).r]||(t[P.r]=[]),t[P.r][P.c]=o):t[l.r]=o}}}0<k.length&&(t["!rows"]=k)});function hl(e,t){for(var r,n,a,s=[],i=[],o=Yt(e["!ref"]),l=[],c=0,f=0,h=e["!rows"],u=Array.isArray(e),d={r:""},p=-1,f=o.s.c;f<=o.e.c;++f)l[f]=Vt(f);for(c=o.s.r;c<=o.e.r;++c){for(i=[],n=Ht(c),f=o.s.c;f<=o.e.c;++f){var m=l[f]+n,g=u?(e[c]||[])[f]:e[m];void 0!==g&&null!=(r=function(e,t,r,n){if(void 0===e.v&&void 0===e.f||"z"===e.t)return"";var a="",s=e.t,i=e.v;switch(e.t){case"b":a=e.v?"1":"0";break;case"n":a=""+e.v;break;case"e":a=Sr[e.v];break;case"d":a=n.cellDates?oe(e.v,-1).toISOString():((e=le(e)).t="n",""+(e.v=G(oe(e.v)))),void 0===e.z&&(e.z=ue._table[14]);break;default:a=e.v}var o=Xe("v",ye(a)),l={r:t},c=Wo(n.cellXfs,e,n);switch(0!==c&&(l.s=c),e.t){case"n":break;case"d":l.t="d";break;case"b":l.t="b";break;case"e":l.t="e";break;default:if(null==e.v){delete e.t;break}if(n.bookSST){o=Xe("v",""+Ho(n.Strings,e.v,n.revStrings)),l.t="s";break}l.t="str"}return e.t!=s&&(e.t=s,e.v=i),e.f&&(i=e.F&&e.F.slice(0,t.length)==t?{t:"array",ref:e.F}:null,o=Ge("f",ye(e.f),i)+(null!=e.v?o:"")),e.l&&r["!links"].push([t,e.l]),e.c&&r["!comments"].push([t,e.c]),Ge("c",o,l)}(g,m,e,t))&&i.push(r)}(0<i.length||h&&h[c])&&(d={r:n},h&&h[c]&&((a=h[c]).hidden&&(d.hidden=1),p=-1,a.hpx?p=Js(a.hpx):a.hpt&&(p=a.hpt),-1<p&&(d.ht=p,d.customHeight=1),a.level&&(d.outlineLevel=a.level)),s[s.length]=Ge("row",i.join(""),d))}if(h)for(;c<h.length;++c)h&&h[c]&&(d={r:c+1},(a=h[c]).hidden&&(d.hidden=1),p=-1,a.hpx?p=Js(a.hpx):a.hpt&&(p=a.hpt),-1<p&&(d.ht=p,d.customHeight=1),a.level&&(d.outlineLevel=a.level),s[s.length]=Ge("row","",d));return s.join("")}var ul=Ge("worksheet",null,{xmlns:Ye.main[0],"xmlns:r":Ye.r});function dl(e,t,r,n){var a,s,i,o,l=[z,ul],c=r.SheetNames[e],f=r.Sheets[c],h=(f=null==f?{}:f)["!ref"]||"A1",c=Yt(h);if(16383<c.e.c||1048575<c.e.r){if(t.WTF)throw new Error("Range "+h+" exceeds format limit A1:XFD1048576");c.e.c=Math.min(c.e.c,16383),c.e.r=Math.min(c.e.c,1048575),h=$t(c)}if(n=n||{},f["!comments"]=[],f["!drawing"]=[],"xlsx"!==t.bookType&&r.vbaraw){var u=r.SheetNames[e];try{r.Workbook&&(u=r.Workbook.Sheets[e].CodeName||u)}catch(e){}l[l.length]=Ge("sheetPr",null,{codeName:ye(u)})}l[l.length]=Ge("dimension",null,{ref:h}),l[l.length]=(c={workbookViewId:"0"},((((u=r)||{}).Workbook||{}).Views||[])[0]&&(c.rightToLeft=u.Workbook.Views[0].RTL?"1":"0"),Ge("sheetViews",Ge("sheetView",null,c),{})),t.sheetFormat&&(l[l.length]=Ge("sheetFormatPr",null,{defaultRowHeight:t.sheetFormat.defaultRowHeight||"16",baseColWidth:t.sheetFormat.baseColWidth||"10",outlineLevelRow:t.sheetFormat.outlineLevelRow||"7"})),null!=f["!cols"]&&0<f["!cols"].length&&(l[l.length]=function(e){for(var t,r=["<cols>"],n=0;n!=e.length;++n)(t=e[n])&&(r[r.length]=Ge("col",null,zo(n,t)));return r[r.length]="</cols>",r.join("")}(f["!cols"])),l[a=l.length]="<sheetData/>",f["!links"]=[],null!=f["!ref"]&&0<(s=hl(f,t)).length&&(l[l.length]=s),l.length>a+1&&(l[l.length]="</sheetData>",l[a]=l[a].replace("/>",">")),null!=f["!protect"]&&(l[l.length]=(i=f["!protect"],o={sheet:1},["objects","scenarios","selectLockedCells","selectUnlockedCells"].forEach(function(e){null!=i[e]&&i[e]&&(o[e]="1")}),["formatColumns","formatRows","formatCells","insertColumns","insertRows","insertHyperlinks","deleteColumns","deleteRows","sort","autoFilter","pivotTables"].forEach(function(e){null==i[e]||i[e]||(o[e]="0")}),i.password&&(o.password=ks(i.password).toString(16).toUpperCase()),Ge("sheetProtection",null,o))),null!=f["!autofilter"]&&(l[l.length]=function(e,t,r,n){var a="string"==typeof e.ref?e.ref:$t(e.ref);r.Workbook||(r.Workbook={}),r.Workbook.Names||(r.Workbook.Names=[]);var s=r.Workbook.Names;(e=Gt(a)).s.r==e.e.r&&(e.e.r=Gt(t["!ref"]).e.r,a=$t(e));for(var i=0;i<s.length;++i){var o=s[i];if("_xlnm._FilterDatabase"==o.Name&&o.Sheet==n){o.Ref="'"+r.SheetNames[n]+"'!"+a;break}}return i==s.length&&s.push({Name:"_xlnm._FilterDatabase",Sheet:n,Ref:"'"+r.SheetNames[n]+"'!"+a}),Ge("autoFilter",null,{ref:a})}(f["!autofilter"],f,r,e)),null!=f["!merges"]&&0<f["!merges"].length&&(l[l.length]=function(e){if(0===e.length)return"";for(var t='<mergeCells count="'+e.length+'">',r=0;r!=e.length;++r)t+='<mergeCell ref="'+$t(e[r])+'"/>';return t+"</mergeCells>"}(f["!merges"]));var d,p,m=-1;return 0<f["!links"].length&&(l[l.length]="<hyperlinks>",f["!links"].forEach(function(e){e[1].Target&&(p={ref:e[0]},"#"!=e[1].Target.charAt(0)&&(m=Yr(n,-1,ye(e[1].Target).replace(/#.*$/,""),Wr.HLINK),p["r:id"]="rId"+m),-1<(d=e[1].Target.indexOf("#"))&&(p.location=ye(e[1].Target.slice(d+1))),e[1].Tooltip&&(p.tooltip=ye(e[1].Tooltip)),l[l.length]=Ge("hyperlink",null,p))}),l[l.length]="</hyperlinks>"),delete f["!links"],null!=f["!margins"]&&(l[l.length]=(Vo(r=f["!margins"]),Ge("pageMargins",null,r))),l[l.length]="",t&&!t.ignoreEC&&null!=t.ignoreEC||(l[l.length]=Xe("ignoredErrors",Ge("ignoredError",null,{numberStoredAsText:1,sqref:h}))),0<f["!drawing"].length?(m=Yr(n,-1,"../drawings/drawing"+(e+1)+".xml",Wr.DRAW),l[l.length]=Ge("drawing",null,{"r:id":"rId"+m})):delete f["!drawing"],0<f["!comments"].length&&(m=Yr(n,-1,"../drawings/vmlDrawing"+(e+1)+".vml",Wr.VML),l[l.length]=Ge("legacyDrawing",null,{"r:id":"rId"+m}),f["!legacy"]=m),2<l.length&&(l[l.length]="</worksheet>",l[1]=l[1].replace("/>",">")),l.join("")}function pl(e,t,r,n){r=function(e,t,r){var n=Rt(145),a=(r["!rows"]||[])[e]||{};n.write_shift(4,e),n.write_shift(4,0);var s=320;a.hpx?s=20*Js(a.hpx):a.hpt&&(s=20*a.hpt),n.write_shift(2,s),n.write_shift(1,0),s=0,a.level&&(s|=a.level),a.hidden&&(s|=16),(a.hpx||a.hpt)&&(s|=32),n.write_shift(1,s),n.write_shift(1,0);var i=0,s=n.l;n.l+=4;for(var o={r:e,c:0},l=0;l<16;++l)if(!(t.s.c>l+1<<10||t.e.c<l<<10)){for(var c=-1,f=-1,h=l<<10;h<l+1<<10;++h)o.c=h,(Array.isArray(r)?(r[o.r]||[])[o.c]:r[jt(o)])&&(c<0&&(c=h),f=h);c<0||(++i,n.write_shift(4,c),n.write_shift(4,f))}return e=n.l,n.l=s,n.write_shift(4,i),n.l=e,n.length>n.l?n.slice(0,n.l):n}(n,r,t);(17<r.length||(t["!rows"]||[])[n])&&Dt(e,"BrtRowHdr",r)}var lo=br,ml=vr;function gl(e,t,r){return or(t,r=null==r?Rt(12):r),function(e,t){null==t&&(t=Rt(4));var r=0,n=0,a=100*e;if(e==(0|e)&&-(1<<29)<=e&&e<1<<29?n=1:a==(0|a)&&-(1<<29)<=a&&a<1<<29&&(r=n=1),!n)throw new Error("unsupported RkNumber "+e);t.write_shift(-4,((r?a:e)<<2)+(r+2))}(e.v,r),r}var y=br,bl=vr;var vl=["left","right","top","bottom","header","footer"];function El(e,t,r,n,a,s){if(void 0!==t.v){var i="";switch(t.t){case"b":i=t.v?"1":"0";break;case"d":(t=le(t)).z=t.z||ue._table[14],t.v=G(oe(t.v)),t.t="n";break;case"n":case"e":i=""+t.v;break;default:i=t.v}var o,l,c,f,h,u,d,p={r:r,c:n};switch(p.s=Wo(a.cellXfs,t,a),t.l&&s["!links"].push([jt(p),t.l]),t.c&&s["!comments"].push([jt(p),t.c]),t.t){case"s":case"str":return void(a.bookSST?(i=Ho(a.Strings,t.v,a.revStrings),p.t="s",p.v=i,Dt(e,"BrtCellIsst",(or(h=p,u=null==u?Rt(12):u),u.write_shift(4,h.v),u))):(p.t="str",Dt(e,"BrtCellSt",(h=t,or(p,f=null==f?Rt(12+4*h.v.length):f),rr(h.v,f),f.length>f.l?f.slice(0,f.l):f))));case"n":return void(t.v==(0|t.v)&&-1e3<t.v&&t.v<1e3?Dt(e,"BrtCellRk",gl(t,p)):Dt(e,"BrtCellReal",(l=t,or(p,c=null==c?Rt(16):c),wr(l.v,c),c)));case"b":return p.t="b",void Dt(e,"BrtCellBool",(l=t,or(p,o=null==o?Rt(9):o),o.write_shift(1,l.v?1:0),o));case"e":p.t="e"}Dt(e,"BrtCellBlank",or(p,d=null==d?Rt(8):d))}}function wl(t,e){var r,n;e&&e["!merges"]&&(Dt(t,"BrtBeginMergeCells",(r=e["!merges"].length,(n=null==n?Rt(4):n).write_shift(4,r),n)),e["!merges"].forEach(function(e){Dt(t,"BrtMergeCell",bl(e))}),Dt(t,"BrtEndMergeCells"))}function Sl(r,e){e&&e["!cols"]&&(Dt(r,"BrtBeginColInfos"),e["!cols"].forEach(function(e,t){e&&Dt(r,"BrtColInfo",function(e,t,r){null==r&&(r=Rt(18));var n=zo(e,t);return r.write_shift(-4,e),r.write_shift(-4,e),r.write_shift(4,256*(n.width||10)),r.write_shift(4,0),e=0,t.hidden&&(e|=1),"number"==typeof n.width&&(e|=2),r.write_shift(1,e),r.write_shift(1,0),r}(t,e))}),Dt(r,"BrtEndColInfos"))}function _l(e,t){var r;t&&t["!ref"]&&(Dt(e,"BrtBeginCellIgnoreECs"),Dt(e,"BrtCellIgnoreEC",(r=Yt(t["!ref"]),(t=Rt(24)).write_shift(4,4),t.write_shift(4,1),vr(r,t),t)),Dt(e,"BrtEndCellIgnoreECs"))}function yl(n,e,a){e["!links"].forEach(function(e){var t,r;e[1].Target&&(t=Yr(a,-1,e[1].Target.replace(/#.*$/,""),Wr.HLINK),Dt(n,"BrtHLink",(r=t,e=Rt(50+4*((t=e)[1].Target.length+(t[1].Tooltip||"").length)),vr({s:Xt(t[0]),e:Xt(t[0])},e),pr("rId"+r,e),rr((-1==(r=t[1].Target.indexOf("#"))?"":t[1].Target.slice(r+1))||"",e),rr(t[1].Tooltip||"",e),rr("",e),e.slice(0,e.l))))}),delete e["!links"]}function Cl(e,t,r){Dt(e,"BrtBeginWsViews"),Dt(e,"BrtBeginWsView",function(e,t){null==t&&(t=Rt(30));var r=924;return(((e||{}).Views||[])[0]||{}).RTL&&(r|=32),t.write_shift(2,r),t.write_shift(4,0),t.write_shift(4,0),t.write_shift(4,0),t.write_shift(1,0),t.write_shift(1,0),t.write_shift(2,0),t.write_shift(2,100),t.write_shift(2,0),t.write_shift(2,0),t.write_shift(2,0),t.write_shift(4,0),t}(r)),Dt(e,"BrtEndWsView"),Dt(e,"BrtEndWsViews")}function Bl(e,t){var r,n;t["!protect"]&&Dt(e,"BrtSheetProtection",(r=t["!protect"],(n=null==n?Rt(66):n).write_shift(2,r.password?ks(r.password):0),n.write_shift(4,1),[["objects",!1],["scenarios",!1],["formatCells",!0],["formatColumns",!0],["formatRows",!0],["insertColumns",!0],["insertRows",!0],["insertHyperlinks",!0],["deleteColumns",!0],["deleteRows",!0],["selectLockedCells",!1],["sort",!0],["autoFilter",!0],["pivotTables",!0],["selectUnlockedCells",!1]].forEach(function(e){e[1]?n.write_shift(4,null==r[e[0]]||r[e[0]]?0:1):n.write_shift(4,null!=r[e[0]]&&r[e[0]]?0:1)}),n))}function Tl(e,t,r,n){var a=Ft(),s=r.SheetNames[e],i=r.Sheets[s]||{},o=s;try{r&&r.Workbook&&(o=r.Workbook.Sheets[e].CodeName||o)}catch(e){}var l,c,s=Yt(i["!ref"]||"A1");if(16383<s.e.c||1048575<s.e.r){if(t.WTF)throw new Error("Range "+(i["!ref"]||"A1")+" exceeds format limit A1:XFD1048576");s.e.c=Math.min(s.e.c,16383),s.e.r=Math.min(s.e.c,1048575)}return i["!links"]=[],i["!comments"]=[],Dt(a,"BrtBeginSheet"),r.vbaraw&&Dt(a,"BrtWsProp",function(e,t){null==t&&(t=Rt(84+4*e.length));for(var r=0;r<3;++r)t.write_shift(1,0);return yr({auto:1},t),t.write_shift(-4,-1),t.write_shift(-4,-1),cr(e,t),t.slice(0,t.l)}(o)),Dt(a,"BrtWsDim",ml(s)),Cl(a,0,r.Workbook),Sl(a,i),function(e,t,r){var n,a=Yt(t["!ref"]||"A1"),s=[];Dt(e,"BrtBeginSheetData");var i=Array.isArray(t),o=a.e.r;t["!rows"]&&(o=Math.max(a.e.r,t["!rows"].length-1));for(var l=a.s.r;l<=o;++l)if(n=Ht(l),pl(e,t,a,l),l<=a.e.r)for(var c=a.s.c;c<=a.e.c;++c){l===a.s.r&&(s[c]=Vt(c));var f=s[c]+n,f=i?(t[l]||[])[c]:t[f];f&&El(e,f,l,c,r,t)}Dt(e,"BrtEndSheetData")}(a,i,t),Bl(a,i),s=a,(r=i)["!autofilter"]&&(Dt(s,"BrtBeginAFilter",vr(Yt(r["!autofilter"].ref))),Dt(s,"BrtEndAFilter")),wl(a,i),yl(a,i,n),i["!margins"]&&Dt(a,"BrtMargins",(l=i["!margins"],null==c&&(c=Rt(48)),Vo(l),vl.forEach(function(e){wr(l[e],c)}),c)),t&&!t.ignoreEC&&null!=t.ignoreEC||_l(a,i),t=a,e=e,n=n,0<(i=i)["!comments"].length&&(e=Yr(n,-1,"../drawings/vmlDrawing"+(e+1)+".vml",Wr.VML),Dt(t,"BrtLegacyDrawing",pr("rId"+e)),i["!legacy"]=e),Dt(a,"BrtEndSheet"),a.end()}function kl(e,t,r,n,a,s){var i=s||{"!type":"chart"};if(!e)return s;var o,l=0,c=0,f={s:{r:2e6,c:2e6},e:{r:0,c:0}};return(e.match(/<c:numCache>[\s\S]*?<\/c:numCache>/gm)||[]).forEach(function(e){var t,r=(t=[],((e=e).match(/<c:pt idx="(\d*)">(.*?)<\/c:pt>/gm)||[]).forEach(function(e){e=e.match(/<c:pt idx="(\d*?)"><c:v>(.*)<\/c:v><\/c:pt>/);e&&(t[+e[1]]=+e[2])}),e=we((e.match(/<c:formatCode>([\s\S]*?)<\/c:formatCode>/)||["","General"])[1]),[t,e]);f.s.r=f.s.c=0,f.e.c=l,o=Vt(l),r[0].forEach(function(e,t){i[o+Ht(t)]={t:"n",v:e,z:r[1]},c=t}),f.e.r<c&&(f.e.r=c),++l}),0<l&&(i["!ref"]=$t(f)),i}Wr.CS="http://schemas.openxmlformats.org/officeDocument/2006/relationships/chartsheet";Ge("chartsheet",null,{xmlns:Ye.main[0],"xmlns:r":Ye.r});var xl=[["allowRefreshQuery",!1,"bool"],["autoCompressPictures",!0,"bool"],["backupFile",!1,"bool"],["checkCompatibility",!1,"bool"],["CodeName",""],["date1904",!1,"bool"],["defaultThemeVersion",0,"int"],["filterPrivacy",!1,"bool"],["hidePivotFieldList",!1,"bool"],["promptedSolutions",!1,"bool"],["publishItems",!1,"bool"],["refreshAllConnections",!1,"bool"],["saveExternalLinkValues",!0,"bool"],["showBorderUnselectedTables",!0,"bool"],["showInkAnnotation",!0,"bool"],["showObjects","all"],["showPivotChartFilter",!1,"bool"],["updateLinks","userSet"]],Al=[["activeTab",0,"int"],["autoFilterDateGrouping",!0,"bool"],["firstSheet",0,"int"],["minimized",!1,"bool"],["showHorizontalScroll",!0,"bool"],["showSheetTabs",!0,"bool"],["showVerticalScroll",!0,"bool"],["tabRatio",600,"int"],["visibility","visible"]],Il=[],Rl=[["calcCompleted","true"],["calcMode","auto"],["calcOnSave","true"],["concurrentCalc","true"],["fullCalcOnLoad","false"],["fullPrecision","true"],["iterate","false"],["iterateCount","100"],["iterateDelta","0.001"],["refMode","A1"]];function Ol(e,t){for(var r=0;r!=e.length;++r)for(var n=e[r],a=0;a!=t.length;++a){var s=t[a];if(null==n[s[0]])n[s[0]]=s[1];else switch(s[2]){case"bool":"string"==typeof n[s[0]]&&(n[s[0]]=Re(n[s[0]]));break;case"int":"string"==typeof n[s[0]]&&(n[s[0]]=parseInt(n[s[0]],10))}}}function Fl(e,t){for(var r=0;r!=t.length;++r){var n=t[r];if(null==e[n[0]])e[n[0]]=n[1];else switch(n[2]){case"bool":"string"==typeof e[n[0]]&&(e[n[0]]=Re(e[n[0]]));break;case"int":"string"==typeof e[n[0]]&&(e[n[0]]=parseInt(e[n[0]],10))}}}function Dl(e){Fl(e.WBProps,xl),Fl(e.CalcPr,Rl),Ol(e.WBView,Al),Ol(e.Sheets,Il),Mo.date1904=Re(e.WBProps.date1904)}var Pl="][*?/\\".split("");function Nl(t,r){if(31<t.length){if(r)return;throw new Error("Sheet names cannot exceed 31 chars")}var n=!0;return Pl.forEach(function(e){if(-1!=t.indexOf(e)){if(!r)throw new Error("Sheet name cannot contain : \\ / ? * [ ]");n=!1}}),n}function Ll(e){if(!e||!e.SheetNames||!e.Sheets)throw new Error("Invalid Workbook");if(!e.SheetNames.length)throw new Error("Workbook is empty");var a,s,i,t=e.Workbook&&e.Workbook.Sheets||[];a=e.SheetNames,s=t,i=!!e.vbaraw,a.forEach(function(e,t){Nl(e);for(var r=0;r<t;++r)if(e==a[r])throw new Error("Duplicate Sheet Name: "+e);if(i){var n=s&&s[t]&&s[t].CodeName||e;if(95==n.charCodeAt(0)&&22<n.length)throw new Error("Bad Code Name: Worksheet"+n)}});for(var r=0;r<e.SheetNames.length;++r)!function(e,t){if(e&&e["!ref"]){var r=Yt(e["!ref"]);if(r.e.c<r.s.c||r.e.r<r.s.r)throw new Error("Bad range ("+t+"): "+e["!ref"])}}(e.Sheets[e.SheetNames[r]],(e.SheetNames[r],r))}var Ml=/<\w+:workbook/;var Ul=Ge("workbook",null,{xmlns:Ye.main[0],"xmlns:r":Ye.r});function Hl(t){var r=[z];r[r.length]=Ul;var e=t.Workbook&&0<(t.Workbook.Names||[]).length,n={codeName:"ThisWorkbook"};t.Workbook&&t.Workbook.WBProps&&(xl.forEach(function(e){null!=t.Workbook.WBProps[e[0]]&&t.Workbook.WBProps[e[0]]!=e[1]&&(n[e[0]]=t.Workbook.WBProps[e[0]])}),t.Workbook.WBProps.CodeName&&(n.codeName=t.Workbook.WBProps.CodeName,delete n.CodeName)),r[r.length]=Ge("workbookPr",null,n);var a=t.Workbook&&t.Workbook.Sheets||[],s=0;for(r[r.length]="<sheets>",s=0;s!=t.SheetNames.length;++s){var i={name:ye(t.SheetNames[s].slice(0,31))};if(i.sheetId=""+(s+1),i["r:id"]="rId"+(s+1),a[s])switch(a[s].Hidden){case 1:i.state="hidden";break;case 2:i.state="veryHidden"}r[r.length]=Ge("sheet",null,i)}return r[r.length]="</sheets>",e&&(r[r.length]="<definedNames>",t.Workbook&&t.Workbook.Names&&t.Workbook.Names.forEach(function(e){var t={name:e.Name};e.Comment&&(t.comment=e.Comment),null!=e.Sheet&&(t.localSheetId=""+e.Sheet),e.Hidden&&(t.hidden="1"),e.Ref&&(r[r.length]=Ge("definedName",String(e.Ref).replace(/</g,"&lt;").replace(/>/g,"&gt;"),t))}),r[r.length]="</definedNames>"),2<r.length&&(r[r.length]="</workbook>",r[1]=r[1].replace("/>",">")),r.join("")}function zl(e,t){Dt(e,"BrtBeginBundleShs");for(var r,n=0;n!=t.SheetNames.length;++n){var a={Hidden:t.Workbook&&t.Workbook.Sheets&&t.Workbook.Sheets[n]&&t.Workbook.Sheets[n].Hidden||0,iTabID:n+1,strRelID:"rId"+(n+1),name:t.SheetNames[n]};Dt(e,"BrtBundleSh",(r=a,(a=(a=void 0)||Rt(127)).write_shift(4,r.Hidden),a.write_shift(4,r.iTabID),pr(r.strRelID,a),rr(r.name.slice(0,31),a),a.length>a.l?a.slice(0,a.l):a))}Dt(e,"BrtEndBundleShs")}function Vl(e,t){if(t.Workbook&&t.Workbook.Sheets){for(var r,n=t.Workbook.Sheets,a=0,s=-1,i=-1;a<n.length;++a)!n[a]||!n[a].Hidden&&-1==s?s=a:1==n[a].Hidden&&-1==i&&(i=a);s<i||(Dt(e,"BrtBeginBookViews"),Dt(e,"BrtBookView",(t=s,(r=r||Rt(29)).write_shift(-4,0),r.write_shift(-4,460),r.write_shift(4,28800),r.write_shift(4,17600),r.write_shift(4,500),r.write_shift(4,t),r.write_shift(4,t),r.write_shift(1,120),r.length>r.l?r.slice(0,r.l):r)),Dt(e,"BrtEndBookViews"))}}function Wl(e,t){var r=Ft();return Dt(r,"BrtBeginBook"),Dt(r,"BrtFileVersion",function(e){e=e||Rt(127);for(var t=0;4!=t;++t)e.write_shift(4,0);return rr("SheetJS",e),rr(n.version,e),rr(n.version,e),rr("7262",e),e.length=e.l,e.length>e.l?e.slice(0,e.l):e}()),Dt(r,"BrtWbProp",function(e,t){t=t||Rt(72);var r=0;return e&&e.filterPrivacy&&(r|=8),t.write_shift(4,r),t.write_shift(4,0),cr(e&&e.CodeName||"ThisWorkbook",t),t.slice(0,t.l)}(e.Workbook&&e.Workbook.WBProps||null)),Vl(r,e),zl(r,e),Dt(r,"BrtEndBook"),r.end()}function Xl(e,t,r){return(".bin"===t.slice(-4)?function(e,n){var a={AppVersion:{},WBProps:{},WBView:[],Sheets:[],CalcPr:{},xmlns:""},s=[],i=!1;(n=n||{}).biff=12;var o=[],l=[[]];return l.SheetNames=[],l.XTI=[],Ot(e,function(e,t,r){switch(r){case 156:l.SheetNames.push(e.name),a.Sheets.push(e);break;case 153:a.WBProps=e;break;case 39:null!=e.Sheet&&(n.SID=e.Sheet),e.Ref=Co(e.Ptg,0,null,l,n),delete n.SID,delete e.Ptg,o.push(e);break;case 1036:break;case 357:case 358:case 355:case 667:l[0].length?l.push([r,e]):l[0]=[r,e],l[l.length-1].XTI=[];break;case 362:0===l.length&&(l[0]=[],l[0].XTI=[]),l[l.length-1].XTI=l[l.length-1].XTI.concat(e),l.XTI=l.XTI.concat(e);break;case 361:break;case 3072:case 3073:case 2071:case 534:case 677:case 158:case 157:case 610:case 2050:case 155:case 548:case 676:case 128:case 665:case 2128:case 2125:case 549:case 2053:case 596:case 2076:case 2075:case 2082:case 397:case 154:case 1117:case 553:case 2091:break;case 35:s.push(t),i=!0;break;case 36:s.pop(),i=!1;break;case 37:s.push(t),i=!0;break;case 38:s.pop(),i=!1;break;case 16:break;default:if(!(0<(t||"").indexOf("Begin"))&&!(0<(t||"").indexOf("End"))&&(!i||n.WTF&&"BrtACBegin"!=s[s.length-1]&&"BrtFRTBegin"!=s[s.length-1]))throw new Error("Unexpected record "+r+" "+t)}},n),Dl(a),a.Names=o,a.supbooks=l,a}:function(n,a){if(!n)throw new Error("Could not find file");var s={AppVersion:{},WBProps:{},WBView:[],Sheets:[],CalcPr:{},Names:[],xmlns:""},i=!1,o="xmlns",l={},c=0;if(n.replace(W,function(e,t){var r=pe(e);switch(me(r[0])){case"<?xml":break;case"<workbook":e.match(Ml)&&(o="xmlns"+e.match(/<(\w+):/)[1]),s.xmlns=r[o];break;case"</workbook>":break;case"<fileVersion":delete r[0],s.AppVersion=r;break;case"<fileVersion/>":case"</fileVersion>":break;case"<fileSharing":case"<fileSharing/>":break;case"<workbookPr":case"<workbookPr/>":xl.forEach(function(e){if(null!=r[e[0]])switch(e[2]){case"bool":s.WBProps[e[0]]=Re(r[e[0]]);break;case"int":s.WBProps[e[0]]=parseInt(r[e[0]],10);break;default:s.WBProps[e[0]]=r[e[0]]}}),r.codeName&&(s.WBProps.CodeName=r.codeName);break;case"</workbookPr>":case"<workbookProtection":case"<workbookProtection/>":break;case"<bookViews":case"<bookViews>":case"</bookViews>":break;case"<workbookView":case"<workbookView/>":delete r[0],s.WBView.push(r);break;case"</workbookView>":break;case"<sheets":case"<sheets>":case"</sheets>":break;case"<sheet":switch(r.state){case"hidden":r.Hidden=1;break;case"veryHidden":r.Hidden=2;break;default:r.Hidden=0}delete r.state,r.name=we(Oe(r.name)),delete r[0],s.Sheets.push(r);break;case"</sheet>":break;case"<functionGroups":case"<functionGroups/>":case"<functionGroup":break;case"<externalReferences":case"</externalReferences>":case"<externalReferences>":case"<externalReference":case"<definedNames/>":break;case"<definedNames>":case"<definedNames":i=!0;break;case"</definedNames>":i=!1;break;case"<definedName":(l={}).Name=Oe(r.name),r.comment&&(l.Comment=r.comment),r.localSheetId&&(l.Sheet=+r.localSheetId),Re(r.hidden||"0")&&(l.Hidden=!0),c=t+e.length;break;case"</definedName>":l.Ref=we(Oe(n.slice(c,t))),s.Names.push(l);break;case"<definedName/>":break;case"<calcPr":case"<calcPr/>":delete r[0],s.CalcPr=r;break;case"</calcPr>":case"<oleSize":break;case"<customWorkbookViews>":case"</customWorkbookViews>":case"<customWorkbookViews":break;case"<customWorkbookView":case"</customWorkbookView>":break;case"<pivotCaches>":case"</pivotCaches>":case"<pivotCaches":case"<pivotCache":break;case"<smartTagPr":case"<smartTagPr/>":break;case"<smartTagTypes":case"<smartTagTypes>":case"</smartTagTypes>":case"<smartTagType":break;case"<webPublishing":case"<webPublishing/>":break;case"<fileRecoveryPr":case"<fileRecoveryPr/>":break;case"<webPublishObjects>":case"<webPublishObjects":case"</webPublishObjects>":case"<webPublishObject":break;case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<ext":i=!0;break;case"</ext>":i=!1;break;case"<ArchID":break;case"<AlternateContent":case"<AlternateContent>":i=!0;break;case"</AlternateContent>":i=!1;break;case"<revisionPtr":break;default:if(!i&&a.WTF)throw new Error("unrecognized "+r[0]+" in workbook")}return e}),-1===Ye.main.indexOf(s.xmlns))throw new Error("Unknown Namespace: "+s.xmlns);return Dl(s),s})(e,r)}function jl(e,t,r,n,a,s,i,o){return(".bin"===t.slice(-4)?function(e,t,s,i,o,l,c){if(!e)return e;var f=t||{};i=i||{"!id":{}},null!=fe&&null==f.dense&&(f.dense=fe);var h,u,d,p,m,g,b,v,E,w,S=f.dense?[]:{},_={s:{r:2e6,c:2e6},e:{r:0,c:0}},y=!1,C=!1,B=[];f.biff=12;var T=f["!row"]=0,k=!1,x=[],A={},I=f.supbooks||o.supbooks||[[]];if(I.sharedf=A,I.arrayf=x,I.SheetNames=o.SheetNames||o.Sheets.map(function(e){return e.name}),!f.supbooks&&(f.supbooks=I,o.Names))for(var r=0;r<o.Names.length;++r)I[0][r+1]=o.Names[r];var R=[],O=[],F=!1;return Ot(e,function(e,t,r){if(!C)switch(r){case 148:h=e;break;case 0:u=e,f.sheetRows&&f.sheetRows<=u.r&&(C=!0),E=Ht(m=u.r),f["!row"]=u.r,(e.hidden||e.hpt||null!=e.level)&&(e.hpt&&(e.hpx=qs(e.hpt)),O[e.r]=e);break;case 2:case 3:case 4:case 5:case 6:case 7:case 8:case 9:case 10:case 11:switch(d={t:e[2]},e[2]){case"n":d.v=e[1];break;case"s":v=Lo[e[1]],d.v=v.t,d.r=v.r;break;case"b":d.v=!!e[1];break;case"e":d.v=e[1],!1!==f.cellText&&(d.w=Sr[d.v]);break;case"str":d.t="s",d.v=e[1]}if((p=c.CellXf[e[0].iStyleRef])&&Xo(d,p.numFmtId,null,f,l,c),g=e[0].c,f.dense?(S[m]||(S[m]=[]),S[m][g]=d):S[Vt(g)+E]=d,f.cellFormula){for(k=!1,T=0;T<x.length;++T){var n=x[T];u.r>=n[0].s.r&&u.r<=n[0].e.r&&g>=n[0].s.c&&g<=n[0].e.c&&(d.F=$t(n[0]),k=!0)}!k&&3<e.length&&(d.f=e[3])}_.s.r>u.r&&(_.s.r=u.r),_.s.c>g&&(_.s.c=g),_.e.r<u.r&&(_.e.r=u.r),_.e.c<g&&(_.e.c=g),f.cellDates&&p&&"n"==d.t&&ue.is_date(ue._table[p.numFmtId])&&((a=ue.parse_date_code(d.v))&&(d.t="d",d.v=new Date(a.y,a.m-1,a.d,a.H,a.M,a.S,a.u)));break;case 1:if(!f.sheetStubs||y)break;d={t:"z",v:void 0},g=e[0].c,f.dense?(S[m]||(S[m]=[]),S[m][g]=d):S[Vt(g)+E]=d,_.s.r>u.r&&(_.s.r=u.r),_.s.c>g&&(_.s.c=g),_.e.r<u.r&&(_.e.r=u.r),_.e.c<g&&(_.e.c=g);break;case 176:B.push(e);break;case 494:var a=i["!id"][e.relId];for(a?(e.Target=a.Target,e.loc&&(e.Target+="#"+e.loc),e.Rel=a):""==e.relId&&(e.Target="#"+e.loc),m=e.rfx.s.r;m<=e.rfx.e.r;++m)for(g=e.rfx.s.c;g<=e.rfx.e.c;++g)f.dense?(S[m]||(S[m]=[]),S[m][g]||(S[m][g]={t:"z",v:void 0}),S[m][g].l=e):(b=jt({c:g,r:m}),S[b]||(S[b]={t:"z",v:void 0}),S[b].l=e);break;case 426:if(!f.cellFormula)break;x.push(e),(w=f.dense?S[m][g]:S[Vt(g)+E]).f=Co(e[1],0,{r:u.r,c:g},I,f),w.F=$t(e[0]);break;case 427:if(!f.cellFormula)break;A[jt(e[0].s)]=e[1],(w=f.dense?S[m][g]:S[Vt(g)+E]).f=Co(e[1],0,{r:u.r,c:g},I,f);break;case 60:if(!f.cellStyles)break;for(;e.e>=e.s;)R[e.e--]={width:e.w/256,hidden:!!(1&e.flags)},F||(F=!0,Ks(e.w/256)),Zs(R[e.e+1]);break;case 161:S["!autofilter"]={ref:$t(e)};break;case 476:S["!margins"]=e;break;case 147:o.Sheets[s]||(o.Sheets[s]={}),e.name&&(o.Sheets[s].CodeName=e.name);break;case 137:o.Views||(o.Views=[{}]),o.Views[0]||(o.Views[0]={}),e.RTL&&(o.Views[0].RTL=!0);break;case 485:break;case 175:case 644:case 625:case 562:case 396:case 1112:case 1146:case 471:case 1050:case 649:case 1105:case 49:case 589:case 607:case 564:case 1055:case 168:case 174:case 1180:case 499:case 64:case 1053:case 550:case 171:case 167:case 1177:case 169:case 1181:case 551:case 552:case 661:case 639:case 478:case 151:case 537:case 477:case 536:case 1103:case 680:case 1104:case 1024:case 152:case 663:case 535:case 678:case 504:case 1043:case 428:case 170:case 3072:case 50:case 2070:case 1045:break;case 35:y=!0;break;case 36:y=!1;break;case 37:case 38:break;default:if(!(0<(t||"").indexOf("Begin"))&&!(0<(t||"").indexOf("End"))&&(!y||f.WTF))throw new Error("Unexpected record "+r+" "+t)}},f),delete f.supbooks,delete f["!row"],!S["!ref"]&&(_.s.r<2e6||h&&(0<h.e.r||0<h.e.c||0<h.s.r||0<h.s.c))&&(S["!ref"]=$t(h||_)),f.sheetRows&&S["!ref"]&&(e=Yt(S["!ref"]),f.sheetRows<=+e.e.r&&(e.e.r=f.sheetRows-1,e.e.r>_.e.r&&(e.e.r=_.e.r),e.e.r<e.s.r&&(e.s.r=e.e.r),e.e.c>_.e.c&&(e.e.c=_.e.c),e.e.c<e.s.c&&(e.s.c=e.e.c),S["!fullref"]=S["!ref"],S["!ref"]=$t(e))),0<B.length&&(S["!merges"]=B),0<R.length&&(S["!cols"]=R),0<O.length&&(S["!rows"]=O),S}:el)(e,n,r,a,s,i,o)}function Gl(e,t,r,n,a,s){return".bin"===t.slice(-4)?function(e,n,a,t,s){if(!e)return e;t=t||{"!id":{}};var i={"!type":"chart","!chart":null,"!rel":""},o=[],l=!1;return Ot(e,function(e,t,r){switch(r){case 550:i["!rel"]=e;break;case 651:s.Sheets[a]||(s.Sheets[a]={}),e.name&&(s.Sheets[a].CodeName=e.name);break;case 562:case 652:case 669:case 679:case 551:case 552:case 476:case 3072:break;case 35:l=!0;break;case 36:l=!1;break;case 37:o.push(t);break;case 38:o.pop();break;default:if(0<(t||"").indexOf("Begin"))o.push(t);else if(0<(t||"").indexOf("End"))o.pop();else if(!l||n.WTF)throw new Error("Unexpected record "+r+" "+t)}},n),t["!id"][i["!rel"]]&&(i["!chart"]=t["!id"][i["!rel"]]),i}(e,n,r,a,s):function(e,t,r,n){if(!e)return e;r=r||{"!id":{}};var a={"!type":"chart","!chart":null,"!rel":""},s=e.match(Jo);return s&&tl(s[0],0,n,t),(e=e.match(/drawing r:id="(.*?)"/))&&(a["!rel"]=e[1]),r["!id"][a["!rel"]]&&(a["!chart"]=r["!id"][a["!rel"]]),a}(e,r,a,s)}function $l(e,t,r,n){return(".bin"===t.slice(-4)?function(e,n,a){var t,s={NumberFmt:[]};for(t in ue._table)s.NumberFmt[t]=ue._table[t];s.CellXf=[],s.Fonts=[];var i=[],o=!1;return Ot(e,function(e,t,r){switch(r){case 44:s.NumberFmt[e[0]]=e[1],ue.load(e[1],e[0]);break;case 43:s.Fonts.push(e),null!=e.color.theme&&n&&n.themeElements&&n.themeElements.clrScheme&&(e.color.rgb=Hs(n.themeElements.clrScheme[e.color.theme].rgb,e.color.tint||0));break;case 1025:case 45:case 46:break;case 47:"BrtBeginCellXFs"==i[i.length-1]&&s.CellXf.push(e);break;case 48:case 507:case 572:case 475:break;case 1171:case 2102:case 1130:case 512:case 2095:case 3072:break;case 35:o=!0;break;case 36:o=!1;break;case 37:i.push(t);break;case 38:i.pop();break;default:if(0<(t||"").indexOf("Begin"))i.push(t);else if(0<(t||"").indexOf("End"))i.pop();else if(!o||a.WTF)throw new Error("Unexpected record "+r+" "+t)}}),s}:li)(e,r,n)}function Yl(e,t,r){return".bin"===t.slice(-4)?(n=r,s=!(a=[]),Ot(e,function(e,t,r){switch(r){case 159:a.Count=e[0],a.Unique=e[1];break;case 19:a.push(e);break;case 160:return 1;case 35:s=!0;break;case 36:s=!1;break;default:if(0<t.indexOf("Begin")||t.indexOf("End"),!s||n.WTF)throw new Error("Unexpected record "+r+" "+t)}}),a):function(e,t){var r=[],n="";if(!e)return r;if(e=e.match(ds)){for(var n=e[2].replace(ps,"").split(ms),a=0;a!=n.length;++a){var s=us(n[a].trim(),t);null!=s&&(r[r.length]=s)}e=pe(e[1]),r.Count=e.count,r.Unique=e.uniqueCount}return r}(e,r);var n,a,s}function Kl(e,t){return".bin"===t.slice(-4)?(n=[],Ot(e,function(e,t,r){if(63===r)n.push(e);else if(!(0<(t||"").indexOf("Begin")||0<(t||"").indexOf("End")))throw new Error("Unexpected record "+r+" "+t)}),n):function(e){var r=[];if(!e)return r;var n=1;return(e.match(W)||[]).forEach(function(e){var t=pe(e);switch(t[0]){case"<?xml":break;case"<calcChain":case"<calcChain>":case"</calcChain>":break;case"<c":delete t[0],t.i?n=t.i:t.i=n,r.push(t)}}),r}(e);var n}function Zl(e,t,r){if(".bin"===t.slice(-4))return function(e,t){if(!e)return e;var n=t||{},a=!1;Ot(e,function(e,t,r){switch(0,r){case 359:case 363:case 364:case 366:case 367:case 368:case 369:case 370:case 371:case 472:case 577:case 578:case 579:case 580:case 581:case 582:case 583:case 584:case 585:case 586:case 587:break;case 35:a=!0;break;case 36:a=!1;break;default:if(!(0<(t||"").indexOf("Begin"))&&!(0<(t||"").indexOf("End"))&&(!a||n.WTF))throw new Error("Unexpected record "+r.toString(16)+" "+t)}},n)}(e,r)}var Ql=/([\w:]+)=((?:")([^"]*)(?:")|(?:')([^']*)(?:'))/g,Jl=/([\w:]+)=((?:")(?:[^"]*)(?:")|(?:')(?:[^']*)(?:'))/,ql=function(e){return String.fromCharCode(e)};function ec(e,t){var r=e.split(/\s+/),n=[];if(t||(n[0]=r[0]),1===r.length)return n;var a,s,i,o=e.match(Ql);if(o)for(i=0;i!=o.length;++i)-1===(s=(a=o[i].match(Jl))[1].indexOf(":"))?n[a[1]]=a[2].slice(1,a[2].length-1):n["xmlns:"===a[1].slice(0,6)?"xmlns"+a[1].slice(6):a[1].slice(s+1)]=a[2].slice(1,a[2].length-1);return n}function tc(e,t,r){if("z"!==e.t){if(!r||!1!==r.cellText)try{"e"===e.t?e.w=e.w||Sr[e.v]:"General"===t?"n"===e.t?(0|e.v)===e.v?e.w=ue._general_int(e.v):e.w=ue._general_num(e.v):e.w=ue._general(e.v):e.w=(n=t||"General",a=e.v,"General"===(n=ae[n]||we(n))?ue._general(a):ue.format(n,a))}catch(e){if(r.WTF)throw e}var n,a;try{var s,i=ae[t]||t||"General";r.cellNF&&(e.z=i),r.cellDates&&"n"==e.t&&ue.is_date(i)&&((s=ue.parse_date_code(e.v))&&(e.t="d",e.v=new Date(s.y,s.m-1,s.d,s.H,s.M,s.S,s.u)))}catch(e){if(r.WTF)throw e}}}function rc(e){if(K&&Buffer.isBuffer(e))return e.toString("utf8");if("string"==typeof e)return e;if("undefined"!=typeof Uint8Array&&e instanceof Uint8Array)return Oe(o(p(e)));throw new Error("Bad input format: expected Buffer or string")}var nc=/<(\/?)([^\s?>!\/:]*:|)([^\s?>:\/]+)[^>]*>/gm;function ac(e,t){var r=t||{};ne(ue);var n,a=te(rc(e)),s=(a="binary"==r.type||"array"==r.type||"base64"==r.type?"undefined"!=typeof cptable?cptable.utils.decode(65001,ee(a)):Oe(a):a).slice(0,1024).toLowerCase(),i=!1;if(-1==s.indexOf("<?xml")&&["html","table","head","meta","script","style","div"].forEach(function(e){0<=s.indexOf("<"+e)&&(i=!0)}),i)return Nc.to_workbook(a,r);var o,l=[];null!=fe&&null==r.dense&&(r.dense=fe);var c,f,h,u,d,p={},m=[],g=r.dense?[]:{},b="",v={},E={},w=ec('<Data ss:Type="String">'),S=0,_=0,y=0,C={s:{r:2e6,c:2e6},e:{r:0,c:0}},B={},T={},k="",x=0,A=[],I={},R={},O=0,F=[],D=[],P={},N=[],L=!1,M=[],U=[],H={},z=0,V=0,W={Sheets:[],WBProps:{date1904:!1}},X={};for(nc.lastIndex=0,a=a.replace(/<!--([\s\S]*?)-->/gm,"");n=nc.exec(a);)switch(n[3]){case"Data":if(l[l.length-1][1])break;"/"===n[1]?function(e,t,r,n,a,s,i,o,l,c){var f="General",h=n.StyleID,u={};c=c||{};var d=[],p=0;for(void 0===(h=void 0===h&&o?o.StyleID:h)&&i&&(h=i.StyleID);void 0!==s[h]&&(s[h].nf&&(f=s[h].nf),s[h].Interior&&d.push(s[h].Interior),s[h].Parent);)h=s[h].Parent;switch(r.Type){case"Boolean":n.t="b",n.v=Re(e);break;case"String":n.t="s",n.r=xe(we(e)),n.v=-1<e.indexOf("<")?we(t):n.r;break;case"DateTime":"Z"!=e.slice(-1)&&(e+="Z"),n.v=(oe(e)-new Date(Date.UTC(1899,11,30)))/864e5,n.v!=n.v?n.v=we(e):n.v<60&&(n.v=n.v-1),f&&"General"!=f||(f="yyyy-mm-dd");case"Number":void 0===n.v&&(n.v=+e),n.t||(n.t="n");break;case"Error":n.t="e",n.v=_r[e],!1!==c.cellText&&(n.w=e);break;default:n.t="s",n.v=xe(t||e)}if(tc(n,f,c),!1!==c.cellFormula)if(n.Formula){r=we(n.Formula);61==r.charCodeAt(0)&&(r=r.slice(1)),n.f=Xi(r,a),delete n.Formula,"RC"==n.ArrayRange?n.F=Xi("RC:RC",a):n.ArrayRange&&(n.F=Xi(n.ArrayRange,a),l.push([Yt(n.F),n.F]))}else for(p=0;p<l.length;++p)a.r>=l[p][0].s.r&&a.r<=l[p][0].e.r&&a.c>=l[p][0].s.c&&a.c<=l[p][0].e.c&&(n.F=l[p][1]);c.cellStyles&&(d.forEach(function(e){!u.patternType&&e.patternType&&(u.patternType=e.patternType)}),n.s=u),void 0!==n.StyleID&&(n.ixfe=n.StyleID)}(a.slice(S,n.index),k,w,"Comment"==l[l.length-1][0]?P:v,{c:_,r:y},B,N[_],E,M,r):(k="",w=ec(n[0]),S=n.index+n[0].length);break;case"Cell":if("/"===n[1])if(0<D.length&&(v.c=D),(!r.sheetRows||r.sheetRows>y)&&void 0!==v.v&&(r.dense?(g[y]||(g[y]=[]),g[y][_]=v):g[Vt(_)+Ht(y)]=v),v.HRef&&(v.l={Target:v.HRef},v.HRefScreenTip&&(v.l.Tooltip=v.HRefScreenTip),delete v.HRef,delete v.HRefScreenTip),(v.MergeAcross||v.MergeDown)&&(z=_+(0|parseInt(v.MergeAcross,10)),V=y+(0|parseInt(v.MergeDown,10)),A.push({s:{c:_,r:y},e:{c:z,r:V}})),r.sheetStubs)if(v.MergeAcross||v.MergeDown){for(var j=_;j<=z;++j)for(var G=y;G<=V;++G)(_<j||y<G)&&(r.dense?(g[G]||(g[G]=[]),g[G][j]={t:"z"}):g[Vt(j)+Ht(G)]={t:"z"});_=z+1}else++_;else v.MergeAcross?_=z+1:++_;else(_=(v=function(e){var t={};if(1===e.split(/\s+/).length)return t;var r,n,a,s=e.match(Ql);if(s)for(a=0;a!=s.length;++a)-1===(n=(r=s[a].match(Jl))[1].indexOf(":"))?t[r[1]]=r[2].slice(1,r[2].length-1):t["xmlns:"===r[1].slice(0,6)?"xmlns"+r[1].slice(6):r[1].slice(n+1)]=r[2].slice(1,r[2].length-1);return t}(n[0])).Index?+v.Index-1:_)<C.s.c&&(C.s.c=_),_>C.e.c&&(C.e.c=_),"/>"===n[0].slice(-2)&&++_,D=[];break;case"Row":"/"===n[1]||"/>"===n[0].slice(-2)?(y<C.s.r&&(C.s.r=y),y>C.e.r&&(C.e.r=y),"/>"===n[0].slice(-2)&&(E=ec(n[0])).Index&&(y=+E.Index-1),_=0,++y):((E=ec(n[0])).Index&&(y=+E.Index-1),H={},"0"!=E.AutoFitHeight&&!E.Height||(H.hpx=parseInt(E.Height,10),H.hpt=Js(H.hpx),U[y]=H),"1"==E.Hidden&&(H.hidden=!0,U[y]=H));break;case"Worksheet":if("/"===n[1]){if((o=l.pop())[0]!==n[3])throw new Error("Bad state: "+o.join("|"));m.push(b),C.s.r<=C.e.r&&C.s.c<=C.e.c&&(g["!ref"]=$t(C),r.sheetRows&&r.sheetRows<=C.e.r&&(g["!fullref"]=g["!ref"],C.e.r=r.sheetRows-1,g["!ref"]=$t(C))),A.length&&(g["!merges"]=A),0<N.length&&(g["!cols"]=N),0<U.length&&(g["!rows"]=U),p[b]=g}else y=_=0,l.push([n[3],!(C={s:{r:2e6,c:2e6},e:{r:0,c:0}})]),o=ec(n[0]),b=we(o.Name),g=r.dense?[]:{},A=[],M=[],U=[],W.Sheets.push(X={name:b,Hidden:0});break;case"Table":if("/"===n[1]){if((o=l.pop())[0]!==n[3])throw new Error("Bad state: "+o.join("|"))}else{if("/>"==n[0].slice(-2))break;ec(n[0]),l.push([n[3],!1]),L=!(N=[])}break;case"Style":"/"===n[1]?(h=B,u=T,(d=r).cellStyles&&(!u.Interior||(d=u.Interior).Pattern&&(d.patternType=ei[d.Pattern]||d.Pattern)),h[u.ID]=u):T=ec(n[0]);break;case"NumberFormat":T.nf=we(ec(n[0]).Format||"General"),ae[T.nf]&&(T.nf=ae[T.nf]);for(var $=0;392!=$&&ue._table[$]!=T.nf;++$);if(392==$)for($=57;392!=$;++$)if(null==ue._table[$]){ue.load(T.nf,$);break}break;case"Column":if("Table"!==l[l.length-1][0])break;if((c=ec(n[0])).Hidden&&(c.hidden=!0,delete c.Hidden),c.Width&&(c.wpx=parseInt(c.Width,10)),!L&&10<c.wpx){L=!0,Xs=zs;for(var Y=0;Y<N.length;++Y)N[Y]&&Zs(N[Y])}L&&Zs(c),N[c.Index-1||N.length]=c;for(var K=0;K<+c.Span;++K)N[N.length]=le(c);break;case"NamedRange":W.Names||(W.Names=[]);var Z=pe(n[0]),Q={Name:Z.Name,Ref:Xi(Z.RefersTo.slice(1),{r:0,c:0})};0<W.Sheets.length&&(Q.Sheet=W.Sheets.length-1),W.Names.push(Q);break;case"NamedCell":case"B":case"I":case"U":case"S":case"Sub":case"Sup":case"Span":case"Border":case"Alignment":case"Borders":break;case"Font":if("/>"===n[0].slice(-2))break;"/"===n[1]?k+=a.slice(x,n.index):x=n.index+n[0].length;break;case"Interior":if(!r.cellStyles)break;T.Interior=ec(n[0]);break;case"Protection":break;case"Author":case"Title":case"Description":case"Created":case"Keywords":case"Subject":case"Category":case"Company":case"LastAuthor":case"LastSaved":case"LastPrinted":case"Version":case"Revision":case"TotalTime":case"HyperlinkBase":case"Manager":case"ContentStatus":case"Identifier":case"Language":case"AppName":if("/>"===n[0].slice(-2))break;"/"===n[1]?(u=I,Z=n[3],Q=a.slice(O,n.index),u[Z=un[Z]||Z]=Q):O=n.index+n[0].length;break;case"Paragraphs":break;case"Styles":case"Workbook":if("/"===n[1]){if((o=l.pop())[0]!==n[3])throw new Error("Bad state: "+o.join("|"))}else l.push([n[3],!1]);break;case"Comment":if("/"===n[1]){if((o=l.pop())[0]!==n[3])throw new Error("Bad state: "+o.join("|"));(f=P).t=f.v||"",f.t=f.t.replace(/\r\n/g,"\n").replace(/\r/g,"\n"),f.v=f.w=f.ixfe=void 0,D.push(P)}else l.push([n[3],!1]),P={a:(o=ec(n[0])).Author};break;case"AutoFilter":if("/"===n[1]){if((o=l.pop())[0]!==n[3])throw new Error("Bad state: "+o.join("|"))}else"/"!==n[0].charAt(n[0].length-2)&&(f=ec(n[0]),g["!autofilter"]={ref:Xi(f.Range).replace(/\$/g,"")},l.push([n[3],!0]));break;case"Name":break;case"ComponentOptions":case"DocumentProperties":case"CustomDocumentProperties":case"OfficeDocumentSettings":case"PivotTable":case"PivotCache":case"Names":case"MapInfo":case"PageBreaks":case"QueryTable":case"DataValidation":case"Sorting":case"Schema":case"data":case"ConditionalFormatting":case"SmartTagType":case"SmartTags":case"ExcelWorkbook":case"WorkbookOptions":case"WorksheetOptions":if("/"===n[1]){if((o=l.pop())[0]!==n[3])throw new Error("Bad state: "+o.join("|"))}else"/"!==n[0].charAt(n[0].length-2)&&l.push([n[3],!0]);break;default:if(0==l.length&&"document"==n[3])return jc(a,r);if(0==l.length&&"UOF"==n[3])return jc(a,r);var J=!0;switch(l[l.length-1][0]){case"OfficeDocumentSettings":switch(n[3]){case"AllowPNG":case"RemovePersonalInformation":case"DownloadComponents":case"LocationOfComponents":case"Colors":case"Color":case"Index":case"RGB":case"PixelsPerInch":case"TargetScreenSize":case"ReadOnlyRecommended":break;default:J=!1}break;case"ComponentOptions":switch(n[3]){case"Toolbar":case"HideOfficeLogo":case"SpreadsheetAutoFit":case"Label":case"Caption":case"MaxHeight":case"MaxWidth":case"NextSheetNumber":break;default:J=!1}break;case"ExcelWorkbook":switch(n[3]){case"Date1904":W.WBProps.date1904=!0;break;case"WindowHeight":case"WindowWidth":case"WindowTopX":case"WindowTopY":case"TabRatio":case"ProtectStructure":case"ProtectWindows":case"ActiveSheet":case"DisplayInkNotes":case"FirstVisibleSheet":case"SupBook":case"SheetName":case"SheetIndex":case"SheetIndexFirst":case"SheetIndexLast":case"Dll":case"AcceptLabelsInFormulas":case"DoNotSaveLinkValues":case"Iteration":case"MaxIterations":case"MaxChange":case"Path":case"Xct":case"Count":case"SelectedSheets":case"Calculation":case"Uncalced":case"StartupPrompt":case"Crn":case"ExternName":case"Formula":case"ColFirst":case"ColLast":case"WantAdvise":case"Boolean":case"Error":case"Text":case"OLE":case"NoAutoRecover":case"PublishObjects":case"DoNotCalculateBeforeSave":case"Number":case"RefModeR1C1":case"EmbedSaveSmartTags":break;default:J=!1}break;case"WorkbookOptions":switch(n[3]){case"OWCVersion":case"Height":case"Width":break;default:J=!1}break;case"WorksheetOptions":switch(n[3]){case"Visible":if("/>"!==n[0].slice(-2))if("/"===n[1])switch(a.slice(O,n.index)){case"SheetHidden":X.Hidden=1;break;case"SheetVeryHidden":X.Hidden=2}else O=n.index+n[0].length;break;case"Header":g["!margins"]||Vo(g["!margins"]={},"xlml"),g["!margins"].header=pe(n[0]).Margin;break;case"Footer":g["!margins"]||Vo(g["!margins"]={},"xlml"),g["!margins"].footer=pe(n[0]).Margin;break;case"PageMargins":var q=pe(n[0]);g["!margins"]||Vo(g["!margins"]={},"xlml"),q.Top&&(g["!margins"].top=q.Top),q.Left&&(g["!margins"].left=q.Left),q.Right&&(g["!margins"].right=q.Right),q.Bottom&&(g["!margins"].bottom=q.Bottom);break;case"DisplayRightToLeft":W.Views||(W.Views=[]),W.Views[0]||(W.Views[0]={}),W.Views[0].RTL=!0;break;case"Unsynced":case"Print":case"Panes":case"Scale":case"Pane":case"Number":case"Layout":case"PageSetup":case"Selected":case"ProtectObjects":case"EnableSelection":case"ProtectScenarios":case"ValidPrinterInfo":case"HorizontalResolution":case"VerticalResolution":case"NumberofCopies":case"ActiveRow":case"ActiveCol":case"ActivePane":case"TopRowVisible":case"TopRowBottomPane":case"LeftColumnVisible":case"LeftColumnRightPane":case"FitToPage":case"RangeSelection":case"PaperSizeIndex":case"PageLayoutZoom":case"PageBreakZoom":case"FilterOn":case"DoNotDisplayGridlines":case"SplitHorizontal":case"SplitVertical":case"FreezePanes":case"FrozenNoSplit":case"FitWidth":case"FitHeight":case"CommentsLayout":case"Zoom":case"LeftToRight":case"Gridlines":case"AllowSort":case"AllowFilter":case"AllowInsertRows":case"AllowDeleteRows":case"AllowInsertCols":case"AllowDeleteCols":case"AllowInsertHyperlinks":case"AllowFormatCells":case"AllowSizeCols":case"AllowSizeRows":case"NoSummaryRowsBelowDetail":case"TabColorIndex":case"DoNotDisplayHeadings":case"ShowPageLayoutZoom":case"NoSummaryColumnsRightDetail":case"BlackAndWhite":case"DoNotDisplayZeros":case"DisplayPageBreak":case"RowColHeadings":case"DoNotDisplayOutline":case"NoOrientation":case"AllowUsePivotTables":case"ZeroHeight":case"ViewableRange":case"Selection":case"ProtectContents":break;default:J=!1}break;case"PivotTable":case"PivotCache":switch(n[3]){case"ImmediateItemsOnDrop":case"ShowPageMultipleItemLabel":case"CompactRowIndent":case"Location":case"PivotField":case"Orientation":case"LayoutForm":case"LayoutSubtotalLocation":case"LayoutCompactRow":case"Position":case"PivotItem":case"DataType":case"DataField":case"SourceName":case"ParentField":case"PTLineItems":case"PTLineItem":case"CountOfSameItems":case"Item":case"ItemType":case"PTSource":case"CacheIndex":case"ConsolidationReference":case"FileName":case"Reference":case"NoColumnGrand":case"NoRowGrand":case"BlankLineAfterItems":case"Hidden":case"Subtotal":case"BaseField":case"MapChildItems":case"Function":case"RefreshOnFileOpen":case"PrintSetTitles":case"MergeLabels":case"DefaultVersion":case"RefreshName":case"RefreshDate":case"RefreshDateCopy":case"VersionLastRefresh":case"VersionLastUpdate":case"VersionUpdateableMin":case"VersionRefreshableMin":case"Calculation":break;default:J=!1}break;case"PageBreaks":switch(n[3]){case"ColBreaks":case"ColBreak":case"RowBreaks":case"RowBreak":case"ColStart":case"ColEnd":case"RowEnd":break;default:J=!1}break;case"AutoFilter":switch(n[3]){case"AutoFilterColumn":case"AutoFilterCondition":case"AutoFilterAnd":case"AutoFilterOr":break;default:J=!1}break;case"QueryTable":switch(n[3]){case"Id":case"AutoFormatFont":case"AutoFormatPattern":case"QuerySource":case"QueryType":case"EnableRedirections":case"RefreshedInXl9":case"URLString":case"HTMLTables":case"Connection":case"CommandText":case"RefreshInfo":case"NoTitles":case"NextId":case"ColumnInfo":case"OverwriteCells":case"DoNotPromptForFile":case"TextWizardSettings":case"Source":case"Number":case"Decimal":case"ThousandSeparator":case"TrailingMinusNumbers":case"FormatSettings":case"FieldType":case"Delimiters":case"Tab":case"Comma":case"AutoFormatName":case"VersionLastEdit":case"VersionLastRefresh":break;default:J=!1}break;case"Sorting":case"ConditionalFormatting":case"DataValidation":switch(n[3]){case"Range":case"Type":case"Min":case"Max":case"Sort":case"Descending":case"Order":case"CaseSensitive":case"Value":case"ErrorStyle":case"ErrorMessage":case"ErrorTitle":case"CellRangeList":case"InputMessage":case"InputTitle":case"ComboHide":case"InputHide":case"Condition":case"Qualifier":case"UseBlank":case"Value1":case"Value2":case"Format":break;default:J=!1}break;case"MapInfo":case"Schema":case"data":switch(n[3]){case"Map":case"Entry":case"Range":case"XPath":case"Field":case"XSDType":case"FilterOn":case"Aggregate":case"ElementType":case"AttributeType":break;case"schema":case"element":case"complexType":case"datatype":case"all":case"attribute":case"extends":case"row":break;default:J=!1}break;case"SmartTags":break;default:J=!1}if(J)break;if(!l[l.length-1][1])throw"Unrecognized tag: "+n[3]+"|"+l.join("|");if("CustomDocumentProperties"===l[l.length-1][0]){if("/>"===n[0].slice(-2))break;"/"===n[1]?function(e,t,r,n){var a=n;switch((r[0].match(/dt:dt="([\w.]+)"/)||["",""])[1]){case"boolean":a=Re(n);break;case"i2":case"int":a=parseInt(n,10);break;case"r4":case"float":a=parseFloat(n);break;case"date":case"dateTime.tz":a=oe(n);break;case"i8":case"string":case"fixed":case"uuid":case"bin.base64":break;default:throw new Error("bad custprop:"+r[0])}e[we(t)]=a}(R,n[3],F,a.slice(O,n.index)):O=(F=n).index+n[0].length;break}if(r.WTF)throw"Unrecognized tag: "+n[3]+"|"+l.join("|")}e={};return r.bookSheets||r.bookProps||(e.Sheets=p),e.SheetNames=m,e.Workbook=W,e.SSF=ue.get_table(),e.Props=I,e.Custprops=R,e}function sc(e,t){switch(uf(t=t||{}),t.type||"base64"){case"base64":return ac(Y.decode(e),t);case"binary":case"buffer":case"file":return ac(e,t);case"array":return ac(o(e),t)}}function ic(e,t){var r,n,a,s,i,o,l,c=[];return e.Props&&c.push((r=e.Props,n=t,a=[],de(hn).map(function(e){for(var t=0;t<qr.length;++t)if(qr[t][1]==e)return qr[t];for(t=0;t<an.length;++t)if(an[t][1]==e)return an[t];throw e}).forEach(function(e){var t;null!=r[e[1]]&&(t=(n&&n.Props&&null!=n.Props[e[1]]?n.Props:r)[e[1]],"number"==typeof(t="date"===e[2]?new Date(t).toISOString().replace(/\.\d*Z/,"Z"):t)?t=String(t):!0===t||!1===t?t=t?"1":"0":t instanceof Date&&(t=new Date(t).toISOString().replace(/\.\d*Z/,"")),a.push(Xe(hn[e[1]]||e[1],t)))}),Ge("DocumentProperties",a.join(""),{xmlns:Ke.o}))),e.Custprops&&c.push((s=e.Props,i=e.Custprops,o=["Worksheets","SheetNames"],e="CustomDocumentProperties",l=[],s&&de(s).forEach(function(e){if(s.hasOwnProperty(e)){for(var t=0;t<qr.length;++t)if(e==qr[t][1])return;for(t=0;t<an.length;++t)if(e==an[t][1])return;for(t=0;t<o.length;++t)if(e==o[t])return;var r="string",n="number"==typeof(n=s[e])?(r="float",String(n)):!0===n||!1===n?(r="boolean",n?"1":"0"):String(n);l.push(Ge(Ce(e),n,{"dt:dt":r}))}}),i&&de(i).forEach(function(e){var t,r;i.hasOwnProperty(e)&&(s&&s.hasOwnProperty(e)||(t="string",r="number"==typeof(r=i[e])?(t="float",String(r)):!0===r||!1===r?(t="boolean",r?"1":"0"):r instanceof Date?(t="dateTime.tz",r.toISOString()):String(r),l.push(Ge(Ce(e),r,{"dt:dt":t}))))}),"<"+e+' xmlns="'+Ke.o+'">'+l.join("")+"</"+e+">")),c.join("")}function oc(e){return Ge("NamedRange",null,{"ss:Name":e.Name,"ss:RefersTo":"="+$i(e.Ref,{r:0,c:0})})}function lc(e,t,r,n,a,s,i){if(!e||null==e.v&&null==e.f)return"";var o={};if(e.f&&(o["ss:Formula"]="="+ye($i(e.f,i))),e.F&&e.F.slice(0,t.length)==t&&(t=Xt(e.F.slice(t.length+1)),o["ss:ArrayRange"]="RC:R"+(t.r==i.r?"":"["+(t.r-i.r)+"]")+"C"+(t.c==i.c?"":"["+(t.c-i.c)+"]")),e.l&&e.l.Target&&(o["ss:HRef"]=ye(e.l.Target),e.l.Tooltip&&(o["x:HRefScreenTip"]=ye(e.l.Tooltip))),r["!merges"])for(var l=r["!merges"],c=0;c!=l.length;++c)l[c].s.c==i.c&&l[c].s.r==i.r&&(l[c].e.c>l[c].s.c&&(o["ss:MergeAcross"]=l[c].e.c-l[c].s.c),l[c].e.r>l[c].s.r&&(o["ss:MergeDown"]=l[c].e.r-l[c].s.r));var f="",h="";switch(e.t){case"z":return"";case"n":f="Number",h=String(e.v);break;case"b":f="Boolean",h=e.v?"1":"0";break;case"e":f="Error",h=Sr[e.v];break;case"d":f="DateTime",h=new Date(e.v).toISOString(),null==e.z&&(e.z=e.z||ue._table[14]);break;case"s":f="String",h=((e.v||"")+"").replace(Se,function(e){return Ee[e]}).replace(Be,function(e){return"&#x"+e.charCodeAt(0).toString(16).toUpperCase()+";"})}n=Wo(n.cellXfs,e,n);o["ss:StyleID"]="s"+(21+n),o["ss:Index"]=i.c+1;n='<Data ss:Type="'+f+'">'+(null!=e.v?h:"")+"</Data>";return 0<(e.c||[]).length&&(n+=e.c.map(function(e){var t=Ge("ss:Data",Ie(e.t||""),{xmlns:"http://www.w3.org/TR/REC-html40"});return Ge("Comment",t,{"ss:Author":e.a})}).join("")),Ge("Cell",n,o)}function cc(e,t){if(!e["!ref"])return"";var r=Yt(e["!ref"]),n=e["!merges"]||[],a=0,s=[];e["!cols"]&&e["!cols"].forEach(function(e,t){Zs(e);var r=!!e.width,n=zo(t,e),t={"ss:Index":t+1};r&&(t["ss:Width"]=js(n.width)),e.hidden&&(t["ss:Hidden"]="1"),s.push(Ge("Column",null,t))});for(var i,o,l=Array.isArray(e),c=r.s.r;c<=r.e.r;++c){for(var f=[(i=c,o=(e["!rows"]||[])[c],i='<Row ss:Index="'+(i+1)+'"',o&&(o.hpt&&!o.hpx&&(o.hpx=qs(o.hpt)),o.hpx&&(i+=' ss:AutoFitHeight="0" ss:Height="'+o.hpx+'"'),o.hidden&&(i+=' ss:Hidden="1"')),i+">")],h=r.s.c;h<=r.e.c;++h){for(var u,d,p,m=!1,a=0;a!=n.length;++a)if(!(n[a].s.c>h||n[a].s.r>c||n[a].e.c<h||n[a].e.r<c)){n[a].s.c==h&&n[a].s.r==c||(m=!0);break}m||(d=jt(u={r:c,c:h}),p=l?(e[c]||[])[h]:e[d],f.push(lc(p,d,e,t,0,0,u)))}f.push("</Row>"),2<f.length&&s.push(f.join(""))}return s.join("")}function fc(e,t,r){var n=[],a=r.SheetNames[e],s=r.Sheets[a],a=s?function(e,t,r){if(!e)return"";if(!((r||{}).Workbook||{}).Names)return"";for(var n=r.Workbook.Names,a=[],s=0;s<n.length;++s){var i=n[s];i.Sheet==t&&(i.Name.match(/^_xlfn\./)||a.push(oc(i)))}return a.join("")}(s,e,r):"";return 0<a.length&&n.push("<Names>"+a+"</Names>"),0<(a=s?cc(s,t):"").length&&n.push("<Table>"+a+"</Table>"),n.push(function(t,e,r){if(!t)return"";var n=[];if(t["!margins"]&&(n.push("<PageSetup>"),t["!margins"].header&&n.push(Ge("Header",null,{"x:Margin":t["!margins"].header})),t["!margins"].footer&&n.push(Ge("Footer",null,{"x:Margin":t["!margins"].footer})),n.push(Ge("PageMargins",null,{"x:Bottom":t["!margins"].bottom||"0.75","x:Left":t["!margins"].left||"0.7","x:Right":t["!margins"].right||"0.7","x:Top":t["!margins"].top||"0.75"})),n.push("</PageSetup>")),r&&r.Workbook&&r.Workbook.Sheets&&r.Workbook.Sheets[e])if(r.Workbook.Sheets[e].Hidden)n.push(Ge("Visible",1==r.Workbook.Sheets[e].Hidden?"SheetHidden":"SheetVeryHidden",{}));else{for(var a=0;a<e&&(!r.Workbook.Sheets[a]||r.Workbook.Sheets[a].Hidden);++a);a==e&&n.push("<Selected/>")}return((((r||{}).Workbook||{}).Views||[])[0]||{}).RTL&&n.push("<DisplayRightToLeft/>"),t["!protect"]&&(n.push(Xe("ProtectContents","True")),t["!protect"].objects&&n.push(Xe("ProtectObjects","True")),t["!protect"].scenarios&&n.push(Xe("ProtectScenarios","True")),null==t["!protect"].selectLockedCells||t["!protect"].selectLockedCells?null==t["!protect"].selectUnlockedCells||t["!protect"].selectUnlockedCells||n.push(Xe("EnableSelection","UnlockedCells")):n.push(Xe("EnableSelection","NoSelection")),[["formatCells","AllowFormatCells"],["formatColumns","AllowSizeCols"],["formatRows","AllowSizeRows"],["insertColumns","AllowInsertCols"],["insertRows","AllowInsertRows"],["insertHyperlinks","AllowInsertHyperlinks"],["deleteColumns","AllowDeleteCols"],["deleteRows","AllowDeleteRows"],["sort","AllowSort"],["autoFilter","AllowFilter"],["pivotTables","AllowUsePivotTables"]].forEach(function(e){t["!protect"][e[0]]&&n.push("<"+e[1]+"/>")})),0==n.length?"":Ge("WorksheetOptions",n.join(""),{xmlns:Ke.x})}(s,e,r)),n.join("")}function hc(e,t){t=t||{},e.SSF||(e.SSF=ue.get_table()),e.SSF&&(ne(ue),ue.load_table(e.SSF),t.revssf=_(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF,t.cellXfs=[],Wo(t.cellXfs,{},{revssf:{General:0}}));var r=[];r.push(ic(e,t)),r.push(""),r.push(""),r.push("");for(var n,a=0;a<e.SheetNames.length;++a)r.push(Ge("Worksheet",fc(a,t,e),{"ss:Name":ye(e.SheetNames[a])}));return r[2]=(n=['<Style ss:ID="Default" ss:Name="Normal"><NumberFormat/></Style>'],t.cellXfs.forEach(function(e,t){var r=[];r.push(Ge("NumberFormat",null,{"ss:Format":ye(ue._table[e.numFmtId])})),n.push(Ge("Style",r.join(""),{"ss:ID":"s"+(21+t)}))}),Ge("Styles",n.join(""))),r[3]=function(e){if(!((e||{}).Workbook||{}).Names)return"";for(var t=e.Workbook.Names,r=[],n=0;n<t.length;++n){var a=t[n];null==a.Sheet&&(a.Name.match(/^_xlfn\./)||r.push(oc(a)))}return Ge("Names",r.join(""))}(e),z+Ge("Workbook",r.join(""),{xmlns:Ke.ss,"xmlns:o":Ke.o,"xmlns:x":Ke.x,"xmlns:ss":Ke.ss,"xmlns:dt":Ke.dt,"xmlns:html":Ke.html})}function uc(e){var t={},r=e.content;if(r.l=28,t.AnsiUserType=r.read_shift(0,"lpstr-ansi"),t.AnsiClipboardFormat=Cr(r,1),r.length-r.l<=4)return t;e=r.read_shift(4);return 0==e||40<e?t:(r.l-=4,t.Reserved1=r.read_shift(0,"lpstr-ansi"),r.length-r.l<=4||1907505652!==(e=r.read_shift(4))?t:(t.UnicodeClipboardFormat=Cr(r,2),0==(e=r.read_shift(4))||40<e?t:(r.l-=4,void(t.Reserved2=r.read_shift(0,"lpwstr")))))}function dc(e,t,r){if("z"!==e.t&&e.XF){var n=0;try{n=e.z||e.XF.numFmtId||0,t.cellNF&&(e.z=ue._table[n])}catch(e){if(t.WTF)throw e}if(!t||!1!==t.cellText)try{"e"===e.t?e.w=e.w||Sr[e.v]:0===n||"General"==n?"n"===e.t?(0|e.v)===e.v?e.w=ue._general_int(e.v):e.w=ue._general_num(e.v):e.w=ue._general(e.v):e.w=ue.format(n,e.v,{date1904:!!r})}catch(e){if(t.WTF)throw e}t.cellDates&&n&&"n"==e.t&&ue.is_date(ue._table[n]||String(n))&&((n=ue.parse_date_code(e.v))&&(e.t="d",e.v=new Date(n.y,n.m-1,n.d,n.H,n.M,n.S,n.u)))}}function pc(e,t,r){return{v:e,ixfe:t,t:r}}function mc(e,t){var r={opts:{}},n={};null!=fe&&null==t.dense&&(t.dense=fe);function a(e,t,r){if(!(1<P)&&(w=!(r.sheetRows&&e.r>=r.sheetRows)&&w)){var n,a,s;if(r.cellStyles&&t.XF&&t.XF.data&&(a=r,(s=(n=t).XF.data)&&s.patternType&&a&&a.cellStyles&&(n.s={},n.s.patternType=s.patternType,(a=Us(B(s.icvFore)))&&(n.s.fgColor={rgb:a}),(a=Us(B(s.icvBack)))&&(n.s.bgColor={rgb:a}))),delete t.ixfe,delete t.XF,b=jt(o=e),u&&u.s&&u.e||(u={s:{r:0,c:0},e:{r:0,c:0}}),e.r<u.s.r&&(u.s.r=e.r),e.c<u.s.c&&(u.s.c=e.c),e.r+1>u.e.r&&(u.e.r=e.r+1),e.c+1>u.e.c&&(u.e.c=e.c+1),r.cellFormula&&t.f)for(var i=0;i<E.length;++i)if(!(E[i][0].s.c>e.c||E[i][0].s.r>e.r||E[i][0].e.c<e.c||E[i][0].e.r<e.r)){t.F=$t(E[i][0]),E[i][0].s.c==e.c&&E[i][0].s.r==e.r||delete t.f,t.f&&(t.f=""+Co(E[i][1],0,e,O,T));break}r.dense?(f[e.r]||(f[e.r]=[]),f[e.r][e.c]=t):f[b]=t}}var o,s,i,l,c,f=t.dense?[]:{},h={},u={},d=null,p=[],m="",g={},b="",v={},E=[],w=!0,S=[],_=[],y={Sheets:[],WBProps:{date1904:!1},Views:[{}]},C={},B=function(e){return!(e<8)&&e<64&&_[e-8]||Pr[e]},T={enc:!1,sbcch:0,snames:[],sharedf:v,arrayf:E,rrtabid:[],lastuser:"",biff:8,codepage:0,winlocked:0,cellStyles:!!t&&!!t.cellStyles,WTF:!!t&&!!t.wtf};t.password&&(T.password=t.password);var k=[],x=[],A=[],I=[],R=!1,O=[];O.SheetNames=T.snames,O.sharedf=T.sharedf,O.arrayf=T.arrayf,O.names=[],O.XTI=[];var F,D="",P=0,N=0,L=[],M=[];T.codepage=1200,ce(1200);for(var U=!1;e.l<e.length-1;){var H=e.l,z=e.read_shift(2);if(0===z&&"EOF"===D)break;var V=e.l===e.length?0:e.read_shift(2),W=Sc[z];if(W&&W.f){if(t.bookSheets&&"BoundSheet8"===D&&"BoundSheet8"!==W.n)break;if(D=W.n,2===W.r||12==W.r){var X=e.read_shift(2);if(V-=2,!T.enc&&X!==z&&((255&X)<<8|X>>8)!==z)throw new Error("rt mismatch: "+X+"!="+z);12==W.r&&(e.l+=10,V-=10)}var j,G,$,Y,K="EOF"===W.n?W.f(e,V,T):function(e,t,r,n){var a=r,s=[],i=t.slice(t.l,t.l+a);if(n&&n.enc&&n.enc.insitu)switch(e.n){case"BOF":case"FilePass":case"FileLock":case"InterfaceHdr":case"RRDInfo":case"RRDHead":case"UsrExcl":break;default:if(0===i.length)break;n.enc.insitu(i)}s.push(i),t.l+=a;for(var o=Sc[vt(t,t.l)],l=0;null!=o&&"Continue"===o.n.slice(0,8);)a=vt(t,t.l+2),l=t.l+4,"ContinueFrt"==o.n?l+=4:"ContinueFrt"==o.n.slice(0,11)&&(l+=12),s.push(t.slice(l,t.l+4+a)),t.l+=4+a,o=Sc[vt(t,t.l)];var c=he(s);At(c,0);var f=0;c.lens=[];for(var h=0;h<s.length;++h)c.lens.push(f),f+=s[h].length;return e.f(c,c.length,n)}(W,e,V,T),Z=W.n;if(0!=P||"BOF"==Z)switch(Z){case"Date1904":r.opts.Date1904=y.WBProps.date1904=K;break;case"WriteProtect":r.opts.WriteProtect=!0;break;case"FilePass":if(T.enc||(e.l=0),T.enc=K,!t.password)throw new Error("File is password-protected");if(null==K.valid)throw new Error("Encryption scheme unsupported");if(!K.valid)throw new Error("Password is incorrect");break;case"WriteAccess":T.lastuser=K;break;case"FileSharing":break;case"CodePage":switch(K){case 21010:K=1200;break;case 32768:K=1e4;break;case 32769:K=1252}ce(T.codepage=K),U=!0;break;case"RRTabId":T.rrtabid=K;break;case"WinProtect":T.winlocked=K;break;case"Template":case"BookBool":case"UsesELFs":case"MTRSettings":break;case"RefreshAll":case"CalcCount":case"CalcDelta":case"CalcIter":case"CalcMode":case"CalcPrecision":case"CalcSaveRecalc":r.opts[Z]=K;break;case"CalcRefMode":T.CalcRefMode=K;break;case"Uncalced":break;case"ForceFullCalculation":r.opts.FullCalc=K;break;case"WsBool":K.fDialog&&(f["!type"]="dialog");break;case"XF":S.push(K);break;case"ExtSST":case"BookExt":case"RichTextStream":case"BkHim":break;case"SupBook":O.push([K]),O[O.length-1].XTI=[];break;case"ExternName":O[O.length-1].push(K);break;case"Index":break;case"Lbl":F={Name:K.Name,Ref:Co(K.rgce,0,null,O,T)},0<K.itab&&(F.Sheet=K.itab-1),O.names.push(F),O[0]||(O[0]=[],O[0].XTI=[]),O[O.length-1].push(K),"_xlnm._FilterDatabase"==K.Name&&0<K.itab&&K.rgce&&K.rgce[0]&&K.rgce[0][0]&&"PtgArea3d"==K.rgce[0][0][0]&&(M[K.itab-1]={ref:$t(K.rgce[0][0][1][2])});break;case"ExternCount":T.ExternCount=K;break;case"ExternSheet":0==O.length&&(O[0]=[],O[0].XTI=[]),O[O.length-1].XTI=O[O.length-1].XTI.concat(K),O.XTI=O.XTI.concat(K);break;case"NameCmt":if(T.biff<8)break;null!=F&&(F.Comment=K[1]);break;case"Protect":f["!protect"]=K;break;case"Password":0!==K&&T.WTF&&console.error("Password verifier: "+K);break;case"Prot4Rev":case"Prot4RevPass":break;case"BoundSheet8":h[K.pos]=K,T.snames.push(K.name);break;case"EOF":if(--P)break;u.e&&(0<u.e.r&&0<u.e.c&&(u.e.r--,u.e.c--,f["!ref"]=$t(u),t.sheetRows&&t.sheetRows<=u.e.r&&(j=u.e.r,u.e.r=t.sheetRows-1,f["!fullref"]=f["!ref"],f["!ref"]=$t(u),u.e.r=j),u.e.r++,u.e.c++),0<k.length&&(f["!merges"]=k),0<x.length&&(f["!objects"]=x),0<A.length&&(f["!cols"]=A),0<I.length&&(f["!rows"]=I),y.Sheets.push(C)),""===m?g=f:n[m]=f,f=t.dense?[]:{};break;case"BOF":if(8===T.biff&&(T.biff={9:2,521:3,1033:4}[z]||{512:2,768:3,1024:4,1280:5,1536:8,2:2,7:2}[K.BIFFVer]||8),8==T.biff&&0==K.BIFFVer&&16==K.dt&&(T.biff=2),P++)break;var Q,w=!0,f=t.dense?[]:{};T.biff<8&&!U&&(U=!0,ce(T.codepage=t.codepage||1252)),T.biff<5?(""===m&&(m="Sheet1"),u={s:{r:0,c:0},e:{r:0,c:0}},Q={pos:e.l-V,name:m},h[Q.pos]=Q,T.snames.push(m)):m=(h[H]||{name:""}).name,32==K.dt&&(f["!type"]="chart"),64==K.dt&&(f["!type"]="macro"),k=[],x=[],T.arrayf=E=[],A=[],0,R=!(I=[]),C={Hidden:(h[H]||{hs:0}).hs,name:m};break;case"Number":case"BIFF2NUM":case"BIFF2INT":"chart"==f["!type"]&&(t.dense?(f[K.r]||[])[K.c]:f[jt({c:K.c,r:K.r})])&&++K.c,ee={ixfe:K.ixfe,XF:S[K.ixfe]||{},v:K.val,t:"n"},0<N&&(ee.z=L[ee.ixfe>>8&31]),dc(ee,t,r.opts.Date1904),a({c:K.c,r:K.r},ee,t);break;case"BoolErr":ee={ixfe:K.ixfe,XF:S[K.ixfe],v:K.val,t:K.t},0<N&&(ee.z=L[ee.ixfe>>8&31]),dc(ee,t,r.opts.Date1904),a({c:K.c,r:K.r},ee,t);break;case"RK":ee={ixfe:K.ixfe,XF:S[K.ixfe],v:K.rknum,t:"n"},0<N&&(ee.z=L[ee.ixfe>>8&31]),dc(ee,t,r.opts.Date1904),a({c:K.c,r:K.r},ee,t);break;case"MulRk":for(var J=K.c;J<=K.C;++J){var q=K.rkrec[J-K.c][0],ee={ixfe:q,XF:S[q],v:K.rkrec[J-K.c][1],t:"n"};0<N&&(ee.z=L[ee.ixfe>>8&31]),dc(ee,t,r.opts.Date1904),a({c:J,r:K.r},ee,t)}break;case"Formula":if("String"==K.val){d=K;break}(ee=pc(K.val,K.cell.ixfe,K.tt)).XF=S[ee.ixfe],t.cellFormula&&(!((Q=K.formula)&&Q[0]&&Q[0][0]&&"PtgExp"==Q[0][0][0])||v[Y=jt({r:G=Q[0][0][1][0],c:$=Q[0][0][1][1]})]?ee.f=""+Co(K.formula,0,K.cell,O,T):ee.F=((t.dense?(f[G]||[])[$]:f[Y])||{}).F),0<N&&(ee.z=L[ee.ixfe>>8&31]),dc(ee,t,r.opts.Date1904),a(K.cell,ee,t),d=K;break;case"String":if(!d)throw new Error("String record expects Formula");(ee=pc(d.val=K,d.cell.ixfe,"s")).XF=S[ee.ixfe],t.cellFormula&&(ee.f=""+Co(d.formula,0,d.cell,O,T)),0<N&&(ee.z=L[ee.ixfe>>8&31]),dc(ee,t,r.opts.Date1904),a(d.cell,ee,t),d=null;break;case"Array":E.push(K);var te=jt(K[0].s),re=t.dense?(f[K[0].s.r]||[])[K[0].s.c]:f[te];if(t.cellFormula&&re){if(!d)break;if(!te||!re)break;re.f=""+Co(K[1],0,K[0],O,T),re.F=$t(K[0])}break;case"ShrFmla":if(!w)break;if(!t.cellFormula)break;if(b){if(!d)break;v[jt(d.cell)]=K[0],((re=t.dense?(f[d.cell.r]||[])[d.cell.c]:f[jt(d.cell)])||{}).f=""+Co(K[0],0,o,O,T)}break;case"LabelSst":(ee=pc(p[K.isst].t,K.ixfe,"s")).XF=S[ee.ixfe],0<N&&(ee.z=L[ee.ixfe>>8&31]),dc(ee,t,r.opts.Date1904),a({c:K.c,r:K.r},ee,t);break;case"Blank":t.sheetStubs&&(ee={ixfe:K.ixfe,XF:S[K.ixfe],t:"z"},0<N&&(ee.z=L[ee.ixfe>>8&31]),dc(ee,t,r.opts.Date1904),a({c:K.c,r:K.r},ee,t));break;case"MulBlank":if(t.sheetStubs)for(var ne=K.c;ne<=K.C;++ne){var ae=K.ixfe[ne-K.c];ee={ixfe:ae,XF:S[ae],t:"z"},0<N&&(ee.z=L[ee.ixfe>>8&31]),dc(ee,t,r.opts.Date1904),a({c:ne,r:K.r},ee,t)}break;case"RString":case"Label":case"BIFF2STR":(ee=pc(K.val,K.ixfe,"s")).XF=S[ee.ixfe],0<N&&(ee.z=L[ee.ixfe>>8&31]),dc(ee,t,r.opts.Date1904),a({c:K.c,r:K.r},ee,t);break;case"Dimensions":1===P&&(u=K);break;case"SST":p=K;break;case"Format":if(4==T.biff){L[N++]=K[1];for(var se=0;se<N+163&&ue._table[se]!=K[1];++se);163<=se&&ue.load(K[1],N+163)}else ue.load(K[1],K[0]);break;case"BIFF2FORMAT":L[N++]=K;for(var ie=0;ie<N+163&&ue._table[ie]!=K;++ie);163<=ie&&ue.load(K,N+163);break;case"MergeCells":k=k.concat(K);break;case"Obj":x[K.cmo[0]]=T.lastobj=K;break;case"TxO":T.lastobj.TxO=K;break;case"ImData":T.lastobj.ImData=K;break;case"HLink":for(i=K[0].s.r;i<=K[0].e.r;++i)for(s=K[0].s.c;s<=K[0].e.c;++s)(re=t.dense?(f[i]||[])[s]:f[jt({c:s,r:i})])&&(re.l=K[1]);break;case"HLinkTooltip":for(i=K[0].s.r;i<=K[0].e.r;++i)for(s=K[0].s.c;s<=K[0].e.c;++s)(re=t.dense?(f[i]||[])[s]:f[jt({c:s,r:i})])&&re.l&&(re.l.Tooltip=K[1]);break;case"Note":if(T.biff<=5&&2<=T.biff)break;re=t.dense?(f[K[0].r]||[])[K[0].c]:f[jt(K[0])];var oe=x[K[2]];if(!re)break;re.c||(re.c=[]),te={a:K[1],t:oe.TxO.t},re.c.push(te);break;default:switch(W.n){case"ClrtClient":break;case"XFExt":S[K.ixfe],K.ext.forEach(function(e){e[0]});break;case"DefColWidth":0;break;case"DefaultRowHeight":K[1];break;case"ColInfo":if(!T.cellStyles)break;for(;K.e>=K.s;)A[K.e--]={width:K.w/256},R||(R=!0,Ks(K.w/256)),Zs(A[K.e+1]);break;case"Row":var le={};null!=K.level&&((I[K.r]=le).level=K.level),K.hidden&&((I[K.r]=le).hidden=!0),K.hpt&&((I[K.r]=le).hpt=K.hpt,le.hpx=qs(K.hpt));break;case"LeftMargin":case"RightMargin":case"TopMargin":case"BottomMargin":f["!margins"]||Vo(f["!margins"]={}),f["!margins"][Z.slice(0,-6).toLowerCase()]=K;break;case"Setup":f["!margins"]||Vo(f["!margins"]={}),f["!margins"].header=K.header,f["!margins"].footer=K.footer;break;case"Window2":K.RTL&&(y.Views[0].RTL=!0);break;case"Header":case"Footer":case"HCenter":case"VCenter":case"Pls":case"GCW":case"LHRecord":case"DBCell":case"EntExU2":case"SxView":case"Sxvd":case"SXVI":case"SXVDEx":case"SxIvd":case"SXString":case"Sync":case"Addin":case"SXDI":case"SXLI":case"SXEx":case"QsiSXTag":case"Selection":case"Feat":break;case"FeatHdr":case"FeatHdr11":break;case"Feature11":case"Feature12":case"List12":break;case"Country":l=K;break;case"RecalcId":case"DxGCol":break;case"Fbi":case"Fbi2":case"GelFrame":case"Font":case"XFCRC":case"Style":case"StyleExt":break;case"Palette":_=K;break;case"Theme":c=K;break;case"ScenarioProtect":case"ObjProtect":case"CondFmt12":case"Table":case"TableStyles":case"TableStyle":case"TableStyleElement":case"SXStreamID":case"SXVS":case"DConRef":case"SXAddl":case"DConBin":case"DConName":case"SXPI":case"SxFormat":case"SxSelect":case"SxRule":case"SxFilt":case"SxItm":case"SxDXF":case"ScenMan":case"DCon":case"CellWatch":case"PrintRowCol":case"PrintGrid":case"PrintSize":case"XCT":case"CRN":case"Scl":case"SheetExt":case"SheetExtOptional":case"ObNoMacros":case"ObProj":break;case"CodeName":m?C.CodeName=K||C.name:y.WBProps.CodeName=K||"ThisWorkbook";break;case"GUIDTypeLib":case"WOpt":case"PhoneticInfo":case"OleObjectSize":break;case"DXF":case"DXFN":case"DXFN12":case"DXFN12List":case"DXFN12NoCB":break;case"Dv":case"DVal":break;case"BRAI":case"Series":case"SeriesText":case"DConn":case"DbOrParamQry":case"DBQueryExt":case"OleDbConn":case"ExtString":case"IFmtRecord":break;case"CondFmt":case"CF":case"CF12":case"CFEx":case"Excel9File":case"Units":break;case"InterfaceHdr":case"Mms":case"InterfaceEnd":case"DSF":case"BuiltInFnGroupCount":break;case"Window1":case"HideObj":case"GridSet":case"Guts":case"UserBView":case"UserSViewBegin":case"UserSViewEnd":case"Pane":break;default:switch(W.n){case"Dat":case"Begin":case"End":case"StartBlock":case"EndBlock":case"Frame":case"Area":case"Axis":case"AxisLine":case"Tick":break;case"AxesUsed":case"CrtLayout12":case"CrtLayout12A":case"CrtLink":case"CrtLine":case"CrtMlFrt":case"CrtMlFrtContinue":break;case"LineFormat":case"AreaFormat":case"Chart":case"Chart3d":case"Chart3DBarShape":case"ChartFormat":case"ChartFrtInfo":break;case"PlotArea":case"PlotGrowth":break;case"SeriesList":case"SerParent":case"SerAuxTrend":break;case"DataFormat":case"SerToCrt":case"FontX":break;case"CatSerRange":case"AxcExt":case"SerFmt":case"ShtProps":break;case"DefaultText":case"Text":case"CatLab":case"DataLabExtContents":break;case"Legend":case"LegendException":break;case"Pie":case"Scatter":break;case"PieFormat":case"MarkerFormat":break;case"StartObject":case"EndObject":break;case"AlRuns":case"ObjectLink":case"SIIndex":break;case"AttachedLabel":case"YMult":break;case"Line":case"Bar":case"Surf":case"AxisParent":case"Pos":case"ValueRange":case"SXViewEx9":case"SXViewLink":case"PivotChartBits":case"SBaseRef":case"TextPropsStream":case"LnExt":case"MkrExt":case"CrtCoopt":break;case"Qsi":case"Qsif":case"Qsir":case"QsiSXTag":case"TxtQry":case"FilterMode":break;case"AutoFilter":case"AutoFilterInfo":case"AutoFilter12":case"DropDownObjIds":case"Sort":case"SortData":case"ShapePropsStream":break;case"MsoDrawing":case"MsoDrawingGroup":case"MsoDrawingSelection":break;case"WebPub":case"AutoWebPub":break;case"HeaderFooter":case"HFPicture":case"PLV":case"HorizontalPageBreaks":case"VerticalPageBreaks":break;case"Backup":case"CompressPictures":case"Compat12":break;case"Continue":case"ContinueFrt12":break;case"FrtFontList":case"FrtWrapper":break;default:switch(W.n){case"TabIdConf":case"Radar":case"RadarArea":case"DropBar":case"Intl":case"CoordList":case"SerAuxErrBar":break;case"BIFF2FONTCLR":case"BIFF2FMTCNT":case"BIFF2FONTXTRA":break;case"BIFF2XF":case"BIFF3XF":case"BIFF4XF":break;case"BIFF4FMTCNT":case"BIFF2ROW":case"BIFF2WINDOW2":break;case"SCENARIO":case"DConBin":case"PicF":case"DataLabExt":case"Lel":case"BopPop":case"BopPopCustom":case"RealTimeData":case"Name":break;case"LHNGraph":case"FnGroupName":case"AddMenu":case"LPr":break;case"ListObj":case"ListField":case"RRSort":case"BigName":break;case"ToolbarHdr":case"ToolbarEnd":case"DDEObjName":case"FRTArchId$":break;default:if(t.WTF)throw"Unrecognized Record "+W.n}}}}}else e.l+=V}return r.SheetNames=de(h).sort(function(e,t){return Number(e)-Number(t)}).map(function(e){return h[e].name}),t.bookSheets||(r.Sheets=n),r.Sheets&&M.forEach(function(e,t){r.Sheets[r.SheetNames[t]]["!autofilter"]=e}),r.Preamble=g,r.Strings=p,r.SSF=ue.get_table(),T.enc&&(r.Encryption=T.enc),c&&(r.Themes=c),r.Metadata={},void 0!==l&&(r.Metadata.Country=l),0<O.names.length&&(y.Names=O.names),r.Workbook=y,r}var gc={SI:"e0859ff2f94f6810ab9108002b27b3d9",DSI:"02d5cdd59c2e1b10939708002b2cf9ae",UDI:"05d5cdd59c2e1b10939708002b2cf9ae"};function bc(e,t){var r,n,a,s,i;if(uf(t=t||{}),c(),t.codepage&&l(t.codepage),e.FullPaths){if(ie.find(e,"/encryption"))throw new Error("File is password-protected");r=ie.find(e,"!CompObj"),a=ie.find(e,"/Workbook")||ie.find(e,"/Book")}else{switch(t.type){case"base64":e=J(Y.decode(e));break;case"binary":e=J(e);break;case"buffer":break;case"array":Array.isArray(e)||(e=Array.prototype.slice.call(e))}At(e,0),a={content:e}}if(r&&uc(r),t.bookProps&&!t.bookSheets)n={};else{var o=K?"buffer":"array";if(a&&a.content)n=mc(a.content,t);else if((a=ie.find(e,"PerfectOffice_MAIN"))&&a.content)n=Ya.to_workbook(a.content,(t.type=o,t));else{if(!(a=ie.find(e,"NativeContent_MAIN"))||!a.content)throw new Error("Cannot find Workbook stream");n=Ya.to_workbook(a.content,(t.type=o,t))}t.bookVBA&&e.FullPaths&&ie.find(e,"/_VBA_PROJECT_CUR/VBA/dir")&&(n.vbaraw=(s=e,i=ie.utils.cfb_new({root:"R"}),s.FullPaths.forEach(function(e,t){"/"!==e.slice(-1)&&e.match(/_VBA_PROJECT_CUR/)&&(e=e.replace(/^[^\/]*/,"R").replace(/\/_VBA_PROJECT_CUR\u0000*/,""),ie.utils.cfb_add(i,e,s.FileIndex[t].content))}),ie.write(i)))}o={};return e.FullPaths&&function(e,t,r){var n=ie.find(e,"!DocumentSummaryInformation");if(n&&0<n.size)try{var a,s=kn(n,kr,gc.DSI);for(a in s)t[a]=s[a]}catch(e){if(r.WTF)throw e}if((e=ie.find(e,"!SummaryInformation"))&&0<e.size)try{var i,o=kn(e,xr,gc.SI);for(i in o)null==t[i]&&(t[i]=o[i])}catch(e){if(r.WTF)throw e}t.HeadingPairs&&t.TitlesOfParts&&(sn(t.HeadingPairs,t.TitlesOfParts,t,r),delete t.HeadingPairs,delete t.TitlesOfParts)}(e,o,t),n.Props=n.Custprops=o,t.bookFiles&&(n.cfb=e),n}function vc(e,t){var r,n,a=t||{},t=ie.utils.cfb_new({root:"R"}),s="/Workbook";switch(a.bookType||"xls"){case"xls":a.bookType="biff8";case"xla":a.bookType||(a.bookType="xla");case"biff8":s="/Workbook",a.biff=8;break;case"biff5":s="/Book",a.biff=5;break;default:throw new Error("invalid type "+a.bookType+" for XLS CFB")}return ie.utils.cfb_add(t,s,Fc(e,a)),8==a.biff&&(e.Props||e.Custprops)&&function(e,t){var r,n=[],a=[],s=[],i=0;if(e.Props)for(r=de(e.Props),i=0;i<r.length;++i)(Ir.hasOwnProperty(r[i])?n:Rr.hasOwnProperty(r[i])?a:s).push([r[i],e.Props[r[i]]]);if(e.Custprops)for(r=de(e.Custprops),i=0;i<r.length;++i)(e.Props||{}).hasOwnProperty(r[i])||(Ir.hasOwnProperty(r[i])?n:Rr.hasOwnProperty(r[i])?a:s).push([r[i],e.Custprops[r[i]]]);for(var o=[],i=0;i<s.length;++i)-1<Bn.indexOf(s[i][0])||null!=s[i][1]&&o.push(s[i]);a.length&&ie.utils.cfb_add(t,"/SummaryInformation",xn(a,gc.SI,Rr,xr)),(n.length||o.length)&&ie.utils.cfb_add(t,"/DocumentSummaryInformation",xn(n,gc.DSI,Ir,kr,o.length?o:null,gc.UDI))}(e,t),8==a.biff&&e.vbaraw&&(r=t,(n=ie.read(e.vbaraw,{type:"string"==typeof e.vbaraw?"binary":"buffer"})).FullPaths.forEach(function(e,t){0==t||"/"!==(e=e.replace(/[^\/]*[\/]/,"/_VBA_PROJECT_CUR/")).slice(-1)&&ie.utils.cfb_add(r,e,n.FileIndex[t].content)})),t}var Ec={0:{n:"BrtRowHdr",f:function(e,t){var r={},n=e.l+t;r.r=e.read_shift(4),e.l+=4;var a=e.read_shift(2);return e.l+=1,t=e.read_shift(1),e.l=n,7&t&&(r.level=7&t),16&t&&(r.hidden=!0),32&t&&(r.hpt=a/20),r}},1:{n:"BrtCellBlank",f:function(e){return[ir(e)]}},2:{n:"BrtCellRk",f:function(e){return[ir(e),mr(e),"n"]}},3:{n:"BrtCellError",f:function(e){return[ir(e),e.read_shift(1),"e"]}},4:{n:"BrtCellBool",f:function(e){return[ir(e),e.read_shift(1),"b"]}},5:{n:"BrtCellReal",f:function(e){return[ir(e),Er(e),"n"]}},6:{n:"BrtCellSt",f:function(e){return[ir(e),tr(e),"str"]}},7:{n:"BrtCellIsst",f:function(e){return[ir(e),e.read_shift(4),"s"]}},8:{n:"BrtFmlaString",f:function(e,t,r){var n=e.l+t,a=ir(e);a.r=r["!row"];var s=[a,tr(e),"str"];return r.cellFormula?(e.l+=2,t=xo(e,n-e.l,r),s[3]=Co(t,0,a,r.supbooks,r)):e.l=n,s}},9:{n:"BrtFmlaNum",f:function(e,t,r){var n=e.l+t,a=ir(e);a.r=r["!row"];var s=[a,Er(e),"n"];return r.cellFormula?(e.l+=2,t=xo(e,n-e.l,r),s[3]=Co(t,0,a,r.supbooks,r)):e.l=n,s}},10:{n:"BrtFmlaBool",f:function(e,t,r){var n=e.l+t,a=ir(e);a.r=r["!row"];var s=[a,e.read_shift(1),"b"];return r.cellFormula?(e.l+=2,t=xo(e,n-e.l,r),s[3]=Co(t,0,a,r.supbooks,r)):e.l=n,s}},11:{n:"BrtFmlaError",f:function(e,t,r){var n=e.l+t,a=ir(e);a.r=r["!row"];var s=[a,e.read_shift(1),"e"];return r.cellFormula?(e.l+=2,t=xo(e,n-e.l,r),s[3]=Co(t,0,a,r.supbooks,r)):e.l=n,s}},16:{n:"BrtFRTArchID$",f:function(e,t){var r={};return e.read_shift(4),r.ArchID=e.read_shift(4),e.l+=t-8,r}},19:{n:"BrtSSTItem",f:nr},20:{n:"BrtPCDIMissing"},21:{n:"BrtPCDINumber"},22:{n:"BrtPCDIBoolean"},23:{n:"BrtPCDIError"},24:{n:"BrtPCDIString"},25:{n:"BrtPCDIDatetime"},26:{n:"BrtPCDIIndex"},27:{n:"BrtPCDIAMissing"},28:{n:"BrtPCDIANumber"},29:{n:"BrtPCDIABoolean"},30:{n:"BrtPCDIAError"},31:{n:"BrtPCDIAString"},32:{n:"BrtPCDIADatetime"},33:{n:"BrtPCRRecord"},34:{n:"BrtPCRRecordDt"},35:{n:"BrtFRTBegin"},36:{n:"BrtFRTEnd"},37:{n:"BrtACBegin"},38:{n:"BrtACEnd"},39:{n:"BrtName",f:function(e,t,r){var n=e.l+t;e.l+=4,e.l+=1;var a=e.read_shift(4),s=ur(e),t=Ao(e,0,r),r=fr(e);return e.l=n,t={Name:s,Ptg:t},a<268435455&&(t.Sheet=a),r&&(t.Comment=r),t}},40:{n:"BrtIndexRowBlock"},42:{n:"BrtIndexBlock"},43:{n:"BrtFont",f:function(e,t,r){var n={};n.sz=e.read_shift(2)/20;var a,s=(s=(a=e).read_shift(1),a.l++,{fItalic:2&s,fStrikeout:8&s,fOutline:16&s,fShadow:32&s,fCondense:64&s,fExtend:128&s});switch(s.fCondense&&(n.condense=1),s.fExtend&&(n.extend=1),s.fShadow&&(n.shadow=1),s.fOutline&&(n.outline=1),s.fStrikeout&&(n.strike=1),s.fItalic&&(n.italic=1),700===e.read_shift(2)&&(n.bold=1),e.read_shift(2)){case 1:n.vertAlign="superscript";break;case 2:n.vertAlign="subscript"}switch(0!=(s=e.read_shift(1))&&(n.underline=s),0<(s=e.read_shift(1))&&(n.family=s),0<(s=e.read_shift(1))&&(n.charset=s),e.l++,n.color=function(e){var t={},r=e.read_shift(1)>>>1,n=e.read_shift(1),a=e.read_shift(2,"i"),s=e.read_shift(1),i=e.read_shift(1),o=e.read_shift(1);switch(e.l++,r){case 0:t.auto=1;break;case 1:t.index=n;var l=Pr[n];l&&(t.rgb=Us(l));break;case 2:t.rgb=Us([s,i,o]);break;case 3:t.theme=n}return 0!=a&&(t.tint=0<a?a/32767:a/32768),t}(e),e.read_shift(1)){case 1:n.scheme="major";break;case 2:n.scheme="minor"}return n.name=tr(e),n}},44:{n:"BrtFmt",f:function(e,t){return[e.read_shift(2),tr(e)]}},45:{n:"BrtFill",f:di},46:{n:"BrtBorder",f:bi},47:{n:"BrtXF",f:function(e,t){var r=e.l+t,n=e.read_shift(2),t=e.read_shift(2);return e.l=r,{ixfe:n,numFmtId:t}}},48:{n:"BrtStyle"},49:{n:"BrtCellMeta"},50:{n:"BrtValueMeta"},51:{n:"BrtMdb"},52:{n:"BrtBeginFmd"},53:{n:"BrtEndFmd"},54:{n:"BrtBeginMdx"},55:{n:"BrtEndMdx"},56:{n:"BrtBeginMdxTuple"},57:{n:"BrtEndMdxTuple"},58:{n:"BrtMdxMbrIstr"},59:{n:"BrtStr"},60:{n:"BrtColInfo",f:ga},62:{n:"BrtCellRString"},63:{n:"BrtCalcChainItem$",f:function(e){var t={};t.i=e.read_shift(4);var r={};return r.r=e.read_shift(4),r.c=e.read_shift(4),t.r=jt(r),2&(e=e.read_shift(1))&&(t.l="1"),8&e&&(t.a="1"),t}},64:{n:"BrtDVal"},65:{n:"BrtSxvcellNum"},66:{n:"BrtSxvcellStr"},67:{n:"BrtSxvcellBool"},68:{n:"BrtSxvcellErr"},69:{n:"BrtSxvcellDate"},70:{n:"BrtSxvcellNil"},128:{n:"BrtFileVersion"},129:{n:"BrtBeginSheet"},130:{n:"BrtEndSheet"},131:{n:"BrtBeginBook",f:It,p:0},132:{n:"BrtEndBook"},133:{n:"BrtBeginWsViews"},134:{n:"BrtEndWsViews"},135:{n:"BrtBeginBookViews"},136:{n:"BrtEndBookViews"},137:{n:"BrtBeginWsView",f:function(e){var t=e.read_shift(2);return e.l+=28,{RTL:32&t}}},138:{n:"BrtEndWsView"},139:{n:"BrtBeginCsViews"},140:{n:"BrtEndCsViews"},141:{n:"BrtBeginCsView"},142:{n:"BrtEndCsView"},143:{n:"BrtBeginBundleShs"},144:{n:"BrtEndBundleShs"},145:{n:"BrtBeginSheetData"},146:{n:"BrtEndSheetData"},147:{n:"BrtWsProp",f:function(e,t){var r={};return e.l+=19,r.name=lr(e,t-19),r}},148:{n:"BrtWsDim",f:lo,p:16},151:{n:"BrtPane"},152:{n:"BrtSel"},153:{n:"BrtWbProp",f:function(e,t){var r={},n=e.read_shift(4);return r.defaultThemeVersion=e.read_shift(4),0<(e=8<t?tr(e):"").length&&(r.CodeName=e),r.autoCompressPictures=!!(65536&n),r.backupFile=!!(64&n),r.checkCompatibility=!!(4096&n),r.date1904=!!(1&n),r.filterPrivacy=!!(8&n),r.hidePivotFieldList=!!(1024&n),r.promptedSolutions=!!(16&n),r.publishItems=!!(2048&n),r.refreshAllConnections=!!(262144&n),r.saveExternalLinkValues=!!(128&n),r.showBorderUnselectedTables=!!(4&n),r.showInkAnnotation=!!(32&n),r.showObjects=["all","placeholders","none"][n>>13&3],r.showPivotChartFilter=!!(32768&n),r.updateLinks=["userSet","never","always"][n>>8&3],r}},154:{n:"BrtWbFactoid"},155:{n:"BrtFileRecover"},156:{n:"BrtBundleSh",f:function(e,t){var r={};return r.Hidden=e.read_shift(4),r.iTabID=e.read_shift(4),r.strRelID=dr(e,t-8),r.name=tr(e),r}},157:{n:"BrtCalcProp"},158:{n:"BrtBookView"},159:{n:"BrtBeginSst",f:function(e){return[e.read_shift(4),e.read_shift(4)]}},160:{n:"BrtEndSst"},161:{n:"BrtBeginAFilter",f:br},162:{n:"BrtEndAFilter"},163:{n:"BrtBeginFilterColumn"},164:{n:"BrtEndFilterColumn"},165:{n:"BrtBeginFilters"},166:{n:"BrtEndFilters"},167:{n:"BrtFilter"},168:{n:"BrtColorFilter"},169:{n:"BrtIconFilter"},170:{n:"BrtTop10Filter"},171:{n:"BrtDynamicFilter"},172:{n:"BrtBeginCustomFilters"},173:{n:"BrtEndCustomFilters"},174:{n:"BrtCustomFilter"},175:{n:"BrtAFilterDateGroupItem"},176:{n:"BrtMergeCell",f:y},177:{n:"BrtBeginMergeCells"},178:{n:"BrtEndMergeCells"},179:{n:"BrtBeginPivotCacheDef"},180:{n:"BrtEndPivotCacheDef"},181:{n:"BrtBeginPCDFields"},182:{n:"BrtEndPCDFields"},183:{n:"BrtBeginPCDField"},184:{n:"BrtEndPCDField"},185:{n:"BrtBeginPCDSource"},186:{n:"BrtEndPCDSource"},187:{n:"BrtBeginPCDSRange"},188:{n:"BrtEndPCDSRange"},189:{n:"BrtBeginPCDFAtbl"},190:{n:"BrtEndPCDFAtbl"},191:{n:"BrtBeginPCDIRun"},192:{n:"BrtEndPCDIRun"},193:{n:"BrtBeginPivotCacheRecords"},194:{n:"BrtEndPivotCacheRecords"},195:{n:"BrtBeginPCDHierarchies"},196:{n:"BrtEndPCDHierarchies"},197:{n:"BrtBeginPCDHierarchy"},198:{n:"BrtEndPCDHierarchy"},199:{n:"BrtBeginPCDHFieldsUsage"},200:{n:"BrtEndPCDHFieldsUsage"},201:{n:"BrtBeginExtConnection"},202:{n:"BrtEndExtConnection"},203:{n:"BrtBeginECDbProps"},204:{n:"BrtEndECDbProps"},205:{n:"BrtBeginECOlapProps"},206:{n:"BrtEndECOlapProps"},207:{n:"BrtBeginPCDSConsol"},208:{n:"BrtEndPCDSConsol"},209:{n:"BrtBeginPCDSCPages"},210:{n:"BrtEndPCDSCPages"},211:{n:"BrtBeginPCDSCPage"},212:{n:"BrtEndPCDSCPage"},213:{n:"BrtBeginPCDSCPItem"},214:{n:"BrtEndPCDSCPItem"},215:{n:"BrtBeginPCDSCSets"},216:{n:"BrtEndPCDSCSets"},217:{n:"BrtBeginPCDSCSet"},218:{n:"BrtEndPCDSCSet"},219:{n:"BrtBeginPCDFGroup"},220:{n:"BrtEndPCDFGroup"},221:{n:"BrtBeginPCDFGItems"},222:{n:"BrtEndPCDFGItems"},223:{n:"BrtBeginPCDFGRange"},224:{n:"BrtEndPCDFGRange"},225:{n:"BrtBeginPCDFGDiscrete"},226:{n:"BrtEndPCDFGDiscrete"},227:{n:"BrtBeginPCDSDTupleCache"},228:{n:"BrtEndPCDSDTupleCache"},229:{n:"BrtBeginPCDSDTCEntries"},230:{n:"BrtEndPCDSDTCEntries"},231:{n:"BrtBeginPCDSDTCEMembers"},232:{n:"BrtEndPCDSDTCEMembers"},233:{n:"BrtBeginPCDSDTCEMember"},234:{n:"BrtEndPCDSDTCEMember"},235:{n:"BrtBeginPCDSDTCQueries"},236:{n:"BrtEndPCDSDTCQueries"},237:{n:"BrtBeginPCDSDTCQuery"},238:{n:"BrtEndPCDSDTCQuery"},239:{n:"BrtBeginPCDSDTCSets"},240:{n:"BrtEndPCDSDTCSets"},241:{n:"BrtBeginPCDSDTCSet"},242:{n:"BrtEndPCDSDTCSet"},243:{n:"BrtBeginPCDCalcItems"},244:{n:"BrtEndPCDCalcItems"},245:{n:"BrtBeginPCDCalcItem"},246:{n:"BrtEndPCDCalcItem"},247:{n:"BrtBeginPRule"},248:{n:"BrtEndPRule"},249:{n:"BrtBeginPRFilters"},250:{n:"BrtEndPRFilters"},251:{n:"BrtBeginPRFilter"},252:{n:"BrtEndPRFilter"},253:{n:"BrtBeginPNames"},254:{n:"BrtEndPNames"},255:{n:"BrtBeginPName"},256:{n:"BrtEndPName"},257:{n:"BrtBeginPNPairs"},258:{n:"BrtEndPNPairs"},259:{n:"BrtBeginPNPair"},260:{n:"BrtEndPNPair"},261:{n:"BrtBeginECWebProps"},262:{n:"BrtEndECWebProps"},263:{n:"BrtBeginEcWpTables"},264:{n:"BrtEndECWPTables"},265:{n:"BrtBeginECParams"},266:{n:"BrtEndECParams"},267:{n:"BrtBeginECParam"},268:{n:"BrtEndECParam"},269:{n:"BrtBeginPCDKPIs"},270:{n:"BrtEndPCDKPIs"},271:{n:"BrtBeginPCDKPI"},272:{n:"BrtEndPCDKPI"},273:{n:"BrtBeginDims"},274:{n:"BrtEndDims"},275:{n:"BrtBeginDim"},276:{n:"BrtEndDim"},277:{n:"BrtIndexPartEnd"},278:{n:"BrtBeginStyleSheet"},279:{n:"BrtEndStyleSheet"},280:{n:"BrtBeginSXView"},281:{n:"BrtEndSXVI"},282:{n:"BrtBeginSXVI"},283:{n:"BrtBeginSXVIs"},284:{n:"BrtEndSXVIs"},285:{n:"BrtBeginSXVD"},286:{n:"BrtEndSXVD"},287:{n:"BrtBeginSXVDs"},288:{n:"BrtEndSXVDs"},289:{n:"BrtBeginSXPI"},290:{n:"BrtEndSXPI"},291:{n:"BrtBeginSXPIs"},292:{n:"BrtEndSXPIs"},293:{n:"BrtBeginSXDI"},294:{n:"BrtEndSXDI"},295:{n:"BrtBeginSXDIs"},296:{n:"BrtEndSXDIs"},297:{n:"BrtBeginSXLI"},298:{n:"BrtEndSXLI"},299:{n:"BrtBeginSXLIRws"},300:{n:"BrtEndSXLIRws"},301:{n:"BrtBeginSXLICols"},302:{n:"BrtEndSXLICols"},303:{n:"BrtBeginSXFormat"},304:{n:"BrtEndSXFormat"},305:{n:"BrtBeginSXFormats"},306:{n:"BrtEndSxFormats"},307:{n:"BrtBeginSxSelect"},308:{n:"BrtEndSxSelect"},309:{n:"BrtBeginISXVDRws"},310:{n:"BrtEndISXVDRws"},311:{n:"BrtBeginISXVDCols"},312:{n:"BrtEndISXVDCols"},313:{n:"BrtEndSXLocation"},314:{n:"BrtBeginSXLocation"},315:{n:"BrtEndSXView"},316:{n:"BrtBeginSXTHs"},317:{n:"BrtEndSXTHs"},318:{n:"BrtBeginSXTH"},319:{n:"BrtEndSXTH"},320:{n:"BrtBeginISXTHRws"},321:{n:"BrtEndISXTHRws"},322:{n:"BrtBeginISXTHCols"},323:{n:"BrtEndISXTHCols"},324:{n:"BrtBeginSXTDMPS"},325:{n:"BrtEndSXTDMPs"},326:{n:"BrtBeginSXTDMP"},327:{n:"BrtEndSXTDMP"},328:{n:"BrtBeginSXTHItems"},329:{n:"BrtEndSXTHItems"},330:{n:"BrtBeginSXTHItem"},331:{n:"BrtEndSXTHItem"},332:{n:"BrtBeginMetadata"},333:{n:"BrtEndMetadata"},334:{n:"BrtBeginEsmdtinfo"},335:{n:"BrtMdtinfo"},336:{n:"BrtEndEsmdtinfo"},337:{n:"BrtBeginEsmdb"},338:{n:"BrtEndEsmdb"},339:{n:"BrtBeginEsfmd"},340:{n:"BrtEndEsfmd"},341:{n:"BrtBeginSingleCells"},342:{n:"BrtEndSingleCells"},343:{n:"BrtBeginList"},344:{n:"BrtEndList"},345:{n:"BrtBeginListCols"},346:{n:"BrtEndListCols"},347:{n:"BrtBeginListCol"},348:{n:"BrtEndListCol"},349:{n:"BrtBeginListXmlCPr"},350:{n:"BrtEndListXmlCPr"},351:{n:"BrtListCCFmla"},352:{n:"BrtListTrFmla"},353:{n:"BrtBeginExternals"},354:{n:"BrtEndExternals"},355:{n:"BrtSupBookSrc",f:dr},357:{n:"BrtSupSelf"},358:{n:"BrtSupSame"},359:{n:"BrtSupTabs"},360:{n:"BrtBeginSupBook"},361:{n:"BrtPlaceholderName"},362:{n:"BrtExternSheet",f:ua},363:{n:"BrtExternTableStart"},364:{n:"BrtExternTableEnd"},366:{n:"BrtExternRowHdr"},367:{n:"BrtExternCellBlank"},368:{n:"BrtExternCellReal"},369:{n:"BrtExternCellBool"},370:{n:"BrtExternCellError"},371:{n:"BrtExternCellString"},372:{n:"BrtBeginEsmdx"},373:{n:"BrtEndEsmdx"},374:{n:"BrtBeginMdxSet"},375:{n:"BrtEndMdxSet"},376:{n:"BrtBeginMdxMbrProp"},377:{n:"BrtEndMdxMbrProp"},378:{n:"BrtBeginMdxKPI"},379:{n:"BrtEndMdxKPI"},380:{n:"BrtBeginEsstr"},381:{n:"BrtEndEsstr"},382:{n:"BrtBeginPRFItem"},383:{n:"BrtEndPRFItem"},384:{n:"BrtBeginPivotCacheIDs"},385:{n:"BrtEndPivotCacheIDs"},386:{n:"BrtBeginPivotCacheID"},387:{n:"BrtEndPivotCacheID"},388:{n:"BrtBeginISXVIs"},389:{n:"BrtEndISXVIs"},390:{n:"BrtBeginColInfos"},391:{n:"BrtEndColInfos"},392:{n:"BrtBeginRwBrk"},393:{n:"BrtEndRwBrk"},394:{n:"BrtBeginColBrk"},395:{n:"BrtEndColBrk"},396:{n:"BrtBrk"},397:{n:"BrtUserBookView"},398:{n:"BrtInfo"},399:{n:"BrtCUsr"},400:{n:"BrtUsr"},401:{n:"BrtBeginUsers"},403:{n:"BrtEOF"},404:{n:"BrtUCR"},405:{n:"BrtRRInsDel"},406:{n:"BrtRREndInsDel"},407:{n:"BrtRRMove"},408:{n:"BrtRREndMove"},409:{n:"BrtRRChgCell"},410:{n:"BrtRREndChgCell"},411:{n:"BrtRRHeader"},412:{n:"BrtRRUserView"},413:{n:"BrtRRRenSheet"},414:{n:"BrtRRInsertSh"},415:{n:"BrtRRDefName"},416:{n:"BrtRRNote"},417:{n:"BrtRRConflict"},418:{n:"BrtRRTQSIF"},419:{n:"BrtRRFormat"},420:{n:"BrtRREndFormat"},421:{n:"BrtRRAutoFmt"},422:{n:"BrtBeginUserShViews"},423:{n:"BrtBeginUserShView"},424:{n:"BrtEndUserShView"},425:{n:"BrtEndUserShViews"},426:{n:"BrtArrFmla",f:function(e,t,r){var n=e.l+t,a=gr(e),t=e.read_shift(1);return(a=[a])[2]=t,r.cellFormula?(r=ko(e,n-e.l,r),a[1]=r):e.l=n,a}},427:{n:"BrtShrFmla",f:function(e,t,r){var n=e.l+t,t=[br(e,16)];return r.cellFormula&&(r=Io(e,n-e.l,r),t[1]=r),e.l=n,t}},428:{n:"BrtTable"},429:{n:"BrtBeginExtConnections"},430:{n:"BrtEndExtConnections"},431:{n:"BrtBeginPCDCalcMems"},432:{n:"BrtEndPCDCalcMems"},433:{n:"BrtBeginPCDCalcMem"},434:{n:"BrtEndPCDCalcMem"},435:{n:"BrtBeginPCDHGLevels"},436:{n:"BrtEndPCDHGLevels"},437:{n:"BrtBeginPCDHGLevel"},438:{n:"BrtEndPCDHGLevel"},439:{n:"BrtBeginPCDHGLGroups"},440:{n:"BrtEndPCDHGLGroups"},441:{n:"BrtBeginPCDHGLGroup"},442:{n:"BrtEndPCDHGLGroup"},443:{n:"BrtBeginPCDHGLGMembers"},444:{n:"BrtEndPCDHGLGMembers"},445:{n:"BrtBeginPCDHGLGMember"},446:{n:"BrtEndPCDHGLGMember"},447:{n:"BrtBeginQSI"},448:{n:"BrtEndQSI"},449:{n:"BrtBeginQSIR"},450:{n:"BrtEndQSIR"},451:{n:"BrtBeginDeletedNames"},452:{n:"BrtEndDeletedNames"},453:{n:"BrtBeginDeletedName"},454:{n:"BrtEndDeletedName"},455:{n:"BrtBeginQSIFs"},456:{n:"BrtEndQSIFs"},457:{n:"BrtBeginQSIF"},458:{n:"BrtEndQSIF"},459:{n:"BrtBeginAutoSortScope"},460:{n:"BrtEndAutoSortScope"},461:{n:"BrtBeginConditionalFormatting"},462:{n:"BrtEndConditionalFormatting"},463:{n:"BrtBeginCFRule"},464:{n:"BrtEndCFRule"},465:{n:"BrtBeginIconSet"},466:{n:"BrtEndIconSet"},467:{n:"BrtBeginDatabar"},468:{n:"BrtEndDatabar"},469:{n:"BrtBeginColorScale"},470:{n:"BrtEndColorScale"},471:{n:"BrtCFVO"},472:{n:"BrtExternValueMeta"},473:{n:"BrtBeginColorPalette"},474:{n:"BrtEndColorPalette"},475:{n:"BrtIndexedColor"},476:{n:"BrtMargins",f:function(t){var r={};return vl.forEach(function(e){r[e]=Er(t)}),r}},477:{n:"BrtPrintOptions"},478:{n:"BrtPageSetup"},479:{n:"BrtBeginHeaderFooter"},480:{n:"BrtEndHeaderFooter"},481:{n:"BrtBeginSXCrtFormat"},482:{n:"BrtEndSXCrtFormat"},483:{n:"BrtBeginSXCrtFormats"},484:{n:"BrtEndSXCrtFormats"},485:{n:"BrtWsFmtInfo",f:function(){}},486:{n:"BrtBeginMgs"},487:{n:"BrtEndMGs"},488:{n:"BrtBeginMGMaps"},489:{n:"BrtEndMGMaps"},490:{n:"BrtBeginMG"},491:{n:"BrtEndMG"},492:{n:"BrtBeginMap"},493:{n:"BrtEndMap"},494:{n:"BrtHLink",f:function(e,t){var r=e.l+t,n=br(e,16),a=fr(e),s=tr(e),i=tr(e),t=tr(e);return e.l=r,t={rfx:n,relId:a,loc:s,display:t},i&&(t.Tooltip=i),t}},495:{n:"BrtBeginDCon"},496:{n:"BrtEndDCon"},497:{n:"BrtBeginDRefs"},498:{n:"BrtEndDRefs"},499:{n:"BrtDRef"},500:{n:"BrtBeginScenMan"},501:{n:"BrtEndScenMan"},502:{n:"BrtBeginSct"},503:{n:"BrtEndSct"},504:{n:"BrtSlc"},505:{n:"BrtBeginDXFs"},506:{n:"BrtEndDXFs"},507:{n:"BrtDXF"},508:{n:"BrtBeginTableStyles"},509:{n:"BrtEndTableStyles"},510:{n:"BrtBeginTableStyle"},511:{n:"BrtEndTableStyle"},512:{n:"BrtTableStyleElement"},513:{n:"BrtTableStyleClient"},514:{n:"BrtBeginVolDeps"},515:{n:"BrtEndVolDeps"},516:{n:"BrtBeginVolType"},517:{n:"BrtEndVolType"},518:{n:"BrtBeginVolMain"},519:{n:"BrtEndVolMain"},520:{n:"BrtBeginVolTopic"},521:{n:"BrtEndVolTopic"},522:{n:"BrtVolSubtopic"},523:{n:"BrtVolRef"},524:{n:"BrtVolNum"},525:{n:"BrtVolErr"},526:{n:"BrtVolStr"},527:{n:"BrtVolBool"},528:{n:"BrtBeginCalcChain$"},529:{n:"BrtEndCalcChain$"},530:{n:"BrtBeginSortState"},531:{n:"BrtEndSortState"},532:{n:"BrtBeginSortCond"},533:{n:"BrtEndSortCond"},534:{n:"BrtBookProtection"},535:{n:"BrtSheetProtection"},536:{n:"BrtRangeProtection"},537:{n:"BrtPhoneticInfo"},538:{n:"BrtBeginECTxtWiz"},539:{n:"BrtEndECTxtWiz"},540:{n:"BrtBeginECTWFldInfoLst"},541:{n:"BrtEndECTWFldInfoLst"},542:{n:"BrtBeginECTwFldInfo"},548:{n:"BrtFileSharing"},549:{n:"BrtOleSize"},550:{n:"BrtDrawing",f:dr},551:{n:"BrtLegacyDrawing"},552:{n:"BrtLegacyDrawingHF"},553:{n:"BrtWebOpt"},554:{n:"BrtBeginWebPubItems"},555:{n:"BrtEndWebPubItems"},556:{n:"BrtBeginWebPubItem"},557:{n:"BrtEndWebPubItem"},558:{n:"BrtBeginSXCondFmt"},559:{n:"BrtEndSXCondFmt"},560:{n:"BrtBeginSXCondFmts"},561:{n:"BrtEndSXCondFmts"},562:{n:"BrtBkHim"},564:{n:"BrtColor"},565:{n:"BrtBeginIndexedColors"},566:{n:"BrtEndIndexedColors"},569:{n:"BrtBeginMRUColors"},570:{n:"BrtEndMRUColors"},572:{n:"BrtMRUColor"},573:{n:"BrtBeginDVals"},574:{n:"BrtEndDVals"},577:{n:"BrtSupNameStart"},578:{n:"BrtSupNameValueStart"},579:{n:"BrtSupNameValueEnd"},580:{n:"BrtSupNameNum"},581:{n:"BrtSupNameErr"},582:{n:"BrtSupNameSt"},583:{n:"BrtSupNameNil"},584:{n:"BrtSupNameBool"},585:{n:"BrtSupNameFmla"},586:{n:"BrtSupNameBits"},587:{n:"BrtSupNameEnd"},588:{n:"BrtEndSupBook"},589:{n:"BrtCellSmartTagProperty"},590:{n:"BrtBeginCellSmartTag"},591:{n:"BrtEndCellSmartTag"},592:{n:"BrtBeginCellSmartTags"},593:{n:"BrtEndCellSmartTags"},594:{n:"BrtBeginSmartTags"},595:{n:"BrtEndSmartTags"},596:{n:"BrtSmartTagType"},597:{n:"BrtBeginSmartTagTypes"},598:{n:"BrtEndSmartTagTypes"},599:{n:"BrtBeginSXFilters"},600:{n:"BrtEndSXFilters"},601:{n:"BrtBeginSXFILTER"},602:{n:"BrtEndSXFilter"},603:{n:"BrtBeginFills"},604:{n:"BrtEndFills"},605:{n:"BrtBeginCellWatches"},606:{n:"BrtEndCellWatches"},607:{n:"BrtCellWatch"},608:{n:"BrtBeginCRErrs"},609:{n:"BrtEndCRErrs"},610:{n:"BrtCrashRecErr"},611:{n:"BrtBeginFonts"},612:{n:"BrtEndFonts"},613:{n:"BrtBeginBorders"},614:{n:"BrtEndBorders"},615:{n:"BrtBeginFmts"},616:{n:"BrtEndFmts"},617:{n:"BrtBeginCellXFs"},618:{n:"BrtEndCellXFs"},619:{n:"BrtBeginStyles"},620:{n:"BrtEndStyles"},625:{n:"BrtBigName"},626:{n:"BrtBeginCellStyleXFs"},627:{n:"BrtEndCellStyleXFs"},628:{n:"BrtBeginComments"},629:{n:"BrtEndComments"},630:{n:"BrtBeginCommentAuthors"},631:{n:"BrtEndCommentAuthors"},632:{n:"BrtCommentAuthor",f:Mi},633:{n:"BrtBeginCommentList"},634:{n:"BrtEndCommentList"},635:{n:"BrtBeginComment",f:function(e){var t={};t.iauthor=e.read_shift(4);var r=br(e,16);return t.rfx=r.s,t.ref=jt(r.s),e.l+=16,t}},636:{n:"BrtEndComment"},637:{n:"BrtCommentText",f:ar},638:{n:"BrtBeginOleObjects"},639:{n:"BrtOleObject"},640:{n:"BrtEndOleObjects"},641:{n:"BrtBeginSxrules"},642:{n:"BrtEndSxRules"},643:{n:"BrtBeginActiveXControls"},644:{n:"BrtActiveX"},645:{n:"BrtEndActiveXControls"},646:{n:"BrtBeginPCDSDTCEMembersSortBy"},648:{n:"BrtBeginCellIgnoreECs"},649:{n:"BrtCellIgnoreEC"},650:{n:"BrtEndCellIgnoreECs"},651:{n:"BrtCsProp",f:function(e,t){return e.l+=10,{name:tr(e)}}},652:{n:"BrtCsPageSetup"},653:{n:"BrtBeginUserCsViews"},654:{n:"BrtEndUserCsViews"},655:{n:"BrtBeginUserCsView"},656:{n:"BrtEndUserCsView"},657:{n:"BrtBeginPcdSFCIEntries"},658:{n:"BrtEndPCDSFCIEntries"},659:{n:"BrtPCDSFCIEntry"},660:{n:"BrtBeginListParts"},661:{n:"BrtListPart"},662:{n:"BrtEndListParts"},663:{n:"BrtSheetCalcProp"},664:{n:"BrtBeginFnGroup"},665:{n:"BrtFnGroup"},666:{n:"BrtEndFnGroup"},667:{n:"BrtSupAddin"},668:{n:"BrtSXTDMPOrder"},669:{n:"BrtCsProtection"},671:{n:"BrtBeginWsSortMap"},672:{n:"BrtEndWsSortMap"},673:{n:"BrtBeginRRSort"},674:{n:"BrtEndRRSort"},675:{n:"BrtRRSortItem"},676:{n:"BrtFileSharingIso"},677:{n:"BrtBookProtectionIso"},678:{n:"BrtSheetProtectionIso"},679:{n:"BrtCsProtectionIso"},680:{n:"BrtRangeProtectionIso"},1024:{n:"BrtRwDescent"},1025:{n:"BrtKnownFonts"},1026:{n:"BrtBeginSXTupleSet"},1027:{n:"BrtEndSXTupleSet"},1028:{n:"BrtBeginSXTupleSetHeader"},1029:{n:"BrtEndSXTupleSetHeader"},1030:{n:"BrtSXTupleSetHeaderItem"},1031:{n:"BrtBeginSXTupleSetData"},1032:{n:"BrtEndSXTupleSetData"},1033:{n:"BrtBeginSXTupleSetRow"},1034:{n:"BrtEndSXTupleSetRow"},1035:{n:"BrtSXTupleSetRowItem"},1036:{n:"BrtNameExt"},1037:{n:"BrtPCDH14"},1038:{n:"BrtBeginPCDCalcMem14"},1039:{n:"BrtEndPCDCalcMem14"},1040:{n:"BrtSXTH14"},1041:{n:"BrtBeginSparklineGroup"},1042:{n:"BrtEndSparklineGroup"},1043:{n:"BrtSparkline"},1044:{n:"BrtSXDI14"},1045:{n:"BrtWsFmtInfoEx14"},1046:{n:"BrtBeginConditionalFormatting14"},1047:{n:"BrtEndConditionalFormatting14"},1048:{n:"BrtBeginCFRule14"},1049:{n:"BrtEndCFRule14"},1050:{n:"BrtCFVO14"},1051:{n:"BrtBeginDatabar14"},1052:{n:"BrtBeginIconSet14"},1053:{n:"BrtDVal14"},1054:{n:"BrtBeginDVals14"},1055:{n:"BrtColor14"},1056:{n:"BrtBeginSparklines"},1057:{n:"BrtEndSparklines"},1058:{n:"BrtBeginSparklineGroups"},1059:{n:"BrtEndSparklineGroups"},1061:{n:"BrtSXVD14"},1062:{n:"BrtBeginSXView14"},1063:{n:"BrtEndSXView14"},1064:{n:"BrtBeginSXView16"},1065:{n:"BrtEndSXView16"},1066:{n:"BrtBeginPCD14"},1067:{n:"BrtEndPCD14"},1068:{n:"BrtBeginExtConn14"},1069:{n:"BrtEndExtConn14"},1070:{n:"BrtBeginSlicerCacheIDs"},1071:{n:"BrtEndSlicerCacheIDs"},1072:{n:"BrtBeginSlicerCacheID"},1073:{n:"BrtEndSlicerCacheID"},1075:{n:"BrtBeginSlicerCache"},1076:{n:"BrtEndSlicerCache"},1077:{n:"BrtBeginSlicerCacheDef"},1078:{n:"BrtEndSlicerCacheDef"},1079:{n:"BrtBeginSlicersEx"},1080:{n:"BrtEndSlicersEx"},1081:{n:"BrtBeginSlicerEx"},1082:{n:"BrtEndSlicerEx"},1083:{n:"BrtBeginSlicer"},1084:{n:"BrtEndSlicer"},1085:{n:"BrtSlicerCachePivotTables"},1086:{n:"BrtBeginSlicerCacheOlapImpl"},1087:{n:"BrtEndSlicerCacheOlapImpl"},1088:{n:"BrtBeginSlicerCacheLevelsData"},1089:{n:"BrtEndSlicerCacheLevelsData"},1090:{n:"BrtBeginSlicerCacheLevelData"},1091:{n:"BrtEndSlicerCacheLevelData"},1092:{n:"BrtBeginSlicerCacheSiRanges"},1093:{n:"BrtEndSlicerCacheSiRanges"},1094:{n:"BrtBeginSlicerCacheSiRange"},1095:{n:"BrtEndSlicerCacheSiRange"},1096:{n:"BrtSlicerCacheOlapItem"},1097:{n:"BrtBeginSlicerCacheSelections"},1098:{n:"BrtSlicerCacheSelection"},1099:{n:"BrtEndSlicerCacheSelections"},1100:{n:"BrtBeginSlicerCacheNative"},1101:{n:"BrtEndSlicerCacheNative"},1102:{n:"BrtSlicerCacheNativeItem"},1103:{n:"BrtRangeProtection14"},1104:{n:"BrtRangeProtectionIso14"},1105:{n:"BrtCellIgnoreEC14"},1111:{n:"BrtList14"},1112:{n:"BrtCFIcon"},1113:{n:"BrtBeginSlicerCachesPivotCacheIDs"},1114:{n:"BrtEndSlicerCachesPivotCacheIDs"},1115:{n:"BrtBeginSlicers"},1116:{n:"BrtEndSlicers"},1117:{n:"BrtWbProp14"},1118:{n:"BrtBeginSXEdit"},1119:{n:"BrtEndSXEdit"},1120:{n:"BrtBeginSXEdits"},1121:{n:"BrtEndSXEdits"},1122:{n:"BrtBeginSXChange"},1123:{n:"BrtEndSXChange"},1124:{n:"BrtBeginSXChanges"},1125:{n:"BrtEndSXChanges"},1126:{n:"BrtSXTupleItems"},1128:{n:"BrtBeginSlicerStyle"},1129:{n:"BrtEndSlicerStyle"},1130:{n:"BrtSlicerStyleElement"},1131:{n:"BrtBeginStyleSheetExt14"},1132:{n:"BrtEndStyleSheetExt14"},1133:{n:"BrtBeginSlicerCachesPivotCacheID"},1134:{n:"BrtEndSlicerCachesPivotCacheID"},1135:{n:"BrtBeginConditionalFormattings"},1136:{n:"BrtEndConditionalFormattings"},1137:{n:"BrtBeginPCDCalcMemExt"},1138:{n:"BrtEndPCDCalcMemExt"},1139:{n:"BrtBeginPCDCalcMemsExt"},1140:{n:"BrtEndPCDCalcMemsExt"},1141:{n:"BrtPCDField14"},1142:{n:"BrtBeginSlicerStyles"},1143:{n:"BrtEndSlicerStyles"},1144:{n:"BrtBeginSlicerStyleElements"},1145:{n:"BrtEndSlicerStyleElements"},1146:{n:"BrtCFRuleExt"},1147:{n:"BrtBeginSXCondFmt14"},1148:{n:"BrtEndSXCondFmt14"},1149:{n:"BrtBeginSXCondFmts14"},1150:{n:"BrtEndSXCondFmts14"},1152:{n:"BrtBeginSortCond14"},1153:{n:"BrtEndSortCond14"},1154:{n:"BrtEndDVals14"},1155:{n:"BrtEndIconSet14"},1156:{n:"BrtEndDatabar14"},1157:{n:"BrtBeginColorScale14"},1158:{n:"BrtEndColorScale14"},1159:{n:"BrtBeginSxrules14"},1160:{n:"BrtEndSxrules14"},1161:{n:"BrtBeginPRule14"},1162:{n:"BrtEndPRule14"},1163:{n:"BrtBeginPRFilters14"},1164:{n:"BrtEndPRFilters14"},1165:{n:"BrtBeginPRFilter14"},1166:{n:"BrtEndPRFilter14"},1167:{n:"BrtBeginPRFItem14"},1168:{n:"BrtEndPRFItem14"},1169:{n:"BrtBeginCellIgnoreECs14"},1170:{n:"BrtEndCellIgnoreECs14"},1171:{n:"BrtDxf14"},1172:{n:"BrtBeginDxF14s"},1173:{n:"BrtEndDxf14s"},1177:{n:"BrtFilter14"},1178:{n:"BrtBeginCustomFilters14"},1180:{n:"BrtCustomFilter14"},1181:{n:"BrtIconFilter14"},1182:{n:"BrtPivotCacheConnectionName"},2048:{n:"BrtBeginDecoupledPivotCacheIDs"},2049:{n:"BrtEndDecoupledPivotCacheIDs"},2050:{n:"BrtDecoupledPivotCacheID"},2051:{n:"BrtBeginPivotTableRefs"},2052:{n:"BrtEndPivotTableRefs"},2053:{n:"BrtPivotTableRef"},2054:{n:"BrtSlicerCacheBookPivotTables"},2055:{n:"BrtBeginSxvcells"},2056:{n:"BrtEndSxvcells"},2057:{n:"BrtBeginSxRow"},2058:{n:"BrtEndSxRow"},2060:{n:"BrtPcdCalcMem15"},2067:{n:"BrtQsi15"},2068:{n:"BrtBeginWebExtensions"},2069:{n:"BrtEndWebExtensions"},2070:{n:"BrtWebExtension"},2071:{n:"BrtAbsPath15"},2072:{n:"BrtBeginPivotTableUISettings"},2073:{n:"BrtEndPivotTableUISettings"},2075:{n:"BrtTableSlicerCacheIDs"},2076:{n:"BrtTableSlicerCacheID"},2077:{n:"BrtBeginTableSlicerCache"},2078:{n:"BrtEndTableSlicerCache"},2079:{n:"BrtSxFilter15"},2080:{n:"BrtBeginTimelineCachePivotCacheIDs"},2081:{n:"BrtEndTimelineCachePivotCacheIDs"},2082:{n:"BrtTimelineCachePivotCacheID"},2083:{n:"BrtBeginTimelineCacheIDs"},2084:{n:"BrtEndTimelineCacheIDs"},2085:{n:"BrtBeginTimelineCacheID"},2086:{n:"BrtEndTimelineCacheID"},2087:{n:"BrtBeginTimelinesEx"},2088:{n:"BrtEndTimelinesEx"},2089:{n:"BrtBeginTimelineEx"},2090:{n:"BrtEndTimelineEx"},2091:{n:"BrtWorkBookPr15"},2092:{n:"BrtPCDH15"},2093:{n:"BrtBeginTimelineStyle"},2094:{n:"BrtEndTimelineStyle"},2095:{n:"BrtTimelineStyleElement"},2096:{n:"BrtBeginTimelineStylesheetExt15"},2097:{n:"BrtEndTimelineStylesheetExt15"},2098:{n:"BrtBeginTimelineStyles"},2099:{n:"BrtEndTimelineStyles"},2100:{n:"BrtBeginTimelineStyleElements"},2101:{n:"BrtEndTimelineStyleElements"},2102:{n:"BrtDxf15"},2103:{n:"BrtBeginDxfs15"},2104:{n:"brtEndDxfs15"},2105:{n:"BrtSlicerCacheHideItemsWithNoData"},2106:{n:"BrtBeginItemUniqueNames"},2107:{n:"BrtEndItemUniqueNames"},2108:{n:"BrtItemUniqueName"},2109:{n:"BrtBeginExtConn15"},2110:{n:"BrtEndExtConn15"},2111:{n:"BrtBeginOledbPr15"},2112:{n:"BrtEndOledbPr15"},2113:{n:"BrtBeginDataFeedPr15"},2114:{n:"BrtEndDataFeedPr15"},2115:{n:"BrtTextPr15"},2116:{n:"BrtRangePr15"},2117:{n:"BrtDbCommand15"},2118:{n:"BrtBeginDbTables15"},2119:{n:"BrtEndDbTables15"},2120:{n:"BrtDbTable15"},2121:{n:"BrtBeginDataModel"},2122:{n:"BrtEndDataModel"},2123:{n:"BrtBeginModelTables"},2124:{n:"BrtEndModelTables"},2125:{n:"BrtModelTable"},2126:{n:"BrtBeginModelRelationships"},2127:{n:"BrtEndModelRelationships"},2128:{n:"BrtModelRelationship"},2129:{n:"BrtBeginECTxtWiz15"},2130:{n:"BrtEndECTxtWiz15"},2131:{n:"BrtBeginECTWFldInfoLst15"},2132:{n:"BrtEndECTWFldInfoLst15"},2133:{n:"BrtBeginECTWFldInfo15"},2134:{n:"BrtFieldListActiveItem"},2135:{n:"BrtPivotCacheIdVersion"},2136:{n:"BrtSXDI15"},2137:{n:"BrtBeginModelTimeGroupings"},2138:{n:"BrtEndModelTimeGroupings"},2139:{n:"BrtBeginModelTimeGrouping"},2140:{n:"BrtEndModelTimeGrouping"},2141:{n:"BrtModelTimeGroupingCalcCol"},3072:{n:"BrtUid"},3073:{n:"BrtRevisionPtr"},5095:{n:"BrtBeginCalcFeatures"},5096:{n:"BrtEndCalcFeatures"},5097:{n:"BrtCalcFeature"},65535:{n:""}},wc=w(Ec,"n"),Sc={3:{n:"BIFF2NUM",f:function(e){var t=Xn(e);return++e.l,e=Er(e),t.t="n",t.val=e,t}},4:{n:"BIFF2STR",f:function(e,t,r){var n=Xn(e);return++e.l,r=Mn(e,0,r),n.t="str",n.val=r,n}},6:{n:"Formula",f:Bo},9:{n:"BOF",f:ea},10:{n:"EOF",f:An},12:{n:"CalcCount",f:On},13:{n:"CalcMode",f:On},14:{n:"CalcPrecision",f:In},15:{n:"CalcRefMode",f:In},16:{n:"CalcDelta",f:Er},17:{n:"CalcIter",f:In},18:{n:"Protect",f:In},19:{n:"Password",f:On},20:{n:"Header",f:la},21:{n:"Footer",f:la},23:{n:"ExternSheet",f:ua},24:{n:"Lbl",f:ha},25:{n:"WinProtect",f:In},26:{n:"VerticalPageBreaks"},27:{n:"HorizontalPageBreaks"},28:{n:"Note",f:function(e,t,r){return function(e,t){if(!(t.biff<8)){var r=e.read_shift(2),n=e.read_shift(2),a=e.read_shift(2),s=e.read_shift(2),i=Mn(e,0,t);return t.biff<8&&e.read_shift(1),[{r:r,c:n},i,s,a]}}(e,r)}},29:{n:"Selection"},34:{n:"Date1904",f:In},35:{n:"ExternName",f:ca},38:{n:"LeftMargin",f:Er},39:{n:"RightMargin",f:Er},40:{n:"TopMargin",f:Er},41:{n:"BottomMargin",f:Er},42:{n:"PrintRowCol",f:In},43:{n:"PrintGrid",f:In},47:{n:"FilePass",f:function(e,t,r){var n={Type:8<=r.biff?e.read_shift(2):0};return n.Type?Ps(e,t-2,n):(t=e,r.biff,e=r,r=n,t={key:On(t),verificationBytes:On(t)},e.password&&(t.verifier=ks(e.password)),r.valid=t.verificationBytes===t.verifier,r.valid&&(r.insitu=Ds(e.password))),n}},49:{n:"Font",f:function(e,t,r){var n={dyHeight:e.read_shift(2),fl:e.read_shift(2)};switch(r&&r.biff||8){case 2:break;case 3:case 4:e.l+=2;break;default:e.l+=10}return n.name=Pn(e,0,r),n}},51:{n:"PrintSize",f:On},60:{n:"Continue"},61:{n:"Window1",f:function(e){return{Pos:[e.read_shift(2),e.read_shift(2)],Dim:[e.read_shift(2),e.read_shift(2)],Flags:e.read_shift(2),CurTab:e.read_shift(2),FirstTab:e.read_shift(2),Selected:e.read_shift(2),TabRatio:e.read_shift(2)}}},64:{n:"Backup",f:In},65:{n:"Pane"},66:{n:"CodePage",f:On},77:{n:"Pls"},80:{n:"DCon"},81:{n:"DConRef"},82:{n:"DConName"},85:{n:"DefColWidth",f:On},89:{n:"XCT"},90:{n:"CRN"},91:{n:"FileSharing"},92:{n:"WriteAccess",f:function(e,t,r){if(r.enc)return e.l+=t,"";var n=e.l,r=Mn(e,0,r);return e.read_shift(t+n-e.l),r}},93:{n:"Obj",f:function(e,t,r){return r&&r.biff<8?function(e,t,r){e.l+=4;var n=e.read_shift(2),a=e.read_shift(2),s=e.read_shift(2);e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=6,t-=36;var i=[];return i.push((pa[n]||It)(e,t,r)),{cmo:[a,n,s],ft:i}}(e,t,r):{cmo:r=Qn(e),ft:function(t,e){for(var r=t.l+e,n=[];t.l<r;){var a=t.read_shift(2);t.l-=2;try{n.push(qn[a](t,r-t.l))}catch(e){return t.l=r,n}}return t.l!=r&&(t.l=r),n}(e,t-22,r[1])}}},94:{n:"Uncalced"},95:{n:"CalcSaveRecalc",f:In},96:{n:"Template"},97:{n:"Intl"},99:{n:"ObjProtect",f:In},125:{n:"ColInfo",f:ga},128:{n:"Guts",f:function(e){if(e.l+=4,0!==(e=[e.read_shift(2),e.read_shift(2)])[0]&&e[0]--,0!==e[1]&&e[1]--,7<e[0]||7<e[1])throw new Error("Bad Gutters: "+e.join("|"));return e}},129:{n:"WsBool",f:function(e,t,r){return{fDialog:16&(r&&8==r.biff||2==t?e.read_shift(2):(e.l+=t,0))}}},130:{n:"GridSet",f:On},131:{n:"HCenter",f:In},132:{n:"VCenter",f:In},133:{n:"BoundSheet8",f:function(e,t,r){var n=e.read_shift(4),a=3&e.read_shift(1),s=e.read_shift(1);switch(s){case 0:s="Worksheet";break;case 1:s="Macrosheet";break;case 2:s="Chartsheet";break;case 6:s="VBAModule"}return r=Pn(e,0,r),{pos:n,hs:a,dt:s,name:r=0===r.length?"Sheet1":r}}},134:{n:"WriteProtect"},140:{n:"Country",f:function(e){var t=[0,0],r=e.read_shift(2);return t[0]=Or[r]||r,r=e.read_shift(2),t[1]=Or[r]||r,t}},141:{n:"HideObj",f:On},144:{n:"Sort"},146:{n:"Palette",f:function(e){for(var t=e.read_shift(2),r=[];0<t--;)r.push(Wn(e));return r}},151:{n:"Sync"},152:{n:"LPr"},153:{n:"DxGCol"},154:{n:"FnGroupName"},155:{n:"FilterMode"},156:{n:"BuiltInFnGroupCount",f:On},157:{n:"AutoFilterInfo"},158:{n:"AutoFilter"},160:{n:"Scl",f:va},161:{n:"Setup",f:function(e,t){var r={};return t<32||(e.l+=16,r.header=Er(e),r.footer=Er(e),e.l+=2),r}},174:{n:"ScenMan"},175:{n:"SCENARIO"},176:{n:"SxView"},177:{n:"Sxvd"},178:{n:"SXVI"},180:{n:"SxIvd"},181:{n:"SXLI"},182:{n:"SXPI"},184:{n:"DocRoute"},185:{n:"RecipName"},189:{n:"MulRk",f:function(e,t){for(var r=e.l+t-2,n=e.read_shift(2),a=e.read_shift(2),s=[];e.l<r;)s.push(Gn(e));if(e.l!==r)throw new Error("MulRK read error");if(t=e.read_shift(2),s.length!=t-a+1)throw new Error("MulRK length mismatch");return{r:n,c:a,C:t,rkrec:s}}},190:{n:"MulBlank",f:function(e,t){for(var r=e.l+t-2,n=e.read_shift(2),a=e.read_shift(2),s=[];e.l<r;)s.push(e.read_shift(2));if(e.l!==r)throw new Error("MulBlank read error");if(t=e.read_shift(2),s.length!=t-a+1)throw new Error("MulBlank length mismatch");return{r:n,c:a,C:t,ixfe:s}}},193:{n:"Mms",f:An},197:{n:"SXDI"},198:{n:"SXDB"},199:{n:"SXFDB"},200:{n:"SXDBB"},201:{n:"SXNum"},202:{n:"SxBool",f:In},203:{n:"SxErr"},204:{n:"SXInt"},205:{n:"SXString"},206:{n:"SXDtr"},207:{n:"SxNil"},208:{n:"SXTbl"},209:{n:"SXTBRGIITM"},210:{n:"SxTbpg"},211:{n:"ObProj"},213:{n:"SXStreamID"},215:{n:"DBCell"},216:{n:"SXRng"},217:{n:"SxIsxoper"},218:{n:"BookBool",f:On},220:{n:"DbOrParamQry"},221:{n:"ScenarioProtect",f:In},222:{n:"OleObjectSize"},224:{n:"XF",f:function(e,t,r){var n,a,s,i={};return i.ifnt=e.read_shift(2),i.numFmtId=e.read_shift(2),i.flags=e.read_shift(2),i.fStyle=i.flags>>2&1,t-=6,i.data=(n=e,i.fStyle,a=r,s={},t=n.read_shift(4),e=n.read_shift(4),r=n.read_shift(4),n=n.read_shift(2),s.patternType=Fr[r>>26],a.cellStyles&&(s.alc=7&t,s.fWrap=t>>3&1,s.alcV=t>>4&7,s.fJustLast=t>>7&1,s.trot=t>>8&255,s.cIndent=t>>16&15,s.fShrinkToFit=t>>20&1,s.iReadOrder=t>>22&2,s.fAtrNum=t>>26&1,s.fAtrFnt=t>>27&1,s.fAtrAlc=t>>28&1,s.fAtrBdr=t>>29&1,s.fAtrPat=t>>30&1,s.fAtrProt=t>>31&1,s.dgLeft=15&e,s.dgRight=e>>4&15,s.dgTop=e>>8&15,s.dgBottom=e>>12&15,s.icvLeft=e>>16&127,s.icvRight=e>>23&127,s.grbitDiag=e>>30&3,s.icvTop=127&r,s.icvBottom=r>>7&127,s.icvDiag=r>>14&127,s.dgDiag=r>>21&15,s.icvFore=127&n,s.icvBack=n>>7&127,s.fsxButton=n>>14&1),s),i}},225:{n:"InterfaceHdr",f:function(e,t){return 0===t||e.read_shift(2),1200}},226:{n:"InterfaceEnd",f:An},227:{n:"SXVS"},229:{n:"MergeCells",f:function(e,t){for(var r=[],n=e.read_shift(2);n--;)r.push($n(e,t));return r}},233:{n:"BkHim"},235:{n:"MsoDrawingGroup"},236:{n:"MsoDrawing"},237:{n:"MsoDrawingSelection"},239:{n:"PhoneticInfo"},240:{n:"SxRule"},241:{n:"SXEx"},242:{n:"SxFilt"},244:{n:"SxDXF"},245:{n:"SxItm"},246:{n:"SxName"},247:{n:"SxSelect"},248:{n:"SXPair"},249:{n:"SxFmla"},251:{n:"SxFormat"},252:{n:"SST",f:function(e,t){for(var r=e.l+t,t=e.read_shift(4),n=e.read_shift(4),a=[],s=0;s!=n&&e.l<r;++s)a.push(function(e){var t=f;f=1200;var r,n=e.read_shift(2),a=4&(l=e.read_shift(1)),s=8&l,i=1+(1&l),o=0,l={};return s&&(o=e.read_shift(2)),a&&(r=e.read_shift(4)),i=0===n?"":e.read_shift(n,2==i?"dbcs-cont":"sbcs-cont"),s&&(e.l+=4*o),a&&(e.l+=r),l.t=i,s||(l.raw="<t>"+l.t+"</t>",l.r=l.t),f=t,l}(e));return a.Count=t,a.Unique=n,a}},253:{n:"LabelSst",f:function(e){var t=Xn(e);return t.isst=e.read_shift(4),t}},255:{n:"ExtSST",f:function(e,t){var r={};return r.dsst=e.read_shift(2),e.l+=t-2,r}},256:{n:"SXVDEx"},259:{n:"SXFormula"},290:{n:"SXDBEx"},311:{n:"RRDInsDel"},312:{n:"RRDHead"},315:{n:"RRDChgCell"},317:{n:"RRTabId",f:Dn},318:{n:"RRDRenSheet"},319:{n:"RRSort"},320:{n:"RRDMove"},330:{n:"RRFormat"},331:{n:"RRAutoFmt"},333:{n:"RRInsertSh"},334:{n:"RRDMoveBegin"},335:{n:"RRDMoveEnd"},336:{n:"RRDInsDelBegin"},337:{n:"RRDInsDelEnd"},338:{n:"RRDConflict"},339:{n:"RRDDefName"},340:{n:"RRDRstEtxp"},351:{n:"LRng"},352:{n:"UsesELFs",f:In},353:{n:"DSF",f:An},401:{n:"CUsr"},402:{n:"CbUsr"},403:{n:"UsrInfo"},404:{n:"UsrExcl"},405:{n:"FileLock"},406:{n:"RRDInfo"},407:{n:"BCUsrs"},408:{n:"UsrChk"},425:{n:"UserBView"},426:{n:"UserSViewBegin"},427:{n:"UserSViewEnd"},428:{n:"RRDUserView"},429:{n:"Qsi"},430:{n:"SupBook",f:function(e,t,r){var n=e.l+t,a=e.read_shift(2),t=e.read_shift(2);if(1025==(r.sbcch=t)||14849==t)return[t,a];if(t<1||255<t)throw new Error("Unexpected SupBook type: "+t);for(var r=Nn(e,t),s=[];n>e.l;)s.push(Ln(e));return[t,a,r,s]}},431:{n:"Prot4Rev",f:In},432:{n:"CondFmt"},433:{n:"CF"},434:{n:"DVal"},437:{n:"DConBin"},438:{n:"TxO",f:function(t,r,e){var n=t.l,a="";try{t.l+=4;var s=(e.lastobj||{cmo:[0,0]}).cmo[1];-1==[0,5,7,11,12,14].indexOf(s)?t.l+=6:function(e){var t=e.read_shift(1);e.l++;var r=e.read_shift(2);e.l+=2}(t);var i=t.read_shift(2);t.read_shift(2),On(t);var o=t.read_shift(2);t.l+=o;for(var l=1;l<t.lens.length-1;++l){if(t.l-n!=t.lens[l])throw new Error("TxO: bad continue record");var c=t[t.l];if((a+=Nn(t,t.lens[l+1]-t.lens[l]-1)).length>=(c?i:2*i))break}if(a.length!==i&&a.length!==2*i)throw new Error("cchText: "+i+" != "+a.length);return t.l=n+r,{t:a}}catch(e){return t.l=n+r,{t:a}}}},439:{n:"RefreshAll",f:In},440:{n:"HLink",f:function(e,t){var r=$n(e,8);return e.l+=16,[r,function(e,t){var r=e.l+t;if(2!==(l=e.read_shift(4)))throw new Error("Unrecognized streamVersion: "+l);t=e.read_shift(2),e.l+=2;var n,a,s,i,o,l="";16&t&&(n=zn(e,e.l)),128&t&&(a=zn(e,e.l)),257==(257&t)&&(s=zn(e,e.l)),1==(257&t)&&(c=Hn(e,e.l)),8&t&&(l=zn(e,e.l)),32&t&&(i=e.read_shift(16)),64&t&&(o=dn(e)),e.l=r;var c=a||s||c||"";return c&&l&&(c+="#"+l),c={Target:c=c||"#"+l},i&&(c.guid=i),o&&(c.time=o),n&&(c.Tooltip=n),c}(e,t-24)]}},441:{n:"Lel"},442:{n:"CodeName",f:Ln},443:{n:"SXFDBType"},444:{n:"Prot4RevPass",f:On},445:{n:"ObNoMacros"},446:{n:"Dv"},448:{n:"Excel9File",f:An},449:{n:"RecalcId",f:function(e){return e.read_shift(2),e.read_shift(4)},r:2},450:{n:"EntExU2",f:An},512:{n:"Dimensions",f:aa},513:{n:"Blank",f:ba},515:{n:"Number",f:function(e){var t=Xn(e),e=Er(e);return t.val=e,t}},516:{n:"Label",f:function(e,t,r){return e.l,t=Xn(e),2==r.biff&&e.l++,r=Ln(e,e.l,r),t.val=r,t}},517:{n:"BoolErr",f:ia},518:{n:"Formula",f:Bo},519:{n:"String",f:Ea},520:{n:"Row",f:function(e){var t={};t.r=e.read_shift(2),t.c=e.read_shift(2),t.cnt=e.read_shift(2)-t.c;var r=e.read_shift(2);e.l+=4;var n=e.read_shift(1);return e.l+=3,7&n&&(t.level=7&n),32&n&&(t.hidden=!0),64&n&&(t.hpt=r/20),t}},523:{n:"Index"},545:{n:"Array",f:da},549:{n:"DefaultRowHeight",f:ra},566:{n:"Table"},574:{n:"Window2",f:function(e,t,r){return r&&2<=r.biff&&r.biff<8?{}:{RTL:64&e.read_shift(2)}}},638:{n:"RK",f:function(e){var t=e.read_shift(2),r=e.read_shift(2),e=Gn(e);return{r:t,c:r,ixfe:e[0],rknum:e[1]}}},659:{n:"Style"},1030:{n:"Formula",f:Bo},1048:{n:"BigName"},1054:{n:"Format",f:function(e,t,r){return[e.read_shift(2),Mn(e,0,r)]}},1084:{n:"ContinueBigName"},1212:{n:"ShrFmla",f:function(e,t,r){var n=Kn(e);e.l++;var a=e.read_shift(1);return[function(e,t,r){var n,a=e.l+t,s=e.read_shift(2),i=So(e,s,r);if(65535==s)return[[],It(e,t-2)];t!==s+2&&(n=wo(e,a-s-2,i,r));return[i,n]}(e,t-=8,r),a,n]}},2048:{n:"HLinkTooltip",f:function(e,t){return e.read_shift(2),[$n(e,8),e.read_shift((t-10)/2,"dbcs-cont").replace(q,"")]}},2049:{n:"WebPub"},2050:{n:"QsiSXTag"},2051:{n:"DBQueryExt"},2052:{n:"ExtString"},2053:{n:"TxtQry"},2054:{n:"Qsir"},2055:{n:"Qsif"},2056:{n:"RRDTQSIF"},2057:{n:"BOF",f:ea},2058:{n:"OleDbConn"},2059:{n:"WOpt"},2060:{n:"SXViewEx"},2061:{n:"SXTH"},2062:{n:"SXPIEx"},2063:{n:"SXVDTEx"},2064:{n:"SXViewEx9"},2066:{n:"ContinueFrt"},2067:{n:"RealTimeData"},2128:{n:"ChartFrtInfo"},2129:{n:"FrtWrapper"},2130:{n:"StartBlock"},2131:{n:"EndBlock"},2132:{n:"StartObject"},2133:{n:"EndObject"},2134:{n:"CatLab"},2135:{n:"YMult"},2136:{n:"SXViewLink"},2137:{n:"PivotChartBits"},2138:{n:"FrtFontList"},2146:{n:"SheetExt"},2147:{n:"BookExt",r:12},2148:{n:"SXAddl"},2149:{n:"CrErr"},2150:{n:"HFPicture"},2151:{n:"FeatHdr",f:An},2152:{n:"Feat"},2154:{n:"DataLabExt"},2155:{n:"DataLabExtContents"},2156:{n:"CellWatch"},2161:{n:"FeatHdr11"},2162:{n:"Feature11"},2164:{n:"DropDownObjIds"},2165:{n:"ContinueFrt11"},2166:{n:"DConn"},2167:{n:"List12"},2168:{n:"Feature12"},2169:{n:"CondFmt12"},2170:{n:"CF12"},2171:{n:"CFEx"},2172:{n:"XFCRC",f:function(e){e.l+=2;var t={cxfs:0,crc:0};return t.cxfs=e.read_shift(2),t.crc=e.read_shift(4),t},r:12},2173:{n:"XFExt",f:function(e,t){e.l,e.l+=2,t=e.read_shift(2),e.l+=2;for(var r=e.read_shift(2),n=[];0<r--;)n.push(Fi(e,e.l));return{ixfe:t,ext:n}},r:12},2174:{n:"AutoFilter12"},2175:{n:"ContinueFrt12"},2180:{n:"MDTInfo"},2181:{n:"MDXStr"},2182:{n:"MDXTuple"},2183:{n:"MDXSet"},2184:{n:"MDXProp"},2185:{n:"MDXKPI"},2186:{n:"MDB"},2187:{n:"PLV"},2188:{n:"Compat12",f:In,r:12},2189:{n:"DXF"},2190:{n:"TableStyles",r:12},2191:{n:"TableStyle"},2192:{n:"TableStyleElement"},2194:{n:"StyleExt"},2195:{n:"NamePublish"},2196:{n:"NameCmt",f:function(e,t,r){if(!(r.biff<8)){var n=e.read_shift(2),a=e.read_shift(2);return[Nn(e,n,r),Nn(e,a,r)]}e.l+=t},r:12},2197:{n:"SortData"},2198:{n:"Theme",f:function(e,t,r){var n=e.l+t;if(124226!==e.read_shift(4))if(r.cellStyles&&I){t=e.slice(e.l);e.l=n;try{a=new I(t)}catch(e){return}var a=U(a,"theme/theme/theme1.xml",!0);if(a)return Ii(a,r)}else e.l=n},r:12},2199:{n:"GUIDTypeLib"},2200:{n:"FnGrp12"},2201:{n:"NameFnGrp12"},2202:{n:"MTRSettings",f:function(e){return[0!==e.read_shift(4),0!==e.read_shift(4),e.read_shift(4)]},r:12},2203:{n:"CompressPictures",f:An},2204:{n:"HeaderFooter"},2205:{n:"CrtLayout12"},2206:{n:"CrtMlFrt"},2207:{n:"CrtMlFrtContinue"},2211:{n:"ForceFullCalculation",f:function(e){var t,r,n=(r=(t=e).read_shift(2),n=t.read_shift(2),t.l+=8,{type:r,flags:n});if(2211!=n.type)throw new Error("Invalid Future Record "+n.type);return 0!==e.read_shift(4)}},2212:{n:"ShapePropsStream"},2213:{n:"TextPropsStream"},2214:{n:"RichTextStream"},2215:{n:"CrtLayout12A"},4097:{n:"Units"},4098:{n:"Chart"},4099:{n:"Series"},4102:{n:"DataFormat"},4103:{n:"LineFormat"},4105:{n:"MarkerFormat"},4106:{n:"AreaFormat"},4107:{n:"PieFormat"},4108:{n:"AttachedLabel"},4109:{n:"SeriesText"},4116:{n:"ChartFormat"},4117:{n:"Legend"},4118:{n:"SeriesList"},4119:{n:"Bar"},4120:{n:"Line"},4121:{n:"Pie"},4122:{n:"Area"},4123:{n:"Scatter"},4124:{n:"CrtLine"},4125:{n:"Axis"},4126:{n:"Tick"},4127:{n:"ValueRange"},4128:{n:"CatSerRange"},4129:{n:"AxisLine"},4130:{n:"CrtLink"},4132:{n:"DefaultText"},4133:{n:"Text"},4134:{n:"FontX",f:On},4135:{n:"ObjectLink"},4146:{n:"Frame"},4147:{n:"Begin"},4148:{n:"End"},4149:{n:"PlotArea"},4154:{n:"Chart3d"},4156:{n:"PicF"},4157:{n:"DropBar"},4158:{n:"Radar"},4159:{n:"Surf"},4160:{n:"RadarArea"},4161:{n:"AxisParent"},4163:{n:"LegendException"},4164:{n:"ShtProps",f:function(e,t,r){var n={area:!1};return 5!=r.biff?e.l+=t:(t=e.read_shift(1),e.l+=3,16&t&&(n.area=!0)),n}},4165:{n:"SerToCrt"},4166:{n:"AxesUsed"},4168:{n:"SBaseRef"},4170:{n:"SerParent"},4171:{n:"SerAuxTrend"},4174:{n:"IFmtRecord"},4175:{n:"Pos"},4176:{n:"AlRuns"},4177:{n:"BRAI"},4187:{n:"SerAuxErrBar"},4188:{n:"ClrtClient",f:function(e){for(var t=e.read_shift(2),r=[];0<t--;)r.push(Wn(e));return r}},4189:{n:"SerFmt"},4191:{n:"Chart3DBarShape"},4192:{n:"Fbi"},4193:{n:"BopPop"},4194:{n:"AxcExt"},4195:{n:"Dat"},4196:{n:"PlotGrowth"},4197:{n:"SIIndex"},4198:{n:"GelFrame"},4199:{n:"BopPopCustom"},4200:{n:"Fbi2"},0:{n:"Dimensions",f:aa},2:{n:"BIFF2INT",f:function(e){var t=Xn(e);return++e.l,e=e.read_shift(2),t.t="n",t.val=e,t}},5:{n:"BoolErr",f:ia},7:{n:"String",f:function(e){var t=e.read_shift(1);return 0===t?(e.l++,""):e.read_shift(t,"sbcs-cont")}},8:{n:"BIFF2ROW"},11:{n:"Index"},22:{n:"ExternCount",f:On},30:{n:"BIFF2FORMAT",f:na},31:{n:"BIFF2FMTCNT"},32:{n:"BIFF2COLINFO"},33:{n:"Array",f:da},37:{n:"DefaultRowHeight",f:ra},50:{n:"BIFF2FONTXTRA",f:function(e,t){e.l+=6,e.l+=2,e.l+=1,e.l+=3,e.l+=1,e.l+=t-13}},52:{n:"DDEObjName"},62:{n:"BIFF2WINDOW2"},67:{n:"BIFF2XF"},69:{n:"BIFF2FONTCLR"},86:{n:"BIFF4FMTCNT"},126:{n:"RK"},127:{n:"ImData",f:function(e){var t=e.read_shift(2),r=e.read_shift(2),n=e.read_shift(4),r={fmt:t,env:r,len:n,data:e.slice(e.l,e.l+n)};return e.l+=n,r}},135:{n:"Addin"},136:{n:"Edg"},137:{n:"Pub"},145:{n:"Sub"},148:{n:"LHRecord"},149:{n:"LHNGraph"},150:{n:"Sound"},169:{n:"CoordList"},171:{n:"GCW"},188:{n:"ShrFmla"},191:{n:"ToolbarHdr"},192:{n:"ToolbarEnd"},194:{n:"AddMenu"},195:{n:"DelMenu"},214:{n:"RString",f:function(e,t,r){var n=e.l+t,a=Xn(e),t=e.read_shift(2),r=Nn(e,t,r);return e.l=n,a.t="str",a.val=r,a}},223:{n:"UDDesc"},234:{n:"TabIdConf"},354:{n:"XL5Modify"},421:{n:"FileSharing2"},521:{n:"BOF",f:ea},536:{n:"Lbl",f:ha},547:{n:"ExternName",f:ca},561:{n:"Font"},579:{n:"BIFF3XF"},1033:{n:"BOF",f:ea},1091:{n:"BIFF4XF"},2157:{n:"FeatInfo"},2163:{n:"FeatInfo11"},2177:{n:"SXAddl12"},2240:{n:"AutoWebPub"},2241:{n:"ListObj"},2242:{n:"ListField"},2243:{n:"ListDV"},2244:{n:"ListCondFmt"},2245:{n:"ListCF"},2246:{n:"FMQry"},2247:{n:"FMSQry"},2248:{n:"PLV"},2249:{n:"LnExt"},2250:{n:"MkrExt"},2251:{n:"CrtCoopt"},2262:{n:"FRTArchId$",r:12},29282:{}},_c=w(Sc,"n");function yc(e,t,r,n){var a=+t||+_c[t];isNaN(a)||(t=n||(r||[]).length||0,(n=e.next(4)).write_shift(2,a),n.write_shift(2,t),0<t&&gt(r)&&e.push(r))}function Cc(e,t,r){return(e=e||Rt(7)).write_shift(2,t),e.write_shift(2,r),e.write_shift(2,0),e.write_shift(1,0),e}function Bc(e,t,r,n){if(null!=t.v)switch(t.t){case"d":case"n":var a="d"==t.t?G(oe(t.v)):t.v;return void(a==(0|a)&&0<=a&&a<65536?yc(e,2,(f=r,h=n,u=a,Cc(d=Rt(9),f,h),d.write_shift(2,u),d)):yc(e,3,(u=r,d=n,a=a,Cc(c=Rt(15),u,d),c.write_shift(8,a,"f"),c)));case"b":case"e":return void yc(e,5,(c=r,s=n,i=t.v,o=t.t,Cc(l=Rt(9),c,s),"e"==o?(l.write_shift(1,i),l.write_shift(1,1)):(l.write_shift(1,i?1:0),l.write_shift(1,0)),l));case"s":case"str":return void yc(e,4,(s=r,o=n,i=t.v,Cc(l=Rt(8+2*i.length),s,o),l.write_shift(1,i.length),l.write_shift(i.length,i,"sbcs"),l.l<l.length?l.slice(0,l.l):l))}var s,i,o,l,c,f,h,u,d;yc(e,1,Cc(null,r,n))}function Tc(e,t){var r=t||{};null!=fe&&null==r.dense&&(r.dense=fe);for(var t=Ft(),n=0,a=0;a<e.SheetNames.length;++a)e.SheetNames[a]==r.sheet&&(n=a);if(0==n&&r.sheet&&e.SheetNames[0]!=r.sheet)throw new Error("Sheet not found: "+r.sheet);return yc(t,9,ta(0,16,r)),function(e,t,r){var n=Array.isArray(t),a=Yt(t["!ref"]||"A1"),s=[];if(255<a.e.c||16383<a.e.r){if(r.WTF)throw new Error("Range "+(t["!ref"]||"A1")+" exceeds format limit A1:IV16384");a.e.c=Math.min(a.e.c,255),a.e.r=Math.min(a.e.c,16383),c=$t(a)}for(var i=a.s.r;i<=a.e.r;++i)for(var o=Ht(i),l=a.s.c;l<=a.e.c;++l){i===a.s.r&&(s[l]=Vt(l));var c=s[l]+o,f=n?(t[i]||[])[l]:t[c];f&&Bc(e,f,i,l)}}(t,e.Sheets[e.SheetNames[n]],r),yc(t,10),t.end()}function kc(e,t,r){var n,a;yc(e,"Font",(a=(n={sz:12,color:{theme:1},name:"Arial",family:2,scheme:"minor"}).name||"Arial",(e=Rt((r=(e=r)&&5==e.biff)?15+a.length:16+2*a.length)).write_shift(2,20*(n.sz||12)),e.write_shift(4,0),e.write_shift(2,400),e.write_shift(4,0),e.write_shift(2,0),e.write_shift(1,a.length),r||e.write_shift(1,1),e.write_shift((r?1:2)*a.length,a,r?"sbcs":"utf16le"),e))}function xc(i,o,l){o&&[[5,8],[23,26],[41,44],[50,392]].forEach(function(e){for(var t,r,n,a,s=e[0];s<=e[1];++s)null!=o[s]&&yc(i,"Format",(r=o[t=s],a=void 0,n=(n=l)&&5==n.biff,(a=a||Rt(n?3+r.length:5+2*r.length)).write_shift(2,t),a.write_shift(n?1:2,r.length),n||a.write_shift(1,1),a.write_shift((n?1:2)*r.length,r,n?"sbcs":"utf16le"),null==(a=a.length>a.l?a.slice(0,a.l):a).l&&(a.l=a.length),a))})}function Ac(e,t){for(var r=0;r<t["!links"].length;++r){var n=t["!links"][r];yc(e,"HLink",ma(n)),n[1].Tooltip&&yc(e,"HLinkTooltip",function(e){var t=e[1].Tooltip,r=Rt(10+2*(t.length+1));r.write_shift(2,2048),e=Xt(e[0]),r.write_shift(2,e.r),r.write_shift(2,e.r),r.write_shift(2,e.c),r.write_shift(2,e.c);for(var n=0;n<t.length;++n)r.write_shift(2,t.charCodeAt(n));return r.write_shift(2,0),r}(n))}delete t["!links"]}function Ic(e,t,r,n,a){var s,i,o,l,c,f=16+Wo(a.cellXfs,t,a);if(null!=t.v)switch(t.t){case"d":case"n":var h="d"==t.t?G(oe(t.v)):t.v;return void yc(e,"Number",(s=r,i=n,o=h,l=f,c=Rt(14),jn(s,i,l,c),wr(o,c),c));case"b":case"e":return void yc(e,517,oa(r,n,t.v,f,0,t.t));case"s":case"str":return void yc(e,"Label",(h=r,s=n,i=t.v,l=f,o=Rt(+(c=!(o=a)||8==o.biff)+8+(1+c)*i.length),jn(h,s,l,o),o.write_shift(2,i.length),c&&o.write_shift(1,1),o.write_shift((1+c)*i.length,i,c?"utf16le":"sbcs"),o))}yc(e,"Blank",jn(r,n,f))}function Rc(e,t,r){var n=Ft(),a=r.SheetNames[e],s=r.Sheets[a]||{},i=(r||{}).Workbook||{},o=(i.Sheets||[])[e]||{},l=Array.isArray(s),c=8==t.biff,f=[],h=Yt(s["!ref"]||"A1"),u=c?65536:16384;if(255<h.e.c||h.e.r>=u){if(t.WTF)throw new Error("Range "+(s["!ref"]||"A1")+" exceeds format limit A1:IV16384");h.e.c=Math.min(h.e.c,255),h.e.r=Math.min(h.e.c,u-1)}yc(n,2057,ta(0,16,t)),yc(n,"CalcMode",Fn(1)),yc(n,"CalcCount",Fn(100)),yc(n,"CalcRefMode",Rn(!0)),yc(n,"CalcIter",Rn(!1)),yc(n,"CalcDelta",wr(.001)),yc(n,"CalcSaveRecalc",Rn(!0)),yc(n,"PrintRowCol",Rn(!1)),yc(n,"PrintGrid",Rn(!1)),yc(n,"GridSet",Fn(1)),yc(n,"Guts",(r=[0,0],(e=Rt(8)).write_shift(4,0),e.write_shift(2,r[0]?r[0]+1:0),e.write_shift(2,r[1]?r[1]+1:0),e)),yc(n,"HCenter",Rn(!1)),yc(n,"VCenter",Rn(!1)),yc(n,512,(u=h,(r=Rt(2*(e=8!=(r=t).biff&&r.biff?2:4)+6)).write_shift(e,u.s.r),r.write_shift(e,u.e.r+1),r.write_shift(2,u.s.c),r.write_shift(2,u.e.c+1),r.write_shift(2,0),r)),c&&(s["!links"]=[]);for(var d=h.s.r;d<=h.e.r;++d)for(var p=Ht(d),m=h.s.c;m<=h.e.c;++m){d===h.s.r&&(f[m]=Vt(m));var g=f[m]+p,b=l?(s[d]||[])[m]:s[g];b&&(Ic(n,b,d,m,t),c&&b.l&&s["!links"].push([g,b.l]))}var v,E,a=o.CodeName||o.name||a;return c&&i.Views&&yc(n,"Window2",(i=i.Views[0],E=Rt(18),v=1718,i&&i.RTL&&(v|=64),E.write_shift(2,v),E.write_shift(4,0),E.write_shift(4,64),E.write_shift(4,0),E.write_shift(4,0),E)),c&&(s["!merges"]||[]).length&&yc(n,"MergeCells",function(e){var t=Rt(2+8*e.length);t.write_shift(2,e.length);for(var r=0;r<e.length;++r)Yn(e[r],t);return t}(s["!merges"])),c&&Ac(n,s),yc(n,"CodeName",Un(a)),c&&(v=n,E=s,(a=Rt(19)).write_shift(4,2151),a.write_shift(4,0),a.write_shift(4,0),a.write_shift(2,3),a.write_shift(1,1),a.write_shift(4,0),yc(v,"FeatHdr",a),(a=Rt(39)).write_shift(4,2152),a.write_shift(4,0),a.write_shift(4,0),a.write_shift(2,3),a.write_shift(1,0),a.write_shift(4,0),a.write_shift(2,1),a.write_shift(4,4),a.write_shift(2,0),Yn(Yt(E["!ref"]||"A1"),a),a.write_shift(4,4),yc(v,"Feat",a)),yc(n,"EOF"),n.end()}function Oc(e,t,r){var n=Ft(),a=(e||{}).Workbook||{},s=a.Sheets||[],i=a.WBProps||{},o=8==r.biff,a=5==r.biff;yc(n,2057,ta(0,5,r)),"xla"==r.bookType&&yc(n,"Addin"),yc(n,"InterfaceHdr",o?Fn(1200):null),yc(n,"Mms",function(e,t){t=t||Rt(e);for(var r=0;r<e;++r)t.write_shift(1,0);return t}(2)),a&&yc(n,"ToolbarHdr"),a&&yc(n,"ToolbarEnd"),yc(n,"InterfaceEnd"),yc(n,"WriteAccess",function(e){var t=!e||8==e.biff,r=Rt(t?112:54);for(r.write_shift(8==e.biff?2:1,7),t&&r.write_shift(1,0),r.write_shift(4,859007059),r.write_shift(4,5458548|(t?0:536870912));r.l<r.length;)r.write_shift(1,t?0:32);return r}(r)),yc(n,"CodePage",Fn(o?1200:1252)),o&&yc(n,"DSF",Fn(0)),o&&yc(n,"Excel9File"),yc(n,"RRTabId",function(e){for(var t=Rt(2*e),r=0;r<e;++r)t.write_shift(2,r+1);return t}(e.SheetNames.length)),o&&e.vbaraw&&(yc(n,"ObProj"),yc(n,"CodeName",Un(i.CodeName||"ThisWorkbook"))),yc(n,"BuiltInFnGroupCount",Fn(17)),yc(n,"WinProtect",Rn(!1)),yc(n,"Protect",Rn(!1)),yc(n,"Password",Fn(0)),o&&yc(n,"Prot4Rev",Rn(!1)),o&&yc(n,"Prot4RevPass",Fn(0)),yc(n,"Window1",((a=Rt(18)).write_shift(2,0),a.write_shift(2,0),a.write_shift(2,29280),a.write_shift(2,17600),a.write_shift(2,56),a.write_shift(2,0),a.write_shift(2,0),a.write_shift(2,1),a.write_shift(2,500),a)),yc(n,"Backup",Rn(!1)),yc(n,"HideObj",Fn(0)),yc(n,"Date1904",Rn("true"==((i=e).Workbook&&i.Workbook.WBProps&&Re(i.Workbook.WBProps.date1904)?"true":"false"))),yc(n,"CalcPrecision",Rn(!0)),o&&yc(n,"RefreshAll",Rn(!1)),yc(n,"BookBool",Fn(0)),kc(n,0,r),xc(n,e.SSF,r),function(t,r){for(var e=0;e<16;++e)yc(t,"XF",sa({numFmtId:0,style:!0},0,r));r.cellXfs.forEach(function(e){yc(t,"XF",sa(e,0,r))})}(n,r),o&&yc(n,"UsesELFs",Rn(!1));a=n.end(),i=Ft();o&&yc(i,"Country",((g=g||Rt(4)).write_shift(2,1),g.write_shift(2,1),g)),yc(i,"EOF");for(var n=i.end(),l=Ft(),c=0,f=0,f=0;f<e.SheetNames.length;++f)c+=(o?12:11)+(o?2:1)*e.SheetNames[f].length;var h,u,d,p=a.length+c+n.length;for(f=0;f<e.SheetNames.length;++f){var m=s[f]||{};yc(l,"BoundSheet8",(h={pos:p,hs:m.Hidden||0,dt:0,name:e.SheetNames[f]},d=u=void 0,u=!(m=r)||8<=m.biff?2:1,(d=Rt(8+u*h.name.length)).write_shift(4,h.pos),d.write_shift(1,h.hs||0),d.write_shift(1,h.dt),d.write_shift(1,h.name.length),8<=m.biff&&d.write_shift(1,1),d.write_shift(u*h.name.length,h.name,m.biff<8?"sbcs":"utf16le"),(m=d.slice(0,d.l)).l=d.l,m)),p+=t[f].length}var g=l.end();if(c!=g.length)throw new Error("BS8 "+c+" != "+g.length);i=[];return a.length&&i.push(a),g.length&&i.push(g),n.length&&i.push(n),Je([i])}function Fc(e,t){var r=t||{};switch(r.biff||2){case 8:case 5:return function(e,t){var r=t||{},n=[];e&&!e.SSF&&(e.SSF=ue.get_table()),e&&e.SSF&&(ne(ue),ue.load_table(e.SSF),r.revssf=_(e.SSF),r.revssf[e.SSF[65535]]=0,r.ssf=e.SSF),r.cellXfs=[],r.Strings=[],r.Strings.Count=0,r.Strings.Unique=0,Wo(r.cellXfs,{},{revssf:{General:0}});for(var a=0;a<e.SheetNames.length;++a)n[n.length]=Rc(a,r,e);return n.unshift(Oc(e,n,r)),Je([n])}(e,t);case 4:case 3:case 2:return Tc(e,t)}throw new Error("invalid type "+r.bookType+" for BIFF")}var Dc,Pc,Nc={to_workbook:function(e,t){return Qt(Lc(e,t),t)},to_sheet:Lc,_row:Mc,BEGIN:Dc='<html><head><meta charset="utf-8"/><title>SheetJS Table Export</title></head><body>',END:Pc="</body></html>",_preamble:Uc,from_sheet:function(e,t){var r=t||{},n=null!=r.header?r.header:Dc,t=null!=r.footer?r.footer:Pc,a=[n],s=Gt(e["!ref"]);r.dense=Array.isArray(e),a.push(Uc(0,0,r));for(var i=s.s.r;i<=s.e.r;++i)a.push(Mc(e,s,i,r));return a.push("</table>"+t),a.join("")}};function Lc(e,t){var r=t||{};null!=fe&&null==r.dense&&(r.dense=fe);var n=r.dense?[]:{},a=e.match(/<table/i);if(!a)throw new Error("Invalid HTML: could not find <table>");for(var s,t=e.match(/<\/table/i),i=a.index,o=t&&t.index||e.length,l=function(e,t,r){if(O||"string"==typeof t)return e.split(t);for(var n=e.split(t),a=[n[0]],s=1;s<n.length;++s)a.push(r),a.push(n[s]);return a}(e.slice(i,o),/(:?<tr[^>]*>)/i,"<tr>"),c=-1,f=0,h={s:{r:1e7,c:1e7},e:{r:0,c:0}},u=[],i=0;i<l.length;++i){var d=l[i].trim(),p=d.slice(0,3).toLowerCase();if("<tr"!=p){if("<td"==p||"<th"==p)for(var m=d.split(/<\/t[dh]>/i),o=0;o<m.length;++o){var g=m[o].trim();if(g.match(/<t[dh]/i)){for(var b=g,v=0;"<"==b.charAt(0)&&-1<(v=b.indexOf(">"));)b=b.slice(v+1);var E=pe(g.slice(0,g.indexOf(">"))),w=E.colspan?+E.colspan:1;(1<(s=+E.rowspan)||1<w)&&u.push({s:{r:c,c:f},e:{r:c+(s||1)-1,c:f+w-1}});g=E.t||"";b.length?(b=Me(b),h.s.r>c&&(h.s.r=c),h.e.r<c&&(h.e.r=c),h.s.c>f&&(h.s.c=f),h.e.c<f&&(h.e.c=f),b.length&&(E={t:"s",v:b},r.raw||!b.trim().length||"s"==g||("TRUE"===b?E={t:"b",v:!0}:"FALSE"===b?E={t:"b",v:!1}:isNaN(x(b))?isNaN(A(b).getDate())||(E={t:"d",v:oe(b)},(E=!r.cellDates?{t:"n",v:G(E.v)}:E).z=r.dateNF||ue._table[14]):E={t:"n",v:x(b)}),r.dense?(n[c]||(n[c]=[]),n[c][f]=E):n[jt({r:c,c:f})]=E,f+=w)):f+=w}}}else{if(++c,r.sheetRows&&r.sheetRows<=c){--c;break}f=0}}return n["!ref"]=$t(h),n}function Mc(e,t,r,n){for(var a=e["!merges"]||[],s=[],i=t.s.c;i<=t.e.c;++i){for(var o,l,c,f,h=0,u=0,d=0;d<a.length;++d)if(!(a[d].s.r>r||a[d].s.c>i||a[d].e.r<r||a[d].e.c<i)){if(a[d].s.r<r||a[d].s.c<i){h=-1;break}h=a[d].e.r-a[d].s.r+1,u=a[d].e.c-a[d].s.c+1;break}h<0||(o=jt({r:r,c:i}),l=n.dense?(e[r]||[])[i]:e[o],c={},1<h&&(c.rowspan=h),1<u&&(c.colspan=u),f=l&&null!=l.v&&(l.h||Te(l.w||(Zt(l),l.w)||""))||"",c.t=l&&l.t||"z",n.editable&&(f='<span contenteditable="true">'+f+"</span>"),c.id="sjs-"+o,s.push(Ge("td",f,c)))}return"<tr>"+s.join("")+"</tr>"}function Uc(e,t,r){return[].join("")+"<table"+(r&&r.id?' id="'+r.id+'"':"")+">"}function Hc(e,t){var r=t||{};null!=fe&&(r.dense=fe);for(var n,a,s=r.dense?[]:{},i=e.getElementsByTagName("tr"),o=r.sheetRows||1e7,l={s:{r:0,c:0},e:{r:0,c:0}},c=[],f=0,h=[],u=0,d=0;u<i.length&&d<o;++u){var p=i[u];if(zc(p)){if(r.display)continue;h[d]={hidden:!0}}for(var m=p.children,g=n=0;g<m.length;++g){var b=m[g];if(!r.display||!zc(b)){for(var v=Me(b.innerHTML),f=0;f<c.length;++f){var E=c[f];E.s.c==n&&E.s.r<=d&&d<=E.e.r&&(n=E.e.c+1,f=-1)}a=+b.getAttribute("colspan")||1,(0<(w=+b.getAttribute("rowspan"))||1<a)&&c.push({s:{r:d,c:n},e:{r:d+(w||1)-1,c:n+a-1}});var w={t:"s",v:v},b=b.getAttribute("t")||"";null!=v&&(0==v.length?w.t=b||"z":r.raw||0==v.trim().length||"s"==b||("TRUE"===v?w={t:"b",v:!0}:"FALSE"===v?w={t:"b",v:!1}:isNaN(x(v))?isNaN(A(v).getDate())||(w={t:"d",v:oe(v)},(w=!r.cellDates?{t:"n",v:G(w.v)}:w).z=r.dateNF||ue._table[14]):w={t:"n",v:x(v)})),r.dense?(s[d]||(s[d]=[]),s[d][n]=w):s[jt({c:n,r:d})]=w,l.e.c<n&&(l.e.c=n),n+=a}}++d}return c.length&&(s["!merges"]=c),h.length&&(s["!rows"]=h),l.e.r=d-1,s["!ref"]=$t(l),o<=d&&(s["!fullref"]=$t((l.e.r=i.length-u+d-1,l))),s}function zc(e){var t,r="",t=(t=e).ownerDocument.defaultView&&"function"==typeof t.ownerDocument.defaultView.getComputedStyle?t.ownerDocument.defaultView.getComputedStyle:"function"==typeof getComputedStyle?getComputedStyle:null;return"none"===(r=(r=t?t(e).getPropertyValue("display"):r)||e.style.display)}var Vc,Wc=(Vc={day:["d","dd"],month:["m","mm"],year:["y","yy"],hours:["h","hh"],minutes:["m","mm"],seconds:["s","ss"],"am-pm":["A/P","AM/PM"],"day-of-week":["ddd","dddd"],era:["e","ee"],quarter:["\\Qm",'m\\"th quarter"']},function(e,t){var r=t||{};null!=fe&&null==r.dense&&(r.dense=fe);var n,a,s,i,o=rc(e),l=[],c={name:""},f="",h=0,u={},d=[],p=r.dense?[]:{},m={value:""},g="",b=0,v=[],E=-1,w=-1,S={s:{r:1e6,c:1e7},e:{r:0,c:0}},_=0,y={},C=[],B={},T=0,k=0,x=[],A=1,I=1,R=[],O={Names:[]},F={},D=["",""],P=[],N={},L="",M=0,U=!1,H=!1,z=0;for(nc.lastIndex=0,o=o.replace(/<!--([\s\S]*?)-->/gm,"").replace(/<!DOCTYPE[^\[]*\[[^\]]*\]>/gm,"");s=nc.exec(o);)switch(s[3]=s[3].replace(/_.*$/,"")){case"table":case"工作表":"/"===s[1]?(S.e.c>=S.s.c&&S.e.r>=S.s.r&&(p["!ref"]=$t(S)),0<r.sheetRows&&r.sheetRows<=S.e.r&&(p["!fullref"]=p["!ref"],S.e.r=r.sheetRows-1,p["!ref"]=$t(S)),C.length&&(p["!merges"]=C),x.length&&(p["!rows"]=x),a.name=a["名称"]||a.name,"undefined"!=typeof JSON&&JSON.stringify(a),d.push(a.name),u[a.name]=p,H=!1):"/"!==s[0].charAt(s[0].length-2)&&(a=pe(s[0],!1),E=w=-1,S.s.r=S.s.c=1e7,S.e.r=S.e.c=0,p=r.dense?[]:{},C=[],x=[],H=!0);break;case"table-row-group":"/"===s[1]?--_:++_;break;case"table-row":case"行":if("/"===s[1]){E+=A,A=1;break}if((V=pe(s[0],!1))["行号"]?E=V["行号"]-1:-1==E&&(E=0),(A=+V["number-rows-repeated"]||1)<10)for(z=0;z<A;++z)0<_&&(x[E+z]={level:_});w=-1;break;case"covered-table-cell":"/"!==s[1]&&++w,r.sheetStubs&&(r.dense?(p[E]||(p[E]=[]),p[E][w]={t:"z"}):p[jt({r:E,c:w})]={t:"z"}),g="",v=[];break;case"table-cell":case"数据":if("/"===s[0].charAt(s[0].length-2))++w,m=pe(s[0],!1),I=parseInt(m["number-columns-repeated"]||"1",10),i={t:"z",v:null},m.formula&&0!=r.cellFormula&&(i.f=Po(we(m.formula))),"string"==(m["数据类型"]||m["value-type"])&&(i.t="s",i.v=we(m["string-value"]||""),r.dense?(p[E]||(p[E]=[]),p[E][w]=i):p[jt({r:E,c:w})]=i),w+=I-1;else if("/"!==s[1]){var I=1,V=A?E+A-1:E;if(++w>S.e.c&&(S.e.c=w),w<S.s.c&&(S.s.c=w),E<S.s.r&&(S.s.r=E),V>S.e.r&&(S.e.r=V),P=[],i={t:(m=pe(s[0],!(N={})))["数据类型"]||m["value-type"],v:null},r.cellFormula)if(m.formula&&(m.formula=we(m.formula)),m["number-matrix-columns-spanned"]&&m["number-matrix-rows-spanned"]&&(T=parseInt(m["number-matrix-rows-spanned"],10)||0,k=parseInt(m["number-matrix-columns-spanned"],10)||0,i.F=$t(B={s:{r:E,c:w},e:{r:E+T-1,c:w+k-1}}),R.push([B,i.F])),m.formula)i.f=Po(m.formula);else for(z=0;z<R.length;++z)E>=R[z][0].s.r&&E<=R[z][0].e.r&&w>=R[z][0].s.c&&w<=R[z][0].e.c&&(i.F=R[z][1]);switch((m["number-columns-spanned"]||m["number-rows-spanned"])&&(T=parseInt(m["number-rows-spanned"],10)||0,k=parseInt(m["number-columns-spanned"],10)||0,C.push(B={s:{r:E,c:w},e:{r:E+T-1,c:w+k-1}})),m["number-columns-repeated"]&&(I=parseInt(m["number-columns-repeated"],10)),i.t){case"boolean":i.t="b",i.v=Re(m["boolean-value"]);break;case"float":case"percentage":case"currency":i.t="n",i.v=parseFloat(m.value);break;case"date":i.t="d",i.v=oe(m["date-value"]),r.cellDates||(i.t="n",i.v=G(i.v)),i.z="m/d/yy";break;case"time":i.t="n",i.v=function(e){var t=0,r=0,n=!1,a=e.match(/P([0-9\.]+Y)?([0-9\.]+M)?([0-9\.]+D)?T([0-9\.]+H)?([0-9\.]+M)?([0-9\.]+S)?/);if(!a)throw new Error("|"+e+"| is not an ISO8601 Duration");for(var s=1;s!=a.length;++s)if(a[s]){switch(3<s&&(n=!0),a[s].slice(a[s].length-(r=1))){case"Y":throw new Error("Unsupported ISO Duration Field: "+a[s].slice(a[s].length-1));case"D":r*=24;case"H":r*=60;case"M":if(!n)throw new Error("Unsupported ISO Duration Field: M");r*=60}t+=r*parseInt(a[s],10)}return t}(m["time-value"])/86400;break;case"number":i.t="n",i.v=parseFloat(m["数据数值"]);break;default:if("string"!==i.t&&"text"!==i.t&&i.t)throw new Error("Unsupported value type "+i.t);i.t="s",null!=m["string-value"]&&(g=we(m["string-value"]),v=[])}}else{if(U=!1,"s"===i.t&&(i.v=g||"",v.length&&(i.R=v),U=0==b),F.Target&&(i.l=F),0<P.length&&(i.c=P,P=[]),g&&!1!==r.cellText&&(i.w=g),(!U||r.sheetStubs)&&!(r.sheetRows&&r.sheetRows<=E))for(var W=0;W<A;++W){if(I=parseInt(m["number-columns-repeated"]||"1",10),r.dense)for(p[E+W]||(p[E+W]=[]),p[E+W][w]=0==W?i:le(i);0<--I;)p[E+W][w+I]=le(i);else for(p[jt({r:E+W,c:w})]=i;0<--I;)p[jt({r:E+W,c:w+I})]=le(i);S.e.c<=w&&(S.e.c=w)}w+=(I=parseInt(m["number-columns-repeated"]||"1",10))-1,I=0,i={},g="",v=[]}F={};break;case"document":case"document-content":case"电子表格文档":case"spreadsheet":case"主体":case"scripts":case"styles":case"font-face-decls":if("/"===s[1]){if((n=l.pop())[0]!==s[3])throw"Bad state: "+n}else"/"!==s[0].charAt(s[0].length-2)&&l.push([s[3],!0]);break;case"annotation":if("/"===s[1]){if((n=l.pop())[0]!==s[3])throw"Bad state: "+n;N.t=g,v.length&&(N.R=v),N.a=L,P.push(N)}else"/"!==s[0].charAt(s[0].length-2)&&l.push([s[3],!1]);g=L="",b=M=0,v=[];break;case"creator":"/"===s[1]?L=o.slice(M,s.index):M=s.index+s[0].length;break;case"meta":case"元数据":case"settings":case"config-item-set":case"config-item-map-indexed":case"config-item-map-entry":case"config-item-map-named":case"shapes":case"frame":case"text-box":case"image":case"data-pilot-tables":case"list-style":case"form":case"dde-links":case"event-listeners":case"chart":if("/"===s[1]){if((n=l.pop())[0]!==s[3])throw"Bad state: "+n}else"/"!==s[0].charAt(s[0].length-2)&&l.push([s[3],!1]);g="",b=0,v=[];break;case"scientific-number":case"currency-symbol":case"currency-style":break;case"number-style":case"percentage-style":case"date-style":case"time-style":if("/"===s[1]){if(y[c.name]=f,(n=l.pop())[0]!==s[3])throw"Bad state: "+n}else"/"!==s[0].charAt(s[0].length-2)&&(f="",c=pe(s[0],!1),l.push([s[3],!0]));break;case"script":case"libraries":case"automatic-styles":case"master-styles":break;case"default-style":case"page-layout":case"style":case"map":case"font-face":case"paragraph-properties":case"table-properties":case"table-column-properties":case"table-row-properties":case"table-cell-properties":break;case"number":switch(l[l.length-1][0]){case"time-style":case"date-style":X=pe(s[0],!1),f+=Vc[s[3]]["long"===X.style?1:0]}break;case"fraction":break;case"day":case"month":case"year":case"era":case"day-of-week":case"week-of-year":case"quarter":case"hours":case"minutes":case"seconds":case"am-pm":switch(l[l.length-1][0]){case"time-style":case"date-style":X=pe(s[0],!1),f+=Vc[s[3]]["long"===X.style?1:0]}break;case"boolean-style":case"boolean":case"text-style":break;case"text":if("/>"===s[0].slice(-2))break;if("/"===s[1])switch(l[l.length-1][0]){case"number-style":case"date-style":case"time-style":f+=o.slice(h,s.index)}else h=s.index+s[0].length;break;case"named-range":var X,D=No((X=pe(s[0],!1))["cell-range-address"]),j={Name:X.name,Ref:D[0]+"!"+D[1]};H&&(j.Sheet=d.length),O.Names.push(j);break;case"text-content":case"text-properties":case"embedded-text":break;case"body":case"电子表格":case"forms":case"table-column":case"table-header-rows":case"table-rows":case"table-column-group":case"table-header-columns":case"table-columns":case"null-date":case"graphic-properties":case"calculation-settings":case"named-expressions":case"label-range":case"label-ranges":case"named-expression":case"sort":case"sort-by":case"sort-groups":case"tab":case"line-break":case"span":break;case"p":case"文本串":"/"!==s[1]||m&&m["string-value"]?(pe(s[0],!1),b=s.index+s[0].length):(j=function(e){e=e.replace(/[\t\r\n]/g," ").trim().replace(/ +/g," ").replace(/<text:s\/>/g," ").replace(/<text:s text:c="(\d+)"\/>/g,function(e,t){return Array(parseInt(t,10)+1).join(" ")}).replace(/<text:tab[^>]*\/>/g,"\t").replace(/<text:line-break\/>/g,"\n");return[we(e.replace(/<[^>]*>/g,""))]}(o.slice(b,s.index)),g=(0<g.length?g+"\n":"")+j[0]);break;case"s":break;case"database-range":if("/"===s[1])break;try{u[(D=No(pe(s[0])["target-range-address"]))[0]]["!autofilter"]={ref:D[1]}}catch(e){}break;case"date":case"object":break;case"title":case"标题":case"desc":case"binary-data":case"table-source":case"scenario":case"iteration":case"content-validations":case"content-validation":case"help-message":case"error-message":case"database-ranges":case"filter":case"filter-and":case"filter-or":case"filter-condition":case"list-level-style-bullet":case"list-level-style-number":case"list-level-properties":break;case"sender-firstname":case"sender-lastname":case"sender-initials":case"sender-title":case"sender-position":case"sender-email":case"sender-phone-private":case"sender-fax":case"sender-company":case"sender-phone-work":case"sender-street":case"sender-city":case"sender-postal-code":case"sender-country":case"sender-state-or-province":case"author-name":case"author-initials":case"chapter":case"file-name":case"template-name":case"sheet-name":case"event-listener":break;case"initial-creator":case"creation-date":case"print-date":case"generator":case"document-statistic":case"user-defined":case"editing-duration":case"editing-cycles":case"config-item":case"page-number":case"page-count":case"time":case"cell-range-source":case"detective":case"operation":case"highlighted-range":break;case"data-pilot-table":case"source-cell-range":case"source-service":case"data-pilot-field":case"data-pilot-level":case"data-pilot-subtotals":case"data-pilot-subtotal":case"data-pilot-members":case"data-pilot-member":case"data-pilot-display-info":case"data-pilot-sort-info":case"data-pilot-layout-info":case"data-pilot-field-reference":case"data-pilot-groups":case"data-pilot-group":case"data-pilot-group-member":case"rect":break;case"dde-connection-decls":case"dde-connection-decl":case"dde-link":case"dde-source":case"properties":case"property":break;case"a":if("/"!==s[1]){if(!(F=pe(s[0],!1)).href)break;F.Target=F.href,delete F.href,"#"==F.Target.charAt(0)&&-1<F.Target.indexOf(".")&&(D=No(F.Target.slice(1)),F.Target="#"+D[0]+"!"+D[1])}break;case"table-protection":case"data-pilot-grand-total":case"office-document-common-attrs":break;default:switch(s[2]){case"dc:":case"calcext:":case"loext:":case"ooo:":case"chartooo:":case"draw:":case"style:":case"chart:":case"form:":case"uof:":case"表:":case"字:":break;default:if(r.WTF)throw new Error(s)}}e={Sheets:u,SheetNames:d,Workbook:O};return r.bookSheets&&delete e.Sheets,e});function Xc(e,t){t=t||{};var r=!!P(e,"objectdata");r&&function(e,t){for(var r,n,a=rc(e);r=nc.exec(a);)switch(r[3]){case"manifest":break;case"file-entry":if("/"==(n=pe(r[0],!1)).path&&n.type!==Kr)throw new Error("This OpenDocument is not a spreadsheet");break;case"encryption-data":case"algorithm":case"start-key-generation":case"key-derivation":throw new Error("Unsupported ODS Encryption");default:if(t&&t.WTF)throw r}}(M(e,"META-INF/manifest.xml"),t);var n=U(e,"content.xml");if(!n)throw new Error("Missing content.xml in "+(r?"ODS":"UOF")+" file");t=Wc(r?n:Oe(n),t);return P(e,"meta.xml")&&(t.Props=tn(M(e,"meta.xml"))),t}function jc(e,t){return Wc(e,t)}var Gc,$c,Yc,Kc,Zc=(Gc="<office:document-styles "+je({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","office:version":"1.2"})+"></office:document-styles>",function(){return z+Gc}),Qc=function(e,t){var r=[z],n=je({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:meta":"urn:oasis:names:tc:opendocument:xmlns:meta:1.0","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:presentation":"urn:oasis:names:tc:opendocument:xmlns:presentation:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:chart":"urn:oasis:names:tc:opendocument:xmlns:chart:1.0","xmlns:dr3d":"urn:oasis:names:tc:opendocument:xmlns:dr3d:1.0","xmlns:math":"http://www.w3.org/1998/Math/MathML","xmlns:form":"urn:oasis:names:tc:opendocument:xmlns:form:1.0","xmlns:script":"urn:oasis:names:tc:opendocument:xmlns:script:1.0","xmlns:ooo":"http://openoffice.org/2004/office","xmlns:ooow":"http://openoffice.org/2004/writer","xmlns:oooc":"http://openoffice.org/2004/calc","xmlns:dom":"http://www.w3.org/2001/xml-events","xmlns:xforms":"http://www.w3.org/2002/xforms","xmlns:xsd":"http://www.w3.org/2001/XMLSchema","xmlns:xsi":"http://www.w3.org/2001/XMLSchema-instance","xmlns:sheet":"urn:oasis:names:tc:opendocument:sh33tjs:1.0","xmlns:rpt":"http://openoffice.org/2005/report","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","xmlns:xhtml":"http://www.w3.org/1999/xhtml","xmlns:grddl":"http://www.w3.org/2003/g/data-view#","xmlns:tableooo":"http://openoffice.org/2009/table","xmlns:drawooo":"http://openoffice.org/2010/draw","xmlns:calcext":"urn:org:documentfoundation:names:experimental:calc:xmlns:calcext:1.0","xmlns:loext":"urn:org:documentfoundation:names:experimental:office:xmlns:loext:1.0","xmlns:field":"urn:openoffice:names:experimental:ooo-ms-interop:xmlns:field:1.0","xmlns:formx":"urn:openoffice:names:experimental:ooxml-odf-interop:xmlns:form:1.0","xmlns:css3t":"http://www.w3.org/TR/css3-text/","office:version":"1.2"}),a=je({"xmlns:config":"urn:oasis:names:tc:opendocument:xmlns:config:1.0","office:mimetype":"application/vnd.oasis.opendocument.spreadsheet"});"fods"==t.bookType?r.push("<office:document"+n+a+">\n"):r.push("<office:document-content"+n+">\n"),(n=r).push(" <office:automatic-styles>\n"),n.push('  <number:date-style style:name="N37" number:automatic-order="true">\n'),n.push('   <number:month number:style="long"/>\n'),n.push("   <number:text>/</number:text>\n"),n.push('   <number:day number:style="long"/>\n'),n.push("   <number:text>/</number:text>\n"),n.push("   <number:year/>\n"),n.push("  </number:date-style>\n"),n.push('  <style:style style:name="ce1" style:family="table-cell" style:parent-style-name="Default" style:data-style-name="N37"/>\n'),n.push(" </office:automatic-styles>\n"),r.push("  <office:body>\n"),r.push("    <office:spreadsheet>\n");for(var s=0;s!=e.SheetNames.length;++s)r.push(Jc(e.Sheets[e.SheetNames[s]],e,s));return r.push("    </office:spreadsheet>\n"),r.push("  </office:body>\n"),"fods"==t.bookType?r.push("</office:document>"):r.push("</office:document-content>"),r.join("")};function Jc(e,t,r){var n=[];n.push('      <table:table table:name="'+ye(t.SheetNames[r])+'">\n');for(var a=0,s=0,i=Gt(e["!ref"]),o=e["!merges"]||[],l=0,c=Array.isArray(e),a=0;a<i.s.r;++a)n.push("        <table:table-row></table:table-row>\n");for(;a<=i.e.r;++a){for(n.push("        <table:table-row>\n"),s=0;s<i.s.c;++s)n.push(Yc);for(;s<=i.e.c;++s){for(var f=!1,h={},u="",l=0;l!=o.length;++l)if(!(o[l].s.c>s||o[l].s.r>a||o[l].e.c<s||o[l].e.r<a)){o[l].s.c==s&&o[l].s.r==a||(f=!0),h["table:number-columns-spanned"]=o[l].e.c-o[l].s.c+1,h["table:number-rows-spanned"]=o[l].e.r-o[l].s.r+1;break}if(f)n.push(Kc);else{var d=jt({r:a,c:s}),p=c?(e[a]||[])[s]:e[d];if(p&&p.f&&(h["table:formula"]=ye(("of:="+p.f.replace(Gi,"$1[.$2$3$4$5]").replace(/\]:\[/g,":")).replace(/;/g,"|").replace(/,/g,";")),p.F&&p.F.slice(0,d.length)==d&&(m=Gt(p.F),h["table:number-matrix-columns-spanned"]=m.e.c-m.s.c+1,h["table:number-matrix-rows-spanned"]=m.e.r-m.s.r+1)),p){switch(p.t){case"b":u=p.v?"TRUE":"FALSE",h["office:value-type"]="boolean",h["office:boolean-value"]=p.v?"true":"false";break;case"n":u=p.w||String(p.v||0),h["office:value-type"]="float",h["office:value"]=p.v||0;break;case"s":case"str":u=p.v,h["office:value-type"]="string";break;case"d":u=p.w||oe(p.v).toISOString(),h["office:value-type"]="date",h["office:date-value"]=oe(p.v).toISOString(),h["table:style-name"]="ce1";break;default:n.push(Yc);continue}var m,d=$c(u);p.l&&p.l.Target&&(d=Ge("text:a",d,{"xlink:href":m="#"==(m=p.l.Target).charAt(0)?"#"+m.slice(1).replace(/\./,"!"):m})),n.push("          "+Ge("table:table-cell",Ge("text:p",d,{}),h)+"\n")}else n.push(Yc)}}n.push("        </table:table-row>\n")}return n.push("      </table:table>\n"),n.join("")}function qc(e,t){if("fods"==t.bookType)return Qc(e,t);var r=new I,n=[],a=[];return r.file("mimetype","application/vnd.oasis.opendocument.spreadsheet"),r.file("content.xml",Qc(e,t)),n.push(["content.xml","text/xml"]),a.push(["content.xml","ContentFile"]),r.file("styles.xml",Zc(e,t)),n.push(["styles.xml","text/xml"]),a.push(["styles.xml","StylesFile"]),r.file("meta.xml",Jr()),n.push(["meta.xml","text/xml"]),a.push(["meta.xml","MetadataFile"]),r.file("manifest.rdf",function(e){var t=[z];t.push('<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">\n');for(var r=0;r!=e.length;++r)t.push(Zr(e[r][0],e[r][1])),t.push(['  <rdf:Description rdf:about="">\n','    <ns0:hasPart xmlns:ns0="http://docs.oasis-open.org/ns/office/1.2/meta/pkg#" rdf:resource="'+e[r][0]+'"/>\n',"  </rdf:Description>\n"].join(""));return t.push(Zr("","Document","pkg")),t.push("</rdf:RDF>"),t.join("")}(a)),n.push(["manifest.rdf","application/rdf+xml"]),r.file("META-INF/manifest.xml",function(e){var t=[z];t.push('<manifest:manifest xmlns:manifest="urn:oasis:names:tc:opendocument:xmlns:manifest:1.0" manifest:version="1.2">\n'),t.push('  <manifest:file-entry manifest:full-path="/" manifest:version="1.2" manifest:media-type="application/vnd.oasis.opendocument.spreadsheet"/>\n');for(var r=0;r<e.length;++r)t.push('  <manifest:file-entry manifest:full-path="'+e[r][0]+'" manifest:media-type="'+e[r][1]+'"/>\n');return t.push("</manifest:manifest>"),t.join("")}(n)),r}function ef(n){return function(e,t){var r=function(e,t){if(!t)return 0;if(-1==(e=e.SheetNames.indexOf(t)))throw new Error("Sheet not found: "+t);return e}(e,t.sheet);return n.from_sheet(e.Sheets[e.SheetNames[r]],t,e)}}var tf=ef(Nc),rf=ef({from_sheet:Df}),nf=ef(Ca),af=ef(ka),sf=ef(za),of=ef(Ns),lf=ef({from_sheet:Pf}),cf=ef(_a),ff=ef(Na);function hf(n){return function(e){for(var t=0;t!=n.length;++t){var r=n[t];void 0===e[r[0]]&&(e[r[0]]=r[1]),"n"===r[2]&&(e[r[0]]=Number(e[r[0]]))}}}var uf=hf([["cellNF",!(Kc="          <table:covered-table-cell/>\n")],["cellHTML",!0],["cellFormula",!0],["cellStyles",!(Yc="          <table:table-cell />\n")],["cellText",!0],["cellDates",!($c=function(e){return ye(e).replace(/  +/g,function(e){return'<text:s text:c="'+e.length+'"/>'}).replace(/\t/g,"<text:tab/>").replace(/\n/g,"<text:line-break/>").replace(/^ /,"<text:s/>").replace(/ $/,"<text:s/>")})],["sheetStubs",!1],["sheetRows",0,"n"],["bookDeps",!1],["bookSheets",!1],["bookProps",!1],["bookFiles",!1],["bookVBA",!1],["password",""],["WTF",!1]]),df=hf([["cellDates",!1],["bookSST",!1],["bookType","xlsx"],["compression",!1],["WTF",!1]]);function pf(t,e){if(!t)return 0;try{t=e.map(function(e){return e.id||(e.id=e.strRelID),[e.name,t["!id"][e.id].Target,(e=t["!id"][e.id].Type,-1<Wr.WS.indexOf(e)?"sheet":Wr.CS&&e==Wr.CS?"chart":Wr.DS&&e==Wr.DS?"dialog":Wr.MS&&e==Wr.MS?"macro":e&&e.length?e:"sheet")]})}catch(e){return null}return t&&0!==t.length?t:null}function mf(e){return"/"==e.charAt(0)?e.slice(1):e}function gf(t,r){if(ne(ue),uf(r=r||{}),P(t,"META-INF/manifest.xml"))return Xc(t,r);if(P(t,"objectdata.xml"))return Xc(t,r);if(P(t,"Index/Document.iwa"))throw new Error("Unsupported NUMBERS file");var e=function(e){for(var t=de(e.files),r=[],n=0;n<t.length;++n)"/"!=t[n].slice(-1)&&r.push(t[n]);return r.sort()}(t),n=function(e){var r=Ur();if(!e||!e.match)return r;var n={};if((e.match(W)||[]).forEach(function(e){var t=pe(e);switch(t[0].replace(X,"<")){case"<?xml":break;case"<Types":r.xmlns=t["xmlns"+(t[0].match(/<(\w+):/)||["",""])[1]];break;case"<Default":n[t.Extension]=t.ContentType;break;case"<Override":void 0!==r[Nr[t.ContentType]]&&r[Nr[t.ContentType]].push(t.PartName)}}),r.xmlns!==Ye.CT)throw new Error("Unknown Namespace: "+r.xmlns);return r.calcchain=0<r.calcchains.length?r.calcchains[0]:"",r.sst=0<r.strs.length?r.strs[0]:"",r.style=0<r.styles.length?r.styles[0]:"",r.defaults=n,delete r.calcchains,r}(U(t,"[Content_Types].xml")),a=!1;if(0===n.workbooks.length&&M(t,u="xl/workbook.xml",!0)&&n.workbooks.push(u),0===n.workbooks.length){if(!M(t,u="xl/workbook.bin",!0))throw new Error("Could not find workbook");n.workbooks.push(u),a=!0}"bin"==n.workbooks[0].slice(-3)&&(a=!0);var s={},i={};if(!r.bookSheets&&!r.bookProps){if(Lo=[],n.sst)try{Lo=Yl(M(t,mf(n.sst)),n.sst,r)}catch(e){if(r.WTF)throw e}r.cellStyles&&n.themes.length&&(d=U(t,n.themes[0].replace(/^\//,""),!0)||"",n.themes[0],s=Ii(d,r)),n.style&&(i=$l(M(t,mf(n.style)),n.style,s,r))}n.links.map(function(e){return Zl(M(t,mf(e)),e,r)});var o,l,c,f=Xl(M(t,mf(n.workbooks[0])),n.workbooks[0],r),h={},u="";n.coreprops.length&&((u=M(t,mf(n.coreprops[0]),!0))&&(h=tn(u)),0!==n.extprops.length&&(u=M(t,mf(n.extprops[0]),!0))&&(p=r,c={},l=(l=h)||{},o=Oe(o=u),an.forEach(function(e){switch(e[2]){case"string":l[e[1]]=(o.match(Le(e[0]))||[])[1];break;case"bool":l[e[1]]="true"===(o.match(Le(e[0]))||[])[1];break;case"raw":var t=o.match(new RegExp("<"+e[0]+"[^>]*>([\\s\\S]*?)</"+e[0]+">"));t&&0<t.length&&(c[e[1]]=t[1])}}),c.HeadingPairs&&c.TitlesOfParts&&sn(c.HeadingPairs,c.TitlesOfParts,l,p)));var d={};r.bookSheets&&!r.bookProps||0!==n.custprops.length&&(u=U(t,mf(n.custprops[0]),!0))&&(d=function(e,t){var r={},n="",a=e.match(ln);if(a)for(var s=0;s!=a.length;++s){var i=a[s],o=pe(i);switch(o[0]){case"<?xml":case"<Properties":break;case"<property":n=o.name;break;case"</property>":n=null;break;default:if(0===i.indexOf("<vt:")){var l=i.split(">"),c=l[0].slice(4),f=l[1];switch(c){case"lpstr":case"bstr":case"lpwstr":r[n]=we(f);break;case"bool":r[n]=Re(f);break;case"i1":case"i2":case"i4":case"i8":case"int":case"uint":r[n]=parseInt(f,10);break;case"r4":case"r8":case"decimal":r[n]=parseFloat(f);break;case"filetime":case"date":r[n]=oe(f);break;case"cy":case"error":r[n]=we(f);break;default:if("/"==c.slice(-1))break;t.WTF&&"undefined"!=typeof console&&console.warn("Unexpected",i,c,l)}}else if("</"!==i.slice(0,2)&&t.WTF)throw new Error(i)}}return r}(u,r));var p={};if((r.bookSheets||r.bookProps)&&(f.Sheets?m=f.Sheets.map(function(e){return e.name}):h.Worksheets&&0<h.SheetNames.length&&(m=h.SheetNames),r.bookProps&&(p.Props=h,p.Custprops=d),r.bookSheets&&void 0!==m&&(p.SheetNames=m),r.bookSheets?p.SheetNames:r.bookProps))return p;var m={},u={};r.bookDeps&&n.calcchain&&(u=Kl(M(t,mf(n.calcchain)),n.calcchain));var g,b,v=0,E={},w=f.Sheets;h.Worksheets=w.length,h.SheetNames=[];for(var S=0;S!=w.length;++S)h.SheetNames[S]=w[S].name;var _=a?"bin":"xml",a=n.workbooks[0].lastIndexOf("/"),y=(n.workbooks[0].slice(0,a+1)+"_rels/"+n.workbooks[0].slice(a+1)+".rels").replace(/^\//,"");P(t,y)||(y="xl/_rels/workbook."+_+".rels");for(var C=(C=jr(U(t,y,!0),y))&&pf(C,f.Sheets),B=M(t,"xl/worksheets/sheet.xml",!0)?1:0,v=0;v!=h.Worksheets;++v){var T="sheet";C&&C[v]?(g="xl/"+C[v][1].replace(/[\/]?xl\//,""),P(t,g)||(g=C[v][1]),P(t,g)||(g=y.replace(/_rels\/.*$/,"")+C[v][1]),T=C[v][2]):g=(g="xl/worksheets/sheet"+(v+1-B)+"."+_).replace(/sheet0\./,"sheet."),b=g.replace(/^(.*)(\/)([^\/]*)$/,"$1/_rels/$3.rels"),function(e,t,r,n,a,s,i,o,l,c,f,h){try{s[n]=jr(U(e,r,!0),t);var u=M(e,t);switch(o){case"sheet":b=jl(u,t,a,l,s[n],c,f,h);break;case"chart":if(!(b=Gl(u,t,a,l,s[n],c))||!b["!chart"])break;var d=H(b["!chart"].Target,t),p=Xr(d),m=H((v=U(e,d,!0),E=jr(U(e,p,!0),d),v?(v=(v.match(/<c:chart [^>]*r:id="([^"]*)"/)||["",""])[1],E["!id"][v].Target):"??"),d),g=Xr(m),b=kl(U(e,m,!0),0,0,jr(U(e,g,!0),m),0,b);break;case"macro":s[n],t.slice(-4),b={"!type":"macro"};break;case"dialog":s[n],t.slice(-4),b={"!type":"dialog"}}i[n]=b}catch(e){if(l.WTF)throw e}var v,E}(t,g,b,h.SheetNames[v],v,E,m,T,r,f,s,i)}return n.comments&&Pi(t,n.comments,m,E,r),p={Directory:n,Workbook:f,Props:h,Custprops:d,Deps:u,Sheets:m,SheetNames:h.SheetNames,Strings:Lo,Styles:i,Themes:s,SSF:ue.get_table()},r.bookFiles&&(p.keys=e,p.files=t.files),r.bookVBA&&(0<n.vba.length?p.vbaraw=M(t,mf(n.vba[0]),!0):n.defaults&&n.defaults.bin===Hi&&(p.vbaraw=M(t,"xl/vbaProject.bin",!0))),p}function bf(e,t){var r,n,a=t||{},s="Workbook",t=ie.find(e,s);try{if(s="/!DataSpaces/Version",!(t=ie.find(e,s))||!t.content)throw new Error("ECMA-376 Encrypted file missing "+s);if(r=t.content,(n={}).id=r.read_shift(0,"lpp4"),n.R=Ss(r,4),n.U=Ss(r,4),n.W=Ss(r,4),s="/!DataSpaces/DataSpaceMap",!(t=ie.find(e,s))||!t.content)throw new Error("ECMA-376 Encrypted file missing "+s);var i=_s(t.content);if(1!==i.length||1!==i[0].comps.length||0!==i[0].comps[0].t||"StrongEncryptionDataSpace"!==i[0].name||"EncryptedPackage"!==i[0].comps[0].v)throw new Error("ECMA-376 Encrypted file bad "+s);if(s="/!DataSpaces/DataSpaceInfo/StrongEncryptionDataSpace",!(t=ie.find(e,s))||!t.content)throw new Error("ECMA-376 Encrypted file missing "+s);var o=function(e){var t=[];e.l+=4;for(var r=e.read_shift(4);0<r--;)t.push(e.read_shift(0,"lpp4"));return t}(t.content);if(1!=o.length||"StrongEncryptionTransform"!=o[0])throw new Error("ECMA-376 Encrypted file bad "+s);if(s="/!DataSpaces/TransformInfo/StrongEncryptionTransform/!Primary",!(t=ie.find(e,s))||!t.content)throw new Error("ECMA-376 Encrypted file missing "+s);ys(t.content)}catch(e){}if(!(t=ie.find(e,s="/EncryptionInfo"))||!t.content)throw new Error("ECMA-376 Encrypted file missing "+s);o=Ts(t.content),s="/EncryptedPackage";if(!(t=ie.find(e,s))||!t.content)throw new Error("ECMA-376 Encrypted file missing "+s);if(4==o[0]&&"undefined"!=typeof decrypt_agile)return decrypt_agile(o[1],t.content,a.password||"",a);if(2==o[0]&&"undefined"!=typeof decrypt_std76)return decrypt_std76(o[1],t.content,a.password||"",a);throw new Error("File is password-protected")}function vf(e,t){if(Di=1024,"ods"==t.bookType)return qc(e,t);e&&!e.SSF&&(e.SSF=ue.get_table()),e&&e.SSF&&(ne(ue),ue.load_table(e.SSF),t.revssf=_(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF),t.rels={},t.wbrels={},t.Strings=[],t.Strings.Count=0,t.Strings.Unique=0,Uo?t.revStrings=new Map:(t.revStrings={},t.revStrings.foo=[],delete t.revStrings.foo);var r="xlsb"==t.bookType?"bin":"xml",n=-1<zi.indexOf(t.bookType),a=Ur();df(t=t||{});var s,i,o,l,c,f,h=new I,u="",d=0;if(t.cellXfs=[],Wo(t.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={}),u="docProps/core.xml",h.file(u,function(e,t){var r=t||{},n=[z,rn],a={};if(!e&&!r.Props)return n.join("");e&&(null!=e.CreatedDate&&nn("dcterms:created","string"==typeof e.CreatedDate?e.CreatedDate:$e(e.CreatedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},n,a),null!=e.ModifiedDate&&nn("dcterms:modified","string"==typeof e.ModifiedDate?e.ModifiedDate:$e(e.ModifiedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},n,a));for(var s=0;s!=qr.length;++s){var i=qr[s],o=r.Props&&null!=r.Props[i[1]]?r.Props[i[1]]:e?e[i[1]]:null;!0===o?o="1":!1===o?o="0":"number"==typeof o&&(o=String(o)),null!=o&&nn(i[0],o,null,n,a)}return 2<n.length&&(n[n.length]="</cp:coreProperties>",n[1]=n[1].replace("/>",">")),n.join("")}(e.Props,t)),a.coreprops.push(u),Yr(t.rels,2,u,Wr.CORE_PROPS),u="docProps/app.xml",!e.Props||!e.Props.SheetNames)if(e.Workbook&&e.Workbook.Sheets){for(var p=[],m=0;m<e.SheetNames.length;++m)2!=(e.Workbook.Sheets[m]||{}).Hidden&&p.push(e.SheetNames[m]);e.Props.SheetNames=p}else e.Props.SheetNames=e.SheetNames;for(e.Props.Worksheets=e.Props.SheetNames.length,h.file(u,(s=e.Props,i=[],o=Ge,(s=s||{}).Application="SheetJS",i[i.length]=z,i[i.length]=on,an.forEach(function(e){if(void 0!==s[e[1]]){var t;switch(e[2]){case"string":t=String(s[e[1]]);break;case"bool":t=s[e[1]]?"true":"false"}void 0!==t&&(i[i.length]=o(e[0],t))}}),i[i.length]=o("HeadingPairs",o("vt:vector",o("vt:variant","<vt:lpstr>Worksheets</vt:lpstr>")+o("vt:variant",o("vt:i4",String(s.Worksheets))),{size:2,baseType:"variant"})),i[i.length]=o("TitlesOfParts",o("vt:vector",s.SheetNames.map(function(e){return"<vt:lpstr>"+ye(e)+"</vt:lpstr>"}).join(""),{size:s.Worksheets,baseType:"lpstr"})),2<i.length&&(i[i.length]="</Properties>",i[1]=i[1].replace("/>",">")),i.join(""))),a.extprops.push(u),Yr(t.rels,3,u,Wr.EXT_PROPS),e.Custprops!==e.Props&&0<de(e.Custprops||{}).length&&(h.file(u="docProps/custom.xml",fn(e.Custprops)),a.custprops.push(u),Yr(t.rels,4,u,Wr.CUST_PROPS)),d=1;d<=e.SheetNames.length;++d){var g,b,v,E={"!id":{}},w=e.Sheets[e.SheetNames[d-1]],u=((w||{})["!type"],"xl/worksheets/sheet"+d+"."+r);h.file(u,(b=d-1,g=t,v=e,l=E,(".bin"===u.slice(-4)?Tl:dl)(b,g,v,l))),a.sheets.push(u),Yr(t.wbrels,-1,"worksheets/sheet"+d+"."+r,Wr.WS[0]),w&&(b=!1,(g=w["!comments"])&&0<g.length&&(h.file(v="xl/comments"+d+"."+r,(l=g,g=t,(".bin"===v.slice(-4)?Ui:Li)(l,g))),a.comments.push(v),Yr(E,-1,"../comments"+d+"."+r,Wr.CMNT),b=!0),w["!legacy"]&&b&&h.file("xl/drawings/vmlDrawing"+d+".vml",function(e,t){for(var r=[21600,21600],n=["m0,0l0",r[1],r[0],r[1],r[0],"0xe"].join(","),a=[Ge("xml",null,{"xmlns:v":Ke.v,"xmlns:o":Ke.o,"xmlns:x":Ke.x,"xmlns:mv":Ke.mv}).replace(/\/>/,">"),Ge("o:shapelayout",Ge("o:idmap",null,{"v:ext":"edit",data:e}),{"v:ext":"edit"}),Ge("v:shapetype",[Ge("v:stroke",null,{joinstyle:"miter"}),Ge("v:path",null,{gradientshapeok:"t","o:connecttype":"rect"})].join(""),{id:"_x0000_t202","o:spt":202,coordsize:r.join(","),path:n})];Di<1e3*e;)Di+=1e3;return t.forEach(function(e){var t=Xt(e[0]);a=a.concat(["<v:shape"+je({id:"_x0000_s"+ ++Di,type:"#_x0000_t202",style:"position:absolute; margin-left:80pt;margin-top:5pt;width:104pt;height:64pt;z-index:10"+(e[1].hidden?";visibility:hidden":""),fillcolor:"#ECFAD4",strokecolor:"#edeaa1"})+">",Ge("v:fill",Ge("o:fill",null,{type:"gradientUnscaled","v:ext":"view"}),{color2:"#BEFF82",angle:"-180",type:"gradient"}),Ge("v:shadow",null,{on:"t",obscured:"t"}),Ge("v:path",null,{"o:connecttype":"none"}),'<v:textbox><div style="text-align:left"></div></v:textbox>','<x:ClientData ObjectType="Note">',"<x:MoveWithCells/>","<x:SizeWithCells/>",Xe("x:Anchor",[t.c,0,t.r,0,t.c+3,100,t.r+5,100].join(",")),Xe("x:AutoFill","False"),Xe("x:Row",String(t.r)),Xe("x:Column",String(t.c)),e[1].hidden?"":"<x:Visible/>","</x:ClientData>","</v:shape>"])}),a.push("</xml>"),a.join("")}(d,w["!comments"])),delete w["!comments"],delete w["!legacy"]),E["!id"].rId1&&h.file(Xr(u),$r(E))}return null!=t.Strings&&0<t.Strings.length&&(u="xl/sharedStrings."+r,h.file(u,(c=t.Strings,f=t,(".bin"===u.slice(-4)?Es:bs)(c,f))),a.strs.push(u),Yr(t.wbrels,-1,"sharedStrings."+r,Wr.SST)),u="xl/workbook."+r,h.file(u,(c=e,f=t,(".bin"===u.slice(-4)?Wl:Hl)(c,f))),a.workbooks.push(u),Yr(t.rels,1,u,Wr.WB),u="xl/theme/theme1.xml",h.file(u,Ri(e.Themes,t)),a.themes.push(u),Yr(t.wbrels,-1,"theme/theme1.xml",Wr.THEME),u="xl/styles."+r,h.file(u,(c=e,f=t,(".bin"===u.slice(-4)?_i:fi)(c,f))),a.styles.push(u),Yr(t.wbrels,-1,"styles."+r,Wr.STY),e.vbaraw&&n&&(u="xl/vbaProject.bin",h.file(u,e.vbaraw),a.vba.push(u),Yr(t.wbrels,-1,"vbaProject.bin",Wr.VBA)),h.file("[Content_Types].xml",Vr(a,t)),h.file("_rels/.rels",$r(t.rels)),h.file("xl/_rels/workbook."+r+".rels",$r(t.wbrels)),delete t.revssf,delete t.ssf,h}function Ef(e,t){var r="";switch((t||{}).type||"base64"){case"buffer":return[e[0],e[1],e[2],e[3]];case"base64":r=Y.decode(e.slice(0,24));break;case"binary":r=e;break;case"array":return[e[0],e[1],e[2],e[3]];default:throw new Error("Unrecognized type "+(t&&t.type||"undefined"))}return[r.charCodeAt(0),r.charCodeAt(1),r.charCodeAt(2),r.charCodeAt(3)]}function wf(e,t){var r=0;e:for(;r<e.length;)switch(e.charCodeAt(r)){case 10:case 13:case 32:++r;break;case 60:return sc(e.slice(r),t);default:break e}return za.to_workbook(e,t)}function Sf(e,t,r,n){return n?(r.type="string",za.to_workbook(e,r)):za.to_workbook(t,r)}function _f(e,t){if(c(),"undefined"!=typeof ArrayBuffer&&e instanceof ArrayBuffer)return _f(new Uint8Array(e),t);var r,n,a,s=e,i=!1,o=t||{};if(Mo={},o.dateNF&&(Mo.dateNF=o.dateNF),o.type||(o.type=K&&Buffer.isBuffer(e)?"buffer":"base64"),"file"==o.type&&(o.type=K?"buffer":"binary",s=function(e){if(void 0!==b)return b.readFileSync(e);if("undefined"!=typeof $&&"undefined"!=typeof File&&"undefined"!=typeof Folder)try{var t=File(e);t.open("r"),t.encoding="binary";var r=t.read();return t.close(),r}catch(e){if(!e.message||!e.message.match(/onstruct/))throw e}throw new Error("Cannot access file "+e)}(e)),"string"==o.type&&(i=!0,o.type="binary",o.codepage=65001,s=(l=e).match(/[^\x00-\x7F]/)?Fe(l):l),"array"==o.type&&"undefined"!=typeof Uint8Array&&e instanceof Uint8Array&&"undefined"!=typeof ArrayBuffer){var l=new ArrayBuffer(3),l=new Uint8Array(l);if(l.foo="bar",!l.foo)return(o=le(o)).type="array",_f(p(s),o)}switch((r=Ef(s,o))[0]){case 208:return n=ie.read(s,o),a=o,(ie.find(n,"EncryptedPackage")?bf:bc)(n,a);case 9:return bc(s,o);case 60:return sc(s,o);case 73:if(68===r[1])return function(t,r){var n=r||{},a=!!n.WTF;n.WTF=!0;try{var e=Ca.to_workbook(t,n);return n.WTF=a,e}catch(e){if(n.WTF=a,!e.message.match(/SYLK bad record ID/)&&a)throw e;return za.to_workbook(t,r)}}(s,o);break;case 84:if(65===r[1]&&66===r[2]&&76===r[3])return ka.to_workbook(s,o);break;case 80:return 75===r[1]&&r[2]<9&&r[3]<9?function(e,t){var r,n=e,a=t||{};switch(a.type||(a.type=K&&Buffer.isBuffer(e)?"buffer":"base64"),a.type){case"base64":r=new I(n,{base64:!0});break;case"binary":case"array":r=new I(n,{base64:!1});break;case"buffer":r=new I(n);break;default:throw new Error("Unrecognized type "+a.type)}return gf(r,a)}(s,o):Sf(e,s,o,i);case 239:return 60===r[3]?sc(s,o):Sf(e,s,o,i);case 255:if(254===r[1])return n=s,"base64"==(a=o).type&&(n=Y.decode(n)),n=cptable.utils.decode(1200,n.slice(2),"str"),a.type="binary",wf(n,a);break;case 0:if(0===r[1]&&2<=r[2]&&0===r[3])return Ya.to_workbook(s,o);break;case 3:case 131:case 139:case 140:return _a.to_workbook(s,o);case 123:if(92===r[1]&&114===r[2]&&116===r[3])return Ns.to_workbook(s,o);break;case 10:case 13:case 32:return function(e,t){var r="",n=Ef(e,t);switch(t.type){case"base64":r=Y.decode(e);break;case"binary":r=e;break;case"buffer":r=e.toString("binary");break;case"array":r=k(e);break;default:throw new Error("Unrecognized type "+t.type)}return wf(r=239==n[0]&&187==n[1]&&191==n[2]?Oe(r):r,t)}(s,o)}return r[2]<=12&&r[3]<=31?_a.to_workbook(s,o):Sf(e,s,o,i)}function yf(e,t){t=t||{};return t.type="file",_f(e,t)}function Cf(e,t){switch(t.type){case"base64":case"binary":break;case"buffer":case"array":t.type="";break;case"file":return E(t.file,ie.write(e,{type:K?"buffer":""}));case"string":throw new Error("'string' output type invalid for '"+t.bookType+"' files");default:throw new Error("Unrecognized type "+t.type)}return ie.write(e,t)}function Bf(e,t,r){var n=(r=r||"")+e;switch(t.type){case"base64":return Y.encode(Fe(n));case"binary":return Fe(n);case"string":return e;case"file":return E(t.file,n,"utf8");case"buffer":return K?s(n,"utf8"):Bf(n,{type:"binary"}).split("").map(function(e){return e.charCodeAt(0)})}throw new Error("Unrecognized type "+t.type)}function Tf(e,t){switch(t.type){case"string":case"base64":case"binary":for(var r="",n=0;n<e.length;++n)r+=String.fromCharCode(e[n]);return"base64"==t.type?Y.encode(r):"string"==t.type?Oe(r):r;case"file":return E(t.file,e);case"buffer":return e;default:throw new Error("Unrecognized type "+t.type)}}function kf(e,t){Ll(e);var r,n=t||{};if("array"==n.type){n.type="binary";t=kf(e,n);return n.type="array",i(t)}switch(n.bookType||"xlsb"){case"xml":case"xlml":return Bf(hc(e,n),n);case"slk":case"sylk":return Bf(nf(e,n),n);case"htm":case"html":return Bf(tf(e,n),n);case"txt":return function(e,t){switch(t.type){case"base64":return Y.encode(e);case"binary":case"string":return e;case"file":return E(t.file,e,"binary");case"buffer":return K?s(e,"binary"):e.split("").map(function(e){return e.charCodeAt(0)})}throw new Error("Unrecognized type "+t.type)}(lf(e,n),n);case"csv":return Bf(rf(e,n),n,"\ufeff");case"dif":return Bf(af(e,n),n);case"dbf":return Tf(cf(e,n),n);case"prn":return Bf(sf(e,n),n);case"rtf":return Bf(of(e,n),n);case"eth":return Bf(ff(e,n),n);case"fods":return Bf(qc(e,n),n);case"biff2":n.biff||(n.biff=2);case"biff3":n.biff||(n.biff=3);case"biff4":return n.biff||(n.biff=4),Tf(Fc(e,n),n);case"biff5":n.biff||(n.biff=5);case"biff8":case"xla":case"xls":return n.biff||(n.biff=8),Cf(vc(e,r=(r=n)||{}),r);case"xlsx":case"xlsm":case"xlam":case"xlsb":case"ods":return function(e,t){var r=t||{};style_builder=new Vf(t);var e=vf(e,r),n={};if(r.compression&&(n.compression="DEFLATE"),r.password)n.type=K?"nodebuffer":"string";else switch(r.type){case"base64":n.type="base64";break;case"binary":n.type="string";break;case"string":throw new Error("'string' output type invalid for '"+r.bookType+"' files");case"buffer":case"file":n.type=K?"nodebuffer":"string";break;default:throw new Error("Unrecognized type "+r.type)}return e=e.generate(n),r.password&&"undefined"!=typeof encrypt_agile?Cf(encrypt_agile(e,r.password),r):"file"===r.type?E(r.file,e):"string"==r.type?Oe(e):e}(e,n);default:throw new Error("Unrecognized bookType |"+n.bookType+"|")}}function xf(e){var t;e.bookType||((t=e.file.slice(e.file.lastIndexOf(".")).toLowerCase()).match(/^\.[a-z]+$/)&&(e.bookType=t.slice(1)),e.bookType={xls:"biff8",htm:"html",slk:"sylk",socialcalc:"eth",Sh33tJS:"WTF"}[e.bookType]||e.bookType)}function Af(e,t,r){r=r||{};return r.type="file",r.file=t,xf(r),kf(e,r)}function If(e,t,r,n,a,s,i,o){var l=Ht(r),c=o.defval,f=o.raw||!o.hasOwnProperty("raw"),h=!0,u=1===a?[]:{};if(1!==a)if(Object.defineProperty)try{Object.defineProperty(u,"__rowNum__",{value:r,enumerable:!1})}catch(e){u.__rowNum__=r}else u.__rowNum__=r;if(!i||e[r])for(var d=t.s.c;d<=t.e.c;++d){var p=i?e[r][d]:e[n[d]+l];if(void 0!==p&&void 0!==p.t){var m=p.v;switch(p.t){case"z":if(null==m)break;continue;case"e":m=void 0;break;case"s":case"d":case"b":case"n":break;default:throw new Error("unrecognized type "+p.t)}if(null!=s[d]){if(null==m)if(void 0!==c)u[s[d]]=c;else{if(!f||null!==m)continue;u[s[d]]=null}else u[s[d]]=f?m:Zt(p,m,o);null!=m&&(h=!1)}}else void 0!==c&&null!=s[d]&&(u[s[d]]=c)}return{row:u,isempty:h}}function Rf(e,t){if(null==e||null==e["!ref"])return[];var r,n={t:"n",v:0},a=0,s=1,i=[],o="",l={s:{r:0,c:0},e:{r:0,c:0}},c=t||{},f=null!=c.range?c.range:e["!ref"];switch(1===c.header?a=1:"A"===c.header?a=2:Array.isArray(c.header)&&(a=3),typeof f){case"string":l=Yt(f);break;case"number":(l=Yt(e["!ref"])).s.r=f;break;default:l=f}0<a&&(s=0);var h=Ht(l.s.r),u=[],d=[],p=0,m=0,g=Array.isArray(e),b=l.s.r,v=0,E=0;for(g&&!e[b]&&(e[b]=[]),v=l.s.c;v<=l.e.c;++v)switch(u[v]=Vt(v),n=g?e[b][v]:e[u[v]+h],a){case 1:i[v]=v-l.s.c;break;case 2:i[v]=u[v];break;case 3:i[v]=c.header[v-l.s.c];break;default:for(o=r=Zt(n=null==n?{w:"__EMPTY",t:"s"}:n,null,c),E=m=0;E<i.length;++E)i[E]==o&&(o=r+"_"+ ++m);i[v]=o}for(b=l.s.r+s;b<=l.e.r;++b){var w=If(e,l,b,u,a,i,g,c);!1!==w.isempty&&(1===a?!1===c.blankrows:!c.blankrows)||(d[p++]=w.row)}return d.length=p,d}var Of=/"/g;function Ff(e,t,r,n,a,s,i,o){for(var l=!0,c=[],f="",h=Ht(r),u=t.s.c;u<=t.e.c;++u)if(n[u]){var d=o.dense?(e[r]||[])[u]:e[n[u]+h];if(null==d)f="";else if(null!=d.v){for(var p,l=!1,f=""+Zt(d,null,o),m=0;m!==f.length;++m)if((p=f.charCodeAt(m))===a||p===s||34===p){f='"'+f.replace(Of,'""')+'"';break}"ID"==f&&(f='"ID"')}else null==d.f||d.F?f="":(l=!1,0<=(f="="+d.f).indexOf(",")&&(f='"'+f.replace(Of,'""')+'"'));c.push(f)}return!1===o.blankrows&&l?null:c.join(i)}function Df(e,t){var r=[],n=null==t?{}:t;if(null==e||null==e["!ref"])return"";var a=Yt(e["!ref"]),s=void 0!==n.FS?n.FS:",",i=s.charCodeAt(0),o=void 0!==n.RS?n.RS:"\n",l=o.charCodeAt(0),c=new RegExp(("|"==s?"\\|":s)+"+$"),f="",h=[];n.dense=Array.isArray(e);for(var u=n.skipHidden&&e["!cols"]||[],d=n.skipHidden&&e["!rows"]||[],p=a.s.c;p<=a.e.c;++p)(u[p]||{}).hidden||(h[p]=Vt(p));for(var m=a.s.r;m<=a.e.r;++m)(d[m]||{}).hidden||null!=(f=Ff(e,a,m,h,i,l,s,n))&&(n.strip&&(f=f.replace(c,"")),r.push(f+o));return delete n.dense,r.join("")}function Pf(e,t){(t=t||{}).FS="\t",t.RS="\n";e=Df(e,t);if("undefined"==typeof cptable||"string"==t.type)return e;e=cptable.utils.encode(1200,e,"str");return String.fromCharCode(255)+String.fromCharCode(254)+e}function Nf(e){var t,r="",n="";if(null==e||null==e["!ref"])return[];for(var a,s=Yt(e["!ref"]),i=[],o=[],l=Array.isArray(e),c=s.s.c;c<=s.e.c;++c)i[c]=Vt(c);for(var f=s.s.r;f<=s.e.r;++f)for(a=Ht(f),c=s.s.c;c<=s.e.c;++c)if(r=i[c]+a,n="",void 0!==(t=l?(e[f]||[])[c]:e[r])){if(null!=t.F){if(r=t.F,!t.f)continue;n=t.f,-1==r.indexOf(":")&&(r=r+":"+r)}if(null!=t.f)n=t.f;else{if("z"==t.t)continue;if("n"==t.t&&null!=t.v)n=""+t.v;else if("b"==t.t)n=t.v?"TRUE":"FALSE";else if(void 0!==t.w)n="'"+t.w;else{if(void 0===t.v)continue;n="s"==t.t?"'"+t.v:""+t.v}}o[o.length]=r+"="+n}return o}function Lf(e,t,r){var s,i=r||{},o=+!i.skipHeader,l=e||{},c=0,f=0;l&&null!=i.origin&&("number"==typeof i.origin?c=i.origin:(n="string"==typeof i.origin?Xt(i.origin):i.origin,c=n.r,f=n.c));var n,e={s:{c:0,r:0},e:{c:f,r:c+t.length-1+o}};l["!ref"]&&(n=Yt(l["!ref"]),e.e.c=Math.max(e.e.c,n.e.c),e.e.r=Math.max(e.e.r,n.e.r),-1==c&&(c=e.e.r+1,e.e.r=c+t.length-1+o));var h=i.header||[],u=0;t.forEach(function(n,a){de(n).forEach(function(e){-1==(u=h.indexOf(e))&&(h[u=h.length]=e);var t=n[e],r="z",e="";!t||"object"!=typeof t||t instanceof Date?("number"==typeof t?r="n":"boolean"==typeof t?r="b":"string"==typeof t?r="s":t instanceof Date&&(r="d",i.cellDates||(r="n",t=G(t)),e=i.dateNF||ue._table[14]),l[jt({c:f+u,r:c+a+o})]=s={t:r,v:t},e&&(s.z=e)):l[jt({c:f+u,r:c+a+o})]=t})}),e.e.c=Math.max(e.e.c,f+h.length-1);var a=Ht(c);if(o)for(u=0;u<h.length;++u)l[Vt(u+f)+a]={t:"s",v:h[u]};return l["!ref"]=$t(e),l}var Mf,Uf,na={encode_col:Vt,encode_row:Ht,encode_cell:jt,encode_range:$t,decode_col:zt,decode_row:Ut,split_cell:Wt,decode_cell:Xt,decode_range:Gt,format_cell:Zt,get_formulae:Nf,make_csv:Df,make_json:Rf,make_formulae:Nf,sheet_add_aoa:Jt,sheet_add_json:Lf,aoa_to_sheet:qt,json_to_sheet:function(e,t){return Lf(null,e,t)},table_to_sheet:Hc,table_to_book:function(e,t){return Qt(Hc(e,t),t)},sheet_to_csv:Df,sheet_to_txt:Pf,sheet_to_json:Rf,sheet_to_html:Nc.from_sheet,sheet_to_dif:ka.from_sheet,sheet_to_slk:Ca.from_sheet,sheet_to_eth:Na.from_sheet,sheet_to_formulae:Nf,sheet_to_row_object_array:Rf};function Hf(e,t,r){return null!=e[t]?e[t]:e[t]=r}(Mf=na).consts=Mf.consts||{},Mf.book_new=function(){return{SheetNames:[],Sheets:{}}},Mf.book_append_sheet=function(e,t,r){if(!r)for(var n=1;n<=65535&&-1!=e.SheetNames.indexOf(r="Sheet"+n);++n);if(!r)throw new Error("Too many worksheets");if(Nl(r),0<=e.SheetNames.indexOf(r))throw new Error("Worksheet with name |"+r+"| already exists!");e.SheetNames.push(r),e.Sheets[r]=t},Mf.book_set_sheet_visibility=function(e,t,r){Hf(e,"Workbook",{}),Hf(e.Workbook,"Sheets",[]);t=function(e,t){if("number"==typeof t){if(0<=t&&e.SheetNames.length>t)return t;throw new Error("Cannot find sheet # "+t)}if("string"!=typeof t)throw new Error("Cannot find sheet |"+t+"|");if(-1<(e=e.SheetNames.indexOf(t)))return e;throw new Error("Cannot find sheet name |"+t+"|")}(e,t);switch(Hf(e.Workbook.Sheets,t,{}),r){case 0:case 1:case 2:break;default:throw new Error("Bad sheet visibility setting "+r)}e.Workbook.Sheets[t].Hidden=r},[["SHEET_VISIBLE",0],["SHEET_HIDDEN",1],["SHEET_VERY_HIDDEN",2]].forEach(function(e){Mf.consts[e[0]]=e[1]}),Mf.cell_set_number_format=function(e,t){return e.z=t,e},Mf.cell_set_hyperlink=function(e,t,r){return t?(e.l={Target:t},r&&(e.l.Tooltip=r)):delete e.l,e},Mf.cell_set_internal_link=function(e,t,r){return Mf.cell_set_hyperlink(e,"#"+t,r)},Mf.cell_add_comment=function(e,t,r){e.c||(e.c=[]),e.c.push({t:t,a:r||"SheetJS"})},Mf.sheet_set_array_formula=function(e,t,r){for(var n="string"!=typeof t?t:Yt(t),a="string"==typeof t?t:$t(t),s=n.s.r;s<=n.e.r;++s)for(var i=n.s.c;i<=n.e.c;++i){var o=function e(t,r,n){return"string"==typeof r?t[r]||(t[r]={t:"z"}):e(t,jt("number"!=typeof r?r:{r:r,c:n||0}))}(e,s,i);o.t="n",o.F=a,delete o.v,s==n.s.r&&i==n.s.c&&(o.f=r)}return e},K&&"undefined"!=typeof require&&(Uf={}.Readable,n.stream={to_json:function(t,e){var r=Uf({objectMode:!0});if(null==t||null==t["!ref"])return r.push(null),r;var n,a={t:"n",v:0},s=0,i=1,o=[],l="",c={s:{r:0,c:0},e:{r:0,c:0}},f=e||{},h=null!=f.range?f.range:t["!ref"];switch(1===f.header?s=1:"A"===f.header?s=2:Array.isArray(f.header)&&(s=3),typeof h){case"string":c=Yt(h);break;case"number":(c=Yt(t["!ref"])).s.r=h;break;default:c=h}0<s&&(i=0);var u=Ht(c.s.r),d=[],p=0,m=Array.isArray(t),g=c.s.r,b=0,v=0;for(m&&!t[g]&&(t[g]=[]),b=c.s.c;b<=c.e.c;++b)switch(d[b]=Vt(b),a=m?t[g][b]:t[d[b]+u],s){case 1:o[b]=b-c.s.c;break;case 2:o[b]=d[b];break;case 3:o[b]=f.header[b-c.s.c];break;default:for(l=n=Zt(a=null==a?{w:"__EMPTY",t:"s"}:a,null,f),v=p=0;v<o.length;++v)o[v]==l&&(l=n+"_"+ ++p);o[b]=l}return g=c.s.r+i,r._read=function(){if(g>c.e.r)return r.push(null);for(;g<=c.e.r;){var e=If(t,c,g,d,s,o,m,f);if(++g,!1===e.isempty||(1===s?!1!==f.blankrows:f.blankrows)){r.push(e.row);break}}},r},to_html:function(e,t){var r=Uf(),n=t||{},t=null!=n.header?n.header:Nc.BEGIN,a=null!=n.footer?n.footer:Nc.END;r.push(t);var s=Gt(e["!ref"]);n.dense=Array.isArray(e),r.push(Nc._preamble(e,s,n));var i=s.s.r,o=!1;return r._read=function(){if(i>s.e.r)return o||(o=!0,r.push("</table>"+a)),r.push(null);for(;i<=s.e.r;){r.push(Nc._row(e,s,i,n)),++i;break}},r},to_csv:function(e,t){var r=Uf(),n=null==t?{}:t;if(null==e||null==e["!ref"])return r.push(null),r;var a=Yt(e["!ref"]),s=void 0!==n.FS?n.FS:",",i=s.charCodeAt(0),o=void 0!==n.RS?n.RS:"\n",l=o.charCodeAt(0),c=new RegExp(("|"==s?"\\|":s)+"+$"),f="",h=[];n.dense=Array.isArray(e);for(var u=n.skipHidden&&e["!cols"]||[],d=n.skipHidden&&e["!rows"]||[],p=a.s.c;p<=a.e.c;++p)(u[p]||{}).hidden||(h[p]=Vt(p));var m=a.s.r,g=!1;return r._read=function(){if(!g)return g=!0,r.push("\ufeff");if(m>a.e.r)return r.push(null);for(;m<=a.e.r;)if(!(d[++m-1]||{}).hidden&&null!=(f=Ff(e,a,m-1,h,i,l,s,n))){n.strip&&(f=f.replace(c,"")),r.push(f+o);break}},r}});var zf=function(){function n(e,t,r){return this instanceof n?(this.tagName=e,this._attributes=t||{},this._children=r||[],this._prefix="",this):new n(e,t,r)}n.prototype.createElement=function(){return new n(arguments)},n.prototype.children=function(){return this._children},n.prototype.append=function(e){return this._children.push(e),this},n.prototype.prefix=function(e){return 0==arguments.length?this._prefix:(this._prefix=e,this)},n.prototype.attr=function(e,t){if(null==t)return delete this._attributes[e],this;if(0==arguments.length)return this._attributes;if("string"==typeof e&&1==arguments.length)return this._attributes.attr[e];if("object"==typeof e&&1==arguments.length)for(var r in e)this._attributes[r]=e[r];else 2==arguments.length&&"string"==typeof e&&(this._attributes[e]=t);return this};QUOTE='"';var e={};return e[QUOTE]="&quot;",e["'"]="&apos;",n.prototype.escapeAttributeValue=function(e){return'"'+e.replace(/\"/g,"&quot;")+'"'},n.prototype.toXml=function(e){var t=(e=e||this)._prefix;if(t+="<"+e.tagName,e._attributes)for(var r in e._attributes)t+=" "+r+"="+this.escapeAttributeValue(""+e._attributes[r]);if(e._children&&0<e._children.length){t+=">";for(var n=0;n<e._children.length;n++)t+=this.toXml(e._children[n]);t+="</"+e.tagName+">"}else t+="/>";return t},n}(),Vf=function(e){var t,r=164,n={0:"General",1:"0",2:"0.00",3:"#,##0",4:"#,##0.00",9:"0%",10:"0.00%",11:"0.00E+00",12:"# ?/?",13:"# ??/??",14:"m/d/yy",15:"d-mmm-yy",16:"d-mmm",17:"mmm-yy",18:"h:mm AM/PM",19:"h:mm:ss AM/PM",20:"h:mm",21:"h:mm:ss",22:"m/d/yy h:mm",37:"#,##0 ;(#,##0)",38:"#,##0 ;[Red](#,##0)",39:"#,##0.00;(#,##0.00)",40:"#,##0.00;[Red](#,##0.00)",45:"mm:ss",46:"[h]:mm:ss",47:"mmss.0",48:"##0.0E+0",49:"@",56:'"上午/下午 "hh"時"mm"分"ss"秒 "'},a={};for(t in n)a[n[t]]=t;return _hashIndex={},_listIndex=[],{initialize:function(e){this.$fonts=zf("fonts").attr("count",0).attr("x14ac:knownFonts","1"),this.$fills=zf("fills").attr("count",0),this.$borders=zf("borders").attr("count",0),this.$numFmts=zf("numFmts").attr("count",0),this.$cellStyleXfs=zf("cellStyleXfs"),this.$xf=zf("xf").attr("numFmtId",0).attr("fontId",0).attr("fillId",0).attr("borderId",0),this.$cellXfs=zf("cellXfs").attr("count",0),this.$cellStyles=zf("cellStyles").append(zf("cellStyle").attr("name","Normal").attr("xfId",0).attr("builtinId",0)),this.$dxfs=zf("dxfs").attr("count","0"),this.$tableStyles=zf("tableStyles").attr("count","0").attr("defaultTableStyle","TableStyleMedium9").attr("defaultPivotStyle","PivotStyleMedium4"),this.$styles=zf("styleSheet").attr("xmlns:mc","http://schemas.openxmlformats.org/markup-compatibility/2006").attr("xmlns:x14ac","http://schemas.microsoft.com/office/spreadsheetml/2009/9/ac").attr("xmlns","http://schemas.openxmlformats.org/spreadsheetml/2006/main").attr("mc:Ignorable","x14ac").prefix('<?xml version="1.0" encoding="UTF-8" standalone="yes"?>').append(this.$numFmts).append(this.$fonts).append(this.$fills).append(this.$borders).append(this.$cellStyleXfs.append(this.$xf)).append(this.$cellXfs).append(this.$cellStyles).append(this.$dxfs).append(this.$tableStyles);var t=e.defaultCellStyle||{};t.font||(t.font={name:"Calibri",sz:"12"}),t.font.name||(t.font.name="Calibri"),t.font.sz||(t.font.sz=11),t.fill||(t.fill={patternType:"none",fgColor:{}}),t.border||(t.border={}),t.numFmt||(t.numFmt=0),this.defaultStyle=t;e=JSON.parse(JSON.stringify(t));return e.fill={patternType:"gray125",fgColor:{}},this.addStyles([t,e]),this},addStyle:function(e){var t=JSON.stringify(e),r=_hashIndex[t];return null==r?(r=this._addXf(e),_hashIndex[t]=r):r=_hashIndex[t],r},addStyles:function(e){var t=this;return e.map(function(e){return t.addStyle(e)})},_duckTypeStyle:function(e){return"object"==typeof e&&(e.patternFill||e.fgColor)?{fill:e}:e.font||e.numFmt||e.border||e.fill?e:this._getStyleCSS(e)},_getStyleCSS:function(e){return e},_addXf:function(e){var t=this._addFont(e.font),r=this._addFill(e.fill),n=this._addBorder(e.border),a=this._addNumFmt(e.numFmt),s=zf("xf").attr("numFmtId",a).attr("fontId",t).attr("fillId",r).attr("borderId",n).attr("xfId","0");0<t&&s.attr("applyFont","1"),0<r&&s.attr("applyFill","1"),0<n&&s.attr("applyBorder","1"),0<a&&s.attr("applyNumberFormat","1"),e.alignment&&(a=zf("alignment"),e.alignment.horizontal&&a.attr("horizontal",e.alignment.horizontal),e.alignment.vertical&&a.attr("vertical",e.alignment.vertical),e.alignment.indent&&a.attr("indent",e.alignment.indent),e.alignment.readingOrder&&a.attr("readingOrder",e.alignment.readingOrder),e.alignment.wrapText&&a.attr("wrapText",e.alignment.wrapText),null!=e.alignment.textRotation&&a.attr("textRotation",e.alignment.textRotation),s.append(a).attr("applyAlignment",1)),this.$cellXfs.append(s);s=+this.$cellXfs.children().length;return this.$cellXfs.attr("count",s),s-1},_addFont:function(e){if(!e)return 0;var t=zf("font").append(zf("sz").attr("val",e.sz||this.defaultStyle.font.sz)).append(zf("name").attr("val",e.name||this.defaultStyle.font.name));e.bold&&t.append(zf("b")),e.underline&&t.append(zf("u")),e.italic&&t.append(zf("i")),e.strike&&t.append(zf("strike")),e.outline&&t.append(zf("outline")),e.shadow&&t.append(zf("shadow")),e.vertAlign&&t.append(zf("vertAlign").attr("val",e.vertAlign)),e.color&&(e.color.theme?(t.append(zf("color").attr("theme",e.color.theme)),e.color.tint&&t.append(zf("tint").attr("theme",e.color.tint))):e.color.rgb&&t.append(zf("color").attr("rgb",e.color.rgb))),this.$fonts.append(t);t=this.$fonts.children().length;return this.$fonts.attr("count",t),t-1},_addNumFmt:function(e){if(!e)return 0;if("string"==typeof e){var t=a[e];if(0<=t)return t}if(/^[0-9]+$/.exec(e))return e;e=e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;");e=zf("numFmt").attr("numFmtId",++r).attr("formatCode",e);this.$numFmts.append(e);e=this.$numFmts.children().length;return this.$numFmts.attr("count",e),r},_addFill:function(e){if(!e)return 0;var t,r=zf("patternFill").attr("patternType",e.patternType||"solid");e.fgColor&&(t=zf("fgColor"),e.fgColor.rgb?(6==e.fgColor.rgb.length&&(e.fgColor.rgb="FF"+e.fgColor.rgb),t.attr("rgb",e.fgColor.rgb),r.append(t)):e.fgColor.theme&&(t.attr("theme",e.fgColor.theme),e.fgColor.tint&&t.attr("tint",e.fgColor.tint),r.append(t)),e.bgColor||(e.bgColor={indexed:"64"})),e.bgColor&&(e=zf("bgColor").attr(e.bgColor),r.append(e));r=zf("fill").append(r);this.$fills.append(r);r=this.$fills.children().length;return this.$fills.attr("count",r),r-1},_getSubBorder:function(e,t){var r=zf(e);return t&&(t.style&&r.attr("style",t.style),t.color&&(e=zf("color"),t.color.auto?e.attr("auto",t.color.auto):t.color.rgb?e.attr("rgb",t.color.rgb):(t.color.theme||t.color.tint)&&(e.attr("theme",t.color.theme||"1"),e.attr("tint",t.color.tint||"0")),r.append(e))),r},_addBorder:function(t){if(!t)return 0;var r=this,n=zf("border").attr("diagonalUp",t.diagonalUp).attr("diagonalDown",t.diagonalDown);["left","right","top","bottom","diagonal"].forEach(function(e){n.append(r._getSubBorder(e,t[e]))}),this.$borders.append(n);var e=this.$borders.children().length;return this.$borders.attr("count",e),e-1},toXml:function(){return this.$styles.toXml()}}.initialize(e||{})};n.parse_xlscfb=bc,n.parse_ods=Xc,n.parse_fods=jc,n.write_ods=qc,n.parse_zip=gf,n.read=_f,n.readFile=yf,n.readFileSync=yf,n.write=kf,n.writeFile=Af,n.writeFileSync=Af,n.writeFileAsync=function(e,t,r,n){var a=r||{};a.type="file",a.file=e,xf(a),a.type="buffer";var s=n;return n instanceof Function||(s=r),b.writeFile(e,kf(t,a),s)},n.utils=na,n.SSF=ue,n.CFB=ie}"undefined"!=typeof exports?make_xlsx_lib(exports):"undefined"!=typeof module&&module.exports?make_xlsx_lib(module.exports):"function"==typeof define&&define.amd?define("xlsx",function(){return XLSX.version||make_xlsx_lib(XLSX),XLSX}):make_xlsx_lib(XLSX);var XLS=XLSX,ODS=XLSX;!function(d){"use strict";var p=void 0,t=1e5;function m(e){switch(typeof e){case"undefined":return"undefined";case"boolean":return"boolean";case"number":return"number";case"string":return"string";default:return null===e?"null":"object"}}function g(e){return Object.prototype.toString.call(e).replace(/^\[object *|\]$/g,"")}function b(e){return"function"==typeof e}function v(e){if(null===e||e===p)throw TypeError();return Object(e)}var n,e,o,E=Math.LN2,w=Math.abs,S=Math.floor,_=Math.log,y=Math.max,C=Math.min,B=Math.pow,r=Math.round;function T(r){if(!("TYPED_ARRAY_POLYFILL_NO_ARRAY_ACCESSORS"in d)){if(r.length>t)throw RangeError("Array too large for polyfill");for(var e=0;e<r.length;e+=1)!function(t){Object.defineProperty(r,t,{get:function(){return r._getter(t)},set:function(e){r._setter(t,e)},enumerable:!0,configurable:!1})}(e)}}function a(e,t){t=32-t;return e<<t>>t}function s(e,t){t=32-t;return e<<t>>>t}function k(e){return[255&e]}function x(e){return a(e[0],8)}function A(e){return[255&e]}function I(e){return s(e[0],8)}function R(e){return[(e=r(Number(e)))<0?0:255<e?255:255&e]}function O(e){return[255&e,e>>8&255]}function F(e){return a(e[1]<<8|e[0],16)}function D(e){return[255&e,e>>8&255]}function P(e){return s(e[1]<<8|e[0],16)}function N(e){return[255&e,e>>8&255,e>>16&255,e>>24&255]}function L(e){return a(e[3]<<24|e[2]<<16|e[1]<<8|e[0],32)}function M(e){return[255&e,e>>8&255,e>>16&255,e>>24&255]}function U(e){return s(e[3]<<24|e[2]<<16|e[1]<<8|e[0],32)}function i(e,t,r){var n,a,s,i,o=(1<<t-1)-1;function l(e){var t=S(e),e=e-t;return!(e<.5)&&(.5<e||t%2)?t+1:t}e!=e?(n=(1<<t)-1,s=B(2,r-1),i=0):e===1/0||e===-1/0?(n=(1<<t)-1,i=e<(s=0)?1:0):0===e?i=1/e==-1/(s=n=0)?1:0:(i=e<0,(e=w(e))>=B(2,1-o)?(n=C(S(_(e)/E),1023),(a=e/B(2,n))<1&&(--n,a*=2),2<=a&&(n+=1,a/=2),n+=o,1<=(s=l(a*(a=B(2,r)))-a)/a&&(n+=1,s=0),2*o<n&&(n=(1<<t)-1,s=0)):(n=0,s=l(e/B(2,1-o-r))));for(var c=[],f=r;f;--f)c.push(s%2?1:0),s=S(s/2);for(f=t;f;--f)c.push(n%2?1:0),n=S(n/2);c.push(i?1:0),c.reverse();for(var h=c.join(""),u=[];h.length;)u.unshift(parseInt(h.substring(0,8),2)),h=h.substring(8);return u}function l(e,t,r){for(var n,a,s,i,o,l,c=[],f=0;f<e.length;++f)for(a=e[f],n=8;n;--n)c.push(a%2?1:0),a>>=1;return c.reverse(),l=c.join(""),s=(1<<t-1)-1,i=parseInt(l.substring(0,1),2)?-1:1,o=parseInt(l.substring(1,1+t),2),l=parseInt(l.substring(1+t),2),o===(1<<t)-1?0!==l?NaN:1/0*i:0<o?i*B(2,o-s)*(1+l/B(2,r)):0!==l?i*B(2,-(s-1))*(l/B(2,r)):i<0?-0:0}function H(e){return l(e,11,52)}function z(e){return i(e,11,52)}function V(e){return l(e,8,23)}function W(e){return i(e,8,23)}function c(e,t){return b(e.get)?e.get(t):e[t]}function f(e,t,r){if(!(e instanceof ArrayBuffer||"ArrayBuffer"===g(e)))throw TypeError();if((t>>>=0)>e.byteLength)throw RangeError("byteOffset out of range");if(r===p?r=e.byteLength-t:r>>>=0,t+r>e.byteLength)throw RangeError("byteOffset and length reference an area beyond the end of the buffer");Object.defineProperty(this,"buffer",{value:e}),Object.defineProperty(this,"byteLength",{value:r}),Object.defineProperty(this,"byteOffset",{value:t})}function h(s){return function(e,t){if((e>>>=0)+s.BYTES_PER_ELEMENT>this.byteLength)throw RangeError("Array index out of range");e+=this.byteOffset;for(var r=new Uint8Array(this.buffer,e,s.BYTES_PER_ELEMENT),n=[],a=0;a<s.BYTES_PER_ELEMENT;a+=1)n.push(c(r,a));return Boolean(t)===Boolean(o)&&n.reverse(),c(new s(new Uint8Array(n).buffer),0)}}function u(i){return function(e,t,r){if((e>>>=0)+i.BYTES_PER_ELEMENT>this.byteLength)throw RangeError("Array index out of range");for(var t=new i([t]),n=new Uint8Array(t.buffer),a=[],s=0;s<i.BYTES_PER_ELEMENT;s+=1)a.push(c(n,s));Boolean(r)===Boolean(o)&&a.reverse(),new Uint8Array(this.buffer,e,i.BYTES_PER_ELEMENT).set(a)}}n=Object.defineProperty,e=!function(){try{return Object.defineProperty({},"x",{})}catch(e){return}}(),n&&!e||(Object.defineProperty=function(e,t,r){if(n)try{return n(e,t,r)}catch(e){}if(e!==Object(e))throw TypeError("Object.defineProperty called on non-object");return Object.prototype.__defineGetter__&&"get"in r&&Object.prototype.__defineGetter__.call(e,t,r.get),Object.prototype.__defineSetter__&&"set"in r&&Object.prototype.__defineSetter__.call(e,t,r.set),"value"in r&&(e[t]=r.value),e}),function(){function a(e){if((e>>=0)<0)throw RangeError("ArrayBuffer size is not a small enough positive integer.");Object.defineProperty(this,"byteLength",{value:e}),Object.defineProperty(this,"_bytes",{value:Array(e)});for(var t=0;t<e;t+=1)this._bytes[t]=0}function s(){if(!arguments.length||"object"!=typeof arguments[0])return function(e){if((e>>=0)<0)throw RangeError("length is not a small enough positive integer.");Object.defineProperty(this,"length",{value:e}),Object.defineProperty(this,"byteLength",{value:e*this.BYTES_PER_ELEMENT}),Object.defineProperty(this,"buffer",{value:new a(this.byteLength)}),Object.defineProperty(this,"byteOffset",{value:0})}.apply(this,arguments);if(1<=arguments.length&&"object"===m(arguments[0])&&arguments[0]instanceof s)return function(e){if(this.constructor!==e.constructor)throw TypeError();var t=e.length*this.BYTES_PER_ELEMENT;Object.defineProperty(this,"buffer",{value:new a(t)}),Object.defineProperty(this,"byteLength",{value:t}),Object.defineProperty(this,"byteOffset",{value:0}),Object.defineProperty(this,"length",{value:e.length});for(var r=0;r<this.length;r+=1)this._setter(r,e._getter(r))}.apply(this,arguments);if(1<=arguments.length&&"object"===m(arguments[0])&&!(arguments[0]instanceof s)&&!(arguments[0]instanceof a||"ArrayBuffer"===g(arguments[0])))return function(e){var t=e.length*this.BYTES_PER_ELEMENT;Object.defineProperty(this,"buffer",{value:new a(t)}),Object.defineProperty(this,"byteLength",{value:t}),Object.defineProperty(this,"byteOffset",{value:0}),Object.defineProperty(this,"length",{value:e.length});for(var r=0;r<this.length;r+=1){var n=e[r];this._setter(r,Number(n))}}.apply(this,arguments);if(1<=arguments.length&&"object"===m(arguments[0])&&(arguments[0]instanceof a||"ArrayBuffer"===g(arguments[0])))return function(e,t,r){if((t>>>=0)>e.byteLength)throw RangeError("byteOffset out of range");if(t%this.BYTES_PER_ELEMENT)throw RangeError("buffer length minus the byteOffset is not a multiple of the element size.");if(r===p){var n=e.byteLength-t;if(n%this.BYTES_PER_ELEMENT)throw RangeError("length of buffer minus byteOffset not a multiple of the element size");r=n/this.BYTES_PER_ELEMENT}else n=(r>>>=0)*this.BYTES_PER_ELEMENT;if(t+n>e.byteLength)throw RangeError("byteOffset and length reference an area beyond the end of the buffer");Object.defineProperty(this,"buffer",{value:e}),Object.defineProperty(this,"byteLength",{value:n}),Object.defineProperty(this,"byteOffset",{value:t}),Object.defineProperty(this,"length",{value:r})}.apply(this,arguments);throw TypeError()}d.ArrayBuffer=d.ArrayBuffer||a,Object.defineProperty(s,"from",{value:function(e){return new this(e)}}),Object.defineProperty(s,"of",{value:function(){return new this(arguments)}});var i={};function e(e,t,r){function n(){Object.defineProperty(this,"constructor",{value:n}),s.apply(this,arguments),T(this)}"__proto__"in n?n.__proto__=s:(n.from=s.from,n.of=s.of),n.BYTES_PER_ELEMENT=e;function a(){}return a.prototype=i,n.prototype=new a,Object.defineProperty(n.prototype,"BYTES_PER_ELEMENT",{value:e}),Object.defineProperty(n.prototype,"_pack",{value:t}),Object.defineProperty(n.prototype,"_unpack",{value:r}),n}s.prototype=i,Object.defineProperty(s.prototype,"_getter",{value:function(e){if(arguments.length<1)throw SyntaxError("Not enough arguments");if((e>>>=0)>=this.length)return p;for(var t=[],r=0,n=this.byteOffset+e*this.BYTES_PER_ELEMENT;r<this.BYTES_PER_ELEMENT;r+=1,n+=1)t.push(this.buffer._bytes[n]);return this._unpack(t)}}),Object.defineProperty(s.prototype,"get",{value:s.prototype._getter}),Object.defineProperty(s.prototype,"_setter",{value:function(e,t){if(arguments.length<2)throw SyntaxError("Not enough arguments");if(!((e>>>=0)>=this.length))for(var r=this._pack(t),n=0,a=this.byteOffset+e*this.BYTES_PER_ELEMENT;n<this.BYTES_PER_ELEMENT;n+=1,a+=1)this.buffer._bytes[a]=r[n]}}),Object.defineProperty(s.prototype,"constructor",{value:s}),Object.defineProperty(s.prototype,"copyWithin",{value:function(e,t){var r,n=arguments[2],a=v(this),s=a.length,s=y(s=s>>>0,0),e=e>>0,i=e<0?y(s+e,0):C(e,s),t=t>>0,o=t<0?y(s+t,0):C(t,s),n=n===p?s:n>>0,n=n<0?y(s+n,0):C(n,s),l=C(n-o,s-i);for(o<i&&i<o+l?(r=-1,o=o+l-1,i=i+l-1):r=1;0<l;)a._setter(i,a._getter(o)),o+=r,i+=r,l-=1;return a}}),Object.defineProperty(s.prototype,"every",{value:function(e){if(this===p||null===this)throw TypeError();var t=Object(this),r=t.length>>>0;if(!b(e))throw TypeError();for(var n=arguments[1],a=0;a<r;a++)if(!e.call(n,t._getter(a),a,t))return!1;return!0}}),Object.defineProperty(s.prototype,"fill",{value:function(e){for(var t=arguments[1],r=arguments[2],n=v(this),a=n.length,a=y(a=a>>>0,0),t=t>>0,s=t<0?y(a+t,0):C(t,a),r=r===p?a:r>>0,i=r<0?y(a+r,0):C(r,a);s<i;)n._setter(s,e),s+=1;return n}}),Object.defineProperty(s.prototype,"filter",{value:function(e){if(this===p||null===this)throw TypeError();var t=Object(this),r=t.length>>>0;if(!b(e))throw TypeError();for(var n=[],a=arguments[1],s=0;s<r;s++){var i=t._getter(s);e.call(a,i,s,t)&&n.push(i)}return new this.constructor(n)}}),Object.defineProperty(s.prototype,"find",{value:function(e){var t=v(this),r=t.length>>>0;if(!b(e))throw TypeError();for(var n=1<arguments.length?arguments[1]:p,a=0;a<r;){var s=t._getter(a),i=e.call(n,s,a,t);if(Boolean(i))return s;++a}return p}}),Object.defineProperty(s.prototype,"findIndex",{value:function(e){var t=v(this),r=t.length>>>0;if(!b(e))throw TypeError();for(var n=1<arguments.length?arguments[1]:p,a=0;a<r;){var s=t._getter(a),s=e.call(n,s,a,t);if(Boolean(s))return a;++a}return-1}}),Object.defineProperty(s.prototype,"forEach",{value:function(e){if(this===p||null===this)throw TypeError();var t=Object(this),r=t.length>>>0;if(!b(e))throw TypeError();for(var n=arguments[1],a=0;a<r;a++)e.call(n,t._getter(a),a,t)}}),Object.defineProperty(s.prototype,"indexOf",{value:function(e){if(this===p||null===this)throw TypeError();var t=Object(this),r=t.length>>>0;if(0==r)return-1;var n=0;if(0<arguments.length&&((n=Number(arguments[1]))!=n?n=0:0!==n&&n!==1/0&&n!==-1/0&&(n=(0<n||-1)*S(w(n)))),r<=n)return-1;for(var a=0<=n?n:y(r-w(n),0);a<r;a++)if(t._getter(a)===e)return a;return-1}}),Object.defineProperty(s.prototype,"join",{value:function(e){if(this===p||null===this)throw TypeError();for(var t=Object(this),r=t.length>>>0,n=Array(r),a=0;a<r;++a)n[a]=t._getter(a);return n.join(e===p?",":e)}}),Object.defineProperty(s.prototype,"lastIndexOf",{value:function(e){if(this===p||null===this)throw TypeError();var t=Object(this),r=t.length>>>0;if(0==r)return-1;var n=r;1<arguments.length&&((n=Number(arguments[1]))!=n?n=0:0!==n&&n!==1/0&&n!==-1/0&&(n=(0<n||-1)*S(w(n))));for(var a=0<=n?C(n,r-1):r-w(n);0<=a;a--)if(t._getter(a)===e)return a;return-1}}),Object.defineProperty(s.prototype,"map",{value:function(e){if(this===p||null===this)throw TypeError();var t=Object(this),r=t.length>>>0;if(!b(e))throw TypeError();var n=[];n.length=r;for(var a=arguments[1],s=0;s<r;s++)n[s]=e.call(a,t._getter(s),s,t);return new this.constructor(n)}}),Object.defineProperty(s.prototype,"reduce",{value:function(e){if(this===p||null===this)throw TypeError();var t=Object(this),r=t.length>>>0;if(!b(e))throw TypeError();if(0==r&&1===arguments.length)throw TypeError();for(var n=0,a=2<=arguments.length?arguments[1]:t._getter(n++);n<r;)a=e.call(p,a,t._getter(n),n,t),n++;return a}}),Object.defineProperty(s.prototype,"reduceRight",{value:function(e){if(this===p||null===this)throw TypeError();var t=Object(this),r=t.length>>>0;if(!b(e))throw TypeError();if(0==r&&1===arguments.length)throw TypeError();for(var n=r-1,a=2<=arguments.length?arguments[1]:t._getter(n--);0<=n;)a=e.call(p,a,t._getter(n),n,t),n--;return a}}),Object.defineProperty(s.prototype,"reverse",{value:function(){if(this===p||null===this)throw TypeError();for(var e=Object(this),t=e.length>>>0,r=S(t/2),n=0,a=t-1;n<r;++n,--a){var s=e._getter(n);e._setter(n,e._getter(a)),e._setter(a,s)}return e}}),Object.defineProperty(s.prototype,"set",{value:function(e,t){if(arguments.length<1)throw SyntaxError("Not enough arguments");var r,n,a,s,i,o,l,c,f,h;if("object"==typeof e&&e.constructor===this.constructor){if((a=t>>>0)+(r=e).length>this.length)throw RangeError("Offset plus length of array is out of range");if(c=this.byteOffset+a*this.BYTES_PER_ELEMENT,f=r.length*this.BYTES_PER_ELEMENT,r.buffer===this.buffer){for(h=[],i=0,o=r.byteOffset;i<f;i+=1,o+=1)h[i]=r.buffer._bytes[o];for(i=0,l=c;i<f;i+=1,l+=1)this.buffer._bytes[l]=h[i]}else for(i=0,o=r.byteOffset,l=c;i<f;i+=1,o+=1,l+=1)this.buffer._bytes[l]=r.buffer._bytes[o]}else{if("object"!=typeof e||void 0===e.length)throw TypeError("Unexpected argument type(s)");if((a=t>>>0)+(s=(n=e).length>>>0)>this.length)throw RangeError("Offset plus length of array is out of range");for(i=0;i<s;i+=1)o=n[i],this._setter(a+i,Number(o))}}}),Object.defineProperty(s.prototype,"slice",{value:function(e,t){for(var r=v(this),n=r.length>>>0,e=e>>0,a=e<0?y(n+e,0):C(e,n),t=t===p?n:t>>0,s=t<0?y(n+t,0):C(t,n),i=new r.constructor(s-a),o=0;a<s;){var l=r._getter(a);i._setter(o,l),++a,++o}return i}}),Object.defineProperty(s.prototype,"some",{value:function(e){if(this===p||null===this)throw TypeError();var t=Object(this),r=t.length>>>0;if(!b(e))throw TypeError();for(var n=arguments[1],a=0;a<r;a++)if(e.call(n,t._getter(a),a,t))return!0;return!1}}),Object.defineProperty(s.prototype,"sort",{value:function(r){if(this===p||null===this)throw TypeError();for(var e=Object(this),t=e.length>>>0,n=Array(t),a=0;a<t;++a)n[a]=e._getter(a);for(n.sort(function(e,t){return e!=e&&t!=t?0:e!=e?1:t!=t?-1:r!==p?r(e,t):e<t?-1:t<e?1:0}),a=0;a<t;++a)e._setter(a,n[a]);return e}}),Object.defineProperty(s.prototype,"subarray",{value:function(e,t){function r(e,t,r){return e<t?t:r<e?r:e}e>>=0,t>>=0,arguments.length<1&&(e=0),arguments.length<2&&(t=this.length),e<0&&(e=this.length+e),t<0&&(t=this.length+t),e=r(e,0,this.length);t=(t=r(t,0,this.length))-e;return new this.constructor(this.buffer,this.byteOffset+e*this.BYTES_PER_ELEMENT,t=t<0?0:t)}});var t=e(1,k,x),r=e(1,A,I),n=e(1,R,I),o=e(2,O,F),l=e(2,D,P),c=e(4,N,L),f=e(4,M,U),h=e(4,W,V),u=e(8,z,H);d.Int8Array=d.Int8Array||t,d.Uint8Array=d.Uint8Array||r,d.Uint8ClampedArray=d.Uint8ClampedArray||n,d.Int16Array=d.Int16Array||o,d.Uint16Array=d.Uint16Array||l,d.Int32Array=d.Int32Array||c,d.Uint32Array=d.Uint32Array||f,d.Float32Array=d.Float32Array||h,d.Float64Array=d.Float64Array||u}(),e=new Uint16Array([4660]),o=18===c(new Uint8Array(e.buffer),0),Object.defineProperty(f.prototype,"getUint8",{value:h(Uint8Array)}),Object.defineProperty(f.prototype,"getInt8",{value:h(Int8Array)}),Object.defineProperty(f.prototype,"getUint16",{value:h(Uint16Array)}),Object.defineProperty(f.prototype,"getInt16",{value:h(Int16Array)}),Object.defineProperty(f.prototype,"getUint32",{value:h(Uint32Array)}),Object.defineProperty(f.prototype,"getInt32",{value:h(Int32Array)}),Object.defineProperty(f.prototype,"getFloat32",{value:h(Float32Array)}),Object.defineProperty(f.prototype,"getFloat64",{value:h(Float64Array)}),Object.defineProperty(f.prototype,"setUint8",{value:u(Uint8Array)}),Object.defineProperty(f.prototype,"setInt8",{value:u(Int8Array)}),Object.defineProperty(f.prototype,"setUint16",{value:u(Uint16Array)}),Object.defineProperty(f.prototype,"setInt16",{value:u(Int16Array)}),Object.defineProperty(f.prototype,"setUint32",{value:u(Uint32Array)}),Object.defineProperty(f.prototype,"setInt32",{value:u(Int32Array)}),Object.defineProperty(f.prototype,"setFloat32",{value:u(Float32Array)}),Object.defineProperty(f.prototype,"setFloat64",{value:u(Float64Array)}),d.DataView=d.DataView||f}(self);