/*!
 * 前端简易样式表
 * Copyright (c) meystack
 * Licensed Apache2.0
 */
body {
    background: #fff;
    font-family: -apple-system,BlinkMacSystemFont,Segoe UI,PingFang SC,Hiragino Sans GB,Microsoft YaHei,Helvetica Neue,Helvetica,Arial,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol;
}
a {
    cursor: pointer;
}

.clear {
    clear: both;
}

.fr {
    float: right;
}

.fl {
    float: left;
}

.hidden {
	display: none;
}

.layui-center {
	text-align: center;
}


.layui-nav .layui-this:after, .layui-nav-bar {
    background: 0!important;
}

.layui-fluid {
	padding: 15px;
}

#userNav .active {
    color: #000!important;
    background: #f2f2f2;
}

.layui-form-label {
    padding: 6px 15px;
}

#header {
	position: fixed;
    top: 0;
    right: 0;
    left: 0;
    z-index: 1030;
    height: 60px;
    line-height: 60px;
	overflow: hidden;
    background: #fff;
}

.layui-nav-scroll {
	background-color: #fff;
	box-shadow: 0 0 10px 0 rgba(0,0,0,0.06);
    color: #000!important;
}

#header .layui-logo img {
	margin-right: 5px;
	margin-bottom: 5px;
}

#header .layui-logo a {
    color: #000;
    outline: 0;
    font-size: 20px;
    font-weight: 500;
    text-decoration: none!important;
}

#header .layui-nav {
    position: absolute;
    left: 190px;
    top: 0;
    padding: 0;
    background: none;
}

#header .layui-nav .layui-nav-item a {
	line-height: 26px;
    color: #9b9bae;
    font-size: 15px;
    font-weight: 400;
    -webkit-transition: all .3s;
    transition: all .3s;
    background-color: transparent!important;
    padding: 6px 0;
    margin: 0 18px;
}

#header .layui-nav li.active a, #header .layui-nav-item a:hover {
    color: #3956de!important;
}

#header #login {
	right: 20px;
	left: unset;
	/*width: 150px;*/
}

#header .nav-item a {
	color: #fff;
	font-size: 16px;
}

#header .nav-item a img {
	border-radius: 50%;
	width: 32px;
	height: 32px;
	margin-right: 15px;
}

#content {
	height: 100%;
    padding-top: 150px;
    padding-bottom: 150px;
    background-position: center center;
    background-size: cover;
    background-repeat: no-repeat;
}

@media screen and (max-width: 992px) {
  #content {
  	padding: 0px;
  }
}

#content .layui-swift-box {
	margin-top: 100px; 
}

#content .layui-swift-box h1 {
    font-weight: 500;
    margin-bottom: 5px;
}

#content .layui-swift-box p {
    margin-top: 20px;
    font-size: 15px;
    line-height: 30px;
    color: #9b9bae!important;
}

#content .layui-swift-box a {
	margin-right: 10px;
}

#content .layui-swift-desc {
	margin-top: 10px;
}

#content .layui-swift-images {
	margin-top: 10px;
}

#content .layui-swift-images img {
	width: 169px;
    margin-top: 20px;
    margin-right: 5px;
    cursor: pointer;
    padding: 2px;
    border: 1px solid #fff;
}

#content .layui-swift-images img:hover {
	border: 1px solid #c7d0f9;
}

.layui-form-fixed {
	padding-right: 35px!important;
	padding-top: 30px!important;
}

.login-link {
    padding: 20px 0 20px 0;
    margin-bottom: 8px;
    border-bottom: 1px solid #e6e6e6
}

#content .retrieve-login-w dl dd {
    height: 24px;
    line-height: 24px
}

.login-link a {
    vertical-align: middle;
    margin-left: 13px;
    width: 56px;
    height: 56px;
    display: inline-block;
}

.login-link span.other {
    font-size: 12px;
    color: #999;
    margin-left: 38px;
}

.login-link .wx {
    background: url(/static/images/wx.png) no-repeat
}

.login-link .qq {
    background: url(/static/images/qq.png) no-repeat
}

.login-link .sina {
    background: url(/static/images/sina.png) no-repeat
}

.login-link .wx:hover {
    background: url(/static/images/wx_h.png) no-repeat
}

.login-link .qq:hover {
    background: url(/static/images/qq_h.png) no-repeat
}

.login-link .sina:hover {
    background: url(/static/images/sina_h.png) no-repeat
}

.login-state em {
    background: url(/static/images/icon.png) no-repeat;
    width: 18px;
    height: 20px;
    cursor: pointer
}

#userLogin {
    position: relative;
    box-sizing: border-box;
}

#userLogin .layui-form {
    padding: 15px 20px;
}

.user-login-icon {
    position: absolute;
    left: 1px;
    top: -2px;
    width: 38px;
    line-height: 36px;
    text-align: center;
    color: #d2d2d2;
}

#userLogin .item, #userLogin .layui-col-xs7 {
    border: 1px solid #d9d9d9;
    border-radius: 5px;
    position: relative;
    margin-bottom: 10px;
}

#userLogin .layui-col-xs3 {
    width: 36%;
}

#userLogin .captcha {
    width: 97%;
    padding: 2px;
    border-radius: 5px;
    border: 1px solid #d9d9d9;
    background-color: #fff;
}

#userLogin .captcha img {
    width: 100%;
}

#userLogin .layui-form-item .item .layui-input {
    padding-left: 38px;
    border-radius: 5px;
    border: 0;
}

#userLogin .captcha:hover, #userLogin .item:hover, #userLogin .layui-col-xs7:hover {
    border: 1px solid #1890ff;
}

#userLogin .login-state em.default {
    background-position: -238px -725px;
    vertical-align: middle
}

.layui-input, .layui-select, .layui-textarea {
	height: 33px;
	line-height: 33px;
}

.layui-form-item .layui-input-inline {
	width: 230px;
}

.captcha {
    display: inline-block;
}

input.captcha {
    width: 200px;
}

.layui-nav-tree .layui-nav-item a:hover {
	background-color: #f2f2f2;
}

#content .layui-nav-tree .layui-nav-bar {
	background: 0!important;
}

#content .layui-user-desc {
	margin-top: 5px;
}

#content .layui-user-desc p {
	margin-top: 10px;
	border-bottom: 1px solid #f6f6f6;
}

#content .layui-user-desc p span {
	font-weight: bold;
	margin-right: 10px;
}

#content .layui-form-label {
	padding: 5px 15px;
}

#content .layui-form-mid {
	padding: 5px 0!important;
}

/* 修改原有样式 */
.layui-btn {
    height: 33px;
    line-height: 33px;
}

@media screen and (max-width: 992px) {
  #content {
  	padding-top: 100px;
  }
}

#footer {
	background-image: url(/static/images/hero-1-bg-img.png);
    background: #292d32;
    background-position: center center;
    background-size: cover;
    width: 100%;
	position: fixed;
    right: 0;
    bottom: 0;
    background-repeat: no-repeat;
}

#footer .layui-footer {
	margin: 0px auto;	
    height: 53px;
    line-height: 53px;
    padding: 20px 0px;
    color: rgba(255,255,255,0.7);
}
