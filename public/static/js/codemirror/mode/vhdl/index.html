<!doctype html>

<title>CodeMirror: VHDL mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">

<link rel="stylesheet" href="../../lib/codemirror.css">
<script src="../../lib/codemirror.js"></script>
<script src="../../addon/edit/matchbrackets.js"></script>
<script src="vhdl.js"></script>
<style>.CodeMirror {border-top: 1px solid black; border-bottom: 1px solid black;}</style>
<div id=nav>
  <a href="https://codemirror.net"><h1>CodeMirror</h1><img id=logo src="../../doc/logo.png" alt=""></a>

  <ul>
    <li><a href="../../index.html">Home</a>
    <li><a href="../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror">Code</a>
  </ul>
  <ul>
    <li><a href="../index.html">Language modes</a>
    <li><a class=active href="#">VHDL</a>
  </ul>
</div>

<article>
<h2>VHDL mode</h2>

<div><textarea id="code" name="code">
LIBRARY ieee;
USE ieee.std_logic_1164.ALL;
USE ieee.numeric_std.ALL;

ENTITY tb IS
END tb;

ARCHITECTURE behavior OF tb IS
   --Inputs
   signal a : unsigned(2 downto 0) := (others => '0');
   signal b : unsigned(2 downto 0) := (others => '0');
    --Outputs
   signal a_eq_b : std_logic;
   signal a_le_b : std_logic;
   signal a_gt_b : std_logic;

    signal i,j : integer;

BEGIN

    -- Instantiate the Unit Under Test (UUT)
   uut: entity work.comparator PORT MAP (
          a => a,
          b => b,
          a_eq_b => a_eq_b,
          a_le_b => a_le_b,
          a_gt_b => a_gt_b
        );

   -- Stimulus process
   stim_proc: process
   begin
        for i in 0 to 8 loop
            for j in 0 to 8 loop
                a <= to_unsigned(i,3); --integer to unsigned type conversion
                b <= to_unsigned(j,3);
                wait for 10 ns;
            end loop;
        end loop;
   end process;

END;
</textarea></div>

<script>
  var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
    lineNumbers: true,
    matchBrackets: true,
    mode: {
      name: "vhdl",
    }
  });
</script>

<p>
Syntax highlighting and indentation for the VHDL language.
<h2>Configuration options:</h2>
  <ul>
    <li><strong>atoms</strong> - List of atom words. Default: "null"</li>
    <li><strong>hooks</strong> - List of meta hooks. Default: ["`", "$"]</li>
    <li><strong>multiLineStrings</strong> - Whether multi-line strings are accepted. Default: false</li>
  </ul>
</p>

<p><strong>MIME types defined:</strong> <code>text/x-vhdl</code>.</p>
</article>
