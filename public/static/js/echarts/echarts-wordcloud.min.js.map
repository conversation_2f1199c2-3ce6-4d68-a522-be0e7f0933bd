{"version": 3, "sources": ["webpack://echarts-wordcloud/webpack/universalModuleDefinition", "webpack://echarts-wordcloud/./src/WordCloudSeries.js", "webpack://echarts-wordcloud/./src/WordCloudView.js", "webpack://echarts-wordcloud/./src/layout.js", "webpack://echarts-wordcloud/./src/wordCloud.js", "webpack://echarts-wordcloud/external \"echarts\"", "webpack://echarts-wordcloud/webpack/bootstrap", "webpack://echarts-wordcloud/webpack/startup", "webpack://echarts-wordcloud/webpack/runtime/make namespace object"], "names": ["root", "factory", "exports", "module", "require", "define", "amd", "self", "__WEBPACK_EXTERNAL_MODULE__83__", "type", "visualStyleAccessPath", "visualStyleMapper", "model", "fill", "get", "visualDrawType", "optionUpdated", "option", "this", "gridSize", "Math", "max", "floor", "getInitialData", "ecModel", "dimensions", "data", "coordDimensions", "list", "initData", "defaultOption", "maskImage", "shape", "keepAspect", "left", "top", "width", "height", "sizeRange", "rotation<PERSON>ange", "rotationStep", "drawOutOfBound", "shrinkToFit", "textStyle", "fontWeight", "render", "seriesModel", "api", "group", "removeAll", "getData", "layoutInstance", "ondraw", "text", "size", "dataIdx", "drawn", "itemModel", "getItemModel", "textStyleModel", "getModel", "textEl", "style", "scaleX", "info", "mu", "scaleY", "x", "gx", "gw", "y", "gy", "gh", "rotation", "rot", "setStyle", "fillTextOffsetX", "fillTextOffsetY", "verticalAlign", "getItemVisual", "fontSize", "add", "setItemGraphicEl", "ensureState", "state", "stateTransition", "duration", "easing", "__highDownD<PERSON><PERSON>tcher", "_model", "remove", "dispose", "window", "setImmediate", "msSetImmediate", "webkitSetImmediate", "mozSetImmediate", "oSetImmediate", "postMessage", "addEventListener", "callbacks", "undefined", "message", "evt", "substr", "length", "stopImmediatePropagation", "id", "parseInt", "clearImmediate", "callback", "push", "toString", "fn", "setTimeout", "msClearImmediate", "webkitClearImmediate", "mozClearImmediate", "oClearImmediate", "timer", "clearTimeout", "isSupported", "canvas", "document", "createElement", "getContext", "ctx", "getImageData", "fillText", "Array", "prototype", "some", "minFontSize", "han<PERSON><PERSON><PERSON>", "m<PERSON><PERSON><PERSON>", "font", "measureText", "shuffle<PERSON><PERSON><PERSON>", "arr", "j", "i", "random", "WordCloud", "elements", "options", "timerId", "Date", "now", "isArray", "for<PERSON>ach", "el", "getElementById", "Error", "tagName", "append<PERSON><PERSON><PERSON>", "settings", "fontFamily", "color", "minSize", "weightFactor", "clearCanvas", "backgroundColor", "origin", "drawMask", "maskColor", "mask<PERSON>ap<PERSON><PERSON><PERSON>", "layoutAnimation", "wait", "abortThr<PERSON>old", "abort", "minRotation", "PI", "maxRotation", "shuffle", "rotateRatio", "ellipticity", "classes", "hover", "click", "key", "factor", "pt", "theta", "sin", "thetaPrime", "cos", "min", "abs", "sqrt", "grid", "ngx", "ngy", "center", "maxRadius", "escapeTime", "getTextColor", "getTextFontWeight", "g", "maskRectWidth", "randomHslColor", "getTextClasses", "hovered", "interactive", "infoGrid", "getInfoGridFromMouseTouchEvent", "clientX", "clientY", "currentTarget", "rect", "getBoundingClientRect", "touches", "eventX", "eventY", "wordcloudhover", "item", "dimension", "wordcloudclick", "preventDefault", "pointsAtRadius", "getPointsAtRadius", "radius", "T", "t", "points", "rx", "exceedTime", "getTime", "fillGridAt", "fillRect", "putWord", "word", "weight", "attributes", "gxy", "rotateDeg", "round", "extraDataArray", "itemCopy", "slice", "splice", "getItemExtraData", "fcanvas", "fctx", "willReadFrequently", "fw", "fh", "boxWidth", "boxHeight", "fgw", "ceil", "fgh", "cgh", "cgw", "setAttribute", "scale", "translate", "rotate", "fillStyle", "textBaseline", "imageData", "occupied", "bounds", "singleGridLoop", "fillTextWidth", "fillTextHeight", "getTextInfo", "r", "concat", "res", "px", "py", "canFitText", "distance", "save", "restore", "span", "transformRule", "styleRules", "position", "display", "lineHeight", "whiteSpace", "transform", "webkitTransform", "msTransform", "transform<PERSON><PERSON>in", "webkitTransformOrigin", "msTransformOrigin", "cssProp", "textContent", "attribute", "className", "drawText", "w", "h", "updateGrid", "sendEvent", "cancelable", "details", "event", "CustomEvent", "detail", "dispatchEvent", "loopingFunction", "stoppingFunction", "clearRect", "bctx", "bgPixel", "e", "webkitTapHighlightColor", "stopInteraction", "removeEventListener", "layouting", "cb", "listener", "anotherWordCloudStart", "loop", "canceled", "start", "toFixed", "eachSeriesByType", "gridRect", "getBoxLayoutParams", "getWidth", "getHeight", "ratio", "aspect", "adjustRectAspect", "drawImage", "<PERSON><PERSON><PERSON><PERSON>", "newImageData", "createImageData", "toneSum", "toneCnt", "tone", "threshold", "putImageData", "updateCanvasMask", "console", "error", "valueExtent", "getDataExtent", "DEGREE_TO_RAD", "onWordCloudDrawn", "mapArray", "value", "idx", "getName", "sort", "a", "b", "series", "compats", "compatTextStyle", "hasOwnProperty", "seriesItem", "normal", "emphasis", "__webpack_module_cache__", "__webpack_require__", "moduleId", "__webpack_modules__", "Symbol", "toStringTag", "Object", "defineProperty"], "mappings": ";CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,EAAQG,QAAQ,YACR,mBAAXC,QAAyBA,OAAOC,IAC9CD,OAAO,CAAC,WAAYJ,GACM,iBAAZC,QACdA,QAAQ,qBAAuBD,EAAQG,QAAQ,YAE/CJ,EAAK,qBAAuBC,EAAQD,EAAc,SARpD,CASGO,MAAM,SAASC,GAClB,M,2DCRA,oBAA0B,CACxBC,KAAM,mBAENC,sBAAuB,YACvBC,kBAAmB,SAAUC,GAC3B,MAAO,CACLC,KAAMD,EAAME,IAAI,WAGpBC,eAAgB,OAEhBC,cAAe,WACb,IAAIC,EAASC,KAAKD,OAClBA,EAAOE,SAAWC,KAAKC,IAAID,KAAKE,MAAML,EAAOE,UAAW,IAG1DI,eAAgB,SAAUN,EAAQO,GAChC,IAAIC,EAAa,0BAAgCR,EAAOS,KAAM,CAC5DC,gBAAiB,CAAC,WAEhBC,EAAO,IAAI,OAAaH,EAAYP,MAExC,OADAU,EAAKC,SAASZ,EAAOS,MACdE,GAITE,cAAe,CACbC,UAAW,KAGXC,MAAO,SACPC,YAAY,EAEZC,KAAM,SAENC,IAAK,SAELC,MAAO,MAEPC,OAAQ,MAERC,UAAW,CAAC,GAAI,IAEhBC,cAAe,EAAE,GAAI,IAErBC,aAAc,GAEdrB,SAAU,EAEVsB,gBAAgB,EAChBC,aAAa,EAEbC,UAAW,CACTC,WAAY,aCrDlB,kBAAwB,CACtBnC,KAAM,YAENoC,OAAQ,SAAUC,EAAatB,EAASuB,GACtC,IAAIC,EAAQ9B,KAAK8B,MACjBA,EAAMC,YAEN,IAAIvB,EAAOoB,EAAYI,UAEnB/B,EAAW2B,EAAYhC,IAAI,YAE/BgC,EAAYK,eAAeC,OAAS,SAAUC,EAAMC,EAAMC,EAASC,GACjE,IAAIC,EAAY/B,EAAKgC,aAAaH,GAC9BI,EAAiBF,EAAUG,SAAS,aAEpCC,EAAS,IAAI,eAAqB,CACpCC,MAAO,yBAA+BH,GACtCI,OAAQ,EAAIP,EAAMQ,KAAKC,GACvBC,OAAQ,EAAIV,EAAMQ,KAAKC,GACvBE,GAAIX,EAAMY,GAAKZ,EAAMQ,KAAKK,GAAK,GAAKlD,EACpCmD,GAAId,EAAMe,GAAKf,EAAMQ,KAAKQ,GAAK,GAAKrD,EACpCsD,SAAUjB,EAAMkB,MAElBb,EAAOc,SAAS,CACdR,EAAGX,EAAMQ,KAAKY,gBACdN,EAAGd,EAAMQ,KAAKa,gBAAyB,GAAPvB,EAChCD,KAAMA,EACNyB,cAAe,SACfjE,KAAMa,EAAKqD,cAAcxB,EAAS,SAAS1C,KAC3CmE,SAAU1B,IAGZN,EAAMiC,IAAIpB,GAEVnC,EAAKwD,iBAAiB3B,EAASM,GAE/BA,EAAOsB,YAAY,YAAYrB,MAAQ,yBACrCL,EAAUG,SAAS,CAAC,WAAY,cAChC,CACEwB,MAAO,aAGXvB,EAAOsB,YAAY,QAAQrB,MAAQ,yBACjCL,EAAUG,SAAS,CAAC,OAAQ,cAC5B,CACEwB,MAAO,SAIX,6BACEvB,EACAJ,EAAU3C,IAAI,CAAC,WAAY,UAC3B2C,EAAU3C,IAAI,CAAC,WAAY,eAG7B+C,EAAOwB,gBAAkB,CACvBC,SAAUxC,EAAYhC,IAAI,aACtBgC,EAAYhC,IAAI,CAAC,iBAAkB,aACnC,EACJyE,OAAQzC,EAAYhC,IAAI,CAAC,iBAAkB,YAG7C+C,EAAO2B,sBAAuB,GAGhCtE,KAAKuE,OAAS3C,GAGhB4C,OAAQ,WACNxE,KAAK8B,MAAMC,YAEX/B,KAAKuE,OAAOtC,eAAewC,WAG7BA,QAAS,WACPzE,KAAKuE,OAAOtC,eAAewC,aClE1BC,OAAOC,eACVD,OAAOC,aAEHD,OAAOE,gBACPF,OAAOG,oBACPH,OAAOI,iBACPJ,OAAOK,eACP,WACE,IAAKL,OAAOM,cAAgBN,OAAOO,iBACjC,OAAO,KAGT,IAAIC,EAAY,MAACC,GACbC,EAAU,uBAgDd,OAnCAV,OAAOO,iBACL,WACA,SAA+BI,GAG7B,GACsB,iBAAbA,EAAI7E,MACX6E,EAAI7E,KAAK8E,OAAO,EAAGF,EAAQG,UAAYH,EAFzC,CAQAC,EAAIG,2BAEJ,IAAIC,EAAKC,SAASL,EAAI7E,KAAK8E,OAAOF,EAAQG,QAAS,IAC9CL,EAAUO,KAIfP,EAAUO,KACVP,EAAUO,QAAMN,OAElB,GAIFT,OAAOiB,eAAiB,SAA0BF,GAC3CP,EAAUO,KAIfP,EAAUO,QAAMN,IAxCG,SAAwBS,GAC3C,IAAIH,EAAKP,EAAUK,OAInB,OAHAL,EAAUW,KAAKD,GACflB,OAAOM,YAAYI,EAAUK,EAAGK,SAAS,IAAK,KAEvCL,GAhBX,IAyDA,SAA8BM,GAC5BrB,OAAOsB,WAAWD,EAAI,KAMzBrB,OAAOiB,iBACVjB,OAAOiB,eAEHjB,OAAOuB,kBACPvB,OAAOwB,sBACPxB,OAAOyB,mBACPzB,OAAO0B,iBAGP,SAAgCC,GAC9B3B,OAAO4B,aAAaD,KAO5B,IAAIE,EAAc,WAChB,IAAIC,EAASC,SAASC,cAAc,UACpC,IAAKF,IAAWA,EAAOG,WACrB,OAAO,EAGT,IAAIC,EAAMJ,EAAOG,WAAW,MAC5B,SAAKC,GAGAA,EAAIC,cAGJD,EAAIE,UAIJC,MAAMC,UAAUC,MAGhBF,MAAMC,UAAUnB,MApBL,GA6BdqB,EAAc,WAChB,GAAKX,EAAL,CAYA,IARA,IAMIY,EAAUC,EANVR,EAAMH,SAASC,cAAc,UAAUC,WAAW,MAGlDvE,EAAO,GAKJA,GAAM,CAEX,GADAwE,EAAIS,KAAOjF,EAAK0D,SAAS,IAAM,gBAE7Bc,EAAIU,YAAY,KAAUpG,QAAUiG,GACpCP,EAAIU,YAAY,KAAKpG,QAAUkG,EAE/B,OAAOhF,EAAO,EAGhB+E,EAAWP,EAAIU,YAAY,KAAUpG,MACrCkG,EAASR,EAAIU,YAAY,KAAKpG,MAE9BkB,IAGF,OAAO,GA5BS,GA2CdmF,EAAe,SAAsBC,GACvC,IAAK,IAAIC,EAAGxE,EAAGyE,EAAIF,EAAIjC,OAAQmC,GAC7BD,EAAIvH,KAAKE,MAAMF,KAAKyH,SAAWD,GAC/BzE,EAAIuE,IAAME,GACVF,EAAIE,GAAKF,EAAIC,GACbD,EAAIC,GAAKxE,EAEX,OAAOuE,GAGLnB,EAAQ,GACRuB,EAAY,SAAmBC,EAAUC,GAC3C,GAAKvB,EAAL,CAIA,IAAIwB,EAAU7H,KAAKE,MAAMF,KAAKyH,SAAWK,KAAKC,OAEzClB,MAAMmB,QAAQL,KACjBA,EAAW,CAACA,IAGdA,EAASM,SAAQ,SAAUC,EAAIV,GAC7B,GAAkB,iBAAPU,GAET,GADAP,EAASH,GAAKjB,SAAS4B,eAAeD,IACjCP,EAASH,GACZ,MAAM,IAAIY,MAAM,+CAEb,IAAKF,EAAGG,UAAYH,EAAGI,YAC5B,MAAM,IAAIF,MACR,+DAMN,IAAIG,EAAW,CACb/H,KAAM,GACNgI,WACE,6FAEFhH,WAAY,SACZiH,MAAO,cACPC,QAAS,EACTC,aAAc,EACdC,aAAa,EACbC,gBAAiB,OAEjB9I,SAAU,EACVsB,gBAAgB,EAChBC,aAAa,EACbwH,OAAQ,KAERC,UAAU,EACVC,UAAW,oBACXC,aAAc,GAEdC,iBAAiB,EAEjBC,KAAM,EACNC,eAAgB,EAChBC,MAAO,aAEPC,aAActJ,KAAKuJ,GAAK,EACxBC,YAAaxJ,KAAKuJ,GAAK,EACvBnI,aAAc,GAEdqI,SAAS,EACTC,YAAa,GAEb9I,MAAO,SACP+I,YAAa,IAEbC,QAAS,KAETC,MAAO,KACPC,MAAO,MAGT,GAAIlC,EACF,IAAK,IAAImC,KAAOnC,EACVmC,KAAOxB,IACTA,EAASwB,GAAOnC,EAAQmC,IAM9B,GAAqC,mBAA1BxB,EAASI,aAA6B,CAC/C,IAAIqB,EAASzB,EAASI,aACtBJ,EAASI,aAAe,SAAsBsB,GAC5C,OAAOA,EAAKD,GAKhB,GAA8B,mBAAnBzB,EAAS3H,MAClB,OAAQ2H,EAAS3H,OACf,IAAK,SAEL,QAEE2H,EAAS3H,MAAQ,SACjB,MAEF,IAAK,WACH2H,EAAS3H,MAAQ,SAAuBsJ,GACtC,OAAO,EAAIlK,KAAKmK,IAAID,IAEtB,MAWF,IAAK,UAIH3B,EAAS3H,MAAQ,SAAqBsJ,GACpC,IAAIE,EAAaF,GAAU,EAAIlK,KAAKuJ,GAAM,GAC1C,OAAO,GAAKvJ,KAAKqK,IAAID,GAAcpK,KAAKmK,IAAIC,KAE9C,MAEF,IAAK,SAGH7B,EAAS3H,MAAQ,SAAqBsJ,GACpC,OAAOlK,KAAKsK,IACV,EAAItK,KAAKuK,IAAIvK,KAAKqK,IAAIH,IACtB,EAAIlK,KAAKuK,IAAIvK,KAAKmK,IAAID,MAG1B,MAEF,IAAK,mBAIH3B,EAAS3H,MAAQ,SAAuBsJ,GACtC,IAAIE,EAAaF,GAAU,EAAIlK,KAAKuJ,GAAM,GAC1C,OACE,GAAKvJ,KAAKqK,IAAID,GAAcpK,KAAKwK,KAAK,GAAKxK,KAAKmK,IAAIC,KAGxD,MAEF,IAAK,WACL,IAAK,mBACH7B,EAAS3H,MAAQ,SAAuBsJ,GACtC,IAAIE,GAAcF,EAAmB,EAAVlK,KAAKuJ,GAAU,IAAO,EAAIvJ,KAAKuJ,GAAM,GAChE,OACE,GAAKvJ,KAAKqK,IAAID,GAAcpK,KAAKwK,KAAK,GAAKxK,KAAKmK,IAAIC,KAGxD,MAEF,IAAK,WACH7B,EAAS3H,MAAQ,SAAuBsJ,GACtC,IAAIE,GAAcF,EAAQ,OAAW,EAAIlK,KAAKuJ,GAAM,GACpD,OAAO,GAAKvJ,KAAKqK,IAAID,GAAc,QAAWpK,KAAKmK,IAAIC,KAEzD,MAEF,IAAK,OACH7B,EAAS3H,MAAQ,SAAmBsJ,GAClC,IAAIE,GAAcF,EAAQ,OAAW,EAAIlK,KAAKuJ,GAAM,IACpD,OACIW,EAAQ,OAAW,EAAIlK,KAAKuJ,GAAM,GAAO,EAAIvJ,KAAKuJ,GAAM,IAC1D,EAGE,GACCvJ,KAAKqK,IAAK,EAAIrK,KAAKuJ,GAAM,GAAKa,GAC7B,QAAUpK,KAAKmK,IAAK,EAAInK,KAAKuJ,GAAM,GAAKa,IAGrC,GAAKpK,KAAKqK,IAAID,GAAc,QAAUpK,KAAKmK,IAAIC,KAQhE7B,EAASxI,SAAWC,KAAKC,IAAID,KAAKE,MAAMqI,EAASxI,UAAW,GAG5D,IASI0K,EACFC,EACAC,EACAC,EACAC,EAGEC,EAGAC,EAiCAC,EApDAC,EAAI1C,EAASxI,SACbmL,EAAgBD,EAAI1C,EAASU,aAG7B9H,EAAgBnB,KAAKuK,IAAIhC,EAASiB,YAAcjB,EAASe,aACzDA,EAActJ,KAAKsK,IAAI/B,EAASiB,YAAajB,EAASe,aACtDlI,EAAemH,EAASnH,aAyB5B,OAAQmH,EAASE,OACf,IAAK,cACHsC,EAAe,WACb,OAAOI,EAAe,GAAI,KAE5B,MAEF,IAAK,eACHJ,EAAe,WACb,OAAOI,EAAe,GAAI,KAE5B,MAEF,QACgC,mBAAnB5C,EAASE,QAClBsC,EAAexC,EAASE,OAOK,mBAAxBF,EAAS/G,aAClBwJ,EAAoBzC,EAAS/G,YAI/B,IAAI4J,EAAiB,KACW,mBAArB7C,EAASqB,UAClBwB,EAAiB7C,EAASqB,SAI5B,IAEIyB,EAFAC,GAAc,EACdC,EAAW,GAGXC,EAAiC,SACnCrG,GAEA,IAEIsG,EACAC,EAHApF,EAASnB,EAAIwG,cACbC,EAAOtF,EAAOuF,wBAId1G,EAAI2G,SACNL,EAAUtG,EAAI2G,QAAQ,GAAGL,QACzBC,EAAUvG,EAAI2G,QAAQ,GAAGJ,UAEzBD,EAAUtG,EAAIsG,QACdC,EAAUvG,EAAIuG,SAEhB,IAAIK,EAASN,EAAUG,EAAK9K,KACxBkL,EAASN,EAAUE,EAAK7K,IAExBgC,EAAI/C,KAAKE,MAAO6L,GAAUzF,EAAOtF,MAAQ4K,EAAK5K,OAAS,GAAMiK,GAC7D/H,EAAIlD,KAAKE,MAAO8L,GAAU1F,EAAOrF,OAAS2K,EAAK3K,QAAU,GAAMgK,GAEnE,OAAOM,EAASxI,GAAGG,IAGjB+I,EAAiB,SAAwB9G,GAC3C,IAAIvC,EAAO4I,EAA+BrG,GAEtCkG,IAAYzI,IAIhByI,EAAUzI,EACLA,EAML2F,EAASsB,MAAMjH,EAAKsJ,KAAMtJ,EAAKuJ,UAAWhH,GALxCoD,EAASsB,WAAM5E,OAAWA,EAAWE,KAQrCiH,EAAiB,SAAwBjH,GAC3C,IAAIvC,EAAO4I,EAA+BrG,GACrCvC,IAIL2F,EAASuB,MAAMlH,EAAKsJ,KAAMtJ,EAAKuJ,UAAWhH,GAC1CA,EAAIkH,mBAIFC,EAAiB,GACjBC,EAAoB,SAA2BC,GACjD,GAAIF,EAAeE,GACjB,OAAOF,EAAeE,GAIxB,IAAIC,EAAa,EAATD,EAGJE,EAAID,EACJE,EAAS,GAMb,IAJe,IAAXH,GACFG,EAAOhH,KAAK,CAACiF,EAAO,GAAIA,EAAO,GAAI,IAG9B8B,KAAK,CAEV,IAAIE,EAAK,EACc,WAAnBrE,EAAS3H,QACXgM,EAAKrE,EAAS3H,MAAO8L,EAAID,EAAK,EAAIzM,KAAKuJ,KAIzCoD,EAAOhH,KAAK,CACViF,EAAO,GAAK4B,EAASI,EAAK5M,KAAKqK,KAAMqC,EAAID,EAAK,EAAIzM,KAAKuJ,IACvDqB,EAAO,GACL4B,EAASI,EAAK5M,KAAKmK,KAAMuC,EAAID,EAAK,EAAIzM,KAAKuJ,IAAMhB,EAASoB,YAC3D+C,EAAID,EAAK,EAAIzM,KAAKuJ,KAKvB,OADA+C,EAAeE,GAAUG,EAClBA,GAILE,EAAa,WACf,OACEtE,EAASa,eAAiB,IAC1B,IAAItB,MAAOgF,UAAYhC,EAAavC,EAASa,gBA0Y7C2D,EAAa,SAAoBhK,EAAGG,EAAG6F,EAAUoD,EAAWD,GAC1DnJ,GAAK2H,GAAOxH,GAAKyH,GAAO5H,EAAI,GAAKG,EAAI,IAIzCuH,EAAK1H,GAAGG,IAAK,EAET6F,GACQpB,EAAS,GAAGlB,WAAW,MAC7BuG,SAASjK,EAAIkI,EAAG/H,EAAI+H,EAAGC,EAAeA,GAGxCI,IACFC,EAASxI,GAAGG,GAAK,CAAEgJ,KAAMA,EAAMC,UAAWA,MA+C1Cc,EAAU,SAASA,EAAQf,GAC7B,IAAIgB,EAAMC,EAAQC,EACdvG,MAAMmB,QAAQkE,IAChBgB,EAAOhB,EAAK,GACZiB,EAASjB,EAAK,KAEdgB,EAAOhB,EAAKgB,KACZC,EAASjB,EAAKiB,OACdC,EAAalB,EAAKkB,YAEpB,IA8BoCC,EAC9BrK,EACAG,EAhCFmK,EA1cyB,IAAzB/E,EAASmB,aAIT1J,KAAKyH,SAAWc,EAASmB,YAHpB,EAOa,IAAlBvI,EACKmI,EAGFA,EAActJ,KAAKuN,MAAMvN,KAAKyH,SAAWtG,EAAgBC,GAAgBA,EAgc5EoM,EAj0Be,SAAUtB,GAC/B,GAAIrF,MAAMmB,QAAQkE,GAAO,CACvB,IAAIuB,EAAWvB,EAAKwB,QAGpB,OADAD,EAASE,OAAO,EAAG,GACZF,EAEP,MAAO,GA0zBcG,CAAiB1B,GAGlCtJ,EAhcY,SAChBsK,EACAC,EACAG,EACAE,GAKA,IACI5J,EAAW2E,EAASI,aAAawE,GACrC,GAAIvJ,GAAY2E,EAASG,QACvB,OAAO,EAMT,IAYIlH,EAZAqB,EAAK,EACLe,EAAWoD,IACbnE,EAAK,WAEH,IADA,IAAIA,EAAK,EACFA,EAAKe,EAAWoD,GACrBnE,GAAM,EAER,OAAOA,EALJ,IAYLrB,EADEwJ,EACWA,EAAkBkC,EAAMC,EAAQvJ,EAAU4J,GAE1CjF,EAAS/G,WAGxB,IAAIqM,EAAUtH,SAASC,cAAc,UACjCsH,EAAOD,EAAQpH,WAAW,KAAM,CAAEsH,oBAAoB,IAE1DD,EAAK3G,KACH3F,EACA,KACCoC,EAAWf,GAAI+C,SAAS,IACzB,MACA2C,EAASC,WAGX,IAAIwF,EAAKF,EAAK1G,YAAY8F,GAAMlM,MAAQ6B,EACpCoL,EACFjO,KAAKC,IACH2D,EAAWf,EACXiL,EAAK1G,YAAY,KAAKpG,MACtB8M,EAAK1G,YAAY,KAAUpG,OACzB6B,EAIFqL,EAAWF,EAAU,EAALC,EAChBE,EAAiB,EAALF,EACZG,EAAMpO,KAAKqO,KAAKH,EAAWjD,GAC3BqD,EAAMtO,KAAKqO,KAAKF,EAAYlD,GAChCiD,EAAWE,EAAMnD,EACjBkD,EAAYG,EAAMrD,EAMlB,IAAIzH,GAAmBwK,EAAK,EAIxBvK,EAAwB,IAALwK,EAGnBM,EAAMvO,KAAKqO,MACZH,EAAWlO,KAAKuK,IAAIvK,KAAKmK,IAAImD,IAC5Ba,EAAYnO,KAAKuK,IAAIvK,KAAKqK,IAAIiD,KAC9BrC,GAEAuD,EAAMxO,KAAKqO,MACZH,EAAWlO,KAAKuK,IAAIvK,KAAKqK,IAAIiD,IAC5Ba,EAAYnO,KAAKuK,IAAIvK,KAAKmK,IAAImD,KAC9BrC,GAEAjK,EAAQwN,EAAMvD,EACdhK,EAASsN,EAAMtD,EAEnB4C,EAAQY,aAAa,QAASzN,GAC9B6M,EAAQY,aAAa,SAAUxN,GAU/B6M,EAAKY,MAAM,EAAI7L,EAAI,EAAIA,GACvBiL,EAAKa,UAAW3N,EAAQ6B,EAAM,EAAI5B,EAAS4B,EAAM,GACjDiL,EAAKc,QAAQtB,GAIbQ,EAAK3G,KACH3F,EACA,KACCoC,EAAWf,GAAI+C,SAAS,IACzB,MACA2C,EAASC,WAQXsF,EAAKe,UAAY,OACjBf,EAAKgB,aAAe,SACpBhB,EAAKlH,SACHsG,EACA1J,EAAkBX,GACjBY,EAA6B,GAAXG,GAAkBf,GAIvC,IAAIkM,EAAYjB,EAAKnH,aAAa,EAAG,EAAG3F,EAAOC,GAAQX,KAEvD,GAAIuM,IACF,OAAO,EAcT,IAJA,IAEI1J,EAAIJ,EAAGG,EAFP8L,EAAW,GACXhM,EAAKwL,EAELS,EAAS,CAACV,EAAM,EAAGC,EAAM,EAAGD,EAAM,EAAGC,EAAM,GACxCxL,KAEL,IADAG,EAAKoL,EACEpL,KAAM,CACXD,EAAI+H,EAEJiE,EAAgB,KAAOhM,KAErB,IADAH,EAAIkI,EACGlI,KACL,GAAIgM,EAAkD,IAAtC5L,EAAK8H,EAAI/H,GAAKlC,GAASgC,EAAKiI,EAAIlI,IAAU,GAAI,CAC5DiM,EAASrJ,KAAK,CAAC3C,EAAIG,IAEfH,EAAKiM,EAAO,KACdA,EAAO,GAAKjM,GAEVA,EAAKiM,EAAO,KACdA,EAAO,GAAKjM,GAEVG,EAAK8L,EAAO,KACdA,EAAO,GAAK9L,GAEVA,EAAK8L,EAAO,KACdA,EAAO,GAAK9L,GAOd,MAAM+L,GAsBhB,MAAO,CACLrM,GAAIA,EACJmM,SAAUA,EACVC,OAAQA,EACRhM,GAAIuL,EACJpL,GAAImL,EACJ/K,gBAAiBA,EACjBC,gBAAiBA,EACjB0L,cAAenB,EACfoB,eAAgBnB,EAChBrK,SAAUA,GAmPDyL,CAAYnC,EAAMC,EAAQG,EAAWE,GAGhD,IAAK5K,EACH,OAAO,EAGT,GAAIiK,IACF,OAAO,EAMT,IAAKtE,EAASlH,iBAAmBkH,EAASjH,YAAa,CACrD,IAAI2N,EAASrM,EAAKqM,OAClB,GAAIA,EAAO,GAAKA,EAAO,GAAK,EAAIvE,GAAOuE,EAAO,GAAKA,EAAO,GAAK,EAAItE,EACjE,OAAO,EA6CX,IAvCA,IAAI2E,EAAIzE,EAAY,EAuCbyE,KAAK,CACV,IAAI3C,EAASJ,EAAkB1B,EAAYyE,GAEvC/G,EAASkB,UACXkD,EAAS,GAAG4C,OAAO5C,GACnBtF,EAAasF,IAMf,IAAK,IAAInF,EAAI,EAAGA,EAAImF,EAAOtH,OAAQmC,IAAK,CACtC,IAAIgI,GAjD4BnC,EAiDFV,EAAOnF,GAhDnCxE,SACAG,SADAH,EAAKhD,KAAKE,MAAMmN,EAAI,GAAKzK,EAAKK,GAAK,GACnCE,EAAKnD,KAAKE,MAAMmN,EAAI,GAAKzK,EAAKQ,GAAK,GAC9BR,EAAKK,GACLL,EAAKQ,KA3QD,SAAoBJ,EAAIG,EAAIF,EAAIG,EAAI4L,GAInD,IADA,IAAIxH,EAAIwH,EAAS3J,OACVmC,KAAK,CACV,IAAIiI,EAAKzM,EAAKgM,EAASxH,GAAG,GACtBkI,EAAKvM,EAAK6L,EAASxH,GAAG,GAE1B,GAAIiI,GAAM/E,GAAOgF,GAAM/E,GAAO8E,EAAK,GAAKC,EAAK,GAC3C,IAAKnH,EAASlH,eACZ,OAAO,OAKX,IAAKoJ,EAAKgF,GAAIC,GACZ,OAAO,EAGX,OAAO,EA4PAC,CAAW3M,EAAIG,EAAIF,EAAIG,EAAIR,EAAKoM,YAxP1B,SACbhM,EACAG,EACAP,EACAsK,EACAC,EACAyC,EACA1F,EACAoD,EACAF,EACAI,GAEA,IACI/E,EAeAjH,EAOAoI,EAvBAhG,EAAWhB,EAAKgB,SAGlB6E,EADEsC,EACMA,EACNmC,EACAC,EACAvJ,EACAgM,EACA1F,EACAsD,GAGMjF,EAASE,MAMjBjH,EADEwJ,EACWA,EAAkBkC,EAAMC,EAAQvJ,EAAU4J,GAE1CjF,EAAS/G,WAKtBoI,EADEwB,EACQA,EAAe8B,EAAMC,EAAQvJ,EAAU4J,GAEvCjF,EAASqB,QAGrBjC,EAASM,SAAQ,SAAUC,GACzB,GAAIA,EAAGzB,WAAY,CACjB,IAAIC,EAAMwB,EAAGzB,WAAW,MACpB5D,EAAKD,EAAKC,GAGd6D,EAAImJ,OACJnJ,EAAIgI,MAAM,EAAI7L,EAAI,EAAIA,GAEtB6D,EAAIS,KACF3F,EACA,KACCoC,EAAWf,GAAI+C,SAAS,IACzB,MACA2C,EAASC,WACX9B,EAAImI,UAAYpG,EAIhB/B,EAAIiI,WAAW3L,EAAKJ,EAAKK,GAAK,GAAKgI,EAAIpI,GAAKM,EAAKP,EAAKQ,GAAK,GAAK6H,EAAIpI,GAElD,IAAdyK,GACF5G,EAAIkI,QAAQtB,GAUd5G,EAAIoI,aAAe,SACnBpI,EAAIE,SACFsG,EACAtK,EAAKY,gBAAkBX,GACtBD,EAAKa,gBAA6B,GAAXG,GAAkBf,GAQ5C6D,EAAIoJ,cACC,CAEL,IAAIC,EAAOxJ,SAASC,cAAc,QAC9BwJ,EAAgB,GACpBA,EAAgB,WAAc1C,EAAYtN,KAAKuJ,GAAM,IAAM,QAC3C,IAAZ3G,EAAKC,KACPmN,GACE,eACApN,EAAKuM,cAAgB,EADrB,aAIA,EAAIvM,EAAKC,GACT,KAEJ,IAAIoN,EAAa,CACfC,SAAU,WACVC,QAAS,QACThJ,KACE3F,EAAa,IAAMoC,EAAWhB,EAAKC,GAAK,MAAQ0F,EAASC,WAC3D1H,MAAOkC,EAAKJ,EAAKK,GAAK,GAAKgI,EAAIrI,EAAKY,gBAAkB,KACtDzC,KAAMoC,EAAKP,EAAKQ,GAAK,GAAK6H,EAAIrI,EAAKa,gBAAkB,KACrDzC,MAAO4B,EAAKuM,cAAgB,KAC5BlO,OAAQ2B,EAAKwM,eAAiB,KAC9BgB,WAAYxM,EAAW,KACvByM,WAAY,SACZC,UAAWN,EACXO,gBAAiBP,EACjBQ,YAAaR,EACbS,gBAAiB,UACjBC,sBAAuB,UACvBC,kBAAmB,WAMrB,IAAK,IAAIC,KAJLnI,IACFwH,EAAWxH,MAAQA,GAErBsH,EAAKc,YAAc3D,EACC+C,EAClBF,EAAKrN,MAAMkO,GAAWX,EAAWW,GAEnC,GAAIxD,EACF,IAAK,IAAI0D,KAAa1D,EACpB2C,EAAKtB,aAAaqC,EAAW1D,EAAW0D,IAGxClH,IACFmG,EAAKgB,WAAanH,GAEpB1B,EAAGI,YAAYyH,OAuHjBiB,CACEhO,EACAG,EACAP,EACAsK,EACAC,EACAtC,EAAYyE,EACZjC,EAAI,GACJC,EACAF,EACAI,GAxGW,SAAoBxK,EAAIG,EAAIF,EAAIG,EAAIR,EAAMsJ,GACzD,IAEIxF,EAOAyF,EATA6C,EAAWpM,EAAKoM,SAChBjG,EAAWR,EAASQ,SASxB,GAPIA,KACFrC,EAAMiB,EAAS,GAAGlB,WAAW,OACzBoJ,OACJnJ,EAAImI,UAAYtG,EAASS,WAIvBsC,EAAa,CACf,IAAI2D,EAASrM,EAAKqM,OAClB9C,EAAY,CACVpJ,GAAIC,EAAKiM,EAAO,IAAMhE,EACtB/H,GAAIC,EAAK8L,EAAO,IAAMhE,EACtBgG,GAAIhC,EAAO,GAAKA,EAAO,GAAK,GAAKhE,EACjCiG,GAAIjC,EAAO,GAAKA,EAAO,GAAK,GAAKhE,GAKrC,IADA,IAAIzD,EAAIwH,EAAS3J,OACVmC,KAAK,CACV,IAAIiI,EAAKzM,EAAKgM,EAASxH,GAAG,GACtBkI,EAAKvM,EAAK6L,EAASxH,GAAG,GAEtBiI,GAAM/E,GAAOgF,GAAM/E,GAAO8E,EAAK,GAAKC,EAAK,GAI7C3C,EAAW0C,EAAIC,EAAI3G,EAAUoD,EAAWD,GAGtCnD,GACFrC,EAAIoJ,UA0EJqB,CAAWnO,EAAIG,EAAIF,EAAIG,EAAIR,EAAMsJ,GAE1B,CACLlJ,GAAIA,EACJG,GAAIA,EACJG,IAAKgK,EACL1K,KAAMA,KAiBN,GAAI4M,EACF,OAAOA,EAUX,GAAIjH,EAASjH,YAMX,OALIuF,MAAMmB,QAAQkE,GAChBA,EAAK,GAAgB,EAAVA,EAAK,GAAU,EAE1BA,EAAKiB,OAAwB,EAAdjB,EAAKiB,OAAc,EAE7BF,EAAQf,GAInB,OAAO,MAKLkF,EAAY,SAAmB/R,EAAMgS,EAAYC,GACnD,GAAID,EACF,OAAQ1J,EAASZ,MAAK,SAAUmB,GAC9B,IAAIqJ,EAAQ,IAAIC,YAAYnS,EAAM,CAChCoS,OAAQH,GAAW,KAErB,OAAQpJ,EAAGwJ,cAAcH,KACxBzR,MAEH6H,EAASM,SAAQ,SAAUC,GACzB,IAAIqJ,EAAQ,IAAIC,YAAYnS,EAAM,CAChCoS,OAAQH,GAAW,KAErBpJ,EAAGwJ,cAAcH,KAChBzR,QAKK,WAGV,IAAIwG,EAASqB,EAAS,GAEtB,GAAIrB,EAAOG,WACTiE,EAAM1K,KAAKqO,KAAK/H,EAAOtF,MAAQiK,GAC/BN,EAAM3K,KAAKqO,KAAK/H,EAAOrF,OAASgK,OAC3B,CACL,IAAIW,EAAOtF,EAAOuF,wBAClBnB,EAAM1K,KAAKqO,KAAKzC,EAAK5K,MAAQiK,GAC7BN,EAAM3K,KAAKqO,KAAKzC,EAAK3K,OAASgK,GAKhC,GAAKmG,EAAU,kBAAkB,GAAjC,CAgBA,IAAIpO,EAAIG,EAAIqE,EAyGRmK,EAAiBC,EAxGrB,GAZAhH,EAASrC,EAASO,OACd,CAACP,EAASO,OAAO,GAAKmC,EAAG1C,EAASO,OAAO,GAAKmC,GAC9C,CAACP,EAAM,EAAGC,EAAM,GAGpBE,EAAY7K,KAAKE,MAAMF,KAAKwK,KAAKE,EAAMA,EAAMC,EAAMA,IAInDF,EAAO,IAGFnE,EAAOG,YAAc8B,EAASK,YAgBjC,IAfAjB,EAASM,SAAQ,SAAUC,GACzB,GAAIA,EAAGzB,WAAY,CACjB,IAAIC,EAAMwB,EAAGzB,WAAW,MACxBC,EAAImI,UAAYtG,EAASM,gBACzBnC,EAAImL,UAAU,EAAG,EAAGnH,GAAOO,EAAI,GAAIN,GAAOM,EAAI,IAC9CvE,EAAIsG,SAAS,EAAG,EAAGtC,GAAOO,EAAI,GAAIN,GAAOM,EAAI,SAE7C/C,EAAG2I,YAAc,GACjB3I,EAAGxF,MAAMmG,gBAAkBN,EAASM,gBACpCX,EAAGxF,MAAMwN,SAAW,cAKxBlN,EAAK0H,EACE1H,KAGL,IAFAyH,EAAKzH,GAAM,GACXG,EAAKwH,EACExH,KACLsH,EAAKzH,GAAIG,IAAM,MAGd,CAGL,IAAI2O,EAAOvL,SAASC,cAAc,UAAUC,WAAW,MAEvDqL,EAAKjD,UAAYtG,EAASM,gBAC1BiJ,EAAK9E,SAAS,EAAG,EAAG,EAAG,GACvB,IAUIjK,EAAGG,EAVH6O,EAAUD,EAAKnL,aAAa,EAAG,EAAG,EAAG,GAAGrG,KAKxCyO,EAAYzI,EACbG,WAAW,MACXE,aAAa,EAAG,EAAG+D,EAAMO,EAAGN,EAAMM,GAAG3K,KAIxC,IAFA0C,EAAK0H,EAEE1H,KAGL,IAFAyH,EAAKzH,GAAM,GACXG,EAAKwH,EACExH,KAAM,CACXD,EAAI+H,EAEJiE,EAAgB,KAAOhM,KAErB,IADAH,EAAIkI,EACGlI,KAEL,IADAyE,EAAI,EACGA,KACL,GACEuH,EAAoD,IAAxC5L,EAAK8H,EAAI/H,GAAKwH,EAAMO,GAAKjI,EAAKiI,EAAIlI,IAAUyE,KACxDuK,EAAQvK,GACR,CACAiD,EAAKzH,GAAIG,IAAM,EACf,MAAM+L,GAKO,IAAjBzE,EAAKzH,GAAIG,KACXsH,EAAKzH,GAAIG,IAAM,GAKrB4L,EAAY+C,EAAOC,OAAU9M,EAI/B,GAAIsD,EAASsB,OAAStB,EAASuB,MAAO,CAKpC,IAJAwB,GAAc,EAGdtI,EAAK0H,EAAM,EACJ1H,KACLuI,EAASvI,GAAM,GAGbuF,EAASsB,OACXvD,EAAOvB,iBAAiB,YAAakH,GAGnC1D,EAASuB,QACXxD,EAAOvB,iBAAiB,QAASqH,GACjC9F,EAAOvB,iBAAiB,aAAcqH,GACtC9F,EAAOvB,iBAAiB,YAAY,SAAUiN,GAC5CA,EAAE3F,oBAEJ/F,EAAO5D,MAAMuP,wBAA0B,oBAGzC3L,EAAOvB,iBAAiB,kBAAkB,SAASmN,IACjD5L,EAAO6L,oBAAoB,iBAAkBD,GAE7C5L,EAAO6L,oBAAoB,YAAalG,GACxC3F,EAAO6L,oBAAoB,QAAS/F,GACpCf,OAAUpG,KAIduC,EAAI,EAEJ,IAAI4K,GAAY,EACX7J,EAASW,gBAOe,IAAlBX,EAASY,MAClBwI,EAAkBnN,OAAOsB,WACzB8L,EAAmBpN,OAAO4B,eAE1BuL,EAAkBnN,OAAOC,aACzBmN,EAAmBpN,OAAOiB,iBAX1BkM,EAAkB,SAAUU,GAC1BA,KAEFT,EAAmB,WACjBQ,GAAY,IAUhB,IAMID,EAAsB,SAA6B9S,EAAMiT,GAC3D3K,EAASM,SAAQ,SAAUC,GACzBA,EAAGiK,oBAAoB9S,EAAMiT,KAC5BxS,OAGDyS,EAAwB,SAASA,IACnCJ,EAAoB,iBAAkBI,GACtCX,EAAiBzL,EAAM0B,MAdF,SAA0BxI,EAAMiT,GACrD3K,EAASM,SAAQ,SAAUC,GACzBA,EAAGnD,iBAeU,iBAfauN,KACzBxS,MAcLiF,CAAiB,EAAkBwN,GAGnCpM,EAAM0B,IAAYU,EAASW,gBAAkByI,EAAkB7L,aAC7D,SAAS0M,IACP,GAAKJ,EAAL,CAGA,GAAI5K,GAAKe,EAAS/H,KAAK6E,OAKrB,OAJAuM,EAAiBzL,EAAM0B,IACvBuJ,EAAU,iBAAiB,GAC3Be,EAAoB,iBAAkBI,eAC/BpM,EAAM0B,GAGfiD,GAAa,IAAIhD,MAAOgF,UACxB,IAAI1K,EAAQ6K,EAAQ1E,EAAS/H,KAAKgH,IAC9BiL,GAAYrB,EAAU,kBAAkB,EAAM,CAChDlF,KAAM3D,EAAS/H,KAAKgH,GACpBpF,MAAOA,IAET,GAAIyK,KAAgB4F,EAMlB,OALAb,EAAiBzL,EAAM0B,IACvBU,EAASc,QACT+H,EAAU,kBAAkB,GAC5BA,EAAU,iBAAiB,QAC3Be,EAAoB,iBAAkBI,GAGxC/K,IACArB,EAAM0B,GAAW8J,EAAgBa,EAAMjK,EAASY,SAElDZ,EAASY,OAKbuJ,GA16BA,SAASvH,EAAeb,EAAKrK,GAC3B,MACE,QACiB,IAAhBD,KAAKyH,UAAgBkL,UACtB,KACiB,GAAhB3S,KAAKyH,SAAgB,IAAIkL,UAC1B,MACC3S,KAAKyH,UAAYxH,EAAMqK,GAAOA,GAAKqI,UACpC,OAq6BNjL,EAAUrB,YAAcA,EACxBqB,EAAUV,YAAcA,EAExB,UC1yCA,IAAK,cACH,MAAM,IAAIoB,MAAM,4CA8ClB,kBAAuB,SAAUhI,EAASuB,GACxCvB,EAAQwS,iBAAiB,aAAa,SAAUlR,GAC9C,IAAImR,EAAW,uBACbnR,EAAYoR,qBACZ,CACE9R,MAAOW,EAAIoR,WACX9R,OAAQU,EAAIqR,cAIZnS,EAAaa,EAAYhC,IAAI,cAC7BiB,EAAYe,EAAYhC,IAAI,aAC5BuT,EAAQtS,EAAYA,EAAUK,MAAQL,EAAUM,OAAS,EAC7DJ,GAqIJ,SAA0BgS,EAAUK,GAGlC,IAAIlS,EAAQ6R,EAAS7R,MACjBC,EAAS4R,EAAS5R,OAClBD,EAAQC,EAASiS,GACnBL,EAAS9P,IAAM/B,EAAQC,EAASiS,GAAU,EAC1CL,EAAS7R,MAAQC,EAASiS,IAE1BL,EAAS3P,IAAMjC,EAASD,EAAQkS,GAAU,EAC1CL,EAAS5R,OAASD,EAAQkS,GA/IZC,CAAiBN,EAAUI,GAEzC,IAAI3S,EAAOoB,EAAYI,UAEnBwE,EAASC,SAASC,cAAc,UACpCF,EAAOtF,MAAQ6R,EAAS7R,MACxBsF,EAAOrF,OAAS4R,EAAS5R,OAEzB,IAAIyF,EAAMJ,EAAOG,WAAW,MAC5B,GAAI9F,EACF,IACE+F,EAAI0M,UAAUzS,EAAW,EAAG,EAAG2F,EAAOtF,MAAOsF,EAAOrF,QAlE5D,SAA0BoS,GAOxB,IANA,IAAI3M,EAAM2M,EAAW5M,WAAW,MAC5BsI,EAAYrI,EAAIC,aAAa,EAAG,EAAG0M,EAAWrS,MAAOqS,EAAWpS,QAChEqS,EAAe5M,EAAI6M,gBAAgBxE,GAEnCyE,EAAU,EACVC,EAAU,EACLjM,EAAI,EAAGA,EAAIuH,EAAUzO,KAAK+E,OAAQmC,GAAK,EAClCuH,EAAUzO,KAAKkH,EAAI,GACnB,MAGVgM,GAFIE,EACF3E,EAAUzO,KAAKkH,GAAKuH,EAAUzO,KAAKkH,EAAI,GAAKuH,EAAUzO,KAAKkH,EAAI,KAE/DiM,GAGN,IAAIE,EAAYH,EAAUC,EAE1B,IAASjM,EAAI,EAAGA,EAAIuH,EAAUzO,KAAK+E,OAAQmC,GAAK,EAAG,CACjD,IAAIkM,EACF3E,EAAUzO,KAAKkH,GAAKuH,EAAUzO,KAAKkH,EAAI,GAAKuH,EAAUzO,KAAKkH,EAAI,GACrDuH,EAAUzO,KAAKkH,EAAI,GAEnB,KAAOkM,EAAOC,GAExBL,EAAahT,KAAKkH,GAAK,EACvB8L,EAAahT,KAAKkH,EAAI,GAAK,EAC3B8L,EAAahT,KAAKkH,EAAI,GAAK,EAC3B8L,EAAahT,KAAKkH,EAAI,GAAK,IAI3B8L,EAAahT,KAAKkH,GAAK,IACvB8L,EAAahT,KAAKkH,EAAI,GAAK,IAC3B8L,EAAahT,KAAKkH,EAAI,GAAK,IAC3B8L,EAAahT,KAAKkH,EAAI,GAAK,KAI/Bd,EAAIkN,aAAaN,EAAc,EAAG,GA4B5BO,CAAiBvN,GACjB,MAAO0L,GACP8B,QAAQC,MAAM,sBACdD,QAAQC,MAAM/B,EAAEpM,YAIpB,IAAI1E,EAAYQ,EAAYhC,IAAI,aAC5ByB,EAAgBO,EAAYhC,IAAI,iBAChCsU,EAAc1T,EAAK2T,cAAc,SAEjCC,EAAgBlU,KAAKuJ,GAAK,IAC1BxJ,EAAW2B,EAAYhC,IAAI,YAgD/B,SAASyU,EAAiBnC,GACxB,IAAI9F,EAAO8F,EAAEP,OAAOvF,KAChB8F,EAAEP,OAAOrP,OAASV,EAAYK,eAAeC,SAC/CgQ,EAAEP,OAAOrP,MAAMY,IAAM6P,EAAS9P,EAAIhD,EAClCiS,EAAEP,OAAOrP,MAAMe,IAAM0P,EAAS3P,EAAInD,EAClC2B,EAAYK,eAAeC,OACzBkK,EAAK,GACLA,EAAK,GACLA,EAAK,GACL8F,EAAEP,OAAOrP,QAxDf,EAAsBkE,EAAQ,CAC5B9F,KAAMF,EACH8T,SAAS,SAAS,SAAUC,EAAOC,GAClC,IAAIjS,EAAY/B,EAAKgC,aAAagS,GAClC,MAAO,CACLhU,EAAKiU,QAAQD,GACbjS,EAAU3C,IAAI,sBAAsB,IAClC,mBAAyB2U,EAAOL,EAAa9S,GAC/CoT,MAGHE,MAAK,SAAUC,EAAGC,GAEjB,OAAOA,EAAE,GAAKD,EAAE,MAEpBjM,WACE9G,EAAYhC,IAAI,yBAChBgC,EAAYhC,IAAI,kCAChBU,EAAQV,IAAI,wBACd8B,WACEE,EAAYhC,IAAI,yBAChBgC,EAAYhC,IAAI,kCAChBU,EAAQV,IAAI,wBAEdK,SAAUA,EAEV4J,YAAakJ,EAAS5R,OAAS4R,EAAS7R,MAExCsI,YAAanI,EAAc,GAAK+S,EAChC1K,YAAarI,EAAc,GAAK+S,EAEhCtL,aAAcjI,EAEd+I,YAAa,EAEbtI,aAAcM,EAAYhC,IAAI,gBAAkBwU,EAEhD7S,eAAgBK,EAAYhC,IAAI,kBAChC4B,YAAaI,EAAYhC,IAAI,eAE7BwJ,gBAAiBxH,EAAYhC,IAAI,mBAEjC+J,SAAS,EAET7I,MAAOc,EAAYhC,IAAI,WAiBzB4G,EAAOvB,iBAAiB,iBAAkBoP,GAEtCzS,EAAYK,gBAEdL,EAAYK,eAAewC,UAG7B7C,EAAYK,eAAiB,CAC3BC,OAAQ,KAERuC,QAAS,WACP+B,EAAO6L,oBAAoB,iBAAkBgC,GAE7C7N,EAAOvB,iBAAiB,kBAAkB,SAAUiN,GAElDA,EAAE3F,4BAOZ,wBAA6B,SAAUxM,GACrC,IAAI8U,GAAU9U,GAAU,IAAI8U,QAC3B,eAAqBA,KAAYA,EAASA,EAAS,CAACA,GAAU,IAE/D,IAAIC,EAAU,CAAC,cAAe,aAAc,gBAAiB,iBAW7D,SAASC,EAAgBtT,GACvBA,GACE,YAAkBqT,GAAS,SAAU7K,GAC/BxI,EAAUuT,eAAe/K,KAC3BxI,EAAU,OAAS,sBAA4BwI,IAAQxI,EAAUwI,OAbzE,YAAkB4K,GAAQ,SAAUI,GAClC,GAAIA,GAAkC,cAApBA,EAAW1V,KAAsB,CACjD,IAAIkC,EAAYwT,EAAWxT,WAAa,GAExCsT,EAAgBtT,EAAUyT,QAC1BH,EAAgBtT,EAAU0T,kB,OC1LhClW,EAAOD,QAAUM,ICCb8V,EAA2B,GAG/B,SAASC,EAAoBC,GAE5B,GAAGF,EAAyBE,GAC3B,OAAOF,EAAyBE,GAAUtW,QAG3C,IAAIC,EAASmW,EAAyBE,GAAY,CAGjDtW,QAAS,IAOV,OAHAuW,EAAoBD,GAAUrW,EAAQA,EAAOD,QAASqW,GAG/CpW,EAAOD,QCjBf,OCFAqW,EAAoB7F,EAAKxQ,IACH,oBAAXwW,QAA0BA,OAAOC,aAC1CC,OAAOC,eAAe3W,EAASwW,OAAOC,YAAa,CAAElB,MAAO,WAE7DmB,OAAOC,eAAe3W,EAAS,aAAc,CAAEuV,OAAO,KDFhDc,EAAoB,M", "file": "echarts-wordcloud.min.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"echarts\"));\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([\"echarts\"], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"echarts-wordcloud\"] = factory(require(\"echarts\"));\n\telse\n\t\troot[\"echarts-wordcloud\"] = factory(root[\"echarts\"]);\n})(self, function(__WEBPACK_EXTERNAL_MODULE__83__) {\nreturn ", "import * as echarts from 'echarts/lib/echarts';\n\necharts.extendSeriesModel({\n  type: 'series.wordCloud',\n\n  visualStyleAccessPath: 'textStyle',\n  visualStyleMapper: function (model) {\n    return {\n      fill: model.get('color')\n    };\n  },\n  visualDrawType: 'fill',\n\n  optionUpdated: function () {\n    var option = this.option;\n    option.gridSize = Math.max(Math.floor(option.gridSize), 4);\n  },\n\n  getInitialData: function (option, ecModel) {\n    var dimensions = echarts.helper.createDimensions(option.data, {\n      coordDimensions: ['value']\n    });\n    var list = new echarts.List(dimensions, this);\n    list.initData(option.data);\n    return list;\n  },\n\n  // Most of options are from https://github.com/timdream/wordcloud2.js/blob/gh-pages/API.md\n  defaultOption: {\n    maskImage: null,\n\n    // Shape can be 'circle', 'cardioid', 'diamond', 'triangle-forward', 'triangle', 'pentagon', 'star'\n    shape: 'circle',\n    keepAspect: false,\n\n    left: 'center',\n\n    top: 'center',\n\n    width: '70%',\n\n    height: '80%',\n\n    sizeRange: [12, 60],\n\n    rotationRange: [-90, 90],\n\n    rotationStep: 45,\n\n    gridSize: 8,\n\n    drawOutOfBound: false,\n    shrinkToFit: false,\n\n    textStyle: {\n      fontWeight: 'normal'\n    }\n  }\n});\n", "import * as echarts from 'echarts/lib/echarts';\n\necharts.extendChartView({\n  type: 'wordCloud',\n\n  render: function (seriesModel, ecModel, api) {\n    var group = this.group;\n    group.removeAll();\n\n    var data = seriesModel.getData();\n\n    var gridSize = seriesModel.get('gridSize');\n\n    seriesModel.layoutInstance.ondraw = function (text, size, dataIdx, drawn) {\n      var itemModel = data.getItemModel(dataIdx);\n      var textStyleModel = itemModel.getModel('textStyle');\n\n      var textEl = new echarts.graphic.Text({\n        style: echarts.helper.createTextStyle(textStyleModel),\n        scaleX: 1 / drawn.info.mu,\n        scaleY: 1 / drawn.info.mu,\n        x: (drawn.gx + drawn.info.gw / 2) * gridSize,\n        y: (drawn.gy + drawn.info.gh / 2) * gridSize,\n        rotation: drawn.rot\n      });\n      textEl.setStyle({\n        x: drawn.info.fillTextOffsetX,\n        y: drawn.info.fillTextOffsetY + size * 0.5,\n        text: text,\n        verticalAlign: 'middle',\n        fill: data.getItemVisual(dataIdx, 'style').fill,\n        fontSize: size\n      });\n\n      group.add(textEl);\n\n      data.setItemGraphicEl(dataIdx, textEl);\n\n      textEl.ensureState('emphasis').style = echarts.helper.createTextStyle(\n        itemModel.getModel(['emphasis', 'textStyle']),\n        {\n          state: 'emphasis'\n        }\n      );\n      textEl.ensureState('blur').style = echarts.helper.createTextStyle(\n        itemModel.getModel(['blur', 'textStyle']),\n        {\n          state: 'blur'\n        }\n      );\n\n      echarts.helper.enableHoverEmphasis(\n        textEl,\n        itemModel.get(['emphasis', 'focus']),\n        itemModel.get(['emphasis', 'blurScope'])\n      );\n\n      textEl.stateTransition = {\n        duration: seriesModel.get('animation')\n          ? seriesModel.get(['stateAnimation', 'duration'])\n          : 0,\n        easing: seriesModel.get(['stateAnimation', 'easing'])\n      };\n      // TODO\n      textEl.__highDownDispatcher = true;\n    };\n\n    this._model = seriesModel;\n  },\n\n  remove: function () {\n    this.group.removeAll();\n\n    this._model.layoutInstance.dispose();\n  },\n\n  dispose: function () {\n    this._model.layoutInstance.dispose();\n  }\n});\n", "/*!\n * wordcloud2.js\n * http://timdream.org/wordcloud2.js/\n *\n * Copyright 2011 - 2019 <PERSON> and contributors.\n * Released under the MIT license\n */\n\n'use strict';\n\n// setImmediate\nif (!window.setImmediate) {\n  window.setImmediate = (function setupSetImmediate() {\n    return (\n      window.msSetImmediate ||\n      window.webkitSetImmediate ||\n      window.mozSetImmediate ||\n      window.oSetImmediate ||\n      (function setupSetZeroTimeout() {\n        if (!window.postMessage || !window.addEventListener) {\n          return null;\n        }\n\n        var callbacks = [undefined];\n        var message = 'zero-timeout-message';\n\n        // Like setTimeout, but only takes a function argument.  There's\n        // no time argument (always zero) and no arguments (you have to\n        // use a closure).\n        var setZeroTimeout = function setZeroTimeout(callback) {\n          var id = callbacks.length;\n          callbacks.push(callback);\n          window.postMessage(message + id.toString(36), '*');\n\n          return id;\n        };\n\n        window.addEventListener(\n          'message',\n          function setZeroTimeoutMessage(evt) {\n            // Skipping checking event source, retarded IE confused this window\n            // object with another in the presence of iframe\n            if (\n              typeof evt.data !== 'string' ||\n              evt.data.substr(0, message.length) !== message /* ||\n            evt.source !== window */\n            ) {\n              return;\n            }\n\n            evt.stopImmediatePropagation();\n\n            var id = parseInt(evt.data.substr(message.length), 36);\n            if (!callbacks[id]) {\n              return;\n            }\n\n            callbacks[id]();\n            callbacks[id] = undefined;\n          },\n          true\n        );\n\n        /* specify clearImmediate() here since we need the scope */\n        window.clearImmediate = function clearZeroTimeout(id) {\n          if (!callbacks[id]) {\n            return;\n          }\n\n          callbacks[id] = undefined;\n        };\n\n        return setZeroTimeout;\n      })() ||\n      // fallback\n      function setImmediateFallback(fn) {\n        window.setTimeout(fn, 0);\n      }\n    );\n  })();\n}\n\nif (!window.clearImmediate) {\n  window.clearImmediate = (function setupClearImmediate() {\n    return (\n      window.msClearImmediate ||\n      window.webkitClearImmediate ||\n      window.mozClearImmediate ||\n      window.oClearImmediate ||\n      // \"clearZeroTimeout\" is implement on the previous block ||\n      // fallback\n      function clearImmediateFallback(timer) {\n        window.clearTimeout(timer);\n      }\n    );\n  })();\n}\n\n// Check if WordCloud can run on this browser\nvar isSupported = (function isSupported() {\n  var canvas = document.createElement('canvas');\n  if (!canvas || !canvas.getContext) {\n    return false;\n  }\n\n  var ctx = canvas.getContext('2d');\n  if (!ctx) {\n    return false;\n  }\n  if (!ctx.getImageData) {\n    return false;\n  }\n  if (!ctx.fillText) {\n    return false;\n  }\n\n  if (!Array.prototype.some) {\n    return false;\n  }\n  if (!Array.prototype.push) {\n    return false;\n  }\n\n  return true;\n})();\n\n// Find out if the browser impose minium font size by\n// drawing small texts on a canvas and measure it's width.\nvar minFontSize = (function getMinFontSize() {\n  if (!isSupported) {\n    return;\n  }\n\n  var ctx = document.createElement('canvas').getContext('2d');\n\n  // start from 20\n  var size = 20;\n\n  // two sizes to measure\n  var hanWidth, mWidth;\n\n  while (size) {\n    ctx.font = size.toString(10) + 'px sans-serif';\n    if (\n      ctx.measureText('\\uFF37').width === hanWidth &&\n      ctx.measureText('m').width === mWidth\n    ) {\n      return size + 1;\n    }\n\n    hanWidth = ctx.measureText('\\uFF37').width;\n    mWidth = ctx.measureText('m').width;\n\n    size--;\n  }\n\n  return 0;\n})();\n\nvar getItemExtraData = function (item) {\n  if (Array.isArray(item)) {\n    var itemCopy = item.slice();\n    // remove data we already have (word and weight)\n    itemCopy.splice(0, 2);\n    return itemCopy;\n  } else {\n    return [];\n  }\n};\n\n// Based on http://jsfromhell.com/array/shuffle\nvar shuffleArray = function shuffleArray(arr) {\n  for (var j, x, i = arr.length; i; ) {\n    j = Math.floor(Math.random() * i);\n    x = arr[--i];\n    arr[i] = arr[j];\n    arr[j] = x;\n  }\n  return arr;\n};\n\nvar timer = {};\nvar WordCloud = function WordCloud(elements, options) {\n  if (!isSupported) {\n    return;\n  }\n\n  var timerId = Math.floor(Math.random() * Date.now());\n\n  if (!Array.isArray(elements)) {\n    elements = [elements];\n  }\n\n  elements.forEach(function (el, i) {\n    if (typeof el === 'string') {\n      elements[i] = document.getElementById(el);\n      if (!elements[i]) {\n        throw new Error('The element id specified is not found.');\n      }\n    } else if (!el.tagName && !el.appendChild) {\n      throw new Error(\n        'You must pass valid HTML elements, or ID of the element.'\n      );\n    }\n  });\n\n  /* Default values to be overwritten by options object */\n  var settings = {\n    list: [],\n    fontFamily:\n      '\"Trebuchet MS\", \"Heiti TC\", \"微軟正黑體\", ' +\n      '\"Arial Unicode MS\", \"Droid Fallback Sans\", sans-serif',\n    fontWeight: 'normal',\n    color: 'random-dark',\n    minSize: 0, // 0 to disable\n    weightFactor: 1,\n    clearCanvas: true,\n    backgroundColor: '#fff', // opaque white = rgba(255, 255, 255, 1)\n\n    gridSize: 8,\n    drawOutOfBound: false,\n    shrinkToFit: false,\n    origin: null,\n\n    drawMask: false,\n    maskColor: 'rgba(255,0,0,0.3)',\n    maskGapWidth: 0.3,\n\n    layoutAnimation: true,\n\n    wait: 0,\n    abortThreshold: 0, // disabled\n    abort: function noop() {},\n\n    minRotation: -Math.PI / 2,\n    maxRotation: Math.PI / 2,\n    rotationStep: 0.1,\n\n    shuffle: true,\n    rotateRatio: 0.1,\n\n    shape: 'circle',\n    ellipticity: 0.65,\n\n    classes: null,\n\n    hover: null,\n    click: null\n  };\n\n  if (options) {\n    for (var key in options) {\n      if (key in settings) {\n        settings[key] = options[key];\n      }\n    }\n  }\n\n  /* Convert weightFactor into a function */\n  if (typeof settings.weightFactor !== 'function') {\n    var factor = settings.weightFactor;\n    settings.weightFactor = function weightFactor(pt) {\n      return pt * factor; // in px\n    };\n  }\n\n  /* Convert shape into a function */\n  if (typeof settings.shape !== 'function') {\n    switch (settings.shape) {\n      case 'circle':\n      /* falls through */\n      default:\n        // 'circle' is the default and a shortcut in the code loop.\n        settings.shape = 'circle';\n        break;\n\n      case 'cardioid':\n        settings.shape = function shapeCardioid(theta) {\n          return 1 - Math.sin(theta);\n        };\n        break;\n\n      /*\n        To work out an X-gon, one has to calculate \"m\",\n        where 1/(cos(2*PI/X)+m*sin(2*PI/X)) = 1/(cos(0)+m*sin(0))\n        http://www.wolframalpha.com/input/?i=1%2F%28cos%282*PI%2FX%29%2Bm*sin%28\n        2*PI%2FX%29%29+%3D+1%2F%28cos%280%29%2Bm*sin%280%29%29\n        Copy the solution into polar equation r = 1/(cos(t') + m*sin(t'))\n        where t' equals to mod(t, 2PI/X);\n        */\n\n      case 'diamond':\n        // http://www.wolframalpha.com/input/?i=plot+r+%3D+1%2F%28cos%28mod+\n        // %28t%2C+PI%2F2%29%29%2Bsin%28mod+%28t%2C+PI%2F2%29%29%29%2C+t+%3D\n        // +0+..+2*PI\n        settings.shape = function shapeSquare(theta) {\n          var thetaPrime = theta % ((2 * Math.PI) / 4);\n          return 1 / (Math.cos(thetaPrime) + Math.sin(thetaPrime));\n        };\n        break;\n\n      case 'square':\n        // http://www.wolframalpha.com/input/?i=plot+r+%3D+min(1%2Fabs(cos(t\n        // )),1%2Fabs(sin(t)))),+t+%3D+0+..+2*PI\n        settings.shape = function shapeSquare(theta) {\n          return Math.min(\n            1 / Math.abs(Math.cos(theta)),\n            1 / Math.abs(Math.sin(theta))\n          );\n        };\n        break;\n\n      case 'triangle-forward':\n        // http://www.wolframalpha.com/input/?i=plot+r+%3D+1%2F%28cos%28mod+\n        // %28t%2C+2*PI%2F3%29%29%2Bsqrt%283%29sin%28mod+%28t%2C+2*PI%2F3%29\n        // %29%29%2C+t+%3D+0+..+2*PI\n        settings.shape = function shapeTriangle(theta) {\n          var thetaPrime = theta % ((2 * Math.PI) / 3);\n          return (\n            1 / (Math.cos(thetaPrime) + Math.sqrt(3) * Math.sin(thetaPrime))\n          );\n        };\n        break;\n\n      case 'triangle':\n      case 'triangle-upright':\n        settings.shape = function shapeTriangle(theta) {\n          var thetaPrime = (theta + (Math.PI * 3) / 2) % ((2 * Math.PI) / 3);\n          return (\n            1 / (Math.cos(thetaPrime) + Math.sqrt(3) * Math.sin(thetaPrime))\n          );\n        };\n        break;\n\n      case 'pentagon':\n        settings.shape = function shapePentagon(theta) {\n          var thetaPrime = (theta + 0.955) % ((2 * Math.PI) / 5);\n          return 1 / (Math.cos(thetaPrime) + 0.726543 * Math.sin(thetaPrime));\n        };\n        break;\n\n      case 'star':\n        settings.shape = function shapeStar(theta) {\n          var thetaPrime = (theta + 0.955) % ((2 * Math.PI) / 10);\n          if (\n            ((theta + 0.955) % ((2 * Math.PI) / 5)) - (2 * Math.PI) / 10 >=\n            0\n          ) {\n            return (\n              1 /\n              (Math.cos((2 * Math.PI) / 10 - thetaPrime) +\n                3.07768 * Math.sin((2 * Math.PI) / 10 - thetaPrime))\n            );\n          } else {\n            return 1 / (Math.cos(thetaPrime) + 3.07768 * Math.sin(thetaPrime));\n          }\n        };\n        break;\n    }\n  }\n\n  /* Make sure gridSize is a whole number and is not smaller than 4px */\n  settings.gridSize = Math.max(Math.floor(settings.gridSize), 4);\n\n  /* shorthand */\n  var g = settings.gridSize;\n  var maskRectWidth = g - settings.maskGapWidth;\n\n  /* normalize rotation settings */\n  var rotationRange = Math.abs(settings.maxRotation - settings.minRotation);\n  var minRotation = Math.min(settings.maxRotation, settings.minRotation);\n  var rotationStep = settings.rotationStep;\n\n  /* information/object available to all functions, set when start() */\n  var grid, // 2d array containing filling information\n    ngx,\n    ngy, // width and height of the grid\n    center, // position of the center of the cloud\n    maxRadius;\n\n  /* timestamp for measuring each putWord() action */\n  var escapeTime;\n\n  /* function for getting the color of the text */\n  var getTextColor;\n  function randomHslColor(min, max) {\n    return (\n      'hsl(' +\n      (Math.random() * 360).toFixed() +\n      ',' +\n      (Math.random() * 30 + 70).toFixed() +\n      '%,' +\n      (Math.random() * (max - min) + min).toFixed() +\n      '%)'\n    );\n  }\n  switch (settings.color) {\n    case 'random-dark':\n      getTextColor = function getRandomDarkColor() {\n        return randomHslColor(10, 50);\n      };\n      break;\n\n    case 'random-light':\n      getTextColor = function getRandomLightColor() {\n        return randomHslColor(50, 90);\n      };\n      break;\n\n    default:\n      if (typeof settings.color === 'function') {\n        getTextColor = settings.color;\n      }\n      break;\n  }\n\n  /* function for getting the font-weight of the text */\n  var getTextFontWeight;\n  if (typeof settings.fontWeight === 'function') {\n    getTextFontWeight = settings.fontWeight;\n  }\n\n  /* function for getting the classes of the text */\n  var getTextClasses = null;\n  if (typeof settings.classes === 'function') {\n    getTextClasses = settings.classes;\n  }\n\n  /* Interactive */\n  var interactive = false;\n  var infoGrid = [];\n  var hovered;\n\n  var getInfoGridFromMouseTouchEvent = function getInfoGridFromMouseTouchEvent(\n    evt\n  ) {\n    var canvas = evt.currentTarget;\n    var rect = canvas.getBoundingClientRect();\n    var clientX;\n    var clientY;\n    /** Detect if touches are available */\n    if (evt.touches) {\n      clientX = evt.touches[0].clientX;\n      clientY = evt.touches[0].clientY;\n    } else {\n      clientX = evt.clientX;\n      clientY = evt.clientY;\n    }\n    var eventX = clientX - rect.left;\n    var eventY = clientY - rect.top;\n\n    var x = Math.floor((eventX * (canvas.width / rect.width || 1)) / g);\n    var y = Math.floor((eventY * (canvas.height / rect.height || 1)) / g);\n\n    return infoGrid[x][y];\n  };\n\n  var wordcloudhover = function wordcloudhover(evt) {\n    var info = getInfoGridFromMouseTouchEvent(evt);\n\n    if (hovered === info) {\n      return;\n    }\n\n    hovered = info;\n    if (!info) {\n      settings.hover(undefined, undefined, evt);\n\n      return;\n    }\n\n    settings.hover(info.item, info.dimension, evt);\n  };\n\n  var wordcloudclick = function wordcloudclick(evt) {\n    var info = getInfoGridFromMouseTouchEvent(evt);\n    if (!info) {\n      return;\n    }\n\n    settings.click(info.item, info.dimension, evt);\n    evt.preventDefault();\n  };\n\n  /* Get points on the grid for a given radius away from the center */\n  var pointsAtRadius = [];\n  var getPointsAtRadius = function getPointsAtRadius(radius) {\n    if (pointsAtRadius[radius]) {\n      return pointsAtRadius[radius];\n    }\n\n    // Look for these number of points on each radius\n    var T = radius * 8;\n\n    // Getting all the points at this radius\n    var t = T;\n    var points = [];\n\n    if (radius === 0) {\n      points.push([center[0], center[1], 0]);\n    }\n\n    while (t--) {\n      // distort the radius to put the cloud in shape\n      var rx = 1;\n      if (settings.shape !== 'circle') {\n        rx = settings.shape((t / T) * 2 * Math.PI); // 0 to 1\n      }\n\n      // Push [x, y, t]; t is used solely for getTextColor()\n      points.push([\n        center[0] + radius * rx * Math.cos((-t / T) * 2 * Math.PI),\n        center[1] +\n          radius * rx * Math.sin((-t / T) * 2 * Math.PI) * settings.ellipticity,\n        (t / T) * 2 * Math.PI\n      ]);\n    }\n\n    pointsAtRadius[radius] = points;\n    return points;\n  };\n\n  /* Return true if we had spent too much time */\n  var exceedTime = function exceedTime() {\n    return (\n      settings.abortThreshold > 0 &&\n      new Date().getTime() - escapeTime > settings.abortThreshold\n    );\n  };\n\n  /* Get the deg of rotation according to settings, and luck. */\n  var getRotateDeg = function getRotateDeg() {\n    if (settings.rotateRatio === 0) {\n      return 0;\n    }\n\n    if (Math.random() > settings.rotateRatio) {\n      return 0;\n    }\n\n    if (rotationRange === 0) {\n      return minRotation;\n    }\n\n    return minRotation + Math.round(Math.random() * rotationRange / rotationStep) * rotationStep;\n  };\n\n  var getTextInfo = function getTextInfo(\n    word,\n    weight,\n    rotateDeg,\n    extraDataArray\n  ) {\n    // calculate the acutal font size\n    // fontSize === 0 means weightFactor function wants the text skipped,\n    // and size < minSize means we cannot draw the text.\n    var debug = false;\n    var fontSize = settings.weightFactor(weight);\n    if (fontSize <= settings.minSize) {\n      return false;\n    }\n\n    // Scale factor here is to make sure fillText is not limited by\n    // the minium font size set by browser.\n    // It will always be 1 or 2n.\n    var mu = 1;\n    if (fontSize < minFontSize) {\n      mu = (function calculateScaleFactor() {\n        var mu = 2;\n        while (mu * fontSize < minFontSize) {\n          mu += 2;\n        }\n        return mu;\n      })();\n    }\n\n    // Get fontWeight that will be used to set fctx.font\n    var fontWeight;\n    if (getTextFontWeight) {\n      fontWeight = getTextFontWeight(word, weight, fontSize, extraDataArray);\n    } else {\n      fontWeight = settings.fontWeight;\n    }\n\n    var fcanvas = document.createElement('canvas');\n    var fctx = fcanvas.getContext('2d', { willReadFrequently: true });\n\n    fctx.font =\n      fontWeight +\n      ' ' +\n      (fontSize * mu).toString(10) +\n      'px ' +\n      settings.fontFamily;\n\n    // Estimate the dimension of the text with measureText().\n    var fw = fctx.measureText(word).width / mu;\n    var fh =\n      Math.max(\n        fontSize * mu,\n        fctx.measureText('m').width,\n        fctx.measureText('\\uFF37').width\n      ) / mu;\n\n    // Create a boundary box that is larger than our estimates,\n    // so text don't get cut of (it sill might)\n    var boxWidth = fw + fh * 2;\n    var boxHeight = fh * 3;\n    var fgw = Math.ceil(boxWidth / g);\n    var fgh = Math.ceil(boxHeight / g);\n    boxWidth = fgw * g;\n    boxHeight = fgh * g;\n\n    // Calculate the proper offsets to make the text centered at\n    // the preferred position.\n\n    // This is simply half of the width.\n    var fillTextOffsetX = -fw / 2;\n    // Instead of moving the box to the exact middle of the preferred\n    // position, for Y-offset we move 0.4 instead, so Latin alphabets look\n    // vertical centered.\n    var fillTextOffsetY = -fh * 0.4;\n\n    // Calculate the actual dimension of the canvas, considering the rotation.\n    var cgh = Math.ceil(\n      (boxWidth * Math.abs(Math.sin(rotateDeg)) +\n        boxHeight * Math.abs(Math.cos(rotateDeg))) /\n        g\n    );\n    var cgw = Math.ceil(\n      (boxWidth * Math.abs(Math.cos(rotateDeg)) +\n        boxHeight * Math.abs(Math.sin(rotateDeg))) /\n        g\n    );\n    var width = cgw * g;\n    var height = cgh * g;\n\n    fcanvas.setAttribute('width', width);\n    fcanvas.setAttribute('height', height);\n\n    if (debug) {\n      // Attach fcanvas to the DOM\n      document.body.appendChild(fcanvas);\n      // Save it's state so that we could restore and draw the grid correctly.\n      fctx.save();\n    }\n\n    // Scale the canvas with |mu|.\n    fctx.scale(1 / mu, 1 / mu);\n    fctx.translate((width * mu) / 2, (height * mu) / 2);\n    fctx.rotate(-rotateDeg);\n\n    // Once the width/height is set, ctx info will be reset.\n    // Set it again here.\n    fctx.font =\n      fontWeight +\n      ' ' +\n      (fontSize * mu).toString(10) +\n      'px ' +\n      settings.fontFamily;\n\n    // Fill the text into the fcanvas.\n    // XXX: We cannot because textBaseline = 'top' here because\n    // Firefox and Chrome uses different default line-height for canvas.\n    // Please read https://bugzil.la/737852#c6.\n    // Here, we use textBaseline = 'middle' and draw the text at exactly\n    // 0.5 * fontSize lower.\n    fctx.fillStyle = '#000';\n    fctx.textBaseline = 'middle';\n    fctx.fillText(\n      word,\n      fillTextOffsetX * mu,\n      (fillTextOffsetY + fontSize * 0.5) * mu\n    );\n\n    // Get the pixels of the text\n    var imageData = fctx.getImageData(0, 0, width, height).data;\n\n    if (exceedTime()) {\n      return false;\n    }\n\n    if (debug) {\n      // Draw the box of the original estimation\n      fctx.strokeRect(fillTextOffsetX * mu, fillTextOffsetY, fw * mu, fh * mu);\n      fctx.restore();\n    }\n\n    // Read the pixels and save the information to the occupied array\n    var occupied = [];\n    var gx = cgw;\n    var gy, x, y;\n    var bounds = [cgh / 2, cgw / 2, cgh / 2, cgw / 2];\n    while (gx--) {\n      gy = cgh;\n      while (gy--) {\n        y = g;\n        /* eslint no-labels: ['error', { 'allowLoop': true }] */\n        singleGridLoop: while (y--) {\n          x = g;\n          while (x--) {\n            if (imageData[((gy * g + y) * width + (gx * g + x)) * 4 + 3]) {\n              occupied.push([gx, gy]);\n\n              if (gx < bounds[3]) {\n                bounds[3] = gx;\n              }\n              if (gx > bounds[1]) {\n                bounds[1] = gx;\n              }\n              if (gy < bounds[0]) {\n                bounds[0] = gy;\n              }\n              if (gy > bounds[2]) {\n                bounds[2] = gy;\n              }\n\n              if (debug) {\n                fctx.fillStyle = 'rgba(255, 0, 0, 0.5)';\n                fctx.fillRect(gx * g, gy * g, g - 0.5, g - 0.5);\n              }\n              break singleGridLoop;\n            }\n          }\n        }\n        if (debug) {\n          fctx.fillStyle = 'rgba(0, 0, 255, 0.5)';\n          fctx.fillRect(gx * g, gy * g, g - 0.5, g - 0.5);\n        }\n      }\n    }\n\n    if (debug) {\n      fctx.fillStyle = 'rgba(0, 255, 0, 0.5)';\n      fctx.fillRect(\n        bounds[3] * g,\n        bounds[0] * g,\n        (bounds[1] - bounds[3] + 1) * g,\n        (bounds[2] - bounds[0] + 1) * g\n      );\n    }\n\n    // Return information needed to create the text on the real canvas\n    return {\n      mu: mu,\n      occupied: occupied,\n      bounds: bounds,\n      gw: cgw,\n      gh: cgh,\n      fillTextOffsetX: fillTextOffsetX,\n      fillTextOffsetY: fillTextOffsetY,\n      fillTextWidth: fw,\n      fillTextHeight: fh,\n      fontSize: fontSize\n    };\n  };\n\n  /* Determine if there is room available in the given dimension */\n  var canFitText = function canFitText(gx, gy, gw, gh, occupied) {\n    // Go through the occupied points,\n    // return false if the space is not available.\n    var i = occupied.length;\n    while (i--) {\n      var px = gx + occupied[i][0];\n      var py = gy + occupied[i][1];\n\n      if (px >= ngx || py >= ngy || px < 0 || py < 0) {\n        if (!settings.drawOutOfBound) {\n          return false;\n        }\n        continue;\n      }\n\n      if (!grid[px][py]) {\n        return false;\n      }\n    }\n    return true;\n  };\n\n  /* Actually draw the text on the grid */\n  var drawText = function drawText(\n    gx,\n    gy,\n    info,\n    word,\n    weight,\n    distance,\n    theta,\n    rotateDeg,\n    attributes,\n    extraDataArray\n  ) {\n    var fontSize = info.fontSize;\n    var color;\n    if (getTextColor) {\n      color = getTextColor(\n        word,\n        weight,\n        fontSize,\n        distance,\n        theta,\n        extraDataArray\n      );\n    } else {\n      color = settings.color;\n    }\n\n    // get fontWeight that will be used to set ctx.font and font style rule\n    var fontWeight;\n    if (getTextFontWeight) {\n      fontWeight = getTextFontWeight(word, weight, fontSize, extraDataArray);\n    } else {\n      fontWeight = settings.fontWeight;\n    }\n\n    var classes;\n    if (getTextClasses) {\n      classes = getTextClasses(word, weight, fontSize, extraDataArray);\n    } else {\n      classes = settings.classes;\n    }\n\n    elements.forEach(function (el) {\n      if (el.getContext) {\n        var ctx = el.getContext('2d');\n        var mu = info.mu;\n\n        // Save the current state before messing it\n        ctx.save();\n        ctx.scale(1 / mu, 1 / mu);\n\n        ctx.font =\n          fontWeight +\n          ' ' +\n          (fontSize * mu).toString(10) +\n          'px ' +\n          settings.fontFamily;\n        ctx.fillStyle = color;\n\n        // Translate the canvas position to the origin coordinate of where\n        // the text should be put.\n        ctx.translate((gx + info.gw / 2) * g * mu, (gy + info.gh / 2) * g * mu);\n\n        if (rotateDeg !== 0) {\n          ctx.rotate(-rotateDeg);\n        }\n\n        // Finally, fill the text.\n\n        // XXX: We cannot because textBaseline = 'top' here because\n        // Firefox and Chrome uses different default line-height for canvas.\n        // Please read https://bugzil.la/737852#c6.\n        // Here, we use textBaseline = 'middle' and draw the text at exactly\n        // 0.5 * fontSize lower.\n        ctx.textBaseline = 'middle';\n        ctx.fillText(\n          word,\n          info.fillTextOffsetX * mu,\n          (info.fillTextOffsetY + fontSize * 0.5) * mu\n        );\n\n        // The below box is always matches how <span>s are positioned\n        /* ctx.strokeRect(info.fillTextOffsetX, info.fillTextOffsetY,\n            info.fillTextWidth, info.fillTextHeight); */\n\n        // Restore the state.\n        ctx.restore();\n      } else {\n        // drawText on DIV element\n        var span = document.createElement('span');\n        var transformRule = '';\n        transformRule = 'rotate(' + (-rotateDeg / Math.PI) * 180 + 'deg) ';\n        if (info.mu !== 1) {\n          transformRule +=\n            'translateX(-' +\n            info.fillTextWidth / 4 +\n            'px) ' +\n            'scale(' +\n            1 / info.mu +\n            ')';\n        }\n        var styleRules = {\n          position: 'absolute',\n          display: 'block',\n          font:\n            fontWeight + ' ' + fontSize * info.mu + 'px ' + settings.fontFamily,\n          left: (gx + info.gw / 2) * g + info.fillTextOffsetX + 'px',\n          top: (gy + info.gh / 2) * g + info.fillTextOffsetY + 'px',\n          width: info.fillTextWidth + 'px',\n          height: info.fillTextHeight + 'px',\n          lineHeight: fontSize + 'px',\n          whiteSpace: 'nowrap',\n          transform: transformRule,\n          webkitTransform: transformRule,\n          msTransform: transformRule,\n          transformOrigin: '50% 40%',\n          webkitTransformOrigin: '50% 40%',\n          msTransformOrigin: '50% 40%'\n        };\n        if (color) {\n          styleRules.color = color;\n        }\n        span.textContent = word;\n        for (var cssProp in styleRules) {\n          span.style[cssProp] = styleRules[cssProp];\n        }\n        if (attributes) {\n          for (var attribute in attributes) {\n            span.setAttribute(attribute, attributes[attribute]);\n          }\n        }\n        if (classes) {\n          span.className += classes;\n        }\n        el.appendChild(span);\n      }\n    });\n  };\n\n  /* Help function to updateGrid */\n  var fillGridAt = function fillGridAt(x, y, drawMask, dimension, item) {\n    if (x >= ngx || y >= ngy || x < 0 || y < 0) {\n      return;\n    }\n\n    grid[x][y] = false;\n\n    if (drawMask) {\n      var ctx = elements[0].getContext('2d');\n      ctx.fillRect(x * g, y * g, maskRectWidth, maskRectWidth);\n    }\n\n    if (interactive) {\n      infoGrid[x][y] = { item: item, dimension: dimension };\n    }\n  };\n\n  /* Update the filling information of the given space with occupied points.\n       Draw the mask on the canvas if necessary. */\n  var updateGrid = function updateGrid(gx, gy, gw, gh, info, item) {\n    var occupied = info.occupied;\n    var drawMask = settings.drawMask;\n    var ctx;\n    if (drawMask) {\n      ctx = elements[0].getContext('2d');\n      ctx.save();\n      ctx.fillStyle = settings.maskColor;\n    }\n\n    var dimension;\n    if (interactive) {\n      var bounds = info.bounds;\n      dimension = {\n        x: (gx + bounds[3]) * g,\n        y: (gy + bounds[0]) * g,\n        w: (bounds[1] - bounds[3] + 1) * g,\n        h: (bounds[2] - bounds[0] + 1) * g\n      };\n    }\n\n    var i = occupied.length;\n    while (i--) {\n      var px = gx + occupied[i][0];\n      var py = gy + occupied[i][1];\n\n      if (px >= ngx || py >= ngy || px < 0 || py < 0) {\n        continue;\n      }\n\n      fillGridAt(px, py, drawMask, dimension, item);\n    }\n\n    if (drawMask) {\n      ctx.restore();\n    }\n  };\n\n  /* putWord() processes each item on the list,\n       calculate it's size and determine it's position, and actually\n       put it on the canvas. */\n  var putWord = function putWord(item) {\n    var word, weight, attributes;\n    if (Array.isArray(item)) {\n      word = item[0];\n      weight = item[1];\n    } else {\n      word = item.word;\n      weight = item.weight;\n      attributes = item.attributes;\n    }\n    var rotateDeg = getRotateDeg();\n\n    var extraDataArray = getItemExtraData(item);\n\n    // get info needed to put the text onto the canvas\n    var info = getTextInfo(word, weight, rotateDeg, extraDataArray);\n\n    // not getting the info means we shouldn't be drawing this one.\n    if (!info) {\n      return false;\n    }\n\n    if (exceedTime()) {\n      return false;\n    }\n\n    // If drawOutOfBound is set to false,\n    // skip the loop if we have already know the bounding box of\n    // word is larger than the canvas.\n    if (!settings.drawOutOfBound && !settings.shrinkToFit) {\n      var bounds = info.bounds;\n      if (bounds[1] - bounds[3] + 1 > ngx || bounds[2] - bounds[0] + 1 > ngy) {\n        return false;\n      }\n    }\n\n    // Determine the position to put the text by\n    // start looking for the nearest points\n    var r = maxRadius + 1;\n\n    var tryToPutWordAtPoint = function (gxy) {\n      var gx = Math.floor(gxy[0] - info.gw / 2);\n      var gy = Math.floor(gxy[1] - info.gh / 2);\n      var gw = info.gw;\n      var gh = info.gh;\n\n      // If we cannot fit the text at this position, return false\n      // and go to the next position.\n      if (!canFitText(gx, gy, gw, gh, info.occupied)) {\n        return false;\n      }\n\n      // Actually put the text on the canvas\n      drawText(\n        gx,\n        gy,\n        info,\n        word,\n        weight,\n        maxRadius - r,\n        gxy[2],\n        rotateDeg,\n        attributes,\n        extraDataArray\n      );\n\n      // Mark the spaces on the grid as filled\n      updateGrid(gx, gy, gw, gh, info, item);\n\n      return {\n        gx: gx,\n        gy: gy,\n        rot: rotateDeg,\n        info: info\n      };\n    };\n\n    while (r--) {\n      var points = getPointsAtRadius(maxRadius - r);\n\n      if (settings.shuffle) {\n        points = [].concat(points);\n        shuffleArray(points);\n      }\n\n      // Try to fit the words by looking at each point.\n      // array.some() will stop and return true\n      // when putWordAtPoint() returns true.\n      for (var i = 0; i < points.length; i++) {\n        var res = tryToPutWordAtPoint(points[i]);\n        if (res) {\n          return res;\n        }\n      }\n\n      // var drawn = points.some(tryToPutWordAtPoint);\n      // if (drawn) {\n      //   // leave putWord() and return true\n      //   return true;\n      // }\n\n      if (settings.shrinkToFit) {\n        if (Array.isArray(item)) {\n          item[1] = (item[1] * 3) / 4;\n        } else {\n          item.weight = (item.weight * 3) / 4;\n        }\n        return putWord(item);\n      }\n    }\n    // we tried all distances but text won't fit, return null\n    return null;\n  };\n\n  /* Send DOM event to all elements. Will stop sending event and return\n       if the previous one is canceled (for cancelable events). */\n  var sendEvent = function sendEvent(type, cancelable, details) {\n    if (cancelable) {\n      return !elements.some(function (el) {\n        var event = new CustomEvent(type, {\n          detail: details || {}\n        });\n        return !el.dispatchEvent(event);\n      }, this);\n    } else {\n      elements.forEach(function (el) {\n        var event = new CustomEvent(type, {\n          detail: details || {}\n        });\n        el.dispatchEvent(event);\n      }, this);\n    }\n  };\n\n  /* Start drawing on a canvas */\n  var start = function start() {\n    // For dimensions, clearCanvas etc.,\n    // we only care about the first element.\n    var canvas = elements[0];\n\n    if (canvas.getContext) {\n      ngx = Math.ceil(canvas.width / g);\n      ngy = Math.ceil(canvas.height / g);\n    } else {\n      var rect = canvas.getBoundingClientRect();\n      ngx = Math.ceil(rect.width / g);\n      ngy = Math.ceil(rect.height / g);\n    }\n\n    // Sending a wordcloudstart event which cause the previous loop to stop.\n    // Do nothing if the event is canceled.\n    if (!sendEvent('wordcloudstart', true)) {\n      return;\n    }\n\n    // Determine the center of the word cloud\n    center = settings.origin\n      ? [settings.origin[0] / g, settings.origin[1] / g]\n      : [ngx / 2, ngy / 2];\n\n    // Maxium radius to look for space\n    maxRadius = Math.floor(Math.sqrt(ngx * ngx + ngy * ngy));\n\n    /* Clear the canvas only if the clearCanvas is set,\n         if not, update the grid to the current canvas state */\n    grid = [];\n\n    var gx, gy, i;\n    if (!canvas.getContext || settings.clearCanvas) {\n      elements.forEach(function (el) {\n        if (el.getContext) {\n          var ctx = el.getContext('2d');\n          ctx.fillStyle = settings.backgroundColor;\n          ctx.clearRect(0, 0, ngx * (g + 1), ngy * (g + 1));\n          ctx.fillRect(0, 0, ngx * (g + 1), ngy * (g + 1));\n        } else {\n          el.textContent = '';\n          el.style.backgroundColor = settings.backgroundColor;\n          el.style.position = 'relative';\n        }\n      });\n\n      /* fill the grid with empty state */\n      gx = ngx;\n      while (gx--) {\n        grid[gx] = [];\n        gy = ngy;\n        while (gy--) {\n          grid[gx][gy] = true;\n        }\n      }\n    } else {\n      /* Determine bgPixel by creating\n           another canvas and fill the specified background color. */\n      var bctx = document.createElement('canvas').getContext('2d');\n\n      bctx.fillStyle = settings.backgroundColor;\n      bctx.fillRect(0, 0, 1, 1);\n      var bgPixel = bctx.getImageData(0, 0, 1, 1).data;\n\n      /* Read back the pixels of the canvas we got to tell which part of the\n           canvas is empty.\n           (no clearCanvas only works with a canvas, not divs) */\n      var imageData = canvas\n        .getContext('2d')\n        .getImageData(0, 0, ngx * g, ngy * g).data;\n\n      gx = ngx;\n      var x, y;\n      while (gx--) {\n        grid[gx] = [];\n        gy = ngy;\n        while (gy--) {\n          y = g;\n          /* eslint no-labels: ['error', { 'allowLoop': true }] */\n          singleGridLoop: while (y--) {\n            x = g;\n            while (x--) {\n              i = 4;\n              while (i--) {\n                if (\n                  imageData[((gy * g + y) * ngx * g + (gx * g + x)) * 4 + i] !==\n                  bgPixel[i]\n                ) {\n                  grid[gx][gy] = false;\n                  break singleGridLoop;\n                }\n              }\n            }\n          }\n          if (grid[gx][gy] !== false) {\n            grid[gx][gy] = true;\n          }\n        }\n      }\n\n      imageData = bctx = bgPixel = undefined;\n    }\n\n    // fill the infoGrid with empty state if we need it\n    if (settings.hover || settings.click) {\n      interactive = true;\n\n      /* fill the grid with empty state */\n      gx = ngx + 1;\n      while (gx--) {\n        infoGrid[gx] = [];\n      }\n\n      if (settings.hover) {\n        canvas.addEventListener('mousemove', wordcloudhover);\n      }\n\n      if (settings.click) {\n        canvas.addEventListener('click', wordcloudclick);\n        canvas.addEventListener('touchstart', wordcloudclick);\n        canvas.addEventListener('touchend', function (e) {\n          e.preventDefault();\n        });\n        canvas.style.webkitTapHighlightColor = 'rgba(0, 0, 0, 0)';\n      }\n\n      canvas.addEventListener('wordcloudstart', function stopInteraction() {\n        canvas.removeEventListener('wordcloudstart', stopInteraction);\n\n        canvas.removeEventListener('mousemove', wordcloudhover);\n        canvas.removeEventListener('click', wordcloudclick);\n        hovered = undefined;\n      });\n    }\n\n    i = 0;\n    var loopingFunction, stoppingFunction;\n    var layouting = true;\n    if (!settings.layoutAnimation) {\n      loopingFunction = function (cb) {\n        cb();\n      };\n      stoppingFunction = function () {\n        layouting = false;\n      };\n    } else if (settings.wait !== 0) {\n      loopingFunction = window.setTimeout;\n      stoppingFunction = window.clearTimeout;\n    } else {\n      loopingFunction = window.setImmediate;\n      stoppingFunction = window.clearImmediate;\n    }\n\n    var addEventListener = function addEventListener(type, listener) {\n      elements.forEach(function (el) {\n        el.addEventListener(type, listener);\n      }, this);\n    };\n\n    var removeEventListener = function removeEventListener(type, listener) {\n      elements.forEach(function (el) {\n        el.removeEventListener(type, listener);\n      }, this);\n    };\n\n    var anotherWordCloudStart = function anotherWordCloudStart() {\n      removeEventListener('wordcloudstart', anotherWordCloudStart);\n      stoppingFunction(timer[timerId]);\n    };\n\n    addEventListener('wordcloudstart', anotherWordCloudStart);\n\n    // At least wait the following code before call the first iteration.\n    timer[timerId] = (settings.layoutAnimation ? loopingFunction : setTimeout)(\n      function loop() {\n        if (!layouting) {\n          return;\n        }\n        if (i >= settings.list.length) {\n          stoppingFunction(timer[timerId]);\n          sendEvent('wordcloudstop', false);\n          removeEventListener('wordcloudstart', anotherWordCloudStart);\n          delete timer[timerId];\n          return;\n        }\n        escapeTime = new Date().getTime();\n        var drawn = putWord(settings.list[i]);\n        var canceled = !sendEvent('wordclouddrawn', true, {\n          item: settings.list[i],\n          drawn: drawn\n        });\n        if (exceedTime() || canceled) {\n          stoppingFunction(timer[timerId]);\n          settings.abort();\n          sendEvent('wordcloudabort', false);\n          sendEvent('wordcloudstop', false);\n          removeEventListener('wordcloudstart', anotherWordCloudStart);\n          return;\n        }\n        i++;\n        timer[timerId] = loopingFunction(loop, settings.wait);\n      },\n      settings.wait\n    );\n  };\n\n  // All set, start the drawing\n  start();\n};\n\nWordCloud.isSupported = isSupported;\nWordCloud.minFontSize = minFontSize;\n\nexport default WordCloud;\n", "import * as echarts from 'echarts/lib/echarts';\n\nimport './WordCloudSeries';\nimport './WordCloudView';\n\nimport wordCloudLayoutHelper from './layout';\n\nif (!wordCloudLayoutHelper.isSupported) {\n  throw new Error('Sorry your browser not support wordCloud');\n}\n\n// https://github.com/timdream/wordcloud2.js/blob/c236bee60436e048949f9becc4f0f67bd832dc5c/index.js#L233\nfunction updateCanvasMask(maskCanvas) {\n  var ctx = maskCanvas.getContext('2d');\n  var imageData = ctx.getImageData(0, 0, maskCanvas.width, maskCanvas.height);\n  var newImageData = ctx.createImageData(imageData);\n\n  var toneSum = 0;\n  var toneCnt = 0;\n  for (var i = 0; i < imageData.data.length; i += 4) {\n    var alpha = imageData.data[i + 3];\n    if (alpha > 128) {\n      var tone =\n        imageData.data[i] + imageData.data[i + 1] + imageData.data[i + 2];\n      toneSum += tone;\n      ++toneCnt;\n    }\n  }\n  var threshold = toneSum / toneCnt;\n\n  for (var i = 0; i < imageData.data.length; i += 4) {\n    var tone =\n      imageData.data[i] + imageData.data[i + 1] + imageData.data[i + 2];\n    var alpha = imageData.data[i + 3];\n\n    if (alpha < 128 || tone > threshold) {\n      // Area not to draw\n      newImageData.data[i] = 0;\n      newImageData.data[i + 1] = 0;\n      newImageData.data[i + 2] = 0;\n      newImageData.data[i + 3] = 0;\n    } else {\n      // Area to draw\n      // The color must be same with backgroundColor\n      newImageData.data[i] = 255;\n      newImageData.data[i + 1] = 255;\n      newImageData.data[i + 2] = 255;\n      newImageData.data[i + 3] = 255;\n    }\n  }\n\n  ctx.putImageData(newImageData, 0, 0);\n}\n\necharts.registerLayout(function (ecModel, api) {\n  ecModel.eachSeriesByType('wordCloud', function (seriesModel) {\n    var gridRect = echarts.helper.getLayoutRect(\n      seriesModel.getBoxLayoutParams(),\n      {\n        width: api.getWidth(),\n        height: api.getHeight()\n      }\n    );\n\n    var keepAspect = seriesModel.get('keepAspect');\n    var maskImage = seriesModel.get('maskImage');\n    var ratio = maskImage ? maskImage.width / maskImage.height : 1;\n    keepAspect && adjustRectAspect(gridRect, ratio);\n\n    var data = seriesModel.getData();\n\n    var canvas = document.createElement('canvas');\n    canvas.width = gridRect.width;\n    canvas.height = gridRect.height;\n\n    var ctx = canvas.getContext('2d');\n    if (maskImage) {\n      try {\n        ctx.drawImage(maskImage, 0, 0, canvas.width, canvas.height);\n        updateCanvasMask(canvas);\n      } catch (e) {\n        console.error('Invalid mask image');\n        console.error(e.toString());\n      }\n    }\n\n    var sizeRange = seriesModel.get('sizeRange');\n    var rotationRange = seriesModel.get('rotationRange');\n    var valueExtent = data.getDataExtent('value');\n\n    var DEGREE_TO_RAD = Math.PI / 180;\n    var gridSize = seriesModel.get('gridSize');\n    wordCloudLayoutHelper(canvas, {\n      list: data\n        .mapArray('value', function (value, idx) {\n          var itemModel = data.getItemModel(idx);\n          return [\n            data.getName(idx),\n            itemModel.get('textStyle.fontSize', true) ||\n              echarts.number.linearMap(value, valueExtent, sizeRange),\n            idx\n          ];\n        })\n        .sort(function (a, b) {\n          // Sort from large to small in case there is no more room for more words\n          return b[1] - a[1];\n        }),\n      fontFamily:\n        seriesModel.get('textStyle.fontFamily') ||\n        seriesModel.get('emphasis.textStyle.fontFamily') ||\n        ecModel.get('textStyle.fontFamily'),\n      fontWeight:\n        seriesModel.get('textStyle.fontWeight') ||\n        seriesModel.get('emphasis.textStyle.fontWeight') ||\n        ecModel.get('textStyle.fontWeight'),\n\n      gridSize: gridSize,\n\n      ellipticity: gridRect.height / gridRect.width,\n\n      minRotation: rotationRange[0] * DEGREE_TO_RAD,\n      maxRotation: rotationRange[1] * DEGREE_TO_RAD,\n\n      clearCanvas: !maskImage,\n\n      rotateRatio: 1,\n\n      rotationStep: seriesModel.get('rotationStep') * DEGREE_TO_RAD,\n\n      drawOutOfBound: seriesModel.get('drawOutOfBound'),\n      shrinkToFit: seriesModel.get('shrinkToFit'),\n\n      layoutAnimation: seriesModel.get('layoutAnimation'),\n\n      shuffle: false,\n\n      shape: seriesModel.get('shape')\n    });\n\n    function onWordCloudDrawn(e) {\n      var item = e.detail.item;\n      if (e.detail.drawn && seriesModel.layoutInstance.ondraw) {\n        e.detail.drawn.gx += gridRect.x / gridSize;\n        e.detail.drawn.gy += gridRect.y / gridSize;\n        seriesModel.layoutInstance.ondraw(\n          item[0],\n          item[1],\n          item[2],\n          e.detail.drawn\n        );\n      }\n    }\n\n    canvas.addEventListener('wordclouddrawn', onWordCloudDrawn);\n\n    if (seriesModel.layoutInstance) {\n      // Dispose previous\n      seriesModel.layoutInstance.dispose();\n    }\n\n    seriesModel.layoutInstance = {\n      ondraw: null,\n\n      dispose: function () {\n        canvas.removeEventListener('wordclouddrawn', onWordCloudDrawn);\n        // Abort\n        canvas.addEventListener('wordclouddrawn', function (e) {\n          // Prevent default to cancle the event and stop the loop\n          e.preventDefault();\n        });\n      }\n    };\n  });\n});\n\necharts.registerPreprocessor(function (option) {\n  var series = (option || {}).series;\n  !echarts.util.isArray(series) && (series = series ? [series] : []);\n\n  var compats = ['shadowColor', 'shadowBlur', 'shadowOffsetX', 'shadowOffsetY'];\n\n  echarts.util.each(series, function (seriesItem) {\n    if (seriesItem && seriesItem.type === 'wordCloud') {\n      var textStyle = seriesItem.textStyle || {};\n\n      compatTextStyle(textStyle.normal);\n      compatTextStyle(textStyle.emphasis);\n    }\n  });\n\n  function compatTextStyle(textStyle) {\n    textStyle &&\n      echarts.util.each(compats, function (key) {\n        if (textStyle.hasOwnProperty(key)) {\n          textStyle['text' + echarts.format.capitalFirst(key)] = textStyle[key];\n        }\n      });\n  }\n});\n\nfunction adjustRectAspect(gridRect, aspect) {\n  // var outerWidth = gridRect.width + gridRect.x * 2;\n  // var outerHeight = gridRect.height + gridRect.y * 2;\n  var width = gridRect.width;\n  var height = gridRect.height;\n  if (width > height * aspect) {\n    gridRect.x += (width - height * aspect) / 2;\n    gridRect.width = height * aspect;\n  } else {\n    gridRect.y += (height - width / aspect) / 2;\n    gridRect.height = width / aspect;\n  }\n}\n", "module.exports = __WEBPACK_EXTERNAL_MODULE__83__;", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tif(__webpack_module_cache__[moduleId]) {\n\t\treturn __webpack_module_cache__[moduleId].exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// module exports must be returned from runtime so entry inlining is disabled\n// startup\n// Load entry module and return exports\nreturn __webpack_require__(638);\n", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};"], "sourceRoot": ""}