!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e=e||self).CherryEngine={})}(this,(function(e){"use strict";var t="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function n(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function r(e,t){return e(t={exports:{}},t.exports),t.exports}var a,o,i=function(e){return e&&e.Math==Math&&e},s=i("object"==typeof globalThis&&globalThis)||i("object"==typeof window&&window)||i("object"==typeof self&&self)||i("object"==typeof t&&t)||function(){return this}()||Function("return this")(),c=function(e){try{return!!e()}catch(e){return!0}},l=!c((function(){var e=function(){}.bind();return"function"!=typeof e||e.hasOwnProperty("prototype")})),u=Function.prototype,f=u.apply,d=u.call,p="object"==typeof Reflect&&Reflect.apply||(l?d.bind(f):function(){return d.apply(f,arguments)}),h=Function.prototype,g=h.bind,m=h.call,b=l&&g.bind(m,m),v=l?function(e){return e&&b(e)}:function(e){return e&&function(){return m.apply(e,arguments)}},y=function(e){return"function"==typeof e},_=!c((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),k=Function.prototype.call,w=l?k.bind(k):function(){return k.apply(k,arguments)},E={}.propertyIsEnumerable,x=Object.getOwnPropertyDescriptor,S={f:x&&!E.call({1:2},1)?function(e){var t=x(this,e);return!!t&&t.enumerable}:E},A=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}},C=v({}.toString),T=v("".slice),O=function(e){return T(C(e),8,-1)},$=s.Object,R=v("".split),P=c((function(){return!$("z").propertyIsEnumerable(0)}))?function(e){return"String"==O(e)?R(e,""):$(e)}:$,I=s.TypeError,L=function(e){if(null==e)throw I("Can't call method on "+e);return e},N=function(e){return P(L(e))},M=function(e){return"object"==typeof e?null!==e:y(e)},j={},D=function(e){return y(e)?e:void 0},B=function(e,t){return arguments.length<2?D(j[e])||D(s[e]):j[e]&&j[e][t]||s[e]&&s[e][t]},F=v({}.isPrototypeOf),H=B("navigator","userAgent")||"",z=s.process,U=s.Deno,W=z&&z.versions||U&&U.version,q=W&&W.v8;q&&(o=(a=q.split("."))[0]>0&&a[0]<4?1:+(a[0]+a[1])),!o&&H&&(!(a=H.match(/Edge\/(\d+)/))||a[1]>=74)&&(a=H.match(/Chrome\/(\d+)/))&&(o=+a[1]);var G=o,K=!!Object.getOwnPropertySymbols&&!c((function(){var e=Symbol();return!String(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&G&&G<41})),Z=K&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Y=s.Object,X=Z?function(e){return"symbol"==typeof e}:function(e){var t=B("Symbol");return y(t)&&F(t.prototype,Y(e))},V=s.String,J=function(e){try{return V(e)}catch(e){return"Object"}},Q=s.TypeError,ee=function(e){if(y(e))return e;throw Q(J(e)+" is not a function")},te=function(e,t){var n=e[t];return null==n?void 0:ee(n)},ne=s.TypeError,re=Object.defineProperty,ae=s["__core-js_shared__"]||function(e,t){try{re(s,e,{value:t,configurable:!0,writable:!0})}catch(n){s[e]=t}return t}("__core-js_shared__",{}),oe=r((function(e){(e.exports=function(e,t){return ae[e]||(ae[e]=void 0!==t?t:{})})("versions",[]).push({version:"3.22.6",mode:"pure",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.22.6/LICENSE",source:"https://github.com/zloirock/core-js"})})),ie=s.Object,se=function(e){return ie(L(e))},ce=v({}.hasOwnProperty),le=Object.hasOwn||function(e,t){return ce(se(e),t)},ue=0,fe=Math.random(),de=v(1..toString),pe=function(e){return"Symbol("+(void 0===e?"":e)+")_"+de(++ue+fe,36)},he=oe("wks"),ge=s.Symbol,me=ge&&ge.for,be=Z?ge:ge&&ge.withoutSetter||pe,ve=function(e){if(!le(he,e)||!K&&"string"!=typeof he[e]){var t="Symbol."+e;K&&le(ge,e)?he[e]=ge[e]:he[e]=Z&&me?me(t):be(t)}return he[e]},ye=s.TypeError,_e=ve("toPrimitive"),ke=function(e,t){if(!M(e)||X(e))return e;var n,r=te(e,_e);if(r){if(void 0===t&&(t="default"),n=w(r,e,t),!M(n)||X(n))return n;throw ye("Can't convert object to primitive value")}return void 0===t&&(t="number"),function(e,t){var n,r;if("string"===t&&y(n=e.toString)&&!M(r=w(n,e)))return r;if(y(n=e.valueOf)&&!M(r=w(n,e)))return r;if("string"!==t&&y(n=e.toString)&&!M(r=w(n,e)))return r;throw ne("Can't convert object to primitive value")}(e,t)},we=function(e){var t=ke(e,"string");return X(t)?t:t+""},Ee=s.document,xe=M(Ee)&&M(Ee.createElement),Se=function(e){return xe?Ee.createElement(e):{}},Ae=!_&&!c((function(){return 7!=Object.defineProperty(Se("div"),"a",{get:function(){return 7}}).a})),Ce=Object.getOwnPropertyDescriptor,Te={f:_?Ce:function(e,t){if(e=N(e),t=we(t),Ae)try{return Ce(e,t)}catch(e){}if(le(e,t))return A(!w(S.f,e,t),e[t])}},Oe=/#|\.prototype\./,$e=function(e,t){var n=Pe[Re(e)];return n==Le||n!=Ie&&(y(t)?c(t):!!t)},Re=$e.normalize=function(e){return String(e).replace(Oe,".").toLowerCase()},Pe=$e.data={},Ie=$e.NATIVE="N",Le=$e.POLYFILL="P",Ne=$e,Me=v(v.bind),je=function(e,t){return ee(e),void 0===t?e:l?Me(e,t):function(){return e.apply(t,arguments)}},De=_&&c((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Be=s.String,Fe=s.TypeError,He=function(e){if(M(e))return e;throw Fe(Be(e)+" is not an object")},ze=s.TypeError,Ue=Object.defineProperty,We=Object.getOwnPropertyDescriptor,qe={f:_?De?function(e,t,n){if(He(e),t=we(t),He(n),"function"==typeof e&&"prototype"===t&&"value"in n&&"writable"in n&&!n.writable){var r=We(e,t);r&&r.writable&&(e[t]=n.value,n={configurable:"configurable"in n?n.configurable:r.configurable,enumerable:"enumerable"in n?n.enumerable:r.enumerable,writable:!1})}return Ue(e,t,n)}:Ue:function(e,t,n){if(He(e),t=we(t),He(n),Ae)try{return Ue(e,t,n)}catch(e){}if("get"in n||"set"in n)throw ze("Accessors not supported");return"value"in n&&(e[t]=n.value),e}},Ge=_?function(e,t,n){return qe.f(e,t,A(1,n))}:function(e,t,n){return e[t]=n,e},Ke=Te.f,Ze=function(e){var t=function(n,r,a){if(this instanceof t){switch(arguments.length){case 0:return new e;case 1:return new e(n);case 2:return new e(n,r)}return new e(n,r,a)}return p(e,this,arguments)};return t.prototype=e.prototype,t},Ye=function(e,t){var n,r,a,o,i,c,l,u,f=e.target,d=e.global,p=e.stat,h=e.proto,g=d?s:p?s[f]:(s[f]||{}).prototype,m=d?j:j[f]||Ge(j,f,{})[f],b=m.prototype;for(a in t)n=!Ne(d?a:f+(p?".":"#")+a,e.forced)&&g&&le(g,a),i=m[a],n&&(c=e.dontCallGetSet?(u=Ke(g,a))&&u.value:g[a]),o=n&&c?c:t[a],n&&typeof i==typeof o||(l=e.bind&&n?je(o,s):e.wrap&&n?Ze(o):h&&y(o)?v(o):o,(e.sham||o&&o.sham||i&&i.sham)&&Ge(l,"sham",!0),Ge(m,a,l),h&&(le(j,r=f+"Prototype")||Ge(j,r,{}),Ge(j[r],a,o),e.real&&b&&!b[a]&&Ge(b,a,o)))},Xe=v([].slice),Ve=s.Function,Je=v([].concat),Qe=v([].join),et={},tt=function(e,t,n){if(!le(et,t)){for(var r=[],a=0;a<t;a++)r[a]="a["+a+"]";et[t]=Ve("C,a","return new C("+Qe(r,",")+")")}return et[t](e,n)},nt=l?Ve.bind:function(e){var t=ee(this),n=t.prototype,r=Xe(arguments,1),a=function(){var n=Je(r,Xe(arguments));return this instanceof a?tt(t,n.length,n):t.apply(e,n)};return M(n)&&(a.prototype=n),a},rt={};rt[ve("toStringTag")]="z";var at="[object z]"===String(rt),ot=ve("toStringTag"),it=s.Object,st="Arguments"==O(function(){return arguments}()),ct=at?O:function(e){var t,n,r;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(n=function(e,t){try{return e[t]}catch(e){}}(t=it(e),ot))?n:st?O(t):"Object"==(r=O(t))&&y(t.callee)?"Arguments":r},lt=v(Function.toString);y(ae.inspectSource)||(ae.inspectSource=function(e){return lt(e)});var ut=ae.inspectSource,ft=function(){},dt=[],pt=B("Reflect","construct"),ht=/^\s*(?:class|function)\b/,gt=v(ht.exec),mt=!ht.exec(ft),bt=function(e){if(!y(e))return!1;try{return pt(ft,dt,e),!0}catch(e){return!1}},vt=function(e){if(!y(e))return!1;switch(ct(e)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return mt||!!gt(ht,ut(e))}catch(e){return!0}};vt.sham=!0;var yt,_t=!pt||c((function(){var e;return bt(bt.call)||!bt(Object)||!bt((function(){e=!0}))||e}))?vt:bt,kt=s.TypeError,wt=function(e){if(_t(e))return e;throw kt(J(e)+" is not a constructor")},Et=Math.ceil,xt=Math.floor,St=Math.trunc||function(e){var t=+e;return(t>0?xt:Et)(t)},At=function(e){var t=+e;return t!=t||0===t?0:St(t)},Ct=Math.max,Tt=Math.min,Ot=function(e,t){var n=At(e);return n<0?Ct(n+t,0):Tt(n,t)},$t=Math.min,Rt=function(e){return e>0?$t(At(e),9007199254740991):0},Pt=function(e){return Rt(e.length)},It=function(e){return function(t,n,r){var a,o=N(t),i=Pt(o),s=Ot(r,i);if(e&&n!=n){for(;i>s;)if((a=o[s++])!=a)return!0}else for(;i>s;s++)if((e||s in o)&&o[s]===n)return e||s||0;return!e&&-1}},Lt={includes:It(!0),indexOf:It(!1)},Nt={},Mt=Lt.indexOf,jt=v([].push),Dt=function(e,t){var n,r=N(e),a=0,o=[];for(n in r)!le(Nt,n)&&le(r,n)&&jt(o,n);for(;t.length>a;)le(r,n=t[a++])&&(~Mt(o,n)||jt(o,n));return o},Bt=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],Ft=Object.keys||function(e){return Dt(e,Bt)},Ht={f:_&&!De?Object.defineProperties:function(e,t){He(e);for(var n,r=N(t),a=Ft(t),o=a.length,i=0;o>i;)qe.f(e,n=a[i++],r[n]);return e}},zt=B("document","documentElement"),Ut=oe("keys"),Wt=function(e){return Ut[e]||(Ut[e]=pe(e))},qt=Wt("IE_PROTO"),Gt=function(){},Kt=function(e){return"<script>"+e+"<\/script>"},Zt=function(e){e.write(Kt("")),e.close();var t=e.parentWindow.Object;return e=null,t},Yt=function(){try{yt=new ActiveXObject("htmlfile")}catch(e){}var e,t;Yt="undefined"!=typeof document?document.domain&&yt?Zt(yt):((t=Se("iframe")).style.display="none",zt.appendChild(t),t.src=String("javascript:"),(e=t.contentWindow.document).open(),e.write(Kt("document.F=Object")),e.close(),e.F):Zt(yt);for(var n=Bt.length;n--;)delete Yt.prototype[Bt[n]];return Yt()};Nt[qt]=!0;var Xt=Object.create||function(e,t){var n;return null!==e?(Gt.prototype=He(e),n=new Gt,Gt.prototype=null,n[qt]=e):n=Yt(),void 0===t?n:Ht.f(n,t)},Vt=B("Reflect","construct"),Jt=Object.prototype,Qt=[].push,en=c((function(){function e(){}return!(Vt((function(){}),[],e)instanceof e)})),tn=!c((function(){Vt((function(){}))})),nn=en||tn;Ye({target:"Reflect",stat:!0,forced:nn,sham:nn},{construct:function(e,t){wt(e),He(t);var n=arguments.length<3?e:wt(arguments[2]);if(tn&&!en)return Vt(e,t,n);if(e==n){switch(t.length){case 0:return new e;case 1:return new e(t[0]);case 2:return new e(t[0],t[1]);case 3:return new e(t[0],t[1],t[2]);case 4:return new e(t[0],t[1],t[2],t[3])}var r=[null];return p(Qt,r,t),new(p(nt,e,r))}var a=n.prototype,o=Xt(M(a)?a:Jt),i=p(e,o,t);return M(i)?i:o}});var rn=j.Reflect.construct,an=rn,on=qe.f;Ye({target:"Object",stat:!0,forced:Object.defineProperty!==on,sham:!_},{defineProperty:on});var sn=r((function(e){var t=j.Object,n=e.exports=function(e,n,r){return t.defineProperty(e,n,r)};t.defineProperty.sham&&(n.sham=!0)})),cn=sn,ln=n(r((function(e){function t(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),cn(e,r.key,r)}}e.exports=function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),cn(e,"prototype",{writable:!1}),e},e.exports.__esModule=!0,e.exports.default=e.exports}))),un=n(r((function(e){e.exports=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},e.exports.__esModule=!0,e.exports.default=e.exports})));Ye({target:"Object",stat:!0,sham:!_},{create:Xt});var fn=j.Object,dn=function(e,t){return fn.create(e,t)},pn=dn,hn=s.String,gn=s.TypeError,mn=Object.setPrototypeOf||("__proto__"in{}?function(){var e,t=!1,n={};try{(e=v(Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set))(n,[]),t=n instanceof Array}catch(e){}return function(n,r){return He(n),function(e){if("object"==typeof e||y(e))return e;throw gn("Can't set "+hn(e)+" as a prototype")}(r),t?e(n,r):n.__proto__=r,n}}():void 0);Ye({target:"Object",stat:!0},{setPrototypeOf:mn});var bn=j.Object.setPrototypeOf,vn=r((function(e){function t(n,r){return e.exports=t=bn||function(e,t){return e.__proto__=t,e},e.exports.__esModule=!0,e.exports.default=e.exports,t(n,r)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}));n(vn);var yn=n(r((function(e){e.exports=function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=pn(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),cn(e,"prototype",{writable:!1}),t&&vn(e,t)},e.exports.__esModule=!0,e.exports.default=e.exports}))),_n=Array.isArray||function(e){return"Array"==O(e)},kn=function(e,t,n){var r=we(t);r in e?qe.f(e,r,A(0,n)):e[r]=n},wn=ve("species"),En=s.Array,xn=function(e,t){return new(function(e){var t;return _n(e)&&(t=e.constructor,(_t(t)&&(t===En||_n(t.prototype))||M(t)&&null===(t=t[wn]))&&(t=void 0)),void 0===t?En:t}(e))(0===t?0:t)},Sn=ve("species"),An=function(e){return G>=51||!c((function(){var t=[];return(t.constructor={})[Sn]=function(){return{foo:1}},1!==t[e](Boolean).foo}))},Cn=ve("isConcatSpreadable"),Tn=s.TypeError,On=G>=51||!c((function(){var e=[];return e[Cn]=!1,e.concat()[0]!==e})),$n=An("concat"),Rn=function(e){if(!M(e))return!1;var t=e[Cn];return void 0!==t?!!t:_n(e)};Ye({target:"Array",proto:!0,arity:1,forced:!On||!$n},{concat:function(e){var t,n,r,a,o,i=se(this),s=xn(i,0),c=0;for(t=-1,r=arguments.length;t<r;t++)if(Rn(o=-1===t?i:arguments[t])){if(c+(a=Pt(o))>9007199254740991)throw Tn("Maximum allowed index exceeded");for(n=0;n<a;n++,c++)n in o&&kn(s,c,o[n])}else{if(c>=9007199254740991)throw Tn("Maximum allowed index exceeded");kn(s,c++,o)}return s.length=c,s}});var Pn,In,Ln,Nn=s.String,Mn=function(e){if("Symbol"===ct(e))throw TypeError("Cannot convert a Symbol value to a string");return Nn(e)},jn=Bt.concat("length","prototype"),Dn={f:Object.getOwnPropertyNames||function(e){return Dt(e,jn)}},Bn=s.Array,Fn=Math.max,Hn=Dn.f,zn="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],Un=function(e){try{return Hn(e)}catch(e){return function(e,t,n){for(var r=Pt(e),a=Ot(t,r),o=Ot(void 0===n?r:n,r),i=Bn(Fn(o-a,0)),s=0;a<o;a++,s++)kn(i,s,e[a]);return i.length=s,i}(zn)}},Wn={f:function(e){return zn&&"Window"==O(e)?Un(e):Hn(N(e))}},qn={f:Object.getOwnPropertySymbols},Gn=function(e,t,n,r){return r&&r.enumerable?e[t]=n:Ge(e,t,n),e},Kn={f:ve},Zn=qe.f,Yn=function(e){var t=j.Symbol||(j.Symbol={});le(t,e)||Zn(t,e,{value:Kn.f(e)})},Xn=function(){var e=B("Symbol"),t=e&&e.prototype,n=t&&t.valueOf,r=ve("toPrimitive");t&&!t[r]&&Gn(t,r,(function(e){return w(n,this)}),{arity:1})},Vn=at?{}.toString:function(){return"[object "+ct(this)+"]"},Jn=qe.f,Qn=ve("toStringTag"),er=function(e,t,n,r){if(e){var a=n?e:e.prototype;le(a,Qn)||Jn(a,Qn,{configurable:!0,value:t}),r&&!at&&Ge(a,"toString",Vn)}},tr=s.WeakMap,nr=y(tr)&&/native code/.test(ut(tr)),rr=s.TypeError,ar=s.WeakMap;if(nr||ae.state){var or=ae.state||(ae.state=new ar),ir=v(or.get),sr=v(or.has),cr=v(or.set);Pn=function(e,t){if(sr(or,e))throw new rr("Object already initialized");return t.facade=e,cr(or,e,t),t},In=function(e){return ir(or,e)||{}},Ln=function(e){return sr(or,e)}}else{var lr=Wt("state");Nt[lr]=!0,Pn=function(e,t){if(le(e,lr))throw new rr("Object already initialized");return t.facade=e,Ge(e,lr,t),t},In=function(e){return le(e,lr)?e[lr]:{}},Ln=function(e){return le(e,lr)}}var ur={set:Pn,get:In,has:Ln,enforce:function(e){return Ln(e)?In(e):Pn(e,{})},getterFor:function(e){return function(t){var n;if(!M(t)||(n=In(t)).type!==e)throw rr("Incompatible receiver, "+e+" required");return n}}},fr=v([].push),dr=function(e){var t=1==e,n=2==e,r=3==e,a=4==e,o=6==e,i=7==e,s=5==e||o;return function(c,l,u,f){for(var d,p,h=se(c),g=P(h),m=je(l,u),b=Pt(g),v=0,y=f||xn,_=t?y(c,b):n||i?y(c,0):void 0;b>v;v++)if((s||v in g)&&(p=m(d=g[v],v,h),e))if(t)_[v]=p;else if(p)switch(e){case 3:return!0;case 5:return d;case 6:return v;case 2:fr(_,d)}else switch(e){case 4:return!1;case 7:fr(_,d)}return o?-1:r||a?a:_}},pr={forEach:dr(0),map:dr(1),filter:dr(2),some:dr(3),every:dr(4),find:dr(5),findIndex:dr(6),filterReject:dr(7)},hr=pr.forEach,gr=Wt("hidden"),mr=ur.set,br=ur.getterFor("Symbol"),vr=Object.prototype,yr=s.Symbol,_r=yr&&yr.prototype,kr=s.TypeError,wr=s.QObject,Er=Te.f,xr=qe.f,Sr=Wn.f,Ar=S.f,Cr=v([].push),Tr=oe("symbols"),Or=oe("op-symbols"),$r=oe("wks"),Rr=!wr||!wr.prototype||!wr.prototype.findChild,Pr=_&&c((function(){return 7!=Xt(xr({},"a",{get:function(){return xr(this,"a",{value:7}).a}})).a}))?function(e,t,n){var r=Er(vr,t);r&&delete vr[t],xr(e,t,n),r&&e!==vr&&xr(vr,t,r)}:xr,Ir=function(e,t){var n=Tr[e]=Xt(_r);return mr(n,{type:"Symbol",tag:e,description:t}),_||(n.description=t),n},Lr=function(e,t,n){e===vr&&Lr(Or,t,n),He(e);var r=we(t);return He(n),le(Tr,r)?(n.enumerable?(le(e,gr)&&e[gr][r]&&(e[gr][r]=!1),n=Xt(n,{enumerable:A(0,!1)})):(le(e,gr)||xr(e,gr,A(1,{})),e[gr][r]=!0),Pr(e,r,n)):xr(e,r,n)},Nr=function(e,t){He(e);var n=N(t),r=Ft(n).concat(Br(n));return hr(r,(function(t){_&&!w(Mr,n,t)||Lr(e,t,n[t])})),e},Mr=function(e){var t=we(e),n=w(Ar,this,t);return!(this===vr&&le(Tr,t)&&!le(Or,t))&&(!(n||!le(this,t)||!le(Tr,t)||le(this,gr)&&this[gr][t])||n)},jr=function(e,t){var n=N(e),r=we(t);if(n!==vr||!le(Tr,r)||le(Or,r)){var a=Er(n,r);return!a||!le(Tr,r)||le(n,gr)&&n[gr][r]||(a.enumerable=!0),a}},Dr=function(e){var t=Sr(N(e)),n=[];return hr(t,(function(e){le(Tr,e)||le(Nt,e)||Cr(n,e)})),n},Br=function(e){var t=e===vr,n=Sr(t?Or:N(e)),r=[];return hr(n,(function(e){!le(Tr,e)||t&&!le(vr,e)||Cr(r,Tr[e])})),r};K||(_r=(yr=function(){if(F(_r,this))throw kr("Symbol is not a constructor");var e=arguments.length&&void 0!==arguments[0]?Mn(arguments[0]):void 0,t=pe(e),n=function(e){this===vr&&w(n,Or,e),le(this,gr)&&le(this[gr],t)&&(this[gr][t]=!1),Pr(this,t,A(1,e))};return _&&Rr&&Pr(vr,t,{configurable:!0,set:n}),Ir(t,e)}).prototype,Gn(_r,"toString",(function(){return br(this).tag})),Gn(yr,"withoutSetter",(function(e){return Ir(pe(e),e)})),S.f=Mr,qe.f=Lr,Ht.f=Nr,Te.f=jr,Dn.f=Wn.f=Dr,qn.f=Br,Kn.f=function(e){return Ir(ve(e),e)},_&&xr(_r,"description",{configurable:!0,get:function(){return br(this).description}})),Ye({global:!0,constructor:!0,wrap:!0,forced:!K,sham:!K},{Symbol:yr}),hr(Ft($r),(function(e){Yn(e)})),Ye({target:"Symbol",stat:!0,forced:!K},{useSetter:function(){Rr=!0},useSimple:function(){Rr=!1}}),Ye({target:"Object",stat:!0,forced:!K,sham:!_},{create:function(e,t){return void 0===t?Xt(e):Nr(Xt(e),t)},defineProperty:Lr,defineProperties:Nr,getOwnPropertyDescriptor:jr}),Ye({target:"Object",stat:!0,forced:!K},{getOwnPropertyNames:Dr}),Xn(),er(yr,"Symbol"),Nt[gr]=!0;var Fr=K&&!!Symbol.for&&!!Symbol.keyFor,Hr=oe("string-to-symbol-registry"),zr=oe("symbol-to-string-registry");Ye({target:"Symbol",stat:!0,forced:!Fr},{for:function(e){var t=Mn(e);if(le(Hr,t))return Hr[t];var n=B("Symbol")(t);return Hr[t]=n,zr[n]=t,n}});var Ur=oe("symbol-to-string-registry");Ye({target:"Symbol",stat:!0,forced:!Fr},{keyFor:function(e){if(!X(e))throw TypeError(J(e)+" is not a symbol");if(le(Ur,e))return Ur[e]}});var Wr=B("JSON","stringify"),qr=v(/./.exec),Gr=v("".charAt),Kr=v("".charCodeAt),Zr=v("".replace),Yr=v(1..toString),Xr=/[\uD800-\uDFFF]/g,Vr=/^[\uD800-\uDBFF]$/,Jr=/^[\uDC00-\uDFFF]$/,Qr=!K||c((function(){var e=B("Symbol")();return"[null]"!=Wr([e])||"{}"!=Wr({a:e})||"{}"!=Wr(Object(e))})),ea=c((function(){return'"\\udf06\\ud834"'!==Wr("\udf06\ud834")||'"\\udead"'!==Wr("\udead")})),ta=function(e,t){var n=Xe(arguments),r=t;if((M(t)||void 0!==e)&&!X(e))return _n(t)||(t=function(e,t){if(y(r)&&(t=w(r,this,e,t)),!X(t))return t}),n[1]=t,p(Wr,null,n)},na=function(e,t,n){var r=Gr(n,t-1),a=Gr(n,t+1);return qr(Vr,e)&&!qr(Jr,a)||qr(Jr,e)&&!qr(Vr,r)?"\\u"+Yr(Kr(e,0),16):e};Wr&&Ye({target:"JSON",stat:!0,arity:3,forced:Qr||ea},{stringify:function(e,t,n){var r=Xe(arguments),a=p(Qr?ta:Wr,null,r);return ea&&"string"==typeof a?Zr(a,Xr,na):a}});var ra=!K||c((function(){qn.f(1)}));Ye({target:"Object",stat:!0,forced:ra},{getOwnPropertySymbols:function(e){var t=qn.f;return t?t(se(e)):[]}}),Yn("asyncIterator"),Yn("hasInstance"),Yn("isConcatSpreadable"),Yn("iterator"),Yn("match"),Yn("matchAll"),Yn("replace"),Yn("search"),Yn("species"),Yn("split"),Yn("toPrimitive"),Xn(),Yn("toStringTag"),er(B("Symbol"),"Symbol"),Yn("unscopables"),er(s.JSON,"JSON",!0);var aa,oa,ia,sa=j.Symbol,ca={},la=Function.prototype,ua=_&&Object.getOwnPropertyDescriptor,fa=le(la,"name"),da={EXISTS:fa,PROPER:fa&&"something"===function(){}.name,CONFIGURABLE:fa&&(!_||_&&ua(la,"name").configurable)},pa=!c((function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype})),ha=Wt("IE_PROTO"),ga=s.Object,ma=ga.prototype,ba=pa?ga.getPrototypeOf:function(e){var t=se(e);if(le(t,ha))return t[ha];var n=t.constructor;return y(n)&&t instanceof n?n.prototype:t instanceof ga?ma:null},va=ve("iterator"),ya=!1;[].keys&&("next"in(ia=[].keys())?(oa=ba(ba(ia)))!==Object.prototype&&(aa=oa):ya=!0);var _a=null==aa||c((function(){var e={};return aa[va].call(e)!==e}));aa=_a?{}:Xt(aa),y(aa[va])||Gn(aa,va,(function(){return this}));var ka={IteratorPrototype:aa,BUGGY_SAFARI_ITERATORS:ya},wa=ka.IteratorPrototype,Ea=function(){return this},xa=da.PROPER,Sa=ka.BUGGY_SAFARI_ITERATORS,Aa=ve("iterator"),Ca=function(){return this},Ta=function(e,t,n,r,a,o,i){!function(e,t,n,r){var a=t+" Iterator";e.prototype=Xt(wa,{next:A(+!r,n)}),er(e,a,!1,!0),ca[a]=Ea}(n,t,r);var s,c,l,u=function(e){if(e===a&&g)return g;if(!Sa&&e in p)return p[e];switch(e){case"keys":case"values":case"entries":return function(){return new n(this,e)}}return function(){return new n(this)}},f=t+" Iterator",d=!1,p=e.prototype,h=p[Aa]||p["@@iterator"]||a&&p[a],g=!Sa&&h||u(a),m="Array"==t&&p.entries||h;if(m&&(s=ba(m.call(new e)))!==Object.prototype&&s.next&&(er(s,f,!0,!0),ca[f]=Ca),xa&&"values"==a&&h&&"values"!==h.name&&(d=!0,g=function(){return w(h,this)}),a)if(c={values:u("values"),keys:o?g:u("keys"),entries:u("entries")},i)for(l in c)(Sa||d||!(l in p))&&Gn(p,l,c[l]);else Ye({target:t,proto:!0,forced:Sa||d},c);return i&&p[Aa]!==g&&Gn(p,Aa,g,{name:a}),ca[t]=g,c},Oa=ur.set,$a=ur.getterFor("Array Iterator"),Ra=(Ta(Array,"Array",(function(e,t){Oa(this,{type:"Array Iterator",target:N(e),index:0,kind:t})}),(function(){var e=$a(this),t=e.target,n=e.kind,r=e.index++;return!t||r>=t.length?(e.target=void 0,{value:void 0,done:!0}):"keys"==n?{value:r,done:!1}:"values"==n?{value:t[r],done:!1}:{value:[r,t[r]],done:!1}}),"values"),ca.Arguments=ca.Array,ve("toStringTag"));for(var Pa in{CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}){var Ia=s[Pa],La=Ia&&Ia.prototype;La&&ct(La)!==Ra&&Ge(La,Ra,Pa),ca[Pa]=ca.Array}var Na=sa;Yn("asyncDispose"),Yn("dispose"),Yn("matcher"),Yn("metadata"),Yn("observable"),Yn("patternMatch"),Yn("replaceAll");var Ma=Na,ja=v("".charAt),Da=v("".charCodeAt),Ba=v("".slice),Fa=function(e){return function(t,n){var r,a,o=Mn(L(t)),i=At(n),s=o.length;return i<0||i>=s?e?"":void 0:(r=Da(o,i))<55296||r>56319||i+1===s||(a=Da(o,i+1))<56320||a>57343?e?ja(o,i):r:e?Ba(o,i,i+2):a-56320+(r-55296<<10)+65536}},Ha={codeAt:Fa(!1),charAt:Fa(!0)}.charAt,za=ur.set,Ua=ur.getterFor("String Iterator");Ta(String,"String",(function(e){za(this,{type:"String Iterator",string:Mn(e),index:0})}),(function(){var e,t=Ua(this),n=t.string,r=t.index;return r>=n.length?{value:void 0,done:!0}:(e=Ha(n,r),t.index+=e.length,{value:e,done:!1})}));var Wa=Kn.f("iterator"),qa=r((function(e){function t(n){return e.exports=t="function"==typeof Ma&&"symbol"==typeof Wa?function(e){return typeof e}:function(e){return e&&"function"==typeof Ma&&e.constructor===Ma&&e!==Ma.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,t(n)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports})),Ga=n(qa),Ka=r((function(e){e.exports=function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e},e.exports.__esModule=!0,e.exports.default=e.exports})),Za=n(Ka),Ya=n(r((function(e){var t=qa.default;e.exports=function(e,n){if(n&&("object"===t(n)||"function"==typeof n))return n;if(void 0!==n)throw new TypeError("Derived constructors may only return object or undefined");return Ka(e)},e.exports.__esModule=!0,e.exports.default=e.exports}))),Xa=c((function(){ba(1)}));Ye({target:"Object",stat:!0,forced:Xa,sham:!pa},{getPrototypeOf:function(e){return ba(se(e))}});var Va=j.Object.getPrototypeOf,Ja=r((function(e){function t(n){return e.exports=t=bn?Va:function(e){return e.__proto__||Va(e)},e.exports.__esModule=!0,e.exports.default=e.exports,t(n)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports})),Qa=n(Ja),eo=n(r((function(e){e.exports=function(e,t,n){return t in e?cn(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e},e.exports.__esModule=!0,e.exports.default=e.exports})));var to=function(){this.__data__=[],this.size=0};var no=function(e,t){return e===t||e!=e&&t!=t};var ro=function(e,t){for(var n=e.length;n--;)if(no(e[n][0],t))return n;return-1},ao=Array.prototype.splice;var oo=function(e){var t=this.__data__,n=ro(t,e);return!(n<0)&&(n==t.length-1?t.pop():ao.call(t,n,1),--this.size,!0)};var io=function(e){var t=this.__data__,n=ro(t,e);return n<0?void 0:t[n][1]};var so=function(e){return ro(this.__data__,e)>-1};var co=function(e,t){var n=this.__data__,r=ro(n,e);return r<0?(++this.size,n.push([e,t])):n[r][1]=t,this};function lo(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}lo.prototype.clear=to,lo.prototype.delete=oo,lo.prototype.get=io,lo.prototype.has=so,lo.prototype.set=co;var uo=lo;var fo=function(){this.__data__=new uo,this.size=0};var po=function(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n};var ho=function(e){return this.__data__.get(e)};var go=function(e){return this.__data__.has(e)},mo="object"==typeof t&&t&&t.Object===Object&&t,bo="object"==typeof self&&self&&self.Object===Object&&self,vo=mo||bo||Function("return this")(),yo=vo.Symbol,_o=Object.prototype,ko=_o.hasOwnProperty,wo=_o.toString,Eo=yo?yo.toStringTag:void 0;var xo=function(e){var t=ko.call(e,Eo),n=e[Eo];try{e[Eo]=void 0;var r=!0}catch(e){}var a=wo.call(e);return r&&(t?e[Eo]=n:delete e[Eo]),a},So=Object.prototype.toString;var Ao=function(e){return So.call(e)},Co=yo?yo.toStringTag:void 0;var To=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":Co&&Co in Object(e)?xo(e):Ao(e)};var Oo=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)};var $o=function(e){if(!Oo(e))return!1;var t=To(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t},Ro=vo["__core-js_shared__"],Po=function(){var e=/[^.]+$/.exec(Ro&&Ro.keys&&Ro.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}();var Io=function(e){return!!Po&&Po in e},Lo=Function.prototype.toString;var No=function(e){if(null!=e){try{return Lo.call(e)}catch(e){}try{return e+""}catch(e){}}return""},Mo=/^\[object .+?Constructor\]$/,jo=Function.prototype,Do=Object.prototype,Bo=jo.toString,Fo=Do.hasOwnProperty,Ho=RegExp("^"+Bo.call(Fo).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");var zo=function(e){return!(!Oo(e)||Io(e))&&($o(e)?Ho:Mo).test(No(e))};var Uo=function(e,t){return null==e?void 0:e[t]};var Wo=function(e,t){var n=Uo(e,t);return zo(n)?n:void 0},qo=Wo(vo,"Map"),Go=Wo(Object,"create");var Ko=function(){this.__data__=Go?Go(null):{},this.size=0};var Zo=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},Yo=Object.prototype.hasOwnProperty;var Xo=function(e){var t=this.__data__;if(Go){var n=t[e];return"__lodash_hash_undefined__"===n?void 0:n}return Yo.call(t,e)?t[e]:void 0},Vo=Object.prototype.hasOwnProperty;var Jo=function(e){var t=this.__data__;return Go?void 0!==t[e]:Vo.call(t,e)};var Qo=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=Go&&void 0===t?"__lodash_hash_undefined__":t,this};function ei(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}ei.prototype.clear=Ko,ei.prototype.delete=Zo,ei.prototype.get=Xo,ei.prototype.has=Jo,ei.prototype.set=Qo;var ti=ei;var ni=function(){this.size=0,this.__data__={hash:new ti,map:new(qo||uo),string:new ti}};var ri=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e};var ai=function(e,t){var n=e.__data__;return ri(t)?n["string"==typeof t?"string":"hash"]:n.map};var oi=function(e){var t=ai(this,e).delete(e);return this.size-=t?1:0,t};var ii=function(e){return ai(this,e).get(e)};var si=function(e){return ai(this,e).has(e)};var ci=function(e,t){var n=ai(this,e),r=n.size;return n.set(e,t),this.size+=n.size==r?0:1,this};function li(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}li.prototype.clear=ni,li.prototype.delete=oi,li.prototype.get=ii,li.prototype.has=si,li.prototype.set=ci;var ui=li;var fi=function(e,t){var n=this.__data__;if(n instanceof uo){var r=n.__data__;if(!qo||r.length<199)return r.push([e,t]),this.size=++n.size,this;n=this.__data__=new ui(r)}return n.set(e,t),this.size=n.size,this};function di(e){var t=this.__data__=new uo(e);this.size=t.size}di.prototype.clear=fo,di.prototype.delete=po,di.prototype.get=ho,di.prototype.has=go,di.prototype.set=fi;var pi=di,hi=function(){try{var e=Wo(Object,"defineProperty");return e({},"",{}),e}catch(e){}}();var gi=function(e,t,n){"__proto__"==t&&hi?hi(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n};var mi=function(e,t,n){(void 0!==n&&!no(e[t],n)||void 0===n&&!(t in e))&&gi(e,t,n)};var bi=function(e){return function(t,n,r){for(var a=-1,o=Object(t),i=r(t),s=i.length;s--;){var c=i[e?s:++a];if(!1===n(o[c],c,o))break}return t}}(),vi=r((function(e,t){var n=t&&!t.nodeType&&t,r=n&&e&&!e.nodeType&&e,a=r&&r.exports===n?vo.Buffer:void 0,o=a?a.allocUnsafe:void 0;e.exports=function(e,t){if(t)return e.slice();var n=e.length,r=o?o(n):new e.constructor(n);return e.copy(r),r}})),yi=vo.Uint8Array;var _i=function(e){var t=new e.constructor(e.byteLength);return new yi(t).set(new yi(e)),t};var ki=function(e,t){var n=t?_i(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.length)};var wi=function(e,t){var n=-1,r=e.length;for(t||(t=Array(r));++n<r;)t[n]=e[n];return t},Ei=Object.create,xi=function(){function e(){}return function(t){if(!Oo(t))return{};if(Ei)return Ei(t);e.prototype=t;var n=new e;return e.prototype=void 0,n}}();var Si=function(e,t){return function(n){return e(t(n))}},Ai=Si(Object.getPrototypeOf,Object),Ci=Object.prototype;var Ti=function(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||Ci)};var Oi=function(e){return"function"!=typeof e.constructor||Ti(e)?{}:xi(Ai(e))};var $i=function(e){return null!=e&&"object"==typeof e};var Ri=function(e){return $i(e)&&"[object Arguments]"==To(e)},Pi=Object.prototype,Ii=Pi.hasOwnProperty,Li=Pi.propertyIsEnumerable,Ni=Ri(function(){return arguments}())?Ri:function(e){return $i(e)&&Ii.call(e,"callee")&&!Li.call(e,"callee")},Mi=Array.isArray;var ji=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991};var Di=function(e){return null!=e&&ji(e.length)&&!$o(e)};var Bi=function(e){return $i(e)&&Di(e)};var Fi=function(){return!1},Hi=r((function(e,t){var n=t&&!t.nodeType&&t,r=n&&e&&!e.nodeType&&e,a=r&&r.exports===n?vo.Buffer:void 0,o=(a?a.isBuffer:void 0)||Fi;e.exports=o})),zi=Function.prototype,Ui=Object.prototype,Wi=zi.toString,qi=Ui.hasOwnProperty,Gi=Wi.call(Object);var Ki=function(e){if(!$i(e)||"[object Object]"!=To(e))return!1;var t=Ai(e);if(null===t)return!0;var n=qi.call(t,"constructor")&&t.constructor;return"function"==typeof n&&n instanceof n&&Wi.call(n)==Gi},Zi={};Zi["[object Float32Array]"]=Zi["[object Float64Array]"]=Zi["[object Int8Array]"]=Zi["[object Int16Array]"]=Zi["[object Int32Array]"]=Zi["[object Uint8Array]"]=Zi["[object Uint8ClampedArray]"]=Zi["[object Uint16Array]"]=Zi["[object Uint32Array]"]=!0,Zi["[object Arguments]"]=Zi["[object Array]"]=Zi["[object ArrayBuffer]"]=Zi["[object Boolean]"]=Zi["[object DataView]"]=Zi["[object Date]"]=Zi["[object Error]"]=Zi["[object Function]"]=Zi["[object Map]"]=Zi["[object Number]"]=Zi["[object Object]"]=Zi["[object RegExp]"]=Zi["[object Set]"]=Zi["[object String]"]=Zi["[object WeakMap]"]=!1;var Yi=function(e){return $i(e)&&ji(e.length)&&!!Zi[To(e)]};var Xi=function(e){return function(t){return e(t)}},Vi=r((function(e,t){var n=t&&!t.nodeType&&t,r=n&&e&&!e.nodeType&&e,a=r&&r.exports===n&&mo.process,o=function(){try{var e=r&&r.require&&r.require("util").types;return e||a&&a.binding&&a.binding("util")}catch(e){}}();e.exports=o})),Ji=Vi&&Vi.isTypedArray,Qi=Ji?Xi(Ji):Yi;var es=function(e,t){if(("constructor"!==t||"function"!=typeof e[t])&&"__proto__"!=t)return e[t]},ts=Object.prototype.hasOwnProperty;var ns=function(e,t,n){var r=e[t];ts.call(e,t)&&no(r,n)&&(void 0!==n||t in e)||gi(e,t,n)};var rs=function(e,t,n,r){var a=!n;n||(n={});for(var o=-1,i=t.length;++o<i;){var s=t[o],c=r?r(n[s],e[s],s,n,e):void 0;void 0===c&&(c=e[s]),a?gi(n,s,c):ns(n,s,c)}return n};var as=function(e,t){for(var n=-1,r=Array(e);++n<e;)r[n]=t(n);return r},os=/^(?:0|[1-9]\d*)$/;var is=function(e,t){var n=typeof e;return!!(t=null==t?9007199254740991:t)&&("number"==n||"symbol"!=n&&os.test(e))&&e>-1&&e%1==0&&e<t},ss=Object.prototype.hasOwnProperty;var cs=function(e,t){var n=Mi(e),r=!n&&Ni(e),a=!n&&!r&&Hi(e),o=!n&&!r&&!a&&Qi(e),i=n||r||a||o,s=i?as(e.length,String):[],c=s.length;for(var l in e)!t&&!ss.call(e,l)||i&&("length"==l||a&&("offset"==l||"parent"==l)||o&&("buffer"==l||"byteLength"==l||"byteOffset"==l)||is(l,c))||s.push(l);return s};var ls=function(e){var t=[];if(null!=e)for(var n in Object(e))t.push(n);return t},us=Object.prototype.hasOwnProperty;var fs=function(e){if(!Oo(e))return ls(e);var t=Ti(e),n=[];for(var r in e)("constructor"!=r||!t&&us.call(e,r))&&n.push(r);return n};var ds=function(e){return Di(e)?cs(e,!0):fs(e)};var ps=function(e){return rs(e,ds(e))};var hs=function(e,t,n,r,a,o,i){var s=es(e,n),c=es(t,n),l=i.get(c);if(l)mi(e,n,l);else{var u=o?o(s,c,n+"",e,t,i):void 0,f=void 0===u;if(f){var d=Mi(c),p=!d&&Hi(c),h=!d&&!p&&Qi(c);u=c,d||p||h?Mi(s)?u=s:Bi(s)?u=wi(s):p?(f=!1,u=vi(c,!0)):h?(f=!1,u=ki(c,!0)):u=[]:Ki(c)||Ni(c)?(u=s,Ni(s)?u=ps(s):Oo(s)&&!$o(s)||(u=Oi(c))):f=!1}f&&(i.set(c,u),a(u,c,r,o,i),i.delete(c)),mi(e,n,u)}};var gs=function e(t,n,r,a,o){t!==n&&bi(n,(function(i,s){if(o||(o=new pi),Oo(i))hs(t,n,s,r,e,a,o);else{var c=a?a(es(t,s),i,s+"",t,n,o):void 0;void 0===c&&(c=i),mi(t,s,c)}}),ds)};var ms=function(e){return e};var bs=function(e,t,n){switch(n.length){case 0:return e.call(t);case 1:return e.call(t,n[0]);case 2:return e.call(t,n[0],n[1]);case 3:return e.call(t,n[0],n[1],n[2])}return e.apply(t,n)},vs=Math.max;var ys=function(e,t,n){return t=vs(void 0===t?e.length-1:t,0),function(){for(var r=arguments,a=-1,o=vs(r.length-t,0),i=Array(o);++a<o;)i[a]=r[t+a];a=-1;for(var s=Array(t+1);++a<t;)s[a]=r[a];return s[t]=n(i),bs(e,this,s)}};var _s=function(e){return function(){return e}},ks=hi?function(e,t){return hi(e,"toString",{configurable:!0,enumerable:!1,value:_s(t),writable:!0})}:ms,ws=Date.now;var Es=function(e){var t=0,n=0;return function(){var r=ws(),a=16-(r-n);if(n=r,a>0){if(++t>=800)return arguments[0]}else t=0;return e.apply(void 0,arguments)}}(ks);var xs=function(e,t){return Es(ys(e,t,ms),e+"")};var Ss=function(e,t,n){if(!Oo(n))return!1;var r=typeof t;return!!("number"==r?Di(n)&&is(t,n.length):"string"==r&&t in n)&&no(n[t],e)};var As=function(e){return xs((function(t,n){var r=-1,a=n.length,o=a>1?n[a-1]:void 0,i=a>2?n[2]:void 0;for(o=e.length>3&&"function"==typeof o?(a--,o):void 0,i&&Ss(n[0],n[1],i)&&(o=a<3?void 0:o,a=1),t=Object(t);++r<a;){var s=n[r];s&&e(t,s,r,o)}return t}))}((function(e,t,n,r){gs(e,t,n,r)})),Cs=sn;Ye({target:"Function",proto:!0,forced:Function.bind!==nt},{bind:nt});var Ts=function(e){return j[e+"Prototype"]},Os=Ts("Function").bind,$s=Function.prototype,Rs=function(e){var t=e.bind;return e===$s||F($s,e)&&t===$s.bind?Os:t},Ps=Rs,Is=Ts("Array").concat,Ls=Array.prototype,Ns=function(e){var t=e.concat;return e===Ls||F(Ls,e)&&t===Ls.concat?Is:t},Ms=function(e,t){var n=[][e];return!!n&&c((function(){n.call(null,t||function(){return 1},1)}))},js=pr.forEach,Ds=Ms("forEach")?[].forEach:function(e){return js(this,e,arguments.length>1?arguments[1]:void 0)};Ye({target:"Array",proto:!0,forced:[].forEach!=Ds},{forEach:Ds});var Bs=Ts("Array").forEach,Fs=Array.prototype,Hs={DOMTokenList:!0,NodeList:!0},zs=function(e){var t=e.forEach;return e===Fs||F(Fs,e)&&t===Fs.forEach||le(Hs,ct(e))?Bs:t},Us=c((function(){Ft(1)}));Ye({target:"Object",stat:!0,forced:Us},{keys:function(e){return Ft(se(e))}});var Ws=j.Object.keys,qs=pr.filter,Gs=An("filter");Ye({target:"Array",proto:!0,forced:!Gs},{filter:function(e){return qs(this,e,arguments.length>1?arguments[1]:void 0)}});var Ks=Ts("Array").filter,Zs=Array.prototype,Ys=function(e){var t=e.filter;return e===Zs||F(Zs,e)&&t===Zs.filter?Ks:t},Xs=pr.findIndex,Vs=!0;"findIndex"in[]&&Array(1).findIndex((function(){Vs=!1})),Ye({target:"Array",proto:!0,forced:Vs},{findIndex:function(e){return Xs(this,e,arguments.length>1?arguments[1]:void 0)}});var Js=Ts("Array").findIndex,Qs=Array.prototype,ec=function(e){var t=e.findIndex;return e===Qs||F(Qs,e)&&t===Qs.findIndex?Js:t},tc=An("splice"),nc=s.TypeError,rc=Math.max,ac=Math.min;Ye({target:"Array",proto:!0,forced:!tc},{splice:function(e,t){var n,r,a,o,i,s,c=se(this),l=Pt(c),u=Ot(e,l),f=arguments.length;if(0===f?n=r=0:1===f?(n=0,r=l-u):(n=f-2,r=ac(rc(At(t),0),l-u)),l+n-r>9007199254740991)throw nc("Maximum allowed length exceeded");for(a=xn(c,r),o=0;o<r;o++)(i=u+o)in c&&kn(a,o,c[i]);if(a.length=r,n<r){for(o=u;o<l-r;o++)s=o+n,(i=o+r)in c?c[s]=c[i]:delete c[s];for(o=l;o>l-r+n;o--)delete c[o-1]}else if(n>r)for(o=l-r;o>u;o--)s=o+n-1,(i=o+r-1)in c?c[s]=c[i]:delete c[s];for(o=0;o<n;o++)c[o+u]=arguments[o+2];return c.length=l-r+n,a}});var oc=Ts("Array").splice,ic=Array.prototype,sc=function(e){var t=e.splice;return e===ic||F(ic,e)&&t===ic.splice?oc:t},cc=!1,lc={SEN:"sentence",PAR:"paragraph",DEFAULT:"sentence"},uc=function(){function e(t){un(this,e),eo(this,"$engine",void 0),this.RULE=this.rule(t)}return ln(e,[{key:"getType",value:function(){return this.constructor.HOOK_TYPE||lc.DEFAULT}},{key:"getName",value:function(){return this.constructor.HOOK_NAME}},{key:"beforeMakeHtml",value:function(e){return e}},{key:"makeHtml",value:function(e){return e}},{key:"afterMakeHtml",value:function(e){return e}},{key:"onKeyDown",value:function(e,t){}},{key:"getOnKeyDown",value:function(){return this.onKeyDown||!1}},{key:"getAttributesTest",value:function(){return/^(color|fontSize|font-size|id|title|class|target|underline|line-through|overline|sub|super)$/}},{key:"$testAttributes",value:function(e,t){this.getAttributesTest().test(e)&&t()}},{key:"getAttributes",value:function(e){return{attrs:{},str:e}}},{key:"test",value:function(e){return!!this.RULE.reg&&this.RULE.reg.test(e)}},{key:"rule",value:function(e){return{begin:"",end:"",content:"",reg:new RegExp("")}}},{key:"mounted",value:function(){}}],[{key:"getMathJaxConfig",value:function(){return cc}},{key:"setMathJaxConfig",value:function(e){cc=e}}]),e}();eo(uc,"HOOK_NAME","default"),eo(uc,"HOOK_TYPE",lc.DEFAULT);var fc=pr.map,dc=An("map");Ye({target:"Array",proto:!0,forced:!dc},{map:function(e){return fc(this,e,arguments.length>1?arguments[1]:void 0)}});var pc,hc=Ts("Array").map,gc=Array.prototype,mc=function(e){var t=e.map;return e===gc||F(gc,e)&&t===gc.map?hc:t},bc="\t\n\v\f\r                　\u2028\u2029\ufeff",vc=v("".replace),yc="["+bc+"]",_c=RegExp("^"+yc+yc+"*"),kc=RegExp(yc+yc+"*$"),wc=function(e){return function(t){var n=Mn(L(t));return 1&e&&(n=vc(n,_c,"")),2&e&&(n=vc(n,kc,"")),n}},Ec={start:wc(1),end:wc(2),trim:wc(3)},xc=da.PROPER,Sc=Ec.trim;Ye({target:"String",proto:!0,forced:(pc="trim",c((function(){return!!bc[pc]()||"​᠎"!=="​᠎"[pc]()||xc&&bc[pc].name!==pc})))},{trim:function(){return Sc(this)}});var Ac=Ts("String").trim,Cc=String.prototype,Tc=function(e){var t=e.trim;return"string"==typeof e||e===Cc||F(Cc,e)&&t===Cc.trim?Ac:t},Oc=Ec.trim,$c=s.parseInt,Rc=s.Symbol,Pc=Rc&&Rc.iterator,Ic=/^[+-]?0x/i,Lc=v(Ic.exec),Nc=8!==$c(bc+"08")||22!==$c(bc+"0x16")||Pc&&!c((function(){$c(Object(Pc))}))?function(e,t){var n=Oc(Mn(e));return $c(n,t>>>0||(Lc(Ic,n)?16:10))}:$c;Ye({global:!0,forced:parseInt!=Nc},{parseInt:Nc});var Mc=j.parseInt;function jc(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(!/^\n/.test(e))return t;if(n){var r,a,o,i=null!==(r=null===(a=e.match(/^\n+/g))||void 0===a||null===(o=a[0])||void 0===o?void 0:o.length)&&void 0!==r?r:0;return i>1?"\n\n".concat(t):"\n".concat(t)}return"\n\n".concat(t)}function Dc(e,t){var n=(e.match(/\n/g)||[]).length;return""!==e&&(n-=2),n+t}function Bc(e){var t=function(){if("undefined"==typeof Reflect||!an)return!1;if(an.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(an(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Qa(e);if(t){var a=Qa(this).constructor;n=an(r,arguments,a)}else n=r.apply(this,arguments);return Ya(this,n)}}var Fc=0,Hc=function(e){yn(n,e);var t=Bc(n);function n(){var e,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{needCache:!1},a=r.needCache,o=r.defaultCache,i=void 0===o?{}:o;return un(this,n),(e=t.call(this,{})).needCache=!!a,e.sign="",a&&(e.cache=i||{},e.cacheKey="~~C".concat(Fc),Fc+=1),e}return ln(n,[{key:"toHtml",value:function(e,t){return e}},{key:"makeHtml",value:function(e,t){return t(e).html}},{key:"afterMakeHtml",value:function(e){return this.restoreCache(e)}},{key:"isContainsCache",value:function(e,t){if(t){var r=/^(\s*~~C\d+I\w+\$\s*)+$/g.test(e),a=new RegExp("~~C\\d+I".concat(n.IN_PARAGRAPH_CACHE_KEY_PREFIX_REGEX,"\\w+\\$"),"g").test(e);return r&&!a}return new RegExp("~~C\\d+I(?!".concat(n.IN_PARAGRAPH_CACHE_KEY_PREFIX_REGEX,")\\w+\\$"),"g").test(e)}},{key:"$splitHtmlByCache",value:function(e){var t=new RegExp("\\n*~~C\\d+I(?!".concat(n.IN_PARAGRAPH_CACHE_KEY_PREFIX_REGEX,")\\w+\\$\\n?"),"g");return{caches:e.match(t),contents:e.split(t)}}},{key:"makeExcludingCached",value:function(e,t){for(var n=this.$splitHtmlByCache(e),r=n.caches,a=n.contents,o=mc(a).call(a,t),i="",s=0;s<o.length;s++){var c;if(i+=o[s],r&&r[s])i+=Tc(c=r[s]).call(c)}return i}},{key:"getCacheWithSpace",value:function(e,t){var n,r,a,o,i,s,c=arguments.length>2&&void 0!==arguments[2]&&arguments[2],l=null!==(n=null===(r=t.match(/^\n+/))||void 0===r?void 0:r[0])&&void 0!==n?n:"",u=null!==(a=null===(o=t.match(/\n+$/))||void 0===o?void 0:o[0])&&void 0!==a?a:"";return c?jc(t,e):Ns(i=Ns(s="".concat(l)).call(s,e)).call(i,u)}},{key:"getLineCount",value:function(e){var t,r,a,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",i=e,s=null!==(t=null===(r=o.match(/^\n+/g))||void 0===r||null===(a=r[0])||void 0===a?void 0:a.length)&&void 0!==t?t:0;s=1===s?1:0,i=i.replace(/^\n+/g,"");var c=new RegExp("\n*~~C\\d+I(?:".concat(n.IN_PARAGRAPH_CACHE_KEY_PREFIX_REGEX,")?\\w+?_L(\\d+)\\$"),"g"),l=0;return i=i.replace(c,(function(e,t){return l+=Mc(t,10),e.replace(/^\n+/g,"")})),s+l+(i.match(/\n/g)||[]).length+1}},{key:"pushCache",value:function(e){var t,n,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;if(this.needCache){var o=r||this.$engine.md5(e);return this.cache[o]={content:e,using:!0},Ns(t=Ns(n="".concat(this.cacheKey,"I")).call(n,o,"_L")).call(t,a,"$")}}},{key:"popCache",value:function(e){if(this.needCache)return this.cache[e].content||""}},{key:"resetCache",value:function(){if(this.needCache){for(var e=0,t=Ws(this.cache);e<t.length;e++){var n=t[e];this.cache[n].using||delete this.cache[n]}for(var r=0,a=Ws(this.cache);r<a.length;r++){var o=a[r];this.cache[o].using=!1}}}},{key:"restoreCache",value:function(e){var t,r=this;if(!this.needCache)return e;var a=new RegExp(Ns(t="".concat(this.cacheKey,"I((?:")).call(t,n.IN_PARAGRAPH_CACHE_KEY_PREFIX_REGEX,")?\\w+)\\$"),"g"),o=e.replace(a,(function(e,t){return r.popCache(t.replace(/_L\d+$/,""))}));return this.resetCache(),o}},{key:"checkCache",value:function(e,t){var n,r,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;return this.sign=this.$engine.md5(e),this.cache[this.sign]?(this.cache[this.sign].using=!0,Ns(n=Ns(r="".concat(this.cacheKey,"I")).call(r,this.sign,"_L")).call(n,a,"$")):this.toHtml(e,t)}},{key:"mounted",value:function(){}},{key:"signWithCache",value:function(e){return!1}}]),n}(uc);eo(Hc,"HOOK_TYPE",lc.PAR),eo(Hc,"IN_PARAGRAPH_CACHE_KEY_PREFIX","!"),eo(Hc,"IN_PARAGRAPH_CACHE_KEY_PREFIX_REGEX","\\!");var zc=c((function(){if("function"==typeof ArrayBuffer){var e=new ArrayBuffer(8);Object.isExtensible(e)&&Object.defineProperty(e,"a",{value:8})}})),Uc=Object.isExtensible,Wc=c((function(){Uc(1)}))||zc?function(e){return!!M(e)&&((!zc||"ArrayBuffer"!=O(e))&&(!Uc||Uc(e)))}:Uc,qc=!c((function(){return Object.isExtensible(Object.preventExtensions({}))})),Gc=r((function(e){var t=qe.f,n=!1,r=pe("meta"),a=0,o=function(e){t(e,r,{value:{objectID:"O"+a++,weakData:{}}})},i=e.exports={enable:function(){i.enable=function(){},n=!0;var e=Dn.f,t=v([].splice),a={};a[r]=1,e(a).length&&(Dn.f=function(n){for(var a=e(n),o=0,i=a.length;o<i;o++)if(a[o]===r){t(a,o,1);break}return a},Ye({target:"Object",stat:!0,forced:!0},{getOwnPropertyNames:Wn.f}))},fastKey:function(e,t){if(!M(e))return"symbol"==typeof e?e:("string"==typeof e?"S":"P")+e;if(!le(e,r)){if(!Wc(e))return"F";if(!t)return"E";o(e)}return e[r].objectID},getWeakData:function(e,t){if(!le(e,r)){if(!Wc(e))return!0;if(!t)return!1;o(e)}return e[r].weakData},onFreeze:function(e){return qc&&n&&Wc(e)&&!le(e,r)&&o(e),e}};Nt[r]=!0})),Kc=(Gc.enable,Gc.fastKey,Gc.getWeakData,Gc.onFreeze,ve("iterator")),Zc=Array.prototype,Yc=function(e){return void 0!==e&&(ca.Array===e||Zc[Kc]===e)},Xc=ve("iterator"),Vc=function(e){if(null!=e)return te(e,Xc)||te(e,"@@iterator")||ca[ct(e)]},Jc=s.TypeError,Qc=function(e,t){var n=arguments.length<2?Vc(e):t;if(ee(n))return He(w(n,e));throw Jc(J(e)+" is not iterable")},el=function(e,t,n){var r,a;He(e);try{if(!(r=te(e,"return"))){if("throw"===t)throw n;return n}r=w(r,e)}catch(e){a=!0,r=e}if("throw"===t)throw n;if(a)throw r;return He(r),n},tl=s.TypeError,nl=function(e,t){this.stopped=e,this.result=t},rl=nl.prototype,al=function(e,t,n){var r,a,o,i,s,c,l,u=n&&n.that,f=!(!n||!n.AS_ENTRIES),d=!(!n||!n.IS_ITERATOR),p=!(!n||!n.INTERRUPTED),h=je(t,u),g=function(e){return r&&el(r,"normal",e),new nl(!0,e)},m=function(e){return f?(He(e),p?h(e[0],e[1],g):h(e[0],e[1])):p?h(e,g):h(e)};if(d)r=e;else{if(!(a=Vc(e)))throw tl(J(e)+" is not iterable");if(Yc(a)){for(o=0,i=Pt(e);i>o;o++)if((s=m(e[o]))&&F(rl,s))return s;return new nl(!1)}r=Qc(e,a)}for(c=r.next;!(l=w(c,r)).done;){try{s=m(l.value)}catch(e){el(r,"throw",e)}if("object"==typeof s&&s&&F(rl,s))return s}return new nl(!1)},ol=s.TypeError,il=function(e,t){if(F(t,e))return e;throw ol("Incorrect invocation")},sl=qe.f,cl=pr.forEach,ll=ur.set,ul=ur.getterFor,fl=function(e,t,n){for(var r in t)n&&n.unsafe&&e[r]?e[r]=t[r]:Gn(e,r,t[r],n);return e},dl=ve("species"),pl=qe.f,hl=Gc.fastKey,gl=ur.set,ml=ur.getterFor;!function(e,t,n){var r,a=-1!==e.indexOf("Map"),o=-1!==e.indexOf("Weak"),i=a?"set":"add",l=s[e],u=l&&l.prototype,f={};if(_&&y(l)&&(o||u.forEach&&!c((function(){(new l).entries().next()})))){var d=(r=t((function(t,n){ll(il(t,d),{type:e,collection:new l}),null!=n&&al(n,t[i],{that:t,AS_ENTRIES:a})}))).prototype,p=ul(e);cl(["add","clear","delete","forEach","get","has","set","keys","values","entries"],(function(e){var t="add"==e||"set"==e;!(e in u)||o&&"clear"==e||Ge(d,e,(function(n,r){var a=p(this).collection;if(!t&&o&&!M(n))return"get"==e&&void 0;var i=a[e](0===n?0:n,r);return t?this:i}))})),o||sl(d,"size",{configurable:!0,get:function(){return p(this).collection.size}})}else r=n.getConstructor(t,e,a,i),Gc.enable();er(r,e,!1,!0),f[e]=r,Ye({global:!0,forced:!0},f),o||n.setStrong(r,e,a)}("Map",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),{getConstructor:function(e,t,n,r){var a=e((function(e,a){il(e,o),gl(e,{type:t,index:Xt(null),first:void 0,last:void 0,size:0}),_||(e.size=0),null!=a&&al(a,e[r],{that:e,AS_ENTRIES:n})})),o=a.prototype,i=ml(t),s=function(e,t,n){var r,a,o=i(e),s=c(e,t);return s?s.value=n:(o.last=s={index:a=hl(t,!0),key:t,value:n,previous:r=o.last,next:void 0,removed:!1},o.first||(o.first=s),r&&(r.next=s),_?o.size++:e.size++,"F"!==a&&(o.index[a]=s)),e},c=function(e,t){var n,r=i(e),a=hl(t);if("F"!==a)return r.index[a];for(n=r.first;n;n=n.next)if(n.key==t)return n};return fl(o,{clear:function(){for(var e=i(this),t=e.index,n=e.first;n;)n.removed=!0,n.previous&&(n.previous=n.previous.next=void 0),delete t[n.index],n=n.next;e.first=e.last=void 0,_?e.size=0:this.size=0},delete:function(e){var t=i(this),n=c(this,e);if(n){var r=n.next,a=n.previous;delete t.index[n.index],n.removed=!0,a&&(a.next=r),r&&(r.previous=a),t.first==n&&(t.first=r),t.last==n&&(t.last=a),_?t.size--:this.size--}return!!n},forEach:function(e){for(var t,n=i(this),r=je(e,arguments.length>1?arguments[1]:void 0);t=t?t.next:n.first;)for(r(t.value,t.key,this);t&&t.removed;)t=t.previous},has:function(e){return!!c(this,e)}}),fl(o,n?{get:function(e){var t=c(this,e);return t&&t.value},set:function(e,t){return s(this,0===e?0:e,t)}}:{add:function(e){return s(this,e=0===e?0:e,e)}}),_&&pl(o,"size",{get:function(){return i(this).size}}),a},setStrong:function(e,t,n){var r=t+" Iterator",a=ml(t),o=ml(r);Ta(e,t,(function(e,t){gl(this,{type:r,target:e,state:a(e),kind:t,last:void 0})}),(function(){for(var e=o(this),t=e.kind,n=e.last;n&&n.removed;)n=n.previous;return e.target&&(e.last=n=n?n.next:e.state.first)?"keys"==t?{value:n.key,done:!1}:"values"==t?{value:n.value,done:!1}:{value:[n.key,n.value],done:!1}:(e.target=void 0,{value:void 0,done:!0})}),n?"entries":"values",!n,!0),function(e){var t=B(e),n=qe.f;_&&t&&!t[dl]&&n(t,dl,{configurable:!0,get:function(){return this}})}(t)}});var bl=j.Map,vl=[].push;Ye({target:"Map",stat:!0,forced:!0},{from:function(e){var t,n,r,a,o=arguments.length,i=o>1?arguments[1]:void 0;return wt(this),(t=void 0!==i)&&ee(i),null==e?new this:(n=[],t?(r=0,a=je(i,o>2?arguments[2]:void 0),al(e,(function(e){w(vl,n,a(e,r++))}))):al(e,vl,{that:n}),new this(n))}});Ye({target:"Map",stat:!0,forced:!0},{of:function(){return new this(Xe(arguments))}});Ye({target:"Map",proto:!0,real:!0,forced:!0},{deleteAll:function(){for(var e,t=He(this),n=ee(t.delete),r=!0,a=0,o=arguments.length;a<o;a++)e=w(n,t,arguments[a]),r=r&&e;return!!r}});Ye({target:"Map",proto:!0,real:!0,forced:!0},{emplace:function(e,t){var n=He(this),r=ee(n.get),a=ee(n.has),o=ee(n.set),i=w(a,n,e)&&"update"in t?t.update(w(r,n,e),e,n):t.insert(e,n);return w(o,n,e,i),i}});var yl=Qc;Ye({target:"Map",proto:!0,real:!0,forced:!0},{every:function(e){var t=He(this),n=yl(t),r=je(e,arguments.length>1?arguments[1]:void 0);return!al(n,(function(e,n,a){if(!r(n,e,t))return a()}),{AS_ENTRIES:!0,IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}});var _l=ve("species"),kl=function(e,t){var n,r=He(e).constructor;return void 0===r||null==(n=He(r)[_l])?t:wt(n)};Ye({target:"Map",proto:!0,real:!0,forced:!0},{filter:function(e){var t=He(this),n=yl(t),r=je(e,arguments.length>1?arguments[1]:void 0),a=new(kl(t,B("Map"))),o=ee(a.set);return al(n,(function(e,n){r(n,e,t)&&w(o,a,e,n)}),{AS_ENTRIES:!0,IS_ITERATOR:!0}),a}}),Ye({target:"Map",proto:!0,real:!0,forced:!0},{find:function(e){var t=He(this),n=yl(t),r=je(e,arguments.length>1?arguments[1]:void 0);return al(n,(function(e,n,a){if(r(n,e,t))return a(n)}),{AS_ENTRIES:!0,IS_ITERATOR:!0,INTERRUPTED:!0}).result}}),Ye({target:"Map",proto:!0,real:!0,forced:!0},{findKey:function(e){var t=He(this),n=yl(t),r=je(e,arguments.length>1?arguments[1]:void 0);return al(n,(function(e,n,a){if(r(n,e,t))return a(e)}),{AS_ENTRIES:!0,IS_ITERATOR:!0,INTERRUPTED:!0}).result}});var wl=v([].push);Ye({target:"Map",stat:!0,forced:!0},{groupBy:function(e,t){ee(t);var n=Qc(e),r=new this,a=ee(r.has),o=ee(r.get),i=ee(r.set);return al(n,(function(e){var n=t(e);w(a,r,n)?wl(w(o,r,n),e):w(i,r,n,[e])}),{IS_ITERATOR:!0}),r}});Ye({target:"Map",proto:!0,real:!0,forced:!0},{includes:function(e){return al(yl(He(this)),(function(t,n,r){if((a=n)===(o=e)||a!=a&&o!=o)return r();var a,o}),{AS_ENTRIES:!0,IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}}),Ye({target:"Map",stat:!0,forced:!0},{keyBy:function(e,t){var n=new this;ee(t);var r=ee(n.set);return al(e,(function(e){w(r,n,t(e),e)})),n}}),Ye({target:"Map",proto:!0,real:!0,forced:!0},{keyOf:function(e){return al(yl(He(this)),(function(t,n,r){if(n===e)return r(t)}),{AS_ENTRIES:!0,IS_ITERATOR:!0,INTERRUPTED:!0}).result}}),Ye({target:"Map",proto:!0,real:!0,forced:!0},{mapKeys:function(e){var t=He(this),n=yl(t),r=je(e,arguments.length>1?arguments[1]:void 0),a=new(kl(t,B("Map"))),o=ee(a.set);return al(n,(function(e,n){w(o,a,r(n,e,t),n)}),{AS_ENTRIES:!0,IS_ITERATOR:!0}),a}}),Ye({target:"Map",proto:!0,real:!0,forced:!0},{mapValues:function(e){var t=He(this),n=yl(t),r=je(e,arguments.length>1?arguments[1]:void 0),a=new(kl(t,B("Map"))),o=ee(a.set);return al(n,(function(e,n){w(o,a,e,r(n,e,t))}),{AS_ENTRIES:!0,IS_ITERATOR:!0}),a}}),Ye({target:"Map",proto:!0,real:!0,arity:1,forced:!0},{merge:function(e){for(var t=He(this),n=ee(t.set),r=arguments.length,a=0;a<r;)al(arguments[a++],n,{that:t,AS_ENTRIES:!0});return t}});var El=s.TypeError;Ye({target:"Map",proto:!0,real:!0,forced:!0},{reduce:function(e){var t=He(this),n=yl(t),r=arguments.length<2,a=r?void 0:arguments[1];if(ee(e),al(n,(function(n,o){r?(r=!1,a=o):a=e(a,o,n,t)}),{AS_ENTRIES:!0,IS_ITERATOR:!0}),r)throw El("Reduce of empty map with no initial value");return a}}),Ye({target:"Map",proto:!0,real:!0,forced:!0},{some:function(e){var t=He(this),n=yl(t),r=je(e,arguments.length>1?arguments[1]:void 0);return al(n,(function(e,n,a){if(r(n,e,t))return a()}),{AS_ENTRIES:!0,IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}});var xl=s.TypeError;Ye({target:"Map",proto:!0,real:!0,forced:!0},{update:function(e,t){var n=He(this),r=ee(n.get),a=ee(n.has),o=ee(n.set),i=arguments.length;ee(t);var s=w(a,n,e);if(!s&&i<3)throw xl("Updating absent value");var c=s?w(r,n,e):ee(i>2?arguments[2]:void 0)(e,n);return w(o,n,e,t(c,e,n)),n}});var Sl=s.TypeError,Al=function(e,t){var n,r=He(this),a=ee(r.get),o=ee(r.has),i=ee(r.set),s=arguments.length>2?arguments[2]:void 0;if(!y(t)&&!y(s))throw Sl("At least one callback required");return w(o,r,e)?(n=w(a,r,e),y(t)&&(n=t(n),w(i,r,e,n))):y(s)&&(n=s(),w(i,r,e,n)),n};Ye({target:"Map",proto:!0,real:!0,forced:!0},{upsert:Al}),Ye({target:"Map",proto:!0,real:!0,name:"upsert",forced:!0},{updateOrInsert:Al});var Cl=bl,Tl=Lt.indexOf,Ol=v([].indexOf),$l=!!Ol&&1/Ol([1],1,-0)<0,Rl=Ms("indexOf");Ye({target:"Array",proto:!0,forced:$l||!Rl},{indexOf:function(e){var t=arguments.length>1?arguments[1]:void 0;return $l?Ol(this,e,t)||0:Tl(this,e,t)}});var Pl=Ts("Array").indexOf,Il=Array.prototype,Ll=function(e){var t=e.indexOf;return e===Il||F(Il,e)&&t===Il.indexOf?Pl:t},Nl=Ll,Ml=r((function(e){e.exports=function(e){var t;return-1!==Nl(t=Function.toString.call(e)).call(t,"[native code]")},e.exports.__esModule=!0,e.exports.default=e.exports}));n(Ml);var jl=rn,Dl=Rs,Bl=r((function(e){e.exports=function(){if("undefined"==typeof Reflect||!jl)return!1;if(jl.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(jl(Boolean,[],(function(){}))),!0}catch(e){return!1}},e.exports.__esModule=!0,e.exports.default=e.exports}));n(Bl);var Fl=r((function(e){function t(n,r,a){return Bl()?(e.exports=t=jl,e.exports.__esModule=!0,e.exports.default=e.exports):(e.exports=t=function(e,t,n){var r=[null];r.push.apply(r,t);var a=new(Dl(Function).apply(e,r));return n&&vn(a,n.prototype),a},e.exports.__esModule=!0,e.exports.default=e.exports),t.apply(null,arguments)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}));n(Fl);var Hl=n(r((function(e){function t(n){var r="function"==typeof Cl?new Cl:void 0;return e.exports=t=function(e){if(null===e||!Ml(e))return e;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==r){if(r.has(e))return r.get(e);r.set(e,t)}function t(){return Fl(e,arguments,Ja(this).constructor)}return t.prototype=pn(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),vn(t,e)},e.exports.__esModule=!0,e.exports.default=e.exports,t(n)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports})));Ye({target:"Array",stat:!0},{isArray:_n});var zl=j.Array.isArray,Ul=zl;function Wl(e){var t=function(){if("undefined"==typeof Reflect||!an)return!1;if(an.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(an(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Qa(e);if(t){var a=Qa(this).constructor;n=an(r,arguments,a)}else n=r.apply(this,arguments);return Ya(this,n)}}var ql=function(e,t){if(!Ul(e)&&Ga(e)!==t.name.toLowerCase()||!Ul(e)&&"array"===t.name.toLowerCase())throw new TypeError("parameter given must be ".concat(t.name));return!0},Gl=function(e,t){if(!(e instanceof t))throw new Error("the hook does not correctly inherit");return!0},Kl=function(e){if("object"!==Ga(e))throw new Error("the hook must be a instance, not a class");return!0},Zl=function(e){yn(n,e);var t=Wl(n);function n(e,r){var a;return un(this,n),(a=t.call(this,e)).name="Error",a.stack=a.buildStackTrace(r),a}return ln(n,[{key:"buildStackTrace",value:function(e){var t,n=e&&e.stack?e.stack:"";return Ns(t="".concat(this.stack,"\nCaused By: ")).call(t,n)}}]),n}(Hl(Error)),Yl=new Proxy({},{get:function(e,t,n){return function(){}}});function Xl(e,t,n){var r,a;if(-1===e)Yl.warn(Ns(r=Ns(a="Duplicate hook name [".concat(t.HOOK_NAME,"] found, hook [")).call(a,t.toString(),"] ")).call(r,isNaN(n)?"":"at index [".concat(n,"] "),"will not take effect."));else if(-2===e){var o;Yl.warn(Ns(o="Hook [".concat(t.toString(),"] ")).call(o,isNaN(n)?"":"at index [".concat(n,"] "),"is not a valid hook, and will not take effect."))}}function Vl(e){return Jl(e)||Ql(e)}function Jl(e){return Object.prototype.isPrototypeOf.call(uc,e)}function Ql(e){return Object.prototype.isPrototypeOf.call(Hc,e)}function eu(e){return Vl(e)&&!0===(null==e?void 0:e.Cherry$$CUSTOM)}var tu=function(){function e(t,n){un(this,e),this.hookList={},this.hookNameList={},ql(t,Array),this.registerInternalHooks(t,n),this.registerCustomHooks(n.engine.customSyntax,n)}return ln(e,[{key:"registerInternalHooks",value:function(e,t){var n=this;zs(e).call(e,(function(e,r){Xl(n.register(e,t),e,r)}))}},{key:"registerCustomHooks",value:function(e,t){var n=this;if(e){var r=Ws(e);zs(r).call(r,(function(r){var a,o,i,s,c={},l=e[r];if(Jl(l))o=l;else{if(!Jl(s=null==(i=l)?void 0:i.syntaxClass)&&!Ql(s))return;o=l.syntaxClass,c.force=Boolean(l.force),l.before?c.before=l.before:l.after&&(c.after=l.after)}Vl(o)?(Cs(o,"Cherry$$CUSTOM",{enumerable:!1,configurable:!1,writable:!1,value:!0}),a=n.register(o,t,c)):a=-2,Xl(a,o,void 0)}))}}},{key:"getHookList",value:function(){return this.hookList}},{key:"getHookNameList",value:function(){return this.hookNameList}},{key:"register",value:function(e,t,n){var r,a,o=t.externals,i=t.engine,s=i.syntax;if(Vl(e)){a=e.HOOK_NAME,r=new e({externals:o,config:(null==s?void 0:s[a])||{},globalConfig:i.global})}else{if("function"!=typeof e)return-2;if(!(r=e(t))||!Vl(r.constructor))return-2;a=r.getName()}if(!1!==s[a]||eu(e)){var c=r.getType();if(this.hookNameList[a]){var l;if(!eu(e))return-1;if(!n.force)return-1;var u=this.hookNameList[a].type;this.hookList[u]=Ys(l=this.hookList[u]).call(l,(function(e){return e.getName()!==a}))}if(this.hookNameList[a]={type:c},this.hookList[c]=this.hookList[c]||[],eu(e)){var f,d,p,h=-1;if(n.before){if(-1===(h=ec(f=this.hookList[c]).call(f,(function(e){return e.getName()===n.before}))))Yl.warn(Ns(d="Cannot find hook named [".concat(n.before,"],\n            custom hook [")).call(d,a,"] will append to the end of the hooks."))}else if(n.after){var g,m;-1===(h=ec(g=this.hookList[c]).call(g,(function(e){return e.getName()===n.after})))?Yl.warn(Ns(m="Cannot find hook named [".concat(n.after,"],\n              custom hook [")).call(m,a,"] will append to the end of the hooks.")):h+=1}if(h<0||h>=this.hookList[c].length)this.hookList[c].push(r);else sc(p=this.hookList[c]).call(p,h,0,r)}else this.hookList[c].push(r)}}}]),e}();function nu(e,t,n){var r=e.begin+e.content+e.end;return n&&(r=r.replace(/\[\\h\]/g,au).replace(/\\h/g,au)),new RegExp(r,t||"g")}function ru(){try{return new RegExp("(?<=.)"),!0}catch(e){}return!1}var au="[ \\t\\u00a0]",ou="[\\u0021-\\u002F\\u003a-\\u0040\\u005b-\\u0060\\u007b-\\u007e]",iu="[\\u0021-\\u002F\\u003a-\\u0040\\u005b\\u005d\\u005e\\u0060\\u007b-\\u007e \\t\\n！“”¥‘’（），。—：；《》？【】「」·～｜]",su=new RegExp([/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+/.source,"@",/[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*/.source].join("")),cu=new RegExp("^".concat(su.source,"$")),lu=new RegExp('(?:\\S+(?::\\S*)?@)?(?:(?:1\\d\\d|2[01]\\d|22[0-3]|[1-9]\\d?)(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}(?:\\.(?:1\\d\\d|2[0-4]\\d|25[0-4]|[1-9]\\d?))|(?![-_])(?:[-\\w\\xa1-\\xff]{0,63}[^-_]\\.)+(?:[a-zA-Z\\xa1-\\xff]{2,}\\.?))(?::\\d{2,5})?(?:[/?#][^\\s<>\\x00-\\x1f"\\(\\)]*)?'),uu=new RegExp("(?:\\/\\/)".concat(lu.source)),fu=new RegExp("^".concat(lu.source,"$")),du=new RegExp("^".concat(uu.source,"$"));var pu=zl,hu=r((function(e){e.exports=function(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r},e.exports.__esModule=!0,e.exports.default=e.exports}));n(hu);var gu=r((function(e){e.exports=function(e){if(pu(e))return hu(e)},e.exports.__esModule=!0,e.exports.default=e.exports}));n(gu);var mu=Vc,bu=function(e,t,n,r){try{return r?t(He(n)[0],n[1]):t(n)}catch(t){el(e,"throw",t)}},vu=s.Array,yu=ve("iterator"),_u=!1;try{var ku=0,wu={next:function(){return{done:!!ku++}},return:function(){_u=!0}};wu[yu]=function(){return this},Array.from(wu,(function(){throw 2}))}catch(e){}var Eu=!function(e,t){if(!t&&!_u)return!1;var n=!1;try{var r={};r[yu]=function(){return{next:function(){return{done:n=!0}}}},e(r)}catch(e){}return n}((function(e){Array.from(e)}));Ye({target:"Array",stat:!0,forced:Eu},{from:function(e){var t=se(e),n=_t(this),r=arguments.length,a=r>1?arguments[1]:void 0,o=void 0!==a;o&&(a=je(a,r>2?arguments[2]:void 0));var i,s,c,l,u,f,d=Vc(t),p=0;if(!d||this==vu&&Yc(d))for(i=Pt(t),s=n?new this(i):vu(i);i>p;p++)f=o?a(t[p],p):t[p],kn(s,p,f);else for(u=(l=Qc(t,d)).next,s=n?new this:[];!(c=w(u,l)).done;p++)f=o?bu(l,a,[c.value,p],!0):c.value,kn(s,p,f);return s.length=p,s}});var xu=j.Array.from,Su=r((function(e){e.exports=function(e){if(void 0!==Ma&&null!=mu(e)||null!=e["@@iterator"])return xu(e)},e.exports.__esModule=!0,e.exports.default=e.exports}));n(Su);var Au=An("slice"),Cu=ve("species"),Tu=s.Array,Ou=Math.max;Ye({target:"Array",proto:!0,forced:!Au},{slice:function(e,t){var n,r,a,o=N(this),i=Pt(o),s=Ot(e,i),c=Ot(void 0===t?i:t,i);if(_n(o)&&(n=o.constructor,(_t(n)&&(n===Tu||_n(n.prototype))||M(n)&&null===(n=n[Cu]))&&(n=void 0),n===Tu||void 0===n))return Xe(o,s,c);for(r=new(void 0===n?Tu:n)(Ou(c-s,0)),a=0;s<c;s++,a++)s in o&&kn(r,a,o[s]);return r.length=a,r}});var $u=Ts("Array").slice,Ru=Array.prototype,Pu=function(e){var t=e.slice;return e===Ru||F(Ru,e)&&t===Ru.slice?$u:t},Iu=Pu,Lu=r((function(e){e.exports=function(e,t){var n;if(e){if("string"==typeof e)return hu(e,t);var r=Iu(n=Object.prototype.toString.call(e)).call(n,8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?xu(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?hu(e,t):void 0}},e.exports.__esModule=!0,e.exports.default=e.exports}));n(Lu);var Nu=r((function(e){e.exports=function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports}));n(Nu);var Mu=n(r((function(e){e.exports=function(e){return gu(e)||Su(e)||Lu(e)||Nu()},e.exports.__esModule=!0,e.exports.default=e.exports}))),ju=r((function(e){e.exports=function(e){if(pu(e))return e},e.exports.__esModule=!0,e.exports.default=e.exports}));n(ju);var Du=r((function(e){e.exports=function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")},e.exports.__esModule=!0,e.exports.default=e.exports}));n(Du);var Bu=n(r((function(e){e.exports=function(e){return ju(e)||Su(e)||Lu(e)||Du()},e.exports.__esModule=!0,e.exports.default=e.exports}))),Fu=Pu,Hu=j.Object.getOwnPropertySymbols,zu=Te.f,Uu=c((function(){zu(1)}));Ye({target:"Object",stat:!0,forced:!_||Uu,sham:!_},{getOwnPropertyDescriptor:function(e,t){return zu(N(e),t)}});var Wu=r((function(e){var t=j.Object,n=e.exports=function(e,n){return t.getOwnPropertyDescriptor(e,n)};t.getOwnPropertyDescriptor.sham&&(n.sham=!0)})),qu=Wu,Gu=v([].concat),Ku=B("Reflect","ownKeys")||function(e){var t=Dn.f(He(e)),n=qn.f;return n?Gu(t,n(e)):t};Ye({target:"Object",stat:!0,sham:!_},{getOwnPropertyDescriptors:function(e){for(var t,n,r=N(e),a=Te.f,o=Ku(r),i={},s=0;o.length>s;)void 0!==(n=a(r,t=o[s++]))&&kn(i,t,n);return i}});var Zu=j.Object.getOwnPropertyDescriptors,Yu=Ht.f;Ye({target:"Object",stat:!0,forced:Object.defineProperties!==Yu,sham:!_},{defineProperties:Yu});var Xu=r((function(e){var t=j.Object,n=e.exports=function(e,n){return t.defineProperties(e,n)};t.defineProperties.sham&&(n.sham=!0)}));function Vu(e,t){var n=Ws(e);if(Hu){var r=Hu(e);t&&(r=Ys(r).call(r,(function(t){return qu(e,t).enumerable}))),n.push.apply(n,r)}return n}function Ju(e){for(var t=1;t<arguments.length;t++){var n,r,a=null!=arguments[t]?arguments[t]:{};t%2?zs(n=Vu(Object(a),!0)).call(n,(function(t){eo(e,t,a[t])})):Zu?Xu(e,Zu(a)):zs(r=Vu(Object(a))).call(r,(function(t){Cs(e,t,qu(a,t))}))}return e}function Qu(e,t){if(!t.length)return e;var n=[],r=0;return zs(t).call(t,(function(a,o){n.push(Fu(e).call(e,r,a.begin)),n.push(a.replacedText),r=a.begin+a.length,o===t.length-1&&n.push(Fu(e).call(e,r))})),n.join("")}function ef(e,t,n){var r,a=arguments.length>3&&void 0!==arguments[3]&&arguments[3],o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1;if(!t)return e;t.lastIndex=0;for(var i=0,s=[];null!==(r=t.exec(e));){var c={begin:r.index,length:r[0].length};if(a&&r.index===i-o){var l,u=r,f=Bu(u),d=f[0],p=Fu(f).call(f,2);s.push({begin:c.begin+o,length:c.length-o,replacedText:n.apply(void 0,Ns(l=[Fu(d).call(d,o),""]).call(l,Mu(p)))})}else s.push(Ju(Ju({},c),{},{replacedText:n.apply(void 0,Mu(r))}));i=t.lastIndex,t.lastIndex-=o}return t.lastIndex=0,Qu(e,s)}function tf(e){var t=function(){if("undefined"==typeof Reflect||!an)return!1;if(an.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(an(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Qa(e);if(t){var a=Qa(this).constructor;n=an(r,arguments,a)}else n=r.apply(this,arguments);return Ya(this,n)}}var nf=function(e){yn(n,e);var t=tf(n);function n(){return un(this,n),t.apply(this,arguments)}return ln(n,[{key:"toHtml",value:function(e,t,n,r){var a,o;return Ns(a=Ns(o="".concat(t,'<span style="color:')).call(o,n,'">')).call(a,r,"</span>")}},{key:"makeHtml",value:function(e){return ru()?e.replace(this.RULE.reg,this.toHtml):ef(e,this.RULE.reg,this.toHtml,!0,1)}},{key:"rule",value:function(){var e={begin:ru()?"((?<!\\\\))!!":"(^|[^\\\\])!!",end:"!!",content:"(#[0-9a-zA-Z]{3,6}|[a-z]{3,20})[\\s]([\\w\\W]+?)"};return e.reg=new RegExp(e.begin+e.content+e.end,"g"),e}}]),n}(uc);function rf(e){var t=function(){if("undefined"==typeof Reflect||!an)return!1;if(an.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(an(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Qa(e);if(t){var a=Qa(this).constructor;n=an(r,arguments,a)}else n=r.apply(this,arguments);return Ya(this,n)}}eo(nf,"HOOK_NAME","fontColor");var af=function(e){yn(n,e);var t=rf(n);function n(){return un(this,n),t.apply(this,arguments)}return ln(n,[{key:"toHtml",value:function(e,t,n,r){var a,o;return Ns(a=Ns(o="".concat(t,'<span style="background-color:')).call(o,n,'">')).call(a,r,"</span>")}},{key:"makeHtml",value:function(e){return ru()?e.replace(this.RULE.reg,this.toHtml):ef(e,this.RULE.reg,this.toHtml,!0,1)}},{key:"rule",value:function(){var e={begin:ru()?"((?<!\\\\))!!!":"(^|[^\\\\])!!!",end:"!!!",content:"(#[0-9a-zA-Z]{3,6}|[a-z]{3,10})[\\s]([\\w\\W]+?)"};return e.reg=new RegExp(e.begin+e.content+e.end,"g"),e}}]),n}(uc);function of(e){var t=function(){if("undefined"==typeof Reflect||!an)return!1;if(an.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(an(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Qa(e);if(t){var a=Qa(this).constructor;n=an(r,arguments,a)}else n=r.apply(this,arguments);return Ya(this,n)}}eo(af,"HOOK_NAME","bgColor");var sf=function(e){yn(n,e);var t=of(n);function n(){return un(this,n),t.apply(this,arguments)}return ln(n,[{key:"makeHtml",value:function(e){return this.test(e)?e.replace(this.RULE.reg,'$2<span style="font-size:$4px;line-height:1em;">$5</span>$7'):e}},{key:"rule",value:function(){var e={begin:"((^|[^\\\\])(\\!))",end:"(\\!([\\s\\S]|$))",content:"([0-9]{1,2})[\\s]([\\w\\W]*?)"};return e.reg=new RegExp(e.begin+e.content+e.end,"g"),e}}]),n}(uc);function cf(e){var t=function(){if("undefined"==typeof Reflect||!an)return!1;if(an.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(an(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Qa(e);if(t){var a=Qa(this).constructor;n=an(r,arguments,a)}else n=r.apply(this,arguments);return Ya(this,n)}}eo(sf,"HOOK_NAME","fontSize");var lf=function(e){yn(n,e);var t=cf(n);function n(){var e,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{config:void 0},a=r.config;return un(this,n),e=t.call(this,{config:a}),a?(e.needWhitespace=!!a.needWhitespace,e):Ya(e)}return ln(n,[{key:"makeHtml",value:function(e){return this.test(e)?e.replace(this.RULE.reg,"$1<del>$2</del>"):e}},{key:"rule",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{config:void 0},t=e.config,n={};return(n=t.needWhitespace?{begin:"(^|[\\s])\\~T\\~T",end:"\\~T\\~T(?=\\s|$)",content:"([\\w\\W]+?)"}:{begin:"(^|[^\\\\])\\~T\\~T",end:"\\~T\\~T",content:"([\\w\\W]+?)"}).reg=new RegExp(n.begin+n.content+n.end,"g"),n}}]),n}(uc);function uf(e){var t=function(){if("undefined"==typeof Reflect||!an)return!1;if(an.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(an(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Qa(e);if(t){var a=Qa(this).constructor;n=an(r,arguments,a)}else n=r.apply(this,arguments);return Ya(this,n)}}eo(lf,"HOOK_NAME","strikethrough");var ff=function(e){yn(n,e);var t=uf(n);function n(){return un(this,n),t.apply(this,arguments)}return ln(n,[{key:"toHtml",value:function(e,t,n){var r;return Ns(r="".concat(t,"<sup>")).call(r,n,"</sup>")}},{key:"makeHtml",value:function(e){return ru()?e.replace(this.RULE.reg,this.toHtml):ef(e,this.RULE.reg,this.toHtml,!0,1)}},{key:"rule",value:function(){var e={begin:ru()?"((?<!\\\\))\\^":"(^|[^\\\\])\\^",end:"\\^",content:"([\\w\\W]+?)"};return e.reg=new RegExp(e.begin+e.content+e.end,"g"),e}}]),n}(uc);function df(e){var t=function(){if("undefined"==typeof Reflect||!an)return!1;if(an.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(an(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Qa(e);if(t){var a=Qa(this).constructor;n=an(r,arguments,a)}else n=r.apply(this,arguments);return Ya(this,n)}}eo(ff,"HOOK_NAME","sup");var pf=function(e){yn(n,e);var t=df(n);function n(){return un(this,n),t.apply(this,arguments)}return ln(n,[{key:"toHtml",value:function(e,t,n){var r;return Ns(r="".concat(t,"<sub>")).call(r,n,"</sub>")}},{key:"makeHtml",value:function(e){return ru()?e.replace(this.RULE.reg,this.toHtml):ef(e,this.RULE.reg,this.toHtml,!0,1)}},{key:"rule",value:function(){var e={begin:ru()?"((?<!\\\\))\\^\\^":"(^|[^\\\\])\\^\\^",end:"\\^\\^",content:"([\\w\\W]+?)"};return e.reg=new RegExp(e.begin+e.content+e.end,"g"),e}}]),n}(uc);eo(pf,"HOOK_NAME","sub");var hf=r((function(e){e.exports=function(e,t){var n=null==e?null:void 0!==Ma&&mu(e)||e["@@iterator"];if(null!=n){var r,a,o=[],i=!0,s=!1;try{for(n=n.call(e);!(i=(r=n.next()).done)&&(o.push(r.value),!t||o.length!==t);i=!0);}catch(e){s=!0,a=e}finally{try{i||null==n.return||n.return()}finally{if(s)throw a}}return o}},e.exports.__esModule=!0,e.exports.default=e.exports}));n(hf);var gf=n(r((function(e){e.exports=function(e,t){return ju(e)||hf(e,t)||Lu(e,t)||Du()},e.exports.__esModule=!0,e.exports.default=e.exports}))),mf=Ll,bf=r((function(e){var n=function(e){var t=/(?:^|\s)lang(?:uage)?-([\w-]+)(?=\s|$)/i,n=0,r={},a={manual:e.Prism&&e.Prism.manual,disableWorkerMessageHandler:e.Prism&&e.Prism.disableWorkerMessageHandler,util:{encode:function e(t){return t instanceof o?new o(t.type,e(t.content),t.alias):Array.isArray(t)?t.map(e):t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/\u00a0/g," ")},type:function(e){return Object.prototype.toString.call(e).slice(8,-1)},objId:function(e){return e.__id||Object.defineProperty(e,"__id",{value:++n}),e.__id},clone:function e(t,n){var r,o;switch(n=n||{},a.util.type(t)){case"Object":if(o=a.util.objId(t),n[o])return n[o];for(var i in r={},n[o]=r,t)t.hasOwnProperty(i)&&(r[i]=e(t[i],n));return r;case"Array":return o=a.util.objId(t),n[o]?n[o]:(r=[],n[o]=r,t.forEach((function(t,a){r[a]=e(t,n)})),r);default:return t}},getLanguage:function(e){for(;e;){var n=t.exec(e.className);if(n)return n[1].toLowerCase();e=e.parentElement}return"none"},setLanguage:function(e,n){e.className=e.className.replace(RegExp(t,"gi"),""),e.classList.add("language-"+n)},currentScript:function(){if("undefined"==typeof document)return null;if("currentScript"in document)return document.currentScript;try{throw new Error}catch(r){var e=(/at [^(\r\n]*\((.*):[^:]+:[^:]+\)$/i.exec(r.stack)||[])[1];if(e){var t=document.getElementsByTagName("script");for(var n in t)if(t[n].src==e)return t[n]}return null}},isActive:function(e,t,n){for(var r="no-"+t;e;){var a=e.classList;if(a.contains(t))return!0;if(a.contains(r))return!1;e=e.parentElement}return!!n}},languages:{plain:r,plaintext:r,text:r,txt:r,extend:function(e,t){var n=a.util.clone(a.languages[e]);for(var r in t)n[r]=t[r];return n},insertBefore:function(e,t,n,r){var o=(r=r||a.languages)[e],i={};for(var s in o)if(o.hasOwnProperty(s)){if(s==t)for(var c in n)n.hasOwnProperty(c)&&(i[c]=n[c]);n.hasOwnProperty(s)||(i[s]=o[s])}var l=r[e];return r[e]=i,a.languages.DFS(a.languages,(function(t,n){n===l&&t!=e&&(this[t]=i)})),i},DFS:function e(t,n,r,o){o=o||{};var i=a.util.objId;for(var s in t)if(t.hasOwnProperty(s)){n.call(t,s,t[s],r||s);var c=t[s],l=a.util.type(c);"Object"!==l||o[i(c)]?"Array"!==l||o[i(c)]||(o[i(c)]=!0,e(c,n,s,o)):(o[i(c)]=!0,e(c,n,null,o))}}},plugins:{},highlightAll:function(e,t){a.highlightAllUnder(document,e,t)},highlightAllUnder:function(e,t,n){var r={callback:n,container:e,selector:'code[class*="language-"], [class*="language-"] code, code[class*="lang-"], [class*="lang-"] code'};a.hooks.run("before-highlightall",r),r.elements=Array.prototype.slice.apply(r.container.querySelectorAll(r.selector)),a.hooks.run("before-all-elements-highlight",r);for(var o,i=0;o=r.elements[i++];)a.highlightElement(o,!0===t,r.callback)},highlightElement:function(t,n,r){var o=a.util.getLanguage(t),i=a.languages[o];a.util.setLanguage(t,o);var s=t.parentElement;s&&"pre"===s.nodeName.toLowerCase()&&a.util.setLanguage(s,o);var c={element:t,language:o,grammar:i,code:t.textContent};function l(e){c.highlightedCode=e,a.hooks.run("before-insert",c),c.element.innerHTML=c.highlightedCode,a.hooks.run("after-highlight",c),a.hooks.run("complete",c),r&&r.call(c.element)}if(a.hooks.run("before-sanity-check",c),(s=c.element.parentElement)&&"pre"===s.nodeName.toLowerCase()&&!s.hasAttribute("tabindex")&&s.setAttribute("tabindex","0"),!c.code)return a.hooks.run("complete",c),void(r&&r.call(c.element));if(a.hooks.run("before-highlight",c),c.grammar)if(n&&e.Worker){var u=new Worker(a.filename);u.onmessage=function(e){l(e.data)},u.postMessage(JSON.stringify({language:c.language,code:c.code,immediateClose:!0}))}else l(a.highlight(c.code,c.grammar,c.language));else l(a.util.encode(c.code))},highlight:function(e,t,n){var r={code:e,grammar:t,language:n};if(a.hooks.run("before-tokenize",r),!r.grammar)throw new Error('The language "'+r.language+'" has no grammar.');return r.tokens=a.tokenize(r.code,r.grammar),a.hooks.run("after-tokenize",r),o.stringify(a.util.encode(r.tokens),r.language)},tokenize:function(e,t){var n=t.rest;if(n){for(var r in n)t[r]=n[r];delete t.rest}var u=new s;return c(u,u.head,e),function e(t,n,r,s,u,f){for(var d in r)if(r.hasOwnProperty(d)&&r[d]){var p=r[d];p=Array.isArray(p)?p:[p];for(var h=0;h<p.length;++h){if(f&&f.cause==d+","+h)return;var g=p[h],m=g.inside,b=!!g.lookbehind,v=!!g.greedy,y=g.alias;if(v&&!g.pattern.global){var _=g.pattern.toString().match(/[imsuy]*$/)[0];g.pattern=RegExp(g.pattern.source,_+"g")}for(var k=g.pattern||g,w=s.next,E=u;w!==n.tail&&!(f&&E>=f.reach);E+=w.value.length,w=w.next){var x=w.value;if(n.length>t.length)return;if(!(x instanceof o)){var S,A=1;if(v){if(!(S=i(k,E,t,b))||S.index>=t.length)break;var C=S.index,T=S.index+S[0].length,O=E;for(O+=w.value.length;C>=O;)w=w.next,O+=w.value.length;if(O-=w.value.length,E=O,w.value instanceof o)continue;for(var $=w;$!==n.tail&&(O<T||"string"==typeof $.value);$=$.next)A++,O+=$.value.length;A--,x=t.slice(E,O),S.index-=E}else if(!(S=i(k,0,x,b)))continue;C=S.index;var R=S[0],P=x.slice(0,C),I=x.slice(C+R.length),L=E+x.length;f&&L>f.reach&&(f.reach=L);var N=w.prev;P&&(N=c(n,N,P),E+=P.length),l(n,N,A);var M=new o(d,m?a.tokenize(R,m):R,y,R);if(w=c(n,N,M),I&&c(n,w,I),A>1){var j={cause:d+","+h,reach:L};e(t,n,r,w.prev,E,j),f&&j.reach>f.reach&&(f.reach=j.reach)}}}}}}(e,u,t,u.head,0),function(e){var t=[],n=e.head.next;for(;n!==e.tail;)t.push(n.value),n=n.next;return t}(u)},hooks:{all:{},add:function(e,t){var n=a.hooks.all;n[e]=n[e]||[],n[e].push(t)},run:function(e,t){var n=a.hooks.all[e];if(n&&n.length)for(var r,o=0;r=n[o++];)r(t)}},Token:o};function o(e,t,n,r){this.type=e,this.content=t,this.alias=n,this.length=0|(r||"").length}function i(e,t,n,r){e.lastIndex=t;var a=e.exec(n);if(a&&r&&a[1]){var o=a[1].length;a.index+=o,a[0]=a[0].slice(o)}return a}function s(){var e={value:null,prev:null,next:null},t={value:null,prev:e,next:null};e.next=t,this.head=e,this.tail=t,this.length=0}function c(e,t,n){var r=t.next,a={value:n,prev:t,next:r};return t.next=a,r.prev=a,e.length++,a}function l(e,t,n){for(var r=t.next,a=0;a<n&&r!==e.tail;a++)r=r.next;t.next=r,r.prev=t,e.length-=a}if(e.Prism=a,o.stringify=function e(t,n){if("string"==typeof t)return t;if(Array.isArray(t)){var r="";return t.forEach((function(t){r+=e(t,n)})),r}var o={type:t.type,content:e(t.content,n),tag:"span",classes:["token",t.type],attributes:{},language:n},i=t.alias;i&&(Array.isArray(i)?Array.prototype.push.apply(o.classes,i):o.classes.push(i)),a.hooks.run("wrap",o);var s="";for(var c in o.attributes)s+=" "+c+'="'+(o.attributes[c]||"").replace(/"/g,"&quot;")+'"';return"<"+o.tag+' class="'+o.classes.join(" ")+'"'+s+">"+o.content+"</"+o.tag+">"},!e.document)return e.addEventListener?(a.disableWorkerMessageHandler||e.addEventListener("message",(function(t){var n=JSON.parse(t.data),r=n.language,o=n.code,i=n.immediateClose;e.postMessage(a.highlight(o,a.languages[r],r)),i&&e.close()}),!1),a):a;var u=a.util.currentScript();function f(){a.manual||a.highlightAll()}if(u&&(a.filename=u.src,u.hasAttribute("data-manual")&&(a.manual=!0)),!a.manual){var d=document.readyState;"loading"===d||"interactive"===d&&u&&u.defer?document.addEventListener("DOMContentLoaded",f):window.requestAnimationFrame?window.requestAnimationFrame(f):window.setTimeout(f,16)}return a}("undefined"!=typeof window?window:"undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope?self:{});e.exports&&(e.exports=n),void 0!==t&&(t.Prism=n)}));Prism.languages.clike={comment:[{pattern:/(^|[^\\])\/\*[\s\S]*?(?:\*\/|$)/,lookbehind:!0,greedy:!0},{pattern:/(^|[^\\:])\/\/.*/,lookbehind:!0,greedy:!0}],string:{pattern:/(["'])(?:\\(?:\r\n|[\s\S])|(?!\1)[^\\\r\n])*\1/,greedy:!0},"class-name":{pattern:/(\b(?:class|extends|implements|instanceof|interface|new|trait)\s+|\bcatch\s+\()[\w.\\]+/i,lookbehind:!0,inside:{punctuation:/[.\\]/}},keyword:/\b(?:break|catch|continue|do|else|finally|for|function|if|in|instanceof|new|null|return|throw|try|while)\b/,boolean:/\b(?:false|true)\b/,function:/\b\w+(?=\()/,number:/\b0x[\da-f]+\b|(?:\b\d+(?:\.\d*)?|\B\.\d+)(?:e[+-]?\d+)?/i,operator:/[<>]=?|[!=]=?=?|--?|\+\+?|&&?|\|\|?|[?*/~^%]/,punctuation:/[{}[\];(),.:]/},Prism.languages.c=Prism.languages.extend("clike",{comment:{pattern:/\/\/(?:[^\r\n\\]|\\(?:\r\n?|\n|(?![\r\n])))*|\/\*[\s\S]*?(?:\*\/|$)/,greedy:!0},string:{pattern:/"(?:\\(?:\r\n|[\s\S])|[^"\\\r\n])*"/,greedy:!0},"class-name":{pattern:/(\b(?:enum|struct)\s+(?:__attribute__\s*\(\([\s\S]*?\)\)\s*)?)\w+|\b[a-z]\w*_t\b/,lookbehind:!0},keyword:/\b(?:_Alignas|_Alignof|_Atomic|_Bool|_Complex|_Generic|_Imaginary|_Noreturn|_Static_assert|_Thread_local|__attribute__|asm|auto|break|case|char|const|continue|default|do|double|else|enum|extern|float|for|goto|if|inline|int|long|register|return|short|signed|sizeof|static|struct|switch|typedef|typeof|union|unsigned|void|volatile|while)\b/,function:/\b[a-z_]\w*(?=\s*\()/i,number:/(?:\b0x(?:[\da-f]+(?:\.[\da-f]*)?|\.[\da-f]+)(?:p[+-]?\d+)?|(?:\b\d+(?:\.\d*)?|\B\.\d+)(?:e[+-]?\d+)?)[ful]{0,4}/i,operator:/>>=?|<<=?|->|([-+&|:])\1|[?:~]|[-+*/%&|^!=<>]=?/}),Prism.languages.insertBefore("c","string",{char:{pattern:/'(?:\\(?:\r\n|[\s\S])|[^'\\\r\n]){0,32}'/,greedy:!0}}),Prism.languages.insertBefore("c","string",{macro:{pattern:/(^[\t ]*)#\s*[a-z](?:[^\r\n\\/]|\/(?!\*)|\/\*(?:[^*]|\*(?!\/))*\*\/|\\(?:\r\n|[\s\S]))*/im,lookbehind:!0,greedy:!0,alias:"property",inside:{string:[{pattern:/^(#\s*include\s*)<[^>]+>/,lookbehind:!0},Prism.languages.c.string],char:Prism.languages.c.char,comment:Prism.languages.c.comment,"macro-name":[{pattern:/(^#\s*define\s+)\w+\b(?!\()/i,lookbehind:!0},{pattern:/(^#\s*define\s+)\w+\b(?=\()/i,lookbehind:!0,alias:"function"}],directive:{pattern:/^(#\s*)[a-z]+/,lookbehind:!0,alias:"keyword"},"directive-hash":/^#/,punctuation:/##|\\(?=[\r\n])/,expression:{pattern:/\S[\s\S]*/,inside:Prism.languages.c}}}}),Prism.languages.insertBefore("c","function",{constant:/\b(?:EOF|NULL|SEEK_CUR|SEEK_END|SEEK_SET|__DATE__|__FILE__|__LINE__|__TIMESTAMP__|__TIME__|__func__|stderr|stdin|stdout)\b/}),delete Prism.languages.c.boolean,function(e){function t(e,t){return e.replace(/<<(\d+)>>/g,(function(e,n){return"(?:"+t[+n]+")"}))}function n(e,n,r){return RegExp(t(e,n),r||"")}function r(e,t){for(var n=0;n<t;n++)e=e.replace(/<<self>>/g,(function(){return"(?:"+e+")"}));return e.replace(/<<self>>/g,"[^\\s\\S]")}var a="bool byte char decimal double dynamic float int long object sbyte short string uint ulong ushort var void",o="class enum interface record struct",i="add alias and ascending async await by descending from(?=\\s*(?:\\w|$)) get global group into init(?=\\s*;) join let nameof not notnull on or orderby partial remove select set unmanaged value when where with(?=\\s*{)",s="abstract as base break case catch checked const continue default delegate do else event explicit extern finally fixed for foreach goto if implicit in internal is lock namespace new null operator out override params private protected public readonly ref return sealed sizeof stackalloc static switch this throw try typeof unchecked unsafe using virtual volatile while yield";function c(e){return"\\b(?:"+e.trim().replace(/ /g,"|")+")\\b"}var l=c(o),u=RegExp(c(a+" "+o+" "+i+" "+s)),f=c(o+" "+i+" "+s),d=c(a+" "+o+" "+s),p=r(/<(?:[^<>;=+\-*/%&|^]|<<self>>)*>/.source,2),h=r(/\((?:[^()]|<<self>>)*\)/.source,2),g=/@?\b[A-Za-z_]\w*\b/.source,m=t(/<<0>>(?:\s*<<1>>)?/.source,[g,p]),b=t(/(?!<<0>>)<<1>>(?:\s*\.\s*<<1>>)*/.source,[f,m]),v=/\[\s*(?:,\s*)*\]/.source,y=t(/<<0>>(?:\s*(?:\?\s*)?<<1>>)*(?:\s*\?)?/.source,[b,v]),_=t(/[^,()<>[\];=+\-*/%&|^]|<<0>>|<<1>>|<<2>>/.source,[p,h,v]),k=t(/\(<<0>>+(?:,<<0>>+)+\)/.source,[_]),w=t(/(?:<<0>>|<<1>>)(?:\s*(?:\?\s*)?<<2>>)*(?:\s*\?)?/.source,[k,b,v]),E={keyword:u,punctuation:/[<>()?,.:[\]]/},x=/'(?:[^\r\n'\\]|\\.|\\[Uux][\da-fA-F]{1,8})'/.source,S=/"(?:\\.|[^\\"\r\n])*"/.source,A=/@"(?:""|\\[\s\S]|[^\\"])*"(?!")/.source;e.languages.csharp=e.languages.extend("clike",{string:[{pattern:n(/(^|[^$\\])<<0>>/.source,[A]),lookbehind:!0,greedy:!0},{pattern:n(/(^|[^@$\\])<<0>>/.source,[S]),lookbehind:!0,greedy:!0}],"class-name":[{pattern:n(/(\busing\s+static\s+)<<0>>(?=\s*;)/.source,[b]),lookbehind:!0,inside:E},{pattern:n(/(\busing\s+<<0>>\s*=\s*)<<1>>(?=\s*;)/.source,[g,w]),lookbehind:!0,inside:E},{pattern:n(/(\busing\s+)<<0>>(?=\s*=)/.source,[g]),lookbehind:!0},{pattern:n(/(\b<<0>>\s+)<<1>>/.source,[l,m]),lookbehind:!0,inside:E},{pattern:n(/(\bcatch\s*\(\s*)<<0>>/.source,[b]),lookbehind:!0,inside:E},{pattern:n(/(\bwhere\s+)<<0>>/.source,[g]),lookbehind:!0},{pattern:n(/(\b(?:is(?:\s+not)?|as)\s+)<<0>>/.source,[y]),lookbehind:!0,inside:E},{pattern:n(/\b<<0>>(?=\s+(?!<<1>>|with\s*\{)<<2>>(?:\s*[=,;:{)\]]|\s+(?:in|when)\b))/.source,[w,d,g]),inside:E}],keyword:u,number:/(?:\b0(?:x[\da-f_]*[\da-f]|b[01_]*[01])|(?:\B\.\d+(?:_+\d+)*|\b\d+(?:_+\d+)*(?:\.\d+(?:_+\d+)*)?)(?:e[-+]?\d+(?:_+\d+)*)?)(?:[dflmu]|lu|ul)?\b/i,operator:/>>=?|<<=?|[-=]>|([-+&|])\1|~|\?\?=?|[-+*/%&|^!=<>]=?/,punctuation:/\?\.?|::|[{}[\];(),.:]/}),e.languages.insertBefore("csharp","number",{range:{pattern:/\.\./,alias:"operator"}}),e.languages.insertBefore("csharp","punctuation",{"named-parameter":{pattern:n(/([(,]\s*)<<0>>(?=\s*:)/.source,[g]),lookbehind:!0,alias:"punctuation"}}),e.languages.insertBefore("csharp","class-name",{namespace:{pattern:n(/(\b(?:namespace|using)\s+)<<0>>(?:\s*\.\s*<<0>>)*(?=\s*[;{])/.source,[g]),lookbehind:!0,inside:{punctuation:/\./}},"type-expression":{pattern:n(/(\b(?:default|sizeof|typeof)\s*\(\s*(?!\s))(?:[^()\s]|\s(?!\s)|<<0>>)*(?=\s*\))/.source,[h]),lookbehind:!0,alias:"class-name",inside:E},"return-type":{pattern:n(/<<0>>(?=\s+(?:<<1>>\s*(?:=>|[({]|\.\s*this\s*\[)|this\s*\[))/.source,[w,b]),inside:E,alias:"class-name"},"constructor-invocation":{pattern:n(/(\bnew\s+)<<0>>(?=\s*[[({])/.source,[w]),lookbehind:!0,inside:E,alias:"class-name"},"generic-method":{pattern:n(/<<0>>\s*<<1>>(?=\s*\()/.source,[g,p]),inside:{function:n(/^<<0>>/.source,[g]),generic:{pattern:RegExp(p),alias:"class-name",inside:E}}},"type-list":{pattern:n(/\b((?:<<0>>\s+<<1>>|record\s+<<1>>\s*<<5>>|where\s+<<2>>)\s*:\s*)(?:<<3>>|<<4>>|<<1>>\s*<<5>>|<<6>>)(?:\s*,\s*(?:<<3>>|<<4>>|<<6>>))*(?=\s*(?:where|[{;]|=>|$))/.source,[l,m,g,w,u.source,h,/\bnew\s*\(\s*\)/.source]),lookbehind:!0,inside:{"record-arguments":{pattern:n(/(^(?!new\s*\()<<0>>\s*)<<1>>/.source,[m,h]),lookbehind:!0,greedy:!0,inside:e.languages.csharp},keyword:u,"class-name":{pattern:RegExp(w),greedy:!0,inside:E},punctuation:/[,()]/}},preprocessor:{pattern:/(^[\t ]*)#.*/m,lookbehind:!0,alias:"property",inside:{directive:{pattern:/(#)\b(?:define|elif|else|endif|endregion|error|if|line|nullable|pragma|region|undef|warning)\b/,lookbehind:!0,alias:"keyword"}}}});var C=S+"|"+x,T=t(/\/(?![*/])|\/\/[^\r\n]*[\r\n]|\/\*(?:[^*]|\*(?!\/))*\*\/|<<0>>/.source,[C]),O=r(t(/[^"'/()]|<<0>>|\(<<self>>*\)/.source,[T]),2),$=/\b(?:assembly|event|field|method|module|param|property|return|type)\b/.source,R=t(/<<0>>(?:\s*\(<<1>>*\))?/.source,[b,O]);e.languages.insertBefore("csharp","class-name",{attribute:{pattern:n(/((?:^|[^\s\w>)?])\s*\[\s*)(?:<<0>>\s*:\s*)?<<1>>(?:\s*,\s*<<1>>)*(?=\s*\])/.source,[$,R]),lookbehind:!0,greedy:!0,inside:{target:{pattern:n(/^<<0>>(?=\s*:)/.source,[$]),alias:"keyword"},"attribute-arguments":{pattern:n(/\(<<0>>*\)/.source,[O]),inside:e.languages.csharp},"class-name":{pattern:RegExp(b),inside:{punctuation:/\./}},punctuation:/[:,]/}}});var P=/:[^}\r\n]+/.source,I=r(t(/[^"'/()]|<<0>>|\(<<self>>*\)/.source,[T]),2),L=t(/\{(?!\{)(?:(?![}:])<<0>>)*<<1>>?\}/.source,[I,P]),N=r(t(/[^"'/()]|\/(?!\*)|\/\*(?:[^*]|\*(?!\/))*\*\/|<<0>>|\(<<self>>*\)/.source,[C]),2),M=t(/\{(?!\{)(?:(?![}:])<<0>>)*<<1>>?\}/.source,[N,P]);function j(t,r){return{interpolation:{pattern:n(/((?:^|[^{])(?:\{\{)*)<<0>>/.source,[t]),lookbehind:!0,inside:{"format-string":{pattern:n(/(^\{(?:(?![}:])<<0>>)*)<<1>>(?=\}$)/.source,[r,P]),lookbehind:!0,inside:{punctuation:/^:/}},punctuation:/^\{|\}$/,expression:{pattern:/[\s\S]+/,alias:"language-csharp",inside:e.languages.csharp}}},string:/[\s\S]+/}}e.languages.insertBefore("csharp","string",{"interpolation-string":[{pattern:n(/(^|[^\\])(?:\$@|@\$)"(?:""|\\[\s\S]|\{\{|<<0>>|[^\\{"])*"/.source,[L]),lookbehind:!0,greedy:!0,inside:j(L,I)},{pattern:n(/(^|[^@\\])\$"(?:\\.|\{\{|<<0>>|[^\\"{])*"/.source,[M]),lookbehind:!0,greedy:!0,inside:j(M,N)}],char:{pattern:RegExp(x),greedy:!0}}),e.languages.dotnet=e.languages.cs=e.languages.csharp}(Prism),function(e){var t=/\b(?:alignas|alignof|asm|auto|bool|break|case|catch|char|char16_t|char32_t|char8_t|class|co_await|co_return|co_yield|compl|concept|const|const_cast|consteval|constexpr|constinit|continue|decltype|default|delete|do|double|dynamic_cast|else|enum|explicit|export|extern|final|float|for|friend|goto|if|import|inline|int|int16_t|int32_t|int64_t|int8_t|long|module|mutable|namespace|new|noexcept|nullptr|operator|override|private|protected|public|register|reinterpret_cast|requires|return|short|signed|sizeof|static|static_assert|static_cast|struct|switch|template|this|thread_local|throw|try|typedef|typeid|typename|uint16_t|uint32_t|uint64_t|uint8_t|union|unsigned|using|virtual|void|volatile|wchar_t|while)\b/,n=/\b(?!<keyword>)\w+(?:\s*\.\s*\w+)*\b/.source.replace(/<keyword>/g,(function(){return t.source}));e.languages.cpp=e.languages.extend("c",{"class-name":[{pattern:RegExp(/(\b(?:class|concept|enum|struct|typename)\s+)(?!<keyword>)\w+/.source.replace(/<keyword>/g,(function(){return t.source}))),lookbehind:!0},/\b[A-Z]\w*(?=\s*::\s*\w+\s*\()/,/\b[A-Z_]\w*(?=\s*::\s*~\w+\s*\()/i,/\b\w+(?=\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>\s*::\s*\w+\s*\()/],keyword:t,number:{pattern:/(?:\b0b[01']+|\b0x(?:[\da-f']+(?:\.[\da-f']*)?|\.[\da-f']+)(?:p[+-]?[\d']+)?|(?:\b[\d']+(?:\.[\d']*)?|\B\.[\d']+)(?:e[+-]?[\d']+)?)[ful]{0,4}/i,greedy:!0},operator:/>>=?|<<=?|->|--|\+\+|&&|\|\||[?:~]|<=>|[-+*/%&|^!=<>]=?|\b(?:and|and_eq|bitand|bitor|not|not_eq|or|or_eq|xor|xor_eq)\b/,boolean:/\b(?:false|true)\b/}),e.languages.insertBefore("cpp","string",{module:{pattern:RegExp(/(\b(?:import|module)\s+)/.source+"(?:"+/"(?:\\(?:\r\n|[\s\S])|[^"\\\r\n])*"|<[^<>\r\n]*>/.source+"|"+/<mod-name>(?:\s*:\s*<mod-name>)?|:\s*<mod-name>/.source.replace(/<mod-name>/g,(function(){return n}))+")"),lookbehind:!0,greedy:!0,inside:{string:/^[<"][\s\S]+/,operator:/:/,punctuation:/\./}},"raw-string":{pattern:/R"([^()\\ ]{0,16})\([\s\S]*?\)\1"/,alias:"string",greedy:!0}}),e.languages.insertBefore("cpp","keyword",{"generic-function":{pattern:/\b(?!operator\b)[a-z_]\w*\s*<(?:[^<>]|<[^<>]*>)*>(?=\s*\()/i,inside:{function:/^\w+/,generic:{pattern:/<[\s\S]+/,alias:"class-name",inside:e.languages.cpp}}}}),e.languages.insertBefore("cpp","operator",{"double-colon":{pattern:/::/,alias:"punctuation"}}),e.languages.insertBefore("cpp","class-name",{"base-clause":{pattern:/(\b(?:class|struct)\s+\w+\s*:\s*)[^;{}"'\s]+(?:\s+[^;{}"'\s]+)*(?=\s*[;{])/,lookbehind:!0,greedy:!0,inside:e.languages.extend("cpp",{})}}),e.languages.insertBefore("inside","double-colon",{"class-name":/\b[a-z_]\w*\b(?!\s*::)/i},e.languages.cpp["base-clause"])}(Prism),Prism.languages.markup={comment:{pattern:/<!--(?:(?!<!--)[\s\S])*?-->/,greedy:!0},prolog:{pattern:/<\?[\s\S]+?\?>/,greedy:!0},doctype:{pattern:/<!DOCTYPE(?:[^>"'[\]]|"[^"]*"|'[^']*')+(?:\[(?:[^<"'\]]|"[^"]*"|'[^']*'|<(?!!--)|<!--(?:[^-]|-(?!->))*-->)*\]\s*)?>/i,greedy:!0,inside:{"internal-subset":{pattern:/(^[^\[]*\[)[\s\S]+(?=\]>$)/,lookbehind:!0,greedy:!0,inside:null},string:{pattern:/"[^"]*"|'[^']*'/,greedy:!0},punctuation:/^<!|>$|[[\]]/,"doctype-tag":/^DOCTYPE/i,name:/[^\s<>'"]+/}},cdata:{pattern:/<!\[CDATA\[[\s\S]*?\]\]>/i,greedy:!0},tag:{pattern:/<\/?(?!\d)[^\s>\/=$<%]+(?:\s(?:\s*[^\s>\/=]+(?:\s*=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+(?=[\s>]))|(?=[\s/>])))+)?\s*\/?>/,greedy:!0,inside:{tag:{pattern:/^<\/?[^\s>\/]+/,inside:{punctuation:/^<\/?/,namespace:/^[^\s>\/:]+:/}},"special-attr":[],"attr-value":{pattern:/=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+)/,inside:{punctuation:[{pattern:/^=/,alias:"attr-equals"},/"|'/]}},punctuation:/\/?>/,"attr-name":{pattern:/[^\s>\/]+/,inside:{namespace:/^[^\s>\/:]+:/}}}},entity:[{pattern:/&[\da-z]{1,8};/i,alias:"named-entity"},/&#x?[\da-f]{1,8};/i]},Prism.languages.markup.tag.inside["attr-value"].inside.entity=Prism.languages.markup.entity,Prism.languages.markup.doctype.inside["internal-subset"].inside=Prism.languages.markup,Prism.hooks.add("wrap",(function(e){"entity"===e.type&&(e.attributes.title=e.content.replace(/&amp;/,"&"))})),Object.defineProperty(Prism.languages.markup.tag,"addInlined",{value:function(e,t){var n={};n["language-"+t]={pattern:/(^<!\[CDATA\[)[\s\S]+?(?=\]\]>$)/i,lookbehind:!0,inside:Prism.languages[t]},n.cdata=/^<!\[CDATA\[|\]\]>$/i;var r={"included-cdata":{pattern:/<!\[CDATA\[[\s\S]*?\]\]>/i,inside:n}};r["language-"+t]={pattern:/[\s\S]+/,inside:Prism.languages[t]};var a={};a[e]={pattern:RegExp(/(<__[^>]*>)(?:<!\[CDATA\[(?:[^\]]|\](?!\]>))*\]\]>|(?!<!\[CDATA\[)[\s\S])*?(?=<\/__>)/.source.replace(/__/g,(function(){return e})),"i"),lookbehind:!0,greedy:!0,inside:r},Prism.languages.insertBefore("markup","cdata",a)}}),Object.defineProperty(Prism.languages.markup.tag,"addAttribute",{value:function(e,t){Prism.languages.markup.tag.inside["special-attr"].push({pattern:RegExp(/(^|["'\s])/.source+"(?:"+e+")"+/\s*=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+(?=[\s>]))/.source,"i"),lookbehind:!0,inside:{"attr-name":/^[^\s=]+/,"attr-value":{pattern:/=[\s\S]+/,inside:{value:{pattern:/(^=\s*(["']|(?!["'])))\S[\s\S]*(?=\2$)/,lookbehind:!0,alias:[t,"language-"+t],inside:Prism.languages[t]},punctuation:[{pattern:/^=/,alias:"attr-equals"},/"|'/]}}}})}}),Prism.languages.html=Prism.languages.markup,Prism.languages.mathml=Prism.languages.markup,Prism.languages.svg=Prism.languages.markup,Prism.languages.xml=Prism.languages.extend("markup",{}),Prism.languages.ssml=Prism.languages.xml,Prism.languages.atom=Prism.languages.xml,Prism.languages.rss=Prism.languages.xml,function(e){var t=/(?:"(?:\\(?:\r\n|[\s\S])|[^"\\\r\n])*"|'(?:\\(?:\r\n|[\s\S])|[^'\\\r\n])*')/;e.languages.css={comment:/\/\*[\s\S]*?\*\//,atrule:{pattern:/@[\w-](?:[^;{\s]|\s+(?![\s{]))*(?:;|(?=\s*\{))/,inside:{rule:/^@[\w-]+/,"selector-function-argument":{pattern:/(\bselector\s*\(\s*(?![\s)]))(?:[^()\s]|\s+(?![\s)])|\((?:[^()]|\([^()]*\))*\))+(?=\s*\))/,lookbehind:!0,alias:"selector"},keyword:{pattern:/(^|[^\w-])(?:and|not|only|or)(?![\w-])/,lookbehind:!0}}},url:{pattern:RegExp("\\burl\\((?:"+t.source+"|"+/(?:[^\\\r\n()"']|\\[\s\S])*/.source+")\\)","i"),greedy:!0,inside:{function:/^url/i,punctuation:/^\(|\)$/,string:{pattern:RegExp("^"+t.source+"$"),alias:"url"}}},selector:{pattern:RegExp("(^|[{}\\s])[^{}\\s](?:[^{};\"'\\s]|\\s+(?![\\s{])|"+t.source+")*(?=\\s*\\{)"),lookbehind:!0},string:{pattern:t,greedy:!0},property:{pattern:/(^|[^-\w\xA0-\uFFFF])(?!\s)[-_a-z\xA0-\uFFFF](?:(?!\s)[-\w\xA0-\uFFFF])*(?=\s*:)/i,lookbehind:!0},important:/!important\b/i,function:{pattern:/(^|[^-a-z0-9])[-a-z0-9]+(?=\()/i,lookbehind:!0},punctuation:/[(){};:,]/},e.languages.css.atrule.inside.rest=e.languages.css;var n=e.languages.markup;n&&(n.tag.addInlined("style","css"),n.tag.addAttribute("style","css"))}(Prism),function(e){var t=[/\b(?:async|sync|yield)\*/,/\b(?:abstract|assert|async|await|break|case|catch|class|const|continue|covariant|default|deferred|do|dynamic|else|enum|export|extends|extension|external|factory|final|finally|for|get|hide|if|implements|import|in|interface|library|mixin|new|null|on|operator|part|rethrow|return|set|show|static|super|switch|sync|this|throw|try|typedef|var|void|while|with|yield)\b/],n=/(^|[^\w.])(?:[a-z]\w*\s*\.\s*)*(?:[A-Z]\w*\s*\.\s*)*/.source,r={pattern:RegExp(n+/[A-Z](?:[\d_A-Z]*[a-z]\w*)?\b/.source),lookbehind:!0,inside:{namespace:{pattern:/^[a-z]\w*(?:\s*\.\s*[a-z]\w*)*(?:\s*\.)?/,inside:{punctuation:/\./}}}};e.languages.dart=e.languages.extend("clike",{"class-name":[r,{pattern:RegExp(n+/[A-Z]\w*(?=\s+\w+\s*[;,=()])/.source),lookbehind:!0,inside:r.inside}],keyword:t,operator:/\bis!|\b(?:as|is)\b|\+\+|--|&&|\|\||<<=?|>>=?|~(?:\/=?)?|[+\-*\/%&^|=!<>]=?|\?/}),e.languages.insertBefore("dart","string",{"string-literal":{pattern:/r?(?:("""|''')[\s\S]*?\1|(["'])(?:\\.|(?!\2)[^\\\r\n])*\2(?!\2))/,greedy:!0,inside:{interpolation:{pattern:/((?:^|[^\\])(?:\\{2})*)\$(?:\w+|\{(?:[^{}]|\{[^{}]*\})*\})/,lookbehind:!0,inside:{punctuation:/^\$\{?|\}$/,expression:{pattern:/[\s\S]+/,inside:e.languages.dart}}},string:/[\s\S]+/}},string:void 0}),e.languages.insertBefore("dart","class-name",{metadata:{pattern:/@\w+/,alias:"function"}}),e.languages.insertBefore("dart","class-name",{generics:{pattern:/<(?:[\w\s,.&?]|<(?:[\w\s,.&?]|<(?:[\w\s,.&?]|<[\w\s,.&?]*>)*>)*>)*>/,inside:{"class-name":r,keyword:t,punctuation:/[<>(),.:]/,operator:/[?&|]/}}})}(Prism),function(e){e.languages.diff={coord:[/^(?:\*{3}|-{3}|\+{3}).*$/m,/^@@.*@@$/m,/^\d.*$/m]};var t={"deleted-sign":"-","deleted-arrow":"<","inserted-sign":"+","inserted-arrow":">",unchanged:" ",diff:"!"};Object.keys(t).forEach((function(n){var r=t[n],a=[];/^\w+$/.test(n)||a.push(/\w+/.exec(n)[0]),"diff"===n&&a.push("bold"),e.languages.diff[n]={pattern:RegExp("^(?:["+r+"].*(?:\r\n?|\n|(?![\\s\\S])))+","m"),alias:a,inside:{line:{pattern:/(.)(?=[\s\S]).*(?:\r\n?|\n)?/,lookbehind:!0},prefix:{pattern:/[\s\S]/,alias:/\w+/.exec(n)[0]}}}})),Object.defineProperty(e.languages.diff,"PREFIXES",{value:t})}(Prism),function(e){var t=/\\[\r\n](?:\s|\\[\r\n]|#.*(?!.))*(?![\s#]|\\[\r\n])/.source,n=/(?:[ \t]+(?![ \t])(?:<SP_BS>)?|<SP_BS>)/.source.replace(/<SP_BS>/g,(function(){return t})),r=/"(?:[^"\\\r\n]|\\(?:\r\n|[\s\S]))*"|'(?:[^'\\\r\n]|\\(?:\r\n|[\s\S]))*'/.source,a=/--[\w-]+=(?:<STR>|(?!["'])(?:[^\s\\]|\\.)+)/.source.replace(/<STR>/g,(function(){return r})),o={pattern:RegExp(r),greedy:!0},i={pattern:/(^[ \t]*)#.*/m,lookbehind:!0,greedy:!0};function s(e,t){return e=e.replace(/<OPT>/g,(function(){return a})).replace(/<SP>/g,(function(){return n})),RegExp(e,t)}e.languages.docker={instruction:{pattern:/(^[ \t]*)(?:ADD|ARG|CMD|COPY|ENTRYPOINT|ENV|EXPOSE|FROM|HEALTHCHECK|LABEL|MAINTAINER|ONBUILD|RUN|SHELL|STOPSIGNAL|USER|VOLUME|WORKDIR)(?=\s)(?:\\.|[^\r\n\\])*(?:\\$(?:\s|#.*$)*(?![\s#])(?:\\.|[^\r\n\\])*)*/im,lookbehind:!0,greedy:!0,inside:{options:{pattern:s(/(^(?:ONBUILD<SP>)?\w+<SP>)<OPT>(?:<SP><OPT>)*/.source,"i"),lookbehind:!0,greedy:!0,inside:{property:{pattern:/(^|\s)--[\w-]+/,lookbehind:!0},string:[o,{pattern:/(=)(?!["'])(?:[^\s\\]|\\.)+/,lookbehind:!0}],operator:/\\$/m,punctuation:/=/}},keyword:[{pattern:s(/(^(?:ONBUILD<SP>)?HEALTHCHECK<SP>(?:<OPT><SP>)*)(?:CMD|NONE)\b/.source,"i"),lookbehind:!0,greedy:!0},{pattern:s(/(^(?:ONBUILD<SP>)?FROM<SP>(?:<OPT><SP>)*(?!--)[^ \t\\]+<SP>)AS/.source,"i"),lookbehind:!0,greedy:!0},{pattern:s(/(^ONBUILD<SP>)\w+/.source,"i"),lookbehind:!0,greedy:!0},{pattern:/^\w+/,greedy:!0}],comment:i,string:o,variable:/\$(?:\w+|\{[^{}"'\\]*\})/,operator:/\\$/m}},comment:i},e.languages.dockerfile=e.languages.docker}(Prism),Prism.languages.git={comment:/^#.*/m,deleted:/^[-–].*/m,inserted:/^\+.*/m,string:/("|')(?:\\.|(?!\1)[^\\\r\n])*\1/,command:{pattern:/^.*\$ git .*$/m,inside:{parameter:/\s--?\w+/}},coord:/^@@.*@@$/m,"commit-sha1":/^commit \w{40}$/m},Prism.languages.glsl=Prism.languages.extend("c",{keyword:/\b(?:active|asm|atomic_uint|attribute|[ibdu]?vec[234]|bool|break|buffer|case|cast|centroid|class|coherent|common|const|continue|d?mat[234](?:x[234])?|default|discard|do|double|else|enum|extern|external|false|filter|fixed|flat|float|for|fvec[234]|goto|half|highp|hvec[234]|[iu]?sampler2DMS(?:Array)?|[iu]?sampler2DRect|[iu]?samplerBuffer|[iu]?samplerCube|[iu]?samplerCubeArray|[iu]?sampler[123]D|[iu]?sampler[12]DArray|[iu]?image2DMS(?:Array)?|[iu]?image2DRect|[iu]?imageBuffer|[iu]?imageCube|[iu]?imageCubeArray|[iu]?image[123]D|[iu]?image[12]DArray|if|in|inline|inout|input|int|interface|invariant|layout|long|lowp|mediump|namespace|noinline|noperspective|out|output|partition|patch|precise|precision|public|readonly|resource|restrict|return|sample|sampler[12]DArrayShadow|sampler[12]DShadow|sampler2DRectShadow|sampler3DRect|samplerCubeArrayShadow|samplerCubeShadow|shared|short|sizeof|smooth|static|struct|subroutine|superp|switch|template|this|true|typedef|uint|uniform|union|unsigned|using|varying|void|volatile|while|writeonly)\b/}),Prism.languages.go=Prism.languages.extend("clike",{string:{pattern:/(^|[^\\])"(?:\\.|[^"\\\r\n])*"|`[^`]*`/,lookbehind:!0,greedy:!0},keyword:/\b(?:break|case|chan|const|continue|default|defer|else|fallthrough|for|func|go(?:to)?|if|import|interface|map|package|range|return|select|struct|switch|type|var)\b/,boolean:/\b(?:_|false|iota|nil|true)\b/,number:[/\b0(?:b[01_]+|o[0-7_]+)i?\b/i,/\b0x(?:[a-f\d_]+(?:\.[a-f\d_]*)?|\.[a-f\d_]+)(?:p[+-]?\d+(?:_\d+)*)?i?(?!\w)/i,/(?:\b\d[\d_]*(?:\.[\d_]*)?|\B\.\d[\d_]*)(?:e[+-]?[\d_]+)?i?(?!\w)/i],operator:/[*\/%^!=]=?|\+[=+]?|-[=-]?|\|[=|]?|&(?:=|&|\^=?)?|>(?:>=?|=)?|<(?:<=?|=|-)?|:=|\.\.\./,builtin:/\b(?:append|bool|byte|cap|close|complex|complex(?:64|128)|copy|delete|error|float(?:32|64)|u?int(?:8|16|32|64)?|imag|len|make|new|panic|print(?:ln)?|real|recover|rune|string|uintptr)\b/}),Prism.languages.insertBefore("go","string",{char:{pattern:/'(?:\\.|[^'\\\r\n]){0,10}'/,greedy:!0}}),delete Prism.languages.go["class-name"],function(e){var t=/[*&][^\s[\]{},]+/,n=/!(?:<[\w\-%#;/?:@&=+$,.!~*'()[\]]+>|(?:[a-zA-Z\d-]*!)?[\w\-%#;/?:@&=+$.~*'()]+)?/,r="(?:"+n.source+"(?:[ \t]+"+t.source+")?|"+t.source+"(?:[ \t]+"+n.source+")?)",a=/(?:[^\s\x00-\x08\x0e-\x1f!"#%&'*,\-:>?@[\]`{|}\x7f-\x84\x86-\x9f\ud800-\udfff\ufffe\uffff]|[?:-]<PLAIN>)(?:[ \t]*(?:(?![#:])<PLAIN>|:<PLAIN>))*/.source.replace(/<PLAIN>/g,(function(){return/[^\s\x00-\x08\x0e-\x1f,[\]{}\x7f-\x84\x86-\x9f\ud800-\udfff\ufffe\uffff]/.source})),o=/"(?:[^"\\\r\n]|\\.)*"|'(?:[^'\\\r\n]|\\.)*'/.source;function i(e,t){t=(t||"").replace(/m/g,"")+"m";var n=/([:\-,[{]\s*(?:\s<<prop>>[ \t]+)?)(?:<<value>>)(?=[ \t]*(?:$|,|\]|\}|(?:[\r\n]\s*)?#))/.source.replace(/<<prop>>/g,(function(){return r})).replace(/<<value>>/g,(function(){return e}));return RegExp(n,t)}e.languages.yaml={scalar:{pattern:RegExp(/([\-:]\s*(?:\s<<prop>>[ \t]+)?[|>])[ \t]*(?:((?:\r?\n|\r)[ \t]+)\S[^\r\n]*(?:\2[^\r\n]+)*)/.source.replace(/<<prop>>/g,(function(){return r}))),lookbehind:!0,alias:"string"},comment:/#.*/,key:{pattern:RegExp(/((?:^|[:\-,[{\r\n?])[ \t]*(?:<<prop>>[ \t]+)?)<<key>>(?=\s*:\s)/.source.replace(/<<prop>>/g,(function(){return r})).replace(/<<key>>/g,(function(){return"(?:"+a+"|"+o+")"}))),lookbehind:!0,greedy:!0,alias:"atrule"},directive:{pattern:/(^[ \t]*)%.+/m,lookbehind:!0,alias:"important"},datetime:{pattern:i(/\d{4}-\d\d?-\d\d?(?:[tT]|[ \t]+)\d\d?:\d{2}:\d{2}(?:\.\d*)?(?:[ \t]*(?:Z|[-+]\d\d?(?::\d{2})?))?|\d{4}-\d{2}-\d{2}|\d\d?:\d{2}(?::\d{2}(?:\.\d*)?)?/.source),lookbehind:!0,alias:"number"},boolean:{pattern:i(/false|true/.source,"i"),lookbehind:!0,alias:"important"},null:{pattern:i(/null|~/.source,"i"),lookbehind:!0,alias:"important"},string:{pattern:i(o),lookbehind:!0,greedy:!0},number:{pattern:i(/[+-]?(?:0x[\da-f]+|0o[0-7]+|(?:\d+(?:\.\d*)?|\.\d+)(?:e[+-]?\d+)?|\.inf|\.nan)/.source,"i"),lookbehind:!0},tag:n,important:t,punctuation:/---|[:[\]{}\-,|>?]|\.\.\./},e.languages.yml=e.languages.yaml}(Prism),function(e){var t=/(?:\\.|[^\\\n\r]|(?:\n|\r\n?)(?![\r\n]))/.source;function n(e){return e=e.replace(/<inner>/g,(function(){return t})),RegExp(/((?:^|[^\\])(?:\\{2})*)/.source+"(?:"+e+")")}var r=/(?:\\.|``(?:[^`\r\n]|`(?!`))+``|`[^`\r\n]+`|[^\\|\r\n`])+/.source,a=/\|?__(?:\|__)+\|?(?:(?:\n|\r\n?)|(?![\s\S]))/.source.replace(/__/g,(function(){return r})),o=/\|?[ \t]*:?-{3,}:?[ \t]*(?:\|[ \t]*:?-{3,}:?[ \t]*)+\|?(?:\n|\r\n?)/.source;e.languages.markdown=e.languages.extend("markup",{}),e.languages.insertBefore("markdown","prolog",{"front-matter-block":{pattern:/(^(?:\s*[\r\n])?)---(?!.)[\s\S]*?[\r\n]---(?!.)/,lookbehind:!0,greedy:!0,inside:{punctuation:/^---|---$/,"front-matter":{pattern:/\S+(?:\s+\S+)*/,alias:["yaml","language-yaml"],inside:e.languages.yaml}}},blockquote:{pattern:/^>(?:[\t ]*>)*/m,alias:"punctuation"},table:{pattern:RegExp("^"+a+o+"(?:"+a+")*","m"),inside:{"table-data-rows":{pattern:RegExp("^("+a+o+")(?:"+a+")*$"),lookbehind:!0,inside:{"table-data":{pattern:RegExp(r),inside:e.languages.markdown},punctuation:/\|/}},"table-line":{pattern:RegExp("^("+a+")"+o+"$"),lookbehind:!0,inside:{punctuation:/\||:?-{3,}:?/}},"table-header-row":{pattern:RegExp("^"+a+"$"),inside:{"table-header":{pattern:RegExp(r),alias:"important",inside:e.languages.markdown},punctuation:/\|/}}}},code:[{pattern:/((?:^|\n)[ \t]*\n|(?:^|\r\n?)[ \t]*\r\n?)(?: {4}|\t).+(?:(?:\n|\r\n?)(?: {4}|\t).+)*/,lookbehind:!0,alias:"keyword"},{pattern:/^```[\s\S]*?^```$/m,greedy:!0,inside:{"code-block":{pattern:/^(```.*(?:\n|\r\n?))[\s\S]+?(?=(?:\n|\r\n?)^```$)/m,lookbehind:!0},"code-language":{pattern:/^(```).+/,lookbehind:!0},punctuation:/```/}}],title:[{pattern:/\S.*(?:\n|\r\n?)(?:==+|--+)(?=[ \t]*$)/m,alias:"important",inside:{punctuation:/==+$|--+$/}},{pattern:/(^\s*)#.+/m,lookbehind:!0,alias:"important",inside:{punctuation:/^#+|#+$/}}],hr:{pattern:/(^\s*)([*-])(?:[\t ]*\2){2,}(?=\s*$)/m,lookbehind:!0,alias:"punctuation"},list:{pattern:/(^\s*)(?:[*+-]|\d+\.)(?=[\t ].)/m,lookbehind:!0,alias:"punctuation"},"url-reference":{pattern:/!?\[[^\]]+\]:[\t ]+(?:\S+|<(?:\\.|[^>\\])+>)(?:[\t ]+(?:"(?:\\.|[^"\\])*"|'(?:\\.|[^'\\])*'|\((?:\\.|[^)\\])*\)))?/,inside:{variable:{pattern:/^(!?\[)[^\]]+/,lookbehind:!0},string:/(?:"(?:\\.|[^"\\])*"|'(?:\\.|[^'\\])*'|\((?:\\.|[^)\\])*\))$/,punctuation:/^[\[\]!:]|[<>]/},alias:"url"},bold:{pattern:n(/\b__(?:(?!_)<inner>|_(?:(?!_)<inner>)+_)+__\b|\*\*(?:(?!\*)<inner>|\*(?:(?!\*)<inner>)+\*)+\*\*/.source),lookbehind:!0,greedy:!0,inside:{content:{pattern:/(^..)[\s\S]+(?=..$)/,lookbehind:!0,inside:{}},punctuation:/\*\*|__/}},italic:{pattern:n(/\b_(?:(?!_)<inner>|__(?:(?!_)<inner>)+__)+_\b|\*(?:(?!\*)<inner>|\*\*(?:(?!\*)<inner>)+\*\*)+\*/.source),lookbehind:!0,greedy:!0,inside:{content:{pattern:/(^.)[\s\S]+(?=.$)/,lookbehind:!0,inside:{}},punctuation:/[*_]/}},strike:{pattern:n(/(~~?)(?:(?!~)<inner>)+\2/.source),lookbehind:!0,greedy:!0,inside:{content:{pattern:/(^~~?)[\s\S]+(?=\1$)/,lookbehind:!0,inside:{}},punctuation:/~~?/}},"code-snippet":{pattern:/(^|[^\\`])(?:``[^`\r\n]+(?:`[^`\r\n]+)*``(?!`)|`[^`\r\n]+`(?!`))/,lookbehind:!0,greedy:!0,alias:["code","keyword"]},url:{pattern:n(/!?\[(?:(?!\])<inner>)+\](?:\([^\s)]+(?:[\t ]+"(?:\\.|[^"\\])*")?\)|[ \t]?\[(?:(?!\])<inner>)+\])/.source),lookbehind:!0,greedy:!0,inside:{operator:/^!/,content:{pattern:/(^\[)[^\]]+(?=\])/,lookbehind:!0,inside:{}},variable:{pattern:/(^\][ \t]?\[)[^\]]+(?=\]$)/,lookbehind:!0},url:{pattern:/(^\]\()[^\s)]+/,lookbehind:!0},string:{pattern:/(^[ \t]+)"(?:\\.|[^"\\])*"(?=\)$)/,lookbehind:!0}}}}),["url","bold","italic","strike"].forEach((function(t){["url","bold","italic","strike","code-snippet"].forEach((function(n){t!==n&&(e.languages.markdown[t].inside.content.inside[n]=e.languages.markdown[n])}))})),e.hooks.add("after-tokenize",(function(e){"markdown"!==e.language&&"md"!==e.language||function e(t){if(t&&"string"!=typeof t)for(var n=0,r=t.length;n<r;n++){var a=t[n];if("code"===a.type){var o=a.content[1],i=a.content[3];if(o&&i&&"code-language"===o.type&&"code-block"===i.type&&"string"==typeof o.content){var s=o.content.replace(/\b#/g,"sharp").replace(/\b\+\+/g,"pp"),c="language-"+(s=(/[a-z][\w-]*/i.exec(s)||[""])[0].toLowerCase());i.alias?"string"==typeof i.alias?i.alias=[i.alias,c]:i.alias.push(c):i.alias=[c]}}else e(a.content)}}(e.tokens)})),e.hooks.add("wrap",(function(t){if("code-block"===t.type){for(var n="",r=0,a=t.classes.length;r<a;r++){var o=t.classes[r],l=/language-(.+)/.exec(o);if(l){n=l[1];break}}var u=e.languages[n];if(u)t.content=e.highlight(function(e){var t=e.replace(i,"");return t=t.replace(/&(\w{1,8}|#x?[\da-f]{1,8});/gi,(function(e,t){var n;if("#"===(t=t.toLowerCase())[0])return n="x"===t[1]?parseInt(t.slice(2),16):Number(t.slice(1)),c(n);var r=s[t];return r||e}))}(t.content),u,n);else if(n&&"none"!==n&&e.plugins.autoloader){var f="md-"+(new Date).valueOf()+"-"+Math.floor(1e16*Math.random());t.attributes.id=f,e.plugins.autoloader.loadLanguages(n,(function(){var t=document.getElementById(f);t&&(t.innerHTML=e.highlight(t.textContent,e.languages[n],n))}))}}}));var i=RegExp(e.languages.markup.tag.pattern.source,"gi"),s={amp:"&",lt:"<",gt:">",quot:'"'},c=String.fromCodePoint||String.fromCharCode;e.languages.md=e.languages.markdown}(Prism),Prism.languages.graphql={comment:/#.*/,description:{pattern:/(?:"""(?:[^"]|(?!""")")*"""|"(?:\\.|[^\\"\r\n])*")(?=\s*[a-z_])/i,greedy:!0,alias:"string",inside:{"language-markdown":{pattern:/(^"(?:"")?)(?!\1)[\s\S]+(?=\1$)/,lookbehind:!0,inside:Prism.languages.markdown}}},string:{pattern:/"""(?:[^"]|(?!""")")*"""|"(?:\\.|[^\\"\r\n])*"/,greedy:!0},number:/(?:\B-|\b)\d+(?:\.\d+)?(?:e[+-]?\d+)?\b/i,boolean:/\b(?:false|true)\b/,variable:/\$[a-z_]\w*/i,directive:{pattern:/@[a-z_]\w*/i,alias:"function"},"attr-name":{pattern:/\b[a-z_]\w*(?=\s*(?:\((?:[^()"]|"(?:\\.|[^\\"\r\n])*")*\))?:)/i,greedy:!0},"atom-input":{pattern:/\b[A-Z]\w*Input\b/,alias:"class-name"},scalar:/\b(?:Boolean|Float|ID|Int|String)\b/,constant:/\b[A-Z][A-Z_\d]*\b/,"class-name":{pattern:/(\b(?:enum|implements|interface|on|scalar|type|union)\s+|&\s*|:\s*|\[)[A-Z_]\w*/,lookbehind:!0},fragment:{pattern:/(\bfragment\s+|\.{3}\s*(?!on\b))[a-zA-Z_]\w*/,lookbehind:!0,alias:"function"},"definition-mutation":{pattern:/(\bmutation\s+)[a-zA-Z_]\w*/,lookbehind:!0,alias:"function"},"definition-query":{pattern:/(\bquery\s+)[a-zA-Z_]\w*/,lookbehind:!0,alias:"function"},keyword:/\b(?:directive|enum|extend|fragment|implements|input|interface|mutation|on|query|repeatable|scalar|schema|subscription|type|union)\b/,operator:/[!=|&]|\.{3}/,"property-query":/\w+(?=\s*\()/,object:/\w+(?=\s*\{)/,punctuation:/[!(){}\[\]:=,]/,property:/\w+/},Prism.hooks.add("after-tokenize",(function(e){if("graphql"===e.language)for(var t=e.tokens.filter((function(e){return"string"!=typeof e&&"comment"!==e.type&&"scalar"!==e.type})),n=0;n<t.length;){var r=t[n++];if("keyword"===r.type&&"mutation"===r.content){var a=[];if(f(["definition-mutation","punctuation"])&&"("===u(1).content){n+=2;var o=d(/^\($/,/^\)$/);if(-1===o)continue;for(;n<o;n++){var i=u(0);"variable"===i.type&&(p(i,"variable-input"),a.push(i.content))}n=o+1}if(f(["punctuation","property-query"])&&"{"===u(0).content&&(n++,p(u(0),"property-mutation"),a.length>0)){var s=d(/^\{$/,/^\}$/);if(-1===s)continue;for(var c=n;c<s;c++){var l=t[c];"variable"===l.type&&a.indexOf(l.content)>=0&&p(l,"variable-input")}}}}function u(e){return t[n+e]}function f(e,t){t=t||0;for(var n=0;n<e.length;n++){var r=u(n+t);if(!r||r.type!==e[n])return!1}return!0}function d(e,r){for(var a=1,o=n;o<t.length;o++){var i=t[o],s=i.content;if("punctuation"===i.type&&"string"==typeof s)if(e.test(s))a++;else if(r.test(s)&&0===--a)return o}return-1}function p(e,t){var n=e.alias;n?Array.isArray(n)||(e.alias=n=[n]):e.alias=n=[],n.push(t)}})),function(e){e.languages.ruby=e.languages.extend("clike",{comment:{pattern:/#.*|^=begin\s[\s\S]*?^=end/m,greedy:!0},"class-name":{pattern:/(\b(?:class|module)\s+|\bcatch\s+\()[\w.\\]+|\b[A-Z_]\w*(?=\s*\.\s*new\b)/,lookbehind:!0,inside:{punctuation:/[.\\]/}},keyword:/\b(?:BEGIN|END|alias|and|begin|break|case|class|def|define_method|defined|do|each|else|elsif|end|ensure|extend|for|if|in|include|module|new|next|nil|not|or|prepend|private|protected|public|raise|redo|require|rescue|retry|return|self|super|then|throw|undef|unless|until|when|while|yield)\b/,operator:/\.{2,3}|&\.|===|<?=>|[!=]?~|(?:&&|\|\||<<|>>|\*\*|[+\-*/%<>!^&|=])=?|[?:]/,punctuation:/[(){}[\].,;]/}),e.languages.insertBefore("ruby","operator",{"double-colon":{pattern:/::/,alias:"punctuation"}});var t={pattern:/((?:^|[^\\])(?:\\{2})*)#\{(?:[^{}]|\{[^{}]*\})*\}/,lookbehind:!0,inside:{content:{pattern:/^(#\{)[\s\S]+(?=\}$)/,lookbehind:!0,inside:e.languages.ruby},delimiter:{pattern:/^#\{|\}$/,alias:"punctuation"}}};delete e.languages.ruby.function;var n="(?:"+[/([^a-zA-Z0-9\s{(\[<=])(?:(?!\1)[^\\]|\\[\s\S])*\1/.source,/\((?:[^()\\]|\\[\s\S]|\((?:[^()\\]|\\[\s\S])*\))*\)/.source,/\{(?:[^{}\\]|\\[\s\S]|\{(?:[^{}\\]|\\[\s\S])*\})*\}/.source,/\[(?:[^\[\]\\]|\\[\s\S]|\[(?:[^\[\]\\]|\\[\s\S])*\])*\]/.source,/<(?:[^<>\\]|\\[\s\S]|<(?:[^<>\\]|\\[\s\S])*>)*>/.source].join("|")+")",r=/(?:"(?:\\.|[^"\\\r\n])*"|(?:\b[a-zA-Z_]\w*|[^\s\0-\x7F]+)[?!]?|\$.)/.source;e.languages.insertBefore("ruby","keyword",{"regex-literal":[{pattern:RegExp(/%r/.source+n+/[egimnosux]{0,6}/.source),greedy:!0,inside:{interpolation:t,regex:/[\s\S]+/}},{pattern:/(^|[^/])\/(?!\/)(?:\[[^\r\n\]]+\]|\\.|[^[/\\\r\n])+\/[egimnosux]{0,6}(?=\s*(?:$|[\r\n,.;})#]))/,lookbehind:!0,greedy:!0,inside:{interpolation:t,regex:/[\s\S]+/}}],variable:/[@$]+[a-zA-Z_]\w*(?:[?!]|\b)/,symbol:[{pattern:RegExp(/(^|[^:]):/.source+r),lookbehind:!0,greedy:!0},{pattern:RegExp(/([\r\n{(,][ \t]*)/.source+r+/(?=:(?!:))/.source),lookbehind:!0,greedy:!0}],"method-definition":{pattern:/(\bdef\s+)\w+(?:\s*\.\s*\w+)?/,lookbehind:!0,inside:{function:/\b\w+$/,keyword:/^self\b/,"class-name":/^\w+/,punctuation:/\./}}}),e.languages.insertBefore("ruby","string",{"string-literal":[{pattern:RegExp(/%[qQiIwWs]?/.source+n),greedy:!0,inside:{interpolation:t,string:/[\s\S]+/}},{pattern:/("|')(?:#\{[^}]+\}|#(?!\{)|\\(?:\r\n|[\s\S])|(?!\1)[^\\#\r\n])*\1/,greedy:!0,inside:{interpolation:t,string:/[\s\S]+/}},{pattern:/<<[-~]?([a-z_]\w*)[\r\n](?:.*[\r\n])*?[\t ]*\1/i,alias:"heredoc-string",greedy:!0,inside:{delimiter:{pattern:/^<<[-~]?[a-z_]\w*|\b[a-z_]\w*$/i,inside:{symbol:/\b\w+/,punctuation:/^<<[-~]?/}},interpolation:t,string:/[\s\S]+/}},{pattern:/<<[-~]?'([a-z_]\w*)'[\r\n](?:.*[\r\n])*?[\t ]*\1/i,alias:"heredoc-string",greedy:!0,inside:{delimiter:{pattern:/^<<[-~]?'[a-z_]\w*'|\b[a-z_]\w*$/i,inside:{symbol:/\b\w+/,punctuation:/^<<[-~]?'|'$/}},string:/[\s\S]+/}}],"command-literal":[{pattern:RegExp(/%x/.source+n),greedy:!0,inside:{interpolation:t,command:{pattern:/[\s\S]+/,alias:"string"}}},{pattern:/`(?:#\{[^}]+\}|#(?!\{)|\\(?:\r\n|[\s\S])|[^\\`#\r\n])*`/,greedy:!0,inside:{interpolation:t,command:{pattern:/[\s\S]+/,alias:"string"}}}]}),delete e.languages.ruby.string,e.languages.insertBefore("ruby","number",{builtin:/\b(?:Array|Bignum|Binding|Class|Continuation|Dir|Exception|FalseClass|File|Fixnum|Float|Hash|IO|Integer|MatchData|Method|Module|NilClass|Numeric|Object|Proc|Range|Regexp|Stat|String|Struct|Symbol|TMS|Thread|ThreadGroup|Time|TrueClass)\b/,constant:/\b[A-Z][A-Z0-9_]*(?:[?!]|\b)/}),e.languages.rb=e.languages.ruby}(Prism),function(e){var t={pattern:/\\[\\(){}[\]^$+*?|.]/,alias:"escape"},n=/\\(?:x[\da-fA-F]{2}|u[\da-fA-F]{4}|u\{[\da-fA-F]+\}|0[0-7]{0,2}|[123][0-7]{2}|c[a-zA-Z]|.)/,r="(?:[^\\\\-]|"+n.source+")",a=RegExp(r+"-"+r),o={pattern:/(<|')[^<>']+(?=[>']$)/,lookbehind:!0,alias:"variable"};e.languages.regex={"char-class":{pattern:/((?:^|[^\\])(?:\\\\)*)\[(?:[^\\\]]|\\[\s\S])*\]/,lookbehind:!0,inside:{"char-class-negation":{pattern:/(^\[)\^/,lookbehind:!0,alias:"operator"},"char-class-punctuation":{pattern:/^\[|\]$/,alias:"punctuation"},range:{pattern:a,inside:{escape:n,"range-punctuation":{pattern:/-/,alias:"operator"}}},"special-escape":t,"char-set":{pattern:/\\[wsd]|\\p\{[^{}]+\}/i,alias:"class-name"},escape:n}},"special-escape":t,"char-set":{pattern:/\.|\\[wsd]|\\p\{[^{}]+\}/i,alias:"class-name"},backreference:[{pattern:/\\(?![123][0-7]{2})[1-9]/,alias:"keyword"},{pattern:/\\k<[^<>']+>/,alias:"keyword",inside:{"group-name":o}}],anchor:{pattern:/[$^]|\\[ABbGZz]/,alias:"function"},escape:n,group:[{pattern:/\((?:\?(?:<[^<>']+>|'[^<>']+'|[>:]|<?[=!]|[idmnsuxU]+(?:-[idmnsuxU]+)?:?))?/,alias:"punctuation",inside:{"group-name":o}},{pattern:/\)/,alias:"punctuation"}],quantifier:{pattern:/(?:[+*?]|\{\d+(?:,\d*)?\})[?+]?/,alias:"number"},alternation:{pattern:/\|/,alias:"keyword"}}}(Prism),Prism.languages.javascript=Prism.languages.extend("clike",{"class-name":[Prism.languages.clike["class-name"],{pattern:/(^|[^$\w\xA0-\uFFFF])(?!\s)[_$A-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\.(?:constructor|prototype))/,lookbehind:!0}],keyword:[{pattern:/((?:^|\})\s*)catch\b/,lookbehind:!0},{pattern:/(^|[^.]|\.\.\.\s*)\b(?:as|assert(?=\s*\{)|async(?=\s*(?:function\b|\(|[$\w\xA0-\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally(?=\s*(?:\{|$))|for|from(?=\s*(?:['"]|$))|function|(?:get|set)(?=\s*(?:[#\[$\w\xA0-\uFFFF]|$))|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\b/,lookbehind:!0}],function:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*(?:\.\s*(?:apply|bind|call)\s*)?\()/,number:{pattern:RegExp(/(^|[^\w$])/.source+"(?:"+/NaN|Infinity/.source+"|"+/0[bB][01]+(?:_[01]+)*n?/.source+"|"+/0[oO][0-7]+(?:_[0-7]+)*n?/.source+"|"+/0[xX][\dA-Fa-f]+(?:_[\dA-Fa-f]+)*n?/.source+"|"+/\d+(?:_\d+)*n/.source+"|"+/(?:\d+(?:_\d+)*(?:\.(?:\d+(?:_\d+)*)?)?|\.\d+(?:_\d+)*)(?:[Ee][+-]?\d+(?:_\d+)*)?/.source+")"+/(?![\w$])/.source),lookbehind:!0},operator:/--|\+\+|\*\*=?|=>|&&=?|\|\|=?|[!=]==|<<=?|>>>?=?|[-+*/%&|^!=<>]=?|\.{3}|\?\?=?|\?\.?|[~:]/}),Prism.languages.javascript["class-name"][0].pattern=/(\b(?:class|extends|implements|instanceof|interface|new)\s+)[\w.\\]+/,Prism.languages.insertBefore("javascript","keyword",{regex:{pattern:RegExp(/((?:^|[^$\w\xA0-\uFFFF."'\])\s]|\b(?:return|yield))\s*)/.source+/\//.source+"(?:"+/(?:\[(?:[^\]\\\r\n]|\\.)*\]|\\.|[^/\\\[\r\n])+\/[dgimyus]{0,7}/.source+"|"+/(?:\[(?:[^[\]\\\r\n]|\\.|\[(?:[^[\]\\\r\n]|\\.|\[(?:[^[\]\\\r\n]|\\.)*\])*\])*\]|\\.|[^/\\\[\r\n])+\/[dgimyus]{0,7}v[dgimyus]{0,7}/.source+")"+/(?=(?:\s|\/\*(?:[^*]|\*(?!\/))*\*\/)*(?:$|[\r\n,.;:})\]]|\/\/))/.source),lookbehind:!0,greedy:!0,inside:{"regex-source":{pattern:/^(\/)[\s\S]+(?=\/[a-z]*$)/,lookbehind:!0,alias:"language-regex",inside:Prism.languages.regex},"regex-delimiter":/^\/|\/$/,"regex-flags":/^[a-z]+$/}},"function-variable":{pattern:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*[=:]\s*(?:async\s*)?(?:\bfunction\b|(?:\((?:[^()]|\([^()]*\))*\)|(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*)\s*=>))/,alias:"function"},parameter:[{pattern:/(function(?:\s+(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*)?\s*\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\))/,lookbehind:!0,inside:Prism.languages.javascript},{pattern:/(^|[^$\w\xA0-\uFFFF])(?!\s)[_$a-z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*=>)/i,lookbehind:!0,inside:Prism.languages.javascript},{pattern:/(\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\)\s*=>)/,lookbehind:!0,inside:Prism.languages.javascript},{pattern:/((?:\b|\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\w\xA0-\uFFFF]))(?:(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*\s*)\(\s*|\]\s*\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\)\s*\{)/,lookbehind:!0,inside:Prism.languages.javascript}],constant:/\b[A-Z](?:[A-Z_]|\dx?)*\b/}),Prism.languages.insertBefore("javascript","string",{hashbang:{pattern:/^#!.*/,greedy:!0,alias:"comment"},"template-string":{pattern:/`(?:\\[\s\S]|\$\{(?:[^{}]|\{(?:[^{}]|\{[^}]*\})*\})+\}|(?!\$\{)[^\\`])*`/,greedy:!0,inside:{"template-punctuation":{pattern:/^`|`$/,alias:"string"},interpolation:{pattern:/((?:^|[^\\])(?:\\{2})*)\$\{(?:[^{}]|\{(?:[^{}]|\{[^}]*\})*\})+\}/,lookbehind:!0,inside:{"interpolation-punctuation":{pattern:/^\$\{|\}$/,alias:"punctuation"},rest:Prism.languages.javascript}},string:/[\s\S]+/}},"string-property":{pattern:/((?:^|[,{])[ \t]*)(["'])(?:\\(?:\r\n|[\s\S])|(?!\2)[^\\\r\n])*\2(?=\s*:)/m,lookbehind:!0,greedy:!0,alias:"property"}}),Prism.languages.insertBefore("javascript","operator",{"literal-property":{pattern:/((?:^|[,{])[ \t]*)(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*:)/m,lookbehind:!0,alias:"property"}}),Prism.languages.markup&&(Prism.languages.markup.tag.addInlined("script","javascript"),Prism.languages.markup.tag.addAttribute(/on(?:abort|blur|change|click|composition(?:end|start|update)|dblclick|error|focus(?:in|out)?|key(?:down|up)|load|mouse(?:down|enter|leave|move|out|over|up)|reset|resize|scroll|select|slotchange|submit|unload|wheel)/.source,"javascript")),Prism.languages.js=Prism.languages.javascript,function(e){function t(e,t){return"___"+e.toUpperCase()+t+"___"}Object.defineProperties(e.languages["markup-templating"]={},{buildPlaceholders:{value:function(n,r,a,o){if(n.language===r){var i=n.tokenStack=[];n.code=n.code.replace(a,(function(e){if("function"==typeof o&&!o(e))return e;for(var a,s=i.length;-1!==n.code.indexOf(a=t(r,s));)++s;return i[s]=e,a})),n.grammar=e.languages.markup}}},tokenizePlaceholders:{value:function(n,r){if(n.language===r&&n.tokenStack){n.grammar=e.languages[r];var a=0,o=Object.keys(n.tokenStack);!function i(s){for(var c=0;c<s.length&&!(a>=o.length);c++){var l=s[c];if("string"==typeof l||l.content&&"string"==typeof l.content){var u=o[a],f=n.tokenStack[u],d="string"==typeof l?l:l.content,p=t(r,u),h=d.indexOf(p);if(h>-1){++a;var g=d.substring(0,h),m=new e.Token(r,e.tokenize(f,n.grammar),"language-"+r,f),b=d.substring(h+p.length),v=[];g&&v.push.apply(v,i([g])),v.push(m),b&&v.push.apply(v,i([b])),"string"==typeof l?s.splice.apply(s,[c,1].concat(v)):l.content=v}}else l.content&&i(l.content)}return s}(n.tokens)}}}})}(Prism),Prism.languages.less=Prism.languages.extend("css",{comment:[/\/\*[\s\S]*?\*\//,{pattern:/(^|[^\\])\/\/.*/,lookbehind:!0}],atrule:{pattern:/@[\w-](?:\((?:[^(){}]|\([^(){}]*\))*\)|[^(){};\s]|\s+(?!\s))*?(?=\s*\{)/,inside:{punctuation:/[:()]/}},selector:{pattern:/(?:@\{[\w-]+\}|[^{};\s@])(?:@\{[\w-]+\}|\((?:[^(){}]|\([^(){}]*\))*\)|[^(){};@\s]|\s+(?!\s))*?(?=\s*\{)/,inside:{variable:/@+[\w-]+/}},property:/(?:@\{[\w-]+\}|[\w-])+(?:\+_?)?(?=\s*:)/,operator:/[+\-*\/]/}),Prism.languages.insertBefore("less","property",{variable:[{pattern:/@[\w-]+\s*:/,inside:{punctuation:/:/}},/@@?[\w-]+/],"mixin-usage":{pattern:/([{;]\s*)[.#](?!\d)[\w-].*?(?=[(;])/,lookbehind:!0,alias:"function"}}),Prism.languages.scss=Prism.languages.extend("css",{comment:{pattern:/(^|[^\\])(?:\/\*[\s\S]*?\*\/|\/\/.*)/,lookbehind:!0},atrule:{pattern:/@[\w-](?:\([^()]+\)|[^()\s]|\s+(?!\s))*?(?=\s+[{;])/,inside:{rule:/@[\w-]+/}},url:/(?:[-a-z]+-)?url(?=\()/i,selector:{pattern:/(?=\S)[^@;{}()]?(?:[^@;{}()\s]|\s+(?!\s)|#\{\$[-\w]+\})+(?=\s*\{(?:\}|\s|[^}][^:{}]*[:{][^}]))/,inside:{parent:{pattern:/&/,alias:"important"},placeholder:/%[-\w]+/,variable:/\$[-\w]+|#\{\$[-\w]+\}/}},property:{pattern:/(?:[-\w]|\$[-\w]|#\{\$[-\w]+\})+(?=\s*:)/,inside:{variable:/\$[-\w]+|#\{\$[-\w]+\}/}}}),Prism.languages.insertBefore("scss","atrule",{keyword:[/@(?:content|debug|each|else(?: if)?|extend|for|forward|function|if|import|include|mixin|return|use|warn|while)\b/i,{pattern:/( )(?:from|through)(?= )/,lookbehind:!0}]}),Prism.languages.insertBefore("scss","important",{variable:/\$[-\w]+|#\{\$[-\w]+\}/}),Prism.languages.insertBefore("scss","function",{"module-modifier":{pattern:/\b(?:as|hide|show|with)\b/i,alias:"keyword"},placeholder:{pattern:/%[-\w]+/,alias:"selector"},statement:{pattern:/\B!(?:default|optional)\b/i,alias:"keyword"},boolean:/\b(?:false|true)\b/,null:{pattern:/\bnull\b/,alias:"keyword"},operator:{pattern:/(\s)(?:[-+*\/%]|[=!]=|<=?|>=?|and|not|or)(?=\s)/,lookbehind:!0}}),Prism.languages.scss.atrule.inside.rest=Prism.languages.scss,function(e){e.languages.haml={"multiline-comment":{pattern:/((?:^|\r?\n|\r)([\t ]*))(?:\/|-#).*(?:(?:\r?\n|\r)\2[\t ].+)*/,lookbehind:!0,alias:"comment"},"multiline-code":[{pattern:/((?:^|\r?\n|\r)([\t ]*)(?:[~-]|[&!]?=)).*,[\t ]*(?:(?:\r?\n|\r)\2[\t ].*,[\t ]*)*(?:(?:\r?\n|\r)\2[\t ].+)/,lookbehind:!0,inside:e.languages.ruby},{pattern:/((?:^|\r?\n|\r)([\t ]*)(?:[~-]|[&!]?=)).*\|[\t ]*(?:(?:\r?\n|\r)\2[\t ].*\|[\t ]*)*/,lookbehind:!0,inside:e.languages.ruby}],filter:{pattern:/((?:^|\r?\n|\r)([\t ]*)):[\w-]+(?:(?:\r?\n|\r)(?:\2[\t ].+|\s*?(?=\r?\n|\r)))+/,lookbehind:!0,inside:{"filter-name":{pattern:/^:[\w-]+/,alias:"symbol"}}},markup:{pattern:/((?:^|\r?\n|\r)[\t ]*)<.+/,lookbehind:!0,inside:e.languages.markup},doctype:{pattern:/((?:^|\r?\n|\r)[\t ]*)!!!(?: .+)?/,lookbehind:!0},tag:{pattern:/((?:^|\r?\n|\r)[\t ]*)[%.#][\w\-#.]*[\w\-](?:\([^)]+\)|\{(?:\{[^}]+\}|[^{}])+\}|\[[^\]]+\])*[\/<>]*/,lookbehind:!0,inside:{attributes:[{pattern:/(^|[^#])\{(?:\{[^}]+\}|[^{}])+\}/,lookbehind:!0,inside:e.languages.ruby},{pattern:/\([^)]+\)/,inside:{"attr-value":{pattern:/(=\s*)(?:"(?:\\.|[^\\"\r\n])*"|[^)\s]+)/,lookbehind:!0},"attr-name":/[\w:-]+(?=\s*!?=|\s*[,)])/,punctuation:/[=(),]/}},{pattern:/\[[^\]]+\]/,inside:e.languages.ruby}],punctuation:/[<>]/}},code:{pattern:/((?:^|\r?\n|\r)[\t ]*(?:[~-]|[&!]?=)).+/,lookbehind:!0,inside:e.languages.ruby},interpolation:{pattern:/#\{[^}]+\}/,inside:{delimiter:{pattern:/^#\{|\}$/,alias:"punctuation"},ruby:{pattern:/[\s\S]+/,inside:e.languages.ruby}}},punctuation:{pattern:/((?:^|\r?\n|\r)[\t ]*)[~=\-&!]+/,lookbehind:!0}};for(var t=["css",{filter:"coffee",language:"coffeescript"},"erb","javascript","less","markdown","ruby","scss","textile"],n={},r=0,a=t.length;r<a;r++){var o=t[r];o="string"==typeof o?{filter:o,language:o}:o,e.languages[o.language]&&(n["filter-"+o.filter]={pattern:RegExp("((?:^|\\r?\\n|\\r)([\\t ]*)):{{filter_name}}(?:(?:\\r?\\n|\\r)(?:\\2[\\t ].+|\\s*?(?=\\r?\\n|\\r)))+".replace("{{filter_name}}",(function(){return o.filter}))),lookbehind:!0,inside:{"filter-name":{pattern:/^:[\w-]+/,alias:"symbol"},text:{pattern:/[\s\S]+/,alias:[o.language,"language-"+o.language],inside:e.languages[o.language]}}})}e.languages.insertBefore("haml","filter",n)}(Prism),function(e){var t=/\b(?:abstract|assert|boolean|break|byte|case|catch|char|class|const|continue|default|do|double|else|enum|exports|extends|final|finally|float|for|goto|if|implements|import|instanceof|int|interface|long|module|native|new|non-sealed|null|open|opens|package|permits|private|protected|provides|public|record(?!\s*[(){}[\]<>=%~.:,;?+\-*/&|^])|requires|return|sealed|short|static|strictfp|super|switch|synchronized|this|throw|throws|to|transient|transitive|try|uses|var|void|volatile|while|with|yield)\b/,n=/(?:[a-z]\w*\s*\.\s*)*(?:[A-Z]\w*\s*\.\s*)*/.source,r={pattern:RegExp(/(^|[^\w.])/.source+n+/[A-Z](?:[\d_A-Z]*[a-z]\w*)?\b/.source),lookbehind:!0,inside:{namespace:{pattern:/^[a-z]\w*(?:\s*\.\s*[a-z]\w*)*(?:\s*\.)?/,inside:{punctuation:/\./}},punctuation:/\./}};e.languages.java=e.languages.extend("clike",{string:{pattern:/(^|[^\\])"(?:\\.|[^"\\\r\n])*"/,lookbehind:!0,greedy:!0},"class-name":[r,{pattern:RegExp(/(^|[^\w.])/.source+n+/[A-Z]\w*(?=\s+\w+\s*[;,=()]|\s*(?:\[[\s,]*\]\s*)?::\s*new\b)/.source),lookbehind:!0,inside:r.inside},{pattern:RegExp(/(\b(?:class|enum|extends|implements|instanceof|interface|new|record|throws)\s+)/.source+n+/[A-Z]\w*\b/.source),lookbehind:!0,inside:r.inside}],keyword:t,function:[e.languages.clike.function,{pattern:/(::\s*)[a-z_]\w*/,lookbehind:!0}],number:/\b0b[01][01_]*L?\b|\b0x(?:\.[\da-f_p+-]+|[\da-f_]+(?:\.[\da-f_p+-]+)?)\b|(?:\b\d[\d_]*(?:\.[\d_]*)?|\B\.\d[\d_]*)(?:e[+-]?\d[\d_]*)?[dfl]?/i,operator:{pattern:/(^|[^.])(?:<<=?|>>>?=?|->|--|\+\+|&&|\|\||::|[?:~]|[-+*/%&|^!=<>]=?)/m,lookbehind:!0}}),e.languages.insertBefore("java","string",{"triple-quoted-string":{pattern:/"""[ \t]*[\r\n](?:(?:"|"")?(?:\\.|[^"\\]))*"""/,greedy:!0,alias:"string"},char:{pattern:/'(?:\\.|[^'\\\r\n]){1,6}'/,greedy:!0}}),e.languages.insertBefore("java","class-name",{annotation:{pattern:/(^|[^.])@\w+(?:\s*\.\s*\w+)*/,lookbehind:!0,alias:"punctuation"},generics:{pattern:/<(?:[\w\s,.?]|&(?!&)|<(?:[\w\s,.?]|&(?!&)|<(?:[\w\s,.?]|&(?!&)|<(?:[\w\s,.?]|&(?!&))*>)*>)*>)*>/,inside:{"class-name":r,keyword:t,punctuation:/[<>(),.:]/,operator:/[?&|]/}},import:[{pattern:RegExp(/(\bimport\s+)/.source+n+/(?:[A-Z]\w*|\*)(?=\s*;)/.source),lookbehind:!0,inside:{namespace:r.inside.namespace,punctuation:/\./,operator:/\*/,"class-name":/\w+/}},{pattern:RegExp(/(\bimport\s+static\s+)/.source+n+/(?:\w+|\*)(?=\s*;)/.source),lookbehind:!0,alias:"static",inside:{namespace:r.inside.namespace,static:/\b\w+$/,punctuation:/\./,operator:/\*/,"class-name":/\w+/}}],namespace:{pattern:RegExp(/(\b(?:exports|import(?:\s+static)?|module|open|opens|package|provides|requires|to|transitive|uses|with)\s+)(?!<keyword>)[a-z]\w*(?:\.[a-z]\w*)*\.?/.source.replace(/<keyword>/g,(function(){return t.source}))),lookbehind:!0,inside:{punctuation:/\./}}})}(Prism),Prism.languages.json={property:{pattern:/(^|[^\\])"(?:\\.|[^\\"\r\n])*"(?=\s*:)/,lookbehind:!0,greedy:!0},string:{pattern:/(^|[^\\])"(?:\\.|[^\\"\r\n])*"(?!\s*:)/,lookbehind:!0,greedy:!0},comment:{pattern:/\/\/.*|\/\*[\s\S]*?(?:\*\/|$)/,greedy:!0},number:/-?\b\d+(?:\.\d+)?(?:e[+-]?\d+)?\b/i,punctuation:/[{}[\],]/,operator:/:/,boolean:/\b(?:false|true)\b/,null:{pattern:/\bnull\b/,alias:"keyword"}},Prism.languages.webmanifest=Prism.languages.json,Prism.languages.lua={comment:/^#!.+|--(?:\[(=*)\[[\s\S]*?\]\1\]|.*)/m,string:{pattern:/(["'])(?:(?!\1)[^\\\r\n]|\\z(?:\r\n|\s)|\\(?:\r\n|[^z]))*\1|\[(=*)\[[\s\S]*?\]\2\]/,greedy:!0},number:/\b0x[a-f\d]+(?:\.[a-f\d]*)?(?:p[+-]?\d+)?\b|\b\d+(?:\.\B|(?:\.\d*)?(?:e[+-]?\d+)?\b)|\B\.\d+(?:e[+-]?\d+)?\b/i,keyword:/\b(?:and|break|do|else|elseif|end|false|for|function|goto|if|in|local|nil|not|or|repeat|return|then|true|until|while)\b/,function:/(?!\d)\w+(?=\s*(?:[({]))/,operator:[/[-+*%^&|#]|\/\/?|<[<=]?|>[>=]?|[=~]=?/,{pattern:/(^|[^.])\.\.(?!\.)/,lookbehind:!0}],punctuation:/[\[\](){},;]|\.+|:+/},Prism.languages.matlab={comment:[/%\{[\s\S]*?\}%/,/%.+/],string:{pattern:/\B'(?:''|[^'\r\n])*'/,greedy:!0},number:/(?:\b\d+(?:\.\d*)?|\B\.\d+)(?:[eE][+-]?\d+)?(?:[ij])?|\b[ij]\b/,keyword:/\b(?:NaN|break|case|catch|continue|else|elseif|end|for|function|if|inf|otherwise|parfor|pause|pi|return|switch|try|while)\b/,function:/\b(?!\d)\w+(?=\s*\()/,operator:/\.?[*^\/\\']|[+\-:@]|[<>=~]=?|&&?|\|\|?/,punctuation:/\.{3}|[.,;\[\](){}!]/},function(e){var t=["$eq","$gt","$gte","$in","$lt","$lte","$ne","$nin","$and","$not","$nor","$or","$exists","$type","$expr","$jsonSchema","$mod","$regex","$text","$where","$geoIntersects","$geoWithin","$near","$nearSphere","$all","$elemMatch","$size","$bitsAllClear","$bitsAllSet","$bitsAnyClear","$bitsAnySet","$comment","$elemMatch","$meta","$slice","$currentDate","$inc","$min","$max","$mul","$rename","$set","$setOnInsert","$unset","$addToSet","$pop","$pull","$push","$pullAll","$each","$position","$slice","$sort","$bit","$addFields","$bucket","$bucketAuto","$collStats","$count","$currentOp","$facet","$geoNear","$graphLookup","$group","$indexStats","$limit","$listLocalSessions","$listSessions","$lookup","$match","$merge","$out","$planCacheStats","$project","$redact","$replaceRoot","$replaceWith","$sample","$set","$skip","$sort","$sortByCount","$unionWith","$unset","$unwind","$setWindowFields","$abs","$accumulator","$acos","$acosh","$add","$addToSet","$allElementsTrue","$and","$anyElementTrue","$arrayElemAt","$arrayToObject","$asin","$asinh","$atan","$atan2","$atanh","$avg","$binarySize","$bsonSize","$ceil","$cmp","$concat","$concatArrays","$cond","$convert","$cos","$dateFromParts","$dateToParts","$dateFromString","$dateToString","$dayOfMonth","$dayOfWeek","$dayOfYear","$degreesToRadians","$divide","$eq","$exp","$filter","$first","$floor","$function","$gt","$gte","$hour","$ifNull","$in","$indexOfArray","$indexOfBytes","$indexOfCP","$isArray","$isNumber","$isoDayOfWeek","$isoWeek","$isoWeekYear","$last","$last","$let","$literal","$ln","$log","$log10","$lt","$lte","$ltrim","$map","$max","$mergeObjects","$meta","$min","$millisecond","$minute","$mod","$month","$multiply","$ne","$not","$objectToArray","$or","$pow","$push","$radiansToDegrees","$range","$reduce","$regexFind","$regexFindAll","$regexMatch","$replaceOne","$replaceAll","$reverseArray","$round","$rtrim","$second","$setDifference","$setEquals","$setIntersection","$setIsSubset","$setUnion","$size","$sin","$slice","$split","$sqrt","$stdDevPop","$stdDevSamp","$strcasecmp","$strLenBytes","$strLenCP","$substr","$substrBytes","$substrCP","$subtract","$sum","$switch","$tan","$toBool","$toDate","$toDecimal","$toDouble","$toInt","$toLong","$toObjectId","$toString","$toLower","$toUpper","$trim","$trunc","$type","$week","$year","$zip","$count","$dateAdd","$dateDiff","$dateSubtract","$dateTrunc","$getField","$rand","$sampleRate","$setField","$unsetField","$comment","$explain","$hint","$max","$maxTimeMS","$min","$orderby","$query","$returnKey","$showDiskLoc","$natural"],n="(?:"+(t=t.map((function(e){return e.replace("$","\\$")}))).join("|")+")\\b";e.languages.mongodb=e.languages.extend("javascript",{}),e.languages.insertBefore("mongodb","string",{property:{pattern:/(?:(["'])(?:\\(?:\r\n|[\s\S])|(?!\1)[^\\\r\n])*\1|(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*)(?=\s*:)/,greedy:!0,inside:{keyword:RegExp("^(['\"])?"+n+"(?:\\1)?$")}}}),e.languages.mongodb.string.inside={url:{pattern:/https?:\/\/[-\w@:%.+~#=]{1,256}\.[a-z0-9()]{1,6}\b[-\w()@:%+.~#?&/=]*/i,greedy:!0},entity:{pattern:/\b(?:(?:[01]?\d\d?|2[0-4]\d|25[0-5])\.){3}(?:[01]?\d\d?|2[0-4]\d|25[0-5])\b/,greedy:!0}},e.languages.insertBefore("mongodb","constant",{builtin:{pattern:RegExp("\\b(?:"+["ObjectId","Code","BinData","DBRef","Timestamp","NumberLong","NumberDecimal","MaxKey","MinKey","RegExp","ISODate","UUID"].join("|")+")\\b"),alias:"keyword"}})}(Prism),function(e){var t=/\$(?:\w[a-z\d]*(?:_[^\x00-\x1F\s"'\\()$]*)?|\{[^}\s"'\\]+\})/i;e.languages.nginx={comment:{pattern:/(^|[\s{};])#.*/,lookbehind:!0,greedy:!0},directive:{pattern:/(^|\s)\w(?:[^;{}"'\\\s]|\\.|"(?:[^"\\]|\\.)*"|'(?:[^'\\]|\\.)*'|\s+(?:#.*(?!.)|(?![#\s])))*?(?=\s*[;{])/,lookbehind:!0,greedy:!0,inside:{string:{pattern:/((?:^|[^\\])(?:\\\\)*)(?:"(?:[^"\\]|\\.)*"|'(?:[^'\\]|\\.)*')/,lookbehind:!0,greedy:!0,inside:{escape:{pattern:/\\["'\\nrt]/,alias:"entity"},variable:t}},comment:{pattern:/(\s)#.*/,lookbehind:!0,greedy:!0},keyword:{pattern:/^\S+/,greedy:!0},boolean:{pattern:/(\s)(?:off|on)(?!\S)/,lookbehind:!0},number:{pattern:/(\s)\d+[a-z]*(?!\S)/i,lookbehind:!0},variable:t}},punctuation:/[{};]/}}(Prism),Prism.languages.objectivec=Prism.languages.extend("c",{string:{pattern:/@?"(?:\\(?:\r\n|[\s\S])|[^"\\\r\n])*"/,greedy:!0},keyword:/\b(?:asm|auto|break|case|char|const|continue|default|do|double|else|enum|extern|float|for|goto|if|in|inline|int|long|register|return|self|short|signed|sizeof|static|struct|super|switch|typedef|typeof|union|unsigned|void|volatile|while)\b|(?:@interface|@end|@implementation|@protocol|@class|@public|@protected|@private|@property|@try|@catch|@finally|@throw|@synthesize|@dynamic|@selector)\b/,operator:/-[->]?|\+\+?|!=?|<<?=?|>>?=?|==?|&&?|\|\|?|[~^%?*\/@]/}),delete Prism.languages.objectivec["class-name"],Prism.languages.objc=Prism.languages.objectivec,Prism.languages.pascal={directive:{pattern:/\{\$[\s\S]*?\}/,greedy:!0,alias:["marco","property"]},comment:{pattern:/\(\*[\s\S]*?\*\)|\{[\s\S]*?\}|\/\/.*/,greedy:!0},string:{pattern:/(?:'(?:''|[^'\r\n])*'(?!')|#[&$%]?[a-f\d]+)+|\^[a-z]/i,greedy:!0},asm:{pattern:/(\basm\b)[\s\S]+?(?=\bend\s*[;[])/i,lookbehind:!0,greedy:!0,inside:null},keyword:[{pattern:/(^|[^&])\b(?:absolute|array|asm|begin|case|const|constructor|destructor|do|downto|else|end|file|for|function|goto|if|implementation|inherited|inline|interface|label|nil|object|of|operator|packed|procedure|program|record|reintroduce|repeat|self|set|string|then|to|type|unit|until|uses|var|while|with)\b/i,lookbehind:!0},{pattern:/(^|[^&])\b(?:dispose|exit|false|new|true)\b/i,lookbehind:!0},{pattern:/(^|[^&])\b(?:class|dispinterface|except|exports|finalization|finally|initialization|inline|library|on|out|packed|property|raise|resourcestring|threadvar|try)\b/i,lookbehind:!0},{pattern:/(^|[^&])\b(?:absolute|abstract|alias|assembler|bitpacked|break|cdecl|continue|cppdecl|cvar|default|deprecated|dynamic|enumerator|experimental|export|external|far|far16|forward|generic|helper|implements|index|interrupt|iochecks|local|message|name|near|nodefault|noreturn|nostackframe|oldfpccall|otherwise|overload|override|pascal|platform|private|protected|public|published|read|register|reintroduce|result|safecall|saveregisters|softfloat|specialize|static|stdcall|stored|strict|unaligned|unimplemented|varargs|virtual|write)\b/i,lookbehind:!0}],number:[/(?:[&%]\d+|\$[a-f\d]+)/i,/\b\d+(?:\.\d+)?(?:e[+-]?\d+)?/i],operator:[/\.\.|\*\*|:=|<[<=>]?|>[>=]?|[+\-*\/]=?|[@^=]/,{pattern:/(^|[^&])\b(?:and|as|div|exclude|in|include|is|mod|not|or|shl|shr|xor)\b/,lookbehind:!0}],punctuation:/\(\.|\.\)|[()\[\]:;,.]/},Prism.languages.pascal.asm.inside=Prism.languages.extend("pascal",{asm:void 0,keyword:void 0,operator:void 0}),Prism.languages.objectpascal=Prism.languages.pascal,function(e){var t=/\/\*[\s\S]*?\*\/|\/\/.*|#(?!\[).*/,n=[{pattern:/\b(?:false|true)\b/i,alias:"boolean"},{pattern:/(::\s*)\b[a-z_]\w*\b(?!\s*\()/i,greedy:!0,lookbehind:!0},{pattern:/(\b(?:case|const)\s+)\b[a-z_]\w*(?=\s*[;=])/i,greedy:!0,lookbehind:!0},/\b(?:null)\b/i,/\b[A-Z_][A-Z0-9_]*\b(?!\s*\()/],r=/\b0b[01]+(?:_[01]+)*\b|\b0o[0-7]+(?:_[0-7]+)*\b|\b0x[\da-f]+(?:_[\da-f]+)*\b|(?:\b\d+(?:_\d+)*\.?(?:\d+(?:_\d+)*)?|\B\.\d+)(?:e[+-]?\d+)?/i,a=/<?=>|\?\?=?|\.{3}|\??->|[!=]=?=?|::|\*\*=?|--|\+\+|&&|\|\||<<|>>|[?~]|[/^|%*&<>.+-]=?/,o=/[{}\[\](),:;]/;e.languages.php={delimiter:{pattern:/\?>$|^<\?(?:php(?=\s)|=)?/i,alias:"important"},comment:t,variable:/\$+(?:\w+\b|(?=\{))/,package:{pattern:/(namespace\s+|use\s+(?:function\s+)?)(?:\\?\b[a-z_]\w*)+\b(?!\\)/i,lookbehind:!0,inside:{punctuation:/\\/}},"class-name-definition":{pattern:/(\b(?:class|enum|interface|trait)\s+)\b[a-z_]\w*(?!\\)\b/i,lookbehind:!0,alias:"class-name"},"function-definition":{pattern:/(\bfunction\s+)[a-z_]\w*(?=\s*\()/i,lookbehind:!0,alias:"function"},keyword:[{pattern:/(\(\s*)\b(?:array|bool|boolean|float|int|integer|object|string)\b(?=\s*\))/i,alias:"type-casting",greedy:!0,lookbehind:!0},{pattern:/([(,?]\s*)\b(?:array(?!\s*\()|bool|callable|(?:false|null)(?=\s*\|)|float|int|iterable|mixed|object|self|static|string)\b(?=\s*\$)/i,alias:"type-hint",greedy:!0,lookbehind:!0},{pattern:/(\)\s*:\s*(?:\?\s*)?)\b(?:array(?!\s*\()|bool|callable|(?:false|null)(?=\s*\|)|float|int|iterable|mixed|never|object|self|static|string|void)\b/i,alias:"return-type",greedy:!0,lookbehind:!0},{pattern:/\b(?:array(?!\s*\()|bool|float|int|iterable|mixed|object|string|void)\b/i,alias:"type-declaration",greedy:!0},{pattern:/(\|\s*)(?:false|null)\b|\b(?:false|null)(?=\s*\|)/i,alias:"type-declaration",greedy:!0,lookbehind:!0},{pattern:/\b(?:parent|self|static)(?=\s*::)/i,alias:"static-context",greedy:!0},{pattern:/(\byield\s+)from\b/i,lookbehind:!0},/\bclass\b/i,{pattern:/((?:^|[^\s>:]|(?:^|[^-])>|(?:^|[^:]):)\s*)\b(?:abstract|and|array|as|break|callable|case|catch|clone|const|continue|declare|default|die|do|echo|else|elseif|empty|enddeclare|endfor|endforeach|endif|endswitch|endwhile|enum|eval|exit|extends|final|finally|fn|for|foreach|function|global|goto|if|implements|include|include_once|instanceof|insteadof|interface|isset|list|match|namespace|never|new|or|parent|print|private|protected|public|readonly|require|require_once|return|self|static|switch|throw|trait|try|unset|use|var|while|xor|yield|__halt_compiler)\b/i,lookbehind:!0}],"argument-name":{pattern:/([(,]\s*)\b[a-z_]\w*(?=\s*:(?!:))/i,lookbehind:!0},"class-name":[{pattern:/(\b(?:extends|implements|instanceof|new(?!\s+self|\s+static))\s+|\bcatch\s*\()\b[a-z_]\w*(?!\\)\b/i,greedy:!0,lookbehind:!0},{pattern:/(\|\s*)\b[a-z_]\w*(?!\\)\b/i,greedy:!0,lookbehind:!0},{pattern:/\b[a-z_]\w*(?!\\)\b(?=\s*\|)/i,greedy:!0},{pattern:/(\|\s*)(?:\\?\b[a-z_]\w*)+\b/i,alias:"class-name-fully-qualified",greedy:!0,lookbehind:!0,inside:{punctuation:/\\/}},{pattern:/(?:\\?\b[a-z_]\w*)+\b(?=\s*\|)/i,alias:"class-name-fully-qualified",greedy:!0,inside:{punctuation:/\\/}},{pattern:/(\b(?:extends|implements|instanceof|new(?!\s+self\b|\s+static\b))\s+|\bcatch\s*\()(?:\\?\b[a-z_]\w*)+\b(?!\\)/i,alias:"class-name-fully-qualified",greedy:!0,lookbehind:!0,inside:{punctuation:/\\/}},{pattern:/\b[a-z_]\w*(?=\s*\$)/i,alias:"type-declaration",greedy:!0},{pattern:/(?:\\?\b[a-z_]\w*)+(?=\s*\$)/i,alias:["class-name-fully-qualified","type-declaration"],greedy:!0,inside:{punctuation:/\\/}},{pattern:/\b[a-z_]\w*(?=\s*::)/i,alias:"static-context",greedy:!0},{pattern:/(?:\\?\b[a-z_]\w*)+(?=\s*::)/i,alias:["class-name-fully-qualified","static-context"],greedy:!0,inside:{punctuation:/\\/}},{pattern:/([(,?]\s*)[a-z_]\w*(?=\s*\$)/i,alias:"type-hint",greedy:!0,lookbehind:!0},{pattern:/([(,?]\s*)(?:\\?\b[a-z_]\w*)+(?=\s*\$)/i,alias:["class-name-fully-qualified","type-hint"],greedy:!0,lookbehind:!0,inside:{punctuation:/\\/}},{pattern:/(\)\s*:\s*(?:\?\s*)?)\b[a-z_]\w*(?!\\)\b/i,alias:"return-type",greedy:!0,lookbehind:!0},{pattern:/(\)\s*:\s*(?:\?\s*)?)(?:\\?\b[a-z_]\w*)+\b(?!\\)/i,alias:["class-name-fully-qualified","return-type"],greedy:!0,lookbehind:!0,inside:{punctuation:/\\/}}],constant:n,function:{pattern:/(^|[^\\\w])\\?[a-z_](?:[\w\\]*\w)?(?=\s*\()/i,lookbehind:!0,inside:{punctuation:/\\/}},property:{pattern:/(->\s*)\w+/,lookbehind:!0},number:r,operator:a,punctuation:o};var i={pattern:/\{\$(?:\{(?:\{[^{}]+\}|[^{}]+)\}|[^{}])+\}|(^|[^\\{])\$+(?:\w+(?:\[[^\r\n\[\]]+\]|->\w+)?)/,lookbehind:!0,inside:e.languages.php},s=[{pattern:/<<<'([^']+)'[\r\n](?:.*[\r\n])*?\1;/,alias:"nowdoc-string",greedy:!0,inside:{delimiter:{pattern:/^<<<'[^']+'|[a-z_]\w*;$/i,alias:"symbol",inside:{punctuation:/^<<<'?|[';]$/}}}},{pattern:/<<<(?:"([^"]+)"[\r\n](?:.*[\r\n])*?\1;|([a-z_]\w*)[\r\n](?:.*[\r\n])*?\2;)/i,alias:"heredoc-string",greedy:!0,inside:{delimiter:{pattern:/^<<<(?:"[^"]+"|[a-z_]\w*)|[a-z_]\w*;$/i,alias:"symbol",inside:{punctuation:/^<<<"?|[";]$/}},interpolation:i}},{pattern:/`(?:\\[\s\S]|[^\\`])*`/,alias:"backtick-quoted-string",greedy:!0},{pattern:/'(?:\\[\s\S]|[^\\'])*'/,alias:"single-quoted-string",greedy:!0},{pattern:/"(?:\\[\s\S]|[^\\"])*"/,alias:"double-quoted-string",greedy:!0,inside:{interpolation:i}}];e.languages.insertBefore("php","variable",{string:s,attribute:{pattern:/#\[(?:[^"'\/#]|\/(?![*/])|\/\/.*$|#(?!\[).*$|\/\*(?:[^*]|\*(?!\/))*\*\/|"(?:\\[\s\S]|[^\\"])*"|'(?:\\[\s\S]|[^\\'])*')+\](?=\s*[a-z$#])/im,greedy:!0,inside:{"attribute-content":{pattern:/^(#\[)[\s\S]+(?=\]$)/,lookbehind:!0,inside:{comment:t,string:s,"attribute-class-name":[{pattern:/([^:]|^)\b[a-z_]\w*(?!\\)\b/i,alias:"class-name",greedy:!0,lookbehind:!0},{pattern:/([^:]|^)(?:\\?\b[a-z_]\w*)+/i,alias:["class-name","class-name-fully-qualified"],greedy:!0,lookbehind:!0,inside:{punctuation:/\\/}}],constant:n,number:r,operator:a,punctuation:o}},delimiter:{pattern:/^#\[|\]$/,alias:"punctuation"}}}}),e.hooks.add("before-tokenize",(function(t){if(/<\?/.test(t.code)){e.languages["markup-templating"].buildPlaceholders(t,"php",/<\?(?:[^"'/#]|\/(?![*/])|("|')(?:\\[\s\S]|(?!\1)[^\\])*\1|(?:\/\/|#(?!\[))(?:[^?\n\r]|\?(?!>))*(?=$|\?>|[\r\n])|#\[|\/\*(?:[^*]|\*(?!\/))*(?:\*\/|$))*?(?:\?>|$)/g)}})),e.hooks.add("after-tokenize",(function(t){e.languages["markup-templating"].tokenizePlaceholders(t,"php")}))}(Prism),Prism.languages.python={comment:{pattern:/(^|[^\\])#.*/,lookbehind:!0,greedy:!0},"string-interpolation":{pattern:/(?:f|fr|rf)(?:("""|''')[\s\S]*?\1|("|')(?:\\.|(?!\2)[^\\\r\n])*\2)/i,greedy:!0,inside:{interpolation:{pattern:/((?:^|[^{])(?:\{\{)*)\{(?!\{)(?:[^{}]|\{(?!\{)(?:[^{}]|\{(?!\{)(?:[^{}])+\})+\})+\}/,lookbehind:!0,inside:{"format-spec":{pattern:/(:)[^:(){}]+(?=\}$)/,lookbehind:!0},"conversion-option":{pattern:/![sra](?=[:}]$)/,alias:"punctuation"},rest:null}},string:/[\s\S]+/}},"triple-quoted-string":{pattern:/(?:[rub]|br|rb)?("""|''')[\s\S]*?\1/i,greedy:!0,alias:"string"},string:{pattern:/(?:[rub]|br|rb)?("|')(?:\\.|(?!\1)[^\\\r\n])*\1/i,greedy:!0},function:{pattern:/((?:^|\s)def[ \t]+)[a-zA-Z_]\w*(?=\s*\()/g,lookbehind:!0},"class-name":{pattern:/(\bclass\s+)\w+/i,lookbehind:!0},decorator:{pattern:/(^[\t ]*)@\w+(?:\.\w+)*/m,lookbehind:!0,alias:["annotation","punctuation"],inside:{punctuation:/\./}},keyword:/\b(?:_(?=\s*:)|and|as|assert|async|await|break|case|class|continue|def|del|elif|else|except|exec|finally|for|from|global|if|import|in|is|lambda|match|nonlocal|not|or|pass|print|raise|return|try|while|with|yield)\b/,builtin:/\b(?:__import__|abs|all|any|apply|ascii|basestring|bin|bool|buffer|bytearray|bytes|callable|chr|classmethod|cmp|coerce|compile|complex|delattr|dict|dir|divmod|enumerate|eval|execfile|file|filter|float|format|frozenset|getattr|globals|hasattr|hash|help|hex|id|input|int|intern|isinstance|issubclass|iter|len|list|locals|long|map|max|memoryview|min|next|object|oct|open|ord|pow|property|range|raw_input|reduce|reload|repr|reversed|round|set|setattr|slice|sorted|staticmethod|str|sum|super|tuple|type|unichr|unicode|vars|xrange|zip)\b/,boolean:/\b(?:False|None|True)\b/,number:/\b0(?:b(?:_?[01])+|o(?:_?[0-7])+|x(?:_?[a-f0-9])+)\b|(?:\b\d+(?:_\d+)*(?:\.(?:\d+(?:_\d+)*)?)?|\B\.\d+(?:_\d+)*)(?:e[+-]?\d+(?:_\d+)*)?j?(?!\w)/i,operator:/[-+%=]=?|!=|:=|\*\*?=?|\/\/?=?|<[<=>]?|>[=>]?|[&|^~]/,punctuation:/[{}[\];(),.:]/},Prism.languages.python["string-interpolation"].inside.interpolation.inside.rest=Prism.languages.python,Prism.languages.py=Prism.languages.python,Prism.languages.r={comment:/#.*/,string:{pattern:/(['"])(?:\\.|(?!\1)[^\\\r\n])*\1/,greedy:!0},"percent-operator":{pattern:/%[^%\s]*%/,alias:"operator"},boolean:/\b(?:FALSE|TRUE)\b/,ellipsis:/\.\.(?:\.|\d+)/,number:[/\b(?:Inf|NaN)\b/,/(?:\b0x[\dA-Fa-f]+(?:\.\d*)?|\b\d+(?:\.\d*)?|\B\.\d+)(?:[EePp][+-]?\d+)?[iL]?/],keyword:/\b(?:NA|NA_character_|NA_complex_|NA_integer_|NA_real_|NULL|break|else|for|function|if|in|next|repeat|while)\b/,operator:/->?>?|<(?:=|<?-)?|[>=!]=?|::?|&&?|\|\|?|[+*\/^$@~]/,punctuation:/[(){}\[\],;]/},Prism.languages.sql={comment:{pattern:/(^|[^\\])(?:\/\*[\s\S]*?\*\/|(?:--|\/\/|#).*)/,lookbehind:!0},variable:[{pattern:/@(["'`])(?:\\[\s\S]|(?!\1)[^\\])+\1/,greedy:!0},/@[\w.$]+/],string:{pattern:/(^|[^@\\])("|')(?:\\[\s\S]|(?!\2)[^\\]|\2\2)*\2/,greedy:!0,lookbehind:!0},identifier:{pattern:/(^|[^@\\])`(?:\\[\s\S]|[^`\\]|``)*`/,greedy:!0,lookbehind:!0,inside:{punctuation:/^`|`$/}},function:/\b(?:AVG|COUNT|FIRST|FORMAT|LAST|LCASE|LEN|MAX|MID|MIN|MOD|NOW|ROUND|SUM|UCASE)(?=\s*\()/i,keyword:/\b(?:ACTION|ADD|AFTER|ALGORITHM|ALL|ALTER|ANALYZE|ANY|APPLY|AS|ASC|AUTHORIZATION|AUTO_INCREMENT|BACKUP|BDB|BEGIN|BERKELEYDB|BIGINT|BINARY|BIT|BLOB|BOOL|BOOLEAN|BREAK|BROWSE|BTREE|BULK|BY|CALL|CASCADED?|CASE|CHAIN|CHAR(?:ACTER|SET)?|CHECK(?:POINT)?|CLOSE|CLUSTERED|COALESCE|COLLATE|COLUMNS?|COMMENT|COMMIT(?:TED)?|COMPUTE|CONNECT|CONSISTENT|CONSTRAINT|CONTAINS(?:TABLE)?|CONTINUE|CONVERT|CREATE|CROSS|CURRENT(?:_DATE|_TIME|_TIMESTAMP|_USER)?|CURSOR|CYCLE|DATA(?:BASES?)?|DATE(?:TIME)?|DAY|DBCC|DEALLOCATE|DEC|DECIMAL|DECLARE|DEFAULT|DEFINER|DELAYED|DELETE|DELIMITERS?|DENY|DESC|DESCRIBE|DETERMINISTIC|DISABLE|DISCARD|DISK|DISTINCT|DISTINCTROW|DISTRIBUTED|DO|DOUBLE|DROP|DUMMY|DUMP(?:FILE)?|DUPLICATE|ELSE(?:IF)?|ENABLE|ENCLOSED|END|ENGINE|ENUM|ERRLVL|ERRORS|ESCAPED?|EXCEPT|EXEC(?:UTE)?|EXISTS|EXIT|EXPLAIN|EXTENDED|FETCH|FIELDS|FILE|FILLFACTOR|FIRST|FIXED|FLOAT|FOLLOWING|FOR(?: EACH ROW)?|FORCE|FOREIGN|FREETEXT(?:TABLE)?|FROM|FULL|FUNCTION|GEOMETRY(?:COLLECTION)?|GLOBAL|GOTO|GRANT|GROUP|HANDLER|HASH|HAVING|HOLDLOCK|HOUR|IDENTITY(?:COL|_INSERT)?|IF|IGNORE|IMPORT|INDEX|INFILE|INNER|INNODB|INOUT|INSERT|INT|INTEGER|INTERSECT|INTERVAL|INTO|INVOKER|ISOLATION|ITERATE|JOIN|KEYS?|KILL|LANGUAGE|LAST|LEAVE|LEFT|LEVEL|LIMIT|LINENO|LINES|LINESTRING|LOAD|LOCAL|LOCK|LONG(?:BLOB|TEXT)|LOOP|MATCH(?:ED)?|MEDIUM(?:BLOB|INT|TEXT)|MERGE|MIDDLEINT|MINUTE|MODE|MODIFIES|MODIFY|MONTH|MULTI(?:LINESTRING|POINT|POLYGON)|NATIONAL|NATURAL|NCHAR|NEXT|NO|NONCLUSTERED|NULLIF|NUMERIC|OFF?|OFFSETS?|ON|OPEN(?:DATASOURCE|QUERY|ROWSET)?|OPTIMIZE|OPTION(?:ALLY)?|ORDER|OUT(?:ER|FILE)?|OVER|PARTIAL|PARTITION|PERCENT|PIVOT|PLAN|POINT|POLYGON|PRECEDING|PRECISION|PREPARE|PREV|PRIMARY|PRINT|PRIVILEGES|PROC(?:EDURE)?|PUBLIC|PURGE|QUICK|RAISERROR|READS?|REAL|RECONFIGURE|REFERENCES|RELEASE|RENAME|REPEAT(?:ABLE)?|REPLACE|REPLICATION|REQUIRE|RESIGNAL|RESTORE|RESTRICT|RETURN(?:ING|S)?|REVOKE|RIGHT|ROLLBACK|ROUTINE|ROW(?:COUNT|GUIDCOL|S)?|RTREE|RULE|SAVE(?:POINT)?|SCHEMA|SECOND|SELECT|SERIAL(?:IZABLE)?|SESSION(?:_USER)?|SET(?:USER)?|SHARE|SHOW|SHUTDOWN|SIMPLE|SMALLINT|SNAPSHOT|SOME|SONAME|SQL|START(?:ING)?|STATISTICS|STATUS|STRIPED|SYSTEM_USER|TABLES?|TABLESPACE|TEMP(?:ORARY|TABLE)?|TERMINATED|TEXT(?:SIZE)?|THEN|TIME(?:STAMP)?|TINY(?:BLOB|INT|TEXT)|TOP?|TRAN(?:SACTIONS?)?|TRIGGER|TRUNCATE|TSEQUAL|TYPES?|UNBOUNDED|UNCOMMITTED|UNDEFINED|UNION|UNIQUE|UNLOCK|UNPIVOT|UNSIGNED|UPDATE(?:TEXT)?|USAGE|USE|USER|USING|VALUES?|VAR(?:BINARY|CHAR|CHARACTER|YING)|VIEW|WAITFOR|WARNINGS|WHEN|WHERE|WHILE|WITH(?: ROLLUP|IN)?|WORK|WRITE(?:TEXT)?|YEAR)\b/i,boolean:/\b(?:FALSE|NULL|TRUE)\b/i,number:/\b0x[\da-f]+\b|\b\d+(?:\.\d*)?|\B\.\d+\b/i,operator:/[-+*\/=%^~]|&&?|\|\|?|!=?|<(?:=>?|<|>)?|>[>=]?|\b(?:AND|BETWEEN|DIV|ILIKE|IN|IS|LIKE|NOT|OR|REGEXP|RLIKE|SOUNDS LIKE|XOR)\b/i,punctuation:/[;[\]()`,.]/},function(e){e.languages.typescript=e.languages.extend("javascript",{"class-name":{pattern:/(\b(?:class|extends|implements|instanceof|interface|new|type)\s+)(?!keyof\b)(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?:\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>)?/,lookbehind:!0,greedy:!0,inside:null},builtin:/\b(?:Array|Function|Promise|any|boolean|console|never|number|string|symbol|unknown)\b/}),e.languages.typescript.keyword.push(/\b(?:abstract|declare|is|keyof|readonly|require)\b/,/\b(?:asserts|infer|interface|module|namespace|type)\b(?=\s*(?:[{_$a-zA-Z\xA0-\uFFFF]|$))/,/\btype\b(?=\s*(?:[\{*]|$))/),delete e.languages.typescript.parameter,delete e.languages.typescript["literal-property"];var t=e.languages.extend("typescript",{});delete t["class-name"],e.languages.typescript["class-name"].inside=t,e.languages.insertBefore("typescript","function",{decorator:{pattern:/@[$\w\xA0-\uFFFF]+/,inside:{at:{pattern:/^@/,alias:"operator"},function:/^[\s\S]+/}},"generic-function":{pattern:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>(?=\s*\()/,greedy:!0,inside:{function:/^#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*/,generic:{pattern:/<[\s\S]+/,alias:"class-name",inside:t}}}}),e.languages.ts=e.languages.typescript}(Prism),function(e){var t=e.util.clone(e.languages.javascript),n=/(?:\s|\/\/.*(?!.)|\/\*(?:[^*]|\*(?!\/))\*\/)/.source,r=/(?:\{(?:\{(?:\{[^{}]*\}|[^{}])*\}|[^{}])*\})/.source,a=/(?:\{<S>*\.{3}(?:[^{}]|<BRACES>)*\})/.source;function o(e,t){return e=e.replace(/<S>/g,(function(){return n})).replace(/<BRACES>/g,(function(){return r})).replace(/<SPREAD>/g,(function(){return a})),RegExp(e,t)}a=o(a).source,e.languages.jsx=e.languages.extend("markup",t),e.languages.jsx.tag.pattern=o(/<\/?(?:[\w.:-]+(?:<S>+(?:[\w.:$-]+(?:=(?:"(?:\\[\s\S]|[^\\"])*"|'(?:\\[\s\S]|[^\\'])*'|[^\s{'"/>=]+|<BRACES>))?|<SPREAD>))*<S>*\/?)?>/.source),e.languages.jsx.tag.inside.tag.pattern=/^<\/?[^\s>\/]*/,e.languages.jsx.tag.inside["attr-value"].pattern=/=(?!\{)(?:"(?:\\[\s\S]|[^\\"])*"|'(?:\\[\s\S]|[^\\'])*'|[^\s'">]+)/,e.languages.jsx.tag.inside.tag.inside["class-name"]=/^[A-Z]\w*(?:\.[A-Z]\w*)*$/,e.languages.jsx.tag.inside.comment=t.comment,e.languages.insertBefore("inside","attr-name",{spread:{pattern:o(/<SPREAD>/.source),inside:e.languages.jsx}},e.languages.jsx.tag),e.languages.insertBefore("inside","special-attr",{script:{pattern:o(/=<BRACES>/.source),alias:"language-javascript",inside:{"script-punctuation":{pattern:/^=(?=\{)/,alias:"punctuation"},rest:e.languages.jsx}}},e.languages.jsx.tag);var i=function(e){return e?"string"==typeof e?e:"string"==typeof e.content?e.content:e.content.map(i).join(""):""},s=function(t){for(var n=[],r=0;r<t.length;r++){var a=t[r],o=!1;if("string"!=typeof a&&("tag"===a.type&&a.content[0]&&"tag"===a.content[0].type?"</"===a.content[0].content[0].content?n.length>0&&n[n.length-1].tagName===i(a.content[0].content[1])&&n.pop():"/>"===a.content[a.content.length-1].content||n.push({tagName:i(a.content[0].content[1]),openedBraces:0}):n.length>0&&"punctuation"===a.type&&"{"===a.content?n[n.length-1].openedBraces++:n.length>0&&n[n.length-1].openedBraces>0&&"punctuation"===a.type&&"}"===a.content?n[n.length-1].openedBraces--:o=!0),(o||"string"==typeof a)&&n.length>0&&0===n[n.length-1].openedBraces){var c=i(a);r<t.length-1&&("string"==typeof t[r+1]||"plain-text"===t[r+1].type)&&(c+=i(t[r+1]),t.splice(r+1,1)),r>0&&("string"==typeof t[r-1]||"plain-text"===t[r-1].type)&&(c=i(t[r-1])+c,t.splice(r-1,1),r--),t[r]=new e.Token("plain-text",c,null,c)}a.content&&"string"!=typeof a.content&&s(a.content)}};e.hooks.add("after-tokenize",(function(e){"jsx"!==e.language&&"tsx"!==e.language||s(e.tokens)}))}(Prism),function(e){var t=e.util.clone(e.languages.typescript);e.languages.tsx=e.languages.extend("jsx",t),delete e.languages.tsx.parameter,delete e.languages.tsx["literal-property"];var n=e.languages.tsx.tag;n.pattern=RegExp(/(^|[^\w$]|(?=<\/))/.source+"(?:"+n.pattern.source+")",n.pattern.flags),n.lookbehind=!0}(Prism),function(e){e.languages.sass=e.languages.extend("css",{comment:{pattern:/^([ \t]*)\/[\/*].*(?:(?:\r?\n|\r)\1[ \t].+)*/m,lookbehind:!0,greedy:!0}}),e.languages.insertBefore("sass","atrule",{"atrule-line":{pattern:/^(?:[ \t]*)[@+=].+/m,greedy:!0,inside:{atrule:/(?:@[\w-]+|[+=])/}}}),delete e.languages.sass.atrule;var t=/\$[-\w]+|#\{\$[-\w]+\}/,n=[/[+*\/%]|[=!]=|<=?|>=?|\b(?:and|not|or)\b/,{pattern:/(\s)-(?=\s)/,lookbehind:!0}];e.languages.insertBefore("sass","property",{"variable-line":{pattern:/^[ \t]*\$.+/m,greedy:!0,inside:{punctuation:/:/,variable:t,operator:n}},"property-line":{pattern:/^[ \t]*(?:[^:\s]+ *:.*|:[^:\s].*)/m,greedy:!0,inside:{property:[/[^:\s]+(?=\s*:)/,{pattern:/(:)[^:\s]+/,lookbehind:!0}],punctuation:/:/,variable:t,operator:n,important:e.languages.sass.important}}}),delete e.languages.sass.property,delete e.languages.sass.important,e.languages.insertBefore("sass","punctuation",{selector:{pattern:/^([ \t]*)\S(?:,[^,\r\n]+|[^,\r\n]*)(?:,[^,\r\n]+)*(?:,(?:\r?\n|\r)\1[ \t]+\S(?:,[^,\r\n]+|[^,\r\n]*)(?:,[^,\r\n]+)*)*/m,lookbehind:!0,greedy:!0}})}(Prism),function(e){var t="\\b(?:BASH|BASHOPTS|BASH_ALIASES|BASH_ARGC|BASH_ARGV|BASH_CMDS|BASH_COMPLETION_COMPAT_DIR|BASH_LINENO|BASH_REMATCH|BASH_SOURCE|BASH_VERSINFO|BASH_VERSION|COLORTERM|COLUMNS|COMP_WORDBREAKS|DBUS_SESSION_BUS_ADDRESS|DEFAULTS_PATH|DESKTOP_SESSION|DIRSTACK|DISPLAY|EUID|GDMSESSION|GDM_LANG|GNOME_KEYRING_CONTROL|GNOME_KEYRING_PID|GPG_AGENT_INFO|GROUPS|HISTCONTROL|HISTFILE|HISTFILESIZE|HISTSIZE|HOME|HOSTNAME|HOSTTYPE|IFS|INSTANCE|JOB|LANG|LANGUAGE|LC_ADDRESS|LC_ALL|LC_IDENTIFICATION|LC_MEASUREMENT|LC_MONETARY|LC_NAME|LC_NUMERIC|LC_PAPER|LC_TELEPHONE|LC_TIME|LESSCLOSE|LESSOPEN|LINES|LOGNAME|LS_COLORS|MACHTYPE|MAILCHECK|MANDATORY_PATH|NO_AT_BRIDGE|OLDPWD|OPTERR|OPTIND|ORBIT_SOCKETDIR|OSTYPE|PAPERSIZE|PATH|PIPESTATUS|PPID|PS1|PS2|PS3|PS4|PWD|RANDOM|REPLY|SECONDS|SELINUX_INIT|SESSION|SESSIONTYPE|SESSION_MANAGER|SHELL|SHELLOPTS|SHLVL|SSH_AUTH_SOCK|TERM|UID|UPSTART_EVENTS|UPSTART_INSTANCE|UPSTART_JOB|UPSTART_SESSION|USER|WINDOWID|XAUTHORITY|XDG_CONFIG_DIRS|XDG_CURRENT_DESKTOP|XDG_DATA_DIRS|XDG_GREETER_DATA_DIR|XDG_MENU_PREFIX|XDG_RUNTIME_DIR|XDG_SEAT|XDG_SEAT_PATH|XDG_SESSION_DESKTOP|XDG_SESSION_ID|XDG_SESSION_PATH|XDG_SESSION_TYPE|XDG_VTNR|XMODIFIERS)\\b",n={pattern:/(^(["']?)\w+\2)[ \t]+\S.*/,lookbehind:!0,alias:"punctuation",inside:null},r={bash:n,environment:{pattern:RegExp("\\$"+t),alias:"constant"},variable:[{pattern:/\$?\(\([\s\S]+?\)\)/,greedy:!0,inside:{variable:[{pattern:/(^\$\(\([\s\S]+)\)\)/,lookbehind:!0},/^\$\(\(/],number:/\b0x[\dA-Fa-f]+\b|(?:\b\d+(?:\.\d*)?|\B\.\d+)(?:[Ee]-?\d+)?/,operator:/--|\+\+|\*\*=?|<<=?|>>=?|&&|\|\||[=!+\-*/%<>^&|]=?|[?~:]/,punctuation:/\(\(?|\)\)?|,|;/}},{pattern:/\$\((?:\([^)]+\)|[^()])+\)|`[^`]+`/,greedy:!0,inside:{variable:/^\$\(|^`|\)$|`$/}},{pattern:/\$\{[^}]+\}/,greedy:!0,inside:{operator:/:[-=?+]?|[!\/]|##?|%%?|\^\^?|,,?/,punctuation:/[\[\]]/,environment:{pattern:RegExp("(\\{)"+t),lookbehind:!0,alias:"constant"}}},/\$(?:\w+|[#?*!@$])/],entity:/\\(?:[abceEfnrtv\\"]|O?[0-7]{1,3}|U[0-9a-fA-F]{8}|u[0-9a-fA-F]{4}|x[0-9a-fA-F]{1,2})/};e.languages.bash={shebang:{pattern:/^#!\s*\/.*/,alias:"important"},comment:{pattern:/(^|[^"{\\$])#.*/,lookbehind:!0},"function-name":[{pattern:/(\bfunction\s+)[\w-]+(?=(?:\s*\(?:\s*\))?\s*\{)/,lookbehind:!0,alias:"function"},{pattern:/\b[\w-]+(?=\s*\(\s*\)\s*\{)/,alias:"function"}],"for-or-select":{pattern:/(\b(?:for|select)\s+)\w+(?=\s+in\s)/,alias:"variable",lookbehind:!0},"assign-left":{pattern:/(^|[\s;|&]|[<>]\()\w+(?=\+?=)/,inside:{environment:{pattern:RegExp("(^|[\\s;|&]|[<>]\\()"+t),lookbehind:!0,alias:"constant"}},alias:"variable",lookbehind:!0},string:[{pattern:/((?:^|[^<])<<-?\s*)(\w+)\s[\s\S]*?(?:\r?\n|\r)\2/,lookbehind:!0,greedy:!0,inside:r},{pattern:/((?:^|[^<])<<-?\s*)(["'])(\w+)\2\s[\s\S]*?(?:\r?\n|\r)\3/,lookbehind:!0,greedy:!0,inside:{bash:n}},{pattern:/(^|[^\\](?:\\\\)*)"(?:\\[\s\S]|\$\([^)]+\)|\$(?!\()|`[^`]+`|[^"\\`$])*"/,lookbehind:!0,greedy:!0,inside:r},{pattern:/(^|[^$\\])'[^']*'/,lookbehind:!0,greedy:!0},{pattern:/\$'(?:[^'\\]|\\[\s\S])*'/,greedy:!0,inside:{entity:r.entity}}],environment:{pattern:RegExp("\\$?"+t),alias:"constant"},variable:r.variable,function:{pattern:/(^|[\s;|&]|[<>]\()(?:add|apropos|apt|apt-cache|apt-get|aptitude|aspell|automysqlbackup|awk|basename|bash|bc|bconsole|bg|bzip2|cal|cat|cfdisk|chgrp|chkconfig|chmod|chown|chroot|cksum|clear|cmp|column|comm|composer|cp|cron|crontab|csplit|curl|cut|date|dc|dd|ddrescue|debootstrap|df|diff|diff3|dig|dir|dircolors|dirname|dirs|dmesg|docker|docker-compose|du|egrep|eject|env|ethtool|expand|expect|expr|fdformat|fdisk|fg|fgrep|file|find|fmt|fold|format|free|fsck|ftp|fuser|gawk|git|gparted|grep|groupadd|groupdel|groupmod|groups|grub-mkconfig|gzip|halt|head|hg|history|host|hostname|htop|iconv|id|ifconfig|ifdown|ifup|import|install|ip|jobs|join|kill|killall|less|link|ln|locate|logname|logrotate|look|lpc|lpr|lprint|lprintd|lprintq|lprm|ls|lsof|lynx|make|man|mc|mdadm|mkconfig|mkdir|mke2fs|mkfifo|mkfs|mkisofs|mknod|mkswap|mmv|more|most|mount|mtools|mtr|mutt|mv|nano|nc|netstat|nice|nl|node|nohup|notify-send|npm|nslookup|op|open|parted|passwd|paste|pathchk|ping|pkill|pnpm|podman|podman-compose|popd|pr|printcap|printenv|ps|pushd|pv|quota|quotacheck|quotactl|ram|rar|rcp|reboot|remsync|rename|renice|rev|rm|rmdir|rpm|rsync|scp|screen|sdiff|sed|sendmail|seq|service|sftp|sh|shellcheck|shuf|shutdown|sleep|slocate|sort|split|ssh|stat|strace|su|sudo|sum|suspend|swapon|sync|tac|tail|tar|tee|time|timeout|top|touch|tr|traceroute|tsort|tty|umount|uname|unexpand|uniq|units|unrar|unshar|unzip|update-grub|uptime|useradd|userdel|usermod|users|uudecode|uuencode|v|vcpkg|vdir|vi|vim|virsh|vmstat|wait|watch|wc|wget|whereis|which|who|whoami|write|xargs|xdg-open|yarn|yes|zenity|zip|zsh|zypper)(?=$|[)\s;|&])/,lookbehind:!0},keyword:{pattern:/(^|[\s;|&]|[<>]\()(?:case|do|done|elif|else|esac|fi|for|function|if|in|select|then|until|while)(?=$|[)\s;|&])/,lookbehind:!0},builtin:{pattern:/(^|[\s;|&]|[<>]\()(?:\.|:|alias|bind|break|builtin|caller|cd|command|continue|declare|echo|enable|eval|exec|exit|export|getopts|hash|help|let|local|logout|mapfile|printf|pwd|read|readarray|readonly|return|set|shift|shopt|source|test|times|trap|type|typeset|ulimit|umask|unalias|unset)(?=$|[)\s;|&])/,lookbehind:!0,alias:"class-name"},boolean:{pattern:/(^|[\s;|&]|[<>]\()(?:false|true)(?=$|[)\s;|&])/,lookbehind:!0},"file-descriptor":{pattern:/\B&\d\b/,alias:"important"},operator:{pattern:/\d?<>|>\||\+=|=[=~]?|!=?|<<[<-]?|[&\d]?>>|\d[<>]&?|[<>][&=]?|&[>&]?|\|[&|]?/,inside:{"file-descriptor":{pattern:/^\d/,alias:"important"}}},punctuation:/\$?\(\(?|\)\)?|\.\.|[{}[\];\\]/,number:{pattern:/(^|\s)(?:[1-9]\d*|0)(?:[.,]\d+)?\b/,lookbehind:!0}},n.inside=e.languages.bash;for(var a=["comment","function-name","for-or-select","assign-left","string","environment","function","keyword","builtin","boolean","file-descriptor","operator","punctuation","number"],o=r.variable[1].inside,i=0;i<a.length;i++)o[a[i]]=e.languages.bash[a[i]];e.languages.shell=e.languages.bash}(Prism),Prism.languages.swift={comment:{pattern:/(^|[^\\:])(?:\/\/.*|\/\*(?:[^/*]|\/(?!\*)|\*(?!\/)|\/\*(?:[^*]|\*(?!\/))*\*\/)*\*\/)/,lookbehind:!0,greedy:!0},"string-literal":[{pattern:RegExp(/(^|[^"#])/.source+"(?:"+/"(?:\\(?:\((?:[^()]|\([^()]*\))*\)|\r\n|[^(])|[^\\\r\n"])*"/.source+"|"+/"""(?:\\(?:\((?:[^()]|\([^()]*\))*\)|[^(])|[^\\"]|"(?!""))*"""/.source+")"+/(?!["#])/.source),lookbehind:!0,greedy:!0,inside:{interpolation:{pattern:/(\\\()(?:[^()]|\([^()]*\))*(?=\))/,lookbehind:!0,inside:null},"interpolation-punctuation":{pattern:/^\)|\\\($/,alias:"punctuation"},punctuation:/\\(?=[\r\n])/,string:/[\s\S]+/}},{pattern:RegExp(/(^|[^"#])(#+)/.source+"(?:"+/"(?:\\(?:#+\((?:[^()]|\([^()]*\))*\)|\r\n|[^#])|[^\\\r\n])*?"/.source+"|"+/"""(?:\\(?:#+\((?:[^()]|\([^()]*\))*\)|[^#])|[^\\])*?"""/.source+")\\2"),lookbehind:!0,greedy:!0,inside:{interpolation:{pattern:/(\\#+\()(?:[^()]|\([^()]*\))*(?=\))/,lookbehind:!0,inside:null},"interpolation-punctuation":{pattern:/^\)|\\#+\($/,alias:"punctuation"},string:/[\s\S]+/}}],directive:{pattern:RegExp(/#/.source+"(?:"+/(?:elseif|if)\b/.source+"(?:[ \t]*"+/(?:![ \t]*)?(?:\b\w+\b(?:[ \t]*\((?:[^()]|\([^()]*\))*\))?|\((?:[^()]|\([^()]*\))*\))(?:[ \t]*(?:&&|\|\|))?/.source+")+|"+/(?:else|endif)\b/.source+")"),alias:"property",inside:{"directive-name":/^#\w+/,boolean:/\b(?:false|true)\b/,number:/\b\d+(?:\.\d+)*\b/,operator:/!|&&|\|\||[<>]=?/,punctuation:/[(),]/}},literal:{pattern:/#(?:colorLiteral|column|dsohandle|file(?:ID|Literal|Path)?|function|imageLiteral|line)\b/,alias:"constant"},"other-directive":{pattern:/#\w+\b/,alias:"property"},attribute:{pattern:/@\w+/,alias:"atrule"},"function-definition":{pattern:/(\bfunc\s+)\w+/,lookbehind:!0,alias:"function"},label:{pattern:/\b(break|continue)\s+\w+|\b[a-zA-Z_]\w*(?=\s*:\s*(?:for|repeat|while)\b)/,lookbehind:!0,alias:"important"},keyword:/\b(?:Any|Protocol|Self|Type|actor|as|assignment|associatedtype|associativity|async|await|break|case|catch|class|continue|convenience|default|defer|deinit|didSet|do|dynamic|else|enum|extension|fallthrough|fileprivate|final|for|func|get|guard|higherThan|if|import|in|indirect|infix|init|inout|internal|is|isolated|lazy|left|let|lowerThan|mutating|none|nonisolated|nonmutating|open|operator|optional|override|postfix|precedencegroup|prefix|private|protocol|public|repeat|required|rethrows|return|right|safe|self|set|some|static|struct|subscript|super|switch|throw|throws|try|typealias|unowned|unsafe|var|weak|where|while|willSet)\b/,boolean:/\b(?:false|true)\b/,nil:{pattern:/\bnil\b/,alias:"constant"},"short-argument":/\$\d+\b/,omit:{pattern:/\b_\b/,alias:"keyword"},number:/\b(?:[\d_]+(?:\.[\de_]+)?|0x[a-f0-9_]+(?:\.[a-f0-9p_]+)?|0b[01_]+|0o[0-7_]+)\b/i,"class-name":/\b[A-Z](?:[A-Z_\d]*[a-z]\w*)?\b/,function:/\b[a-z_]\w*(?=\s*\()/i,constant:/\b(?:[A-Z_]{2,}|k[A-Z][A-Za-z_]+)\b/,operator:/[-+*/%=!<>&|^~?]+|\.[.\-+*/%=!<>&|^~?]+/,punctuation:/[{}[\]();,.:\\]/},Prism.languages.swift["string-literal"].forEach((function(e){e.inside.interpolation.inside=Prism.languages.swift})),Prism.languages["visual-basic"]={comment:{pattern:/(?:['‘’]|REM\b)(?:[^\r\n_]|_(?:\r\n?|\n)?)*/i,inside:{keyword:/^REM/i}},directive:{pattern:/#(?:Const|Else|ElseIf|End|ExternalChecksum|ExternalSource|If|Region)(?:\b_[ \t]*(?:\r\n?|\n)|.)+/i,alias:"property",greedy:!0},string:{pattern:/\$?["“”](?:["“”]{2}|[^"“”])*["“”]C?/i,greedy:!0},date:{pattern:/#[ \t]*(?:\d+([/-])\d+\1\d+(?:[ \t]+(?:\d+[ \t]*(?:AM|PM)|\d+:\d+(?::\d+)?(?:[ \t]*(?:AM|PM))?))?|\d+[ \t]*(?:AM|PM)|\d+:\d+(?::\d+)?(?:[ \t]*(?:AM|PM))?)[ \t]*#/i,alias:"number"},number:/(?:(?:\b\d+(?:\.\d+)?|\.\d+)(?:E[+-]?\d+)?|&[HO][\dA-F]+)(?:[FRD]|U?[ILS])?/i,boolean:/\b(?:False|Nothing|True)\b/i,keyword:/\b(?:AddHandler|AddressOf|Alias|And(?:Also)?|As|Boolean|ByRef|Byte|ByVal|Call|Case|Catch|C(?:Bool|Byte|Char|Date|Dbl|Dec|Int|Lng|Obj|SByte|Short|Sng|Str|Type|UInt|ULng|UShort)|Char|Class|Const|Continue|Currency|Date|Decimal|Declare|Default|Delegate|Dim|DirectCast|Do|Double|Each|Else(?:If)?|End(?:If)?|Enum|Erase|Error|Event|Exit|Finally|For|Friend|Function|Get(?:Type|XMLNamespace)?|Global|GoSub|GoTo|Handles|If|Implements|Imports|In|Inherits|Integer|Interface|Is|IsNot|Let|Lib|Like|Long|Loop|Me|Mod|Module|Must(?:Inherit|Override)|My(?:Base|Class)|Namespace|Narrowing|New|Next|Not(?:Inheritable|Overridable)?|Object|Of|On|Operator|Option(?:al)?|Or(?:Else)?|Out|Overloads|Overridable|Overrides|ParamArray|Partial|Private|Property|Protected|Public|RaiseEvent|ReadOnly|ReDim|RemoveHandler|Resume|Return|SByte|Select|Set|Shadows|Shared|short|Single|Static|Step|Stop|String|Structure|Sub|SyncLock|Then|Throw|To|Try|TryCast|Type|TypeOf|U(?:Integer|Long|Short)|Until|Using|Variant|Wend|When|While|Widening|With(?:Events)?|WriteOnly|Xor)\b/i,operator:/[+\-*/\\^<=>&#@$%!]|\b_(?=[ \t]*[\r\n])/,punctuation:/[{}().,:?]/},Prism.languages.vb=Prism.languages["visual-basic"],Prism.languages.vba=Prism.languages["visual-basic"];var vf=s.RangeError,yf=String.fromCharCode,_f=String.fromCodePoint,kf=v([].join),wf=!!_f&&1!=_f.length;Ye({target:"String",stat:!0,arity:1,forced:wf},{fromCodePoint:function(e){for(var t,n=[],r=arguments.length,a=0;r>a;){if(t=+arguments[a++],Ot(t,1114111)!==t)throw vf(t+" is not a valid code point");n[a]=t<65536?yf(t):yf(55296+((t-=65536)>>10),t%1024+56320)}return kf(n,"")}});var Ef,xf,Sf=j.String.fromCodePoint;function Af(e,t){var n=Ws(e);if(Hu){var r=Hu(e);t&&(r=Ys(r).call(r,(function(t){return qu(e,t).enumerable}))),n.push.apply(n,r)}return n}function Cf(e){for(var t=1;t<arguments.length;t++){var n,r,a=null!=arguments[t]?arguments[t]:{};t%2?zs(n=Af(Object(a),!0)).call(n,(function(t){eo(e,t,a[t])})):Zu?Xu(e,Zu(a)):zs(r=Af(Object(a))).call(r,(function(t){Cs(e,t,qu(a,t))}))}return e}var Tf={"<":"&lt;",">":"&gt;","&":"&amp;",'"':"&quot;","'":"&#x27;"},Of={lt:"<",gt:">",amp:"&",quot:'"',apos:"'"},$f=Cf(Cf(Cf(Cf(Cf(Cf({},{34:"&quot;",38:"&amp;",39:"&apos;",60:"&lt;",62:"&gt;"}),{192:"&Agrave;",193:"&Aacute;",194:"&Acirc;",195:"&Atilde;",196:"&Auml;",197:"&Aring;",198:"&AElig;",199:"&Ccedil;",200:"&Egrave;",201:"&Eacute;",202:"&Ecirc;",203:"&Euml;",204:"&Igrave;",205:"&Iacute;",206:"&Icirc;",207:"&Iuml;",208:"&ETH;",209:"&Ntilde;",210:"&Ograve;",211:"&Oacute;",212:"&Ocirc;",213:"&Otilde;",214:"&Ouml;",216:"&Oslash;",217:"&Ugrave;",218:"&Uacute;",219:"&Ucirc;",220:"&Uuml;",221:"&Yacute;",222:"&THORN;",223:"&szlig;",224:"&agrave;",225:"&aacute;",226:"&acirc;",227:"&atilde;",228:"&auml;",229:"&aring;",230:"&aelig;",231:"&ccedil;",232:"&egrave;",233:"&eacute;",234:"&ecirc;",235:"&euml;",236:"&igrave;",237:"&iacute;",238:"&icirc;",239:"&iuml;",240:"&eth;",241:"&ntilde;",242:"&ograve;",243:"&oacute;",244:"&ocirc;",245:"&otilde;",246:"&ouml;",248:"&oslash;",249:"&ugrave;",250:"&uacute;",251:"&ucirc;",252:"&uuml;",253:"&yacute;",254:"&thorn;",255:"&yuml;"}),{160:"&nbsp;",161:"&iexcl;",162:"&cent;",163:"&pound;",164:"&curren;",165:"&yen;",166:"&brvbar;",167:"&sect;",168:"&uml;",169:"&copy;",170:"&ordf;",171:"&laquo;",172:"&not;",173:"&shy;",174:"&reg;",175:"&macr;",176:"&deg;",177:"&plusmn;",178:"&sup2;",179:"&sup3;",180:"&acute;",181:"&micro;",182:"&para;",184:"&cedil;",185:"&sup1;",186:"&ordm;",187:"&raquo;",188:"&frac14;",189:"&frac12;",190:"&frac34;",191:"&iquest;",215:"&times;",247:"&divide;"}),{8704:"&forall;",8706:"&part;",8707:"&exist;",8709:"&empty;",8711:"&nabla;",8712:"&isin;",8713:"&notin;",8715:"&ni;",8719:"&prod;",8721:"&sum;",8722:"&minus;",8727:"&lowast;",8730:"&radic;",8733:"&prop;",8734:"&infin;",8736:"&ang;",8743:"&and;",8744:"&or;",8745:"&cap;",8746:"&cup;",8747:"&int;",8756:"&there4;",8764:"&sim;",8773:"&cong;",8776:"&asymp;",8800:"&ne;",8801:"&equiv;",8804:"&le;",8805:"&ge;",8834:"&sub;",8835:"&sup;",8836:"&nsub;",8838:"&sube;",8839:"&supe;",8853:"&oplus;",8855:"&otimes;",8869:"&perp;",8901:"&sdot;"}),{913:"&Alpha;",914:"&Beta;",915:"&Gamma;",916:"&Delta;",917:"&Epsilon;",918:"&Zeta;",919:"&Eta;",920:"&Theta;",921:"&Iota;",922:"&Kappa;",923:"&Lambda;",924:"&Mu;",925:"&Nu;",926:"&Xi;",927:"&Omicron;",928:"&Pi;",929:"&Rho;",931:"&Sigma;",932:"&Tau;",933:"&Upsilon;",934:"&Phi;",935:"&Chi;",936:"&Psi;",937:"&Omega;",945:"&alpha;",946:"&beta;",947:"&gamma;",948:"&delta;",949:"&epsilon;",950:"&zeta;",951:"&eta;",952:"&theta;",953:"&iota;",954:"&kappa;",955:"&lambda;",956:"&mu;",957:"&nu;",958:"&xi;",959:"&omicron;",960:"&pi;",961:"&rho;",962:"&sigmaf;",963:"&sigma;",964:"&tau;",965:"&upsilon;",966:"&phi;",967:"&chi;",968:"&psi;",969:"&omega;",977:"&thetasym;",978:"&upsih;",982:"&piv;"}),{338:"&OElig;",339:"&oelig;",352:"&Scaron;",353:"&scaron;",376:"&Yuml;",402:"&fnof;",710:"&circ;",732:"&tilde;",8194:"&ensp;",8195:"&emsp;",8201:"&thinsp;",8204:"&zwnj;",8205:"&zwj;",8206:"&lrm;",8207:"&rlm;",8211:"&ndash;",8212:"&mdash;",8216:"&lsquo;",8217:"&rsquo;",8218:"&sbquo;",8220:"&ldquo;",8221:"&rdquo;",8222:"&bdquo;",8224:"&dagger;",8225:"&Dagger;",8226:"&bull;",8230:"&hellip;",8240:"&permil;",8242:"&prime;",8243:"&Prime;",8249:"&lsaquo;",8250:"&rsaquo;",8254:"&oline;",8364:"&euro;",8482:"&trade;",8592:"&larr;",8593:"&uarr;",8594:"&rarr;",8595:"&darr;",8596:"&harr;",8629:"&crarr;",8968:"&lceil;",8969:"&rceil;",8970:"&lfloor;",8971:"&rfloor;",9674:"&loz;",9824:"&spades;",9827:"&clubs;",9829:"&hearts;",9830:"&diams;"}),Rf=Ws($f),Pf=mc(Rf).call(Rf,(function(e){return $f[e].replace(/^&(\w+);$/g,(function(e,t){return t.toLowerCase()}))})),If=function(e){return"string"!=typeof e||e.length<=0},Lf=function(e){try{var t=Sf(e);return!If(t)}catch(e){return!1}};var Nf=["h1|h2|h3|h4|h5|h6","ul|ol|li|dd|dl|dt","table|thead|tbody|tfoot|col|colgroup|th|td|tr","div|article|section|footer|aside|details|summary|code|audio|video|canvas|figure","address|center|cite|p|pre|blockquote|marquee|caption|figcaption|track|source|output|svg"].join("|"),Mf=["span|a|link|b|s|i|del|u|em|strong|sup|sub|kbd","nav|font|bdi|samp|map|area|small|time|bdo|var|wbr|meter|dfn","ruby|rt|rp|mark|q|progress|input|textarea|select|ins"].join("|"),jf=new RegExp(Ns(Ef=Ns(xf="^(".concat(Nf,"|")).call(xf,Mf,"|")).call(Ef,"br|img|hr",")( |$|/)"),"i");function Df(e,t){return"string"!=typeof e?"":t?e.replace(/[<>&]/g,(function(e){return Tf[e]||e})):e.replace(/[<>&"']/g,(function(e){return Tf[e]||e}))}function Bf(e,t){if("string"!=typeof e)return"";var n=Ff(e);return Df(n=function(e){return"string"!=typeof e?"":e.replace(/&(\w+);?/g,(function(e,t){return Of[t]||e}))}(n),t)}function Ff(e){return e.replace(/&#(\d+);?/g,(function(e,t){return $f[t]||e}))}function Hf(e){var t=function(e){return e.replace(/&#x([0-9a-f]+);?/gi,(function(e,t){var n=Mc("0x".concat(t),16);try{return Sf(n)}catch(t){return e}}))}(function(e){return e.replace(/&#(\d+);?/g,(function(e,t){try{return Sf(t)}catch(t){return e}}))}(e)).match(/^\s*([\w\W]+?)(?=:)/i);if(!t)return!0;var n=["javascript","data"],r=t[1].replace(/[\s]/g,"");return-1===mf(n).call(n,r.toLowerCase())}function zf(e){return encodeURI(e).replace(/[!'()*]/g,(function(e){return"%".concat(e.charCodeAt(0).toString(16))})).replace(/%25/g,"%")}function Uf(e,t){var n=Ws(e);if(Hu){var r=Hu(e);t&&(r=Ys(r).call(r,(function(t){return qu(e,t).enumerable}))),n.push.apply(n,r)}return n}function Wf(e){var t=function(){if("undefined"==typeof Reflect||!an)return!1;if(an.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(an(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Qa(e);if(t){var a=Qa(this).constructor;n=an(r,arguments,a)}else n=r.apply(this,arguments);return Ya(this,n)}}bf.manual=!0;var qf={figure:"figure"},Gf=function(e){yn(n,e);var t=Wf(n);function n(e){e.externals;var r,a,o=e.config;(un(this,n),r=t.call(this,{needCache:!0}),n.inlineCodeCache={},r.codeCache={},r.customLang=[],r.customParser={},r.wrap=o.wrap,r.lineNumber=o.lineNumber,r.indentedCodeBlock=void 0===o.indentedCodeBlock||o.indentedCodeBlock,o&&o.customRenderer)&&(r.customLang=mc(a=Ws(o.customRenderer)).call(a,(function(e){return e.toLowerCase()})),r.customParser=function(e){for(var t=1;t<arguments.length;t++){var n,r,a=null!=arguments[t]?arguments[t]:{};t%2?zs(n=Uf(Object(a),!0)).call(n,(function(t){eo(e,t,a[t])})):Zu?Xu(e,Zu(a)):zs(r=Uf(Object(a))).call(r,(function(t){Cs(e,t,qu(a,t))}))}return e}({},o.customRenderer));return r.customHighlighter=o.highlighter,r}return ln(n,[{key:"$codeCache",value:function(e,t){return e&&t&&(this.codeCache[e]=t),this.codeCache[e]?this.codeCache[e]:(this.codeCache.length>40&&(this.codeCache.length=0),!1)}},{key:"parseCustomLanguage",value:function(e,t,n){var r,a,o,i,s,c=this.customParser[e];if(!c||"function"!=typeof c.render)return!1;var l=c.render(t,n.sign,this.$engine);if(!l)return!1;var u=qf[c.constructor.TYPE]||"div";return Ns(r=Ns(a=Ns(o=Ns(i=Ns(s="<".concat(u,' data-sign="')).call(s,n.sign,'" data-type="')).call(i,e,'" data-lines="')).call(o,n.lines,'">')).call(a,l,"</")).call(r,u,">")}},{key:"fillTag",value:function(e){var t=[];return mc(e).call(e,(function(e){if(!e)return"";for(var n=e;t.length;){var r,a=t.pop();n=Ns(r="".concat(a)).call(r,n)}var o=n.match(/<span class="(.+?)">|<\/span>/g),i=0;if(!o)return n;for(;o.length;){var s=o.pop();/<\/span>/.test(s)?i+=1:i?i-=1:t.unshift(s.match(/<span class="(.+?)">/)[0])}for(var c=0;c<t.length;c++)n="".concat(n,"</span>");return n}))}},{key:"renderLineNumber",value:function(e){if(!this.lineNumber)return e;var t=e.split("\n");return t.pop(),t=this.fillTag(t),'<span class="code-line">'.concat(t.join('</span>\n<span class="code-line">'),"</span>")}},{key:"isInternalCustomLangCovered",value:function(e){var t;return-1!==mf(t=this.customLang).call(t,e)}},{key:"computeLines",value:function(e,t,n){var r=t,a=this.getLineCount(e,r);return{sign:this.$engine.md5(e.replace(/^\n+/,"")+a),lines:a}}},{key:"appendMermaid",value:function(e,t){var n=e,r=t;if(/^flow([ ](TD|LR))?$/i.test(r)&&!this.isInternalCustomLangCovered(r)){var a,o=r.match(/^flow(?:[ ](TD|LR))?$/i)||[];n=Ns(a="graph ".concat(o[1]||"TD","\n")).call(a,n),r="mermaid"}return/^seq$/i.test(r)&&!this.isInternalCustomLangCovered(r)&&(n="sequenceDiagram\n".concat(n),r="mermaid"),"mermaid"===r&&(n=(n=n.replace(/(^[\s]*)stateDiagram-v2\n/,"$1stateDiagram\n")).replace(/(^[\s]*)sequenceDiagram[ \t]*\n[\s]*autonumber[ \t]*\n/,"$1sequenceDiagram\n")),[n,r]}},{key:"wrapCode",value:function(e,t){var n,r;return Ns(n=Ns(r='<code class="language-'.concat(t)).call(r,this.wrap?" wrap":"",'">')).call(n,e,"</code>")}},{key:"renderCodeBlock",value:function(e,t,n,r){var a,o,i,s=e,c=t;return this.customHighlighter?s=this.customHighlighter(s,c):(c&&bf.languages[c]||(c="javascript"),s=bf.highlight(s,bf.languages[c],c),s=this.renderLineNumber(s)),s=Ns(a=Ns(o=Ns(i='<div data-sign="'.concat(n,'" data-type="codeBlock" data-lines="')).call(i,r,'">\n      <div class="cherry-copy-code-block" style="display:none;"><i class="ch-icon ch-icon-copy" title="copy"></i></div>\n      <pre class="language-')).call(o,c,'">')).call(a,this.wrapCode(s,c),"</pre>\n    </div>")}},{key:"$getIndentedCodeReg",value:function(){return new RegExp("(?:^|\\n\\s*\\n)(?: {4}|\\t)"+"([\\s\\S]+?)"+"(?=$|\\n( {0,3}[^ \\t\\n]|\\n[^ \\t\\n]))","g")}},{key:"$getIndentCodeBlock",value:function(e){var t=this;return this.indentedCodeBlock?this.$recoverCodeInIndent(e).replace(this.$getIndentedCodeReg(),(function(e,n){var r,a,o=(e.match(/\n/g)||[]).length,i=t.$engine.md5(e),s=Ns(r=Ns(a='<pre data-sign="'.concat(i,'" data-lines="')).call(a,o,'"><code>')).call(r,Df(n.replace(/\n( {4}|\t)/g,"\n")),"</code></pre>");return jc(e,t.pushCache(s,i,o))})):e}},{key:"$replaceCodeInIndent",value:function(e){return this.indentedCodeBlock?e.replace(this.$getIndentedCodeReg(),(function(e){return e.replace(/`/g,"~~~IndentCode")})):e}},{key:"$recoverCodeInIndent",value:function(e){return this.indentedCodeBlock?e.replace(this.$getIndentedCodeReg(),(function(e){return e.replace(/~~~IndentCode/g,"`")})):e}},{key:"beforeMakeHtml",value:function(e,t,r){var a=this,o=e;o=(o=this.$replaceCodeInIndent(o)).replace(this.RULE.reg,(function(e,t,n,r){var o,i,s,c=r,l=a.computeLines(e,t,r),u=l.sign,f=l.lines,d=a.$codeCache(u);if(d&&""!==d)return a.getCacheWithSpace(a.pushCache(d,u,f),e);c=(c=(c=a.$recoverCodeInIndent(c)).replace(/~D/g,"$")).replace(/~T/g,"~");var p=null!==(o=null==t||null===(i=t.match(/[ ]/g))||void 0===i?void 0:i.length)&&void 0!==o?o:0;if(p>0){var h=new RegExp("(^|\\n)[ ]{1,".concat(p,"}"),"g");c=c.replace(h,"$1")}var g=Tc(n).call(n);if(/^(math|katex|latex)$/i.test(g)&&!a.isInternalCustomLangCovered(g)){var m,b=e.match(/^\s*/g);return Ns(m="".concat(b,"~D~D\n")).call(m,c,"~D~D")}var v=a.appendMermaid(c,g),y=gf(v,2);return c=y[0],g=y[1],-1!==mf(s=a.customLang).call(s,g.toLowerCase())&&(d=a.parseCustomLanguage(g,c,{lines:f,sign:u}))&&""!==d?(a.$codeCache(u,d),a.getCacheWithSpace(a.pushCache(d,u,f),e)):(c=c.replace(/~X/g,"\\`"),d=(d=a.renderCodeBlock(c,g,u,f)).replace(/\\/g,"\\\\"),d=a.$codeCache(u,d),a.getCacheWithSpace(a.pushCache(d,u,f),e))}));var i=/(`+)(.+?(?:\n.+?)*?)\1/g;return i.test(o)&&(o=(o=(o=o.replace(/\\`/g,"~~not~inlineCode")).replace(i,(function(e,t,r){if("`"===Tc(r).call(r))return e;var o=r.replace(/~~not~inlineCode/g,"\\`");o=(o=a.$replaceSpecialChar(o)).replace(/\\/g,"\\\\");var i="<code>".concat(Df(o),"</code>"),s=a.$engine.md5(i);return n.inlineCodeCache[s]=i,"~~CODE".concat(s,"$")}))).replace(/~~not~inlineCode/g,"\\`")),o=this.$getIndentCodeBlock(o)}},{key:"makeHtml",value:function(e){return e}},{key:"$replaceSpecialChar",value:function(e){var t=e.replace(/~Q/g,"\\~");return t=(t=(t=(t=t.replace(/~Y/g,"\\!")).replace(/~Z/g,"\\#")).replace(/~&/g,"\\&")).replace(/~K/g,"\\/")}},{key:"rule",value:function(){return(e={begin:/(?:^|\n)(\n*(?:[^\S\n]*))```([^`]*?)\n/,content:/([\w\W]*?)/,end:/[^\S\n]*```[ \t]*(?=$|\n+)/}).reg=new RegExp(e.begin.source+e.content.source+e.end.source,"g"),e;var e}},{key:"mounted",value:function(e){}}]),n}(Hc);function Kf(e){var t=function(){if("undefined"==typeof Reflect||!an)return!1;if(an.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(an(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Qa(e);if(t){var a=Qa(this).constructor;n=an(r,arguments,a)}else n=r.apply(this,arguments);return Ya(this,n)}}eo(Gf,"HOOK_NAME","codeBlock"),eo(Gf,"inlineCodeCache",{});var Zf=function(e){yn(n,e);var t=Kf(n);function n(){return un(this,n),t.apply(this,arguments)}return ln(n,[{key:"makeHtml",value:function(e){return e}},{key:"afterMakeHtml",value:function(e){var t=e;return Ws(Gf.inlineCodeCache).length>0&&(t=t.replace(/~~CODE([0-9a-zA-Z]+)\$/g,(function(e,t){return Gf.inlineCodeCache[t]})),Gf.inlineCodeCache={}),t}},{key:"rule",value:function(){var e={begin:"(`+)[ ]*",end:"[ ]*\\1",content:"(.+?(?:\\n.+?)*?)"};return e.reg=nu(e,"g"),e}}]),n}(Hc);eo(Zf,"HOOK_NAME","inlineCode");var Yf=r((function(e){!function(){var t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",n={rotl:function(e,t){return e<<t|e>>>32-t},rotr:function(e,t){return e<<32-t|e>>>t},endian:function(e){if(e.constructor==Number)return 16711935&n.rotl(e,8)|4278255360&n.rotl(e,24);for(var t=0;t<e.length;t++)e[t]=n.endian(e[t]);return e},randomBytes:function(e){for(var t=[];e>0;e--)t.push(Math.floor(256*Math.random()));return t},bytesToWords:function(e){for(var t=[],n=0,r=0;n<e.length;n++,r+=8)t[r>>>5]|=e[n]<<24-r%32;return t},wordsToBytes:function(e){for(var t=[],n=0;n<32*e.length;n+=8)t.push(e[n>>>5]>>>24-n%32&255);return t},bytesToHex:function(e){for(var t=[],n=0;n<e.length;n++)t.push((e[n]>>>4).toString(16)),t.push((15&e[n]).toString(16));return t.join("")},hexToBytes:function(e){for(var t=[],n=0;n<e.length;n+=2)t.push(parseInt(e.substr(n,2),16));return t},bytesToBase64:function(e){for(var n=[],r=0;r<e.length;r+=3)for(var a=e[r]<<16|e[r+1]<<8|e[r+2],o=0;o<4;o++)8*r+6*o<=8*e.length?n.push(t.charAt(a>>>6*(3-o)&63)):n.push("=");return n.join("")},base64ToBytes:function(e){e=e.replace(/[^A-Z0-9+\/]/gi,"");for(var n=[],r=0,a=0;r<e.length;a=++r%4)0!=a&&n.push((t.indexOf(e.charAt(r-1))&Math.pow(2,-2*a+8)-1)<<2*a|t.indexOf(e.charAt(r))>>>6-2*a);return n}};e.exports=n}()})),Xf={utf8:{stringToBytes:function(e){return Xf.bin.stringToBytes(unescape(encodeURIComponent(e)))},bytesToString:function(e){return decodeURIComponent(escape(Xf.bin.bytesToString(e)))}},bin:{stringToBytes:function(e){for(var t=[],n=0;n<e.length;n++)t.push(255&e.charCodeAt(n));return t},bytesToString:function(e){for(var t=[],n=0;n<e.length;n++)t.push(String.fromCharCode(e[n]));return t.join("")}}},Vf=Xf,Jf=function(e){return null!=e&&(Qf(e)||function(e){return"function"==typeof e.readFloatLE&&"function"==typeof e.slice&&Qf(e.slice(0,0))}(e)||!!e._isBuffer)};function Qf(e){return!!e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)}var ed=r((function(e){!function(){var t=Yf,n=Vf.utf8,r=Jf,a=Vf.bin,o=function(e,i){e.constructor==String?e=i&&"binary"===i.encoding?a.stringToBytes(e):n.stringToBytes(e):r(e)?e=Array.prototype.slice.call(e,0):Array.isArray(e)||e.constructor===Uint8Array||(e=e.toString());for(var s=t.bytesToWords(e),c=8*e.length,l=1732584193,u=-271733879,f=-1732584194,d=271733878,p=0;p<s.length;p++)s[p]=16711935&(s[p]<<8|s[p]>>>24)|4278255360&(s[p]<<24|s[p]>>>8);s[c>>>5]|=128<<c%32,s[14+(c+64>>>9<<4)]=c;var h=o._ff,g=o._gg,m=o._hh,b=o._ii;for(p=0;p<s.length;p+=16){var v=l,y=u,_=f,k=d;l=h(l,u,f,d,s[p+0],7,-680876936),d=h(d,l,u,f,s[p+1],12,-389564586),f=h(f,d,l,u,s[p+2],17,606105819),u=h(u,f,d,l,s[p+3],22,-1044525330),l=h(l,u,f,d,s[p+4],7,-176418897),d=h(d,l,u,f,s[p+5],12,1200080426),f=h(f,d,l,u,s[p+6],17,-1473231341),u=h(u,f,d,l,s[p+7],22,-45705983),l=h(l,u,f,d,s[p+8],7,1770035416),d=h(d,l,u,f,s[p+9],12,-1958414417),f=h(f,d,l,u,s[p+10],17,-42063),u=h(u,f,d,l,s[p+11],22,-1990404162),l=h(l,u,f,d,s[p+12],7,1804603682),d=h(d,l,u,f,s[p+13],12,-40341101),f=h(f,d,l,u,s[p+14],17,-1502002290),l=g(l,u=h(u,f,d,l,s[p+15],22,1236535329),f,d,s[p+1],5,-165796510),d=g(d,l,u,f,s[p+6],9,-1069501632),f=g(f,d,l,u,s[p+11],14,643717713),u=g(u,f,d,l,s[p+0],20,-373897302),l=g(l,u,f,d,s[p+5],5,-701558691),d=g(d,l,u,f,s[p+10],9,38016083),f=g(f,d,l,u,s[p+15],14,-660478335),u=g(u,f,d,l,s[p+4],20,-405537848),l=g(l,u,f,d,s[p+9],5,568446438),d=g(d,l,u,f,s[p+14],9,-1019803690),f=g(f,d,l,u,s[p+3],14,-187363961),u=g(u,f,d,l,s[p+8],20,1163531501),l=g(l,u,f,d,s[p+13],5,-1444681467),d=g(d,l,u,f,s[p+2],9,-51403784),f=g(f,d,l,u,s[p+7],14,1735328473),l=m(l,u=g(u,f,d,l,s[p+12],20,-1926607734),f,d,s[p+5],4,-378558),d=m(d,l,u,f,s[p+8],11,-2022574463),f=m(f,d,l,u,s[p+11],16,1839030562),u=m(u,f,d,l,s[p+14],23,-35309556),l=m(l,u,f,d,s[p+1],4,-1530992060),d=m(d,l,u,f,s[p+4],11,1272893353),f=m(f,d,l,u,s[p+7],16,-155497632),u=m(u,f,d,l,s[p+10],23,-1094730640),l=m(l,u,f,d,s[p+13],4,681279174),d=m(d,l,u,f,s[p+0],11,-358537222),f=m(f,d,l,u,s[p+3],16,-722521979),u=m(u,f,d,l,s[p+6],23,76029189),l=m(l,u,f,d,s[p+9],4,-640364487),d=m(d,l,u,f,s[p+12],11,-421815835),f=m(f,d,l,u,s[p+15],16,530742520),l=b(l,u=m(u,f,d,l,s[p+2],23,-995338651),f,d,s[p+0],6,-198630844),d=b(d,l,u,f,s[p+7],10,1126891415),f=b(f,d,l,u,s[p+14],15,-1416354905),u=b(u,f,d,l,s[p+5],21,-57434055),l=b(l,u,f,d,s[p+12],6,1700485571),d=b(d,l,u,f,s[p+3],10,-1894986606),f=b(f,d,l,u,s[p+10],15,-1051523),u=b(u,f,d,l,s[p+1],21,-2054922799),l=b(l,u,f,d,s[p+8],6,1873313359),d=b(d,l,u,f,s[p+15],10,-30611744),f=b(f,d,l,u,s[p+6],15,-1560198380),u=b(u,f,d,l,s[p+13],21,1309151649),l=b(l,u,f,d,s[p+4],6,-145523070),d=b(d,l,u,f,s[p+11],10,-1120210379),f=b(f,d,l,u,s[p+2],15,718787259),u=b(u,f,d,l,s[p+9],21,-343485551),l=l+v>>>0,u=u+y>>>0,f=f+_>>>0,d=d+k>>>0}return t.endian([l,u,f,d])};o._ff=function(e,t,n,r,a,o,i){var s=e+(t&n|~t&r)+(a>>>0)+i;return(s<<o|s>>>32-o)+t},o._gg=function(e,t,n,r,a,o,i){var s=e+(t&r|n&~r)+(a>>>0)+i;return(s<<o|s>>>32-o)+t},o._hh=function(e,t,n,r,a,o,i){var s=e+(t^n^r)+(a>>>0)+i;return(s<<o|s>>>32-o)+t},o._ii=function(e,t,n,r,a,o,i){var s=e+(n^(t|~r))+(a>>>0)+i;return(s<<o|s>>>32-o)+t},o._blocksize=16,o._digestsize=16,e.exports=function(e,n){if(null==e)throw new Error("Illegal argument "+e);var r=t.wordsToBytes(o(e,n));return n&&n.asBytes?r:n&&n.asString?a.bytesToString(r):t.bytesToHex(r)}}()})),td={},nd=/^cherry-inner:\/\/([0-9a-f]+)$/i;var rd=function(){function e(){un(this,e)}return ln(e,null,[{key:"isInnerLink",value:function(e){return nd.test(e)}},{key:"set",value:function(e){var t=ed(e);return td[t]=e,"cherry-inner://".concat(t)}},{key:"get",value:function(e){var t,n=null!==(t=e.match(nd))&&void 0!==t?t:[],r=gf(n,2)[1];if(r)return td[r]}},{key:"replace",value:function(e,t){var n,r=null!==(n=e.match(nd))&&void 0!==n?n:[],a=gf(r,2)[1];if(a)return td[a]=t,e}},{key:"restoreAll",value:function(t){return t.replace(/cherry-inner:\/\/([0-9a-f]+)/gi,(function(t){return e.get(t)||t}))}},{key:"clear",value:function(){td={}}}]),e}();function ad(e){var t=function(){if("undefined"==typeof Reflect||!an)return!1;if(an.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(an(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Qa(e);if(t){var a=Qa(this).constructor;n=an(r,arguments,a)}else n=r.apply(this,arguments);return Ya(this,n)}}var od=function(e){yn(n,e);var t=ad(n);function n(e){var r,a=e.config,o=e.globalConfig;return un(this,n),(r=t.call(this,{config:a})).urlProcessor=o.urlProcessor,r.openNewPage=a.openNewPage,r}return ln(n,[{key:"beforeMakeHtml",value:function(e){return e.replace(this.RULE.reg,(function(e){return e.replace(/~D/g,"~1D")}))}},{key:"checkBrackets",value:function(e){for(var t=[],n="[".concat(e,"]"),r=function(e){return 1&Fu(n).call(n,0,e).match(/\\*$/)[0].length},a=n.length-1;n[a]&&(a!==n.length-1||!r(a));a--)if("]"!==n[a]||r(a)||t.push("]"),"["===n[a]&&!r(a)&&(t.pop(),!t.length))return{isValid:!0,coreText:Fu(n).call(n,a+1,n.length-1),extraLeadingChar:Fu(n).call(n,0,a)};return{isValid:!1,coreText:e,extraLeadingChar:""}}},{key:"toHtml",value:function(e,t,n,r,a,o,i){var s=void 0===r?"ref":"url",c="";if("ref"===s)return e;if("url"===s){var l,u=this.checkBrackets(n),f=u.isValid,d=u.coreText,p=u.extraLeadingChar;if(!f)return e;c=a&&""!==Tc(a).call(a)?' title="'.concat(Df(a.replace(/["']/g,"")),'"'):"",i?c+=' target="'.concat(i.replace(/{target\s*=\s*(.*?)}/,"$1"),'"'):this.openNewPage&&(c+=' target="_blank"');var h,g,m,b=Tc(r).call(r).replace(/~1D/g,"~D"),v=d.replace(/~1D/g,"~D");return Hf(b)?(b=zf(b=this.urlProcessor(b,"link")),Ns(h=Ns(g=Ns(m="".concat(t+p,'<a href="')).call(m,rd.set(b),'" rel="nofollow"')).call(g,c,">")).call(h,v,"</a>")):Ns(l="".concat(t+p,"<span>")).call(l,n,"</span>")}return e}},{key:"toStdMarkdown",value:function(e){return e}},{key:"makeHtml",value:function(e){var t,n;return this.test(e)?ru()?e.replace(this.RULE.reg,Ps(n=this.toHtml).call(n,this)):ef(e,this.RULE.reg,Ps(t=this.toHtml).call(t,this),!0,1):e}},{key:"rule",value:function(){var e={begin:ru()?"((?<!\\\\))":"(^|[^\\\\])",content:["\\[([^\\n]+?)\\]","[ \\t]*","".concat("(?:\\(([^\\s)]+)(?:[ \\t]((?:\".*?\")|(?:'.*?')))?\\)|\\[(").concat("(?:[^\\n]*?\\S[^\\n]*?)",")\\]")+")","(\\{target\\s*=\\s*(_blank|_parent|_self|_top)\\})?"].join(""),end:""};return e.reg=nu(e,"g"),e}}]),n}(uc);eo(od,"HOOK_NAME","link");var id=s.RangeError;Ye({target:"String",proto:!0},{repeat:function(e){var t=Mn(L(this)),n="",r=At(e);if(r<0||r==1/0)throw id("Wrong number of repetitions");for(;r>0;(r>>>=1)&&(t+=t))1&r&&(n+=t);return n}});var sd=Ts("String").repeat,cd=String.prototype,ld=function(e){var t=e.repeat;return"string"==typeof e||e===cd||F(cd,e)&&t===cd.repeat?sd:t};function ud(e){var t=function(){if("undefined"==typeof Reflect||!an)return!1;if(an.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(an(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Qa(e);if(t){var a=Qa(this).constructor;n=an(r,arguments,a)}else n=r.apply(this,arguments);return Ya(this,n)}}var fd=function(e){yn(n,e);var t=ud(n);function n(){var e,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{config:void 0},a=r.config;return un(this,n),e=t.call(this,{config:a}),a?(e.allowWhitespace=!!a.allowWhitespace,e):Ya(e)}return ln(n,[{key:"makeHtml",value:function(e,t){var n=function(e,n,r,a){var o,i,s,c,l,u=r.length%2==1?"em":"strong",f=Math.floor(r.length/2),d=ld(o="<strong>").call(o,f),p=ld(i="</strong>").call(i,f);return"em"===u&&(d+="<em>",p="</em>".concat(p)),Ns(s=Ns(c=Ns(l="".concat(n)).call(l,d)).call(c,t(a).html.replace(/_/g,"~U"))).call(s,p)},r=e;return(r=(r=this.allowWhitespace?(r=(r=r.replace(/(^|\n[\s]*)(\*)([^\s*](?:.*?)(?:(?:\n.*?)*?))\*/g,n)).replace(/(^|\n[\s]*)(\*{2,})((?:.*?)(?:(?:\n.*?)*?))\2/g,n)).replace(/([^\n*\\\s][ ]*)(\*+)((?:.*?)(?:(?:\n.*?)*?))\2/g,n):r.replace(this.RULE.asterisk.reg,n)).replace(this.RULE.underscore.reg,(function(e,n,r,a,o,i){var s,c,l,u,f;if(""===Tc(a).call(a))return e;var d=r.length%2==1?"em":"strong",p=Math.floor(r.length/2),h=ld(s="<strong>").call(s,p),g=ld(c="</strong>").call(c,p),m=t(a).html;return"em"===d&&(h+="<em>",g="</em>".concat(g)),Ns(l=Ns(u=Ns(f="".concat(n)).call(f,h)).call(u,m)).call(l,g)}))).replace(/~U/g,"_")}},{key:"test",value:function(e,t){return this.RULE[t].reg&&this.RULE[t].reg.test(e)}},{key:"rule",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{config:void 0},t=e.config,n=!!t&&!!t.allowWhitespace,r=n?"(?:.*?)(?:(?:\\n.*?)*?)":"(?:(?:\\S|(?:\\S.*?\\S))(?:[ \\t]*\\n.*?)*?)",a={begin:"(^|[^\\\\])(\\*+)",content:"(".concat(r,")"),end:"\\2"},o={begin:"(^|".concat(iu,")(_+)"),content:"(".concat(r,")"),end:"\\2(?=".concat(iu,"|$)")};return a.reg=nu(a,"g"),o.reg=nu(o,"g"),{asterisk:a,underscore:o}}}]),n}(uc);function dd(e){var t=function(){if("undefined"==typeof Reflect||!an)return!1;if(an.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(an(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Qa(e);if(t){var a=Qa(this).constructor;n=an(r,arguments,a)}else n=r.apply(this,arguments);return Ya(this,n)}}eo(fd,"HOOK_NAME","fontEmphasis");var pd=function(e){yn(n,e);var t=dd(n);function n(e){var r;return un(this,n),(r=t.call(this)).classicBr=e.globalConfig.classicBr,r.removeBrAfterBlock=null,r.removeBrBeforeBlock=null,r.removeNewlinesBetweenTags=null,r}return ln(n,[{key:"$cleanParagraph",value:function(e){var t=this.$engine.$cherry.options.engine.global.classicBr,n=e.replace(/^\n+/,"").replace(/\n+$/,"");return t?n:this.joinRawHtml(n).replace(/\n/g,"<br>").replace(/\r/g,"\n")}},{key:"joinRawHtml",value:function(e){if(!this.removeBrAfterBlock){var t,n,r,a,o=null!==(t=null===(n=this.$engine.htmlWhiteListAppend)||void 0===n?void 0:n.split("|"))&&void 0!==t?t:[];o=Ys(r=mc(o).call(o,(function(e){return/[a-z-]+/gi.test(e)?e:null}))).call(r,(function(e){return null!==e}));var i=Ns(o).call(o,Nf).join("|");this.removeBrAfterBlock=new RegExp("<(".concat(i,")(>| [^>]*?>)[^\\S\\n]*?\\n"),"ig"),this.removeBrBeforeBlock=new RegExp("\\n[^\\S\\n]*?<\\/(".concat(i,")>[^\\S\\n]*?\\n"),"ig"),this.removeNewlinesBetweenTags=new RegExp(Ns(a="<\\/(".concat(i,")>[^\\S\\n]*?\\n([^\\S\\n]*?)<(")).call(a,i,")(>| [^>]*?>)"),"ig")}return e.replace(this.removeBrAfterBlock,"<$1$2").replace(this.removeBrBeforeBlock,"</$1>").replace(this.removeNewlinesBetweenTags,"</$1>\r$2<$3$4")}},{key:"makeHtml",value:function(e,t){var n=this;return this.test(e)?e.replace(this.RULE.reg,(function(e,r,a){var o;if(n.isContainsCache(e,!0))return e;var i,s=function(e){var r,a,o,i,s,c;if(""===Tc(e).call(e))return"";var l=t(e),u=l.sign,f=l.html,d="p";new RegExp("<(".concat(Nf,")[^>]*>"),"i").test(f)&&(d="div");var p=n.getLineCount(e,e);return Ns(r=Ns(a=Ns(o=Ns(i=Ns(s=Ns(c="<".concat(d,' data-sign="')).call(c,u)).call(s,p,'" data-type="')).call(i,d,'" data-lines="')).call(o,p,'">')).call(a,n.$cleanParagraph(f),"</")).call(r,d,">")};return n.isContainsCache(a)?n.makeExcludingCached(Ns(i="".concat(r)).call(i,a),s):s(Ns(o="".concat(r)).call(o,a))})):e}},{key:"rule",value:function(){var e={begin:"(?:^|\\n)(\\n*)",end:"(?=\\s*$|\\n\\n)",content:"([\\s\\S]+?)"};return e.reg=new RegExp(e.begin+e.content+e.end,"g"),e}}]),n}(Hc);eo(pd,"HOOK_NAME","normalParagraph");var hd=function(e){return void 0!==e&&(le(e,"value")||le(e,"writable"))};Ye({target:"Reflect",stat:!0},{get:function e(t,n){var r,a,o=arguments.length<3?t:arguments[2];return He(t)===o?t[n]:(r=Te.f(t,n))?hd(r)?r.value:void 0===r.get?void 0:w(r.get,o):M(a=ba(t))?e(a,n,o):void 0}});var gd=j.Reflect.get,md=Wu,bd=r((function(e){e.exports=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=Ja(e)););return e},e.exports.__esModule=!0,e.exports.default=e.exports}));n(bd);var vd=n(r((function(e){function t(){return"undefined"!=typeof Reflect&&gd?(e.exports=t=gd,e.exports.__esModule=!0,e.exports.default=e.exports):(e.exports=t=function(e,t,n){var r=bd(e,t);if(r){var a=md(r,t);return a.get?a.get.call(arguments.length<3?e:n):a.value}},e.exports.__esModule=!0,e.exports.default=e.exports),t.apply(this,arguments)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports})));function yd(e){var t=function(){if("undefined"==typeof Reflect||!an)return!1;if(an.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(an(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Qa(e);if(t){var a=Qa(this).constructor;n=an(r,arguments,a)}else n=r.apply(this,arguments);return Ya(this,n)}}var _d=/[\s\-_]/,kd=/[A-Za-z]/,wd=/[0-9]/,Ed=function(e){yn(n,e);var t=yd(n);function n(){var e,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{config:void 0,externals:void 0},a=(r.externals,r.config);return un(this,n),(e=t.call(this,{needCache:!0})).strict=!a||!!a.strict,e.RULE=e.rule(),e.headerIDCache=[],e.headerIDCounter={},e.config=a||{},e}return ln(n,[{key:"$parseTitleText",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return"string"!=typeof e?"":e.replace(/<.*?>/g,"").replace(/&#60;/g,"<").replace(/&#62;/g,">")}},{key:"$generateId",value:function(e){for(var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=e.length,r="",a=0;a<n;a++){var o=e.charAt(a);if(kd.test(o))r+=t?o.toLowerCase():o;else if(wd.test(o))r+=o;else if(_d.test(o))r+=r.length<1||"-"!==r.charAt(r.length-1)?"-":"";else if(o.charCodeAt(0)>255)try{r+=encodeURIComponent(o)}catch(e){}}return r}},{key:"generateIDNoDup",value:function(e){var t,n=e.replace(/&#60;/g,"<").replace(/&#62;/g,">"),r=this.$generateId(n,!0),a=mf(t=this.headerIDCache).call(t,r);if(-1!==a)this.headerIDCounter[a]+=1,r+="-".concat(this.headerIDCounter[a]+1);else{var o=this.headerIDCache.push(r);this.headerIDCounter[o-1]=1}return r}},{key:"$wrapHeader",value:function(e,t,n,r){var a,o,i,s,c,l,u,f=r(Tc(e).call(e)),d=f.html,p=d.match(/\s+\{#([A-Za-z0-9-]+)\}$/);null!==p&&(d=d.substring(0,p.index),u=gf(p,2)[1]);var h=this.$parseTitleText(d);if(!u){u=this.generateIDNoDup(h.replace(/~fn#([0-9]+)#/g,""))}var g=this.$engine.md5(Ns(a=Ns(o=Ns(i="".concat(t,"-")).call(i,f.sign,"-")).call(o,u,"-")).call(a,n));return{html:[Ns(s=Ns(c=Ns(l="<h".concat(t,' id="')).call(l,u,'" data-sign="')).call(c,g,'" data-lines="')).call(s,n,'">'),this.$getAnchor(u),"".concat(d),"</h".concat(t,">")].join(""),sign:"".concat(g)}}},{key:"$getAnchor",value:function(e){return"none"===(this.config.anchorStyle||"default")?"":'<a class="anchor" href="#'.concat(e,'"></a>')}},{key:"beforeMakeHtml",value:function(e){var t=this,n=e;return this.test(n,"atx")&&(n=n.replace(this.RULE.atx.reg,(function(e,n,r,a){return""===Tc(a).call(a)?e:t.getCacheWithSpace(t.pushCache(e),e,!0)}))),this.test(n,"setext")&&(n=n.replace(this.RULE.setext.reg,(function(e,n,r){return""===Tc(r).call(r)||t.isContainsCache(r)?e:t.getCacheWithSpace(t.pushCache(e),e,!0)}))),n}},{key:"makeHtml",value:function(e,t){var n=this,r=this.restoreCache(e);return this.test(r,"atx")&&(r=r.replace(this.RULE.atx.reg,(function(e,r,a,o){var i=Dc(r,n.getLineCount(e.replace(/^\n+/,""))),s=o.replace(/\s+#+\s*$/,""),c=n.$wrapHeader(s,a.length,i,t),l=c.html,u=c.sign;return n.getCacheWithSpace(n.pushCache(l,u,i),e,!0)}))),this.test(r,"setext")&&(r=r.replace(this.RULE.setext.reg,(function(e,r,a,o){if(n.isContainsCache(a))return e;var i=Dc(r,n.getLineCount(e.replace(/^\n+/,""))),s="-"===o[0]?2:1,c=n.$wrapHeader(a,s,i,t),l=c.html,u=c.sign;return n.getCacheWithSpace(n.pushCache(l,u,i),e,!0)}))),r}},{key:"afterMakeHtml",value:function(e){var t=vd(Qa(n.prototype),"afterMakeHtml",this).call(this,e);return this.headerIDCache=[],this.headerIDCounter={},t}},{key:"test",value:function(e,t){return this.RULE[t].reg&&this.RULE[t].reg.test(e)}},{key:"rule",value:function(){var e={begin:"(?:^|\\n)(\\n*)",content:["(?:\\h*","(.+)",")\\n","(?:\\h*","([=]+|[-]+)",")"].join(""),end:"(?=$|\\n)"};e.reg=nu(e,"g",!0);var t={begin:"(?:^|\\n)(\\n*)(?:\\h*(#{1,6}))",content:"(.+?)",end:"(?=$|\\n)"};return this.strict&&(t.begin+="(?=\\h+)"),t.reg=nu(t,"g",!0),{setext:e,atx:t}}}]),n}(Hc);function xd(e){var t=function(){if("undefined"==typeof Reflect||!an)return!1;if(an.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(an(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Qa(e);if(t){var a=Qa(this).constructor;n=an(r,arguments,a)}else n=r.apply(this,arguments);return Ya(this,n)}}eo(Ed,"HOOK_NAME","header");var Sd=function(e){yn(n,e);var t=xd(n);function n(){return un(this,n),t.apply(this,arguments)}return ln(n,[{key:"rule",value:function(){var e={};return e.reg=new RegExp(""),e}},{key:"beforeMakeHtml",value:function(e){return e.replace(/\\\n/g,"\\ \n")}},{key:"afterMakeHtml",value:function(e){var t=e.replace(/~Q/g,"~");return t=(t=(t=(t=(t=t.replace(/~X/g,"`")).replace(/~Y/g,"!")).replace(/~Z/g,"#")).replace(/~&/g,"&")).replace(/~K/g,"/")}}]),n}(uc);eo(Sd,"HOOK_NAME","transfer");var Ad=s.TypeError,Cd=function(e){return function(t,n,r,a){ee(n);var o=se(t),i=P(o),s=Pt(o),c=e?s-1:0,l=e?-1:1;if(r<2)for(;;){if(c in i){a=i[c],c+=l;break}if(c+=l,e?c<0:s<=c)throw Ad("Reduce of empty array with no initial value")}for(;e?c>=0:s>c;c+=l)c in i&&(a=n(a,i[c],c,o));return a}},Td={left:Cd(!1),right:Cd(!0)},Od="process"==O(s.process),$d=Td.left,Rd=Ms("reduce");Ye({target:"Array",proto:!0,forced:!Rd||!Od&&G>79&&G<83},{reduce:function(e){var t=arguments.length;return $d(this,e,t,t>1?arguments[1]:void 0)}});var Pd=Ts("Array").reduce,Id=Array.prototype,Ld=function(e){var t=e.reduce;return e===Id||F(Id,e)&&t===Id.reduce?Pd:t};function Nd(e,t){var n=Ws(e);if(Hu){var r=Hu(e);t&&(r=Ys(r).call(r,(function(t){return qu(e,t).enumerable}))),n.push.apply(n,r)}return n}function Md(e){for(var t=1;t<arguments.length;t++){var n,r,a=null!=arguments[t]?arguments[t]:{};t%2?zs(n=Nd(Object(a),!0)).call(n,(function(t){eo(e,t,a[t])})):Zu?Xu(e,Zu(a)):zs(r=Nd(Object(a))).call(r,(function(t){Cs(e,t,qu(a,t))}))}return e}function jd(e){var t=function(){if("undefined"==typeof Reflect||!an)return!1;if(an.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(an(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Qa(e);if(t){var a=Qa(this).constructor;n=an(r,arguments,a)}else n=r.apply(this,arguments);return Ya(this,n)}}var Dd=function(e){yn(n,e);var t=jd(n);function n(e){var r,a=e.externals,o=e.config;un(this,n),r=t.call(this,{needCache:!0});var i=o.enableChart,s=o.chartRenderEngine,c=o.externals,l=o.chartEngineOptions,u=void 0===l?{}:l;if(r.chartRenderEngine=null,!0===i)try{r.chartRenderEngine=new s(Md(Md({},a&&c instanceof Array&&Ld(c).call(c,(function(e,t){return delete u[t],Md(Md({},e),{},eo({},t,a[t]))}),{})),{},{renderer:"svg",width:500,height:300},u))}catch(e){console.warn(e)}return r}return ln(n,[{key:"$extendColumns",value:function(e,t){var n,r=t-e.length;return r<1?e:Ns(e).call(e,ld(n="&nbsp;|").call(n,r).split("|",r))}},{key:"$parseChartOptions",value:function(e){if(!this.chartRenderEngine)return null;var t=/^[ ]*:(\w+):(?:[ ]*{(.*?)}[ ]*)?$/;if(!t.test(e))return null;var n=e.match(t),r=gf(n,3),a=r[1],o=r[2];return{type:a,options:o?o.split(/\s*,\s*/):["x","y"]}}},{key:"$parseColumnAlignRules",value:function(e){var t=["U","L","R","C"];return{textAlignRules:mc(e).call(e,(function(e){var n=Tc(e).call(e),r=0;return/^:/.test(n)&&(r+=1),/:$/.test(n)&&(r+=2),t[r]})),COLUMN_ALIGN_MAP:{L:"left",R:"right",C:"center"}}}},{key:"$parseTable",value:function(e,t,n){var r,a,o,i,s,c,l=this,u=0,f=mc(e).call(e,(function(e,t){var n=e.replace(/\\\|/g,"~CS").split("|");return""===n[0]&&n.shift(),""===n[n.length-1]&&n.pop(),1!==t&&(u=Math.max(u,n.length)),n})),d=this.$parseColumnAlignRules(f[1]),p=d.textAlignRules,h=d.COLUMN_ALIGN_MAP,g={header:[],rows:[],colLength:u,rowLength:f.length-2},m=this.$parseChartOptions(f[0][0]),b=this.$engine.md5(f[0][0]);m&&(f[0][0]="");var v=mc(r=this.$extendColumns(f[0],u)).call(r,(function(e,n){var r,a;g.header.push(e.replace(/~CS/g,"\\|"));var o=t(Tc(r=e.replace(/~CS/g,"\\|")).call(r)).html;return Ns(a="~CTH".concat(p[n]||"U"," ")).call(a,o," ~CTH$")})).join(""),y=Ld(f).call(f,(function(e,n,r){var a;if(r<=1)return e;var o=r-2;g.rows[o]=[];var i=mc(a=l.$extendColumns(n,u)).call(a,(function(e,n){var r,a;g.rows[o].push(e.replace(/~CS/g,"\\|"));var i=t(Tc(r=e.replace(/~CS/g,"\\|")).call(r)).html;return Ns(a="~CTD".concat(p[n]||"U"," ")).call(a,i," ~CTD$")}));return e.push("~CTR".concat(i.join(""),"~CTR$")),e}),[]).join(""),_=this.$renderTable(h,v,y,n);if(!m)return _;var k=this.chartRenderEngine.render(m.type,m.options,g),w=Ns(a=Ns(o=Ns(i=Ns(s='<figure id="table_chart_'.concat(b,"_")).call(s,_.sign,'"\n      data-sign="table_chart_')).call(i,b,"_")).call(o,_.sign,'" data-lines="0">')).call(a,k,"</figure>");return{html:Ns(c="".concat(w)).call(c,_.html),sign:b+_.sign}}},{key:"$renderTable",value:function(e,t,n,r){var a,o,i,s,c=Ns(a="~CTHD".concat(t,"~CTHD$~CTBD")).call(a,n,"~CTBD$"),l=this.$engine.md5(c),u=c.replace(/~CTHD\$/g,"</thead>").replace(/~CTHD/g,"<thead>").replace(/~CTBD\$/g,"</tbody>").replace(/~CTBD/g,"</tbody>").replace(/~CTR\$/g,"</tr>").replace(/~CTR/g,"<tr>").replace(/[ ]?~CTH\$/g,"</th>").replace(/[ ]?~CTD\$/g,"</td>").replace(/~CT(D|H)(L|R|C|U)[ ]?/g,(function(t,n,r){var a="<t".concat(n);return a+="U"===r?">":' align="'.concat(e[r],'">')})).replace(/\\\|/g,"|");return{html:Ns(o=Ns(i=Ns(s='<div class="cherry-table-container" data-sign="'.concat(l)).call(s,r,'" data-lines="')).call(i,r,'">\n        <table class="cherry-table">')).call(o,u,"</table></div>"),sign:l}}},{key:"makeHtml",value:function(e,t){var n=this,r=e;return this.test(r,"strict")&&(r=r.replace(this.RULE.strict.reg,(function(e,r){var a,o=n.getLineCount(e,r),i=mc(a=Tc(e).call(e).split(/\n/)).call(a,(function(e){var t;return Tc(t=String(e)).call(t)})),s=n.$parseTable(i,t,o),c=s.html,l=s.sign;return n.getCacheWithSpace(n.pushCache(c,l,o),e)}))),this.test(r,"loose")&&(r=r.replace(this.RULE.loose.reg,(function(e,r){var a,o=n.getLineCount(e,r),i=mc(a=Tc(e).call(e).split(/\n/)).call(a,(function(e){var t;return Tc(t=String(e)).call(t)})),s=n.$parseTable(i,t,o),c=s.html,l=s.sign;return n.getCacheWithSpace(n.pushCache(c,l,o),e)}))),r}},{key:"test",value:function(e,t){return this.RULE[t].reg&&this.RULE[t].reg.test(e)}},{key:"rule",value:function(){return function(){var e,t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],n={begin:"(?:^|\\n)(\\n*)",content:["(\\h*\\|[^\\n]+\\|?\\h*)","\\n","(?:(?:\\h*\\|\\h*:?[-]{1,}:?\\h*)+\\|?\\h*)","((\\n\\h*\\|[^\\n]+\\|?\\h*)*)"].join(""),end:"(?=$|\\n)"};n.reg=nu(n,"g",!0);var r={begin:"(?:^|\\n)(\\n*)",content:["(\\|?[^\\n|]+(\\|[^\\n|]+)+\\|?)","\\n","(?:\\|?\\h*:?[-]{1,}:?[\\h]*(?:\\|[\\h]*:?[-]{1,}:?\\h*)+\\|?)","((\\n\\|?([^\\n|]+(\\|[^\\n|]*)+)\\|?)*)"].join(""),end:"(?=$|\\n)"};if(r.reg=nu(r,"g",!0),!1===t)return{strict:n,loose:r};var a=Ns(e="(?:".concat(n.begin+n.content+n.end,"|")).call(e,r.begin+r.content+r.end,")");return nu({begin:"",content:a,end:""},"g",!0)}()}}]),n}(Hc);function Bd(){return"object"===("undefined"==typeof window?"undefined":Ga(window))}function Fd(e){var t=function(){if("undefined"==typeof Reflect||!an)return!1;if(an.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(an(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Qa(e);if(t){var a=Qa(this).constructor;n=an(r,arguments,a)}else n=r.apply(this,arguments);return Ya(this,n)}}eo(Dd,"HOOK_NAME","table");var Hd=function(e){yn(n,e);var t=Fd(n);function n(e){var r;return un(this,n),(r=t.call(this,{needCache:!0})).classicBr=e.globalConfig.classicBr,r}return ln(n,[{key:"beforeMakeHtml",value:function(e){var t=this;return this.test(e)?e.replace(this.RULE.reg,(function(e,n,r){if(0===r)return e;var a,o,i=n.match(/\n/g).length,s="br".concat(i),c="",l=t.$engine.$cherry.options.engine.global.classicBr;Bd()?c=l?Ns(a='<span data-sign="'.concat(s,'" data-type="br" data-lines="')).call(a,i,'"></span>'):Ns(o='<p data-sign="'.concat(s,'" data-type="br" data-lines="')).call(o,i,'">&nbsp;</p>'):c=l?"":"<br/>";var u=t.pushCache(c,s);return"\n\n".concat(u,"\n")})):e}},{key:"makeHtml",value:function(e,t){return e}},{key:"rule",value:function(){var e={begin:"(?:\\n)",end:"",content:"((?:\\h*\\n){2,})"};return e.reg=nu(e,"g",!0),e}}]),n}(Hc);function zd(e){var t=function(){if("undefined"==typeof Reflect||!an)return!1;if(an.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(an(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Qa(e);if(t){var a=Qa(this).constructor;n=an(r,arguments,a)}else n=r.apply(this,arguments);return Ya(this,n)}}eo(Hd,"HOOK_NAME","br");var Ud=function(e){yn(n,e);var t=zd(n);function n(){return un(this,n),t.call(this,{needCache:!0})}return ln(n,[{key:"beforeMakeHtml",value:function(e){var t=this;return e.replace(this.RULE.reg,(function(e,n){var r,a=(n.match(/\n/g)||[]).length+1,o="hr".concat(a);return jc(e,t.pushCache(Ns(r='<hr data-sign="'.concat(o,'" data-lines="')).call(r,a,'" />'),o))}))}},{key:"makeHtml",value:function(e,t){return e}},{key:"rule",value:function(){var e={begin:"(?:^|\\n)(\\n*)[ ]*",end:"(?=$|\\n)",content:"((?:-[ \\t]*){3,}|(?:\\*[ \\t]*){3,}|(?:_[ \\t]*){3,})"};return e.reg=new RegExp(e.begin+e.content+e.end,"g"),e}}]),n}(Hc);eo(Ud,"HOOK_NAME","hr");var Wd={processExtendAttributesInAlt:function(e){var t=e.match(/#([0-9]+(px|em|pt|pc|in|mm|cm|ex|%)|auto)/g);if(!t)return"";var n="",r=gf(t,2),a=r[0],o=r[1];return a&&(n=' width="'.concat(a.replace(/[ #]*/g,""),'"')),o&&(n+=' height="'.concat(o.replace(/[ #]*/g,""),'"')),n},processExtendStyleInAlt:function(e){var t=this.$getAlignment(e),n="",r=e.match(/#(border|shadow|radius|B|S|R)/g);if(r)for(var a=0;a<r.length;a++)switch(r[a]){case"#border":case"#B":t+="border:1px solid #888888;padding: 2px;box-sizing: border-box;",n+=" cherry-img-border";break;case"#shadow":case"#S":t+="box-shadow:0 2px 15px -5px rgb(0 0 0 / 50%);",n+=" cherry-img-shadow";break;case"#radius":case"#R":t+="border-radius: 15px;",n+=" cherry-img-radius"}return{extendStyles:t,extendClasses:n}},$getAlignment:function(e){var t=e.match(/#(center|right|left|float-right|float-left)/i);if(!t)return"";switch(gf(t,2)[1]){case"center":return"transform:translateX(-50%);margin-left:50%;display:block;";case"right":return"transform:translateX(-100%);margin-left:100%;margin-right:-100%;display:block;";case"left":return"transform:translateX(0);margin-left:0;display:block;";case"float-right":return"float:right;transform:translateX(0);margin-left:0;display:block;";case"float-left":return"float:left;transform:translateX(0);margin-left:0;display:block;"}}};function qd(e,t){var n=Ws(e);if(Hu){var r=Hu(e);t&&(r=Ys(r).call(r,(function(t){return qu(e,t).enumerable}))),n.push.apply(n,r)}return n}function Gd(e){var t=function(){if("undefined"==typeof Reflect||!an)return!1;if(an.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(an(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Qa(e);if(t){var a=Qa(this).constructor;n=an(r,arguments,a)}else n=r.apply(this,arguments);return Ya(this,n)}}var Kd=function(e,t,n,r,a,o,i,s,c){var l=void 0===a?"ref":"url",u="";if("ref"===l)return t;if("url"===l){var f,d,p,h,g,m,b,v,y=Wd.processExtendAttributesInAlt(r),_=Wd.processExtendStyleInAlt(r),k=_.extendStyles,w=_.extendClasses;k&&(k=' style="'.concat(k,'" ')),w&&(w=' class="'.concat(w,'" ')),u=o&&""!==Tc(o).call(o)?' title="'.concat(Bf(o),'"'):"",i&&(u+=" poster=".concat(zf(i)));var E=c.urlProcessor(a,e),x=Ns(f=Ns(d=Ns(p=Ns(h=Ns(g=Ns(m=Ns(b="<".concat(e,' src="')).call(b,rd.set(zf(E)),'"')).call(m,u," ")).call(g,y," ")).call(h,k," ")).call(p,w,' controls="controls">')).call(d,Bf(r||""),"</")).call(f,e,">");return Ns(v="".concat(n)).call(v,s.videoWrapper?s.videoWrapper(a):x)}return t},Zd=function(e){yn(n,e);var t=Gd(n);function n(e){var r,a=e.config,o=e.globalConfig;return un(this,n),(r=t.call(this,null)).urlProcessor=o.urlProcessor,r.extendMedia={tag:["video","audio"],replacer:{video:function(e,t,n,r,i,s){return Kd("video",e,t,n,r,i,s,a,o)},audio:function(e,t,n,r,i,s){return Kd("audio",e,t,n,r,i,s,a,o)}}},r.RULE=r.rule(r.extendMedia),r}return ln(n,[{key:"toHtml",value:function(e,t,n,r,a,o){var i=void 0===r?"ref":"url",s="";if("ref"===i)return e;if("url"===i){var c,l,u,f,d,p,h,g=Wd.processExtendAttributesInAlt(n),m=Wd.processExtendStyleInAlt(n),b=m.extendStyles,v=m.extendClasses;b&&(b=' style="'.concat(b,'" ')),v&&(v=' class="'.concat(v,'" ')),s=a&&""!==Tc(a).call(a)?' title="'.concat(Bf(a.replace(/["']/g,"")),'"'):"";var y,_="src",k=this.$engine.$cherry.options;if(k.callback&&k.callback.beforeImageMounted){var w=k.callback.beforeImageMounted(_,r);_=w.srcProp||_,y=w.src||r}return Ns(c=Ns(l=Ns(u=Ns(f=Ns(d=Ns(p=Ns(h="".concat(t,"<img ")).call(h,_,'="')).call(p,rd.set(zf(this.urlProcessor(y,"image"))),'" ')).call(d,g," ")).call(f,b," ")).call(u,v,' alt="')).call(l,Bf(n||""),'"')).call(c,s,"/>")}return e}},{key:"toMediaHtml",value:function(e,t,n,r,a,o,i,s,c){var l,u;if(!this.extendMedia.replacer[n])return e;for(var f=arguments.length,d=new Array(f>9?f-9:0),p=9;p<f;p++)d[p-9]=arguments[p];return(l=this.extendMedia.replacer[n]).call.apply(l,Ns(u=[this,e,t,r,a,o,c]).call(u,d))}},{key:"makeHtml",value:function(e){var t,n,r,a,o=e;this.test(o)&&(o=ru()?o.replace(this.RULE.reg,Ps(t=this.toHtml).call(t,this)):ef(o,this.RULE.reg,Ps(n=this.toHtml).call(n,this),!0,1));this.testMedia(o)&&(o=ru()?o.replace(this.RULE.regExtend,Ps(r=this.toMediaHtml).call(r,this)):ef(o,this.RULE.regExtend,Ps(a=this.toMediaHtml).call(a,this),!0,1));return o}},{key:"testMedia",value:function(e){return this.RULE.regExtend&&this.RULE.regExtend.test(e)}},{key:"rule",value:function(e){var t={begin:ru()?"((?<!\\\\))!":"(^|[^\\\\])!",content:["\\[([^\\n]*?)\\]","[ \\t]*","".concat('(?:\\(([^"][^\\s]+?)(?:[ \\t]((?:".*?")|(?:\'.*?\')))?\\)|\\[(').concat("(?:[^\\n]*?\\S[^\\n]*?)",")\\]")+")"].join(""),end:""};if(e){var n=function(e){for(var t=1;t<arguments.length;t++){var n,r,a=null!=arguments[t]?arguments[t]:{};t%2?zs(n=qd(Object(a),!0)).call(n,(function(t){eo(e,t,a[t])})):Zu?Xu(e,Zu(a)):zs(r=qd(Object(a))).call(r,(function(t){Cs(e,t,qu(a,t))}))}return e}({},t);n.begin=ru()?"((?<!\\\\))!(".concat(e.tag.join("|"),")"):"(^|[^\\\\])!(".concat(e.tag.join("|"),")"),n.end="({poster=(.*)})?",t.regExtend=nu(n,"g")}return t.reg=nu(t,"g"),t}}]),n}(uc);function Yd(e,t){var n=Ws(e);if(Hu){var r=Hu(e);t&&(r=Ys(r).call(r,(function(t){return qu(e,t).enumerable}))),n.push.apply(n,r)}return n}function Xd(e){for(var t=1;t<arguments.length;t++){var n,r,a=null!=arguments[t]?arguments[t]:{};t%2?zs(n=Yd(Object(a),!0)).call(n,(function(t){eo(e,t,a[t])})):Zu?Xu(e,Zu(a)):zs(r=Yd(Object(a))).call(r,(function(t){Cs(e,t,qu(a,t))}))}return e}function Vd(e){var t=function(){if("undefined"==typeof Reflect||!an)return!1;if(an.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(an(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Qa(e);if(t){var a=Qa(this).constructor;n=an(r,arguments,a)}else n=r.apply(this,arguments);return Ya(this,n)}}eo(Zd,"HOOK_NAME","image");function Jd(e){var t;if("object"!==Ga(e)&&Ws(e).length<1)return"";var n=[""];return zs(t=Ws(e)).call(t,(function(t){var r;n.push(Ns(r="".concat(t,'="')).call(r,e[t],'"'))})),n.join(" ")}function Qd(e,t){for(var n=/^(\t|[ ])/,r=e;n.test(r);)t.space+="\t"===r[0]?4:1,r=r.replace(n,"");return r}function ep(e,t){var n=/^((([*+-]|\d+[.]|[a-z]\.|[I一二三四五六七八九十]+\.)[ \t]+)([^\r]*?)($|\n{2,}(?=\S)(?![ \t]*(?:[*+-]|\d+[.]|[a-z]\.|[I一二三四五六七八九十]+\.)[ \t]+)))/;return n.test(e)?e.replace(n,(function(e,n,r,a,o){return t.type=r.search(/[*+-]/g)>-1?"ul":"ol",t.listStyle=function(e){return/^[a-z]/.test(e)?"lower-greek":/^[一二三四五六七八九十]/.test(e)?"cjk-ideographic":/^I/.test(e)?"upper-roman":/^\+/.test(e)?"circle":/^\*/.test(e)?"square":"default"}(r),t.start=Number(r.replace(".",""))?Number(r.replace(".","")):1,o})):(t.type="blank",e)}var tp=ln((function e(){un(this,e),this.index=0,this.space=0,this.type="",this.start=1,this.listStyle="",this.strs=[],this.children=[],this.lines=0})),np=function(e){yn(n,e);var t=Vd(n);function n(e){var r,a=e.config;return un(this,n),(r=t.call(this,{needCache:!0})).config=a||{},r.tree=[],r.emptyLines=0,r.indentSpace=Math.max(r.config.indentSpace,2),r}return ln(n,[{key:"addNode",value:function(e,t,n,r){"blank"===e.type?this.tree[r].strs.push(e.strs[0]):(this.tree[n].children.push(t),this.tree[t]=Xd(Xd({},e),{},{parent:n}))}},{key:"buildTree",value:function(e,t){var n=e.split("\n");this.tree=[],n.unshift("");for(var r=e.match(/\n*$/g)[0].length,a=0;a<n.length-r;a++){var o=new tp;if(n[a]=Qd(n[a],o),n[a]=ep(n[a],o),o.strs.push(t(n[a]).html),o.index=a,0!==a){for(var i=a-1;!this.tree[i];)i-=1;if("blank"===o.type)this.addNode(o,a,this.tree[i].parent,i);else{for(;!this.tree[i]||this.tree[i].space>o.space;)i-=1;var s=o.space,c=this.tree[i].space;s<c+this.indentSpace?this.config.listNested&&this.tree[i].type!==o.type?this.addNode(o,a,i):this.addNode(o,a,this.tree[i].parent):s<c+this.indentSpace+4?this.addNode(o,a,i):(o.type="blank",this.addNode(o,a,this.tree[i].parent,i))}}else o.space=-2,this.tree.push(o)}}},{key:"renderSubTree",value:function(e,t,n){var r,a,o,i=this,s=0,c={},l=Ld(t).call(t,(function(t,n){var r,a,o,c=i.tree[n],l={},u="<p>".concat(c.strs.join("<br>"),"</p>");c.lines+=i.getLineCount(c.strs.join("\n"));var f=c.children.length?i.renderTree(n):"";e.lines+=c.lines,s+=c.lines;return/<span class="ch-icon ch-icon-(square|check)"><\/span>/.test(u)&&(l.class="check-list-item"),Ns(r=Ns(a=Ns(o="".concat(t,"<li")).call(o,Jd(l),">")).call(a,u)).call(r,f,"</li>")}),"");return void 0===e.parent&&(c["data-lines"]=0===e.index?s+this.emptyLines:s,c["data-sign"]=this.sign),t[0]&&"ol"===n&&(c.start=this.tree[t[0]].start),c.class="cherry-list__".concat(this.tree[t[0]].listStyle),Ns(r=Ns(a=Ns(o="<".concat(n)).call(o,Jd(c),">")).call(a,l,"</")).call(r,n,">")}},{key:"renderTree",value:function(e){var t=this,n=0,r=this.tree[e],a=r.children;return Ld(a).call(a,(function(e,o,i){if(0===i)return e;if(t.tree[a[i]].type===t.tree[a[i-1]].type)return e;var s=t.renderSubTree(r,Fu(a).call(a,n,i),t.tree[a[i-1]].type);return n=i,e+s}),"")+(a.length?this.renderSubTree(r,Fu(a).call(a,n,a.length),this.tree[a[a.length-1]].type):"")}},{key:"toHtml",value:function(e,t){var n,r;this.emptyLines=null!==(n=null===(r=e.match(/^\n\n/))||void 0===r?void 0:r.length)&&void 0!==n?n:0;var a=e.replace(/~0$/g,"").replace(/^\n+/,"");this.buildTree(function(e){return e.replace(/^((?:|[\t ]+)[*+-]\s+)\[(\s|x)\]/gm,(function(e,t,n){var r,a=/\s/.test(n)?'<span class="ch-icon ch-icon-square"></span>':'<span class="ch-icon ch-icon-check"></span>';return Ns(r="".concat(t)).call(r,a)}))}(a),t);var o=this.renderTree(0);return this.pushCache(o,this.sign)}},{key:"makeHtml",value:function(e,t){var n=this,r="".concat(e,"~0");return this.test(r)&&(r=r.replace(this.RULE.reg,(function(e){return n.getCacheWithSpace(n.checkCache(e,t),e)}))),r=r.replace(/~0$/g,"")}},{key:"rule",value:function(){var e={begin:"(?:^|\n)(\n*)(([ ]{0,3}([*+-]|\\d+[.]|[a-z]\\.|[I一二三四五六七八九十]+\\.)[ \\t]+)",content:"([^\\r]+?)",end:"(~0|\\n{2,}(?=\\S)(?![ \\t]*(?:[*+-]|\\d+[.]|[a-z]\\.|[I一二三四五六七八九十]+\\.)[ \\t]+)))"};return e.reg=new RegExp(e.begin+e.content+e.end,"gm"),e}}]),n}(Hc);function rp(e){var t=function(){if("undefined"==typeof Reflect||!an)return!1;if(an.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(an(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Qa(e);if(t){var a=Qa(this).constructor;n=an(r,arguments,a)}else n=r.apply(this,arguments);return Ya(this,n)}}function ap(e){for(var t=/^(\t|[ ]{1,4})/,n=e,r=0;t.test(n);)n=n.replace(/^(\t|[ ]{1,4})/g,""),r+=1;return r}eo(np,"HOOK_NAME","list");var op=function(e){yn(n,e);var t=rp(n);function n(){return un(this,n),t.call(this,{needCache:!0})}return ln(n,[{key:"handleMatch",value:function(e,t){var n=this;return e.replace(this.RULE.reg,(function(e,r,a){for(var o,i,s,c=t(a),l=c.sign,u=c.html,f=n.signWithCache(u)||l,d=n.getLineCount(e,r),p=/^(([ \t]{0,3}([*+-]|\d+[.]|[a-z]\.|[I一二三四五六七八九十]+\.)[ \t]+)([^\r]+?)($|\n{2,}(?=\S)(?![ \t]*(?:[*+-]|\d+[.]|[a-z]\.|[I一二三四五六七八九十]+\.)[ \t]+)))/,h=ap(r),g=u.split("\n"),m=/^[>\s]+/,b=/>/g,v=1,y=0,_=Ns(o=Ns(i='<blockquote data-sign="'.concat(f,"_")).call(i,d,'" data-lines="')).call(o,d,'">'),k=0;g[k];k++){if(0!==k){var w=ap(g[k]);if(w<=h&&p.test(g[k]))break;h=w}var E,x=g[k].replace(m,(function(e){var t=e.match(b);return y=t&&t.length>v?t.length:v,""}));if(v===y&&0!==k&&(_+="<br>"),v<y)_+=ld(E="<blockquote>").call(E,y-v),v=y;_+=x}return _+=ld(s="</blockquote>").call(s,v),n.getCacheWithSpace(n.pushCache(_,f,d),e)}))}},{key:"makeHtml",value:function(e,t){return this.test(e)?this.handleMatch(e,t):e}},{key:"rule",value:function(){var e={begin:"(?:^|\\n)(\\s*)",content:["(",">(?:.+?\\n(?![*+-]|\\d+[.]|[a-z]\\.))(?:>*.+?\\n(?![*+-]|\\d+[.]|[a-z]\\.))*(?:>*.+?)","|",">(?:.+?)",")"].join(""),end:"(?=(\\n)|$)"};return e.reg=nu(e,"g"),e}}]),n}(Hc);function ip(e){var t=function(){if("undefined"==typeof Reflect||!an)return!1;if(an.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(an(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Qa(e);if(t){var a=Qa(this).constructor;n=an(r,arguments,a)}else n=r.apply(this,arguments);return Ya(this,n)}}eo(op,"HOOK_NAME","blockquote");var sp=function(e){yn(n,e);var t=ip(n);function n(e){var r,a=e.config,o=e.globalConfig;return un(this,n),(r=t.call(this,{config:a})).urlProcessor=o.urlProcessor,r}return ln(n,[{key:"isLinkInHtmlAttribute",value:function(e,t,n){for(var r,a=new RegExp(["<","([a-zA-Z][a-zA-Z0-9-]*)","(",["\\s+[a-zA-Z_:][a-zA-Z0-9_.:-]*","(",["\\s*=\\s*","(",["([^\\s\"'=<>`]+)","('[^']*')",'("[^"]*")'].join("|"),")"].join(""),")?"].join(""),")*","\\s*[/]?>"].join(""),"g");null!==(r=a.exec(e))&&!(r.index>t+n);)if(r.index<t&&r.index+r[0].length>=t+n)return!0;return!1}},{key:"isLinkInATag",value:function(e,t,n){for(var r,a=/<a.*>[^<]*<\/a>/g;null!==(r=a.exec(e))&&!(r.index>t+n);)if(r.index<t&&r.index+r[0].length>=t+n)return!0;return!1}},{key:"makeHtml",value:function(e,t){var n=this;return this.test(e)&&(su.test(e)||lu.test(e))?e.replace(this.RULE.reg,(function(e,t,r,a,o,i,s){var c,l,u;if(n.isLinkInHtmlAttribute(s,i,r.length+a.length)||n.isLinkInATag(s,i,r.length+a.length))return e;var f=r.toLowerCase(),d="",p="",h=!0;if(("<"!==t&&"&#60;"!==t||">"!==o&&"&#62;"!==o)&&(d=t,p=o,h=!1),""===Tc(a).call(a)||!h&&""===f&&!/www\./.test(a))return e;switch(f){case"javascript:":return e;case"mailto:":var g,m,b,v;return cu.test(a)?Ns(g=Ns(m=Ns(b="".concat(d,'<a href="')).call(b,zf(Ns(v="".concat(f)).call(v,a)),'" rel="nofollow">')).call(m,Bf(a),"</a>")).call(g,p):e;case"":var y,_,k,w,E,x;if(d===p||!h)return cu.test(a)?Ns(y=Ns(_=Ns(k="".concat(d,'<a href="mailto:')).call(k,zf(a),'" rel="nofollow">')).call(_,Bf(a),"</a>")).call(y,p):fu.test(a)?Ns(w=Ns(E="".concat(d)).call(E,n.renderLink("//".concat(a),a))).call(w,p):e;if(h)return cu.test(a)?Ns(x='<a href="mailto:'.concat(zf(a),'" rel="nofollow">')).call(x,Bf(a),"</a>"):du.test(a)||fu.test(a)?n.renderLink(a):e;default:return du.test(a)?Ns(c=Ns(l="".concat(d)).call(l,n.renderLink(Ns(u="".concat(f)).call(u,a)))).call(c,p):e}return e})):e}},{key:"rule",value:function(){var e,t={begin:"(<?)",content:["((?:[a-z][a-z0-9+.-]{1,31}:)?)",Ns(e="((?:".concat(uu.source,")|(?:")).call(e,su.source,"))")].join(""),end:"(>?)"};return t.reg=nu(t,"ig"),t}},{key:"renderLink",value:function(e,t){var n,r=t;"string"!=typeof r&&(r=e);var a=this.urlProcessor(e,"autolink");return Ns(n='<a href="'.concat(zf(a),'" rel="nofollow">')).call(n,Bf(r),"</a>")}}]),n}(uc);function cp(){var e,t,n,r;Bd()&&(this.katex=null!==(e=null===(t=this.externals)||void 0===t?void 0:t.katex)&&void 0!==e?e:window.katex,this.MathJax=null!==(n=null===(r=this.externals)||void 0===r?void 0:r.MathJax)&&void 0!==n?n:window.MathJax)}eo(sp,"HOOK_NAME","autoLink");var lp=["&","<",">",'"',"'"],up=function(e){return e.replace(new RegExp(ou,"g"),(function(e){return-1!==mf(lp).call(lp,e)?Df(e):"\\".concat(e)}))},fp=Ec.trim,dp=v("".charAt),pp=s.parseFloat,hp=s.Symbol,gp=hp&&hp.iterator,mp=1/pp(bc+"-0")!=-1/0||gp&&!c((function(){pp(Object(gp))}))?function(e){var t=fp(Mn(e)),n=pp(t);return 0===n&&"-"==dp(t,0)?-0:n}:pp;Ye({global:!0,forced:parseFloat!=mp},{parseFloat:mp});var bp=ve("match"),vp=s.TypeError,yp=function(e){if(function(e){var t;return M(e)&&(void 0!==(t=e[bp])?!!t:"RegExp"==O(e))}(e))throw vp("The method doesn't accept regular expressions");return e},_p=ve("match"),kp=function(e){var t=/./;try{"/./"[e](t)}catch(n){try{return t[_p]=!1,"/./"[e](t)}catch(e){}}return!1},wp=v("".startsWith),Ep=v("".slice),xp=Math.min,Sp=kp("startsWith");Ye({target:"String",proto:!0,forced:!Sp},{startsWith:function(e){var t=Mn(L(this));yp(e);var n=Rt(xp(arguments.length>1?arguments[1]:void 0,t.length)),r=Mn(e);return wp?wp(t,r,n):Ep(t,n,n+r.length)===r}});var Ap=Ts("String").startsWith,Cp=String.prototype,Tp=function(e){var t=e.startsWith;return"string"==typeof e||e===Cp||F(Cp,e)&&t===Cp.startsWith?Ap:t};function Op(e,t){if(!e||!e.tagName)return"";var n,r,a=document.createElement("div");return a.appendChild(e.cloneNode(!1)),n=a.innerHTML,t&&(r=mf(n).call(n,">")+1,n=n.substring(0,r)+e.innerHTML+n.substring(r)),a=null,n}function $p(e){var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},a=document.createElement(e);(a.className=n,void 0!==r)&&zs(t=Ws(r)).call(t,(function(e){var t=r[e];if(Tp(e).call(e,"data-")){var n=e.replace(/^data-/,"");a.dataset[n]=t}else a.setAttribute(e,t)}));return a}function Rp(e){var t=function(){if("undefined"==typeof Reflect||!an)return!1;if(an.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(an(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Qa(e);if(t){var a=Qa(this).constructor;n=an(r,arguments,a)}else n=r.apply(this,arguments);return Ya(this,n)}}var Pp=function(e){yn(n,e);var t=Rp(n);function n(e){var r,a,o=e.config;return un(this,n),a=t.call(this,{needCache:!0}),eo(Za(a),"engine","MathJax"),eo(Za(a),"katex",void 0),eo(Za(a),"MathJax",void 0),a.engine=Bd()?null!==(r=o.engine)&&void 0!==r?r:"MathJax":"node",a}return ln(n,[{key:"toHtml",value:function(e,t,n,r){var a,o,i;Ps(cp).call(cp,this)("engine");var s=e.replace(/^[ \f\r\t\v]*/,"").replace(/\s*$/,""),c=t.replace(/^[ \f\r\t\v]*\n/,""),l=this.$engine.md5(e),u=this.getLineCount(s,c);if(/\n/.test(t)||(u-=1),/\n\s*$/.test(e)||(u-=1),u=u>0?u:0,"katex"===this.engine){var f,d,p=this.katex.renderToString(r,{throwOnError:!1,displayMode:!0}),h=Ns(f=Ns(d='<div data-sign="'.concat(l,'" class="Cherry-Math" data-type="mathBlock"\n            data-lines="')).call(d,u,'">')).call(f,p,"</div>");return n+this.getCacheWithSpace(this.pushCache(h,l,u),e)}if(null!==(a=this.MathJax)&&void 0!==a&&a.tex2svg){var g,m,b=Op(this.MathJax.tex2svg(r),!0),v=Ns(g=Ns(m='<div data-sign="'.concat(l,'" class="Cherry-Math" data-type="mathBlock"\n            data-lines="')).call(m,u,'">')).call(g,b,"</div>");return n+this.getCacheWithSpace(this.pushCache(v,l,u),e)}var y=Ns(o=Ns(i='<div data-sign="'.concat(l,'" class="Cherry-Math" data-type="mathBlock"\n          data-lines="')).call(i,u,'">$$')).call(o,up(r),"$$</div>");return n+this.getCacheWithSpace(this.pushCache(y,l,u),e)}},{key:"beforeMakeHtml",value:function(e){var t,n;return ru()?e.replace(this.RULE.reg,Ps(n=this.toHtml).call(n,this)):ef(e,this.RULE.reg,Ps(t=this.toHtml).call(t,this),!0,1)}},{key:"makeHtml",value:function(e){return e}},{key:"rule",value:function(){var e={begin:ru()?"(\\s*)((?<!\\\\))~D~D\\s*":"(\\s*)(^|[^\\\\])~D~D\\s*",content:"([\\w\\W]*?)",end:"(\\s*)~D~D(?:\\s{0,1})"};return e.reg=new RegExp(e.begin+e.content+e.end,"g"),e}}]),n}(Hc);function Ip(e){var t=function(){if("undefined"==typeof Reflect||!an)return!1;if(an.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(an(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Qa(e);if(t){var a=Qa(this).constructor;n=an(r,arguments,a)}else n=r.apply(this,arguments);return Ya(this,n)}}eo(Pp,"HOOK_NAME","mathBlock");var Lp=function(e){yn(n,e);var t=Ip(n);function n(e){var r,a,o=e.config;return un(this,n),a=t.call(this,{needCache:!0}),eo(Za(a),"engine","MathJax"),eo(Za(a),"katex",void 0),eo(Za(a),"MathJax",void 0),a.engine=Bd()?null!==(r=o.engine)&&void 0!==r?r:"MathJax":"node",a}return ln(n,[{key:"toHtml",value:function(e,t,n){var r,a,o,i;if(!n)return e;Ps(cp).call(cp,this)("engine");var s=n.match(/\n/g),c=s?s.length+2:2,l=this.$engine.md5(e);if("katex"===this.engine&&null!==(r=this.katex)&&void 0!==r&&r.renderToString){var u,f,d=this.katex.renderToString(n,{throwOnError:!1}),p=Ns(u=Ns(f="".concat(t,'<span class="Cherry-InlineMath" data-type="mathBlock" data-lines="')).call(f,c,'">')).call(u,d,"</span>");return this.pushCache(p,Hc.IN_PARAGRAPH_CACHE_KEY_PREFIX+l)}if(null!==(a=this.MathJax)&&void 0!==a&&a.tex2svg){var h,g,m=Op(this.MathJax.tex2svg(n,{em:12,ex:6,display:!1}),!0),b=Ns(h=Ns(g="".concat(t,'<span class="Cherry-InlineMath" data-type="mathBlock" data-lines="')).call(g,c,'">')).call(h,m,"</span>");return this.pushCache(b,Hc.IN_PARAGRAPH_CACHE_KEY_PREFIX+l)}var v=Ns(o=Ns(i="".concat(t,'<span class="Cherry-InlineMath" data-type="mathBlock"\n        data-lines="')).call(i,c,'">$')).call(o,up(n),"$</span>");return this.pushCache(v,Hc.IN_PARAGRAPH_CACHE_KEY_PREFIX+l)}},{key:"beforeMakeHtml",value:function(e){var t,n;return this.test(e)?ru()?e.replace(this.RULE.reg,Ps(n=this.toHtml).call(n,this)):ef(e,this.RULE.reg,Ps(t=this.toHtml).call(t,this),!0,1):e}},{key:"makeHtml",value:function(e){return e}},{key:"rule",value:function(){var e={begin:ru()?"((?<!\\\\))~D\\n?":"(^|[^\\\\])~D\\n?",content:"(.*?)\\n?",end:"~D"};return e.reg=new RegExp(e.begin+e.content+e.end,"g"),e}}]),n}(Hc);eo(Lp,"HOOK_NAME","inlineMath");Ye({target:"Array",proto:!0},{fill:function(e){for(var t=se(this),n=Pt(t),r=arguments.length,a=Ot(r>1?arguments[1]:void 0,n),o=r>2?arguments[2]:void 0,i=void 0===o?n:Ot(o,n);i>a;)t[a++]=e;return t}});var Np=Ts("Array").fill,Mp=Array.prototype,jp=function(e){var t=e.fill;return e===Mp||F(Mp,e)&&t===Mp.fill?Np:t};function Dp(e){var t=function(){if("undefined"==typeof Reflect||!an)return!1;if(an.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(an(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Qa(e);if(t){var a=Qa(this).constructor;n=an(r,arguments,a)}else n=r.apply(this,arguments);return Ya(this,n)}}function Bp(e){return e}var Fp={tocStyle:"plain",tocNodeClass:"toc-li",tocContainerClass:"toc",tocTitleClass:"toc-title",linkProcessor:Bp},Hp=function(e){yn(n,e);var t=Dp(n);function n(e){e.externals;var r,a,o=e.config;return un(this,n),a=t.call(this,{needCache:!0}),eo(Za(a),"tocStyle","nested"),eo(Za(a),"tocNodeClass","toc-li"),eo(Za(a),"tocContainerClass","toc"),eo(Za(a),"tocTitleClass","toc-title"),eo(Za(a),"linkProcessor",Bp),eo(Za(a),"baseLevel",1),eo(Za(a),"isFirstTocToken",!0),eo(Za(a),"allowMultiToc",!1),zs(r=Ws(Fp)).call(r,(function(e){a[e]=o[e]||Fp[e]})),a}return ln(n,[{key:"beforeMakeHtml",value:function(e){var t=this,n=e;return this.test(n,"extend")&&(n=n.replace(this.RULE.extend.reg,(function(e,n,r){var a;if(!t.allowMultiToc&&!t.isFirstTocToken)return Ns(a="\n".concat(n)).call(a,'<p data-sign="empty-toc" data-lines="1">&nbsp;</p>');var o=t.pushCache(e);return t.isFirstTocToken=!1,jc(e,o)}))),this.test(n,"standard")&&(n=n.replace(this.RULE.standard.reg,(function(e,n,r){var a;return t.allowMultiToc||t.isFirstTocToken?(t.isFirstTocToken=!1,jc(e,t.pushCache(e))):Ns(a="\n".concat(n)).call(a,'<p data-sign="empty-toc" data-lines="1">&nbsp;</p>')}))),n}},{key:"makeHtml",value:function(e){return e}},{key:"$makeLevel",value:function(e){for(var t="",n=this.baseLevel;n<e;n++)t+="&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;";return t}},{key:"$makeTocItem",value:function(e,t){var n,r,a,o,i,s=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],c="";t&&(c=this.$makeLevel(e.level));var l=this.linkProcessor("#".concat(e.id));return Ns(n=Ns(r=Ns(a=Ns(o=Ns(i='<li class="'.concat(this.tocNodeClass,'">')).call(i,c,'<a href="')).call(o,l,'" class="level-')).call(a,e.level,'">')).call(r,e.text,"</a>")).call(n,s?"</li>":"")}},{key:"$makePlainToc",value:function(e){var t=this;return mc(e).call(e,(function(e){return t.$makeTocItem(e,!0)})).join("")}},{key:"$makeNestedToc",value:function(e){var t,n,r=this,a=0,o=jp(t=new Array(7)).call(t,!1),i=jp(n=new Array(7)).call(n,!1),s="";zs(e).call(e,(function(e){var t=e.level;if(0===a){for(var n=t;n>=r.baseLevel;n--)s+="<ul>",i[n]=!0;return s+=r.$makeTocItem(e,!1,!1),o[t]=!0,void(a=t)}if(t<a){for(var c=a;c>=t;c--)o[c]&&(s+="</li>",o[c]=!1),i[c]&&c>t&&(s+="</ul>",i[c]=!1);o[t]=!0,s+=r.$makeTocItem(e,!1,!1),a=t}else if(t===a)o[a]&&(s+="</li>"),s+=r.$makeTocItem(e,!1,!1),o[t]=!0,i[t]=!0;else{for(var l=a+1;l<=t;l++)s+="<ul>",i[l]=!0;o[t]=!0,s+=r.$makeTocItem(e,!1,!1),a=t}}));for(var c=a;c>=this.baseLevel;c--)o[c]&&(s+="</li>",o[c]=!1),i[c]&&(s+="</ul>",i[c]=!1);return s}},{key:"$makeToc",value:function(e,t,n){var r,a,o,i=Dc(n,1),s=Ns(r=Ns(a=Ns(o='<dir class="'.concat(this.tocContainerClass,'" data-sign="')).call(o,t,"-")).call(a,i,'" data-lines="')).call(r,i,'">');return s+='<p class="'.concat(this.tocTitleClass,'">目录</p>'),e.length<=0?"":(this.baseLevel=Math.min.apply(Math,Mu(mc(e).call(e,(function(e){return e.level})))),"nested"===this.tocStyle?s+=this.$makeNestedToc(e):s+=this.$makePlainToc(e),s+="</dir>")}},{key:"afterMakeHtml",value:function(e){var t=this,r=vd(Qa(n.prototype),"afterMakeHtml",this).call(this,e),a=[],o="";return r.replace(/<h([1-6]) id="([^"]+?)" data-sign=".+?" data-lines="[0-9]+"><a[^/]+?\/a>(.+?)<\/h\1>/g,(function(e,t,n,r){var i,s=r.replace(/~fn#[0-9]+#/g,"");a.push({level:+t,id:n,text:s}),o+=Ns(i="".concat(t)).call(i,n)})),o=this.$engine.md5(o),r=(r=(r=r.replace(/(?:^|\n)(\[\[|\[|【【)(toc|TOC)(\]\]|\]|】】)([<~])/,(function(e){return e.replace(/(\]\]|\]|】】)([<~])/,"$1\n$2")}))).replace(this.RULE.extend.reg,(function(e,n){return t.$makeToc(a,o,n)}))).replace(this.RULE.standard.reg,(function(e,n){return t.$makeToc(a,o,n)})),this.isFirstTocToken=!0,r}},{key:"test",value:function(e,t){return!!this.RULE[t].reg&&this.RULE[t].reg.test(e)}},{key:"rule",value:function(){var e={begin:"(?:^|\\n)(\\n*)",end:"(?=$|\\n)",content:"[ ]*((?:【【|\\[\\[)(?:toc|TOC)(?:\\]\\]|】】))[ ]*"};e.reg=new RegExp(e.begin+e.content+e.end,"g");var t={begin:"(?:^|\\n)(\\n*)",end:"(?=$|\\n)",content:"[ ]*(\\[(?:toc|TOC)\\])[ ]*"};return t.reg=new RegExp(t.begin+t.content+t.end,"g"),{extend:e,standard:t}}}]),n}(Hc);function zp(e){var t=function(){if("undefined"==typeof Reflect||!an)return!1;if(an.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(an(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Qa(e);if(t){var a=Qa(this).constructor;n=an(r,arguments,a)}else n=r.apply(this,arguments);return Ya(this,n)}}eo(Hp,"HOOK_NAME","toc");var Up=function(e){yn(n,e);var t=zp(n);function n(e){var r;e.externals,e.config;return un(this,n),(r=t.call(this)).footnoteCache={},r.footnoteMap={},r.footnote=[],r}return ln(n,[{key:"$cleanCache",value:function(){this.footnoteCache={},this.footnoteMap={},this.footnote=[]}},{key:"pushFootnoteCache",value:function(e,t){this.footnoteCache[e]=t}},{key:"getFootnoteCache",value:function(e){return this.footnoteCache[e]||null}},{key:"pushFootNote",value:function(e,t){var n,r,a,o,i,s;if(this.footnoteMap[e])return this.footnoteMap[e];var c=this.footnote.length+1,l={};l.fn=Ns(n=Ns(r=Ns(a='<sup><a href="#fn:'.concat(c,'" id="fnref:')).call(a,c,'" title="')).call(r,e,'" class="footnote">[')).call(n,c,"]</a></sup>"),l.fnref=Ns(o=Ns(i=Ns(s='<a href="#fnref:'.concat(c,'" id="fn:')).call(s,c,'" title="')).call(i,e,'" class="footnote-ref">[')).call(o,c,"]</a>"),l.num=c,l.note=Tc(t).call(t),this.footnote.push(l);var u="\0~fn#".concat(c-1,"#\0");return this.footnoteMap[e]=u,u}},{key:"getFootNote",value:function(){return this.footnote}},{key:"formatFootNote",value:function(){var e,t=this.getFootNote();if(t.length<=0)return"";var n=mc(t).call(t,(function(e){var t;return Ns(t='<div class="one-footnote">\n'.concat(e.fnref)).call(t,e.note,"\n</div>")})).join(""),r=this.$engine.md5(n);return n=Ns(e='<div class="footnote" data-sign="'.concat(r,'" data-lines="0"><div class="footnote-title">脚注</div>')).call(e,n,"</div>")}},{key:"beforeMakeHtml",value:function(e){var t=this,n=e;return this.test(n)&&(n=(n=n.replace(this.RULE.reg,(function(e,n,r,a){return t.pushFootnoteCache(r,a),(e.match(/\n/g)||[]).join("")}))).replace(/\[\^([^\]]+?)\](?!:)/g,(function(e,n){var r=t.getFootnoteCache(n);return r?t.pushFootNote(n,r):e})),n+=this.formatFootNote()),n}},{key:"makeHtml",value:function(e,t){return e}},{key:"afterMakeHtml",value:function(e){var t=this.getFootNote(),n=e.replace(/\0~fn#([0-9]+)#\0/g,(function(e,n){return t[n].fn}));return this.$cleanCache(),n}},{key:"rule",value:function(){var e={begin:"(^|\\n)[ \t]*",content:["\\[\\^([^\\]]+?)\\]:\\h*","([\\s\\S]+?)"].join(""),end:"(?=\\s*$|\\n\\n)"};return e.reg=nu(e,"g",!0),e}}]),n}(Hc);function Wp(e){var t=function(){if("undefined"==typeof Reflect||!an)return!1;if(an.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(an(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Qa(e);if(t){var a=Qa(this).constructor;n=an(r,arguments,a)}else n=r.apply(this,arguments);return Ya(this,n)}}eo(Up,"HOOK_NAME","footnote");var qp=function(e){yn(n,e);var t=Wp(n);function n(e){var r;e.externals,e.config;return un(this,n),(r=t.call(this)).commentCache={},r}return ln(n,[{key:"$cleanCache",value:function(){this.commentCache={}}},{key:"pushCommentReferenceCache",value:function(e,t){var n,r=t.split(/[ ]+/g),a=Bu(r),o=a[0],i=Fu(a).call(a,1),s=rd.set(o);this.commentCache["".concat(e).toLowerCase()]=Ns(n=[s]).call(n,Mu(i)).join(" ")}},{key:"getCommentReferenceCache",value:function(e){return this.commentCache["".concat(e).toLowerCase()]||null}},{key:"beforeMakeHtml",value:function(e){var t=this,n=e;if(this.test(n)){n=(n=n.replace(this.RULE.reg,(function(e,n,r,a){var o;return t.pushCommentReferenceCache(r,a),(null!==(o=e.match(/\n/g))&&void 0!==o?o:[]).join("")}))).replace(/(\[[^\]\n]+?\])?(?:\[([^\]\n]+?)\])/g,(function(e,n,r){var a,o,i=t.getCommentReferenceCache(r);return i?n?Ns(o="".concat(n,"(")).call(o,i,")"):Ns(a="[".concat(r,"](")).call(a,i,")"):e})),this.$cleanCache()}return n}},{key:"makeHtml",value:function(e,t){return e}},{key:"afterMakeHtml",value:function(e){return rd.restoreAll(e)}},{key:"rule",value:function(){var e={begin:"(^|\\n)[ \t]*",content:["\\[([^^][^\\]]*?)\\]:\\h*","([^\\n]+?)"].join(""),end:"(?=$|\\n)"};return e.reg=nu(e,"g",!0),e}}]),n}(Hc);eo(qp,"HOOK_NAME","commentReference");var Gp=pr.some,Kp=Ms("some");Ye({target:"Array",proto:!0,forced:!Kp},{some:function(e){return Gp(this,e,arguments.length>1?arguments[1]:void 0)}});var Zp=Ts("Array").some,Yp=Array.prototype,Xp=function(e){var t=e.some;return e===Yp||F(Yp,e)&&t===Yp.some?Zp:t},Vp=r((function(e,t){e.exports=function(){function e(t){return(e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)}function t(e,n){return(t=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,n)}function n(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}function r(e,a,o){return(r=n()?Reflect.construct:function(e,n,r){var a=[null];a.push.apply(a,n);var o=new(Function.bind.apply(e,a));return r&&t(o,r.prototype),o}).apply(null,arguments)}function a(e){return function(e){if(Array.isArray(e))return o(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return o(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?o(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function o(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var i=Object.hasOwnProperty,s=Object.setPrototypeOf,c=Object.isFrozen,l=Object.getPrototypeOf,u=Object.getOwnPropertyDescriptor,f=Object.freeze,d=Object.seal,p=Object.create,h="undefined"!=typeof Reflect&&Reflect,g=h.apply,m=h.construct;g||(g=function(e,t,n){return e.apply(t,n)}),f||(f=function(e){return e}),d||(d=function(e){return e}),m||(m=function(e,t){return r(e,a(t))});var b,v=T(Array.prototype.forEach),y=T(Array.prototype.pop),_=T(Array.prototype.push),k=T(String.prototype.toLowerCase),w=T(String.prototype.match),E=T(String.prototype.replace),x=T(String.prototype.indexOf),S=T(String.prototype.trim),A=T(RegExp.prototype.test),C=(b=TypeError,function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return m(b,t)});function T(e){return function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),a=1;a<n;a++)r[a-1]=arguments[a];return g(e,t,r)}}function O(e,t){s&&s(e,null);for(var n=t.length;n--;){var r=t[n];if("string"==typeof r){var a=k(r);a!==r&&(c(t)||(t[n]=a),r=a)}e[r]=!0}return e}function $(e){var t,n=p(null);for(t in e)g(i,e,[t])&&(n[t]=e[t]);return n}function R(e,t){for(;null!==e;){var n=u(e,t);if(n){if(n.get)return T(n.get);if("function"==typeof n.value)return T(n.value)}e=l(e)}return function(e){return console.warn("fallback value for",e),null}}var P=f(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),I=f(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),L=f(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),N=f(["animate","color-profile","cursor","discard","fedropshadow","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),M=f(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover"]),j=f(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),D=f(["#text"]),B=f(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","xmlns","slot"]),F=f(["accent-height","accumulate","additive","alignment-baseline","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),H=f(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),z=f(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),U=d(/\{\{[\w\W]*|[\w\W]*\}\}/gm),W=d(/<%[\w\W]*|[\w\W]*%>/gm),q=d(/^data-[\-\w.\u00B7-\uFFFF]/),G=d(/^aria-[\-\w]+$/),K=d(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),Z=d(/^(?:\w+script|data):/i),Y=d(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),X=d(/^html$/i),V=function(){return"undefined"==typeof window?null:window},J=function(t,n){if("object"!==e(t)||"function"!=typeof t.createPolicy)return null;var r=null;n.currentScript&&n.currentScript.hasAttribute("data-tt-policy-suffix")&&(r=n.currentScript.getAttribute("data-tt-policy-suffix"));var a="dompurify"+(r?"#"+r:"");try{return t.createPolicy(a,{createHTML:function(e){return e}})}catch(e){return console.warn("TrustedTypes policy "+a+" could not be created."),null}};return function t(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:V(),r=function(e){return t(e)};if(r.version="2.3.8",r.removed=[],!n||!n.document||9!==n.document.nodeType)return r.isSupported=!1,r;var o=n.document,i=n.document,s=n.DocumentFragment,c=n.HTMLTemplateElement,l=n.Node,u=n.Element,d=n.NodeFilter,p=n.NamedNodeMap,h=void 0===p?n.NamedNodeMap||n.MozNamedAttrMap:p,g=n.HTMLFormElement,m=n.DOMParser,b=n.trustedTypes,T=u.prototype,Q=R(T,"cloneNode"),ee=R(T,"nextSibling"),te=R(T,"childNodes"),ne=R(T,"parentNode");if("function"==typeof c){var re=i.createElement("template");re.content&&re.content.ownerDocument&&(i=re.content.ownerDocument)}var ae=J(b,o),oe=ae?ae.createHTML(""):"",ie=i,se=ie.implementation,ce=ie.createNodeIterator,le=ie.createDocumentFragment,ue=ie.getElementsByTagName,fe=o.importNode,de={};try{de=$(i).documentMode?i.documentMode:{}}catch(e){}var pe={};r.isSupported="function"==typeof ne&&se&&void 0!==se.createHTMLDocument&&9!==de;var he,ge,me=U,be=W,ve=q,ye=G,_e=Z,ke=Y,we=K,Ee=null,xe=O({},[].concat(a(P),a(I),a(L),a(M),a(D))),Se=null,Ae=O({},[].concat(a(B),a(F),a(H),a(z))),Ce=Object.seal(Object.create(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),Te=null,Oe=null,$e=!0,Re=!0,Pe=!1,Ie=!1,Le=!1,Ne=!1,Me=!1,je=!1,De=!1,Be=!1,Fe=!0,He=!0,ze=!1,Ue={},We=null,qe=O({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]),Ge=null,Ke=O({},["audio","video","img","source","image","track"]),Ze=null,Ye=O({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),Xe="http://www.w3.org/1998/Math/MathML",Ve="http://www.w3.org/2000/svg",Je="http://www.w3.org/1999/xhtml",Qe=Je,et=!1,tt=["application/xhtml+xml","text/html"],nt="text/html",rt=null,at=i.createElement("form"),ot=function(e){return e instanceof RegExp||e instanceof Function},it=function(t){rt&&rt===t||(t&&"object"===e(t)||(t={}),t=$(t),Ee="ALLOWED_TAGS"in t?O({},t.ALLOWED_TAGS):xe,Se="ALLOWED_ATTR"in t?O({},t.ALLOWED_ATTR):Ae,Ze="ADD_URI_SAFE_ATTR"in t?O($(Ye),t.ADD_URI_SAFE_ATTR):Ye,Ge="ADD_DATA_URI_TAGS"in t?O($(Ke),t.ADD_DATA_URI_TAGS):Ke,We="FORBID_CONTENTS"in t?O({},t.FORBID_CONTENTS):qe,Te="FORBID_TAGS"in t?O({},t.FORBID_TAGS):{},Oe="FORBID_ATTR"in t?O({},t.FORBID_ATTR):{},Ue="USE_PROFILES"in t&&t.USE_PROFILES,$e=!1!==t.ALLOW_ARIA_ATTR,Re=!1!==t.ALLOW_DATA_ATTR,Pe=t.ALLOW_UNKNOWN_PROTOCOLS||!1,Ie=t.SAFE_FOR_TEMPLATES||!1,Le=t.WHOLE_DOCUMENT||!1,je=t.RETURN_DOM||!1,De=t.RETURN_DOM_FRAGMENT||!1,Be=t.RETURN_TRUSTED_TYPE||!1,Me=t.FORCE_BODY||!1,Fe=!1!==t.SANITIZE_DOM,He=!1!==t.KEEP_CONTENT,ze=t.IN_PLACE||!1,we=t.ALLOWED_URI_REGEXP||we,Qe=t.NAMESPACE||Je,t.CUSTOM_ELEMENT_HANDLING&&ot(t.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(Ce.tagNameCheck=t.CUSTOM_ELEMENT_HANDLING.tagNameCheck),t.CUSTOM_ELEMENT_HANDLING&&ot(t.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(Ce.attributeNameCheck=t.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),t.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof t.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(Ce.allowCustomizedBuiltInElements=t.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),he=he=-1===tt.indexOf(t.PARSER_MEDIA_TYPE)?nt:t.PARSER_MEDIA_TYPE,ge="application/xhtml+xml"===he?function(e){return e}:k,Ie&&(Re=!1),De&&(je=!0),Ue&&(Ee=O({},a(D)),Se=[],!0===Ue.html&&(O(Ee,P),O(Se,B)),!0===Ue.svg&&(O(Ee,I),O(Se,F),O(Se,z)),!0===Ue.svgFilters&&(O(Ee,L),O(Se,F),O(Se,z)),!0===Ue.mathMl&&(O(Ee,M),O(Se,H),O(Se,z))),t.ADD_TAGS&&(Ee===xe&&(Ee=$(Ee)),O(Ee,t.ADD_TAGS)),t.ADD_ATTR&&(Se===Ae&&(Se=$(Se)),O(Se,t.ADD_ATTR)),t.ADD_URI_SAFE_ATTR&&O(Ze,t.ADD_URI_SAFE_ATTR),t.FORBID_CONTENTS&&(We===qe&&(We=$(We)),O(We,t.FORBID_CONTENTS)),He&&(Ee["#text"]=!0),Le&&O(Ee,["html","head","body"]),Ee.table&&(O(Ee,["tbody"]),delete Te.tbody),f&&f(t),rt=t)},st=O({},["mi","mo","mn","ms","mtext"]),ct=O({},["foreignobject","desc","title","annotation-xml"]),lt=O({},["title","style","font","a","script"]),ut=O({},I);O(ut,L),O(ut,N);var ft=O({},M);O(ft,j);var dt=function(e){var t=ne(e);t&&t.tagName||(t={namespaceURI:Je,tagName:"template"});var n=k(e.tagName),r=k(t.tagName);return e.namespaceURI===Ve?t.namespaceURI===Je?"svg"===n:t.namespaceURI===Xe?"svg"===n&&("annotation-xml"===r||st[r]):Boolean(ut[n]):e.namespaceURI===Xe?t.namespaceURI===Je?"math"===n:t.namespaceURI===Ve?"math"===n&&ct[r]:Boolean(ft[n]):e.namespaceURI===Je&&!(t.namespaceURI===Ve&&!ct[r])&&!(t.namespaceURI===Xe&&!st[r])&&!ft[n]&&(lt[n]||!ut[n])},pt=function(e){_(r.removed,{element:e});try{e.parentNode.removeChild(e)}catch(t){try{e.outerHTML=oe}catch(t){e.remove()}}},ht=function(e,t){try{_(r.removed,{attribute:t.getAttributeNode(e),from:t})}catch(e){_(r.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e&&!Se[e])if(je||De)try{pt(t)}catch(e){}else try{t.setAttribute(e,"")}catch(e){}},gt=function(e){var t,n;if(Me)e="<remove></remove>"+e;else{var r=w(e,/^[\r\n\t ]+/);n=r&&r[0]}"application/xhtml+xml"===he&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");var a=ae?ae.createHTML(e):e;if(Qe===Je)try{t=(new m).parseFromString(a,he)}catch(e){}if(!t||!t.documentElement){t=se.createDocument(Qe,"template",null);try{t.documentElement.innerHTML=et?"":a}catch(e){}}var o=t.body||t.documentElement;return e&&n&&o.insertBefore(i.createTextNode(n),o.childNodes[0]||null),Qe===Je?ue.call(t,Le?"html":"body")[0]:Le?t.documentElement:o},mt=function(e){return ce.call(e.ownerDocument||e,e,d.SHOW_ELEMENT|d.SHOW_COMMENT|d.SHOW_TEXT,null,!1)},bt=function(e){return e instanceof g&&("string"!=typeof e.nodeName||"string"!=typeof e.textContent||"function"!=typeof e.removeChild||!(e.attributes instanceof h)||"function"!=typeof e.removeAttribute||"function"!=typeof e.setAttribute||"string"!=typeof e.namespaceURI||"function"!=typeof e.insertBefore)},vt=function(t){return"object"===e(l)?t instanceof l:t&&"object"===e(t)&&"number"==typeof t.nodeType&&"string"==typeof t.nodeName},yt=function(e,t,n){pe[e]&&v(pe[e],(function(e){e.call(r,t,n,rt)}))},_t=function(e){var t;if(yt("beforeSanitizeElements",e,null),bt(e))return pt(e),!0;if(A(/[\u0080-\uFFFF]/,e.nodeName))return pt(e),!0;var n=ge(e.nodeName);if(yt("uponSanitizeElement",e,{tagName:n,allowedTags:Ee}),e.hasChildNodes()&&!vt(e.firstElementChild)&&(!vt(e.content)||!vt(e.content.firstElementChild))&&A(/<[/\w]/g,e.innerHTML)&&A(/<[/\w]/g,e.textContent))return pt(e),!0;if("select"===n&&A(/<template/i,e.innerHTML))return pt(e),!0;if(!Ee[n]||Te[n]){if(!Te[n]&&wt(n)){if(Ce.tagNameCheck instanceof RegExp&&A(Ce.tagNameCheck,n))return!1;if(Ce.tagNameCheck instanceof Function&&Ce.tagNameCheck(n))return!1}if(He&&!We[n]){var a=ne(e)||e.parentNode,o=te(e)||e.childNodes;if(o&&a)for(var i=o.length-1;i>=0;--i)a.insertBefore(Q(o[i],!0),ee(e))}return pt(e),!0}return e instanceof u&&!dt(e)?(pt(e),!0):"noscript"!==n&&"noembed"!==n||!A(/<\/no(script|embed)/i,e.innerHTML)?(Ie&&3===e.nodeType&&(t=e.textContent,t=E(t,me," "),t=E(t,be," "),e.textContent!==t&&(_(r.removed,{element:e.cloneNode()}),e.textContent=t)),yt("afterSanitizeElements",e,null),!1):(pt(e),!0)},kt=function(e,t,n){if(Fe&&("id"===t||"name"===t)&&(n in i||n in at))return!1;if(Re&&!Oe[t]&&A(ve,t));else if($e&&A(ye,t));else if(!Se[t]||Oe[t]){if(!(wt(e)&&(Ce.tagNameCheck instanceof RegExp&&A(Ce.tagNameCheck,e)||Ce.tagNameCheck instanceof Function&&Ce.tagNameCheck(e))&&(Ce.attributeNameCheck instanceof RegExp&&A(Ce.attributeNameCheck,t)||Ce.attributeNameCheck instanceof Function&&Ce.attributeNameCheck(t))||"is"===t&&Ce.allowCustomizedBuiltInElements&&(Ce.tagNameCheck instanceof RegExp&&A(Ce.tagNameCheck,n)||Ce.tagNameCheck instanceof Function&&Ce.tagNameCheck(n))))return!1}else if(Ze[t]);else if(A(we,E(n,ke,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==x(n,"data:")||!Ge[e])if(Pe&&!A(_e,E(n,ke,"")));else if(n)return!1;return!0},wt=function(e){return e.indexOf("-")>0},Et=function(e){var t,n,a,o;yt("beforeSanitizeAttributes",e,null);var i=e.attributes;if(i){var s={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:Se};for(o=i.length;o--;){var c=t=i[o],l=c.name,u=c.namespaceURI;if(n="value"===l?t.value:S(t.value),a=ge(l),s.attrName=a,s.attrValue=n,s.keepAttr=!0,s.forceKeepAttr=void 0,yt("uponSanitizeAttribute",e,s),n=s.attrValue,!s.forceKeepAttr&&(ht(l,e),s.keepAttr))if(A(/\/>/i,n))ht(l,e);else{Ie&&(n=E(n,me," "),n=E(n,be," "));var f=ge(e.nodeName);if(kt(f,a,n))try{u?e.setAttributeNS(u,l,n):e.setAttribute(l,n),y(r.removed)}catch(e){}}}yt("afterSanitizeAttributes",e,null)}},xt=function e(t){var n,r=mt(t);for(yt("beforeSanitizeShadowDOM",t,null);n=r.nextNode();)yt("uponSanitizeShadowNode",n,null),_t(n)||(n.content instanceof s&&e(n.content),Et(n));yt("afterSanitizeShadowDOM",t,null)};return r.sanitize=function(t,a){var i,c,u,f,d;if((et=!t)&&(t="\x3c!--\x3e"),"string"!=typeof t&&!vt(t)){if("function"!=typeof t.toString)throw C("toString is not a function");if("string"!=typeof(t=t.toString()))throw C("dirty is not a string, aborting")}if(!r.isSupported){if("object"===e(n.toStaticHTML)||"function"==typeof n.toStaticHTML){if("string"==typeof t)return n.toStaticHTML(t);if(vt(t))return n.toStaticHTML(t.outerHTML)}return t}if(Ne||it(a),r.removed=[],"string"==typeof t&&(ze=!1),ze){if(t.nodeName){var p=ge(t.nodeName);if(!Ee[p]||Te[p])throw C("root node is forbidden and cannot be sanitized in-place")}}else if(t instanceof l)1===(c=(i=gt("\x3c!----\x3e")).ownerDocument.importNode(t,!0)).nodeType&&"BODY"===c.nodeName||"HTML"===c.nodeName?i=c:i.appendChild(c);else{if(!je&&!Ie&&!Le&&-1===t.indexOf("<"))return ae&&Be?ae.createHTML(t):t;if(!(i=gt(t)))return je?null:Be?oe:""}i&&Me&&pt(i.firstChild);for(var h=mt(ze?t:i);u=h.nextNode();)3===u.nodeType&&u===f||_t(u)||(u.content instanceof s&&xt(u.content),Et(u),f=u);if(f=null,ze)return t;if(je){if(De)for(d=le.call(i.ownerDocument);i.firstChild;)d.appendChild(i.firstChild);else d=i;return Se.shadowroot&&(d=fe.call(o,d,!0)),d}var g=Le?i.outerHTML:i.innerHTML;return Le&&Ee["!doctype"]&&i.ownerDocument&&i.ownerDocument.doctype&&i.ownerDocument.doctype.name&&A(X,i.ownerDocument.doctype.name)&&(g="<!DOCTYPE "+i.ownerDocument.doctype.name+">\n"+g),Ie&&(g=E(g,me," "),g=E(g,be," ")),ae&&Be?ae.createHTML(g):g},r.setConfig=function(e){it(e),Ne=!0},r.clearConfig=function(){rt=null,Ne=!1},r.isValidAttribute=function(e,t,n){rt||it({});var r=ge(e),a=ge(t);return kt(r,a,n)},r.addHook=function(e,t){"function"==typeof t&&(pe[e]=pe[e]||[],_(pe[e],t))},r.removeHook=function(e){if(pe[e])return y(pe[e])},r.removeHooks=function(e){pe[e]&&(pe[e]=[])},r.removeAllHooks=function(){pe={}},r}()}()}))(window);function Jp(e){var t=function(){if("undefined"==typeof Reflect||!an)return!1;if(an.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(an(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Qa(e);if(t){var a=Qa(this).constructor;n=an(r,arguments,a)}else n=r.apply(this,arguments);return Ya(this,n)}}var Qp=function(e){yn(n,e);var t=Jp(n);function n(){return un(this,n),t.call(this,{needCache:!0})}return ln(n,[{key:"isAutoLinkTag",value:function(e){var t=[/^<([a-z][a-z0-9+.-]{1,31}:\/\/[^<> `]+)>$/i,/^<(mailto:[^<> `]+)>$/i,/^<([^()<>[\]:'@\\,"\s`]+@[^()<>[\]:'@\\,"\s`.]+\.[^()<>[\]:'@\\,"\s`]+)>$/i];return Xp(t).call(t,(function(t){return t.test(e)}))}},{key:"isHtmlComment",value:function(e){return/^<!--.*?-->$/.test(e)}},{key:"beforeMakeHtml",value:function(e,t){var n=this;this.$engine.htmlWhiteListAppend?(this.htmlWhiteListAppend=new RegExp("^(".concat(this.$engine.htmlWhiteListAppend,")( |$|/)"),"i"),this.htmlWhiteList=this.$engine.htmlWhiteListAppend.split("|")):(this.htmlWhiteListAppend=!1,this.htmlWhiteList=[]);var r=e;return r=(r=(r=(r=function(e){if("string"!=typeof e)return"";var t=e.replace(/&(\w+);?/g,(function(e,t){return-1===mf(e).call(e,";")||-1===mf(Pf).call(Pf,t.toLowerCase())?e.replace(/&/g,"&amp;"):e}));return t=(t=t.replace(/&#(?!x)(\d*);?/gi,(function(e,t){return If(t)||-1===mf(e).call(e,";")||t.lenth>7||!Lf(t)?e.replace(/&/g,"&amp;"):e}))).replace(/&#x([0-9a-f]*);?/gi,(function(e,t){if(If(t))return e.replace(/&/g,"&amp;");var n="0x".concat(t),r=Mc(n,16);return isNaN(r)||-1===mf(e).call(e,";")||t.lenth>6||!Lf(n)?e.replace(/&/g,"&amp;"):e}))}(r=Ff(r))).replace(/<[/]?(.*?)>/g,(function(e,t){return jf.test(t)||n.isAutoLinkTag(e)||n.isHtmlComment(e)||!1!==n.htmlWhiteListAppend&&n.htmlWhiteListAppend.test(t)?e.replace(/</g,"$#60;").replace(/>/g,"$#62;"):e.replace(/</g,"&#60;").replace(/>/g,"&#62;")}))).replace(/<(?=\/?(\w|\n|$))/g,"&#60;")).replace(/\$#60;/g,"<").replace(/\$#62;/g,">")}},{key:"makeHtml",value:function(e,t){return e}},{key:"afterMakeHtml",value:function(e){var t=e,n={ALLOW_UNKNOWN_PROTOCOLS:!0,ADD_ATTR:["target"]};if(!1!==this.htmlWhiteListAppend){var r;if(n.ADD_TAGS=this.htmlWhiteList,(this.htmlWhiteListAppend.test("style")||this.htmlWhiteListAppend.test("ALL"))&&(t=t.replace(/<style(>| [^>]*>).*?<\/style>/gi,(function(e){return e.replace(/<br>/gi,"")}))),this.htmlWhiteListAppend.test("iframe")||this.htmlWhiteListAppend.test("ALL"))n.ADD_ATTR=Ns(r=n.ADD_ATTR).call(r,["align","frameborder","height","longdesc","marginheight","marginwidth","name","sandbox","scrolling","seamless","src","srcdoc","width"]),n.SANITIZE_DOM=!1,t=t.replace(/<iframe(>| [^>]*>).*?<\/iframe>/gi,(function(e){return e.replace(/<br>/gi,"").replace(/\n/g,"")}));if(this.htmlWhiteListAppend.test("script")||this.htmlWhiteListAppend.test("ALL"))return t=t.replace(/<script(>| [^>]*>).*?<\/script>/gi,(function(e){return e.replace(/<br>/gi,"")}))}return Bd()||(n.FORBID_ATTR=["data-sign","data-lines"]),Vp.sanitize(t,n)}}]),n}(Hc);eo(Qp,"HOOK_NAME","htmlBlock");var eh={"+1":"1f44d","-1":"1f44e",100:"1f4af",1234:"1f522","1st_place_medal":"1f947","2nd_place_medal":"1f948","3rd_place_medal":"1f949","8ball":"1f3b1",a:"1f170",ab:"1f18e",abacus:"1f9ee",abc:"1f524",abcd:"1f521",accept:"1f251",adhesive_bandage:"1fa79",adult:"1f9d1",aerial_tramway:"1f6a1",afghanistan:"1f1e6-1f1eb",airplane:"2708",aland_islands:"1f1e6-1f1fd",alarm_clock:"23f0",albania:"1f1e6-1f1f1",alembic:"2697",algeria:"1f1e9-1f1ff",alien:"1f47d",ambulance:"1f691",american_samoa:"1f1e6-1f1f8",amphora:"1f3fa",anchor:"2693",andorra:"1f1e6-1f1e9",angel:"1f47c",anger:"1f4a2",angola:"1f1e6-1f1f4",angry:"1f620",anguilla:"1f1e6-1f1ee",anguished:"1f627",ant:"1f41c",antarctica:"1f1e6-1f1f6",antigua_barbuda:"1f1e6-1f1ec",apple:"1f34e",aquarius:"2652",argentina:"1f1e6-1f1f7",aries:"2648",armenia:"1f1e6-1f1f2",arrow_backward:"25c0",arrow_double_down:"23ec",arrow_double_up:"23eb",arrow_down:"2b07",arrow_down_small:"1f53d",arrow_forward:"25b6",arrow_heading_down:"2935",arrow_heading_up:"2934",arrow_left:"2b05",arrow_lower_left:"2199",arrow_lower_right:"2198",arrow_right:"27a1",arrow_right_hook:"21aa",arrow_up:"2b06",arrow_up_down:"2195",arrow_up_small:"1f53c",arrow_upper_left:"2196",arrow_upper_right:"2197",arrows_clockwise:"1f503",arrows_counterclockwise:"1f504",art:"1f3a8",articulated_lorry:"1f69b",artificial_satellite:"1f6f0",artist:"1f9d1-1f3a8",aruba:"1f1e6-1f1fc",ascension_island:"1f1e6-1f1e8",asterisk:"002a-20e3",astonished:"1f632",astronaut:"1f9d1-1f680",athletic_shoe:"1f45f",atm:"1f3e7",atom_symbol:"269b",australia:"1f1e6-1f1fa",austria:"1f1e6-1f1f9",auto_rickshaw:"1f6fa",avocado:"1f951",axe:"1fa93",azerbaijan:"1f1e6-1f1ff",b:"1f171",baby:"1f476",baby_bottle:"1f37c",baby_chick:"1f424",baby_symbol:"1f6bc",back:"1f519",bacon:"1f953",badger:"1f9a1",badminton:"1f3f8",bagel:"1f96f",baggage_claim:"1f6c4",baguette_bread:"1f956",bahamas:"1f1e7-1f1f8",bahrain:"1f1e7-1f1ed",balance_scale:"2696",bald_man:"1f468-1f9b2",bald_woman:"1f469-1f9b2",ballet_shoes:"1fa70",balloon:"1f388",ballot_box:"1f5f3",ballot_box_with_check:"2611",bamboo:"1f38d",banana:"1f34c",bangbang:"203c",bangladesh:"1f1e7-1f1e9",banjo:"1fa95",bank:"1f3e6",bar_chart:"1f4ca",barbados:"1f1e7-1f1e7",barber:"1f488",baseball:"26be",basket:"1f9fa",basketball:"1f3c0",basketball_man:"26f9-2642",basketball_woman:"26f9-2640",bat:"1f987",bath:"1f6c0",bathtub:"1f6c1",battery:"1f50b",beach_umbrella:"1f3d6",bear:"1f43b",bearded_person:"1f9d4",bed:"1f6cf",bee:"1f41d",beer:"1f37a",beers:"1f37b",beetle:"1f41e",beginner:"1f530",belarus:"1f1e7-1f1fe",belgium:"1f1e7-1f1ea",belize:"1f1e7-1f1ff",bell:"1f514",bellhop_bell:"1f6ce",benin:"1f1e7-1f1ef",bento:"1f371",bermuda:"1f1e7-1f1f2",beverage_box:"1f9c3",bhutan:"1f1e7-1f1f9",bicyclist:"1f6b4",bike:"1f6b2",biking_man:"1f6b4-2642",biking_woman:"1f6b4-2640",bikini:"1f459",billed_cap:"1f9e2",biohazard:"2623",bird:"1f426",birthday:"1f382",black_circle:"26ab",black_flag:"1f3f4",black_heart:"1f5a4",black_joker:"1f0cf",black_large_square:"2b1b",black_medium_small_square:"25fe",black_medium_square:"25fc",black_nib:"2712",black_small_square:"25aa",black_square_button:"1f532",blond_haired_man:"1f471-2642",blond_haired_person:"1f471",blond_haired_woman:"1f471-2640",blonde_woman:"1f471-2640",blossom:"1f33c",blowfish:"1f421",blue_book:"1f4d8",blue_car:"1f699",blue_heart:"1f499",blue_square:"1f7e6",blush:"1f60a",boar:"1f417",boat:"26f5",bolivia:"1f1e7-1f1f4",bomb:"1f4a3",bone:"1f9b4",book:"1f4d6",bookmark:"1f516",bookmark_tabs:"1f4d1",books:"1f4da",boom:"1f4a5",boot:"1f462",bosnia_herzegovina:"1f1e7-1f1e6",botswana:"1f1e7-1f1fc",bouncing_ball_man:"26f9-2642",bouncing_ball_person:"26f9",bouncing_ball_woman:"26f9-2640",bouquet:"1f490",bouvet_island:"1f1e7-1f1fb",bow:"1f647",bow_and_arrow:"1f3f9",bowing_man:"1f647-2642",bowing_woman:"1f647-2640",bowl_with_spoon:"1f963",bowling:"1f3b3",boxing_glove:"1f94a",boy:"1f466",brain:"1f9e0",brazil:"1f1e7-1f1f7",bread:"1f35e",breast_feeding:"1f931",bricks:"1f9f1",bride_with_veil:"1f470",bridge_at_night:"1f309",briefcase:"1f4bc",british_indian_ocean_territory:"1f1ee-1f1f4",british_virgin_islands:"1f1fb-1f1ec",broccoli:"1f966",broken_heart:"1f494",broom:"1f9f9",brown_circle:"1f7e4",brown_heart:"1f90e",brown_square:"1f7eb",brunei:"1f1e7-1f1f3",bug:"1f41b",building_construction:"1f3d7",bulb:"1f4a1",bulgaria:"1f1e7-1f1ec",bullettrain_front:"1f685",bullettrain_side:"1f684",burkina_faso:"1f1e7-1f1eb",burrito:"1f32f",burundi:"1f1e7-1f1ee",bus:"1f68c",business_suit_levitating:"1f574",busstop:"1f68f",bust_in_silhouette:"1f464",busts_in_silhouette:"1f465",butter:"1f9c8",butterfly:"1f98b",cactus:"1f335",cake:"1f370",calendar:"1f4c6",call_me_hand:"1f919",calling:"1f4f2",cambodia:"1f1f0-1f1ed",camel:"1f42b",camera:"1f4f7",camera_flash:"1f4f8",cameroon:"1f1e8-1f1f2",camping:"1f3d5",canada:"1f1e8-1f1e6",canary_islands:"1f1ee-1f1e8",cancer:"264b",candle:"1f56f",candy:"1f36c",canned_food:"1f96b",canoe:"1f6f6",cape_verde:"1f1e8-1f1fb",capital_abcd:"1f520",capricorn:"2651",car:"1f697",card_file_box:"1f5c3",card_index:"1f4c7",card_index_dividers:"1f5c2",caribbean_netherlands:"1f1e7-1f1f6",carousel_horse:"1f3a0",carrot:"1f955",cartwheeling:"1f938",cat:"1f431",cat2:"1f408",cayman_islands:"1f1f0-1f1fe",cd:"1f4bf",central_african_republic:"1f1e8-1f1eb",ceuta_melilla:"1f1ea-1f1e6",chad:"1f1f9-1f1e9",chains:"26d3",chair:"1fa91",champagne:"1f37e",chart:"1f4b9",chart_with_downwards_trend:"1f4c9",chart_with_upwards_trend:"1f4c8",checkered_flag:"1f3c1",cheese:"1f9c0",cherries:"1f352",cherry_blossom:"1f338",chess_pawn:"265f",chestnut:"1f330",chicken:"1f414",child:"1f9d2",children_crossing:"1f6b8",chile:"1f1e8-1f1f1",chipmunk:"1f43f",chocolate_bar:"1f36b",chopsticks:"1f962",christmas_island:"1f1e8-1f1fd",christmas_tree:"1f384",church:"26ea",cinema:"1f3a6",circus_tent:"1f3aa",city_sunrise:"1f307",city_sunset:"1f306",cityscape:"1f3d9",cl:"1f191",clamp:"1f5dc",clap:"1f44f",clapper:"1f3ac",classical_building:"1f3db",climbing:"1f9d7",climbing_man:"1f9d7-2642",climbing_woman:"1f9d7-2640",clinking_glasses:"1f942",clipboard:"1f4cb",clipperton_island:"1f1e8-1f1f5",clock1:"1f550",clock10:"1f559",clock1030:"1f565",clock11:"1f55a",clock1130:"1f566",clock12:"1f55b",clock1230:"1f567",clock130:"1f55c",clock2:"1f551",clock230:"1f55d",clock3:"1f552",clock330:"1f55e",clock4:"1f553",clock430:"1f55f",clock5:"1f554",clock530:"1f560",clock6:"1f555",clock630:"1f561",clock7:"1f556",clock730:"1f562",clock8:"1f557",clock830:"1f563",clock9:"1f558",clock930:"1f564",closed_book:"1f4d5",closed_lock_with_key:"1f510",closed_umbrella:"1f302",cloud:"2601",cloud_with_lightning:"1f329",cloud_with_lightning_and_rain:"26c8",cloud_with_rain:"1f327",cloud_with_snow:"1f328",clown_face:"1f921",clubs:"2663",cn:"1f1e8-1f1f3",coat:"1f9e5",cocktail:"1f378",coconut:"1f965",cocos_islands:"1f1e8-1f1e8",coffee:"2615",coffin:"26b0",cold_face:"1f976",cold_sweat:"1f630",collision:"1f4a5",colombia:"1f1e8-1f1f4",comet:"2604",comoros:"1f1f0-1f1f2",compass:"1f9ed",computer:"1f4bb",computer_mouse:"1f5b1",confetti_ball:"1f38a",confounded:"1f616",confused:"1f615",congo_brazzaville:"1f1e8-1f1ec",congo_kinshasa:"1f1e8-1f1e9",congratulations:"3297",construction:"1f6a7",construction_worker:"1f477",construction_worker_man:"1f477-2642",construction_worker_woman:"1f477-2640",control_knobs:"1f39b",convenience_store:"1f3ea",cook:"1f9d1-1f373",cook_islands:"1f1e8-1f1f0",cookie:"1f36a",cool:"1f192",cop:"1f46e",copyright:"00a9",corn:"1f33d",costa_rica:"1f1e8-1f1f7",cote_divoire:"1f1e8-1f1ee",couch_and_lamp:"1f6cb",couple:"1f46b",couple_with_heart:"1f491",couple_with_heart_man_man:"1f468-2764-1f468",couple_with_heart_woman_man:"1f469-2764-1f468",couple_with_heart_woman_woman:"1f469-2764-1f469",couplekiss:"1f48f",couplekiss_man_man:"1f468-2764-1f48b-1f468",couplekiss_man_woman:"1f469-2764-1f48b-1f468",couplekiss_woman_woman:"1f469-2764-1f48b-1f469",cow:"1f42e",cow2:"1f404",cowboy_hat_face:"1f920",crab:"1f980",crayon:"1f58d",credit_card:"1f4b3",crescent_moon:"1f319",cricket:"1f997",cricket_game:"1f3cf",croatia:"1f1ed-1f1f7",crocodile:"1f40a",croissant:"1f950",crossed_fingers:"1f91e",crossed_flags:"1f38c",crossed_swords:"2694",crown:"1f451",cry:"1f622",crying_cat_face:"1f63f",crystal_ball:"1f52e",cuba:"1f1e8-1f1fa",cucumber:"1f952",cup_with_straw:"1f964",cupcake:"1f9c1",cupid:"1f498",curacao:"1f1e8-1f1fc",curling_stone:"1f94c",curly_haired_man:"1f468-1f9b1",curly_haired_woman:"1f469-1f9b1",curly_loop:"27b0",currency_exchange:"1f4b1",curry:"1f35b",cursing_face:"1f92c",custard:"1f36e",customs:"1f6c3",cut_of_meat:"1f969",cyclone:"1f300",cyprus:"1f1e8-1f1fe",czech_republic:"1f1e8-1f1ff",dagger:"1f5e1",dancer:"1f483",dancers:"1f46f",dancing_men:"1f46f-2642",dancing_women:"1f46f-2640",dango:"1f361",dark_sunglasses:"1f576",dart:"1f3af",dash:"1f4a8",date:"1f4c5",de:"1f1e9-1f1ea",deaf_man:"1f9cf-2642",deaf_person:"1f9cf",deaf_woman:"1f9cf-2640",deciduous_tree:"1f333",deer:"1f98c",denmark:"1f1e9-1f1f0",department_store:"1f3ec",derelict_house:"1f3da",desert:"1f3dc",desert_island:"1f3dd",desktop_computer:"1f5a5",detective:"1f575",diamond_shape_with_a_dot_inside:"1f4a0",diamonds:"2666",diego_garcia:"1f1e9-1f1ec",disappointed:"1f61e",disappointed_relieved:"1f625",diving_mask:"1f93f",diya_lamp:"1fa94",dizzy:"1f4ab",dizzy_face:"1f635",djibouti:"1f1e9-1f1ef",dna:"1f9ec",do_not_litter:"1f6af",dog:"1f436",dog2:"1f415",dollar:"1f4b5",dolls:"1f38e",dolphin:"1f42c",dominica:"1f1e9-1f1f2",dominican_republic:"1f1e9-1f1f4",door:"1f6aa",doughnut:"1f369",dove:"1f54a",dragon:"1f409",dragon_face:"1f432",dress:"1f457",dromedary_camel:"1f42a",drooling_face:"1f924",drop_of_blood:"1fa78",droplet:"1f4a7",drum:"1f941",duck:"1f986",dumpling:"1f95f",dvd:"1f4c0","e-mail":"1f4e7",eagle:"1f985",ear:"1f442",ear_of_rice:"1f33e",ear_with_hearing_aid:"1f9bb",earth_africa:"1f30d",earth_americas:"1f30e",earth_asia:"1f30f",ecuador:"1f1ea-1f1e8",egg:"1f95a",eggplant:"1f346",egypt:"1f1ea-1f1ec",eight:"0038-20e3",eight_pointed_black_star:"2734",eight_spoked_asterisk:"2733",eject_button:"23cf",el_salvador:"1f1f8-1f1fb",electric_plug:"1f50c",elephant:"1f418",elf:"1f9dd",elf_man:"1f9dd-2642",elf_woman:"1f9dd-2640",email:"2709",end:"1f51a",england:"1f3f4-e0067-e0062-e0065-e006e-e0067-e007f",envelope:"2709",envelope_with_arrow:"1f4e9",equatorial_guinea:"1f1ec-1f1f6",eritrea:"1f1ea-1f1f7",es:"1f1ea-1f1f8",estonia:"1f1ea-1f1ea",ethiopia:"1f1ea-1f1f9",eu:"1f1ea-1f1fa",euro:"1f4b6",european_castle:"1f3f0",european_post_office:"1f3e4",european_union:"1f1ea-1f1fa",evergreen_tree:"1f332",exclamation:"2757",exploding_head:"1f92f",expressionless:"1f611",eye:"1f441",eye_speech_bubble:"1f441-1f5e8",eyeglasses:"1f453",eyes:"1f440",face_with_head_bandage:"1f915",face_with_thermometer:"1f912",facepalm:"1f926",facepunch:"1f44a",factory:"1f3ed",factory_worker:"1f9d1-1f3ed",fairy:"1f9da",fairy_man:"1f9da-2642",fairy_woman:"1f9da-2640",falafel:"1f9c6",falkland_islands:"1f1eb-1f1f0",fallen_leaf:"1f342",family:"1f46a",family_man_boy:"1f468-1f466",family_man_boy_boy:"1f468-1f466-1f466",family_man_girl:"1f468-1f467",family_man_girl_boy:"1f468-1f467-1f466",family_man_girl_girl:"1f468-1f467-1f467",family_man_man_boy:"1f468-1f468-1f466",family_man_man_boy_boy:"1f468-1f468-1f466-1f466",family_man_man_girl:"1f468-1f468-1f467",family_man_man_girl_boy:"1f468-1f468-1f467-1f466",family_man_man_girl_girl:"1f468-1f468-1f467-1f467",family_man_woman_boy:"1f468-1f469-1f466",family_man_woman_boy_boy:"1f468-1f469-1f466-1f466",family_man_woman_girl:"1f468-1f469-1f467",family_man_woman_girl_boy:"1f468-1f469-1f467-1f466",family_man_woman_girl_girl:"1f468-1f469-1f467-1f467",family_woman_boy:"1f469-1f466",family_woman_boy_boy:"1f469-1f466-1f466",family_woman_girl:"1f469-1f467",family_woman_girl_boy:"1f469-1f467-1f466",family_woman_girl_girl:"1f469-1f467-1f467",family_woman_woman_boy:"1f469-1f469-1f466",family_woman_woman_boy_boy:"1f469-1f469-1f466-1f466",family_woman_woman_girl:"1f469-1f469-1f467",family_woman_woman_girl_boy:"1f469-1f469-1f467-1f466",family_woman_woman_girl_girl:"1f469-1f469-1f467-1f467",farmer:"1f9d1-1f33e",faroe_islands:"1f1eb-1f1f4",fast_forward:"23e9",fax:"1f4e0",fearful:"1f628",feet:"1f43e",female_detective:"1f575-2640",female_sign:"2640",ferris_wheel:"1f3a1",ferry:"26f4",field_hockey:"1f3d1",fiji:"1f1eb-1f1ef",file_cabinet:"1f5c4",file_folder:"1f4c1",film_projector:"1f4fd",film_strip:"1f39e",finland:"1f1eb-1f1ee",fire:"1f525",fire_engine:"1f692",fire_extinguisher:"1f9ef",firecracker:"1f9e8",firefighter:"1f9d1-1f692",fireworks:"1f386",first_quarter_moon:"1f313",first_quarter_moon_with_face:"1f31b",fish:"1f41f",fish_cake:"1f365",fishing_pole_and_fish:"1f3a3",fist:"270a",fist_left:"1f91b",fist_oncoming:"1f44a",fist_raised:"270a",fist_right:"1f91c",five:"0035-20e3",flags:"1f38f",flamingo:"1f9a9",flashlight:"1f526",flat_shoe:"1f97f",fleur_de_lis:"269c",flight_arrival:"1f6ec",flight_departure:"1f6eb",flipper:"1f42c",floppy_disk:"1f4be",flower_playing_cards:"1f3b4",flushed:"1f633",flying_disc:"1f94f",flying_saucer:"1f6f8",fog:"1f32b",foggy:"1f301",foot:"1f9b6",football:"1f3c8",footprints:"1f463",fork_and_knife:"1f374",fortune_cookie:"1f960",fountain:"26f2",fountain_pen:"1f58b",four:"0034-20e3",four_leaf_clover:"1f340",fox_face:"1f98a",fr:"1f1eb-1f1f7",framed_picture:"1f5bc",free:"1f193",french_guiana:"1f1ec-1f1eb",french_polynesia:"1f1f5-1f1eb",french_southern_territories:"1f1f9-1f1eb",fried_egg:"1f373",fried_shrimp:"1f364",fries:"1f35f",frog:"1f438",frowning:"1f626",frowning_face:"2639",frowning_man:"1f64d-2642",frowning_person:"1f64d",frowning_woman:"1f64d-2640",fu:"1f595",fuelpump:"26fd",full_moon:"1f315",full_moon_with_face:"1f31d",funeral_urn:"26b1",gabon:"1f1ec-1f1e6",gambia:"1f1ec-1f1f2",game_die:"1f3b2",garlic:"1f9c4",gb:"1f1ec-1f1e7",gear:"2699",gem:"1f48e",gemini:"264a",genie:"1f9de",genie_man:"1f9de-2642",genie_woman:"1f9de-2640",georgia:"1f1ec-1f1ea",ghana:"1f1ec-1f1ed",ghost:"1f47b",gibraltar:"1f1ec-1f1ee",gift:"1f381",gift_heart:"1f49d",giraffe:"1f992",girl:"1f467",globe_with_meridians:"1f310",gloves:"1f9e4",goal_net:"1f945",goat:"1f410",goggles:"1f97d",golf:"26f3",golfing:"1f3cc",golfing_man:"1f3cc-2642",golfing_woman:"1f3cc-2640",gorilla:"1f98d",grapes:"1f347",greece:"1f1ec-1f1f7",green_apple:"1f34f",green_book:"1f4d7",green_circle:"1f7e2",green_heart:"1f49a",green_salad:"1f957",green_square:"1f7e9",greenland:"1f1ec-1f1f1",grenada:"1f1ec-1f1e9",grey_exclamation:"2755",grey_question:"2754",grimacing:"1f62c",grin:"1f601",grinning:"1f600",guadeloupe:"1f1ec-1f1f5",guam:"1f1ec-1f1fa",guard:"1f482",guardsman:"1f482-2642",guardswoman:"1f482-2640",guatemala:"1f1ec-1f1f9",guernsey:"1f1ec-1f1ec",guide_dog:"1f9ae",guinea:"1f1ec-1f1f3",guinea_bissau:"1f1ec-1f1fc",guitar:"1f3b8",gun:"1f52b",guyana:"1f1ec-1f1fe",haircut:"1f487",haircut_man:"1f487-2642",haircut_woman:"1f487-2640",haiti:"1f1ed-1f1f9",hamburger:"1f354",hammer:"1f528",hammer_and_pick:"2692",hammer_and_wrench:"1f6e0",hamster:"1f439",hand:"270b",hand_over_mouth:"1f92d",handbag:"1f45c",handball_person:"1f93e",handshake:"1f91d",hankey:"1f4a9",hash:"0023-20e3",hatched_chick:"1f425",hatching_chick:"1f423",headphones:"1f3a7",health_worker:"1f9d1-2695",hear_no_evil:"1f649",heard_mcdonald_islands:"1f1ed-1f1f2",heart:"2764",heart_decoration:"1f49f",heart_eyes:"1f60d",heart_eyes_cat:"1f63b",heartbeat:"1f493",heartpulse:"1f497",hearts:"2665",heavy_check_mark:"2714",heavy_division_sign:"2797",heavy_dollar_sign:"1f4b2",heavy_exclamation_mark:"2757",heavy_heart_exclamation:"2763",heavy_minus_sign:"2796",heavy_multiplication_x:"2716",heavy_plus_sign:"2795",hedgehog:"1f994",helicopter:"1f681",herb:"1f33f",hibiscus:"1f33a",high_brightness:"1f506",high_heel:"1f460",hiking_boot:"1f97e",hindu_temple:"1f6d5",hippopotamus:"1f99b",hocho:"1f52a",hole:"1f573",honduras:"1f1ed-1f1f3",honey_pot:"1f36f",honeybee:"1f41d",hong_kong:"1f1ed-1f1f0",horse:"1f434",horse_racing:"1f3c7",hospital:"1f3e5",hot_face:"1f975",hot_pepper:"1f336",hotdog:"1f32d",hotel:"1f3e8",hotsprings:"2668",hourglass:"231b",hourglass_flowing_sand:"23f3",house:"1f3e0",house_with_garden:"1f3e1",houses:"1f3d8",hugs:"1f917",hungary:"1f1ed-1f1fa",hushed:"1f62f",ice_cream:"1f368",ice_cube:"1f9ca",ice_hockey:"1f3d2",ice_skate:"26f8",icecream:"1f366",iceland:"1f1ee-1f1f8",id:"1f194",ideograph_advantage:"1f250",imp:"1f47f",inbox_tray:"1f4e5",incoming_envelope:"1f4e8",india:"1f1ee-1f1f3",indonesia:"1f1ee-1f1e9",infinity:"267e",information_desk_person:"1f481",information_source:"2139",innocent:"1f607",interrobang:"2049",iphone:"1f4f1",iran:"1f1ee-1f1f7",iraq:"1f1ee-1f1f6",ireland:"1f1ee-1f1ea",isle_of_man:"1f1ee-1f1f2",israel:"1f1ee-1f1f1",it:"1f1ee-1f1f9",izakaya_lantern:"1f3ee",jack_o_lantern:"1f383",jamaica:"1f1ef-1f1f2",japan:"1f5fe",japanese_castle:"1f3ef",japanese_goblin:"1f47a",japanese_ogre:"1f479",jeans:"1f456",jersey:"1f1ef-1f1ea",jigsaw:"1f9e9",jordan:"1f1ef-1f1f4",joy:"1f602",joy_cat:"1f639",joystick:"1f579",jp:"1f1ef-1f1f5",judge:"1f9d1-2696",juggling_person:"1f939",kaaba:"1f54b",kangaroo:"1f998",kazakhstan:"1f1f0-1f1ff",kenya:"1f1f0-1f1ea",key:"1f511",keyboard:"2328",keycap_ten:"1f51f",kick_scooter:"1f6f4",kimono:"1f458",kiribati:"1f1f0-1f1ee",kiss:"1f48b",kissing:"1f617",kissing_cat:"1f63d",kissing_closed_eyes:"1f61a",kissing_heart:"1f618",kissing_smiling_eyes:"1f619",kite:"1fa81",kiwi_fruit:"1f95d",kneeling_man:"1f9ce-2642",kneeling_person:"1f9ce",kneeling_woman:"1f9ce-2640",knife:"1f52a",koala:"1f428",koko:"1f201",kosovo:"1f1fd-1f1f0",kr:"1f1f0-1f1f7",kuwait:"1f1f0-1f1fc",kyrgyzstan:"1f1f0-1f1ec",lab_coat:"1f97c",label:"1f3f7",lacrosse:"1f94d",lantern:"1f3ee",laos:"1f1f1-1f1e6",large_blue_circle:"1f535",large_blue_diamond:"1f537",large_orange_diamond:"1f536",last_quarter_moon:"1f317",last_quarter_moon_with_face:"1f31c",latin_cross:"271d",latvia:"1f1f1-1f1fb",laughing:"1f606",leafy_green:"1f96c",leaves:"1f343",lebanon:"1f1f1-1f1e7",ledger:"1f4d2",left_luggage:"1f6c5",left_right_arrow:"2194",left_speech_bubble:"1f5e8",leftwards_arrow_with_hook:"21a9",leg:"1f9b5",lemon:"1f34b",leo:"264c",leopard:"1f406",lesotho:"1f1f1-1f1f8",level_slider:"1f39a",liberia:"1f1f1-1f1f7",libra:"264e",libya:"1f1f1-1f1fe",liechtenstein:"1f1f1-1f1ee",light_rail:"1f688",link:"1f517",lion:"1f981",lips:"1f444",lipstick:"1f484",lithuania:"1f1f1-1f1f9",lizard:"1f98e",llama:"1f999",lobster:"1f99e",lock:"1f512",lock_with_ink_pen:"1f50f",lollipop:"1f36d",loop:"27bf",lotion_bottle:"1f9f4",lotus_position:"1f9d8",lotus_position_man:"1f9d8-2642",lotus_position_woman:"1f9d8-2640",loud_sound:"1f50a",loudspeaker:"1f4e2",love_hotel:"1f3e9",love_letter:"1f48c",love_you_gesture:"1f91f",low_brightness:"1f505",luggage:"1f9f3",luxembourg:"1f1f1-1f1fa",lying_face:"1f925",m:"24c2",macau:"1f1f2-1f1f4",macedonia:"1f1f2-1f1f0",madagascar:"1f1f2-1f1ec",mag:"1f50d",mag_right:"1f50e",mage:"1f9d9",mage_man:"1f9d9-2642",mage_woman:"1f9d9-2640",magnet:"1f9f2",mahjong:"1f004",mailbox:"1f4eb",mailbox_closed:"1f4ea",mailbox_with_mail:"1f4ec",mailbox_with_no_mail:"1f4ed",malawi:"1f1f2-1f1fc",malaysia:"1f1f2-1f1fe",maldives:"1f1f2-1f1fb",male_detective:"1f575-2642",male_sign:"2642",mali:"1f1f2-1f1f1",malta:"1f1f2-1f1f9",man:"1f468",man_artist:"1f468-1f3a8",man_astronaut:"1f468-1f680",man_cartwheeling:"1f938-2642",man_cook:"1f468-1f373",man_dancing:"1f57a",man_facepalming:"1f926-2642",man_factory_worker:"1f468-1f3ed",man_farmer:"1f468-1f33e",man_firefighter:"1f468-1f692",man_health_worker:"1f468-2695",man_in_manual_wheelchair:"1f468-1f9bd",man_in_motorized_wheelchair:"1f468-1f9bc",man_in_tuxedo:"1f935",man_judge:"1f468-2696",man_juggling:"1f939-2642",man_mechanic:"1f468-1f527",man_office_worker:"1f468-1f4bc",man_pilot:"1f468-2708",man_playing_handball:"1f93e-2642",man_playing_water_polo:"1f93d-2642",man_scientist:"1f468-1f52c",man_shrugging:"1f937-2642",man_singer:"1f468-1f3a4",man_student:"1f468-1f393",man_teacher:"1f468-1f3eb",man_technologist:"1f468-1f4bb",man_with_gua_pi_mao:"1f472",man_with_probing_cane:"1f468-1f9af",man_with_turban:"1f473-2642",mandarin:"1f34a",mango:"1f96d",mans_shoe:"1f45e",mantelpiece_clock:"1f570",manual_wheelchair:"1f9bd",maple_leaf:"1f341",marshall_islands:"1f1f2-1f1ed",martial_arts_uniform:"1f94b",martinique:"1f1f2-1f1f6",mask:"1f637",massage:"1f486",massage_man:"1f486-2642",massage_woman:"1f486-2640",mate:"1f9c9",mauritania:"1f1f2-1f1f7",mauritius:"1f1f2-1f1fa",mayotte:"1f1fe-1f1f9",meat_on_bone:"1f356",mechanic:"1f9d1-1f527",mechanical_arm:"1f9be",mechanical_leg:"1f9bf",medal_military:"1f396",medal_sports:"1f3c5",medical_symbol:"2695",mega:"1f4e3",melon:"1f348",memo:"1f4dd",men_wrestling:"1f93c-2642",menorah:"1f54e",mens:"1f6b9",mermaid:"1f9dc-2640",merman:"1f9dc-2642",merperson:"1f9dc",metal:"1f918",metro:"1f687",mexico:"1f1f2-1f1fd",microbe:"1f9a0",micronesia:"1f1eb-1f1f2",microphone:"1f3a4",microscope:"1f52c",middle_finger:"1f595",milk_glass:"1f95b",milky_way:"1f30c",minibus:"1f690",minidisc:"1f4bd",mobile_phone_off:"1f4f4",moldova:"1f1f2-1f1e9",monaco:"1f1f2-1f1e8",money_mouth_face:"1f911",money_with_wings:"1f4b8",moneybag:"1f4b0",mongolia:"1f1f2-1f1f3",monkey:"1f412",monkey_face:"1f435",monocle_face:"1f9d0",monorail:"1f69d",montenegro:"1f1f2-1f1ea",montserrat:"1f1f2-1f1f8",moon:"1f314",moon_cake:"1f96e",morocco:"1f1f2-1f1e6",mortar_board:"1f393",mosque:"1f54c",mosquito:"1f99f",motor_boat:"1f6e5",motor_scooter:"1f6f5",motorcycle:"1f3cd",motorized_wheelchair:"1f9bc",motorway:"1f6e3",mount_fuji:"1f5fb",mountain:"26f0",mountain_bicyclist:"1f6b5",mountain_biking_man:"1f6b5-2642",mountain_biking_woman:"1f6b5-2640",mountain_cableway:"1f6a0",mountain_railway:"1f69e",mountain_snow:"1f3d4",mouse:"1f42d",mouse2:"1f401",movie_camera:"1f3a5",moyai:"1f5ff",mozambique:"1f1f2-1f1ff",mrs_claus:"1f936",muscle:"1f4aa",mushroom:"1f344",musical_keyboard:"1f3b9",musical_note:"1f3b5",musical_score:"1f3bc",mute:"1f507",myanmar:"1f1f2-1f1f2",nail_care:"1f485",name_badge:"1f4db",namibia:"1f1f3-1f1e6",national_park:"1f3de",nauru:"1f1f3-1f1f7",nauseated_face:"1f922",nazar_amulet:"1f9ff",necktie:"1f454",negative_squared_cross_mark:"274e",nepal:"1f1f3-1f1f5",nerd_face:"1f913",netherlands:"1f1f3-1f1f1",neutral_face:"1f610",new:"1f195",new_caledonia:"1f1f3-1f1e8",new_moon:"1f311",new_moon_with_face:"1f31a",new_zealand:"1f1f3-1f1ff",newspaper:"1f4f0",newspaper_roll:"1f5de",next_track_button:"23ed",ng:"1f196",ng_man:"1f645-2642",ng_woman:"1f645-2640",nicaragua:"1f1f3-1f1ee",niger:"1f1f3-1f1ea",nigeria:"1f1f3-1f1ec",night_with_stars:"1f303",nine:"0039-20e3",niue:"1f1f3-1f1fa",no_bell:"1f515",no_bicycles:"1f6b3",no_entry:"26d4",no_entry_sign:"1f6ab",no_good:"1f645",no_good_man:"1f645-2642",no_good_woman:"1f645-2640",no_mobile_phones:"1f4f5",no_mouth:"1f636",no_pedestrians:"1f6b7",no_smoking:"1f6ad","non-potable_water":"1f6b1",norfolk_island:"1f1f3-1f1eb",north_korea:"1f1f0-1f1f5",northern_mariana_islands:"1f1f2-1f1f5",norway:"1f1f3-1f1f4",nose:"1f443",notebook:"1f4d3",notebook_with_decorative_cover:"1f4d4",notes:"1f3b6",nut_and_bolt:"1f529",o:"2b55",o2:"1f17e",ocean:"1f30a",octopus:"1f419",oden:"1f362",office:"1f3e2",office_worker:"1f9d1-1f4bc",oil_drum:"1f6e2",ok:"1f197",ok_hand:"1f44c",ok_man:"1f646-2642",ok_person:"1f646",ok_woman:"1f646-2640",old_key:"1f5dd",older_adult:"1f9d3",older_man:"1f474",older_woman:"1f475",om:"1f549",oman:"1f1f4-1f1f2",on:"1f51b",oncoming_automobile:"1f698",oncoming_bus:"1f68d",oncoming_police_car:"1f694",oncoming_taxi:"1f696",one:"0031-20e3",one_piece_swimsuit:"1fa71",onion:"1f9c5",open_book:"1f4d6",open_file_folder:"1f4c2",open_hands:"1f450",open_mouth:"1f62e",open_umbrella:"2602",ophiuchus:"26ce",orange:"1f34a",orange_book:"1f4d9",orange_circle:"1f7e0",orange_heart:"1f9e1",orange_square:"1f7e7",orangutan:"1f9a7",orthodox_cross:"2626",otter:"1f9a6",outbox_tray:"1f4e4",owl:"1f989",ox:"1f402",oyster:"1f9aa",package:"1f4e6",page_facing_up:"1f4c4",page_with_curl:"1f4c3",pager:"1f4df",paintbrush:"1f58c",pakistan:"1f1f5-1f1f0",palau:"1f1f5-1f1fc",palestinian_territories:"1f1f5-1f1f8",palm_tree:"1f334",palms_up_together:"1f932",panama:"1f1f5-1f1e6",pancakes:"1f95e",panda_face:"1f43c",paperclip:"1f4ce",paperclips:"1f587",papua_new_guinea:"1f1f5-1f1ec",parachute:"1fa82",paraguay:"1f1f5-1f1fe",parasol_on_ground:"26f1",parking:"1f17f",parrot:"1f99c",part_alternation_mark:"303d",partly_sunny:"26c5",partying_face:"1f973",passenger_ship:"1f6f3",passport_control:"1f6c2",pause_button:"23f8",paw_prints:"1f43e",peace_symbol:"262e",peach:"1f351",peacock:"1f99a",peanuts:"1f95c",pear:"1f350",pen:"1f58a",pencil:"1f4dd",pencil2:"270f",penguin:"1f427",pensive:"1f614",people_holding_hands:"1f9d1-1f91d-1f9d1",performing_arts:"1f3ad",persevere:"1f623",person_bald:"1f9d1-1f9b2",person_curly_hair:"1f9d1-1f9b1",person_fencing:"1f93a",person_in_manual_wheelchair:"1f9d1-1f9bd",person_in_motorized_wheelchair:"1f9d1-1f9bc",person_red_hair:"1f9d1-1f9b0",person_white_hair:"1f9d1-1f9b3",person_with_probing_cane:"1f9d1-1f9af",person_with_turban:"1f473",peru:"1f1f5-1f1ea",petri_dish:"1f9eb",philippines:"1f1f5-1f1ed",phone:"260e",pick:"26cf",pie:"1f967",pig:"1f437",pig2:"1f416",pig_nose:"1f43d",pill:"1f48a",pilot:"1f9d1-2708",pinching_hand:"1f90f",pineapple:"1f34d",ping_pong:"1f3d3",pirate_flag:"1f3f4-2620",pisces:"2653",pitcairn_islands:"1f1f5-1f1f3",pizza:"1f355",place_of_worship:"1f6d0",plate_with_cutlery:"1f37d",play_or_pause_button:"23ef",pleading_face:"1f97a",point_down:"1f447",point_left:"1f448",point_right:"1f449",point_up:"261d",point_up_2:"1f446",poland:"1f1f5-1f1f1",police_car:"1f693",police_officer:"1f46e",policeman:"1f46e-2642",policewoman:"1f46e-2640",poodle:"1f429",poop:"1f4a9",popcorn:"1f37f",portugal:"1f1f5-1f1f9",post_office:"1f3e3",postal_horn:"1f4ef",postbox:"1f4ee",potable_water:"1f6b0",potato:"1f954",pouch:"1f45d",poultry_leg:"1f357",pound:"1f4b7",pout:"1f621",pouting_cat:"1f63e",pouting_face:"1f64e",pouting_man:"1f64e-2642",pouting_woman:"1f64e-2640",pray:"1f64f",prayer_beads:"1f4ff",pregnant_woman:"1f930",pretzel:"1f968",previous_track_button:"23ee",prince:"1f934",princess:"1f478",printer:"1f5a8",probing_cane:"1f9af",puerto_rico:"1f1f5-1f1f7",punch:"1f44a",purple_circle:"1f7e3",purple_heart:"1f49c",purple_square:"1f7ea",purse:"1f45b",pushpin:"1f4cc",put_litter_in_its_place:"1f6ae",qatar:"1f1f6-1f1e6",question:"2753",rabbit:"1f430",rabbit2:"1f407",raccoon:"1f99d",racehorse:"1f40e",racing_car:"1f3ce",radio:"1f4fb",radio_button:"1f518",radioactive:"2622",rage:"1f621",railway_car:"1f683",railway_track:"1f6e4",rainbow:"1f308",rainbow_flag:"1f3f3-1f308",raised_back_of_hand:"1f91a",raised_eyebrow:"1f928",raised_hand:"270b",raised_hand_with_fingers_splayed:"1f590",raised_hands:"1f64c",raising_hand:"1f64b",raising_hand_man:"1f64b-2642",raising_hand_woman:"1f64b-2640",ram:"1f40f",ramen:"1f35c",rat:"1f400",razor:"1fa92",receipt:"1f9fe",record_button:"23fa",recycle:"267b",red_car:"1f697",red_circle:"1f534",red_envelope:"1f9e7",red_haired_man:"1f468-1f9b0",red_haired_woman:"1f469-1f9b0",red_square:"1f7e5",registered:"00ae",relaxed:"263a",relieved:"1f60c",reminder_ribbon:"1f397",repeat:"1f501",repeat_one:"1f502",rescue_worker_helmet:"26d1",restroom:"1f6bb",reunion:"1f1f7-1f1ea",revolving_hearts:"1f49e",rewind:"23ea",rhinoceros:"1f98f",ribbon:"1f380",rice:"1f35a",rice_ball:"1f359",rice_cracker:"1f358",rice_scene:"1f391",right_anger_bubble:"1f5ef",ring:"1f48d",ringed_planet:"1fa90",robot:"1f916",rocket:"1f680",rofl:"1f923",roll_eyes:"1f644",roll_of_paper:"1f9fb",roller_coaster:"1f3a2",romania:"1f1f7-1f1f4",rooster:"1f413",rose:"1f339",rosette:"1f3f5",rotating_light:"1f6a8",round_pushpin:"1f4cd",rowboat:"1f6a3",rowing_man:"1f6a3-2642",rowing_woman:"1f6a3-2640",ru:"1f1f7-1f1fa",rugby_football:"1f3c9",runner:"1f3c3",running:"1f3c3",running_man:"1f3c3-2642",running_shirt_with_sash:"1f3bd",running_woman:"1f3c3-2640",rwanda:"1f1f7-1f1fc",sa:"1f202",safety_pin:"1f9f7",safety_vest:"1f9ba",sagittarius:"2650",sailboat:"26f5",sake:"1f376",salt:"1f9c2",samoa:"1f1fc-1f1f8",san_marino:"1f1f8-1f1f2",sandal:"1f461",sandwich:"1f96a",santa:"1f385",sao_tome_principe:"1f1f8-1f1f9",sari:"1f97b",sassy_man:"1f481-2642",sassy_woman:"1f481-2640",satellite:"1f4e1",satisfied:"1f606",saudi_arabia:"1f1f8-1f1e6",sauna_man:"1f9d6-2642",sauna_person:"1f9d6",sauna_woman:"1f9d6-2640",sauropod:"1f995",saxophone:"1f3b7",scarf:"1f9e3",school:"1f3eb",school_satchel:"1f392",scientist:"1f9d1-1f52c",scissors:"2702",scorpion:"1f982",scorpius:"264f",scotland:"1f3f4-e0067-e0062-e0073-e0063-e0074-e007f",scream:"1f631",scream_cat:"1f640",scroll:"1f4dc",seat:"1f4ba",secret:"3299",see_no_evil:"1f648",seedling:"1f331",selfie:"1f933",senegal:"1f1f8-1f1f3",serbia:"1f1f7-1f1f8",service_dog:"1f415-1f9ba",seven:"0037-20e3",seychelles:"1f1f8-1f1e8",shallow_pan_of_food:"1f958",shamrock:"2618",shark:"1f988",shaved_ice:"1f367",sheep:"1f411",shell:"1f41a",shield:"1f6e1",shinto_shrine:"26e9",ship:"1f6a2",shirt:"1f455",poo:"1f4a9",shoe:"1f45e",shopping:"1f6cd",shopping_cart:"1f6d2",shorts:"1fa73",shower:"1f6bf",shrimp:"1f990",shrug:"1f937",shushing_face:"1f92b",sierra_leone:"1f1f8-1f1f1",signal_strength:"1f4f6",singapore:"1f1f8-1f1ec",singer:"1f9d1-1f3a4",sint_maarten:"1f1f8-1f1fd",six:"0036-20e3",six_pointed_star:"1f52f",skateboard:"1f6f9",ski:"1f3bf",skier:"26f7",skull:"1f480",skull_and_crossbones:"2620",skunk:"1f9a8",sled:"1f6f7",sleeping:"1f634",sleeping_bed:"1f6cc",sleepy:"1f62a",slightly_frowning_face:"1f641",slightly_smiling_face:"1f642",slot_machine:"1f3b0",sloth:"1f9a5",slovakia:"1f1f8-1f1f0",slovenia:"1f1f8-1f1ee",small_airplane:"1f6e9",small_blue_diamond:"1f539",small_orange_diamond:"1f538",small_red_triangle:"1f53a",small_red_triangle_down:"1f53b",smile:"1f604",smile_cat:"1f638",smiley:"1f603",smiley_cat:"1f63a",smiling_face_with_three_hearts:"1f970",smiling_imp:"1f608",smirk:"1f60f",smirk_cat:"1f63c",smoking:"1f6ac",snail:"1f40c",snake:"1f40d",sneezing_face:"1f927",snowboarder:"1f3c2",snowflake:"2744",snowman:"26c4",snowman_with_snow:"2603",soap:"1f9fc",sob:"1f62d",soccer:"26bd",socks:"1f9e6",softball:"1f94e",solomon_islands:"1f1f8-1f1e7",somalia:"1f1f8-1f1f4",soon:"1f51c",sos:"1f198",sound:"1f509",south_africa:"1f1ff-1f1e6",south_georgia_south_sandwich_islands:"1f1ec-1f1f8",south_sudan:"1f1f8-1f1f8",space_invader:"1f47e",spades:"2660",spaghetti:"1f35d",sparkle:"2747",sparkler:"1f387",sparkles:"2728",sparkling_heart:"1f496",speak_no_evil:"1f64a",speaker:"1f508",speaking_head:"1f5e3",speech_balloon:"1f4ac",speedboat:"1f6a4",spider:"1f577",spider_web:"1f578",spiral_calendar:"1f5d3",spiral_notepad:"1f5d2",sponge:"1f9fd",spoon:"1f944",squid:"1f991",sri_lanka:"1f1f1-1f1f0",st_barthelemy:"1f1e7-1f1f1",st_helena:"1f1f8-1f1ed",st_kitts_nevis:"1f1f0-1f1f3",st_lucia:"1f1f1-1f1e8",st_martin:"1f1f2-1f1eb",st_pierre_miquelon:"1f1f5-1f1f2",st_vincent_grenadines:"1f1fb-1f1e8",stadium:"1f3df",standing_man:"1f9cd-2642",standing_person:"1f9cd",standing_woman:"1f9cd-2640",star:"2b50",star2:"1f31f",star_and_crescent:"262a",star_of_david:"2721",star_struck:"1f929",stars:"1f320",station:"1f689",statue_of_liberty:"1f5fd",steam_locomotive:"1f682",stethoscope:"1fa7a",stew:"1f372",stop_button:"23f9",stop_sign:"1f6d1",stopwatch:"23f1",straight_ruler:"1f4cf",strawberry:"1f353",stuck_out_tongue:"1f61b",stuck_out_tongue_closed_eyes:"1f61d",stuck_out_tongue_winking_eye:"1f61c",student:"1f9d1-1f393",studio_microphone:"1f399",stuffed_flatbread:"1f959",sudan:"1f1f8-1f1e9",sun_behind_large_cloud:"1f325",sun_behind_rain_cloud:"1f326",sun_behind_small_cloud:"1f324",sun_with_face:"1f31e",sunflower:"1f33b",sunglasses:"1f60e",sunny:"2600",sunrise:"1f305",sunrise_over_mountains:"1f304",superhero:"1f9b8",superhero_man:"1f9b8-2642",superhero_woman:"1f9b8-2640",supervillain:"1f9b9",supervillain_man:"1f9b9-2642",supervillain_woman:"1f9b9-2640",surfer:"1f3c4",surfing_man:"1f3c4-2642",surfing_woman:"1f3c4-2640",suriname:"1f1f8-1f1f7",sushi:"1f363",suspension_railway:"1f69f",svalbard_jan_mayen:"1f1f8-1f1ef",swan:"1f9a2",swaziland:"1f1f8-1f1ff",sweat:"1f613",sweat_drops:"1f4a6",sweat_smile:"1f605",sweden:"1f1f8-1f1ea",sweet_potato:"1f360",swim_brief:"1fa72",swimmer:"1f3ca",swimming_man:"1f3ca-2642",swimming_woman:"1f3ca-2640",switzerland:"1f1e8-1f1ed",symbols:"1f523",synagogue:"1f54d",syria:"1f1f8-1f1fe",syringe:"1f489","t-rex":"1f996",taco:"1f32e",tada:"1f389",taiwan:"1f1f9-1f1fc",tajikistan:"1f1f9-1f1ef",takeout_box:"1f961",tanabata_tree:"1f38b",tangerine:"1f34a",tanzania:"1f1f9-1f1ff",taurus:"2649",taxi:"1f695",tea:"1f375",teacher:"1f9d1-1f3eb",technologist:"1f9d1-1f4bb",teddy_bear:"1f9f8",telephone:"260e",telephone_receiver:"1f4de",telescope:"1f52d",tennis:"1f3be",tent:"26fa",test_tube:"1f9ea",thailand:"1f1f9-1f1ed",thermometer:"1f321",thinking:"1f914",thought_balloon:"1f4ad",thread:"1f9f5",three:"0033-20e3",thumbsdown:"1f44e",thumbsup:"1f44d",ticket:"1f3ab",tickets:"1f39f",tiger:"1f42f",tiger2:"1f405",timer_clock:"23f2",timor_leste:"1f1f9-1f1f1",tipping_hand_man:"1f481-2642",tipping_hand_person:"1f481",tipping_hand_woman:"1f481-2640",tired_face:"1f62b",tm:"2122",togo:"1f1f9-1f1ec",toilet:"1f6bd",tokelau:"1f1f9-1f1f0",tokyo_tower:"1f5fc",tomato:"1f345",tonga:"1f1f9-1f1f4",tongue:"1f445",toolbox:"1f9f0",tooth:"1f9b7",top:"1f51d",tophat:"1f3a9",tornado:"1f32a",tr:"1f1f9-1f1f7",trackball:"1f5b2",tractor:"1f69c",traffic_light:"1f6a5",train:"1f68b",train2:"1f686",tram:"1f68a",triangular_flag_on_post:"1f6a9",triangular_ruler:"1f4d0",trident:"1f531",trinidad_tobago:"1f1f9-1f1f9",tristan_da_cunha:"1f1f9-1f1e6",triumph:"1f624",trolleybus:"1f68e",trophy:"1f3c6",tropical_drink:"1f379",tropical_fish:"1f420",truck:"1f69a",trumpet:"1f3ba",tshirt:"1f455",tulip:"1f337",tumbler_glass:"1f943",tunisia:"1f1f9-1f1f3",turkey:"1f983",turkmenistan:"1f1f9-1f1f2",turks_caicos_islands:"1f1f9-1f1e8",turtle:"1f422",tuvalu:"1f1f9-1f1fb",tv:"1f4fa",twisted_rightwards_arrows:"1f500",two:"0032-20e3",two_hearts:"1f495",two_men_holding_hands:"1f46c",two_women_holding_hands:"1f46d",u5272:"1f239",u5408:"1f234",u55b6:"1f23a",u6307:"1f22f",u6708:"1f237",u6709:"1f236",u6e80:"1f235",u7121:"1f21a",u7533:"1f238",u7981:"1f232",u7a7a:"1f233",uganda:"1f1fa-1f1ec",uk:"1f1ec-1f1e7",ukraine:"1f1fa-1f1e6",umbrella:"2614",unamused:"1f612",underage:"1f51e",unicorn:"1f984",united_arab_emirates:"1f1e6-1f1ea",united_nations:"1f1fa-1f1f3",unlock:"1f513",up:"1f199",upside_down_face:"1f643",uruguay:"1f1fa-1f1fe",us:"1f1fa-1f1f8",us_outlying_islands:"1f1fa-1f1f2",us_virgin_islands:"1f1fb-1f1ee",uzbekistan:"1f1fa-1f1ff",v:"270c",vampire:"1f9db",vampire_man:"1f9db-2642",vampire_woman:"1f9db-2640",vanuatu:"1f1fb-1f1fa",vatican_city:"1f1fb-1f1e6",venezuela:"1f1fb-1f1ea",vertical_traffic_light:"1f6a6",vhs:"1f4fc",vibration_mode:"1f4f3",video_camera:"1f4f9",video_game:"1f3ae",vietnam:"1f1fb-1f1f3",violin:"1f3bb",virgo:"264d",volcano:"1f30b",volleyball:"1f3d0",vomiting_face:"1f92e",vs:"1f19a",vulcan_salute:"1f596",waffle:"1f9c7",wales:"1f3f4-e0067-e0062-e0077-e006c-e0073-e007f",walking:"1f6b6",walking_man:"1f6b6-2642",walking_woman:"1f6b6-2640",wallis_futuna:"1f1fc-1f1eb",waning_crescent_moon:"1f318",waning_gibbous_moon:"1f316",warning:"26a0",wastebasket:"1f5d1",watch:"231a",water_buffalo:"1f403",water_polo:"1f93d",watermelon:"1f349",wave:"1f44b",wavy_dash:"3030",waxing_crescent_moon:"1f312",waxing_gibbous_moon:"1f314",wc:"1f6be",weary:"1f629",wedding:"1f492",weight_lifting:"1f3cb",weight_lifting_man:"1f3cb-2642",weight_lifting_woman:"1f3cb-2640",western_sahara:"1f1ea-1f1ed",whale:"1f433",whale2:"1f40b",wheel_of_dharma:"2638",wheelchair:"267f",white_check_mark:"2705",white_circle:"26aa",white_flag:"1f3f3",white_flower:"1f4ae",white_haired_man:"1f468-1f9b3",white_haired_woman:"1f469-1f9b3",white_heart:"1f90d",white_large_square:"2b1c",white_medium_small_square:"25fd",white_medium_square:"25fb",white_small_square:"25ab",white_square_button:"1f533",wilted_flower:"1f940",wind_chime:"1f390",wind_face:"1f32c",wine_glass:"1f377",wink:"1f609",wolf:"1f43a",woman:"1f469",woman_artist:"1f469-1f3a8",woman_astronaut:"1f469-1f680",woman_cartwheeling:"1f938-2640",woman_cook:"1f469-1f373",woman_dancing:"1f483",woman_facepalming:"1f926-2640",woman_factory_worker:"1f469-1f3ed",woman_farmer:"1f469-1f33e",woman_firefighter:"1f469-1f692",woman_health_worker:"1f469-2695",woman_in_manual_wheelchair:"1f469-1f9bd",woman_in_motorized_wheelchair:"1f469-1f9bc",woman_judge:"1f469-2696",woman_juggling:"1f939-2640",woman_mechanic:"1f469-1f527",woman_office_worker:"1f469-1f4bc",woman_pilot:"1f469-2708",woman_playing_handball:"1f93e-2640",woman_playing_water_polo:"1f93d-2640",woman_scientist:"1f469-1f52c",woman_shrugging:"1f937-2640",woman_singer:"1f469-1f3a4",woman_student:"1f469-1f393",woman_teacher:"1f469-1f3eb",woman_technologist:"1f469-1f4bb",woman_with_headscarf:"1f9d5",woman_with_probing_cane:"1f469-1f9af",woman_with_turban:"1f473-2640",womans_clothes:"1f45a",womans_hat:"1f452",women_wrestling:"1f93c-2640",womens:"1f6ba",woozy_face:"1f974",world_map:"1f5fa",worried:"1f61f",wrench:"1f527",wrestling:"1f93c",writing_hand:"270d",x:"274c",yarn:"1f9f6",yawning_face:"1f971",yellow_circle:"1f7e1",yellow_heart:"1f49b",yellow_square:"1f7e8",yemen:"1f1fe-1f1ea",yen:"1f4b4",yin_yang:"262f",yo_yo:"1fa80",yum:"1f60b",zambia:"1f1ff-1f1f2",zany_face:"1f92a",zap:"26a1",zebra:"1f993",zero:"0030-20e3",zimbabwe:"1f1ff-1f1fc",zipper_mouth_face:"1f910",zombie:"1f9df",zombie_man:"1f9df-2642",zombie_woman:"1f9df-2640",zzz:"1f4a4"};function th(e,t){var n=Ws(e);if(Hu){var r=Hu(e);t&&(r=Ys(r).call(r,(function(t){return qu(e,t).enumerable}))),n.push.apply(n,r)}return n}function nh(e){for(var t=1;t<arguments.length;t++){var n,r,a=null!=arguments[t]?arguments[t]:{};t%2?zs(n=th(Object(a),!0)).call(n,(function(t){eo(e,t,a[t])})):Zu?Xu(e,Zu(a)):zs(r=th(Object(a))).call(r,(function(t){Cs(e,t,qu(a,t))}))}return e}function rh(e){var t=function(){if("undefined"==typeof Reflect||!an)return!1;if(an.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(an(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Qa(e);if(t){var a=Qa(this).constructor;n=an(r,arguments,a)}else n=r.apply(this,arguments);return Ya(this,n)}}function ah(){for(var e=[],t=0,n="",r=0,a=arguments.length;r!==a;++r){var o=+(r<0||arguments.length<=r?void 0:arguments[r]);if(!(o<1114111&&o>>>0===o))throw new RangeError("Invalid code point: ".concat(o));o<=65535?t=e.push(o):(o-=65536,t=e.push(55296+(o>>10),o%1024+56320)),t>=16383&&(n+=String.fromCharCode.apply(null,e),e.length=0)}return n+String.fromCharCode.apply(null,e)}var oh=function(e){yn(n,e);var t=rh(n);function n(){var e,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{config:void 0},a=r.config;if(un(this,n),(e=t.call(this,{config:a})).options={useUnicode:!0,upperCase:!1,customHandled:!1,resourceURL:"https://github.githubassets.com/images/icons/emoji/unicode/${code}.png?v8",emojis:nh({},eh)},"object"!==Ga(a))return Ya(e);var o=a.useUnicode,i=a.customResourceURL,s=a.customRenderer,c=a.upperCase;return e.options.useUnicode="boolean"==typeof o?o:e.options.useUnicode,e.options.upperCase="boolean"==typeof o?c:e.options.upperCase,!1===o&&"string"==typeof i&&(e.options.resourceURL=i),"function"==typeof s&&(e.options.customHandled=!0,e.options.customRenderer=s),e}return ln(n,[{key:"makeHtml",value:function(e,t){var n=this;return this.test(e)?e.replace(this.RULE.reg,(function(e,t){var r;if(n.options.customHandled&&"function"==typeof n.options.customRenderer)return n.options.customRenderer(t);var a=n.options.emojis[t];if("string"!=typeof a)return e;if(n.options.useUnicode){var o,i=mc(o=a.split("-")).call(o,(function(e){return"0x".concat(e)}));return ah.apply(void 0,Mu(i))}n.options.upperCase&&(a=a.toUpperCase());var s=n.options.resourceURL.replace(/\$\{code\}/g,a);return Ns(r='<img class="emoji" src="'.concat(s,'" alt="')).call(r,Bf(t),'" />')})):e}},{key:"rule",value:function(){var e={begin:":",content:"([a-zA-Z0-9+_]+?)",end:":"};return e.reg=nu(e,"g"),e}}]),n}(uc);function ih(e){var t=function(){if("undefined"==typeof Reflect||!an)return!1;if(an.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(an(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Qa(e);if(t){var a=Qa(this).constructor;n=an(r,arguments,a)}else n=r.apply(this,arguments);return Ya(this,n)}}eo(oh,"HOOK_NAME","emoji");var sh=function(e){yn(n,e);var t=ih(n);function n(){return un(this,n),t.apply(this,arguments)}return ln(n,[{key:"makeHtml",value:function(e){return this.test(e)?e.replace(this.RULE.reg,'$1<span style="text-decoration: underline;">$2</span>$3'):e}},{key:"rule",value:function(){var e={begin:"(^| )\\/",end:"\\/( |$)",content:"([^\\n]+?)"};return e.reg=new RegExp(e.begin+e.content+e.end,"g"),e}}]),n}(uc);function ch(e){var t=function(){if("undefined"==typeof Reflect||!an)return!1;if(an.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(an(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Qa(e);if(t){var a=Qa(this).constructor;n=an(r,arguments,a)}else n=r.apply(this,arguments);return Ya(this,n)}}eo(sh,"HOOK_NAME","underline");var lh=function(e){yn(n,e);var t=ch(n);function n(){return un(this,n),t.apply(this,arguments)}return ln(n,[{key:"makeHtml",value:function(e){return this.test(e)?e.replace(this.RULE.reg,"$1<mark>$2</mark>$3"):e}},{key:"rule",value:function(){var e={begin:"(^| )==",end:"==( |$|\\n)",content:"([^\\n]+?)"};return e.reg=new RegExp(e.begin+e.content+e.end,"g"),e}}]),n}(uc);eo(lh,"HOOK_NAME","highLight"),j.JSON||(j.JSON={stringify:JSON.stringify});var uh=function(e,t,n){return p(j.JSON.stringify,null,arguments)},fh=Lt.includes,dh=c((function(){return!Array(1).includes()}));Ye({target:"Array",proto:!0,forced:dh},{includes:function(e){return fh(this,e,arguments.length>1?arguments[1]:void 0)}});var ph=Ts("Array").includes,hh=v("".indexOf);Ye({target:"String",proto:!0,forced:!kp("includes")},{includes:function(e){return!!~hh(Mn(L(this)),Mn(yp(e)),arguments.length>1?arguments[1]:void 0)}});var gh=Ts("String").includes,mh=Array.prototype,bh=String.prototype,vh=function(e){var t=e.includes;return e===mh||F(mh,e)&&t===mh.includes?ph:"string"==typeof e||e===bh||F(bh,e)&&t===bh.includes?gh:t},yh=s.TypeError,_h=function(e,t){if(e<t)throw yh("Not enough arguments");return e},kh=/MSIE .\./.test(H),wh=s.Function,Eh=function(e){return kh?function(t,n){var r=_h(arguments.length,1)>2,a=y(t)?t:wh(t),o=r?Xe(arguments,2):void 0;return e(r?function(){p(a,this,o)}:a,n)}:e},xh={setTimeout:Eh(s.setTimeout),setInterval:Eh(s.setInterval)},Sh=xh.setInterval;Ye({global:!0,bind:!0,forced:s.setInterval!==Sh},{setInterval:Sh});var Ah=xh.setTimeout;Ye({global:!0,bind:!0,forced:s.setTimeout!==Ah},{setTimeout:Ah});var Ch=j.setTimeout;var Th=function(e,t){for(var n=-1,r=null==e?0:e.length,a=Array(r);++n<r;)a[n]=t(e[n],n,e);return a};var Oh=function(e){return"symbol"==typeof e||$i(e)&&"[object Symbol]"==To(e)},$h=yo?yo.prototype:void 0,Rh=$h?$h.toString:void 0;var Ph=function e(t){if("string"==typeof t)return t;if(Mi(t))return Th(t,e)+"";if(Oh(t))return Rh?Rh.call(t):"";var n=t+"";return"0"==n&&1/t==-1/0?"-0":n};var Ih=function(e){return null==e?"":Ph(e)},Lh=/[\\^$.*+?()[\]{}|]/g,Nh=RegExp(Lh.source);var Mh=function(e){return(e=Ih(e))&&Nh.test(e)?e.replace(Lh,"\\$&"):e},jh=dn,Dh=function(){return"CodeMirror.Pass"};function Bh(e){var t=function(){if("undefined"==typeof Reflect||!an)return!1;if(an.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(an(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Qa(e);if(t){var a=Qa(this).constructor;n=an(r,arguments,a)}else n=r.apply(this,arguments);return Ya(this,n)}}var Fh=function(e){yn(n,e);var t=Bh(n);function n(e){var r,a=e.config;return un(this,n),(r=t.call(this,{needCache:!0})).initConfig(a),r.RULE=r.rule(),r}return ln(n,[{key:"initConfig",value:function(e){var t=this,n=e.suggester;this.suggester={},n&&(zs(n).call(n,(function(e){e.suggestList?(e.keyword||(e.keyword="@"),t.suggester[e.keyword]=e):console.warn("[cherry-suggester]: the suggestList of config is missing.")})),zh.hasEditor()&&(zh.editor=null))}},{key:"makeHtml",value:function(e){var t,n;if(!this.RULE.reg)return e;if(!zh.hasEditor()&&Bd()){var r=this.$engine.$cherry.editor;zh.setEditor(r),zh.setSuggester(this.suggester),zh.bindEvent()}return ru()?e.replace(this.RULE.reg,Ps(n=this.toHtml).call(n,this)):ef(e,this.RULE.reg,Ps(t=this.toHtml).call(t,this),!0,1)}},{key:"toHtml",value:function(e,t,n,r){var a,o,i,s,c;return r?(null===(o=this.suggester[n])||void 0===o||null===(i=o.echo)||void 0===i?void 0:i.call(this,r))||Ns(s=Ns(c="".concat(t,'<span class="cherry-suggestion">')).call(c,n)).call(s,r,"</span>"):!1===(null===(a=this.suggester[n])||void 0===a?void 0:a.echo)?"".concat(t):this.suggester[n]?r?t+r:"".concat(t):t+r}},{key:"rule",value:function(){var e,t,n;if(!this.suggester||Ws(this.suggester).length<=0)return{};var r=mc(e=Ws(this.suggester)).call(e,(function(e){return Mh(e)})).join("|");return{reg:new RegExp(Ns(t=Ns(n="".concat(ru()?"((?<!\\\\))[ ]":"(^|[^\\\\])[ ]","(")).call(n,r,")(([^")).call(t,r,"\\s])+)"),"g")}}},{key:"mounted",value:function(){if(!zh.hasEditor()&&Bd()){var e=this.$engine.$cherry.editor;zh.setEditor(e),zh.setSuggester(this.suggester),zh.bindEvent()}}}]),n}(uc);eo(Fh,"HOOK_NAME","suggester");var Hh=function(){function e(){un(this,e),this.searchCache=!1,this.searchKeyCache=[],this.optionList=[],this.cursorMove=!0,this.suggesterConfig={},!this.$suggesterPanel&&Bd()&&(document.body.append(this.createDom(e.panelWrap)),this.$suggesterPanel=document.querySelector(".cherry-suggester-panel"))}return ln(e,[{key:"hasEditor",value:function(){return!!this.editor&&!!this.editor.editor.display&&!!this.editor.editor.display.wrapper}},{key:"setEditor",value:function(e){this.editor=e}},{key:"setSuggester",value:function(e){this.suggesterConfig=e}},{key:"bindEvent",value:function(){var e=this,t=!1;this.editor.editor.on("change",(function(n,r){t=!0,e.onCodeMirrorChange(n,r)})),this.editor.editor.on("keydown",(function(n,r){t=!0,e.enableRelate()&&e.onKeyDown(n,r)})),this.editor.editor.on("cursorActivity",(function(){t||e.stopRelate(),t=!1}));var n=this.editor.editor.getOption("extraKeys"),r=["Up","Down","Enter"];zs(r).call(r,(function(t){if("function"==typeof n[t]){var r=n[t];n[t]=function(e){if(zh.cursorMove){var t=r.call(e,e);return t||Dh()}}}else if(n[t]){if("string"==typeof n[t]){var a=n[t];n[t]=function(t){zh.cursorMove&&e.editor.editor.execCommand(a)}}}else n[t]=function(){if(zh.cursorMove)return Dh()}})),this.editor.editor.setOption("extraKeys",n),this.editor.editor.on("scroll",(function(t,n){e.searchCache&&e.relocatePanel(e.editor.editor)})),this.onClickPancelItem()}},{key:"onClickPancelItem",value:function(){var e=this;this.$suggesterPanel.addEventListener("click",(function(t){var n,r,a,o,i=(n=e.$suggesterPanel,r=t.target,o=-1,zs(a=n.childNodes).call(a,(function(e,t){return e===r?o=t:""})),o);i>-1&&e.pasteSelectResult(i),e.stopRelate()}),!1)}},{key:"showsuggesterPanel",value:function(t){var n=t.left,r=t.top,a=t.items;this.$suggesterPanel||(document.body.append(this.createDom(e.panelWrap)),this.$suggesterPanel=document.querySelector(".cherry-suggester-panel")),this.updatePanel(a),this.$suggesterPanel.style.left="".concat(n,"px"),this.$suggesterPanel.style.top="".concat(r,"px"),this.$suggesterPanel.style.display="block",this.$suggesterPanel.style.position="absolute",this.$suggesterPanel.style.zIndex="100"}},{key:"hidesuggesterPanel",value:function(){this.$suggesterPanel&&(this.$suggesterPanel.style.display="none")}},{key:"updatePanel",value:function(e){var t=this,n=mc(e).call(e,(function(e,n){return t.renderPanelItem(e,0===n)})).join("");this.suggesterConfig[this.keyword]&&this.suggesterConfig[this.keyword].suggestListRender&&(n=this.suggesterConfig[this.keyword].suggestListRender.call(this,e)||n),"string"==typeof n?this.$suggesterPanel.innerHTML=n:"object"===Ga(n)&&1===n.nodeType&&this.$suggesterPanel.append(n)}},{key:"renderPanelItem",value:function(e,t){return t?'<div class="cherry-suggester-panel__item cherry-suggester-panel__item--selected">'.concat(e,"</div>"):'<div class="cherry-suggester-panel__item">'.concat(e,"</div>")}},{key:"createDom",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";this.template||(this.template=document.createElement("div")),this.template.innerHTML=Tc(e).call(e);var t=document.createDocumentFragment();return mc(Array.prototype).call(this.template.childNodes,(function(e,n){t.appendChild(e)})),t}},{key:"relocatePanel",value:function(e){var t=document.querySelector(".CodeMirror-cursors .CodeMirror-cursor");if(!t)return!1;var n=e.getCursor(),r=e.lineInfo(n.line).handle.height,a=t.getBoundingClientRect(),o=a.top+r,i=a.left;this.showsuggesterPanel({left:i,top:o,items:this.optionList})}},{key:"startRelate",value:function(e,t,n){this.cursorFrom=n,this.keyword=t,this.searchCache=!0,this.searchKeyCache=[t],this.relocatePanel(e)}},{key:"stopRelate",value:function(){this.hidesuggesterPanel(),this.cursorFrom=null,this.cursorTo=null,this.keyword="",this.searchKeyCache=[],this.searchCache=!1,this.cursorMove=!0}},{key:"pasteSelectResult",value:function(e){if(this.cursorTo||(this.cursorTo=JSON.parse(uh(this.cursorFrom))),this.cursorTo&&(this.cursorTo.ch+=1,this.optionList[e])){var t,n=Ns(t=" ".concat(this.keyword)).call(t,this.optionList[e]," ");this.editor.editor.replaceRange(n,this.cursorFrom,this.cursorTo)}}},{key:"findSelectedItemIndex",value:function(){return ec(Array.prototype).call(this.$suggesterPanel.childNodes,(function(e){return e.classList.contains("cherry-suggester-panel__item--selected")}))}},{key:"enableRelate",value:function(){return this.searchCache}},{key:"onCodeMirrorChange",value:function(e,t){var n=this,r=t.text,a=t.from,o=t.to,i=t.origin,s=1===r.length?r[0]:"";if(this.suggesterConfig[s])this.startRelate(e,s,a);else if(this.enableRelate()&&(s||"+delete"===i)){if(this.cursorTo=o,s)this.searchKeyCache.push(s);else if("+delete"===i&&(this.searchKeyCache.pop(),0===this.searchKeyCache.length))return void this.stopRelate();this.suggesterConfig[this.keyword].suggestList(this.searchKeyCache.join(""),(function(e){e&&e.length&&(n.optionList=e,n.updatePanel(n.optionList))}))}}},{key:"onKeyDown",value:function(e,t){var n,r=this;if(!this.$suggesterPanel)return!1;var a=t.keyCode;if(vh(n=[38,40]).call(n,a)){this.cursorMove=!1;var o=this.$suggesterPanel.querySelector(".cherry-suggester-panel__item--selected"),i=null;38!==a||o.previousElementSibling?40!==a||o.nextElementSibling?38===a?i=o.previousElementSibling:40===a&&(i=o.nextElementSibling):i=this.$suggesterPanel.firstElementChild:i=this.$suggesterPanel.lastElementChild,o.classList.remove("cherry-suggester-panel__item--selected"),i.classList.add("cherry-suggester-panel__item--selected")}else 13===a&&(t.stopPropagation(),this.cursorMove=!1,this.pasteSelectResult(this.findSelectedItemIndex()),e.focus(),Ch((function(){r.stopRelate()}),0))}}]),e}();eo(Hh,"panelWrap",'<div class="cherry-suggester-panel"></div>');var zh=new Hh;function Uh(e){var t=function(){if("undefined"==typeof Reflect||!an)return!1;if(an.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(an(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Qa(e);if(t){var a=Qa(this).constructor;n=an(r,arguments,a)}else n=r.apply(this,arguments);return Ya(this,n)}}var Wh=function(e){yn(n,e);var t=Uh(n);function n(){return un(this,n),t.apply(this,arguments)}return ln(n,[{key:"makeHtml",value:function(e){return this.test(e)?e.replace(this.RULE.reg,"$1<ruby>$2<rt>$3</rt></ruby>$4"):e}},{key:"rule",value:function(){var e={begin:"(^| )\\{",end:"\\}( |$)",content:"([^\n]+?)\\|([^\n]+?)"};return e.reg=new RegExp(e.begin+e.content+e.end,"g"),e}}]),n}(uc);eo(Wh,"HOOK_NAME","ruby");var qh=[Gf,Zf,Pp,Lp,Qp,Up,qp,Sd,Hd,Dd,op,Hp,Ed,Ud,np,pd,oh,Zd,od,sp,fd,af,nf,sf,pf,ff,Wh,lf,sh,lh,Fh],Gh={run:function(e){var t,n="<div>".concat(e,"</div>");this.tagParser.formatEngine=this.mdFormatEngine,n=n.replace(/<!--[\s\S]*?-->/g,"");var r=this.htmlParser.parseHtml(n);return r=this.paragraphStyleClear(r),Tc(t=this.$dealHtml(r).replace(/\n{3,}/g,"\n\n\n").replace(/&gt;/g,">").replace(/&lt;/g,"<")).call(t,"\n")},$dealHtml:function(e){for(var t="",n=0;n<e.length;n++){var r=e[n];"tag"===r.type?t=this.$handleTagObject(r,t):"text"===r.type&&r.content.length>0&&(t+=r.content.replace(/&nbsp;/g," ").replace(/[\n]+/g,"\n"))}return t},$handleTagObject:function(e,t){var n,r,a,o=t;if(e.attrs.class&&mf(n=e.attrs.class).call(n,"mermaid")>=0)try{o+=["\n```",e.attrs["data-type"],"\n",decodeURIComponent(atob(e.attrs["data-code"])),"\n```\n"].join("")}catch(t){o+=["\n```",e.attrs["data-type"],"\n",decodeURIComponent(e.attrs["data-code"]),"\n```\n"].join("")}else if(e.attrs.class&&mf(r=e.attrs.class).call(r,"mathjax-wrapper")>=0)try{o+=" ".concat(decodeURIComponent(atob(e.attrs["data-source"]))," ")}catch(t){o+=" ".concat(decodeURIComponent(e.attrs["data-source"])," ")}else if(e.attrs["data-control"]&&"tapd-table"===e.attrs["data-control"]){if(o+=["\n```"," tapd-table ",e.attrs["data-size"],"\n"].join(""),e.children[1]&&e.children[1].children[0].content)o+=["\n",e.children[1].children[0].content.replace(/\s+/g,""),"\n```\n"].join("");else o+=["\n",'"工作表":{"数据":{"19::25":" "}} ',"\n```\n"].join("")}else if(e.attrs.class&&mf(a=e.attrs.class).call(a,"ch-icon")>=0){var i;mf(i=e.attrs.class).call(i,"ch-icon-check")>=0?o+="[x]":o+="[ ]"}else o+=this.$dealTag(e);return o},$dealTag:function(e){var t="";return e.children&&(t=this.$dealHtml(e.children)),"style"===e.name?"":"code"===e.name||"pre"===e.name?this.tagParser.codeParser(e,this.$dealCodeTag(e)):"function"==typeof this.tagParser["".concat(e.name,"Parser")]?this.tagParser["".concat(e.name,"Parser")](e,t):t},$dealCodeTag:function(e){if(e.children.length<0)return"";for(var t="",n=0;n<e.children.length;n++){var r=e.children[n];"text"!==r.type?("li"===r.name&&(t+="\n"),"br"===r.name&&(t+="\n"),t+=this.$dealCodeTag(r)):t+=r.content.replace(/&lt;/g,"<").replace(/&gt;/g,">")}return t},htmlParser:{attrRE:/([\w-]+)|['"]{1}([^'"]*)['"]{1}/g,lookup:{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,video:!0,input:!0,keygen:!0,link:!0,menuitem:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0},tagRE:/<(?:"[^"]*"['"]*|'[^']*'['"]*|[^'">])+>/g,empty:jh?jh(null):{},parseTags:function(e){var t,n=this,r=0,a={type:"tag",name:"",voidElement:!1,attrs:{},children:[]};return e.replace(this.attrRE,(function(o){r%2?t=o:0===r?((n.lookup[o]||"/"===e.charAt(e.length-2))&&(a.voidElement=!0),a.name=o):a.attrs[t]=o.replace(/['"]/g,""),r+=1})),a},parseHtml:function(e,t){var n=this,r=t||{};r.components||(r.components=this.empty);var a,o=[],i=-1,s=[],c={},l=!1;return e.replace(this.tagRE,(function(t,u){if(l){if(t!=="</".concat(a.name,">"))return;l=!1}var f,d="/"!==t.charAt(1),p=u+t.length,h=e.charAt(p);d&&(i+=1,"tag"===(a=n.parseTags(t)).type&&r.components[a.name]&&(a.type="component",l=!0),a.voidElement||l||!h||"<"===h||a.children.push({type:"text",content:Fu(e).call(e,p,mf(e).call(e,"<",p))}),c[a.tagName]=a,0===i&&o.push(a),(f=s[i-1])&&f.children.push(a),s[i]=a),d&&!a.voidElement||(i-=1,!l&&"<"!==h&&h&&s[i]&&s[i].children.push({type:"text",content:Fu(e).call(e,p,mf(e).call(e,"<",p))}))})),o}},tagParser:{formatEngine:{},pParser:function(e,t){var n=t.replace(/\t/g,"");return/\n$/.test(n)?n:"".concat(n,"\n")},divParser:function(e,t){var n=t.replace(/\t/g,"");return/\n$/.test(n)?n:"".concat(n,"\n")},spanParser:function(e,t){var n=t.replace(/\t/g,"");return e.attrs&&e.attrs.style,n},codeParser:function(e,t){return this.formatEngine.convertCode(t)},brParser:function(e,t){return this.formatEngine.convertBr(t,"\n")},imgParser:function(e,t){return e.attrs&&"tapd-graph"===e.attrs["data-control"]?this.formatEngine.convertGraph(e.attrs.title,e.attrs.src,e.attrs["data-origin-xml"],e):e.attrs&&e.attrs.src?this.formatEngine.convertImg(e.attrs.alt,e.attrs.src):void 0},videoParser:function(e,t){if(e.attrs&&e.attrs.src)return this.formatEngine.convertVideo(t,e.attrs.src,e.attrs.poster,e.attrs.title)},bParser:function(e,t){for(var n=t.split("\n"),r=[],a=0;a<n.length;a++)r.push(this.formatEngine.convertB(n[a]));return r.join("\n")},iParser:function(e,t){for(var n=t.split("\n"),r=[],a=0;a<n.length;a++)r.push(this.formatEngine.convertI(n[a]));return r.join("\n")},strikeParser:function(e,t){for(var n=t.split("\n"),r=[],a=0;a<n.length;a++)r.push(this.formatEngine.convertStrike(n[a]));return r.join("\n")},delParser:function(e,t){for(var n=t.split("\n"),r=[],a=0;a<n.length;a++)r.push(this.formatEngine.convertDel(n[a]));return r.join("\n")},uParser:function(e,t){for(var n=t.split("\n"),r=[],a=0;a<n.length;a++)r.push(this.formatEngine.convertU(n[a]));return r.join("\n")},aParser:function(e,t){return e.attrs&&e.attrs.href?this.formatEngine.convertA(t,e.attrs.href):""},supParser:function(e,t){return this.formatEngine.convertSup(t)},subParser:function(e,t){return this.formatEngine.convertSub(t)},tdParser:function(e,t){return this.formatEngine.convertTd(t)},trParser:function(e,t){return this.formatEngine.convertTr(t)},thParser:function(e,t){return this.formatEngine.convertTh(t)},theadParser:function(e,t){return this.formatEngine.convertThead(t)},tableParser:function(e,t){return this.formatEngine.convertTable(t)},liParser:function(e,t){return this.formatEngine.convertLi(t)},ulParser:function(e,t){return this.formatEngine.convertUl(t)},olParser:function(e,t){return this.formatEngine.convertOl(t)},strongParser:function(e,t){return this.formatEngine.convertStrong(t)},hrParser:function(e,t){return this.formatEngine.convertHr(t)},h1Parser:function(e,t){return this.formatEngine.convertH1(t)},h2Parser:function(e,t){return this.formatEngine.convertH2(t)},h3Parser:function(e,t){return this.formatEngine.convertH3(t)},h4Parser:function(e,t){return this.formatEngine.convertH4(t)},h5Parser:function(e,t){return this.formatEngine.convertH5(t)},h6Parser:function(e,t){return this.formatEngine.convertH6(t)},blockquoteParser:function(e,t){return this.formatEngine.convertBlockquote(t.replace(/\n+/g,"\n"))},addressParser:function(e,t){return this.formatEngine.convertAddress(t.replace(/\n+/g,"\n"))},styleParser:{colorAttrParser:function(e){var t=e.match(/color:\s*(#[a-zA-Z0-9]{3,6});/);return t&&t[1]?t[1]:""},sizeAttrParser:function(e){var t=e.match(/font-size:\s*([a-zA-Z0-9-]+?);/);if(t&&t[1]){var n,r=0;if(/[0-9]+px/.test(t[1]))r=Tc(n=t[1].replace(/px/,"")).call(n);else switch(t[1]){case"x-small":r=10;break;case"small":r=12;break;case"medium":r=16;break;case"large":r=18;break;case"x-large":r=24;break;case"xx-large":r=32;break;default:r=""}return r>0?r:""}return""},bgColorAttrParser:function(e){var t=e.match(/background-color:\s*([^;]+?);/);if(t&&t[1]){var n="";if(/rgb\([ 0-9]+,[ 0-9]+,[ 0-9]+\)/.test(t[1])){var r,a,o,i,s,c=t[1].match(/rgb\(([ 0-9]+),([ 0-9]+),([ 0-9]+)\)/);if(c[1]&&c[2]&&c[3])c[1]=Mc(Tc(r=c[1]).call(r),10),c[2]=Mc(Tc(a=c[2]).call(a),10),c[3]=Mc(Tc(o=c[3]).call(o),10),n=Ns(i=Ns(s="#".concat(c[1].toString(16))).call(s,c[2].toString(16))).call(i,c[3].toString(16))}else{n=gf(t,2)[1]}return n}return""}}},mdFormatEngine:{convertColor:function(e,t){var n,r=Tc(e).call(e);return!r||/\n/.test(r)?r:t?Ns(n="!!".concat(t," ")).call(n,r,"!!"):r},convertSize:function(e,t){var n,r=Tc(e).call(e);return!r||/\n/.test(r)?r:t?Ns(n="!".concat(t," ")).call(n,r,"!"):r},convertBgColor:function(e,t){var n,r=Tc(e).call(e);return!r||/\n/.test(r)?r:t?Ns(n="!!!".concat(t," ")).call(n,r,"!!!"):r},convertBr:function(e,t){return e+t},convertCode:function(e){return/\n/.test(e)?"\n```\n".concat(e.replace(/\n+$/,""),"\n```\n"):" `".concat(e.replace(/`/g,"\\`"),"` ")},convertB:function(e){return/^\s*$/.test(e)?"":"**".concat(e,"**")},convertI:function(e){return/^\s*$/.test(e)?"":"*".concat(e,"*")},convertU:function(e){return/^\s*$/.test(e)?"":" /".concat(e,"/ ")},convertImg:function(e,t){var n,r=e&&e.length>0?r:"image";return Ns(n="![".concat(r,"](")).call(n,t,")")},convertGraph:function(e,t,n,r){var a,o,i,s=e&&e.length>0?e:"graph",c="";if(r)try{var l,u=r.attrs;zs(l=Ws(u)).call(l,(function(e){var t;Object.prototype.hasOwnProperty.call(u,e)&&(mf(e).call(e,"data-graph-")>=0&&u[e]&&(c+=Ns(t=" ".concat(e,"=")).call(t,u[e])))}))}catch(e){}return Ns(a=Ns(o=Ns(i="![".concat(s,"](")).call(i,t,"){data-control=tapd-graph data-origin-xml=")).call(o,n)).call(a,c,"}")},convertVideo:function(e,t,n,r){var a,o,i=r&&r.length>0?r:"video";return Ns(a=Ns(o="!video[".concat(i,"](")).call(o,t,"){poster=")).call(a,n,"}")},convertA:function(e,t){var n;if(e===t)return"".concat(e," ");var r=Tc(e).call(e);return r?Ns(n="[".concat(r,"](")).call(n,t,")"):r},convertSup:function(e){return"^".concat(Tc(e).call(e).replace(/\^/g,"\\^"),"^")},convertSub:function(e){return"^^".concat(Tc(e).call(e).replace(/\^\^/g,"\\^\\^"),"^^")},convertTd:function(e){return"~|".concat(Tc(e).call(e).replace(/\n/g,"<br>")," ~|")},convertTh:function(e){return"~|".concat(Tc(e).call(e).replace(/\n/g,"<br>")," ~|")},convertTr:function(e){return"".concat(e,"\n")},convertThead:function(e){return"".concat(e.replace(/~\|~\|/g,"~|").replace(/~\|/g,"|"),"|:--|\n")},convertTable:function(e){var t="\n".concat(e.replace(/~\|~\|/g,"~|").replace(/~\|/g,"|"),"\n");return/\|:--\|/.test(t)?t:"\n| |\n|:--|".concat(t)},convertLi:function(e){return"- ".concat(e.replace(/^\n/,"").replace(/\n+$/,"").replace(/\n+/g,"\n\t"),"\n")},convertUl:function(e){return"\n\n".concat(e,"\n\n")},convertOl:function(e){for(var t=e.split("\n"),n=1,r=0;r<t.length;r++)/^- /.test(t[r])&&(t[r]=t[r].replace(/^- /,"".concat(n,". ")),n+=1);var a=t.join("\n");return"\n\n".concat(a,"\n\n")},convertStrong:function(e){return/^\s*$/.test(e)?"":"**".concat(e,"**")},convertStrike:function(e){return/^\s*$/.test(e)?"":"~~".concat(e,"~~")},convertDel:function(e){return/^\s*$/.test(e)?"":"~~".concat(e,"~~")},convertHr:function(e){return/^\s*$/.test(e)?"\n\n----\n":"\n\n----\n".concat(e)},convertH1:function(e){return"# ".concat(Tc(e).call(e).replace(/\n+$/,""),"\n\n")},convertH2:function(e){return"## ".concat(Tc(e).call(e).replace(/\n+$/,""),"\n\n")},convertH3:function(e){return"### ".concat(Tc(e).call(e).replace(/\n+$/,""),"\n\n")},convertH4:function(e){return"#### ".concat(Tc(e).call(e).replace(/\n+$/,""),"\n\n")},convertH5:function(e){return"##### ".concat(Tc(e).call(e).replace(/\n+$/,""),"\n\n")},convertH6:function(e){return"###### ".concat(Tc(e).call(e).replace(/\n+$/,""),"\n\n")},convertBlockquote:function(e){return"\n>".concat(Tc(e).call(e),"\n\n")},convertAddress:function(e){return"\n>".concat(Tc(e).call(e),"\n\n")}},paragraphStyleClear:function(e){for(var t=0;t<e[0].children.length;t++){for(var n=[e[0].children[t]],r=[];n.length;){var a=n.shift(),o=this.notEmptyTagCount(a);if(1===o)r.push(a);else if(o>1)for(var i=0;i<a.children.length;i++)n.push(a.children[i]);else 1===r.length&&this.clearChildColorAttrs(r.pop()),r=[]}1===r.length&&this.clearChildColorAttrs(r.pop())}return e},notEmptyTagCount:function(e){if(!e||e.voidElement||"tag"===e.type&&!e.children.length||"text"===e.type&&!e.content.replace(/(\r|\n|\s)+/g,""))return 0;if(e.children&&e.children.length){for(var t=0,n=0;n<e.children.length;n++)t+=this.notEmptyTagCount(e.children[n]);return t}return 1},clearChildColorAttrs:function(e){var t=this;this.forEachHtmlParsedItems(e,(function(e){t.clearSelfNodeColorAttrs(e)}))},clearSelfNodeColorAttrs:function(e){if(e.attrs&&e.attrs.style){for(var t=e.attrs.style.split(";"),n=[],r=0;r<t.length;r++){var a;t[r]&&-1===mf(a=t[r]).call(a,"color")&&n.push(t[r])}n.length?e.attrs.style="".concat(n.join(";"),";"):delete e.attrs.style}},forEachHtmlParsedItems:function(e,t){if(e&&(t(e),e.children&&e.children.length))for(var n=0;n<e.children.length;n++)this.forEachHtmlParsedItems(e.children[n],t)}},Kh=function(){function e(t,n){un(this,e),this.$cherry=n,Cs(this,"_cherry",{get:function(){return Yl.warn("`_engine._cherry` is deprecated. Use `$engine.$cherry` instead."),this.$cherry}}),this.initMath(t),this.$configInit(t),this.hookCenter=new tu(qh,t),this.hooks=this.hookCenter.getHookList(),this.md5Cache={},this.md5StrMap={},this.markdownParams=t,this.currentStrMd5=[],this.htmlWhiteListAppend=t.engine.global.htmlWhiteList}return ln(e,[{key:"initMath",value:function(e){var t=e.externals,n=e.engine.syntax,r=n.mathBlock.plugins;if(Bd()&&(n.mathBlock.src||n.inlineMath.src)&&!t.MathJax&&!window.MathJax){!function(e){if(Bd()){var t=e?["input/asciimath","[tex]/noerrors","[tex]/cancel","[tex]/color","[tex]/boldsymbol"]:[];window.MathJax={startup:{elements:[".Cherry-Math",".Cherry-InlineMath"],typeset:!0},tex:{inlineMath:[["$","$"],["\\(","\\)"]],displayMath:[["$$","$$"],["\\[","\\]"]],tags:"ams",packages:{"[+]":["noerrors","cancel","color"]},macros:{bm:["{\\boldsymbol{#1}}",1]}},options:{skipHtmlTags:["script","noscript","style","textarea","pre","code","a"],ignoreHtmlClass:"tex2jax_ignore",processHtmlClass:"tex2jax_process",enableMenu:!1},loader:{load:t}}}}(r);var a=document.createElement("script");a.src=n.mathBlock.src?n.mathBlock.src:n.inlineMath.src,a.async=!0,a.src&&document.head.appendChild(a)}}},{key:"$configInit",value:function(e){if(e.hooksConfig&&ql(e.hooksConfig.hooksList,Array))for(var t=0;t<e.hooksConfig.hooksList.length;t++){var n=e.hooksConfig.hooksList[t];try{"sentence"===n.getType()&&Gl(n,uc),"paragraph"===n.getType()&&Gl(n,Hc),Kl(n),qh.push(n)}catch(e){throw new Error("the hook does not correctly inherit")}}}},{key:"$beforeMakeHtml",value:function(e){var t=e.replace(/~/g,"~T");return"\n"!==(t=(t=(t=t.replace(/\$/g,"~D")).replace(/\r\n/g,"\n")).replace(/\r/g,"\n"))[t.length-1]&&(t+="\n"),t=this.$fireHookAction(t,"sentence","beforeMakeHtml"),t=this.$fireHookAction(t,"paragraph","beforeMakeHtml")}},{key:"$afterMakeHtml",value:function(e){var t=this.$fireHookAction(e,"paragraph","afterMakeHtml");return t=(t=(t=(t=(t=t.replace(/~D/g,"$")).replace(/~T/g,"~")).replace(/\\<\//g,"\\ </")).replace(new RegExp("\\\\(".concat(ou,")"),"g"),(function(e,t){return"&"===t?e:Df(t)})).replace(/\\&(?!(amp|lt|gt|quot|apos);)/,(function(){return"&amp;"}))).replace(/\\ <\//g,"\\</"),t=rd.restoreAll(t)}},{key:"$dealSentenceByCache",value:function(e){var t=this;return this.$checkCache(e,(function(e){return t.$dealSentence(e)}))}},{key:"$dealSentence",value:function(e){var t;return this.$fireHookAction(e,"sentence","makeHtml",Ps(t=this.$dealSentenceByCache).call(t,this))}},{key:"$fireHookAction",value:function(e,t,n,r){var a=this,o=e,i="afterMakeHtml"===n?"reduceRight":"reduce";if(!this.hooks&&!this.hooks[t]&&!this.hooks[t][i])return o;try{o=this.hooks[t][i]((function(e,t){return t.$engine||(t.$engine=a,Cs(t,"_engine",{get:function(){return Yl.warn("`this._engine` is deprecated. Use `this.$engine` instead."),this.$engine}})),t[n]?t[n](e,r,a.markdownParams):e}),o)}catch(e){throw new Zl(e)}return o}},{key:"md5",value:function(e){return this.md5StrMap[e]||(this.md5StrMap[e]=ed(e)),this.md5StrMap[e]}},{key:"$checkCache",value:function(e,t){var n=this.md5(e);return void 0===this.md5Cache[n]&&(this.md5Cache[n]=t(e)),{sign:n,html:this.md5Cache[n]}}},{key:"$dealParagraph",value:function(e){var t;return this.$fireHookAction(e,"paragraph","makeHtml",Ps(t=this.$dealSentenceByCache).call(t,this))}},{key:"makeHtml",value:function(e){var t=this.$beforeMakeHtml(e);return t=this.$dealParagraph(t),t=this.$afterMakeHtml(t)}},{key:"mounted",value:function(){this.$fireHookAction("","sentence","mounted"),this.$fireHookAction("","paragraph","mounted")}},{key:"makeMarkdown",value:function(e){return Gh.run(e)}}]),e}(),Zh={zh_CN:{bold:"加粗",code:"代码",graph:"画图",h1:"一级标题",h2:"二级标题",h3:"三级标题",h4:"四级标题",h5:"五级标题",header:"标题",insert:"插入",italic:"斜体",list:"列表",quickTable:"表格",quote:"引用",size:"大小",color:"文字颜色&背景",strikethrough:"删除线",sub:"下标",sup:"上标",togglePreview:"预览",fullScreen:"全屏",image:"图片",audio:"音频",video:"视频",link:"超链接",hr:"分隔线",br:"换行",toc:"目录",pdf:"pdf",word:"word",table:"表格","line-table":"折线表格","bar-table":"柱状表格",formula:"公式",insertFormula:"公式",insertFlow:"流程图",insertSeq:"时序图",insertState:"状态图",insertClass:"类图",insertPie:"饼图",insertGantt:"甘特图",checklist:"清单",ol:"有序列表",ul:"无序列表",undo:"撤销",redo:"恢复",previewClose:"关闭预览",codeTheme:"代码主题",switchModel:"模式切换",switchPreview:"预览",switchEdit:"返回编辑",classicBr:"经典换行",normalBr:"常规换行",settings:"设置",mobilePreview:"移动端预览",copy:"复制内容",export:"导出"}};function Yh(e,t){return e.style.left="".concat(t.left+t.width/2,"px"),e.style.top="".concat(t.top+t.height,"px"),e}function Xh(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"absolute",n=e.getBoundingClientRect();return"fixed"===t?n:{left:e.offsetLeft,top:e.offsetTop,width:n.width,height:n.height}}var Vh=function(){function e(t,n,r,a,o){var i=arguments.length>5&&void 0!==arguments[5]?arguments[5]:"absolute";un(this,e),this.name=n,this.dom=null,this.visible=!1,this.context=t,this.positionModel=i,this.init(n,r,a,o)}return ln(e,[{key:"init",value:function(e,t,n,r){var a=this,o=function(e,t,n){var r=$p("div",t,{name:e});return Yh(r,n),r}(e,"cherry-dropdown",t);o.style.position=this.positionModel;var i="function"==typeof(null==r?void 0:r.click)?r.click:this.onClick;zs(n).call(n,(function(e){var t=$p("span","cherry-dropdown-item");if(e.noIcon)t.innerHTML="".concat(e.name);else{var n=$p("i","ch-icon ch-icon-".concat(e.iconName));t.appendChild(n),t.innerHTML+=Zh.zh_CN[e.name]||Bf(e.name)}t.addEventListener("click",Ps(i).call(i,a.context,e.onclick,e.async),!1),o.appendChild(t)})),o.addEventListener("EditorHideToolbarSubMenu",(function(){a.hide()})),this.dom=o}},{key:"show",value:function(e){e&&Yh(this.dom,e),this.dom.style.display="block",this.visible=!0}},{key:"hide",value:function(){this.dom.style.display="none",this.visible=!1}},{key:"onClick",value:function(){}}]),e}(),Jh=function(){function e(t){un(this,e),eo(this,"_onClick",void 0),this.bubbleMenu=!1,this.subMenu=null,this.name="",this.editor=t,this.dom=null,this.updateMarkdown=!0,this.subMenuConfig=[],this.positionModel="absolute","function"==typeof this._onClick&&(Yl.warn("`MenuBase._onClick` is deprecated. Override `$onClick` instead"),this.$onClick=this._onClick)}return ln(e,[{key:"getSubMenuConfig",value:function(){return this.subMenuConfig}},{key:"setName",value:function(e,t){this.name=e,this.iconName=t}},{key:"createBtn",value:function(){var e,t,n=[];this.subMenuConfig.length>0&&n.push("cherry-toolbar-dropdown"),n.push("cherry-toolbar-button","cherry-toolbar-".concat(this.iconName?this.iconName:this.name));var r=$p("span",n.join(" "));if(this.iconName&&!vh(e=["insert","graph"]).call(e,this.name)){var a=$p("i","ch-icon ch-icon-".concat(this.iconName),{title:Zh.zh_CN[this.name]||Bf(this.name)});r.appendChild(a)}else r.innerHTML=Zh.zh_CN[this.name]||Bf(this.name);return r.addEventListener("click",Ps(t=this.$onClick).call(t,this),!1),this.dom=r,r}},{key:"$onClick",value:function(t){var n=this;if(t.stopPropagation(),this.subMenuConfig.length)return this.toggleSubMenu();if("function"==typeof this.onClick){e.cleanSubMenu();var r=this.editor.editor.getSelections(),a=mc(r).call(r,(function(e,r,a){return n.onClick(e,"",t)||a[r]}));!this.bubbleMenu&&this.updateMarkdown&&(this.editor.editor.replaceSelections(a,"around"),this.editor.editor.focus())}}},{key:"onKeyDown",value:function(e,t,n){var r=this,a=mc(t).call(t,(function(e){return r.onClick(e,n)}));return e.replaceSelections(a,"around")}},{key:"bindSubClick",value:function(e,t){return this.onClick(t,e)}},{key:"onClick",value:function(e,t,n){return e}},{key:"shortcutKeys",get:function(){return[]}},{key:"shortcutKey",value:function(e){var t,n=this;return Ld(t=this.shortcutKeys).call(t,(function(t,r){var a;return t[(a=r,e&&e.isMac?a.replace(/mod/i,"Command"):a.replace(/mod/i,"Ctrl"))]=function(e,t){return n.onKeyDown(t,t.getSelections(),r)},t}),{})}},{key:"initSubMenu",value:function(){if(this.subMenuConfig.length){/cherry-bubble/.test(this.dom.parentElement.className)?this.positionModel="fixed":this.positionModel="absolute";var e=Xh(this.dom,this.positionModel);this.subMenu=new Vh(this,this.name,e,this.subMenuConfig,{click:this.onSubClick},this.positionModel),this.editor.options.wrapperDom.appendChild(this.subMenu.dom)}}},{key:"showSubMenu",value:function(){if(!this.subMenu)return this.initSubMenu(),e.hideSubMenuExcept(this.subMenu.name),void this.subMenu.show();e.hideSubMenuExcept(this.subMenu.name),/cherry-bubble/.test(this.dom.parentElement.className)?this.positionModel="fixed":this.positionModel="absolute";var t=Xh(this.dom,this.positionModel);this.subMenu.show(t)}},{key:"hideSubMenu",value:function(){this.subMenu&&this.subMenu.hide()}},{key:"toggleSubMenu",value:function(){if(this.subMenu&&this.subMenu.visible)return this.hideSubMenu();this.showSubMenu()}},{key:"onSubClick",value:function(e,t,n){if(t){var r,a=this.editor.editor.getSelection();e(a,!0,Ps(r=this.editor.editor.replaceSelection).call(r,this.editor.editor))}else{var o=this.editor.editor.getSelections(),i=mc(o).call(o,(function(t,n,r){return e(t)||r[n]}));this.updateMarkdown&&(this.editor.editor.replaceSelections(i,"around"),this.editor.editor.focus())}this.hideSubMenu()}}],[{key:"cleanSubMenu",value:function(){this.hideSubMenuExcept(null)}},{key:"hideSubMenuExcept",value:function(e){var t=document.querySelectorAll(".cherry-dropdown");zs(t).call(t,(function(t){e&&t.dataset.name===e||t.dispatchEvent(new Event("EditorHideToolbarSubMenu"))}))}}]),e}();function Qh(e){var t=function(){if("undefined"==typeof Reflect||!an)return!1;if(an.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(an(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Qa(e);if(t){var a=Qa(this).constructor;n=an(r,arguments,a)}else n=r.apply(this,arguments);return Ya(this,n)}}function eg(e,t,n){var r,a={};return zs(r=Ws(e)).call(r,(function(r){-1!==mf(t).call(t,r)&&("object"===Ga(n)?"string"==typeof n[r]?Ga(e[r])===n[r]&&(a[r]=e[r]):e[r]instanceof n[r]&&(a[r]=e[r]):"string"==typeof n&&Ga(e[r])===n&&(a[r]=e[r]))})),a}var tg={HOOKS_TYPE_LIST:lc},ng=[];Bd()||zs(ng).call(ng,(function(e){}));var rg=function(){function e(){un(this,e)}return ln(e,null,[{key:"usePlugin",value:function(t){var n;if(this===e)throw new Error("`usePlugin` is not allowed to called through CherryStatic class.");if(this.initialized)throw new Error("The function `usePlugin` should be called before Cherry is instantiated.");if(!0!==t.$cherry$mounted){for(var r=arguments.length,a=new Array(r>1?r-1:0),o=1;o<r;o++)a[o-1]=arguments[o];t.install.apply(t,Ns(n=[this.config.defaults]).call(n,a)),t.$cherry$mounted=!0}}}]),e}();eo(rg,"createSyntaxHook",(function(e,t,n){var r,a=t===lc.PAR?Hc:uc,o=eg(n,["beforeMakeHtml","makeHtml","afterMakeHtml","rule","test"],"function"),i={needCache:n.needCache,defaultCache:n.defaultCache};return eo(r=function(e){yn(r,e);var n=Qh(r);function r(){var e,a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return un(this,r),(e=t===lc.PAR?n.call(this,{needCache:!!i.needCache,defaultCache:i.defaultCache}):n.call(this)).config=a.config,Ya(e)}return ln(r,[{key:"beforeMakeHtml",value:function(){for(var e,t,n=arguments.length,a=new Array(n),i=0;i<n;i++)a[i]=arguments[i];return o.beforeMakeHtml?o.beforeMakeHtml.apply(this,a):(e=vd(Qa(r.prototype),"beforeMakeHtml",this)).call.apply(e,Ns(t=[this]).call(t,a))}},{key:"makeHtml",value:function(){for(var e,t,n=arguments.length,a=new Array(n),i=0;i<n;i++)a[i]=arguments[i];return o.makeHtml?o.makeHtml.apply(this,a):(e=vd(Qa(r.prototype),"makeHtml",this)).call.apply(e,Ns(t=[this]).call(t,a))}},{key:"afterMakeHtml",value:function(){for(var e,t,n=arguments.length,a=new Array(n),i=0;i<n;i++)a[i]=arguments[i];return o.afterMakeHtml?o.afterMakeHtml.apply(this,a):(e=vd(Qa(r.prototype),"afterMakeHtml",this)).call.apply(e,Ns(t=[this]).call(t,a))}},{key:"test",value:function(){for(var e,t,n=arguments.length,a=new Array(n),i=0;i<n;i++)a[i]=arguments[i];return o.test?o.test.apply(this,a):(e=vd(Qa(r.prototype),"test",this)).call.apply(e,Ns(t=[this]).call(t,a))}},{key:"rule",value:function(){for(var e,t,n=arguments.length,a=new Array(n),i=0;i<n;i++)a[i]=arguments[i];return o.rule?o.rule.apply(this,a):(e=vd(Qa(r.prototype),"rule",this)).call.apply(e,Ns(t=[this]).call(t,a))}}]),r}(a),"HOOK_NAME",e),r})),eo(rg,"createMenuHook",(function(e,t){var n=eg(t,["subMenuConfig","onClick","shortcutKeys","iconName"],{subMenuConfig:Array,onClick:"function",shortcutKeys:Array,iconName:"string"});return function(t){yn(a,t);var r=Qh(a);function a(t){var o;return un(this,a),(o=r.call(this,t)).setName(e,n.iconName),o.subMenuConfig=n.subMenuConfig||[],o}return ln(a,[{key:"onClick",value:function(){for(var e,t,r=arguments.length,o=new Array(r),i=0;i<r;i++)o[i]=arguments[i];return n.onClick?n.onClick.apply(this,o):(e=vd(Qa(a.prototype),"onClick",this)).call.apply(e,Ns(t=[this]).call(t,o))}},{key:"shortcutKeys",get:function(){return n.shortcutKeys?n.shortcutKeys:[]}}]),a}(Jh)})),eo(rg,"constants",tg),eo(rg,"VERSION","0.7.6");var ag=function(e,t){for(var n=-1,r=null==e?0:e.length;++n<r&&!1!==t(e[n],n,e););return e},og=Si(Object.keys,Object),ig=Object.prototype.hasOwnProperty;var sg=function(e){if(!Ti(e))return og(e);var t=[];for(var n in Object(e))ig.call(e,n)&&"constructor"!=n&&t.push(n);return t};var cg=function(e){return Di(e)?cs(e):sg(e)};var lg=function(e,t){return e&&rs(t,cg(t),e)};var ug=function(e,t){return e&&rs(t,ds(t),e)};var fg=function(e,t){for(var n=-1,r=null==e?0:e.length,a=0,o=[];++n<r;){var i=e[n];t(i,n,e)&&(o[a++]=i)}return o};var dg=function(){return[]},pg=Object.prototype.propertyIsEnumerable,hg=Object.getOwnPropertySymbols,gg=hg?function(e){return null==e?[]:(e=Object(e),fg(hg(e),(function(t){return pg.call(e,t)})))}:dg;var mg=function(e,t){return rs(e,gg(e),t)};var bg=function(e,t){for(var n=-1,r=t.length,a=e.length;++n<r;)e[a+n]=t[n];return e},vg=Object.getOwnPropertySymbols?function(e){for(var t=[];e;)bg(t,gg(e)),e=Ai(e);return t}:dg;var yg=function(e,t){return rs(e,vg(e),t)};var _g=function(e,t,n){var r=t(e);return Mi(e)?r:bg(r,n(e))};var kg=function(e){return _g(e,cg,gg)};var wg=function(e){return _g(e,ds,vg)},Eg=Wo(vo,"DataView"),xg=Wo(vo,"Promise"),Sg=Wo(vo,"Set"),Ag=Wo(vo,"WeakMap"),Cg=No(Eg),Tg=No(qo),Og=No(xg),$g=No(Sg),Rg=No(Ag),Pg=To;(Eg&&"[object DataView]"!=Pg(new Eg(new ArrayBuffer(1)))||qo&&"[object Map]"!=Pg(new qo)||xg&&"[object Promise]"!=Pg(xg.resolve())||Sg&&"[object Set]"!=Pg(new Sg)||Ag&&"[object WeakMap]"!=Pg(new Ag))&&(Pg=function(e){var t=To(e),n="[object Object]"==t?e.constructor:void 0,r=n?No(n):"";if(r)switch(r){case Cg:return"[object DataView]";case Tg:return"[object Map]";case Og:return"[object Promise]";case $g:return"[object Set]";case Rg:return"[object WeakMap]"}return t});var Ig=Pg,Lg=Object.prototype.hasOwnProperty;var Ng=function(e){var t=e.length,n=new e.constructor(t);return t&&"string"==typeof e[0]&&Lg.call(e,"index")&&(n.index=e.index,n.input=e.input),n};var Mg=function(e,t){var n=t?_i(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.byteLength)},jg=/\w*$/;var Dg=function(e){var t=new e.constructor(e.source,jg.exec(e));return t.lastIndex=e.lastIndex,t},Bg=yo?yo.prototype:void 0,Fg=Bg?Bg.valueOf:void 0;var Hg=function(e){return Fg?Object(Fg.call(e)):{}};var zg=function(e,t,n){var r=e.constructor;switch(t){case"[object ArrayBuffer]":return _i(e);case"[object Boolean]":case"[object Date]":return new r(+e);case"[object DataView]":return Mg(e,n);case"[object Float32Array]":case"[object Float64Array]":case"[object Int8Array]":case"[object Int16Array]":case"[object Int32Array]":case"[object Uint8Array]":case"[object Uint8ClampedArray]":case"[object Uint16Array]":case"[object Uint32Array]":return ki(e,n);case"[object Map]":return new r;case"[object Number]":case"[object String]":return new r(e);case"[object RegExp]":return Dg(e);case"[object Set]":return new r;case"[object Symbol]":return Hg(e)}};var Ug=function(e){return $i(e)&&"[object Map]"==Ig(e)},Wg=Vi&&Vi.isMap,qg=Wg?Xi(Wg):Ug;var Gg=function(e){return $i(e)&&"[object Set]"==Ig(e)},Kg=Vi&&Vi.isSet,Zg=Kg?Xi(Kg):Gg,Yg={};Yg["[object Arguments]"]=Yg["[object Array]"]=Yg["[object ArrayBuffer]"]=Yg["[object DataView]"]=Yg["[object Boolean]"]=Yg["[object Date]"]=Yg["[object Float32Array]"]=Yg["[object Float64Array]"]=Yg["[object Int8Array]"]=Yg["[object Int16Array]"]=Yg["[object Int32Array]"]=Yg["[object Map]"]=Yg["[object Number]"]=Yg["[object Object]"]=Yg["[object RegExp]"]=Yg["[object Set]"]=Yg["[object String]"]=Yg["[object Symbol]"]=Yg["[object Uint8Array]"]=Yg["[object Uint8ClampedArray]"]=Yg["[object Uint16Array]"]=Yg["[object Uint32Array]"]=!0,Yg["[object Error]"]=Yg["[object Function]"]=Yg["[object WeakMap]"]=!1;var Xg=function e(t,n,r,a,o,i){var s,c=1&n,l=2&n,u=4&n;if(r&&(s=o?r(t,a,o,i):r(t)),void 0!==s)return s;if(!Oo(t))return t;var f=Mi(t);if(f){if(s=Ng(t),!c)return wi(t,s)}else{var d=Ig(t),p="[object Function]"==d||"[object GeneratorFunction]"==d;if(Hi(t))return vi(t,c);if("[object Object]"==d||"[object Arguments]"==d||p&&!o){if(s=l||p?{}:Oi(t),!c)return l?yg(t,ug(s,t)):mg(t,lg(s,t))}else{if(!Yg[d])return o?t:{};s=zg(t,d,c)}}i||(i=new pi);var h=i.get(t);if(h)return h;i.set(t,s),Zg(t)?t.forEach((function(a){s.add(e(a,n,r,a,t,i))})):qg(t)&&t.forEach((function(a,o){s.set(o,e(a,n,r,o,t,i))}));var g=f?void 0:(u?l?wg:kg:l?ds:cg)(t);return ag(g||t,(function(a,o){g&&(a=t[o=a]),ns(s,o,e(a,n,r,o,t,i))})),s};var Vg=function(e){return Xg(e,5)},Jg={urlProcessor:function(e,t){return e},fileUpload:function(e,t){t("images/demo-dog.png")},afterChange:function(e,t){},afterInit:function(e,t){},beforeImageMounted:function(e,t){return{srcProp:e,src:t}},onClickPreview:function(e){},onCopyCode:function(e,t){return t},changeString2Pinyin:function(e){return e}},Qg=Vg({externals:{},engine:{global:{classicBr:!1,urlProcessor:Jg.urlProcessor,htmlWhiteList:""},syntax:{list:{listNested:!1,indentSpace:2},table:{enableChart:!1},inlineCode:{theme:"red"},codeBlock:{theme:"dark",wrap:!0,lineNumber:!0,customRenderer:{},indentedCodeBlock:!0},emoji:{useUnicode:!0},fontEmphasis:{allowWhitespace:!1},strikethrough:{needWhitespace:!1},mathBlock:{engine:"MathJax",src:"",plugins:!0},inlineMath:{engine:"MathJax",src:""},toc:{allowMultiToc:!1},header:{anchorStyle:"default"}}},editor:{theme:"default",height:"100%",defaultModel:"edit&preview",convertWhenPaste:!0,codemirror:{autofocus:!0}},toolbars:{theme:"dark",showToolbar:!0,toolbar:["bold","italic","strikethrough","|","color","header","ruby","|","list",{insert:["image","audio","video","link","hr","br","code","formula","toc","table","line-table","bar-table","pdf","word"]},"graph","settings"],sidebar:[],bubble:["bold","italic","underline","strikethrough","sub","sup","quote","|","size","color"],float:["h1","h2","h3","|","checklist","quote","quickTable","code"]},fileUpload:Jg.fileUpload,callback:{afterChange:Jg.afterChange,afterInit:Jg.afterInit,beforeImageMounted:Jg.beforeImageMounted,onClickPreview:Jg.onClickPreview,onCopyCode:Jg.onCopyCode,changeString2Pinyin:Jg.changeString2Pinyin},previewer:{dom:!1,className:"cherry-markdown",enablePreviewerBubble:!0,lazyLoadImg:{loadingImgPath:"",maxNumPerTime:2,noLoadImgNum:5,autoLoadImgNum:5,maxTryTimesPerSrc:2,beforeLoadOneImgCallback:function(e){return!0},failLoadOneImgCallback:function(e){},afterLoadOneImgCallback:function(e){},afterLoadAllImgCallback:function(){}}},isPreviewOnly:!1,autoScrollByCursor:!0,forceAppend:!0});function em(e,t){if(Ul(t))return t}function tm(e){var t=function(){if("undefined"==typeof Reflect||!an)return!1;if(an.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(an(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=Qa(e);if(t){var a=Qa(this).constructor;n=an(r,arguments,a)}else n=r.apply(this,arguments);return Ya(this,n)}}var nm=function(e){yn(n,e);var t=tm(n);function n(e){var r;un(this,n),r=t.call(this),n.initialized=!0;var a,o=Vg(n.config.defaults),i=As({},o,e,em);"function"==typeof i.engine.global.urlProcessor&&(i.engine.global.urlProcessor=(a=i.engine.global.urlProcessor,function(e,t){if(rd.isInnerLink(e)){var n=a(rd.get(e),t);return rd.replace(e,n)}return a(e,t)}));var s=new Kh(i,{options:i});return Ya(r,s)}return ln(n)}(rg);eo(nm,"initialized",!1),eo(nm,"config",{defaults:Qg}),e.MenuHookBase=Jh,e.SyntaxHookBase=uc,e.default=nm,Object.defineProperty(e,"__esModule",{value:!0})}));