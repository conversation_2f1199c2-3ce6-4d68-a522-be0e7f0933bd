$(document).on("click",".header li>a",function(){
  var url = $(this).attr("data-url");
  if(url){
    url = url+"?type="+params_type+"&id="+params_id
    var target = $(this).attr("_target");
    if(target=="_blank"){
      window.open(url, target);
    }else{
      window.location.href = url;
    }
  }
});

var filter_d = "展开";
$(".newlist-filter-d").on("click",function(){
  $(".newlist-filter-bg").toggleClass("active");
  $(".newlist-filter-bg-1").toggleClass("active");
  filter_d = filter_d == "展开"?'收起':"展开"
  $(".newlist-filter-d").text(filter_d)
});

var filter_d = "展开";
$(".newlist-filter-btn").on("click",function(){
  $(".newlist-filter-bg").toggleClass("active");
  $(".newlist-filter-bg-1").toggleClass("active");
  filter_d = filter_d == "展开"?'收起':"展开"
  $(".newlist-filter-btn").text(filter_d)
});

$(document).on("click",".jdb-r-d dl",function(){
  var action = $(this).attr("data-action");
  var id = $(this).attr("data-id");
  var hotspot = $(this).attr("data-hotspot");
  var hotspot_from = $(this).attr("data-hotspot_from");
  if(hotspot&&hotspot_from){
        var iframe = document.getElementById('mapiframe');
        var childWindow = iframe.contentWindow;
        if (childWindow && childWindow.childChange) {
            childWindow.childChange(hotspot,hotspot_from); // 传递参数
        } else {
            console.error("The function 'childChange' does not exist in the iframe.");
        }
  }else if(action=='building'){
        showBuilding(id)
  }
});

$(document).on("click",".navBack",function(){
  var action = $(this).attr("data-action");
  var id = $(this).attr("data-id");
  if(action){
    if(action=='index'){
        window.location.href='/admin/sc/index/index';
        return;
    }else if(action!='grid'){
        var iframe = document.getElementById('mapiframe');
        var childWindow = iframe.contentWindow;
        if (childWindow && childWindow.navBack) {
            childWindow.navBack(); // 传递参数
        } else {
            console.error("The function 'navBack' does not exist in the iframe.");
        }
    }else{
        $(".sqbox-bg").removeClass("active");
        $(".jdb-r").load('/admin/sc/index/'+action,{id:id,menu:1});
    }
  }
});

$(document).on("click",".jdb-r-d .change",function(){
  var url = $(this).attr("data-url");
  if(url){
     window.location.href=url
  }
});

$(document).on("click",".place",function(){
  var url = $(this).attr("data-url");
  window.open(url, "_blank");
});

$(".topNavBack").on("click",function(){
  if (window.close) {
    window.close();
  } else {
    // 如果无法关闭窗口，尝试使用opener返回上一级
    if (window.opener) {
      window.opener.focus();
      window.close();
    } else {
      // 如果没有opener，尝试使用history.back()
      window.history.back();
    }
  }
})

$(".on-qjqh").on("click",function(){
  var $div = $(".jdb-r");
  if ($div.hasClass('active')) {
    $div.removeClass('active');
  } else {
    $div.addClass('active');
    $(".jdbgk-rkfl").addClass("active");
    $(".jdbgk-fwsj").addClass("active");
    $(".jdbgk-jgdw").addClass("active");
    $(".jdbgk-pffb").addClass("active");
    $(".jdbgk-ggss").addClass("active");
    $(".jdbgk-tsrq").addClass("active");
    $(".jdbgk-m-bg").addClass("active");
  }
});

// 导航下拉菜单
$(".smenu").hover(function () {
  $(this).find("ul").slideToggle(150);
})

$(".on-rkfl").on("click",function(){
  var $div = $(".jdbgk-rkfl");
  if ($div.hasClass('active')) {
    $div.removeClass('active');
  } else {
    $div.addClass('active');
  }
})
$(".on-fwsj").on("click",function(){
  var $div = $(".jdbgk-fwsj");
  if ($div.hasClass('active')) {
    $div.removeClass('active');
  } else {
    $div.addClass('active');
  }
})
$(".on-jgdw").on("click",function(){
  var $div = $(".jdbgk-jgdw");
  if ($div.hasClass('active')) {
    $div.removeClass('active');
  } else {
    $div.addClass('active');
  }
})
$(".on-pffb").on("click",function(){
  var $div = $(".jdbgk-pffb");
  if ($div.hasClass('active')) {
    $div.removeClass('active');
  } else {
    $div.addClass('active');
  }
})
$(".on-ggss").on("click",function(){
  var $div = $(".jdbgk-ggss");
  if ($div.hasClass('active')) {
    $div.removeClass('active');
  } else {
    $div.addClass('active');
  }
})
$(".on-tsrq").on("click",function(){
  var $div = $(".jdbgk-tsrq");
  if ($div.hasClass('active')) {
    $div.removeClass('active');
  } else {
    $div.addClass('active');
  }
})
$(".on-jdbgk-m").on("click",function(){
  var $div = $(".jdbgk-m-bg");
  if ($div.hasClass('active')) {
    $div.removeClass('active');
  } else {
    $div.addClass('active');
  }
})

$(document).on("click",".sqbox-on",function(){
  var id = $(this).attr("data-id");
  var unit = $(this).attr("data-unit");
  var image = $(".unit-"+unit).attr("src");
  if(image===undefined){
    image = $(".sqbox img").attr("src");
  }
  $(".sqbox-bg").addClass("active");
  // 应用缩放变换：设置默认大小,解决720最后建筑图片缩放后，点击右侧房屋数量、人口数量后，房屋信息窗口被同时缩放问题
  $(".sqbox")[0].style.transform = `scale(1)`; 
  // $(".sqbox").load("/admin/sc/index/houseList",{id:id,unit:unit,image:image});
  $(".sqbox").load("/admin/sc/index/houseList2",{id:id,unit:unit,image:image});
})

$('.sqbox-bg,.sqbox-x').on('click', function(event) {
  if (event.target === this) {
    $(".sqbox-bg").removeClass("active");
    if($(".sqbox-bg").hasClass("building")){
        $(".navBack").click();
    }
  }
});
$(".details").on("click",function(){
  $(".jdbgk-box").load($(this).attr("detail-url"),function(){});
  $(".jdbgk-box").toggle();
});
$(".bmld-m-t-d ul").on("click",'li',function () {
  // tab栏切换
  $(this).addClass("active").siblings().removeClass("active");
  var index = $(this).index();
  // tab栏切换对应的内容
  $(".bmld-m-t-t li").eq(index).show().siblings().hide();
});
// $(".sqgl-m-t-d ul").on("click",'li',function () {
//   $(this).addClass("active").siblings().removeClass("active");
//   var index = $(this).index();
//   $(".sqgl-m-t-t li").eq(index).show().siblings().hide();
// });
$(".jdzh-m-box-t-t").on("click",'li',function () {
  // tab栏切换
  $(this).addClass("active").siblings().removeClass("active");
  var index = $(this).index();
  // tab栏切换对应的内容
  $(".jdzh-m-box-t-d").eq(index).show().siblings(".jdzh-m-box-t-d").hide();
});
$(".csxx-l-d>ul>li").on("click",function(){
  $(this).addClass("active").find("ul").slideDown(300);
  $(this).siblings("li").removeClass("active").find("ul").slideUp(300);
  $(this).find(".glyphicon").addClass("glyphicon-menu-up").removeClass("glyphicon-menu-down");
  $(this).siblings("li").find(".glyphicon").addClass("glyphicon-menu-down").removeClass("glyphicon-menu-up");
})


// 替换为你的 OpenWeatherMap API 密钥
const apiKey = '********************************';
const city = 'jiamusi'; // 你可以添加一个输入框让用户选择城市
const url = `https://api.openweathermap.org/data/2.5/weather?q=${city}&appid=${apiKey}&units=metric`;

fetch(url)
    .then(response => {
      if (!response.ok) {
        throw new Error('Network response was not ok');
      }
      return response.json();
    })
    .then(data => {
      const weatherDiv = document.getElementById('weather');
      const temperature = data.main.temp;
      const icon = data.weather[0].icon;
      const weatherCondition = data.weather[0].main;
      const description = data.weather[0].description;
      if(weatherDiv){
           weatherDiv.innerHTML = `
      <img src=" http://openweathermap.org/img/w/${icon}.png">温度: ${temperature} °C`;
      }
     
    })
    .catch(error => {
      console.error('There has been a problem with your fetch operation:', error);
    });
setInterval(function(){
  var now = new Date();

  // 格式化时间为 AM/PM 时：分
  var hours = now.getHours();
  var minutes = now.getMinutes();
  var ampm = hours >= 12 ? 'PM' : 'AM';
  hours = hours % 12;
  hours = hours ? hours : 12; // 如果小时为0，则设置为12
  minutes = minutes < 10 ? '0' + minutes : minutes;
  var strTime = hours + ':' + minutes + ' ' + ampm;
  const currentDate = new Date();

// 获取日期的信息
  const year = currentDate.getFullYear(); // 年份
  const month = currentDate.getMonth() + 1; // 月份，+1因为getMonth()返回的月份从0开始
  const day = currentDate.getDate(); // 日期

// 获取星期的信息
  const dayOfWeek = currentDate.getDay(); // 星期几，0代表星期日
  const daysOfWeek = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
  const dayName = daysOfWeek[dayOfWeek]; // 转换为中文星期名称
  $(".time").text(year+'-'+month+'-'+day+' '+dayName+' '+strTime);
},999)

setTimeout(function() {
    $(".on-qjqh").click();
}, 3000);

$("#community_id").on("change",function(){
  $("#search").click();
  $('#grid_group_id').empty();
  $('#grid_id').empty();
  if($("#building").is("*")){
    $('#building').empty();
  }
  if($("#house").is("*")) {
    $('#house').empty();
  }
  if($("#unit").is("*")) {
    $('#unit').empty();
  }

  $('select[name="building_id"]').empty();
  $('select[name="usage_category"]').val('');
  $('select[name="property_right"]').val('');
  
  var community_id = $(this).val(); // roomName 选中的省份名称
  var data = {community_id:community_id};
  if($("#all").is("*")){
    data.all = 1;
  }
  $.ajax({
    url: '/admin/Ajax/getGridGroupList',
    dataType: 'json',
    type: 'post',
    data:data,
    success: function (resData) {
      $('#grid_group_id').append(new Option("请选择", ""));// 下拉菜单里添加元素
      $.each(resData, function (index, value) {
        $('#grid_group_id').append(new Option(value.grid_name, value.id));// 下拉菜单里添加元素
      });
    }
  });
});

$("#grid_group_id").on("change",function(){
  $('#grid_id').empty();
  if($("#building").is("*")){
    $('#building').empty();
  }
  if($("#house").is("*")) {
    $('#house').empty();
  }
  if($("#unit").is("*")) {
    $('#unit').empty();
  }
  $("#search").click();
  var grid_id = $(this).val(); // roomName 选中的省份名称
  var data = {grid_id:grid_id};
  if($("#all").is("*")){
    data.all = 1;
  }
  $.ajax({
    url: '/admin/Ajax/getGridList',
    dataType: 'json',
    type: 'post',
    data:data,
    success: function (resData) {
      $('#grid_id').append(new Option("请选择", ""));// 下拉菜单里添加元素
      $.each(resData, function (index, value) {
        $('#grid_id').append(new Option(value.grid_name, value.id));// 下拉菜单里添加元素
      });
    }
  });
});
$("#grid_id").on("change",function(){
  $("#search").click();
  if($("#building_id").is("*")){
      $('#building_id').empty();
      if($("#house").is("*")) {
        $('#house').empty();
      }
      if($("#unit").is("*")) {
        $('#unit').empty();
      }
    var grid_id = $(this).val(); // roomName 选中的省份名称
    console.log(grid_id);
    $.ajax({
      url: '/admin/Ajax/getBuildingList',
      dataType: 'json',
      type: 'post',
      data:{grid_id:grid_id},
      success: function (resData) {
        var bhtml = "<option value=''>所属建筑</option>";
        $.each(resData, function (index, value) {
          bhtml += "<option unit='"+value.unit+"' value='"+value.id+"'>"+value.building_name+"</option>";
        });
        $('#building_id').html(bhtml);
      }
    });
  }
});

$("#building_id").on('change', function(data) {
  if($("#house").is("*")) {
    $('#house').empty();
  }
  var building_id = $("#building_id").val(); // roomName 选中的省份名称
  $.ajax({
    url: '{:url("/Ajax/getHouseList")}',
    dataType: 'json',
    type: 'post',
    data:{building_id:building_id},
    success: function (resData) {
      var hhtml = "<option value=''>请选择</option>";
      $.each(resData, function (index, value) {
        if(value.floor){
          value.code = value.floor+"楼"+value.code;
        }
        if(value.unit){
          value.code = value.unit+"单元"+value.code;
        }
        if(value.street){
          value.code = value.code+'('+value.street+')';
        }
        hhtml += "<option value='"+value.id+"' area='"+value.area+"'>"+value.code+"</option>";
      });
      $('#house').html(hhtml);
    }
  });
  if($("#unit").is("*")){
    $('#unit').empty();
    var buildingObj = $('#building_id option:selected');
    var unit = buildingObj.attr('unit');
    var uhtml = "<option value=''>请选择</option>";
    for(var i=1;i<=unit;i++){
      uhtml += "<option value='"+i+"'>"+i+"单元</option>";
    }
    $('#unit').html(uhtml)
  }
});

function getOpts(type,data,canvas,ctx){
  if(type == 1){
    return {
      type: "arcbar",
      context: ctx,
      width: canvas.width,
      height: canvas.height,
      series: [
        {
          name: data.name,
          color: "#16EA96",
          data: data.per
        }
      ],
      animation: true,
      timing: "easeOut",
      duration: 1000,
      rotate: false,
      rotateLock: false,
      background: "#FFFFFF",
      color: ["#1890FF","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],
      padding: undefined,
      fontSize: 13,
      fontColor: "#E2E2E2",
      dataLabel: true,
      dataPointShape: true,
      dataPointShapeType: "solid",
      touchMoveLimit: 60,
      enableScroll: false,
      enableMarkLine: false,
      title: {
        name: data.title,
        fontSize: 20,
        color: "#FFFFFF",
        offsetX: 0,
        offsetY: 0
      },
      subtitle: {
        name: "",
        fontSize: 15,
        color: "#E2E2E2",
        offsetX: 0,
        offsetY: 0
      },
      extra: {
        arcbar: {
          type: "circle",
          width: 13,
          backgroundColor: "#172744",
          startAngle: 1.5,
          endAngle: 0.25,
          gap: 2,
          direction: "cw",
          lineCap: "round",
          centerX: 0,
          centerY: 0,
          linearType: "custom",
          customColor: [
            "#41DFFB",
            "#41DFFB"
          ]
        }
      }
    }
  }else if(type == 2){
    return {
      type: "bar",
      context: ctx,
      width: canvas.width,
      height: canvas.height,
      categories: data.categories,
      series: data.series,
      animation: true,
      timing: "easeOut",
      duration: 1000,
      rotate: false,
      rotateLock: false,
      background: "#FFFFFF",
      color: ["#068870","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],
      padding: [15,50,0,5],
      fontSize: 13,
      fontColor: "#E2E2E2",
      dataLabel: true,
      dataPointShape: true,
      dataPointShapeType: "solid",
      touchMoveLimit: 60,
      enableScroll: false,
      enableMarkLine: false,
      legend: {
        show: false,
        position: "bottom",
        float: "center",
        padding: 5,
        margin: 5,
        backgroundColor: "rgba(0,0,20,0)",
        borderColor: "rgba(0,0,0,0)",
        borderWidth: 0,
        fontSize: 13,
        fontColor: "#E2E2E2",
        lineHeight: 11,
        hiddenColor: "#CECECE",
        itemGap: 10
      },
      xAxis: {
        boundaryGap: "justify",
        disableGrid: true,
        min: 0,
        axisLine: false,
        disabled: true,
        axisLineColor: "#CCCCCC",
        calibration: false,
        fontColor: "#E2E2E2",
        fontSize: 13,
        lineHeight: 20,
        marginTop: 0,
        rotateLabel: false,
        rotateAngle: 90,
        itemCount: 5,
        splitNumber: 5,
        gridColor: "#CCCCCC",
        gridType: "solid",
        dashLength: 4,
        gridEval: 1,
        scrollShow: false,
        scrollAlign: "left",
        scrollColor: "#A6A6A6",
        scrollBackgroundColor: "#EFEBEF",
        title: "",
        titleFontSize: 13,
        titleFontColor: "#E2E2E2",
        formatter: ""
      },
      yAxis: {
        disabled: false,
        disableGrid: true,
        splitNumber: 5,
        gridType: "solid",
        dashLength: 8,
        gridColor: "#CCCCCC",
        padding: 10,
        showTitle: false,
        data: [],
      },
      extra: {
        bar: {
          type: "group",
          width: 18,
          meterBorde: 1,
          meterFillColor: "#FFFFFF",
          activeBgColor: "#000000",
          activeBgOpacity: 0.08,
          seriesGap: 2,
          categoryGap: 3,
          barBorderCircle: false,
          linearType: "custom",
          linearOpacity: 1,
          customColor: [
            "#126d84",
            "#1B5995"
          ],
          colorStop: 0.5
        },
        tooltip: {
          showBox: false,
          showArrow: true,
          showCategory: false,
          borderWidth: 0,
          borderRadius: 0,
          borderColor: "#000000",
          borderOpacity: 0.7,
          bgColor: "#000000",
          bgOpacity: 0.7,
          gridType: "solid",
          dashLength: 4,
          gridColor: "#CCCCCC",
          boxPadding: 3,
          fontSize: 13,
          lineHeight: 20,
          fontColor: "#FFFFFF",
          legendShow: true,
          legendShape: "auto",
          splitLine: true,
          horizentalLine: false,
          xAxisLabel: false,
          yAxisLabel: false,
          labelBgColor: "#FFFFFF",
          labelBgOpacity: 0.7,
          labelFontColor: "#E2E2E2"
        },
        markLine: {
          type: "solid",
          dashLength: 4,
          data: []
        }
      }
    }
  }else if(type == 3){
    return {
      type: "column",
      context: ctx,
      width: canvas.width,
      height: canvas.height,
      categories: data.categories,
      series: data.series,
      animation: true,
      timing: "easeOut",
      duration: 1000,
      rotate: false,
      rotateLock: false,
      background: "#FFFFFF",
      color: ["#17c6b3","#91CB74","#FAC858","#EE6666","#73C0DE","#3CA272","#FC8452","#9A60B4","#ea7ccc"],
      padding: [15,15,0,5],
      fontSize: 13,
      fontColor: "#E2E2E2",
      dataLabel: true,
      dataPointShape: true,
      dataPointShapeType: "solid",
      touchMoveLimit: 60,
      enableScroll: false,
      enableMarkLine: false,
      legend: {
        show: false,
        position: "bottom",
        float: "center",
        padding: 5,
        margin: 5,
        backgroundColor: "rgba(0,0,0,0)",
        borderColor: "rgba(0,0,0,0)",
        borderWidth: 0,
        fontSize: 13,
        fontColor: "#E2E2E2",
        lineHeight: 11,
        hiddenColor: "#CECECE",
        itemGap: 10
      },
      xAxis: {
        disableGrid: true,
        disabled: false,
        axisLine: true,
        axisLineColor: "#CCCCCC",
        calibration: false,
        fontColor: "#E2E2E2",
        fontSize: 13,
        lineHeight: 20,
        marginTop: 0,
        rotateLabel: false,
        rotateAngle: 45,
        itemCount: 5,
        boundaryGap: "center",
        splitNumber: 5,
        gridColor: "#CCCCCC",
        gridType: "solid",
        dashLength: 4,
        gridEval: 1,
        scrollShow: false,
        scrollAlign: "left",
        scrollColor: "#A6A6A6",
        scrollBackgroundColor: "#EFEBEF",
        title: "",
        titleFontSize: 13,
        titleOffsetY: 0,
        titleOffsetX: 0,
        titleFontColor: "#E2E2E2",
        formatter: ""
      },
      yAxis: {
        data: [{min:0}],
        disabled: true,
        disableGrid: true,
        splitNumber: 5,
        gridType: "solid",
        dashLength: 8,
        gridColor: "#CCCCCC",
        padding: 10,
        showTitle: false
      },
      extra: {
        column: {
          type: "group",
          width: 15,
          activeBgColor: "#000000",
          activeBgOpacity: 0.08,
          seriesGap: 2,
          categoryGap: 3,
          barBorderCircle: false,
          linearType: "custom",
          linearOpacity: 1,
          customColor: [
            "#98F2B7"
          ],
          colorStop: 0,
          meterBorder: 1,
          meterFillColor: "#FFFFFF",
          labelPosition: "outside"
        },
        tooltip: {
          showBox: false,
          showArrow: true,
          showCategory: false,
          borderWidth: 0,
          borderRadius: 0,
          borderColor: "#000000",
          borderOpacity: 0.7,
          bgColor: "#000000",
          bgOpacity: 0.7,
          gridType: "solid",
          dashLength: 4,
          gridColor: "#CCCCCC",
          boxPadding: 3,
          fontSize: 13,
          lineHeight: 20,
          fontColor: "#FFFFFF",
          legendShow: true,
          legendShape: "auto",
          splitLine: true,
          horizentalLine: false,
          xAxisLabel: false,
          yAxisLabel: false,
          labelBgColor: "#FFFFFF",
          labelBgOpacity: 0.7,
          labelFontColor: "#E2E2E2"
        },
        markLine: {
          type: "solid",
          dashLength: 4,
          data: []
        }
      }
    }
  }else if(type == 4){
    if(data.color == undefined){
      data.color = ['#16e195', '#35e1e1']
    }
    return  {
      tooltip: {trigger: 'axis', axisPointer: {lineStyle: {color: '#E0E0E0'}}},
      backgroundColor: 'rgba(0, 0, 0, 0)',
      grid: {top: '24px', left: '60px', right: '12px', bottom: '24px'},
      xAxis: {
        type: 'category',
        data: data.categories,
        axisLabel:{
          interval:0
        }
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: data.name,
          data: data.data,
          type: 'bar',
          barWidth: '50%',
          barMaxWidth: '20px',
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: data.color[0] },
              { offset: 1, color: data.color[1] }
            ])
          },
          label: {
            show: true, // 显示标签
            position: 'top' // 在顶部显示标签
          }
        }
      ]
    };
  }else if(type == 5){
    return  {
      tooltip: {trigger: 'axis', axisPointer: {lineStyle: {color: '#E0E0E0'}}},
      legend: data.legend,
      backgroundColor: 'rgba(0, 0, 0, 0)',
      grid: {top: '12px', left: '60px', right: '48px', bottom: '24px'},
      yAxis: {
        type: 'category',
        data: data.categories,
        axisLabel:{
          interval:0
        }
      },
      xAxis: {
        type: 'value'
      },
      series: data.series
    };
  }else if(type == 6){
    return {
      backgroundColor: 'rgba(0, 0, 0, 0)',
      tooltip: {
        trigger: 'item'
      },
      series: [
        {
          backgroundColor: 'rgba(0, 0, 0, 0)',
          grid: {top: '24px', left: '60px', right: '12px', bottom: '24px'},
          name: '人数',
          type: 'pie',
          radius: '65%',
          center: ['50%', '50%'],
          data: data,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    };
  }else if(type == 7){
    return {
      color: ['#80FFA5', '#00DDFF', '#37A2FF', '#FF0087', '#FFBF00'],
      backgroundColor: 'rgba(0, 0, 0, 0)',
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985'
          }
        }
      },
      legend: {
        data: data.name
      },
      grid: {top: '24px', left: '60px', right: '12px', bottom: '45px'},
      xAxis: [
        {
          axisLabel: {
            formatter: function (params) {
              if(params==='研究生（硕士）') {
                return '研究生\r\n（硕士）';
              } else if (params==='研究生（博士）') {
                return '研究生\r\n（博士）';
              } else {
                return params;
              }
            },
            align: 'right',
            rotate: 0,
            interval: 0 // 确保所有标签都显示
          },
          type: 'category',
          boundaryGap: false,
          data: data.categories
        }
      ],
      yAxis: [
        {
          type: 'value'
        }
      ],
      series: [
        {
          name: data.name,
          type: 'line',
          stack: 'Total',
          smooth: true,
          lineStyle: {
            width: 0
          },
          showSymbol: false,
          areaStyle: {
            opacity: 0.8,
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgb(128, 255, 165)'
              },
              {
                offset: 1,
                color: 'rgb(1, 191, 236)'
              }
            ])
          },
          emphasis: {
            focus: 'series'
          },
          data: data.data,
        },
      ]
    };
  }else if(type == 8){
    return {
      color: [ '#37A2FF','#FF0087','#FFBF00', '#80FFA5',   '#00DDFF'],
      tooltip: {trigger: 'axis', axisPointer: {lineStyle: {color: '#E0E0E0'}}},
      backgroundColor: 'rgba(0, 0, 0, 0)',
      grid: {top: '24px', left: '60px', right: '12px', bottom: '24px'},
      xAxis: {
        type: 'category',
        data: data.categories,
        axisLabel:{
          interval:0
        }
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          data: data.data,
          type: 'line'
        }
      ]
    };
  }else if(type == 9){
    return {
      color: ['#80FFA5', '#00DDFF',  '#FF0087', '#FFBF00'],
      backgroundColor: 'rgba(0, 0, 0, 0)',
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985'
          }
        }
      },
      legend: {
        data: data.name
      },
      grid: {top: '24px', left: '60px', right: '12px', bottom: '24px'},
      xAxis: [
        {
          type: 'category',
          boundaryGap: false,
          data: data.categories
        }
      ],
      yAxis: [
        {
          type: 'value'
        }
      ],
      series: [
        {
          name: data.name[0],
          type: 'line',
          stack: 'Total',
          smooth: true,
          lineStyle: {
            width: 0
          },
          showSymbol: false,
          areaStyle: {
            opacity: 0.8,
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgb(128, 255, 165)'
              },
              {
                offset: 1,
                color: 'rgb(1, 191, 236)'
              }
            ])
          },
          emphasis: {
            focus: 'series'
          },
          data: data.data[0],
        },
        {
          name: data.name[1],
          type: 'line',
          stack: 'Total',
          smooth: true,
          lineStyle: {
            width: 0
          },
          showSymbol: false,
          areaStyle: {
            opacity: 0.8,
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgb(0, 221, 255)'
              },
              {
                offset: 1,
                color: 'rgb(77, 119, 255)'
              }
            ])
          },
          emphasis: {
            focus: 'series'
          },
          data: data.data[1],
        },
        {
          name: data.name[2],
          type: 'line',
          stack: 'Total',
          smooth: true,
          lineStyle: {
            width: 0
          },
          showSymbol: false,
          areaStyle: {
            opacity: 0.8,
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgb(255, 0, 135)'
              },
              {
                offset: 1,
                color: 'rgb(135, 0, 157)'
              }
            ])
          },
          emphasis: {
            focus: 'series'
          },
          data: data.data[2],
        },
        {
          name: data.name[3],
          type: 'line',
          stack: 'Total',
          smooth: true,
          lineStyle: {
            width: 0
          },
          showSymbol: false,
          areaStyle: {
            opacity: 0.8,
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgb(255, 191, 0)'
              },
              {
                offset: 1,
                color: 'rgb(224, 62, 76)'
              }
            ])
          },
          emphasis: {
            focus: 'series'
          },
          data: data.data[3],
        },
      ]
    };
  }else if(type == 10){
    data.color = ['#16e195', '#35e1e1','#f3e02b','#f6b51b']
    return  {
      tooltip: {trigger: 'axis', axisPointer: {lineStyle: {color: '#E0E0E0'}}},
      backgroundColor: 'rgba(0, 0, 0, 0)',
      grid: {top: '24px', left: '60px', right: '12px', bottom: '24px'},
      xAxis: {
        type: 'category',
        data: data.categories,
        axisLabel:{
          interval:0
        }
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: data.name[0],
          data: data.data[0],
          type: 'bar',
          barWidth: '50%',
          barMaxWidth: '20px',
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: data.color[0] },
              { offset: 1, color: data.color[1] }
            ])
          },
          label: {
            show: true, // 显示标签
            position: 'top' // 在顶部显示标签
          }
        },
        {
          name: data.name[1],
          data: data.data[1],
          type: 'bar',
          barWidth: '50%',
          barMaxWidth: '20px',
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: data.color[2] },
              { offset: 1, color: data.color[3] }
            ])
          },
          label: {
            show: true, // 显示标签
            position: 'top' // 在顶部显示标签
          }
        }
      ]
    };
  }
}

