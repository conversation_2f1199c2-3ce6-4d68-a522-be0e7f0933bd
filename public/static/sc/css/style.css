html,body,div,ul,ol,li,dl,dt,dd,p,h1,h2,h3,h4,h5{margin:0; padding:0;}
li{list-style:none;}
a, a:hover, a:focus{text-decoration:none;}
input:focus{outline:none;}
select{width: 100%;border: none;background-color: rgba(1,27,48,0.9); color: rgb(0, 252, 255);}
.form-select{font-size: 18px; margin: 5px 0 5px -20px;}
::-webkit-scrollbar {width: 12px; height:12px;}
::-webkit-scrollbar-track {background: #1c535d;}
::-webkit-scrollbar-thumb {width:12px; border-radius:12px; background:#42959b;}
.flex{display:flex;justify-content: space-evenly;}
.flex-col{justify-content:space-between;}
.flex-col-c{justify-content:center;}
.flex-col-r{justify-content:flex-start;}
.flex-col-r{justify-content:flex-end;}
.flex-row-c{align-items:center;}
.flex-row-d{align-items:flex-end;}
.flex-wrap{flex-flow:row wrap;}
.flex-column{flex-flow:column;}
.image{overflow:hidden;}
.image img{width:100%; height:100%; object-fit:cover;}

html,body{width:100%; margin:0 auto; height:100%; overflow:hidden; font-size:12px; font-family:"微软雅黑";}

body{background:#012430;width:calc(100% - 76px); }

.mapiframe{width:100%; height:calc(100% - 126px); margin-top:126px; z-index:0;}
.header{width:100%; height:126px; position:absolute; top:0; left:0; background:url(../images/header.png) center top no-repeat; z-index:99;}
.header-l, .header-r{width:573px; padding-top:12px;}
.header-1{width:100%; height:126px; position:absolute; top:0; left:0; background:url(../images/header-1.png) center top no-repeat; z-index:2;}
.weather{color:#fff; margin-left:27px;}
.weather-l{font-size:16px;}
.weather-l img{display:flex;}
.weather-r{margin-left:5px; font-size:16px;}
.header-m{width:440px; margin:0 167px;}
.header-m-t{font-size:40px; color:#00fcff; font-weight:bold; padding:10px 0 10px;}
.header-m-d{width:400px; height:48px; }
.header-m-d input{width:calc(100% - 54px); height:40px; background:none; border:none; padding:0 10px; font-size:24px; color:#fff; text-align:right;vertical-align: top;}
.header-m-d button{width:44px; height:36px; background:none; border:none;}
.header-r-d-l, .header-l-d-r{padding-top:45px;}
.header-r-d-r{color:#fff; font-size:16px; margin-right:42px;}
.header-r-d-r p{font-size:28px;}
.header-l-t>ul>li, .header-l-d-r>ul>li{margin-left:53px;}
.header-r-t>ul>li, .header-r-d-l>ul>li{margin-right:53px;}
.header-l-t a, .header-r-t a, .header-l-d-r a, .header-r-d-l a{font-size:18px; color:#fff;}
.header-l-t>ul>li.active a, .header-r-t>ul>li.active a, .header-l-d-r>ul>li.active a, .header-r-d-l>ul>li.active a{color:#3affff;}
.header-1 .header-m-t{font-size:40px; color:#fff; font-style:italic; font-weight:bold; padding:10px 0 10px;}
.header-1 .header-l-t>ul>li.active a, .header-1 .header-r-t>ul>li.active a, .header-1 .header-l-d-r>ul>li.active a, .header-1 .header-r-d-l>ul>li.active a{color:#fd7b7b;}
.header-l-t>ul>li, .header-l-d-r>ul>li, .header-r-t>ul>li, .header-r-d-l>ul>li{position:relative;}
.header-l-t>ul>li>ul, .header-l-d-r>ul>li>ul, .header-r-t>ul>li>ul, .header-r-d-l>ul>li>ul{width:105px; position:absolute; top:25px; left:0; z-index:999; overflow: hidden; display:none;}
.header-l-t>ul>li>ul>li, .header-l-d-r>ul>li>ul>li, .header-r-t>ul>li>ul>li, .header-r-d-l>ul>li>ul>li{padding:10px; background:#014e4f;}
.header-l-t>ul>li>ul>li a, .header-l-d-r>ul>li>ul>li a, .header-r-t>ul>li>ul>li a, .header-r-d-l>ul>li>ul>li a{font-size:16px; color:#fff;}
/* .header-l-t>ul>li:hover ul, .header-l-d-r>ul>li:hover ul, .header-r-t>ul>li:hover ul, .header-r-d-l>ul>li:hover ul{height:auto;} */


.main{width:100%; height:100%; padding-top:146px;}
.title{height:59px; position:relative; padding:0 216px 0 51px;}
.title::after, .title::before{height:59px; content:""; display:inline-block;  position:absolute; top:0; z-index:8;}
.title::after{width:51px; left:0; background:url(../images/title-l.png) no-repeat;}
.title::before{width:216px; right:0; background:url(../images/title-r.png) no-repeat;}
.title h3{width:100%; height:59px; padding-top:7px; color:#00fcff; font-size:18px; font-weight:600; background:url(../images/title-m.png) repeat-x; display:flex; align-items:center;}
.title-2{width:298px; height:56px; display:flex; align-items:center; justify-content:center; font-size:30px; color:#00fcff; margin:0 auto; background:url(../images/title-2.png) no-repeat; padding-bottom:6px;}
.wtitle-3{width:150px; height:40px; display:flex; align-items:center; justify-content:center; font-size:24px; color:#00fcff; margin:0 auto; background:url(../images/title-2.png); background-size: 100% 100%; no-repeat; padding-bottom:6px;}
.wbox{  background-color: #0d3743;
    background-image: url(../images/wbox-title.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    height: 400px;
    min-width: 200px;
    max-width: 100%;
    position: relative;}
.box{border:1px solid #00fcff; position:relative;}
.box-t::after, .box-t::before,.box-d::after, .box-d::before{content:""; width:10px; height:10px; display:block; position:absolute; z-index:8;}
.box-t::after, .box-t::before{top:-2px;}
.box-t::after{left:-2px; border-top:3px solid #00fcff; border-left:3px solid #00fcff}
.box-t::before{right:-2px; border-top:3px solid #00fcff; border-right:3px solid #00fcff}
.box-d::after, .box-d::before{bottom:-2px;}
.box-d::after{left:-2px; border-bottom:3px solid #00fcff; border-left:3px solid #00fcff}
.box-d::before{right:-2px; border-bottom:3px solid #00fcff; border-right:3px solid #00fcff}
.box-m{width:100%; height:100%; position:absolute; z-index:9;}
.bg-1{background:#020c10; border:1px solid #01222d;}
.bg-2{background:rgba(2,2,2,0.7);}


.jdb-r-icon{width:38px; height:120px; background:#012430; border-radius:5px 0 0 5px; display:flex; flex-flow:column; align-items:center; justify-content:center; font-size:18px; color:#00fcff; overflow: hidden; padding:0 10px; margin-bottom:7px; position:absolute; right:0; top:50%; margin-top:-60px; z-index:99; cursor:pointer;z-index:3;}

.jdb-l{position:absolute; left:0; top:116px; z-index:2;}
.jdb-l a{width:38px; height:120px; background:#012430; border-radius:0 5px 5px 0; display:flex; flex-flow:column; align-items:center; justify-content:center; font-size:18px; color:#00fcff; overflow: hidden; padding:0 10px; margin-bottom:7px;}
.on-pffb{position: absolute; top: 126px; right: 0;width:38px; height:120px; background:#012430; border-radius:0 5px 5px 0; display:flex; flex-flow:column; align-items:center; justify-content:center; font-size:18px; color:#00fcff; overflow: hidden; padding:0 10px; margin-bottom:7px;}

.jdb-r{width:440px; position:absolute; right:-440px; top:126px; background:#012430; transition:all 0.3s; z-index:3;}
.jdb-r.active{right:38px; z-index:30;}
.jdb-r-t{width:100%; height:53px; background:#174954;}
.jdb-r-t li{width:50%; height:32px; display:flex; justify-content:center; align-items:center; font-size:20px; color:#c5c3c3;}
.jdb-r-t li span{font-size:16px; margin-left:10px;}
.jdb-r-t li:nth-child(1){border-right:2px solid #fff;}
.jdb-r-d{height:840px; padding:0 30px; overflow-y:auto;}
.navBack:hover{cursor: pointer;}

.jdb-r-d ul li{border-bottom:1px dashed #809298; padding:25px 0 15px;}
.jdb-r-d ul li a{display:flex; align-items:flex-end; justify-content:space-between; color:#fff;}
.jdb-r-d ul li a:hover{color:#3affff;}
.jdb-r-d ul li dl dt{display:flex; align-items:center; font-size:20px; margin-bottom:18px;}
.jdb-r-d ul li dl dt i{width:30px; height:30px; border-radius:50%; border:2px solid #00fcff; font-size:20px; font-style:normal; display:flex; align-items:center; justify-content:center; margin-right:10px;}
.jdb-r-d ul li dl dd{padding-top:12px; font-size:18px;}
.jdb-r-d ul li .img{width:122px; height:123px; border-radius:5px; overflow:hidden;}
.jdb-r-d ul li .img img{width:100%; height:97px; object-fit:cover;}
.jdb-r-d ul li .img div{background:#174954; border-radius:5px; color:#fff; font-size: 18px; text-align: center;}

.sqbox-bg{width:100%; height:100%; position:absolute; top:0; left:0; background:rgba(0,0,0,0.7); z-index:20; display:none;}
.sqbox-bg.active{display:flex;}
.sqbox-img{width:calc(100% - 610px);}
.sqbox-img img{width:100%;}
.sqbox{background:#174954; padding:0; position:relative; left: -220px; top:50px;}
.sqbox-m{width:2px; height:calc(100% - 40px); position:absolute; left:50%; top:20px; background:#39ffff; margin-left:-14px;}
.sqbox ul{width:610px;  height:660px; overflow-y:auto;}
.sqbox ul::-webkit-scrollbar {width: 12px;}
.sqbox ul::-webkit-scrollbar-track {background: #1c535d;}
.sqbox ul::-webkit-scrollbar-thumb {width:12px; border-radius:12px; background:#42959b;}
.sqbox ul li{width:282px; overflow:hidden; display:flex; align-items:center; margin-bottom:20px;}
.sqbox ul li p{font-size:12px; color:#fff;}
.sqbox ul li>span{font-size:12px; color:#fff; margin:0 15px;}
.sqbox ul li dl{padding:7px 15px 20px; border:1px solid #39ffff; margin:0 20px;}
.sqbox ul li dl dt{color:#fff; font-size:20px; padding-bottom:5px; display:flex; align-items:flex-end;}
.sqbox ul li dl dt i{font-size:26px; color:#39ffff; margin:0 5px -4px 0;}
.sqbox ul li dl dd{font-size:12px; color:#fff; margin-top:5px;}
.sqbox-x{font-size:24px; color:#fff; margin-top:20px; cursor:pointer;left: -220px; top:50px;}

.sqbox-s{background:#174954; width:610px; position:relative;}
.sqbox-s ul{width:610px;  height:660px; overflow-y:auto;}
.sqbox-s ul::-webkit-scrollbar {width: 12px;}
.sqbox-s ul::-webkit-scrollbar-track {background: #1c535d;}
.sqbox-s ul::-webkit-scrollbar-thumb {width:12px; border-radius:12px; background:#42959b;}
.sqbox-s ul li{width:282px; overflow:hidden; display:flex; align-items:center; margin-bottom:20px;}
.sqbox-s ul li p{font-size:12px; color:#fff;}
.sqbox-s ul li>span{font-size:12px; color:#fff; margin:0 15px;}
.sqbox-s ul li dl{padding:7px 15px 20px; border:1px solid #39ffff; margin:0 20px;}
.sqbox-s ul li dl dt{color:#fff; font-size:20px; padding-bottom:5px; display:flex; align-items:flex-end;}
.sqbox-s ul li dl dt i{font-size:26px; color:#39ffff; margin:0 5px -4px 0;}
.sqbox-s ul li dl dd{font-size:12px; color:#fff; margin-top:5px;}

.bmld-l{width:510px; padding-right:7px;}
.bmld-l-t{padding:25px 50px;}
.bmld-l-t a{width:128px; display:block; color:#00fcff; font-size:30px; font-weight:bold; margin-bottom:25px;}
.bmld-l-t a .box{width:128px; height:60px;}
.bmld-l-t a .box-m{display:flex; align-items:center; justify-content:center;}
/* .bmld-l-d{height:714px;} */
.bmld-l-d ul{padding:45px 0 4px;}
.bmld-l-d li{display:flex; flex-flow:column; align-items:center; }
.bmld-l-d-img{width:368px; height:242px; border-radius:10px; overflow:hidden; position:relative;}
.bmld-l-d-img span{font-size:64px; color:#999; position:absolute; top:32%; left:39%;}
.bmld-l-d li a{font-size:16px; color:#fff;}
.bmld-l-d li a p{height:50px; display:flex; align-items:center; justify-content:center;}
.bmld-m{width:950px;}

.bmld-m-t{width:908px; margin:0 auto; background:#014e4f; padding:0 0 25px; border-radius:10px; margin-top:30px;}
.bmld-m-t h2{line-height:55px; font-size:24px; color:#fff; text-align:center;}
.bmld-m-t-t{width:880px; margin:0 auto 20px;}
.bmld-m-t-t li{width:880px; height:428px; display:none;}
.bmld-m-t-t li.active{display:block;}
.bmld-m-t-d{width:784px; margin:0 auto; position:relative;}
.bmld-m-t-d .bd{width:784px; margin:0 auto; overflow:hidden;}
.bmld-m-t-d .bd ul{overflow:hidden; zoom:1; }
.bmld-m-t-d .bd ul li{margin:0 9px; float:left; _display:inline; overflow:hidden; text-align:center; position:relative; border:2px solid #012430; border-radius:5px;}
.bmld-m-t-d .bd ul li .image{width:178px; height:98px; display:block; }
.bmld-m-t-d .bd ul li.active{border-color:#999;}
.bmld-m-t-d .bd ul li p{width:100%; height:24px; background:#014e4f; position:absolute; bottom:0; left:0; color:#fff; text-align:center; font-size:12px;}
.bmld-m-t-d .prev, .bmld-m-t-d .next{position:absolute; top:30px; font-size:32px; color:#012430;}
.bmld-m-t-d .prev{left:-40px;}
.bmld-m-t-d .next{right:-40px;}
.bmld-m-d{height:166px;}
.bmld-m-d dl{width:128px; height:76px; background:#014e4f; color:#fff; font-size:14px; display:flex; flex-flow:column; align-items:center; justify-content:center; margin:0 17px; border-radius:10px;}
.bmld-m-d dl dt{font-weight:500;}

.bmld-r{width:460px; padding-left:7px;}
.bmld-r-t{margin-bottom:3px;}
.bmld-r-t-img{width:369px; height:231px; overflow:hidden; margin:15px auto 0; border:1px solid #00fcff;}
.bmld-r-t p{height:50px; color:#fff; font-size:16px; display:flex; align-items:center; justify-content:center;;}
.bmld-r-d{padding:20px 0 5px;}
.bmld-r-d-img{width:383px; height:216px; margin:0 auto; border:1px solid #00fcff;}
.bmld-r-d dl{width:383px; height:251px; margin:0 auto; color:#fff; overflow-y:auto;}
.bmld-r-d dl dt{font-size:18px; text-align:center; padding:10px 0 15px;}
.bmld-r-d dl dd{font-size:14px; text-indent:2em;}

.csxx-main{width:calc(100% - 76px); position:absolute; top:180px; left:40px; z-index:1;zoom:0.8}
.csxx-l{width:420px;}
.csxx-l-t{line-height:52px; background:#225c66; padding-left:25px; font-size:24px; color:#fff;}
.csxx-l-d{height:808px; padding:35px 15px 0; background:#012a2b;}
.csxx-l-d>ul>li{width:363px; margin-bottom:10px;}
.csxx-l-d>ul>li>a{height:40px; border:2px solid #00fcff; color:#00fcff; font-size:24px; padding:0 15px; display:flex; align-items:center; justify-content:space-between;}
.csxx-l-d>ul>li.active>a{color:#fff; background:#225c66; border-color:#225c66;}
.csxx-l-d>ul>li>ul{display:none;}
.csxx-l-d>ul>li>ul{padding:20px 0 5px 5px;}
.csxx-l-d>ul>li>ul li{margin-bottom:6px; padding-left:20px; position:relative;}
.csxx-l-d>ul>li>ul li::after{content:""; width:10px; height:10px; border-radius:50%; background:#fff; position:absolute; left:0; top:7px; display:inline-block;}
.csxx-l-d>ul>li>ul li a{color:#fff; font-size:18px;}
.csxx-l-d>ul>li.active>ul{display:block;}
.csxx-l-d>ul>li>ul li.active a{color:#00fcff;}
.csxx-l-d>ul>li>ul li.active::after{background:#00fcff;}
.csxx-r{width:calc(100% - 420px); background:rgba(1,27,48,0.9); zoom:0.8}
.csxx-r-l{padding:125px 40px 0 20px;}
.csxx-r-l .box{width:330px; height:440px;}
.csxx-r-l .image{width:100%;}
.csxx-r-l .map{width:330px; height:165px; border:1px solid #00fcff; margin-top:38px;}
.csxx-r-r{}
.csxx-r-r h3{font-size:36px; color:#00fcff; padding:55px 0 15px;}
.csxx-r-r p{font-size:24px; margin-top:25px; color:#fff; padding-left:55px;}
.csxx-title{font-size:30px; color:#00fcff; display:flex; align-items:center; margin-top:40px;}
.csxx-title span{font-size:34px; margin-right:20px;}
.csxx-table{padding-left:55px; margin-top:25px; height:240px; overflow-y:auto;}
.csxx-table .table{width:959px; margin:0;}
.csxx-table .table th{height:60px; font-size:24px; color:#00fcff; background:#225c66; padding-left:20px;}
.csxx-table .table td{height:60px; font-size:24px; color:#fff; padding-left:20px;}

.csxx-r-sydw{width:100%;}
.csxx-r-sydw h3{font-size:36px; color:#00fcff; text-align:center; padding:30px 0 20px;}
.csxx-r-sydw .box{width:1374px; height:748px; margin:0 auto;}
.csxx-r-sydw .box .image{width:1372px; height:746px;}

.wggk-main{width:100%; padding-top:126px;}
.wggk-l{width:500px;}
.wggk-l-t{height:210px; margin-bottom:5px;}
.wggk-l-m{height:330px; margin-bottom:5px;}
.wggk-l-d{height:222px;}
.wggk-m{width:900px; margin:0 5px;}
.wggk-m-t, .wggk-m-t img{width:900px; height:600px;}
.wggk-m-d{width:900px; height:295px; background:#020c10;}
.wggk-r{width:510px;}
.wggk-r-t{height:250px; margin-bottom:5px;}
.wggk-r-m{height:262px; margin-bottom:5px;}
.wggk-r-d{height:250px;}

.rkxx-main{width:100%; height:calc(100% - 226px); position:absolute; left:0; top:176px; z-index:2; padding:0 30px; zoom:0.8;}
.rkxx-l{width:192px; height:100%; margin-right:5px;  background:#011b30; overflow-y:auto;}
.rkxx-l>ul>li{cursor:pointer; background:url(../images/title-1.png) top  no-repeat;}
.rkxx-l-t{height:64px; color:#00fcff; font-size:24px; padding:17px 0 0 24px;}
.rkxx-l-d{height:calc(100% - 64px); padding:35px 0 0 15px; display:none;}
.rkxx-l-d.active{display:block;}
.rkxx-l-d>ul>li{margin-bottom:10px; padding-left:20px; position:relative;}
.rkxx-l-d>ul>li>::after{content:""; width:10px; height:10px; border-radius:50%; background:#fff; position:absolute; left:0; top:7px; display:inline-block;}
.rkxx-l-d>ul>li>a{color:#fff; font-size:18px;}
.rkxx-l-d>ul>li.active>a{color:#00fcff;}
.rkxx-l-d>ul>li>ul{padding-right:10px; display:none;}
.rkxx-l-d>ul>li>ul>li{background:#215863; padding:10px; border-top:1px dashed #fff;}
.rkxx-l-d>ul>li>ul>li:nth-child(1){border:none;}
.rkxx-l-d>ul>li>ul>li>a{color:#fff; font-size:14px;}
.rkxx-r{width:calc(100% - 200px); height:100%; background:rgba(1,27,48,0.9);}
.rkxx-r .table th{text-align:center; height:74px; background:#215863; vertical-align:middle; font-size:24px; color:#00fcff; border:none;}
.rkxx-r .table th span{font-size:18px; margin-left:10px;}
.rkxx-r .table tr:nth-child(even){background:rgba(34,92,102,0.4);}
.rkxx-r .table td{height:74px; border:none; vertical-align:middle; font-size:24px; color:#fff; text-align:center;}

.rksj-main{width:calc(100% - 60px); height:calc(100% - 60px); position:absolute; left:30px; top:30px; z-index:2; background:rgba(1,27,48,0.9); border-radius:10px; overflow-y:auto; padding-top:20px; zoom:0.8}
.rksj-t{display:flex; align-items:center; margin:0 0 20px 45px; padding-top:20px;}
.rksj-t p{font-size:30px; color:#fff; margin:0 20px 0 5px;}
.rksj-d, .rksj-d-1{width:1700px; margin:0 0 20px 75px; background:rgba(1,27,48,0.8); box-shadow:0 10px 5px 5px rgba(0,0,0,0.5);}
.rksj-d ul, .rksj-d-1 ul{padding:0 5px;}
.rksj-d li{width:25%; padding:15px 0 15px 25px; display:flex; align-items:flex-start; border-top:1px solid #56636d;}
.rksj-t a {color:#00fcff;}
.rksj-d li p, .rksj-d-1 li p{font-size:24px; color:#00fcff;}
.rksj-d li span, .rksj-d-1 li span{font-size:24px; color:#fff;}
.rksj-d ul li:nth-child(1), .rksj-d ul li:nth-child(2), .rksj-d ul li:nth-child(3), .rksj-d ul li:nth-child(4){border:none;}
.rksj-d li .image{width:407px; height:320px; margin-top:20px;}
.rksj-d-1 li{width:350px; padding:15px 0 15px 25px; display:flex; align-items:flex-start; border-top:1px solid #56636d;}
.rksj-d-1 li.bn{border:none;}
.rksj-d-1 li.long{width:calc(100% - 350px);}
.rksj-table{width:1700px; margin:0 0 20px 75px; background:rgba(1,27,48,0.8); box-shadow:0 10px 5px 5px rgba(0,0,0,0.5);}
.rksj-table .table th{height:55px; text-align:center; vertical-align:middle; color:#5bc1c4; font-size:24px; background:#14424e; border:none;}
.rksj-table .table td{height:55px; text-align:center; vertical-align:middle; border-color:#56636d; color:#fff; font-size:24px;}

.jdbgk-l{width:500px; position:absolute; top:126px; left:38px;}
.jdbgk-l-t{height:190px; margin-bottom:10px;}
.jdbgk-l-t canvas{width: 24%;height:116px; margin: 15px 0;}
.jdbgk-l-t span {width: 24.7%; color: #E2E2E2; display: block; float: left; font-size: 16px; text-align: center;}
.jdbgk-l-m{height:232px; margin-bottom:10px;}
.jdbgk-l-d{height:285px;}
.jdbgk-l-d canvas{width: 100%;height:195px; margin: 15px 0;}
.jdbgk-m-bg{width:840px; position:absolute; bottom: 24px; left:50%; margin-left: -420px; transition:all 0.3s;}
.jdbgk-m-bg.active{bottom:-340px;}
.jdbgk-m{height:280px;}
.jdbgk-m canvas{width: 100%;height:250px; margin: 15px 0;}
.jdbgk-r{width:500px; position:absolute; top:126px; right:38px;}
.jdbgk-r-t{height:252px; margin-bottom:10px;}
.jdbgk-r-t canvas{width: 100%;height:226px;margin: 15px 0; }
.jdbgk-r-m{height:255px; margin-bottom:10px;}
.jdbgk-r-m canvas{width: 100%;height:224px;margin: 15px 0; }
.jdbgk-r-d{height:250px;}

.jdbgk-rkfl{width:500px; height:260px; position:absolute; top:0; left:0; transition:all 0.6s;}
.jdbgk-fwsj{width:500px; height:302px; position:absolute; top:274px; left:0; transition:all 0.6s;}
.jdbgk-jgdw{width:500px; height:344px; position:absolute; top:590px; left:0; transition:all 0.6s;}
.jdbgk-pffb{width:500px; height:311px; position:absolute; top:0; right:0; transition:all 0.6s;}
.jdbgk-ggss{width:500px; height:314px; position:absolute; top:325px; right:0; transition:all 0.6s;}
.jdbgk-tsrq{width:500px; height:314px; position:absolute; top:650px; right:0; transition:all 0.6s;}
.jdbgk-rkfl.active{left:-538px;}
.jdbgk-fwsj.active{left:-538px;}
.jdbgk-jgdw.active{left:-538px;}
.jdbgk-pffb.active{right:-538px;}
.jdbgk-ggss.active{right:-538px;}
.jdbgk-tsrq.active{right:-538px;}

.jdbgk-box{width:850px; height:700px; position:absolute; z-index:11; background:url(../images/box-2.png) no-repeat; background-size:cover; padding:30px 30px 0;}
.jdbgk-box-s{width:800px; height:650px; overflow-y:auto;}
.jdbgk-box dl{padding-top:30px;}
.jdbgk-box dl dt{background:url(../images/jdbgk-box-t.png) right bottom no-repeat; display:inline-block; padding:0 15px 5px 5px; border-bottom:1px solid #00ffff; font-size:20px; color:#fff; margin-bottom:10px;}
.jdbgk-box dl dd{font-size:14px; color:#fff; text-indent:2em;}

.sqgl-main{width:calc(100% - 80px); position:absolute; top:126px; left:40px; z-index:3;}
.sqgl-l{width:450px;}
.sqgl-l li{padding:5px; background:#014e4f; border-radius:10px; margin-bottom:15px;}
.sqgl-l li img, .sqgl-m-d li img{width:100%; height:175px;}
.sqgl-l li p, .sqgl-m-d li p{font-size:18px; color:#fff; text-align:center; margin-top:10px;}
.sqgl-m{width:910px;}
.sqgl-m-t{width:910px; margin:0 auto 15px; background:#014e4f; padding:2px 0 6px; border-radius:10px;}
.sqgl-m-t h2{line-height:50px; font-size:24px; color:#fff; text-align:center;}
.sqgl-m-t-t{width:900px; height:298px; margin:0 auto 5px;}
.sqgl-m-t-t li{width:900px; height:290px; display:block;}
.sqgl-m-t-t li.active{display:block;}
.sqgl-m-t-d{width:786px; margin:0 auto; position:relative;}
.sqgl-m-t-d .bd{width:786px; margin:0 auto; overflow:hidden;}
.sqgl-m-t-d .bd ul{overflow:hidden; zoom:1; }
.sqgl-m-t-d .bd ul li{margin:0 9px; float:left; _display:inline; overflow:hidden; text-align:center; position:relative; border:2px solid #012430; border-radius:5px;}
.sqgl-m-t-d .bd ul li .image{width:178px; height:98px; display:block; }
.sqgl-m-t-d .bd ul li.active{border-color:#999;}
.sqgl-m-t-d .bd ul li p{width:100%; height:24px; background:#014e4f; position:absolute; bottom:0; left:0; color:#fff; text-align:center; font-size:12px;}
.sqgl-m-t-d .prev, .sqgl-m-t-d .next{position:absolute; top:30px; font-size:32px; color:#012430;}
.sqgl-m-t-d .prev{left:-40px;}
.sqgl-m-t-d .next{right:-40px;}
.sqgl-m-d li{width:calc(50% - 5px); padding:5px; background:#014e4f; border-radius:10px; margin-bottom:15px;}

.djgz-main, .news-main{width:calc(100% - 160px); position:absolute; top:145px; left:80px;}
.djgz-t{}
.djgz-t li{width:406px; border:3px solid #f85d5d; border-radius:10px;}
.djgz-t li .image{width:400px; height:225px; border-radius:5px;}
.djgz-t li p{font-size:24px; color:#fff; padding-left:40px; line-height:50px; position:relative;}
.djgz-t li p::after{content:""; width:12px; height:12px; border-radius:50%; position:absolute; left:16px; top:19px; background:#c10303;}
.djgz-d{background:#7d0a0a; border:2px solid #8f0b0b; box-shadow:0 5px 5px 5px rgba(0,0,0,0.2); margin:20px 0 0;}
.djgz-d-l{width:890px;}
.djgz-d-l-t a{font-size:18px; color:#fff; display:inline-block; margin-top:20px;}
.djgz-d-l-t h3{width:545px; height:69px; background:url(../images/djgz-d-l-t.png) left no-repeat; padding:15px 0 0 55px; color:#fff; font-size:24px; font-weight:bold;}
.djgz-d-l-d{padding:50px 0;}
.djgz-d-l-d li{display:flex; align-items:center; justify-content:space-between; margin-bottom:20px; position:relative; padding-left:46px;}
.djgz-d-l-d li::after{content:""; width:10px; height:10px; border-radius:50%; position:absolute; left:24px; top:8px; background:#f85d5d;}
.djgz-d-l-d li a{font-size:18px; color:#fff;}
.djgz-d-l-d li span{font-size:18px; color:#fff;}
.djgz-d-r{width:755px; margin-right:55px; padding-top:20px;}
.djgz-d-r-d li{width:217px; height:182px; margin:40px 15px 0; border:1px solid #f85d5d; border-radius:10px; overflow:hidden;;}
.djgz-d-r-d li .image{width:215px; height:180px;}

/* 新党建工作 */
.djyd-menu{height:66px; background:#d74339; padding:0 80px;}
.djyd-menu .weather{color:#fff; margin-left:32px;}
.djyd-menu .weather-l{font-size:14px;}
.djyd-menu .weather-l img{display:flex; width:40px;}
.djyd-menu .weather-r{margin-left:5px; font-size:14px;}
.djyd-menu-m{}
.djyd-menu-m>li{padding:0 30px; position:relative;}
.djyd-menu-m li a{color:#fff; font-size:20px;}
.djyd-menu-m li.active a{color:#fcf300;}
.djyd-menu-m li ul{width:100%; display:none; position:absolute; left:0; background:#d74339;}
.djyd-menu-m li ul li{padding:10px 0; text-align:center;}
.djyd-menu-r{color:#fff; font-size:16px; margin-right:42px;}
.djyd-menu-r p{font-size:24px;}
.djyd-menu-d, .djyd-menu-d img{width:100%; display:block;}
.djyd-main{margin:0 130px;}
.djyd-m{margin:25px 0; position:relative;}
.djyd-m-l{width:658px; height:400px; overflow:hidden;}
.djyd-m-l img{width:100%; height:100%; object-fit:cover;}
.djyd-m-r{width:calc(100% - 714px);}
.djyd-m-r-nav{position:absolute; top:0; right:0; cursor:pointer;}
.djyd-m-r-nav span{width:144px; height:44px; background:#d74339; display:flex; align-items:center; justify-content:center; font-size:24px; color:#fff; border-radius:5px;}
.djyd-m-r-nav ul{background:#d74339; padding:0 10px; display:none; border-radius:5px;}
.djyd-m-r-nav ul li{height:44px; display:flex; align-items:center; justify-content:center; border-bottom:1px dashed #fff;}
.djyd-m-r-nav ul li a{font-size:22px; color:#fff;}
.djyd-m-r-t{font-size:36px; text-align:center; margin-bottom:45px;}
.djyd-m-r-d{font-size:26px; text-indent:2em;}

.djyd-d{overflow:hidden; position:relative; width:1660px; margin:0 auto;}
.djyd-d .hd{margin-top:30px;}
.djyd-d .hd ul{width:100%; display:flex; align-items:center; justify-content:center;}
.djyd-d .hd ul li{width:11px; height:11px; border-radius:11px; background:#aaa; color:#aaa; overflow:hidden; cursor:pointer; margin:0 8px;}
.djyd-d .hd ul li.on{background:#e37b74; color:#e37b74;}

.djyd-d .bd{overflow:hidden;}
.djyd-d .bd li{width:775px !important; float:left; margin-right:55px;}
.djyd-d-t{height:70px; display:flex; align-items:center; justify-content:space-between; border-bottom:2px solid #d74339;}
.djyd-d-t h3{width:205px; height:68px; font-size:36px; display:flex; align-items:center; justify-content:center; padding-top:5px; border-bottom:5px solid #d74339; font-weight:bold; color:#d74339;}
.djyd-d-t a{font-size:24px; color:#6d6d6d; margin-right:13px;}
.djyd-d-t span{font-size:24px; cursor:pointer;}
.djyd-d-d{padding:20px 0 0 20px;}
.djyd-d-d .item{width:755px; height:50px; padding-left:18px; display:flex; align-items:center; justify-content:space-between; position:relative;}
.djyd-d-d .item::before{content:""; width:8px; height:8px; border-radius:8px; background:#d74339; display:block; position:absolute; left:0; top:20px;}
.djyd-d-d .item a{font-size:18px; color:#333;}
.djyd-d-d .item span{color:#666;}


.zzgj-main-bg{width:100%; height:100%; background:rgba(0,0,0,0.5); position:absolute; top:0; left:0; z-index:1;}
.zzgj-main{width:100%; position:absolute; top:126px; left:0px; z-index:2;}


.news-t li{width:300px; border:4px solid #00fcff; border-radius:10px; margin:0 15px 20px; cursor:pointer;}
.news-t li .image{width:292px; height:164px; border-radius:4px;}
.news-t li p{font-size:22px; color:#00fcff; text-align:center; line-height:50px; position:relative;}

.news-search{}
.news-search ul{display:flex; flex-flow:row wrap;}
.news-search li{display:flex; align-items:center; margin-bottom:15px;}
.news-search li p{width:93px; text-align:right; font-size:16px; padding-right:10px; color:#fff;}
.news-search li select{width:190px; height:30px; background:#00a8a6; border-radius:5px; padding:0 10px; border:none; font-size:16px; color:#fff !important;}

.ywdt{width:1830px; overflow:hidden; position:relative; margin:0 auto}
.ywdt .hd{margin-top:30px;}
.ywdt .hd ul{width:100%; display:flex; align-items:center; justify-content:center;}
.ywdt .hd ul li{width:11px; height:11px; border-radius:11px; background:#aaa; color:#aaa; overflow:hidden; cursor:pointer; margin:0 8px;}
.ywdt .hd ul li.on{background:#00fcff; color:#00fcff;}
.ywdt .bd{overflow:hidden;}
.ywdt .bd li{width:860px !important; float:left; margin-right:55px;}
.ywdt-t .title{width:770px;}
.ywdt-t .title h3{font-size:24px;}
.ywdt-t span, .ywdt-t a{width:100px; margin-left:30px; color:#fff; font-size:18px; cursor:pointer;}
.ywdt-d{width:860px; padding:30px 0 10px; background:rgba(2,2,2,0.7);}
.ywdt-d .item{padding:0 50px 0 48px; position:relative; margin-bottom:23px; display:flex; justify-content:space-between; align-items:center;}
.ywdt-d .item::before{content:""; width:10px; height:10px; border-radius:50%; position:absolute; left:26px; top:8px; background:#00fcff;}
.ywdt-d .item a{width:520px; overflow:hidden; white-space: nowrap; text-overflow: ellipsis; font-size:18px; color:#00fcff;}
.ywdt-d .item span, .ywdt-d .item h3{font-size:18px; color:#f1f1f1;}


.ssks-main{width:100%; margin-top:126px;}
.ssks-l{width:500px;}
.ssks-l-t{padding:20px 0 30px;}
.ssks-l-t a{width:376px; display:block; color:#00fcff; font-size:24px; font-weight:bold; margin-bottom:15px;}
.ssks-l-t a .box{width:376px; height:60px;}
.ssks-l-t a .box-m{display:flex; align-items:center; justify-content:center;}
.ssks-l-d{padding:50px 0 30px;}
.ssks-l-d p{font-size:18px; color:#fff; margin-top:30px;}
.ssks-m{width:976px;}
.ssks-m img{width:100%;}
.ssks-r{width:444px;}
.ssks-r-t{height:300px; text-align:center; padding-top:20px;}
.ssks-r-d{padding:20px 0 10px;}
.ssks-r-d img{width:284px; height:215px; margin-bottom:20px;}

.sjcl-main{width:calc(100% - 88px); height:852px; position:absolute; top:171px; left:44px; background:#011b30;}
.sjcl-l{width:1210px;}
.sjcl-l .table th{text-align:center; height:67px; background:#215863; vertical-align:middle; font-size:24px; color:#00fcff; border:none;}
.sjcl-l .table th span{font-size:18px; margin-left:10px;}
.sjcl-l .table tr:nth-child(even){background:rgba(34,92,102,0.4);}
.sjcl-l .table td{height:67px; border:none; vertical-align:middle; font-size:24px; color:#fff; text-align:center;}
.sjcl-r{width:calc(100% - 1250px); height:812px; margin:20px; background:#0c2f43;}
.sjcl-r .box{width:100%; height:100%;}
.sjcl-r-title{width:343px; height:65px; background:url(../images/sjcl-r-title.png) no-repeat; margin:0 auto; font-size:24px; color:#fff; display:flex; align-items:center; justify-content:center; padding-bottom:5px;}
.sjcl-r .box-m img{margin:20px auto 0; display:block;}

.sphy-main{width:100%; position:absolute; top:150px; left:0; padding:0 50px;}
.sphy-main .box{width:100%; height:100%;}
.sphy-t{padding-bottom:5px;}
.sphy-t-l, .sphy-t-m, .sphy-t-r{height:400px; background:#0c3743;}
.sphy-t-l{width:596px;}
.sphy-t-l ul{margin:20px 0 30px;}
.sphy-t-l ul li{padding:0 50px; position:relative; margin-bottom:10px; display:flex; justify-content:space-between; align-items:center;}
.sphy-t-l ul li::before{content:""; width:10px; height:10px; border-radius:50%; position:absolute; left:26px; top:8px; background:#00fcff;}
.sphy-t-l ul li a{font-size:18px; color:#00fcff;}
.sphy-t-l ul li span{font-size:18px; color:#00fcff;}
.sphy-t-l-d{padding:0 50px;}
.sphy-t-l-d-l a{font-size:18px; color:#00fcff; border:1px solid #00fcff; border-radius:5px; padding:2px 5px; margin-right:20px;}
.sphy-t-l-d-l a span{margin-right:5px;}
.sphy-t-l-d-r a{font-size:14px; color:#00fcff;}
.sphy-t-m{width:539px;}
.sphy-t-m .box-m{padding:40px 0;}
.sphy-t-m .image{width:184px; height:184px; border-radius:50%;}
.sphy-t-m p{width:360px; height:60px; border:1px solid #00fcff; border-radius:10px; font-size:38px; color:#00fcff; display:flex; align-items:center; justify-content:center;}
.sphy-t-r{width:596px;}
.sphy-t-r-d{height:340px; padding:40px 0}
.sphy-t-r-d-l{margin-right:40px;}
.sphy-t-r-d-l dl{font-size:24px; color:#00fcff;}
.sphy-t-r-d-l dl dt{margin-bottom:30px;}
.sphy-t-r-d-r p{font-size:24px; color:#00fcff;}
.sphy-d li{width:274px; height:200px; margin:35px 0 0 35px;}
.sphy-d li:nth-child(1), .sphy-d li:nth-child(7){margin-left:0;}
.sphy-d .box-m{padding:20px 0;}
.sphy-d .image{width:90px; height:90px; border-radius:50%; background:#5c5d5d; font-size:24px; color:#fff; display:flex; align-items:center; justify-content:center;}
.sphy-d p{width:170px; height:30px; border:1px solid #00fcff; border-radius:5px; font-size:18px; color:#00fcff; display:flex; align-items:center; justify-content:center;}

.wjgl-main{width:100%; position:absolute; top:141px; left:0; padding:0 50px;}
.wjgl-item{width:595px; height:400px; margin-top:35px;}
.wjgl-item .box{width:100%; height:100%;}
.wjgl-item ul{margin:25px 0 20px;}
.wjgl-item ul li{padding:0 25px 0 50px; position:relative; margin-top:25px; display:flex; justify-content:space-between; align-items:center;}
.wjgl-item ul li::before{content:""; width:10px; height:10px; border-radius:50%; position:absolute; left:26px; top:8px; background:#00fcff;}
.wjgl-item ul li a{font-size:18px; color:#00fcff;}
.wjgl-item ul li span{font-size:18px; color:#00fcff;}
.wjgl-item ul li i{width:50px; text-align:center; display:inline-block; font-size:14px; color:#fff; background:#ffa800; border-radius:5px; padding:2px 0; font-style:normal;}
.wjgl-item ul li i.red{background:#ff0000;}
.wjgl-item-d{padding:10px 25px 0 0;}
.wjgl-item-d-l{width:500px; padding-left:90px;}
.wjgl-item-d-l a{width:85px; height:30px; font-size:18px; color:#fff; background:#026164; border-radius:30px; margin:0 20px; display:flex; align-items:center; justify-content:center;}
.wjgl-item-d-l a span{margin-right:5px;}
.wjgl-item-d-r a{font-size:14px; color:#00fcff;}
.wjgl-item-1-t{font-size:18px; color:#fff; display:flex; justify-content:center; align-items:flex-end; padding:5px 0 0;}
.wjgl-item-1-t span{font-size:30px;}
.wjgl-item-1-d{padding:0 40px 10px 20px; display:flex; flex-flow:wrap;}
.wjgl-item-1-d dl{width:112px; height:52px; border-radius:5px; background:#026164; color:#fff; font-size:18px; text-align:center; margin:20px 0 0px 20px;}
.wjgl-item-2{width:calc(100% - 20px); height:276px; padding:20px 20px 10px 10px; overflow-y:auto; margin-right:20px;}
.wjgl-item-2 .table{width:100%; margin:0;}
.wjgl-item-2 .table th{height:30px; font-size:18px; color:#00fcff; background:#225c66; padding:0 10px; border:none; font-weight:500; text-align:center;}
.wjgl-item-2 .table td{height:36px; font-size:18px; color:#fff; padding:0 10px; border:none; text-align:center; vertical-align:middle;}
.wjgl-item-2 .table tr:nth-child(even){background:#225c66;}

.lfgk-main{width:100%; position:absolute; top:126px; left:0;}
.lfgk-l{width:500px;}
.lfgk-l-t{height:265px; margin-bottom:10px;}
.lfgk-l-m{height:385px; margin-bottom:10px;}
.lfgk-l-d{height:285px;}
.lfgk-m{width:900px;}
.lfgk-m-t, .lfgk-m-t .image{width:900px; height:595px;}
.lfgk-m-d{padding-top:10px;}
.lfgk-m-d-l{width:380px; height:350px;}
.lfgk-m-d-r{width:510px; height:350px;}
.lfgk-r{width:500px;}
.lfgk-r-t{height:310px; margin-bottom:10px;}
.lfgk-r-m{height:320px; margin-bottom:10px;}
.lfgk-r-d{height:305px;}

.login-bg{width:100%; height:100%; position:absolute; top:0; left:0; background:rgba(1,36,48,0.9); z-index:10;}
.login{width:499px; background:rgba(20,28,108,0.1); border:1px solid #0e3c4c;}
.login::before, .login::after{content:""; width:499px; height:39px; display:block;}
.login::before{background:url(../images/login-t.png) top no-repeat;}
.login::after{background:url(../images/login-d.png) bottom no-repeat;}
.login h2{text-align:center; font-size:38px; color:#fff;}
.login ul{width:400px; margin:45px auto 50px;}
.login ul li{width:400px; height:55px; display:flex; align-items:center; border:1px solid #2a8898; border-radius:5px; margin-bottom:30px;}
.login ul li p{width:130px; font-size:20px; color:#fff; display:flex; align-items:center;}
.login ul li p span{margin:0 20px;}
.login ul li input{height:30px; color:#fff; font-size:18px; border:none; background:none;}
.login button{width:400px; height:55px; margin:0 auto; display:block; border:none; border-radius:5px; font-size:20px; color:#fff; background: linear-gradient(to bottom, #17f1fb, #017b7c);}


.jdzh-main{width:100%; height:calc(100% - 126px); position:absolute; top:126px; left:0;}
.jdzh-l{width:500px;}
.jdzh-l-t{height:310px;}
.jdzh-l-t a{width:376px; display:block; color:#00fcff; font-size:24px; font-weight:bold; margin-bottom:15px;}
.jdzh-l-t a .box{width:376px; height:74px;}
.jdzh-l-t a .box-m{display:flex; align-items:center; justify-content:center;}
.jdzh-l-d{height:calc(100% - 310px);}
.jdzh-l-d ul{padding:10px 50px 0;}
.jdzh-l-d ul li{font-size:18px; color:#fff; margin-top:15px;}
.jdzh-m{width:976px; height:100%; position:relative;}
.jdzh-m iframe{width:100%; height:100%;}
.jdzh-m-box{width:220px; position:absolute; bottom:10px; right:0; z-index:12;}
.jdzh-m-box-t{background:#01232f; border-radius:5px; overflow:hidden;}
.jdzh-m-box-t-t{display:flex; align-items:center;}
.jdzh-m-box-t-t li{width:50%; height:38px; font-size:14px; color:#016e71; display:flex; align-items:center; justify-content:center; cursor:pointer;}
.jdzh-m-box-t-t li.active{background:#016e71; color:#fff;}
.jdzh-m-box-t-d{display:none; padding-top:10px}
.jdzh-m-box-t-d li{display:flex; align-items:center; color:#fff; font-size:14px; margin-bottom:10px; padding-right:10px;}
.jdzh-m-box-t-d li span{margin:0 5px; color:#00a8a6; font-size:18px;}
.jdzh-m-box-d{margin-top:10px;}
.jdzh-m-box-d a{width:100%; height:32px; font-size:18px; color:#00fcff; display:flex; align-items:center; justify-content:center; border-radius:32px; background:#01232f;}
.jdzh-r{width:444px;}
.jdzh-r-t{height:350px; padding:0 30px;}
.jdzh-r-t .image{width:384px; height:216px; border:1px solid #00fcff; margin:30px 0 0;}
.jdzh-r-d{height:calc(100% - 350px); padding:0 30px;}
.jdzh-r-d .image{width:384px; height:216px; border:1px solid #00fcff; margin:40px 0 60px;}
.jdzh-r-d ul{}
.jdzh-r-d ul li{width:82px; height:36px; display:flex; align-items:center; justify-content:center; border:1px solid #00fcff; margin:0 6px 20px;}
.jdzh-r-d ul li a{font-size:18px; color:#00fcff;}

.zdrq-main{width:100%; height:calc(100% - 145px); position:absolute; top:145px; left:0;}
.zdrq-l{width:500px;}
.zdrq-l-t{height:215px;}
.zdrq-l-t a{width:376px; display:block; color:#00fcff; font-size:24px; font-weight:bold; margin-bottom:7px;}
.zdrq-l-t a .box{width:344px; height:60px;}
.zdrq-l-t a .box-m{display:flex; align-items:center; justify-content:center;}
.zdrq-l-d{height:calc(100% - 215px);}
.zdrq-l-d .image{width:390px; height:192px; margin:40px auto; border:2px solid #00fcff;}
.zdrq-l-d ul{padding:0 53px;}
.zdrq-l-d ul li{font-size:18px; color:#fff; margin-bottom:10px;}
.zdrq-m{width:950px;}
.zdrq-m .image{width:909px; height:644px; margin:15px auto 30px; border:2px solid #00fcff;}
.zdrq-m li{width:50%; margin-bottom:20px; padding-left:20px; display:flex; align-items:center; font-size:18px; color:#fff;}
.zdrq-m li span{color:#00fcff; font-size:24px; margin-right:10px;}
.zdrq-r{width:444px;}
.zdrq-r-t{height:353px; margin-bottom:5px;}
.zdrq-r-t p{width:128px; font-size:18px; color:#00fcff; display:flex; align-items:flex-start; justify-content:center; margin-top:20px;}
.zdrq-r-t p span{margin:3px 0 0 5px; font-size:14px;}
.zdrq-r-t .image{width:286px; height:230px; border:2px solid #00fcff; margin:20px 0 0;}
.zdrq-r-d{height:calc(100% - 358px);}
.zdrq-r-d .image{width:222px; height:162px; border:2px solid #00fcff; margin:24px auto;}
.zdrq-r-d dl{color:#fff; padding:20px 26px 0;}
.zdrq-r-d dl dt{font-size:18px; text-align:center;}
.zdrq-r-d dl dd{font-size:14px; text-indent:2em;}
.zdrq-r-d-d{width:444px; height:calc(100% - 59px); overflow:hidden; position:relative;}
.zdrq-r-d-d .hd{width:100%; height:15px; position:absolute; left:0; bottom:15px;}
.zdrq-r-d-d .hd ul{display:flex; align-items:center; justify-content:center;}
.zdrq-r-d-d .hd ul li{width:10px; height:10px; margin:0 3px; background:#183043; cursor:pointer; border-radius:50%;}
.zdrq-r-d-d .hd ul li.on{background:#00fbfe;}


.content-main{width:90%; }
.content-main-t{padding:30px 100px 10px; font-size:32px; color:#fff; text-align:center;}
.content-main-t p{font-size:18px; margin-top:20px;}
.content-main-img{display:flex; justify-content:center; align-items:center; padding:20px 0;}
.content-main-img-item{margin:0 auto;}
.content-main-m{padding:10px 30px 30px; color:#fff;font-size:18px;}
.content-main-d{padding:0 30px 30px;}
.content-main-d li{font-size:16px; color:#fff; margin-bottom:10px;}
.content-main-d li a{color:#00fcff;}
.content-x{width:30px; height:30px; line-height:24px; border-radius:30px; background:#000; color:#fff; font-size:20px; border:1px solid #fff; text-align:center;  margin-top:10px; cursor:pointer;}

.content-main.red .content-main-d li a{color:#fd9898;}

.list-main{width:100%;}
.list{}
.list ul{padding:50px 100px;}
.list ul li{display:flex; align-items:center; justify-content:space-between; margin-bottom:20px; position:relative; padding-left:46px;}
.list ul li::after{content:""; width:10px; height:10px; border-radius:50%; position:absolute; left:24px; top:8px; background:#00fbfe;}
.list ul li a{font-size:18px; color:#fff;}
.list ul li span{font-size:18px; color:#fff;}
.list ul li a:hover{color:#00fbfe;}
.list.red  ul li::after{background:#f87a7a;}
.list.red ul li a:hover{color:#f87a7a;}


.m-page{width:100%; display:flex; align-items:center; justify-content:center; gap:0 10px; padding-top:20px;}
.m-page a, .m-page span{font-size:14px; color:#9b9b9b; padding:2px 5px;}
.m-page a.active{color:#00fcff;}
.m-page .prev, .m-page .next{border:1px solid #9b9b9b;}
.m-page.red a.active{color:#f87a7a;}

.newlist-main{width:100%; height:calc(100% - 152px); position:absolute; left:0; top:140px; z-index:2; padding:0 30px;}
.newlist-filter-bg{width:calc(100% - 60px); height:60px; background:#011b30; position:absolute; top:0; left:30px; z-index:20; transition:height 0.5s ease; border:1px solid #016e71}
.newlist-filter{height:100%; height:100%; overflow:hidden; color:#fff; padding:15px 20px 0;}
.newlist-filter h3{color:#fff; font-size:16px; margin-bottom:15px;}
.newlist-filter ul, .newlist-filter-d ul{display:flex; flex-flow:row wrap;}
.newlist-filter li{display:flex; align-items:center; margin-bottom:15px;}
.newlist-filter li p{width:93px; text-align:right; font-size:16px; padding-right:10px;}
.newlist-filter li input[type=text],.newlist-filter li input[type=number], .newlist-filter li select{width:190px; height:30px; background:#00a8a6; border-radius:5px; padding:0 10px; border:none; font-size:16px; color:#fff !important;}
.newlist-filter li input[type=number]{width:100px;}
.newlist-filter li label{font-size:16px; margin:0 20px 0 5px; font-weight:500; color:#fff;}
.newlist-filter li input[type=checkbox]{width:18px; height:18px; margin-top:2px; position:relative; margin:0;}
.newlist-filter li input[type=checkbox]::after {position:absolute; top:0; color:#000; width:18px; height:18px; display:inline-block; visibility:visible; padding-left:0px; text-align:center; content:' '; border-radius:3px;}
.newlist-filter li input[type=checkbox]:checked::after{content:"✓"; color:#fff; font-size:14px; line-height:15px; background-color:#42BEAD;}
.newlist-filter li span{padding:0 10px;}
.newlist-filter-btn{width:80px; height:30px; border-radius:0 0 5px 5px; color:#fff; font-size:16px; display:flex; align-items:center; justify-content:center; cursor:pointer; background:#00a8a6; border-radius:5px; padding:0 10px; margin:0 20px; border:none;}
.newlist-filter-bg.active{height:auto;  transition:height 0.5s ease; opacity:0.9}
.newlist{width:100%; height:calc(100% - 60px); margin-top:60px; background:#011b30}
.newlist .table th{text-align:center; height:60px; background:#215863; vertical-align:middle; font-size:20px; color:#00fcff; border:none;}
.newlist .table th span{font-size:18px; margin-left:10px;}
.newlist .table tr:nth-child(even){background:rgba(34,92,102,0.4);}
.newlist .table td{height:60px; border:none; vertical-align:middle; font-size:20px; color:#fff; text-align:center;}

.person-main{width:100%; height:calc(100% - 126px); position:absolute; left:0; top:126px; z-index:2; overflow-y:scroll;}
.newlist-main-1{width:100%; height:900px; padding:0 30px; margin-bottom:20px;}
.newlist-filter-bg-1{width:100%; height:60px; background:#011b30; z-index:20; transition:height 0.5s ease; border:1px solid #016e71;}
.newlist-filter-bg-1.active{height:auto;  transition:height 0.5s ease; opacity:0.9}
.person{width:100%; height:955px; padding:0 10px;}
.person ul{height:100%;}
.person li{width:calc(33.33% - 20px); height:calc(33.33% - 20px); margin:10px; background: rgba(2, 2, 2, 0.7)}
.person-item{height:calc(100% - 59px);}
.person-item img{width:100%; height:100%;}


.pt20{padding-top:20px;}
.pb20{padding-bottom:20px;}
.mt20{margin-top:20px;}
.mb10{margin-bottom:10px;}
.mb20{margin-bottom:20px;}

.layui-table td, .layui-table th, .layui-table-col-set, .layui-table-fixed-r, .layui-table-grid-down, .layui-table-header, .layui-table-mend, .layui-table-page, .layui-table-tips-main, .layui-table-tool, .layui-table-total, .layui-table-view, .layui-table[lay-skin=line], .layui-table[lay-skin=row]{border: none;}
.layui-table{background:rgba(34,92,102,0.4);}
.layui-table-hover{background:rgba(34,92,102,0);}
.layui-table th{text-align:center; height:54px; background:#215863; vertical-align:middle; font-size:20px; color:#00fcff; border:none;}
.layui-table th span{font-size:20px; margin-left:10px;}
.layui-table tr:nth-child(even){background:rgba(34,92,102,0.4);}
.layui-table td{height:54px; border:none; vertical-align:middle; font-size:20px; color:#fff; text-align:center;}
.layui-laypage-count{color:#16baaa;}
.layui-laypage a[data-page],.layui-laypage span{color:#16baaa;}

.layui-layer-setwin .layui-layer-close2{    width: 36px; height: 36px; font-size: 24px; line-height: 25px; font-weight: bolder;}
