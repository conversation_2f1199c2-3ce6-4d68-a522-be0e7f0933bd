/*jslint browser: true */ /*global jQuery: true */



/**

 * jQuery Cookie plugin

 *

 * Copyright (c) 2010 <PERSON> (stilbuero.de)

 * Dual licensed under the MIT and GPL licenses:

 * http://www.opensource.org/licenses/mit-license.php

 * http://www.gnu.org/licenses/gpl.html

 *

 */



// TODO JsDoc



/**

 * Create a cookie with the given key and value and other optional parameters.

 *

 * @example $.cookie('the_cookie', 'the_value');

 * @desc Set the value of a cookie.

 * @example $.cookie('the_cookie', 'the_value', { expires: 7, path: '/', domain: 'jquery.com', secure: true });

 * @desc Create a cookie with all available options.

 * @example $.cookie('the_cookie', 'the_value');

 * @desc Create a session cookie.

 * @example $.cookie('the_cookie', null);

 * @desc Delete a cookie by passing null as value. Keep in mind that you have to use the same path and domain

 *       used when the cookie was set.

 *

 * @param String key The key of the cookie.

 * @param String value The value of the cookie.

 * @param Object options An object literal containing key/value pairs to provide optional cookie attributes.

 * @option Number|Date expires Either an integer specifying the expiration date from now on in days or a Date object.

 *                             If a negative value is specified (e.g. a date in the past), the cookie will be deleted.

 *                             If set to null or omitted, the cookie will be a session cookie and will not be retained

 *                             when the the browser exits.

 * @option String path The value of the path atribute of the cookie (default: path of page that created the cookie).

 * @option String domain The value of the domain attribute of the cookie (default: domain of page that created the cookie).

 * @option Boolean secure If true, the secure attribute of the cookie will be set and the cookie transmission will

 *                        require a secure protocol (like HTTPS).

 * @type undefined

 *

 * @name $.cookie

 * @cat Plugins/Cookie

 * <AUTHOR> Hartl/<EMAIL>

 */



/**

 * Get the value of a cookie with the given key.

 *

 * @example $.cookie('the_cookie');

 * @desc Get the value of a cookie.

 *

 * @param String key The key of the cookie.

 * @return The value of the cookie.

 * @type String

 *

 * @name $.cookie

 * @cat Plugins/Cookie

 * <AUTHOR> Hartl/<EMAIL>

 */



	jQuery.cookie = function (key, value, options) {



		// key and value given, set cookie...

		if (arguments.length > 1 && (value === null || typeof value !== "object")) {

			options = jQuery.extend({}, options);



			if (value === null) {

				options.expires = -1;

			}



			if (typeof options.expires === 'number') {

				var days = options.expires, t = options.expires = new Date();

				t.setDate(t.getDate() + days);

			}



			return (document.cookie = [

				encodeURIComponent(key), '=',

				options.raw ? String(value) : encodeURIComponent(String(value)),

				options.expires ? '; expires=' + options.expires.toUTCString() : '', // use expires attribute, max-age is not supported by IE

				options.path ? '; path=' + options.path : '; path=/',

				options.domain ? '; domain=' + options.domain : '',

				options.secure ? '; secure' : ''

			].join(''));

		}



		// key and possibly options given, get cookie...

		options = value || {};

		var result, decode = options.raw ? function (s) { return s; } : decodeURIComponent;

		return (result = new RegExp('(?:^|; )' + encodeURIComponent(key) + '=([^;]*)').exec(document.cookie)) ? decode(result[1]) : null;

	};