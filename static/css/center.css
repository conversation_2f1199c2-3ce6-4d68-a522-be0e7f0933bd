/*!
 * center.css 用户中心样式表 - 2022-10-10
 * https://github.com/meystack/swiftadmin
 * Copyright (c) meystack
 * Licensed Apache2.0
 */
body {
	background-color: #eee;
}
.fl {
	float: left;
}

.fr {
	float: right;
}

.clear {
	clear: both;
}

i {
	cursor: pointer;
}

.pt0 {
	padding-top: 0 !important;
}

.pb0 {
	padding-bottom: 0 !important;
}

.pb200 {
	padding-bottom: 200px !important;
}

.pr77 {
	padding-right: 77px;
}

.pl0 {
	padding-left: 0 !important;
}

.pr0 {
	padding-right: 0 !important;
}

.mt0 {
	margin-top: 0 !important;
}

.mt10 {
	margin-top: 10px !important;
}

b.msg {
	font-weight: bold;
	color: red;
	margin: 0 3px;
}

.layui-nav {
	color: #000;
}

.layui-btn {
	height: 33px!important;
	line-height: 33px!important;
}

.layui-form-label {
	padding: 6px 15px;
}

.layui-form-radio {
	margin-top: 3px;
}

.layui-form-radio>i {
	font-size: 20px;
}

.layui-anim-scaleSpring {
	-webkit-animation-name: none;
	animation-name: none;
}

.layui-form-radio:hover *, .layui-form-radioed, .layui-form-radioed>i {
	color: #3956de;
}

.layui-input, .layui-select, .layui-textarea {
	height: 33px;
	line-height: 33px;
}

.layui-input:hover, .layui-textarea:hover {
	border-color: #eee!important;
}

.layui-form-item .layui-input:hover, .layui-select:hover, .layui-form-select:hover, .layui-textarea:hover, .layui-form-checked i:hover, .layui-input:hover {
	border-color: #3464FF!important;
}

.layui-nav .layui-this:after, .layui-nav-bar {
	background: 0!important;
}

.layui-header {
	background-color: #fff!important;
	box-shadow: 0 1px 2px 0 rgb(0 0 0 / 15%);
}

.layui-header .layui-layout-left .layui-nav-item {
	margin: 0 10px;
}

.layui-header .layui-nav-item a {
	color: #0C1021!important;
}

.layui-header .layui-logo {
	box-shadow: 0 0px 2px 0 rgb(0 0 0 / 15%);
}

.logo-text, .layui-logo {
	color: #959890!important;
}

.logo-text {
	font-size: 18px;
}

.layui-btn-primary {
	background: 0 0!important;
}

.layui-btn-primary:hover {
	border-color: #3956de;
}

.layui-laypage .layui-laypage-curr .layui-laypage-em {
	background-color: #3956de;
}

.layui-form-fixed {
	padding-right: 35px;
	padding-top: 30px;
}

.layui-center {
	text-align: center;
}

.layui-nav {
	background-color: unset;
}
.layui-bg-white {
	background-color: #fff!important;
}

.layui-nav-tree .layui-nav-item a {
	padding-left: 30px!important;
}

.layui-bg-white .layui-nav-tree .layui-nav-child a,.layui-bg-white .layui-nav-tree a {
	color: #000!important;
}

.layui-bg-white .layui-nav .layui-nav-item a {
	color: #000!important;
}

.layui-bg-white .layui-nav-tree dl.layui-nav-child {
	background-color: #fafafa!important;
}

.layui-bg-white .layui-nav-tree li dl.layui-nav-child dd {
	padding-left: 15px;
}

.layui-bg-white .layui-nav-tree .layui-nav-item .layui-icon {
	position: absolute;
	left: 22px;
	color: #000;
	top: 2px;
	z-index: 99999;
}

.layui-bg-white .layui-nav-tree li.layui-nav-item:hover>a,
.layui-bg-white .layui-nav-tree li.layui-nav-item dl dd:hover>a,
.layui-bg-white .layui-nav-tree li.layui-nav-item dl dd:hover>i {
	color: #1890ff!important;
}

.layui-bg-white .layui-nav-tree .layui-nav-child dd.layui-this,
.layui-bg-white .layui-nav-tree .layui-nav-child dd.layui-this a,
.layui-bg-white .layui-nav-tree .layui-this,.layui-bg-white .layui-nav-tree .layui-this>a {
	background-color: #e6f7ff!important;;
	border-right: 1px solid #1890ff!important;;
	color: #1890ff!important;
}

.layui-bg-white .layui-nav-tree .layui-this i,
.layui-bg-white .layui-nav-tree .layui-this a {
	color: #1890ff!important;
}

.layui-nav-tree li a {
	cursor: pointer;
}

.layui-nav-tree li.layui-nav-item a>span {
	position: absolute;
	right: 32px;
}

.layui-form-checkbox[lay-skin=primary]:hover i {
	border-color: #3956de;
}

.layui-form-checked[lay-skin=primary] i {
	border-color: #3956de!important;
	background-color: #3956de;
	color: #fff;
}

.layui-table-cell div {
	display: inline-block;
}

a.layui-table-text {
	cursor: pointer;
}

a[lay-event="del"] {
	color: #FE232D;
}

.layui-form-footer {
	background-color: #fff;
	box-shadow: 0 -1px 5px rgba(0,0,0,.15);
	padding: 15px;
	z-index: 9999;
	margin-bottom: 0px;
	margin-top: 10px;
	text-align: center;
}

.layui-imagesbox .layui-upload-drag {
	height: 88px;
	padding-top: 20px;
}

.layui-upload-drag {
	padding: 15px;
}

.layui-imagesbox {
	width: 810px;
	overflow: hidden;
}

.layui-imagesbox .layui-input-inline {
	width: 181px!important;
}

.layui-imagesbox .layui-input-inline span {
	position: absolute;
	right: 5px;
	top: 5px;
	cursor: pointer;
	width: 30px;
	height: 20px;
	font-size: 10px;
	line-height: 20px;
	padding: 0;
}

.layui-imagesbox img {
	width: 100%;
	height: 116px;
	cursor: pointer;
}

.layui-project .layui-project-text {
	height: auto;
}

.layui-nav #user-info .chongzhi {
	display: inline-block;
	width: 57px;
	height: 26px;
	margin-top: 5px;
	border: 1px solid #E8001C;
	color: #E8001C;
	font-size: 12px;
	line-height: 26px;
	text-align: center;
	transition: all .3s;
}

.layui-nav #user-info .chongzhi:hover {
	color: #fff;
	background-color: #E8001C;
}

.layui-nav-tree .layui-nav-item {
	border-bottom: 1px solid rgba(0, 0, 0, 0.2);
	border-top: 1px solid rgba(255, 255, 255, 0.05);
}

.layui-nav {
	border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.layui-nav .layui-nav-itemed > .layui-nav-child {
	padding: 5px 0;
}

.layui-nav-itemed > .layui-nav-child {
	box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.2), inset 0 0px 5px rgba(0, 0, 0, 0.2);
}

.layui-nav .layui-nav-item .layui-nav-child a:hover,
.layui-nav .layui-nav-item .layui-nav-child .layui-this a {
	transition: background-color .2s, color .2s;
	background: rgba(0, 0, 0, 0.2) !important;
	color: #fff !important;
}

.layui-nav-tree .layui-nav-child dd.layui-this,
.layui-nav-tree .layui-nav-child dd.layui-this a,
.layui-nav-tree .layui-this, .layui-nav-tree .layui-this>a,
.layui-nav-tree .layui-this>a:hover {
	transition: background-color .2s, color .2s;
	background: rgba(0, 0, 0, 0.2) !important;
	color: #fff !important;
}

.layui-nav .layui-nav-item .layui-nav-child .layui-this a {
	background: #3464FF !important;
}

.layui-layout-left a.flexible-btn {
	float: left;
	margin: 18px 13px 0 0;
	color: rgba(255, 255, 255, .6);
	padding: 0 10px;
}

/* 内容开始 */
#content {
	padding: 15px;
	overflow: hidden;
	margin: 0!important;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	background: unset;
}

#iframe {
	position: absolute;
	width: 100%;
	height: 100%;
	left: 0;
	top: 0;
	right: 0;
	bottom: 0;
	overflow: auto;
}

#content .swift-index {
	padding: 20px;
	overflow: hidden;
}

#content .swift-index .tips {
	font-size: 18px;
	margin-top: 15px;
}

#content #profile {
	padding: 20px;
	overflow: hidden;
}

#profile .contract-title {
	white-space: nowrap;
	text-overflow: ellipsis;
	overflow: hidden;
	line-height: 24px;
	margin: 20px 0 6px;
	font-size: 14px;
	color: #a2a0a0;
}

#content #profile .layui-col-md3 {
	margin-bottom: 15px;
}

#content #profile i {
	cursor: pointer;
}

#content #profile i:hover {
	color: #2a94de;
}

#content #account {
	background: #2a94de;
	border-radius: 3px;
	color: #fff;
	padding: 48px 48px 20px;
}

#content #imgHead img {
	width: 90px;
	height: 90px;
	border-radius: 50px;
	cursor: pointer;
}

#content .layui-user-avatar {
	position: relative;
	display: inline-block;
	cursor: pointer;
	text-align: center;
}

#content .layui-user-avatar:hover:after {
	content: '\e65d';
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	color: #eee;
	background: rgba(0, 0, 0, 0.5);
	font-family: layui-icon;
	font-size: 24px;
	font-style: normal;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	cursor: pointer;
	line-height: 110px;
	border-radius: 50%;
}

#content .nickname {
	font-size: 23px;
	margin-top: 36px;
	color: #fff;
	margin-bottom: 10px;
}

#content #appkey {
	overflow: hidden;
}

#user-detail {
	min-height: 120px;
	border: 1px solid #eee;
	padding-left: 28px;
	padding-top: 38px;
	background: #f7f9fa ;
	color: #333;
	font-size: 12px;
	overflow: hidden;
}

#user-detail .layui-user-avatar img {
	width: 90px;
	height: 90px;
	border-radius: 50px;
	cursor: pointer;
}

#user-detail .u-info {
	line-height: 32px;
}

#user-detail .label {
	display: inline-block;
	width: 68px;
	color: #a0a2a3;
}

#user-detail .layui-progress {
	display: inline-block;
	width: 230px;
	max-width: 300px;
}

#user-detail .layui-progress-big, .layui-progress-big .layui-progress-bar {
	height: 12px;
	line-height: 12px;
}

#user-detail .text-stress {
	margin-left: 20px;
	color: #ff6600;
}

#content .settings-tier {
	clear: both;
	height: 80px;
	line-height: 79px;
	border-bottom: 1px dashed #e6e6e6;
	white-space: nowrap;
}

#content .settings-tier a {
	cursor: pointer;
}

#content .setting-status {
	height: 24px;
	width: 24px;
	background: no-repeat left center;
	vertical-align: middle;
}

#content .setting-status i.layui-icon {
	font-size: 16px;
	margin-left: 10px;
	color: #999;
}

#content .setting-status i.layui-icon-ok-circle {
	color: #0b8235;
}

#content .setting-status i.layui-icon-question {
	color: #ffa200;
}

#content .setting-name {
	display: inline-block;
	margin-left: 16px;
	font-size: 12px;
}

#content .setting-intro {
	color: #999;
	margin-left: 30px;
}

#content .setting-operate {
	width: 80px;
	white-space: nowrap;
	text-align: center;
}

#content .setting-operate a {
	color: #1890ff;
}

#content #cardInfo {
	background: rgba(52, 100, 255, 0.1);
	border: 1px solid rgba(52, 100, 255, 0.3);
	color: #0D236D;
	padding: 20px;
}

#content #cardInfo span {
	display: block;
	margin: 5px 0;
}

#content #cardInfo .urls  {
	width: 500px;
	margin-top: 15px;
	display: inline-block;
}

#content #cardInfoList {
	margin-top: 10px;
	padding: 10px 10px 20px;
}

#content #cardInfoList .cardInfoUsers {
	overflow: hidden;
}

#content #cardInfoList .cardInfoUsers li {
	display: inline-block;
	width: 89px;
	text-align: center;
	overflow: hidden;
	margin: 2px 0;
}

#content #cardInfoList .cardInfoUsers li img.media-object {
	border-radius: 120px;
	height: 40px;
	width: 40px;
}

#content #cardInfoList .cardInfoUsers li a.truncate {
	font-size: 12px;
	display: block;
	white-space: nowrap;
	overflow: hidden;
	margin-top: 6px;
	text-overflow: ellipsis;
}

#content .layui-comment {
	padding-top: 15px!important;
}

#content .layui-btn-normal {
	background: #3464FF;
}

#content .vip-head-box {
	background: -webkit-gradient(linear,left top,left bottom,from(#effff1),to(#fff));
	background: linear-gradient(180deg,#effff1 0,#fff 100%);
	-webkit-box-shadow: 0 2px 4px 0 rgba(12,0,51,.1);
	box-shadow: 0 2px 4px 0 rgba(12,0,51,.1);
	border-radius: 8px;
}

#content .user-face-box {
	position: relative;
	float: left;
	width: 172px;
	height: 100%;
	overflow: hidden;
	text-align: center;
}

#content .user-face-box .img {
	position: relative;
	width: 106px;
	height: 106px;
	margin: 30px auto 0;
}

#content .user-face-box .img img {
	width: 100%;
	height: 100%;
	border-radius: 50%;
}

#content .user-vip-info {
	height: 100%;
}

#content .user-vip-info .vip-type-time {

	width: 100%;
	height: 160px;
	padding-top: 40px;
}

#content .user-vip-info .vip-type-time .vip-tips {
	position: absolute;
	top: 17px;
	right: 18px;
	width: 153px;
	height: 33px;
	line-height: 33px;
	background: #fffefd;
	border-radius: 21px;
}

#content .user-vip-info .vip-type-time .vip-tips a {
	float: left;
	width: 50px;
	height: 15px;
	line-height: 15px;
	margin-top: 10px;
	text-align: center;
	font-size: 14px;
}

#content  .user-vip-info .vip-type-time .vip-tips a:nth-child(1) {
	border-right: 1px solid #999;
}

#content .user-vip-info .vip-type-time .vip-tips a:nth-child(2) {
	border-right: 1px solid #999;
}

#content .vip-type-time .vip-name {
	height: 30px;
	line-height: 30px;
	font-size: 18px;
	font-weight: 400;
	color: rgba(0,0,0,.65);
	width: 800px;
}

#content .vip-type-time .vip-name span {
	display: inline-block;
	height: 30px;
	line-height: 30px;
	margin-right: 15px;
	vertical-align: middle;
}

#content .vip-type-time .vip-name span a {
	display: inline-block;
	height: 30px;
	line-height: 30px;
	font-size: 14px;
	font-weight: 400;
	color: #fa6400;
	vertical-align: bottom;
}

#content .vip-time-row {
	height: 38px;
	padding-top: 20px;
	overflow-x: auto;
	overflow-y: hidden;
	white-space: nowrap;
}

#content .vip-item {
	width: auto;
	margin-right: 10px;
	display: inline-block;
	position: relative;
}

#content .vip-item span {
	color: #bebebe;
}

#content .vip-item .vip-item-type {
	background-color: #bebebe;
	color: #fff;
	padding: 3px 5px;
	border-radius: 5px;
	margin-right: 3px;
}

#content .vip-item .vip-item-name {
	font-size: 16px;
}

#content .vip-active .vip-item-type {
	background-color: #FFB800;
	color: #fff;
}

#content .vip-active .vip-item-name {
	color: #FFB800;
}

#content #login_third a {
	padding: 0px;
	margin-top: 10px;
	height: 23px;
	line-height: 23px;
}

#content .bind-third i.layui-icon {
	color: #1E9FFF;
}

#content .news-list li {
	overflow: hidden;
	padding: 0 30px;
}

#content .news-list li:hover {
	background-color: #F2F5FF;
}

#content .news-list li a {
	width: 100%;
	float: left;
	transition: all .2s;
	line-height: 49px;
	box-sizing: border-box;
	border-bottom: 1px dashed #E1E6F0;
}

#content .news-list li a strong {
	font-weight: 400;
	max-width: 260px;
	white-space: nowrap;
	text-overflow: ellipsis;
	overflow: hidden;
	float: left;
}

#content .news-list li span {
	float: right;
	margin-left: 8px;
	color: #8187A1;
	transition: all .2s;
}

#content .vip-coupon-row {
	overflow: hidden;
	white-space: nowrap;
}

#content .vip-coupon-item {
	margin-top: 20px;
	padding: 0 15px 0 10px;
	display: inline-block;
	border-right: 1px solid rgba(0,0,0,.16);
}

#content .vip-coupon-item p {
	width: 100%;
	text-align: center;
	font-size: 14px;
	font-weight: 500;
	color: rgba(0,0,0,.65);
}

#content .vip-coupon-item p:nth-child(1) {
	height: 30px;
	line-height: 30px;
}

#content .vip-coupon-row p em {
	font-size: 22px;
	font-weight: 500;
	vertical-align: middle;
	margin-right: 5px;
	font-style: normal;
}

#content .vip-coupon-item:last-of-type {
	border: 0;
}

#content .speed-entry-item {
	padding: 5px;
	text-align: center;
	border: 1px solid #eee;
	border-radius: 5px;
	cursor: pointer;
	margin-bottom: 2px;
}

#content .speed-entry-item:hover {
	background-color: #F2F5FF;
	color: #3464ff;
	border-color: #3464ff;
}

#content #pluginApp img {
	width: 595px;
	height: 116px;
}
