/* .layui-layer-photos */

.layui-layer-photos {

    background: 0 0;

    box-shadow: none

}

.layui-layer-photos .layui-layer-content {

    overflow: visible;

    text-align: center

}

.layui-layer-photos .layer-layer-photos-main img {

    position: relative;

    width: 100%;

    display: inline-block;

    *display: inline;

    *zoom:1;vertical-align: top

}

.layui-layer-photos-next,.layui-layer-photos-prev {

    position: fixed;

    top: 50%;

    width: 52px;

    height: 52px;

    line-height: 52px;

    margin-top: -26px;

    cursor: pointer;

    font-size: 52px;

    color: #717171

}

.layui-layer-photos-prev {

    left: 32px

}

.layui-layer-photos-next {

    right: 32px

}

.layui-layer-photos-next:hover,.layui-layer-photos-prev:hover {

    color: #959595

}

.layui-layer-photos-toolbar {

    position: fixed;

    left: 0;

    right: 0;

    bottom: 0;

    width: 100%;

    height: 52px;

    line-height: 52px;

    background-color: rgba(0,0,0,.32);

    color: #fff;

    text-overflow: ellipsis;

    overflow: hidden;

    white-space: nowrap;

    font-size: 0

}

.layui-layer-photos-toolbar>* {

    display: inline-block;

    vertical-align: top;

    padding: 0 16px;

    font-size: 12px;

    color: #fff;

    *display: inline;

    *zoom:1

}

.layui-layer-photos-toolbar * {

    font-size: 12px

}

.layui-layer-photos-header {

    top: 0;

    bottom: auto

}

.layui-layer-photos-header>span {

    cursor: pointer

}

.layui-layer-photos-header>span:hover {

    background-color: rgba(51,51,51,.32)

}

.layui-layer-photos-header .layui-icon {

    font-size: 18px

}

.layui-layer-photos-footer>h3 {

    max-width: 65%;

    text-overflow: ellipsis;

    overflow: hidden;

    white-space: nowrap

}

.layui-layer-photos-footer a:hover {

    text-decoration: underline

}

.layui-layer-photos-footer em {

    font-style: normal

}

@-webkit-keyframes layer-bounceOut {

    100% {

        opacity: 0;

        -webkit-transform: scale(.7);

        transform: scale(.7)

    }



    30% {

        -webkit-transform: scale(1.05);

        transform: scale(1.05)

    }



    0% {

        -webkit-transform: scale(1);

        transform: scale(1)

    }

}

@keyframes layer-bounceOut {

    100% {

        opacity: 0;

        -webkit-transform: scale(.7);

        -ms-transform: scale(.7);

        transform: scale(.7)

    }



    30% {

        -webkit-transform: scale(1.05);

        -ms-transform: scale(1.05);

        transform: scale(1.05)

    }



    0% {

        -webkit-transform: scale(1);

        -ms-transform: scale(1);

        transform: scale(1)

    }

}

.layer-anim-close {

    -webkit-animation-name: layer-bounceOut;

    animation-name: layer-bounceOut;

    -webkit-animation-fill-mode: both;

    animation-fill-mode: both;

    -webkit-animation-duration: .2s;

    animation-duration: .2s

}