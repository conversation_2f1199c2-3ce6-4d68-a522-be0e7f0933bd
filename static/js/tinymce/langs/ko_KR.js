tinymce.addI18n('ko_KR',{
"Redo": "\ub2e4\uc2dc \uc2e4\ud589",
"Undo": "\uc2e4\ud589 \ucde8\uc18c",
"Cut": "\uc798\ub77c\ub0b4\uae30",
"Copy": "\ubcf5\uc0ac",
"Paste": "\ubd99\uc5ec\ub123\uae30",
"Select all": "\uc804\uccb4\uc120\ud0dd",
"New document": "\uc0c8 \ubb38\uc11c",
"Ok": "\ud655\uc778",
"Cancel": "\ucde8\uc18c",
"Visual aids": "\uc2dc\uac01\uad50\uc7ac",
"Bold": "\uad75\uac8c",
"Italic": "\uae30\uc6b8\uc784\uaf34",
"Underline": "\ubc11\uc904",
"Strikethrough": "\ucde8\uc18c\uc120",
"Superscript": "\uc704 \ucca8\uc790",
"Subscript": "\uc544\ub798 \ucca8\uc790",
"Clear formatting": "\uc11c\uc2dd \uc9c0\uc6b0\uae30",
"Align left": "\uc67c\ucabd \ub9de\ucda4",
"Align center": "\uac00\uc6b4\ub370 \ub9de\ucda4",
"Align right": "\uc624\ub978\ucabd \ub9de\ucda4",
"Justify": "\uc591\ucabd \ub9de\ucda4",
"Bullet list": "\uae00\uba38\ub9ac \uae30\ud638 \ubaa9\ub85d",
"Numbered list": "\ubc88\ud638 \ub9e4\uae30\uae30 \ubaa9\ub85d",
"Decrease indent": "\ub0b4\uc5b4\uc4f0\uae30",
"Increase indent": "\ub4e4\uc5ec\uc4f0\uae30",
"Close": "\ub2eb\uae30",
"Formats": "\uc11c\uc2dd",
"Your browser doesn't support direct access to the clipboard. Please use the Ctrl+X\/C\/V keyboard shortcuts instead.": "\ube0c\ub77c\uc6b0\uc800\uac00 \ud074\ub9bd\ubcf4\ub4dc \uc811\uadfc\uc744 \uc9c0\uc6d0\ud558\uc9c0 \uc54a\uc2b5\ub2c8\ub2e4. Ctrl+X\/C\/V \ub2e8\ucd95\ud0a4\ub97c \uc774\uc6a9\ud558\uc2ed\uc2dc\uc624.",
"Headers": "\uba38\ub9ac\uae00",
"Header 1": "\uba38\ub9ac\uae00 1",
"Header 2": "\uba38\ub9ac\uae00 2",
"Header 3": "\uba38\ub9ac\uae00 3",
"Header 4": "\uba38\ub9ac\uae00 4",
"Header 5": "\uba38\ub9ac\uae00 5",
"Header 6": "\uba38\ub9ac\uae00 6",
"Headings": "\uc81c\ubaa9",
"Heading 1": "\uc81c\ubaa9 1",
"Heading 2": "\uc81c\ubaa9 2",
"Heading 3": "\uc81c\ubaa9 3",
"Heading 4": "\uc81c\ubaa9 4",
"Heading 5": "\uc81c\ubaa9 5",
"Heading 6": "\uc81c\ubaa9 6",
"Preformatted": "\uc11c\uc2dd \ubbf8\uc124\uc815",
"Div": "Div",
"Pre": "Pre",
"Code": "\ucf54\ub4dc",
"Paragraph": "\ub2e8\ub77d",
"Blockquote": "\uc778\uc6a9\ubb38",
"Inline": "\uc778\ub77c\uc778",
"Blocks": "\ube14\ub85d",
"Paste is now in plain text mode. Contents will now be pasted as plain text until you toggle this option off.": "\uc2a4\ud0c0\uc77c\ubcf5\uc0ac \ub044\uae30. \uc774 \uc635\uc158\uc744 \ub044\uae30 \uc804\uc5d0\ub294 \ubcf5\uc0ac \uc2dc, \uc2a4\ud0c0\uc77c\uc774 \ubcf5\uc0ac\ub418\uc9c0 \uc54a\uc2b5\ub2c8\ub2e4.",
"Fonts": "\uae00\uaf34",
"Font Sizes": "\uae00\uaf34 \ud06c\uae30",
"Class": "\ud074\ub798\uc2a4",
"Browse for an image": "\uc774\ubbf8\uc9c0 \ucc3e\uae30",
"OR": "\ub610\ub294",
"Drop an image here": "\uc5ec\uae30\ub85c \uc774\ubbf8\uc9c0 \ub04c\uc5b4\uc624\uae30",
"Upload": "\uc5c5\ub85c\ub4dc",
"Block": "\ube14\ub85d",
"Align": "\uc815\ub82c",
"Default": "\uae30\ubcf8",
"Circle": "\uc6d0",
"Disc": "\uc6d0\ubc18",
"Square": "\uc0ac\uac01",
"Lower Alpha": "\uc54c\ud30c\ubcb3 \uc18c\ubb38\uc790",
"Lower Greek": "\uadf8\ub9ac\uc2a4\uc5b4 \uc18c\ubb38\uc790",
"Lower Roman": "\ub85c\ub9c8\uc790 \uc18c\ubb38\uc790",
"Upper Alpha": "\uc54c\ud30c\ubcb3 \uc18c\ubb38\uc790",
"Upper Roman": "\ub85c\ub9c8\uc790 \ub300\ubb38\uc790",
"Anchor...": "\uc575\ucee4...",
"Name": "\uc774\ub984",
"Id": "\uc544\uc774\ub514",
"Id should start with a letter, followed only by letters, numbers, dashes, dots, colons or underscores.": "\uc544\uc774\ub514\ub294 \ubb38\uc790, \uc22b\uc790, \ub300\uc2dc, \uc810, \ucf5c\ub860 \ub610\ub294 \ubc11\uc904\ub85c \uc2dc\uc791\ud574\uc57c\ud569\ub2c8\ub2e4.",
"You have unsaved changes are you sure you want to navigate away?": "\uc800\uc7a5\ud558\uc9c0 \uc54a\uc740 \uc815\ubcf4\uac00 \uc788\uc2b5\ub2c8\ub2e4. \uc774 \ud398\uc774\uc9c0\ub97c \ubc97\uc5b4\ub098\uc2dc\uaca0\uc2b5\ub2c8\uae4c?",
"Restore last draft": "\ub9c8\uc9c0\ub9c9 \ucd08\uc548 \ubcf5\uc6d0",
"Special character...": "\ud2b9\uc218 \ubb38\uc790...",
"Source code": "\uc18c\uc2a4\ucf54\ub4dc",
"Insert\/Edit code sample": "\ucf54\ub4dc\uc0d8\ud50c \uc0bd\uc785\/\ud3b8\uc9d1",
"Language": "\uc5b8\uc5b4",
"Code sample...": "\ucf54\ub4dc \uc0d8\ud50c...",
"Color Picker": "\uc0c9 \uc120\ud0dd\uae30",
"R": "R",
"G": "G",
"B": "B",
"Left to right": "\uc67c\ucabd\uc5d0\uc11c \uc624\ub978\ucabd",
"Right to left": "\uc624\ub978\ucabd\uc5d0\uc11c \uc67c\ucabd",
"Emoticons...": "\uc774\ubaa8\ud2f0\ucf58...",
"Metadata and Document Properties": "\uba54\ud0c0\ub370\uc774\ud130\uc640 \ubb38\uc11c \uc18d\uc131",
"Title": "\uc81c\ubaa9",
"Keywords": "\ud0a4\uc6cc\ub4dc",
"Description": "\uc124\uba85",
"Robots": "\ub85c\ubd07",
"Author": "\uc800\uc790",
"Encoding": "\uc778\ucf54\ub529",
"Fullscreen": "\uc804\uccb4\ud654\uba74",
"Action": "\ub3d9\uc791",
"Shortcut": "\ub2e8\ucd95\ud0a4",
"Help": "\ub3c4\uc6c0\ub9d0",
"Address": "\uc8fc\uc18c",
"Focus to menubar": "\uba54\ub274\uc5d0 \ud3ec\ucee4\uc2a4",
"Focus to toolbar": "\ud234\ubc14\uc5d0 \ud3ec\ucee4\uc2a4",
"Focus to element path": "element path\uc5d0 \ud3ec\ucee4\uc2a4",
"Focus to contextual toolbar": "\ucf04\ud14d\uc2a4\ud2b8 \ud234\ubc14\uc5d0 \ud3ec\ucee4\uc2a4",
"Insert link (if link plugin activated)": "\ub9c1\ud06c \uc0bd\uc785 (link \ud50c\ub7ec\uadf8\uc778\uc774 \ud65c\uc131\ud654\ub41c \uc0c1\ud0dc\uc5d0\uc11c)",
"Save (if save plugin activated)": "\uc800\uc7a5 (save \ud50c\ub7ec\uadf8\uc778\uc774 \ud65c\uc131\ud654\ub41c \uc0c1\ud0dc\uc5d0\uc11c)",
"Find (if searchreplace plugin activated)": "\ucc3e\uae30(searchreplace \ud50c\ub7ec\uadf8\uc778\uc774 \ud65c\uc131\ud654\ub41c \uc0c1\ud0dc\uc5d0\uc11c)",
"Plugins installed ({0}):": "\uc124\uce58\ub41c \ud50c\ub7ec\uadf8\uc778 ({0}):",
"Premium plugins:": "\uace0\uae09 \ud50c\ub7ec\uadf8\uc778",
"Learn more...": "\uc880 \ub354 \uc0b4\ud3b4\ubcf4\uae30",
"You are using {0}": "{0}\ub97c \uc0ac\uc6a9\uc911",
"Plugins": "\ud50c\ub7ec\uadf8\uc778",
"Handy Shortcuts": "\ub2e8\ucd95\ud0a4",
"Horizontal line": "\uac00\ub85c",
"Insert\/edit image": "\uc774\ubbf8\uc9c0 \uc0bd\uc785\/\uc218\uc815",
"Image description": "\uc774\ubbf8\uc9c0 \uc124\uba85",
"Source": "\uc18c\uc2a4",
"Dimensions": "\ud06c\uae30",
"Constrain proportions": "\uc791\uc5c5 \uc81c\ud55c",
"General": "\uc77c\ubc18",
"Advanced": "\uace0\uae09",
"Style": "\uc2a4\ud0c0\uc77c",
"Vertical space": "\uc218\uc9c1 \uacf5\ubc31",
"Horizontal space": "\uc218\ud3c9 \uacf5\ubc31",
"Border": "\ud14c\ub450\ub9ac",
"Insert image": "\uc774\ubbf8\uc9c0 \uc0bd\uc785",
"Image...": "\uc774\ubbf8\uc9c0...",
"Image list": "\uc774\ubbf8\uc9c0 \ubaa9\ub85d",
"Rotate counterclockwise": "\uc2dc\uacc4\ubc18\ub300\ubc29\ud5a5\uc73c\ub85c \ud68c\uc804",
"Rotate clockwise": "\uc2dc\uacc4\ubc29\ud5a5\uc73c\ub85c \ud68c\uc804",
"Flip vertically": "\uc218\uc9c1 \ub4a4\uc9d1\uae30",
"Flip horizontally": "\uc218\ud3c9 \ub4a4\uc9d1\uae30",
"Edit image": "\uc774\ubbf8\uc9c0 \ud3b8\uc9d1",
"Image options": "\uc774\ubbf8\uc9c0 \uc635\uc158",
"Zoom in": "\ud655\ub300",
"Zoom out": "\ucd95\uc18c",
"Crop": "\uc790\ub974\uae30",
"Resize": "\ud06c\uae30 \uc870\uc808",
"Orientation": "\ubc29\ud5a5",
"Brightness": "\ubc1d\uae30",
"Sharpen": "\uc120\uba85\ud558\uac8c",
"Contrast": "\ub300\ube44",
"Color levels": "\uc0c9\uc0c1\ub808\ubca8",
"Gamma": "\uac10\ub9c8",
"Invert": "\ubc18\uc804",
"Apply": "\uc801\uc6a9",
"Back": "\ub4a4\ub85c",
"Insert date\/time": "\ub0a0\uc9dc\/\uc2dc\uac04\uc0bd\uc785",
"Date\/time": "\ub0a0\uc9dc\/\uc2dc\uac04",
"Insert\/Edit Link": "\ub9c1\ud06c \uc0bd\uc785\/\ud3b8\uc9d1",
"Insert\/edit link": "\ub9c1\ud06c \uc0bd\uc785\/\uc218\uc815",
"Text to display": "\ubcf8\ubb38",
"Url": "\uc8fc\uc18c",
"Open link in...": "...\uc5d0\uc11c \ub9c1\ud06c \uc5f4\uae30",
"Current window": "\ud604\uc7ac \ucc3d",
"None": "\uc5c6\uc74c",
"New window": "\uc0c8\ucc3d",
"Remove link": "\ub9c1\ud06c\uc0ad\uc81c",
"Anchors": "\ucc45\uac08\ud53c",
"Link...": "\ub9c1\ud06c...",
"Paste or type a link": "\ub9c1\ud06c\ub97c \ubd99\uc5ec\ub123\uac70\ub098 \uc785\ub825\ud558\uc138\uc694",
"The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?": "\ud604\uc7ac E-mail\uc8fc\uc18c\ub97c \uc785\ub825\ud558\uc168\uc2b5\ub2c8\ub2e4. E-mail \uc8fc\uc18c\uc5d0 \ub9c1\ud06c\ub97c \uac78\uae4c\uc694?",
"The URL you entered seems to be an external link. Do you want to add the required http:\/\/ prefix?": "\ud604\uc7ac \uc6f9\uc0ac\uc774\ud2b8 \uc8fc\uc18c\ub97c \uc785\ub825\ud558\uc168\uc2b5\ub2c8\ub2e4. \ud574\ub2f9 \uc8fc\uc18c\uc5d0 \ub9c1\ud06c\ub97c \uac78\uae4c\uc694?",
"Link list": "\ub9c1\ud06c \ub9ac\uc2a4\ud2b8",
"Insert video": "\ube44\ub514\uc624 \uc0bd\uc785",
"Insert\/edit video": "\ube44\ub514\uc624 \uc0bd\uc785\/\uc218\uc815",
"Insert\/edit media": "\ubbf8\ub514\uc5b4 \uc0bd\uc785\/\uc218\uc815",
"Alternative source": "\ub300\uccb4 \uc18c\uc2a4",
"Alternative source URL": "\ub300\uccb4 \uc6d0\ubcf8 URL",
"Media poster (Image URL)": "\ub300\ud45c \uc774\ubbf8\uc9c0(\uc774\ubbf8\uc9c0 URL)",
"Paste your embed code below:": "\uc544\ub798\uc5d0 \ucf54\ub4dc\ub97c \ubd99\uc5ec\ub123\uc73c\uc138\uc694:",
"Embed": "\uc0bd\uc785",
"Media...": "\ubbf8\ub514\uc5b4...",
"Nonbreaking space": "\ub744\uc5b4\uc4f0\uae30",
"Page break": "\ud398\uc774\uc9c0 \uad6c\ubd84\uc790",
"Paste as text": "\ud14d\uc2a4\ud2b8\ub85c \ubd99\uc5ec\ub123\uae30",
"Preview": "\ubbf8\ub9ac\ubcf4\uae30",
"Print...": "\uc778\uc1c4...",
"Save": "\uc800\uc7a5",
"Find": "\ucc3e\uae30",
"Replace with": "\uad50\uccb4",
"Replace": "\uad50\uccb4",
"Replace all": "\uc804\uccb4 \uad50\uccb4",
"Previous": "\uc774\uc804",
"Next": "\ub2e4\uc74c",
"Find and replace...": "\ucc3e\uae30 \ubc0f \ubc14\uafb8\uae30...",
"Could not find the specified string.": "\ubb38\uc790\ub97c \ucc3e\uc744 \uc218 \uc5c6\uc2b5\ub2c8\ub2e4.",
"Match case": "\ub300\uc18c\ubb38\uc790 \uc77c\uce58",
"Find whole words only": "\ubaa8\ub450 \uc77c\uce58\ud558\ub294 \ubb38\uc790 \ucc3e\uae30",
"Spell check": "\ub9de\ucda4\ubc95 \uac80\uc0ac",
"Ignore": "\ubb34\uc2dc",
"Ignore all": "\uc804\uccb4\ubb34\uc2dc",
"Finish": "\uc644\ub8cc",
"Add to Dictionary": "\uc0ac\uc804\uc5d0 \ucd94\uac00",
"Insert table": "\ud14c\uc774\ube14 \uc0bd\uc785",
"Table properties": "\ud14c\uc774\ube14 \uc18d\uc131",
"Delete table": "\ud14c\uc774\ube14 \uc0ad\uc81c",
"Cell": "\uc140",
"Row": "\uc5f4",
"Column": "\ud589",
"Cell properties": "\uc140 \uc18d",
"Merge cells": "\uc140 \ud569\uce58\uae30",
"Split cell": "\uc140 \ub098\ub204\uae30",
"Insert row before": "\uc774\uc804\uc5d0 \ud589 \uc0bd\uc785",
"Insert row after": "\ub2e4\uc74c\uc5d0 \ud589 \uc0bd\uc785",
"Delete row": "\ud589 \uc9c0\uc6b0\uae30",
"Row properties": "\ud589 \uc18d\uc131",
"Cut row": "\ud589 \uc798\ub77c\ub0b4\uae30",
"Copy row": "\ud589 \ubcf5\uc0ac",
"Paste row before": "\uc774\uc804\uc5d0 \ud589 \ubd99\uc5ec\ub123\uae30",
"Paste row after": "\ub2e4\uc74c\uc5d0 \ud589 \ubd99\uc5ec\ub123\uae30",
"Insert column before": "\uc774\uc804\uc5d0 \ud589 \uc0bd\uc785",
"Insert column after": "\ub2e4\uc74c\uc5d0 \uc5f4 \uc0bd\uc785",
"Delete column": "\uc5f4 \uc9c0\uc6b0\uae30",
"Cols": "\uc5f4",
"Rows": "\ud589",
"Width": "\ub113\uc774",
"Height": "\ub192\uc774",
"Cell spacing": "\uc140 \uac04\uaca9",
"Cell padding": "\uc140 \uc548\ucabd \uc5ec\ubc31",
"Show caption": "\ucea1\uc158 \ud45c\uc2dc",
"Left": "\uc67c\ucabd",
"Center": "\uac00\uc6b4\ub370",
"Right": "\uc624\ub978\ucabd",
"Cell type": "\uc140 \ud0c0\uc785",
"Scope": "\ubc94\uc704",
"Alignment": "\uc815\ub82c",
"H Align": "\uac00\ub85c \uc815\ub82c",
"V Align": "\uc138\ub85c \uc815\ub82c",
"Top": "\uc0c1\ub2e8",
"Middle": "\uc911\uac04",
"Bottom": "\ud558\ub2e8",
"Header cell": "\ud5e4\ub354 \uc140",
"Row group": "\ud589 \uadf8\ub8f9",
"Column group": "\uc5f4 \uadf8\ub8f9",
"Row type": "\ud589 \ud0c0\uc785",
"Header": "\ud5e4\ub354",
"Body": "\ubc14\ub514",
"Footer": "\ud478\ud130",
"Border color": "\ud14c\ub450\ub9ac \uc0c9",
"Insert template...": "\ud15c\ud50c\ub9bf \uc0bd\uc785...",
"Templates": "\ud15c\ud50c\ub9bf",
"Template": "\ud15c\ud50c\ub9bf",
"Text color": "\ubb38\uc790 \uc0c9\uae54",
"Background color": "\ubc30\uacbd\uc0c9",
"Custom...": "\uc9c1\uc811 \uc0c9\uae54 \uc9c0\uc815\ud558\uae30",
"Custom color": "\uc9c1\uc811 \uc9c0\uc815\ud55c \uc0c9\uae54",
"No color": "\uc0c9\uc0c1 \uc5c6\uc74c",
"Remove color": "\uc0c9 \uc81c\uac70",
"Table of Contents": "\ubaa9\ucc28",
"Show blocks": "\ube14\ub7ed \ubcf4\uc5ec\uc8fc\uae30",
"Show invisible characters": "\uc548\ubcf4\uc774\ub294 \ubb38\uc790 \ubcf4\uc774\uae30",
"Word count": "\ub2e8\uc5b4 \uc218",
"Count": "\uac1c\uc218",
"Document": "\ubb38\uc11c",
"Selection": "\uc120\ud0dd",
"Words": "\ub2e8\uc5b4",
"Words: {0}": "\ub2e8\uc5b4: {0}",
"{0} words": "{0} \ub2e8\uc5b4",
"File": "\ud30c\uc77c",
"Edit": "\uc218\uc815",
"Insert": "\uc0bd\uc785",
"View": "\ubcf4\uae30",
"Format": "\ud3ec\ub9f7",
"Table": "\ud14c\uc774\ube14",
"Tools": "\ub3c4\uad6c",
"Powered by {0}": "Powered by {0}",
"Rich Text Area. Press ALT-F9 for menu. Press ALT-F10 for toolbar. Press ALT-0 for help": "\uc11c\uc2dd \uc788\ub294 \ud14d\uc2a4\ud2b8 \ud3b8\uc9d1\uae30 \uc785\ub2c8\ub2e4. ALT-F9\ub97c \ub204\ub974\uba74 \uba54\ub274, ALT-F10\ub97c \ub204\ub974\uba74 \ud234\ubc14, ALT-0\uc744 \ub204\ub974\uba74 \ub3c4\uc6c0\ub9d0\uc744 \ubcfc \uc218 \uc788\uc2b5\ub2c8\ub2e4.",
"Image title": "\uc774\ubbf8\uc9c0 \uc81c\ubaa9",
"Border width": "\ud14c\ub450\ub9ac \ub450\uaed8",
"Border style": "\ud14c\ub450\ub9ac \uc2a4\ud0c0\uc77c",
"Error": "\uc624\ub958",
"Warn": "\uacbd\uace0",
"Valid": "\uc720\ud6a8\ud568",
"To open the popup, press Shift+Enter": "\ud31d\uc5c5\uc744 \uc5f4\ub824\uba74 Shift+Enter\ub97c \ub204\ub974\uc2ed\uc2dc\uc624.",
"Rich Text Area. Press ALT-0 for help.": "\uc11c\uc2dd \uc788\ub294 \ud14d\uc2a4\ud2b8 \uc601\uc5ed. ALT-0\uc744 \ub204\ub974\uba74 \ub3c4\uc6c0\ub9d0\uc744 \ubcfc \uc218 \uc788\uc2b5\ub2c8\ub2e4.",
"System Font": "\uc2dc\uc2a4\ud15c \uae00\uaf34",
"Failed to upload image: {0}": "\uc774\ubbf8\uc9c0 \uc5c5\ub85c\ub4dc \uc2e4\ud328: {0}",
"Failed to load plugin: {0} from url {1}": "\ud50c\ub7ec\uadf8\uc778 \ub85c\ub4dc \uc2e4\ud328:  URL: {1}\uc5d0\uc11c\uc758 {0}",
"Failed to load plugin url: {0}": "\ud50c\ub7ec\uadf8\uc778 URL \ub85c\ub4dc \uc2e4\ud328: {0}",
"Failed to initialize plugin: {0}": "\ud50c\ub7ec\uadf8\uc778 \ucd08\uae30\ud654 \uc2e4\ud328: {0}",
"example": "\uc608\uc81c",
"Search": "\uac80\uc0c9",
"All": "\ubaa8\ub450",
"Currency": "\ud1b5\ud654",
"Text": "\ud14d\uc2a4\ud2b8",
"Quotations": "\uc778\uc6a9\ubb38",
"Mathematical": "\uc218\ud559",
"Extended Latin": "\ud655\uc7a5 \ub77c\ud2f4\uc5b4",
"Symbols": "\uae30\ud638",
"Arrows": "\ud654\uc0b4\ud45c",
"User Defined": "\uc0ac\uc6a9\uc790 \uc815\uc758",
"dollar sign": "\ub2ec\ub7ec \uae30\ud638",
"currency sign": "\ud1b5\ud654 \uae30\ud638",
"euro-currency sign": "\uc720\ub85c\ud654 \uae30\ud638",
"colon sign": "\ucf5c\ub860 \uae30\ud638",
"cruzeiro sign": "\ud06c\ub8e8\uc81c\uc774\ub8e8 \uae30\ud638",
"french franc sign": "\ud504\ub791\uc2a4 \ud504\ub791 \uae30\ud638",
"lira sign": "\ub9ac\ub77c \uae30\ud638",
"mill sign": "\ubc00 \uae30\ud638",
"naira sign": "\ub098\uc774\ub77c \uae30\ud638",
"peseta sign": "\ud398\uc138\ud0c0 \uae30\ud638",
"rupee sign": "\ub8e8\ud53c \uae30\ud638",
"won sign": "\uc6d0 \uae30\ud638",
"new sheqel sign": "\ub274 \uc138\ucf08 \uae30\ud638",
"dong sign": "\ub3d9 \uae30\ud638",
"kip sign": "\ud0b5 \uae30\ud638",
"tugrik sign": "\ud22c\uadf8\ub9ac\ud06c \uae30\ud638",
"drachma sign": "\ub4dc\ub77c\ud06c\ub9c8 \uae30\ud638",
"german penny symbol": "\ub3c5\uc77c \ud398\ub2c8 \uae30\ud638",
"peso sign": "\ud398\uc18c \uae30\ud638",
"guarani sign": "\uacfc\ub77c\ub2c8 \uae30\ud638",
"austral sign": "\uc544\uc6b0\uc2a4\ud2b8\ub784 \uae30\ud638",
"hryvnia sign": "\uadf8\ub9ac\ube0c\ub098 \uae30\ud638",
"cedi sign": "\uc138\ub514 \uae30\ud638",
"livre tournois sign": "\ub9ac\ube0c\ub974 \ud2b8\ub974\ub204\uc544 \uae30\ud638",
"spesmilo sign": "\uc2a4\ud398\uc2a4\ubc00\ub85c \uae30\ud638",
"tenge sign": "\ud161\uac8c \uae30\ud638",
"indian rupee sign": "\uc778\ub3c4 \ub8e8\ud53c \uae30\ud638",
"turkish lira sign": "\ud130\ud0a4 \ub9ac\ub77c \uae30\ud638",
"nordic mark sign": "\ub178\ub974\ub515 \ub9c8\ub974\ud06c \uae30\ud638",
"manat sign": "\ub9c8\ub098\ud2b8 \uae30\ud638",
"ruble sign": "\ub8e8\ube14 \uae30\ud638",
"yen character": "\uc5d4 \uae30\ud638",
"yuan character": "\uc704\uc548 \uae30\ud638",
"yuan character, in hong kong and taiwan": "\ub300\ub9cc \uc704\uc548 \uae30\ud638",
"yen\/yuan character variant one": "\uc5d4\/\uc704\uc548 \ubb38\uc790 \ubcc0\ud615",
"Loading emoticons...": "\uc774\ubaa8\ud2f0\ucf58 \ubd88\ub7ec\uc624\ub294 \uc911...",
"Could not load emoticons": "\uc774\ubaa8\ud2f0\ucf58\uc744 \ubd88\ub7ec\uc62c \uc218 \uc5c6\uc74c",
"People": "\uc0ac\ub78c",
"Animals and Nature": "\ub3d9\ubb3c\uacfc \uc790\uc5f0",
"Food and Drink": "\uc74c\uc2dd\uacfc \uc74c\ub8cc",
"Activity": "\ud65c\ub3d9",
"Travel and Places": "\uc5ec\ud589\uacfc \uc7a5\uc18c",
"Objects": "\ubb3c\uac74",
"Flags": "\uae43\ubc1c",
"Characters": "\ubb38\uc790",
"Characters (no spaces)": "\ubb38\uc790(\uacf5\ubc31 \uc5c6\uc74c)",
"{0} characters": "{0} \ubb38\uc790",
"Error: Form submit field collision.": "\uc624\ub958: \uc591\uc2dd \uc81c\ucd9c \ud544\ub4dc \ubd88\uc77c\uce58",
"Error: No form element found.": "\uc624\ub958: \uc591\uc2dd \ud56d\ubaa9 \uc5c6\uc74c",
"Update": "\uc5c5\ub370\uc774\ud2b8",
"Color swatch": "\uc0c9\uc0c1 \uacac\ubcf8",
"Turquoise": "\uccad\ub85d\uc0c9",
"Green": "\ucd08\ub85d\uc0c9",
"Blue": "\ud30c\ub780\uc0c9",
"Purple": "\ubcf4\ub77c\uc0c9",
"Navy Blue": "\ub0a8\uc0c9",
"Dark Turquoise": "\uc9c4\ud55c \uccad\ub85d\uc0c9",
"Dark Green": "\uc9c4\ud55c \ucd08\ub85d\uc0c9",
"Medium Blue": "\uc911\uac04 \ud30c\ub780\uc0c9",
"Medium Purple": "\uc911\uac04 \ubcf4\ub77c\uc0c9",
"Midnight Blue": "\uc9c4\ud55c \ud30c\ub780\uc0c9",
"Yellow": "\ub178\ub780\uc0c9",
"Orange": "\uc8fc\ud669\uc0c9",
"Red": "\ube68\uac04\uc0c9",
"Light Gray": "\ubc1d\uc740 \ud68c\uc0c9",
"Gray": "\ud68c\uc0c9",
"Dark Yellow": "\uc9c4\ud55c \ub178\ub780\uc0c9",
"Dark Orange": "\uc9c4\ud55c \uc8fc\ud669\uc0c9",
"Dark Red": "\uc9c4\ud55c \ube68\uac04\uc0c9",
"Medium Gray": "\uc911\uac04 \ud68c\uc0c9",
"Dark Gray": "\uc9c4\ud55c \ud68c\uc0c9",
"Light Green": "\ubc1d\uc740 \ub179\uc0c9",
"Light Yellow": "\ubc1d\uc740 \ub178\ub780\uc0c9",
"Light Red": "\ubc1d\uc740 \ube68\uac04\uc0c9",
"Light Purple": "\ubc1d\uc740 \ubcf4\ub77c\uc0c9",
"Light Blue": "\ubc1d\uc740 \ud30c\ub780\uc0c9",
"Dark Purple": "\uc9c4\ud55c \ubcf4\ub77c\uc0c9",
"Dark Blue": "\uc9c4\ud55c \ud30c\ub780\uc0c9",
"Black": "\uac80\uc740\uc0c9",
"White": "\ud770\uc0c9",
"Switch to or from fullscreen mode": "\uc804\uccb4 \ud654\uba74\uc73c\ub85c\/\uc5d0\uc11c \uc804\ud658",
"Open help dialog": "\ub3c4\uc6c0\ub9d0 \ub300\ud654\ucc3d \uc5f4\uae30",
"history": "\uae30\ub85d",
"styles": "\uc2a4\ud0c0\uc77c",
"formatting": "\ud3ec\ub9f7\ud305",
"alignment": "\uc815\ub82c",
"indentation": "\ub4e4\uc5ec\uc4f0\uae30",
"permanent pen": "\uc720\uc131\ud39c",
"comments": "\uc8fc\uc11d",
"Format Painter": "\uc11c\uc2dd \ubcf5\uc0ac",
"Insert\/edit iframe": "\uc544\uc774\ud504\ub808\uc784 \uc0bd\uc785\/\ud3b8\uc9d1",
"Capitalization": "\ub300\ubb38\uc790\ud654",
"lowercase": "\uc18c\ubb38\uc790",
"UPPERCASE": "\ub300\ubb38\uc790",
"Title Case": "\uc81c\ubaa9\uc744 \ub300\ubb38\uc790\ud654",
"Permanent Pen Properties": "\uc601\uad6c \ud39c \ud2b9\uc131",
"Permanent pen properties...": "\uc601\uad6c \ud39c \ud2b9\uc131...",
"Font": "\uae00\uaf34",
"Size": "\ud06c\uae30",
"More...": "\ub354 \ubcf4\uae30...",
"Spellcheck Language": "\ub9de\ucda4\ubc95 \uac80\uc0ac \uc5b8\uc5b4",
"Select...": "\uc120\ud0dd...",
"Preferences": "\ud658\uacbd\uc124\uc815",
"Yes": "\ub124",
"No": "\uc544\ub2c8\uc624",
"Keyboard Navigation": "\ud0a4 \uc120\ud0dd",
"Version": "\ubc84\uc804",
"Anchor": "\uc575\ucee4",
"Special character": "\ud2b9\uc218\ubb38\uc790",
"Code sample": "\ucf54\ub4dc\uc0d8\ud50c",
"Color": "\uc0c9\uc0c1",
"Emoticons": "\uc774\ubaa8\ud2f0\ucf58",
"Document properties": "\ubb38\uc11c \uc18d\uc131",
"Image": "\uc774\ubbf8\uc9c0",
"Insert link": "\ub9c1\ud06c \uc0bd\uc785 ",
"Target": "\ub300\uc0c1",
"Link": "\ub9c1\ud06c",
"Poster": "\ud3ec\uc2a4\ud130",
"Media": "\ubbf8\ub514\uc5b4",
"Print": "\ucd9c\ub825",
"Prev": "\uc774\uc804",
"Find and replace": "\ucc3e\uc544\uc11c \uad50\uccb4",
"Whole words": "\uc804\uccb4 \ub2e8\uc5b4",
"Spellcheck": "\ubb38\ubc95\uccb4\ud06c",
"Caption": "\ucea1\uc158",
"Insert template": "\ud15c\ud50c\ub9bf \uc0bd\uc785"
});