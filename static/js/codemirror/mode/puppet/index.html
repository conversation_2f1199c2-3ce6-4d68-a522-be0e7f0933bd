<!doctype html>

<title>CodeMirror: Puppet mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">

<link rel="stylesheet" href="../../lib/codemirror.css">
<script src="../../lib/codemirror.js"></script>
<script src="../../addon/edit/matchbrackets.js"></script>
<script src="puppet.js"></script>
<style>
      .CodeMirror {border-top: 1px solid black; border-bottom: 1px solid black;}
      .cm-s-default span.cm-arrow { color: red; }
    </style>
<div id=nav>
  <a href="https://codemirror.net"><h1>CodeMirror</h1><img id=logo src="../../doc/logo.png" alt=""></a>

  <ul>
    <li><a href="../../index.html">Home</a>
    <li><a href="../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror">Code</a>
  </ul>
  <ul>
    <li><a href="../index.html">Language modes</a>
    <li><a class=active href="#">Puppet</a>
  </ul>
</div>

<article>
<h2>Puppet mode</h2>
<form><textarea id="code" name="code">
# == Class: automysqlbackup
#
# Puppet module to install AutoMySQLBackup for periodic MySQL backups.
#
# class { 'automysqlbackup':
#   backup_dir => '/mnt/backups',
# }
#

class automysqlbackup (
  $bin_dir = $automysqlbackup::params::bin_dir,
  $etc_dir = $automysqlbackup::params::etc_dir,
  $backup_dir = $automysqlbackup::params::backup_dir,
  $install_multicore = undef,
  $config = {},
  $config_defaults = {},
) inherits automysqlbackup::params {

# Ensure valid paths are assigned
  validate_absolute_path($bin_dir)
  validate_absolute_path($etc_dir)
  validate_absolute_path($backup_dir)

# Create a subdirectory in /etc for config files
  file { $etc_dir:
    ensure => directory,
    owner => 'root',
    group => 'root',
    mode => '0750',
  }

# Create an example backup file, useful for reference
  file { "${etc_dir}/automysqlbackup.conf.example":
    ensure => file,
    owner => 'root',
    group => 'root',
    mode => '0660',
    source => 'puppet:///modules/automysqlbackup/automysqlbackup.conf',
  }

# Add files from the developer
  file { "${etc_dir}/AMB_README":
    ensure => file,
    source => 'puppet:///modules/automysqlbackup/AMB_README',
  }
  file { "${etc_dir}/AMB_LICENSE":
    ensure => file,
    source => 'puppet:///modules/automysqlbackup/AMB_LICENSE',
  }

# Install the actual binary file
  file { "${bin_dir}/automysqlbackup":
    ensure => file,
    owner => 'root',
    group => 'root',
    mode => '0755',
    source => 'puppet:///modules/automysqlbackup/automysqlbackup',
  }

# Create the base backup directory
  file { $backup_dir:
    ensure => directory,
    owner => 'root',
    group => 'root',
    mode => '0755',
  }

# If you'd like to keep your config in hiera and pass it to this class
  if !empty($config) {
    create_resources('automysqlbackup::backup', $config, $config_defaults)
  }

# If using RedHat family, must have the RPMforge repo's enabled
  if $install_multicore {
    package { ['pigz', 'pbzip2']: ensure => installed }
  }

}
</textarea></form>
    <script>
      var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
        mode: "text/x-puppet",
        matchBrackets: true,
        indentUnit: 4
      });
    </script>

    <p><strong>MIME types defined:</strong> <code>text/x-puppet</code>.</p>

  </article>
