<!doctype html>

<title>CodeMirror: Crystal mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">

<link rel="stylesheet" href="../../lib/codemirror.css">
<script src="../../lib/codemirror.js"></script>
<script src="../../addon/edit/matchbrackets.js"></script>
<script src="crystal.js"></script>
<style>
  .CodeMirror {border-top: 1px solid black; border-bottom: 1px solid black;}
  .cm-s-default span.cm-arrow { color: red; }
</style>

<div id=nav>
  <a href="https://codemirror.net"><h1>CodeMirror</h1><img id=logo src="../../doc/logo.png" alt=""></a>

  <ul>
    <li><a href="../../index.html">Home</a>
    <li><a href="../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror">Code</a>
  </ul>
  <ul>
    <li><a href="../index.html">Language modes</a>
    <li><a class=active href="#">Crystal</a>
  </ul>
</div>

<article>
<h2>Crystal mode</h2>
<form><textarea id="code" name="code">
# Features of Crystal
# - Ruby-inspired syntax.
# - Statically type-checked but without having to specify the type of variables or method arguments.
# - Be able to call C code by writing bindings to it in Crystal.
# - Have compile-time evaluation and generation of code, to avoid boilerplate code.
# - Compile to efficient native code.

# A very basic HTTP server
require "http/server"

server = HTTP::Server.new(8080) do |request|
  HTTP::Response.ok "text/plain", "Hello world, got #{request.path}!"
end

puts "Listening on http://0.0.0.0:8080"
server.listen

module Foo
  abstract def abstract_method : String

  @[AlwaysInline]
  def with_foofoo
    with Foo.new(self) yield
  end

  struct Foo
    def initialize(@foo : ::Foo)
    end

    def hello_world
      @foo.abstract_method
    end
  end
end

class Bar
  include Foo

  @@foobar = 12345

  def initialize(@bar : Int32)
  end

  macro alias_method(name, method)
    def {{ name }}(*args)
      {{ method }}(*args)
    end
  end

  def a_method
    "Hello, World"
  end

  alias_method abstract_method, a_method

  def show_instance_vars : Nil
    {% for var in @type.instance_vars %}
      puts "@{{ var }} = #{ @{{ var }} }"
    {% end %}
  end
end

class Baz &lt; Bar; end

lib LibC
  fun c_puts = "puts"(str : Char*) : Int
end

baz = Baz.new(100)
baz.show_instance_vars
baz.with_foofoo do
  LibC.c_puts hello_world
end
</textarea></form>
<script>
  var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
    mode: "text/x-crystal",
    matchBrackets: true,
    indentUnit: 2
  });
</script>

<p><strong>MIME types defined:</strong> <code>text/x-crystal</code>.</p>
</article>
