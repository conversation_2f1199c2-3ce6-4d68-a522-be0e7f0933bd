<!doctype html>

<title>CodeMirror: Vue.js mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">

<link rel="stylesheet" href="../../lib/codemirror.css">
<script src="../../lib/codemirror.js"></script>
<script src="../../addon/mode/overlay.js"></script>
<script src="../../addon/mode/simple.js"></script>
<script src="../../addon/selection/selection-pointer.js"></script>
<script src="../xml/xml.js"></script>
<script src="../javascript/javascript.js"></script>
<script src="../css/css.js"></script>
<script src="../coffeescript/coffeescript.js"></script>
<script src="../sass/sass.js"></script>
<script src="../pug/pug.js"></script>

<script src="../handlebars/handlebars.js"></script>
<script src="../htmlmixed/htmlmixed.js"></script>
<script src="vue.js"></script>
<style>.CodeMirror {border-top: 1px solid black; border-bottom: 1px solid black;}</style>
<div id=nav>
  <a href="https://codemirror.net"><h1>CodeMirror</h1><img id=logo src="../../doc/logo.png" alt=""></a>

  <ul>
    <li><a href="../../index.html">Home</a>
    <li><a href="../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror">Code</a>
  </ul>
  <ul>
    <li><a href="../index.html">Language modes</a>
    <li><a class=active href="#">Vue.js mode</a>
  </ul>
</div>

<article>
<h2>Vue.js mode</h2>
<form><textarea id="code" name="code">
<template>
  <div class="sass">I'm a {{mustache-like}} template</div>
</template>

<script lang="coffee">
  module.exports =
    props: ['one', 'two', 'three']
</script>

<style lang="sass">
.sass
  font-size: 18px
</style>

</textarea></form>
    <script>
      // Define an extended mixed-mode that understands vbscript and
      // leaves mustache/handlebars embedded templates in html mode
      var mixedMode = {
        name: "vue"
      };
      var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
        mode: mixedMode,
        selectionPointer: true
      });
    </script>

    <p><strong>MIME types defined:</strong> <code>text/x-vue</code></p>

  </article>
