{"name": "yansongda/pay", "description": "可能是我用过的最优雅的 Alipay 和 WeChat 的支付 SDK 扩展包了", "keywords": ["alipay", "wechat", "pay"], "type": "library", "license": "MIT", "support": {"issues": "https://github.com/yansongda/pay/issues", "source": "https://github.com/yansongda/pay", "homepage": "https://pay.yansongda.cn"}, "authors": [{"name": "ya<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=7.4", "ext-openssl": "*", "ext-simplexml": "*", "ext-libxml": "*", "ext-json": "*", "ext-bcmath": "*", "psr/event-dispatcher": "^1.0", "psr/log": "^1.1 | ^2.0 | ^3.0", "psr/container": "^1.1 | ^2.0", "psr/http-client": "^1.0", "psr/http-message": "^1.0", "yansongda/supports": "~3.2.0"}, "require-dev": {"phpunit/phpunit": "^9.0", "mockery/mockery": "^1.4", "friendsofphp/php-cs-fixer": "^3.0", "phpstan/phpstan": "^1.0.0", "monolog/monolog": "^2.2", "symfony/var-dumper": "^5.1", "symfony/http-foundation": "^5.2.0", "symfony/event-dispatcher": "^5.2.0", "symfony/psr-http-message-bridge": "^2.1", "guzzlehttp/guzzle": "^7.0"}, "autoload": {"psr-4": {"Yansongda\\Pay\\": "src"}, "files": ["src/Functions.php"]}, "autoload-dev": {"psr-4": {"Yansongda\\Pay\\Tests\\": "tests"}}, "suggest": {"illuminate/container": "Laravel 框架下使用 SDK，请安装", "hyperf/utils": "Hyperf 框架下使用 SDK，请安装", "php-di/php-di": "其它/无框架下使用 SDK，请安装"}, "scripts": {"test": "./vendor/bin/phpunit -c phpunit.xml --colors=always", "cs-fix": "php-cs-fixer fix --dry-run --diff 1>&2", "analyse": "phpstan analyse --memory-limit 300M -l 5 -c phpstan.neon ./src"}}